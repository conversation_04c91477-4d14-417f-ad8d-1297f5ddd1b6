# SageBase Security Action Plan

## 🚨 Critical Security Issues to Address Immediately

### 1. Secrets Management (HIGH PRIORITY)
**Current Issue**: API keys and secrets exposed in .env files
```bash
# Found in .env files:
OPENAI_API_KEY=sk-proj-v4DvCsMGYgnPXpPYtcUc...
GITHUB_APP_CLIENT_SECRET=0296cf7b4e8bb6aa9d1f...
DATABASE_URL=postgres://wissem:Test123456@...
```

**Action Required**:
1. Set up Azure Key Vault
2. Migrate all secrets to Key Vault
3. Update application to use Key Vault SDK
4. Rotate all exposed secrets

**Commands to run on Azure VM**:
```bash
# Install Azure CLI and Key Vault tools
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
az login
az keyvault create --name sagebase-secrets --resource-group <your-rg> --location francecentral
```

### 2. Production Configuration (HIGH PRIORITY)
**Current Issues**:
- `DEBUG = True` in production
- `ALLOWED_HOSTS = ['*']` too permissive
- `CORS_ALLOW_ALL_ORIGINS = True` development setting

**Action Required**: Update Django settings for production

### 3. Database Security Verification (MEDIUM PRIORITY)
**Action Required**: Verify Azure PostgreSQL encryption settings
```bash
# Commands to run on Azure VM:
az postgres server show --resource-group <rg> --name qtest --query "{sslEnforcement:sslEnforcement,infrastructureEncryption:infrastructureEncryption}"
```

## 🔧 Commands to Run on Azure VM

### System Information Collection
```bash
# System and OS information
uname -a
lsb_release -a
cat /etc/os-release

# Check installed packages and versions
dpkg -l | grep -E "(nginx|docker|python|postgresql)" > system_packages.txt

# Docker information
docker --version
docker-compose --version
docker images
docker ps -a

# Network and firewall status
sudo ufw status
netstat -tlnp
ss -tlnp

# Check SSL certificates
sudo certbot certificates
openssl x509 -in /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem -text -noout | grep -E "(Subject|Issuer|Not Before|Not After)"
```

### Security Scanning
```bash
# Make the security scan script executable and run it
chmod +x security_scan_setup.sh
./security_scan_setup.sh

# Additional Docker security scan
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  -v $(pwd):/tmp aquasec/trivy image sagebase_backend-web:latest

# Check for rootkits and malware
sudo apt install -y rkhunter chkrootkit
sudo rkhunter --check --sk
sudo chkrootkit
```

### Nginx Security Analysis
```bash
# Check nginx configuration
nginx -t
nginx -T | grep -E "(ssl_|security|header)"

# Test SSL configuration
curl -I https://sagebaseserverfrontend.sagebase.tech
openssl s_client -connect sagebaseserverfrontend.sagebase.tech:443 -servername sagebaseserverfrontend.sagebase.tech
```

### Database Security Check
```bash
# Check PostgreSQL connection and encryption
psql "postgres://wissem:<EMAIL>:5432/sageBase_test_azure?sslmode=require" -c "\conninfo"
psql "postgres://wissem:<EMAIL>:5432/sageBase_test_azure?sslmode=require" -c "SHOW ssl;"
```

## 🛠️ Immediate Security Fixes to Implement

### 1. Update Django Settings for Production
Create a new secure settings file:

```python
# SageBase_Backend/SageBase_Backend/settings_production.py
import os
from .settings import *

# Security settings for production
DEBUG = False
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')  # Move to Azure Key Vault
ALLOWED_HOSTS = [
    'sagebaseserverfrontend.sagebase.tech',
    'app.sagebase.tech',
    'sagebasetestserverazure.sagebase.tech'
]

# CORS settings for production
CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = [
    "https://sagebaseserverfrontend.sagebase.tech",
    "https://app.sagebase.tech",
    "https://sagebasetestserverazure.sagebase.tech"
]

# Security headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Session security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
```

### 2. Add Security Headers to Nginx
```nginx
# Add to nginx configuration
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### 3. Implement Rate Limiting
```nginx
# Add to nginx configuration
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
}

server {
    location /api/ {
        limit_req zone=api burst=20 nodelay;
    }
    
    location /api/auth/ {
        limit_req zone=login burst=5 nodelay;
    }
}
```

## 📊 Security Monitoring Setup

### 1. Log Monitoring
```bash
# Set up log rotation and monitoring
sudo apt install logrotate
sudo nano /etc/logrotate.d/sagebase

# Content for logrotate config:
/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
    postrotate
        systemctl reload nginx
    endscript
}
```

### 2. Automated Security Updates
```bash
# Enable automatic security updates
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 🔍 Free Security Tools to Install and Configure

### 1. Fail2Ban (Intrusion Prevention)
```bash
sudo apt install fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 2. OSSEC (Host Intrusion Detection)
```bash
wget https://github.com/ossec/ossec-hids/archive/3.7.0.tar.gz
tar -xzf 3.7.0.tar.gz
cd ossec-hids-3.7.0
sudo ./install.sh
```

### 3. Lynis (Security Auditing)
```bash
sudo apt install lynis
sudo lynis audit system
```

## 📋 Compliance Checklist

### GDPR Compliance
- [ ] Data mapping and inventory
- [ ] Privacy policy update
- [ ] User consent mechanisms
- [ ] Data export functionality
- [ ] Data deletion functionality
- [ ] Breach notification procedures
- [ ] Data Protection Impact Assessment (DPIA)

### Security Best Practices
- [ ] Regular security updates
- [ ] Vulnerability scanning
- [ ] Penetration testing
- [ ] Security training
- [ ] Incident response plan
- [ ] Backup and recovery testing
- [ ] Access control review

## 📈 Next Steps After Initial Assessment

1. **Run all commands on Azure VM** and collect results
2. **Review security scan reports** and prioritize fixes
3. **Implement critical security fixes** (secrets, production config)
4. **Set up monitoring and alerting**
5. **Create security documentation** for the team
6. **Schedule regular security reviews**

This action plan provides a comprehensive roadmap for improving SageBase's security posture and preparing for the security presentation.
