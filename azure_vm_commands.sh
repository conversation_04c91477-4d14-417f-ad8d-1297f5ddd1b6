#!/bin/bash

# SageBase Security Assessment - Azure VM Commands
# Run these commands on your Azure VM to collect security information

echo "🔒 SageBase Security Assessment - Data Collection"
echo "================================================"
echo "Run date: $(date)"
echo ""

# Create reports directory
mkdir -p security_assessment_$(date +%Y%m%d)
cd security_assessment_$(date +%Y%m%d)

echo "📋 1. System Information Collection"
echo "=================================="

# System and OS information
echo "Collecting system information..."
uname -a > system_info.txt
lsb_release -a >> system_info.txt 2>/dev/null
cat /etc/os-release >> system_info.txt
echo "System info saved to: system_info.txt"

# Installed packages
echo "Collecting package information..."
dpkg -l > installed_packages.txt
dpkg -l | grep -E "(nginx|docker|python|postgresql|ssl|security)" > security_packages.txt
echo "Package info saved to: installed_packages.txt, security_packages.txt"

# Docker information
echo "Collecting Docker information..."
docker --version > docker_info.txt 2>/dev/null || echo "Docker not installed" > docker_info.txt
docker-compose --version >> docker_info.txt 2>/dev/null || echo "Docker-compose not installed" >> docker_info.txt
docker images >> docker_info.txt 2>/dev/null || echo "Cannot access Docker images" >> docker_info.txt
docker ps -a >> docker_info.txt 2>/dev/null || echo "Cannot access Docker containers" >> docker_info.txt
echo "Docker info saved to: docker_info.txt"

echo ""
echo "🌐 2. Network and Security Configuration"
echo "======================================="

# Network configuration
echo "Collecting network information..."
netstat -tlnp > network_ports.txt 2>/dev/null || ss -tlnp > network_ports.txt
sudo ufw status > firewall_status.txt 2>/dev/null || echo "UFW not available" > firewall_status.txt
ip addr show > network_interfaces.txt
echo "Network info saved to: network_ports.txt, firewall_status.txt, network_interfaces.txt"

# SSL Certificate information
echo "Collecting SSL certificate information..."
sudo certbot certificates > ssl_certificates.txt 2>/dev/null || echo "Certbot not available" > ssl_certificates.txt

# Check SSL certificate details
if [ -f "/etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem" ]; then
    openssl x509 -in /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem -text -noout > ssl_cert_details.txt
    echo "SSL certificate details saved to: ssl_cert_details.txt"
else
    echo "SSL certificate not found at expected location" > ssl_cert_details.txt
fi

echo ""
echo "🔧 3. Service Configuration Analysis"
echo "==================================="

# Nginx configuration
echo "Collecting Nginx configuration..."
nginx -t > nginx_test.txt 2>&1 || echo "Nginx not available or configuration error" > nginx_test.txt
nginx -T > nginx_full_config.txt 2>/dev/null || echo "Cannot access Nginx configuration" > nginx_full_config.txt
echo "Nginx config saved to: nginx_test.txt, nginx_full_config.txt"

# Check running services
echo "Collecting service information..."
systemctl list-units --type=service --state=running > running_services.txt
systemctl status nginx > nginx_status.txt 2>/dev/null || echo "Nginx service not found" > nginx_status.txt
systemctl status docker > docker_status.txt 2>/dev/null || echo "Docker service not found" > docker_status.txt
echo "Service info saved to: running_services.txt, nginx_status.txt, docker_status.txt"

echo ""
echo "🛡️ 4. Security Scanning"
echo "======================"

# Install security tools if not present
echo "Installing security scanning tools..."
sudo apt update
sudo apt install -y python3-pip curl wget

# Install Python security tools
pip3 install safety bandit --user 2>/dev/null || echo "Failed to install Python security tools"

# Run security scans if tools are available
echo "Running security scans..."

# Python dependency scan
if command -v safety &> /dev/null; then
    cd /path/to/SageBase_Backend 2>/dev/null || cd ~
    safety check > ../security_assessment_$(date +%Y%m%d)/python_vulnerabilities.txt 2>&1 || echo "Safety scan failed" > ../security_assessment_$(date +%Y%m%d)/python_vulnerabilities.txt
    cd ../security_assessment_$(date +%Y%m%d)
    echo "Python vulnerability scan completed"
else
    echo "Safety tool not available" > python_vulnerabilities.txt
fi

# Bandit SAST scan
if command -v bandit &> /dev/null; then
    cd /path/to/SageBase_Backend 2>/dev/null || cd ~
    bandit -r . > ../security_assessment_$(date +%Y%m%d)/bandit_sast.txt 2>&1 || echo "Bandit scan failed" > ../security_assessment_$(date +%Y%m%d)/bandit_sast.txt
    cd ../security_assessment_$(date +%Y%m%d)
    echo "Bandit SAST scan completed"
else
    echo "Bandit tool not available" > bandit_sast.txt
fi

echo ""
echo "🔍 5. Database Security Check"
echo "============================"

# Test database connection and SSL
echo "Testing database connection..."
# Note: Replace with actual database credentials from environment
DB_URL="postgres://wissem:<EMAIL>:5432/sageBase_test_azure?sslmode=require"

if command -v psql &> /dev/null; then
    echo "Testing database SSL connection..."
    psql "$DB_URL" -c "\conninfo" > database_connection.txt 2>&1 || echo "Database connection failed" > database_connection.txt
    psql "$DB_URL" -c "SHOW ssl;" >> database_connection.txt 2>&1 || echo "SSL check failed" >> database_connection.txt
    echo "Database security check completed"
else
    echo "PostgreSQL client not available" > database_connection.txt
fi

echo ""
echo "🌐 6. Web Security Tests"
echo "======================"

# Test SSL configuration
echo "Testing SSL/TLS configuration..."
curl -I https://sagebaseserverfrontend.sagebase.tech > ssl_test.txt 2>&1 || echo "HTTPS test failed" > ssl_test.txt
curl -I https://app.sagebase.tech >> ssl_test.txt 2>&1 || echo "App HTTPS test failed" >> ssl_test.txt

# Test SSL cipher strength
echo "Testing SSL cipher strength..."
openssl s_client -connect sagebaseserverfrontend.sagebase.tech:443 -servername sagebaseserverfrontend.sagebase.tech < /dev/null > ssl_cipher_test.txt 2>&1 || echo "SSL cipher test failed" > ssl_cipher_test.txt

echo ""
echo "📊 7. System Security Audit"
echo "=========================="

# Install and run Lynis if available
if command -v lynis &> /dev/null; then
    echo "Running Lynis security audit..."
    sudo lynis audit system > lynis_audit.txt 2>&1
    echo "Lynis audit completed"
else
    echo "Installing Lynis..."
    sudo apt install -y lynis 2>/dev/null || echo "Failed to install Lynis"
    if command -v lynis &> /dev/null; then
        sudo lynis audit system > lynis_audit.txt 2>&1
        echo "Lynis audit completed"
    else
        echo "Lynis not available" > lynis_audit.txt
    fi
fi

# Check for rootkits
echo "Running rootkit scan..."
sudo apt install -y rkhunter chkrootkit 2>/dev/null || echo "Failed to install rootkit scanners"
sudo rkhunter --check --sk > rkhunter_scan.txt 2>&1 || echo "RKHunter scan failed" > rkhunter_scan.txt
sudo chkrootkit > chkrootkit_scan.txt 2>&1 || echo "Chkrootkit scan failed" > chkrootkit_scan.txt

echo ""
echo "📋 8. Generating Summary Report"
echo "=============================="

# Create summary report
cat > security_assessment_summary.txt << EOF
SageBase Security Assessment Summary
===================================
Assessment Date: $(date)
Server: $(hostname)
OS: $(lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)

Files Generated:
- system_info.txt: System and OS information
- installed_packages.txt: All installed packages
- security_packages.txt: Security-related packages
- docker_info.txt: Docker configuration and containers
- network_ports.txt: Open ports and services
- firewall_status.txt: Firewall configuration
- ssl_certificates.txt: SSL certificate information
- ssl_cert_details.txt: Detailed certificate analysis
- nginx_test.txt: Nginx configuration test
- nginx_full_config.txt: Complete Nginx configuration
- running_services.txt: Active system services
- python_vulnerabilities.txt: Python dependency vulnerabilities
- bandit_sast.txt: Static application security testing results
- database_connection.txt: Database security configuration
- ssl_test.txt: HTTPS connectivity tests
- ssl_cipher_test.txt: SSL/TLS cipher analysis
- lynis_audit.txt: Comprehensive security audit
- rkhunter_scan.txt: Rootkit detection scan
- chkrootkit_scan.txt: Additional rootkit scan

Next Steps:
1. Review all generated files for security issues
2. Address critical vulnerabilities found
3. Implement recommended security improvements
4. Set up automated security monitoring
5. Schedule regular security assessments

Critical Items to Review:
- Check python_vulnerabilities.txt for high-severity issues
- Review nginx_full_config.txt for security headers
- Examine lynis_audit.txt for system hardening recommendations
- Verify SSL configuration in ssl_cipher_test.txt
- Check database_connection.txt for encryption status
EOF

echo ""
echo "✅ Security Assessment Complete!"
echo "==============================="
echo "📁 All reports saved in: $(pwd)"
echo "📋 Summary available in: security_assessment_summary.txt"
echo ""
echo "📤 Next Steps:"
echo "1. Review all generated files"
echo "2. Share findings with security team"
echo "3. Implement critical security fixes"
echo "4. Update security documentation"
echo ""
echo "🔍 To review results:"
echo "   ls -la"
echo "   cat security_assessment_summary.txt"
echo ""
echo "⚠️  Important: Review and address any critical or high-severity issues found"
