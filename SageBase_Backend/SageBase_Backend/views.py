from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.conf import settings
import os


@api_view(["GET"]) 
@permission_classes([AllowAny])
def version_view(_request):
    return Response({
        "version": getattr(settings, "APP_VERSION", os.getenv("APP_VERSION", "dev")),
        "env": os.getenv("DJANGO_ENV", "development"),
        "commit": getattr(settings, "GIT_COMMIT", os.getenv("GIT_COMMIT", "")),
        "build_time": getattr(settings, "BUILD_TIME", os.getenv("BUILD_TIME", "")),
    })