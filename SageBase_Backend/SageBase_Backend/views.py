from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.conf import settings
import os


# Cache version info at module load time
_VERSION_INFO = None

def _get_version_info():
    """Get version information from git - cached at module load"""
    global _VERSION_INFO
    
    if _VERSION_INFO is None:
        import subprocess
        import logging
        
        logger = logging.getLogger(__name__)
        
        # Get current git branch with better error handling
        branch = "unknown"
        try:
            # First, try to fix git ownership issues
            try:
                subprocess.run(
                    ['git', 'config', '--global', '--add', 'safe.directory', '/app'],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                logger.info("✅ Git safe directory configured")
            except Exception as e:
                logger.warning(f"⚠️ Could not configure git safe directory: {e}")
            
            # Ensure we're in the correct directory
            result = subprocess.run(
                ['git', 'rev-parse', '--abbrev-ref', 'HEAD'], 
                capture_output=True,
                text=True,
                cwd='/app',
                timeout=10
            )
            if result.returncode == 0:
                branch = result.stdout.strip()
                logger.info(f"✅ Git branch detected: {branch}")
            else:
                logger.warning(f"⚠️ Git branch command failed: {result.stderr}")
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired) as e:
            logger.warning(f"⚠️ Git branch detection failed: {e}")
        
        # Determine version based on branch
        version = "dev-unknown"
        if branch in ['main', 'master', 'production', 'deployment']:
            # Deployment branches - use latest tag
            try:
                result = subprocess.run(
                    ['git', 'describe', '--tags', '--abbrev=0'], 
                    capture_output=True,
                    text=True,
                    cwd='/app',
                    timeout=10
                )
                if result.returncode == 0:
                    version = result.stdout.strip()
                    logger.info(f"✅ Git tag detected: {version}")
                else:
                    logger.warning(f"⚠️ Git tag command failed: {result.stderr}")
                    # Fallback to environment variable
                    version = getattr(settings, "APP_VERSION", os.getenv("APP_VERSION", "dev"))
            except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired) as e:
                logger.warning(f"⚠️ Git tag detection failed: {e}")
                # Fallback to environment variable
                version = getattr(settings, "APP_VERSION", os.getenv("APP_VERSION", "dev"))
        else:
            # Development branches - show branch name
            version = f"dev-{branch}"
        
        _VERSION_INFO = {
            "version": version,
            "branch": branch,
            "env": os.getenv("DJANGO_ENV", "development"),
            "commit": getattr(settings, "GIT_COMMIT", os.getenv("GIT_COMMIT", "")),
            "build_time": getattr(settings, "BUILD_TIME", os.getenv("BUILD_TIME", "")),
        }
        
        # Log the version info for debugging
        logger.info(f"🚀 App version info loaded: {_VERSION_INFO}")
    
    return _VERSION_INFO

@api_view(["GET"]) 
@permission_classes([AllowAny])
def version_view(_request):
    """Return cached version information"""
    # Allow force refresh with query param (useful for development)
    force_refresh = _request.GET.get('refresh', '').lower() == 'true'
    
    if force_refresh:
        global _VERSION_INFO
        _VERSION_INFO = None  # Force cache refresh
    
    return Response(_get_version_info())

# Don't call _get_version_info() at module load - let it happen lazily
# _get_version_info()