"""
Async Generator Error Suppression

This module suppresses harmless but annoying async generator cleanup errors
that occur when MCP servers are cleaned up. These errors don't affect functionality
but create noise in the logs.
"""

import asyncio
import logging

# Get logger for this module
logger = logging.getLogger(__name__)

def setup_async_generator_suppression():
    """
    Setup global async generator error suppression for MCP-related cleanup issues.
    
    This patches asyncio's logger to suppress specific async generator cleanup errors
    that are harmless but create noise in the logs.
    """
    
    # Hook into asyncio's internal logging mechanism - this is the most effective approach
    import logging as logging_module
    
    # Get the asyncio logger and patch its error method
    asyncio_logger = logging_module.getLogger('asyncio')
    original_error = asyncio_logger.error
    
    def patched_asyncio_error(msg, *args, **kwargs):
        # Check if this is the specific async generator cleanup error
        full_msg = str(msg) % args if args else str(msg)
        
        if ('an error occurred during closing of asynchronous generator' in full_msg and
            'streamablehttp_client' in full_msg):
            logger.debug(f"Suppressed asyncio async generator cleanup error")
            return
            
        # Check for other MCP-related patterns
        if ('streamablehttp_client' in full_msg and 'async generator' in full_msg):
            logger.debug(f"Suppressed asyncio streamable http error")
            return
            
        # Call original error method for all other errors
        original_error(msg, *args, **kwargs)
    
    asyncio_logger.error = patched_asyncio_error
    
    print("[INFO] 🔇 Async generator error suppression enabled")
    logger.info("🔇 Async generator error suppression enabled")

# Automatically apply suppression when module is imported
setup_async_generator_suppression()