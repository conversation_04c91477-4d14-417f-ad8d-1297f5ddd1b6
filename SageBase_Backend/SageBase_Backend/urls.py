"""
URL configuration for SageBase_Backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Add an import:  from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from integrations import views as integrations_views
from konowledge_detection_agents.api_views import AgentOrchestratorAskView
from SageBase_Backend.views import version_view

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/version', version_view),
    path('api/integrations/', include('integrations.urls')),
    path('api/vectordb/', include('vectordb.api.urls')),
    path('api/agent/ask/', AgentOrchestratorAskView.as_view(), name='agent-orchestrator-ask'),
    path('api/possible_connections/', integrations_views.possible_connections, name='possible-connections'),
    # Knowledge Map API - direct routes for frontend
    path('api/', include('knowledge_map.urls')),
    path('api/', include('knowledge_spaces_Q_A.urls')),
    # Google Drive API
    path('api/google-drive/', include('interfaces_data.google_drive.urls')),
]

# Serve static files during development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
