"""
Django settings for SageBase_Backend project.

Generated by 'django-admin startproject' using Django 4.2.21.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os
import environ
import dj_database_url
import re
import logging
import sys

# Import async generator suppression early to handle MCP cleanup issues
try:
    from . import async_generator_suppression
except ImportError:
    pass  # Ignore if module doesn't exist

def patch_sqlite_for_chromadb():
    """Patch sqlite3 to use pysqlite3-binary for ChromaDB compatibility"""
    try:
        # Try to install pysqlite3-binary if not available
        try:
            import pysqlite3
        except ImportError:
            print("Installing pysqlite3-binary for ChromaDB compatibility...")
            import subprocess
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pysqlite3-binary"], 
                                    capture_output=True, text=True)
                import pysqlite3
                print("✅ pysqlite3-binary installed successfully")
            except Exception as e:
                print(f"⚠️ Could not install pysqlite3-binary: {e}")
                return False
        
        # Check if we need to patch (sqlite3 version < 3.35.0)
        import sqlite3
        current_version = sqlite3.sqlite_version
        required_version = "3.35.0"
        
        def version_tuple(v):
            return tuple(map(int, (v.split("."))))
        
        if version_tuple(current_version) < version_tuple(required_version):
            print(f"Patching SQLite: {current_version} -> {pysqlite3.sqlite_version}")
            
            # Replace sqlite3 module with pysqlite3
            sys.modules['sqlite3'] = pysqlite3
            
            # Verify the patch
            import sqlite3
            print(f"✅ SQLite patched successfully. New version: {sqlite3.sqlite_version}")
            return True
        else:
            print(f"✅ SQLite version {current_version} is already compatible")
            return True
            
    except Exception as e:
        print(f"⚠️ Failed to patch SQLite for ChromaDB: {e}")
        return False

# Apply SQLite patch immediately
patch_sqlite_for_chromadb()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Initialize environ
env = environ.Env()

# Force set logging levels early for deployment
import os
DJANGO_ENV = os.getenv('DJANGO_ENV', 'development')

# Configure HTTP and external library logging (for all environments)
import logging

# Ensure UTF-8 encoding for console streams on Windows to avoid UnicodeEncodeError with emojis
try:
    import sys
    if os.name == 'nt':
        for stream in (sys.stdout, sys.stderr):
            if hasattr(stream, 'reconfigure'):
                try:
                    stream.reconfigure(encoding='utf-8')
                except Exception:
                    pass
        # Best-effort hint for Python I/O encoding
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
except Exception:
    pass

logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('httpcore.http11').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('slack_sdk').setLevel(logging.WARNING)
logging.getLogger('slack_sdk.web.base_client').setLevel(logging.WARNING)

if DJANGO_ENV == 'deployment':
    logging.getLogger('integrations').setLevel(logging.INFO)  # Allow INFO messages for Slack webhook
    logging.getLogger('integrations.views').setLevel(logging.INFO)
    logging.getLogger('integrations.auth_utils').setLevel(logging.WARNING)
    logging.getLogger('integrations.slack.api.slack_webhook_receiver').setLevel(logging.INFO)  # Allow INFO for webhook
    logging.getLogger('knowledge_spaces_Q_A').setLevel(logging.WARNING)
    logging.getLogger('knowledge_spaces_Q_A.views').setLevel(logging.WARNING)
    logging.getLogger('konowledge_detection_agents').setLevel(logging.WARNING)
    logging.getLogger('django.server').setLevel(logging.WARNING)
    logging.getLogger('vectordb').setLevel(logging.INFO)
    logging.getLogger('integrations.slack.api.slack_webhook_receiver').setLevel(logging.INFO)

# Configure logging early so it is available for all settings
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')

def prompt_ngrok_setup():
    """
    Prompts user to enter ngrok URL and automatically sets environment variable
    """
    logging.info("\n" + "=" * 60)
    logging.info("🚀 DEVELOPMENT MODE DETECTED")
    logging.info("=" * 60)
    logging.warning("⚠️  BACKEND_NGROK_BASE_URL not found!")
    logging.info("📋 Please run ngrok in a separate terminal:")
    logging.info("   ngrok http 127.0.0.1:8000")
    logging.info("")
    logging.info("Then paste your ngrok URL below:")
    logging.info("=" * 60)
    
    while True:
        ngrok_url = input("🌐 Enter your ngrok URL, you can use this command to create one ngrok http 127.0.0.1:8000 ").strip()
        
        # Validate URL format
        if not ngrok_url:
            logging.error("❌ Please enter a URL")
            continue
        
        # Check if it's a valid ngrok URL
        if not re.match(r'https://[a-zA-Z0-9\-]+\.ngrok-free\.app', ngrok_url):
            logging.error("❌ Please enter a valid ngrok URL (https://....ngrok-free.app)")
            continue
        
        # Remove trailing slash if present
        ngrok_url = ngrok_url.rstrip('/')
        
        # Set environment variable for current session
        os.environ['BACKEND_NGROK_BASE_URL'] = ngrok_url
        os.environ['BACKEND_BASE_URL'] = ngrok_url
        
        logging.info(f"✅ Environment variable set: BACKEND_NGROK_BASE_URL={ngrok_url}")
        logging.info("🚀 Django will now use your ngrok URL!")
        logging.info("💡 This allows both frontend and backend to use the same ngrok URL!")
        logging.info("=" * 60)
        break

# Determine environment and load appropriate .env file
ENVIRONMENT = os.getenv('DJANGO_ENV', 'development')  # Default to development

# Define possible env files in order of preference
env_files = []
if ENVIRONMENT == 'production' or ENVIRONMENT == 'deployment':
    env_files = [
        os.path.join(BASE_DIR, '.env.deployment'),
        os.path.join(BASE_DIR, '.env.production'),
        os.path.join(BASE_DIR, '.env')
    ]
elif ENVIRONMENT == 'development':
    env_files = [
        os.path.join(BASE_DIR, '.env.dev'),
        os.path.join(BASE_DIR, '.env.development'),
        os.path.join(BASE_DIR, '.env')
    ]
elif ENVIRONMENT == 'test':
    env_files = [
        os.path.join(BASE_DIR, '.env.test'),
        os.path.join(BASE_DIR, '.env')
    ]

# Load the first existing env file
env_file_loaded = None
for env_file in env_files:
    if os.path.exists(env_file):
        environ.Env.read_env(env_file)
        env_file_loaded = env_file
        break

# Additionally load Azure-specific environment file if present
azure_env_file = os.path.join(BASE_DIR, '.env.azure')
if os.path.exists(azure_env_file):
    environ.Env.read_env(azure_env_file)
    logging.info(f"📁 LOADED AZURE ENV FILE: {os.path.basename(azure_env_file)}")

# Additionally load version-specific overrides if present (written by scripts/deploy.sh)
version_env_file = os.path.join(BASE_DIR, '.env.version')
if os.path.exists(version_env_file):
    environ.Env.read_env(version_env_file)
    logging.info(f"📁 LOADED VERSION ENV FILE: {os.path.basename(version_env_file)}")

# Check ngrok setup for development mode
if ENVIRONMENT == 'development':
    current_ngrok_url = os.getenv('BACKEND_NGROK_BASE_URL', '')
    
    if not current_ngrok_url or current_ngrok_url == 'Not Set':
        prompt_ngrok_setup()
    else:
        logging.info(f"🌐 Using ngrok URL: {current_ngrok_url}")
        logging.info("💡 To update ngrok URL, set new BACKEND_NGROK_BASE_URL and restart Django")

# Log which configuration is being used
logging.info(f"🔍 RAW DJANGO_ENV: {os.getenv('DJANGO_ENV')}")
logging.info(f"🚀 DJANGO ENVIRONMENT: {ENVIRONMENT}")
if env_file_loaded:
    logging.info(f"📁 LOADED ENV FILE: {os.path.basename(env_file_loaded)}")
    logging.info(f"📂 FULL PATH: {env_file_loaded}")
else:
    logging.warning("⚠️  NO ENV FILE FOUND - Using system environment variables only")

# Log database configuration
logging.info(f"🗄️  DATABASE_URL: {'Set' if os.getenv('DATABASE_URL') else 'Not Set'}")
logging.info(f"🗄️  AZURE_DB_HOST: {os.getenv('AZURE_DB_HOST', 'Not Set')}")
logging.info(f"🗄️  AZURE_DB_USER: {os.getenv('AZURE_DB_USER', 'Not Set')}")

# Show the ngrok URL being used
CURRENT_NGROK_URL = os.getenv('BACKEND_NGROK_BASE_URL')
logging.info(f"🌐 NGROK URL: {CURRENT_NGROK_URL}")
logging.info("=" * 60)

GITHUB_CLIENT_ID = os.getenv("GITHUB_CLIENT_ID", "your_github_client_id_here")
GITHUB_CLIENT_SECRET = os.getenv("GITHUB_CLIENT_SECRET", "your_github_client_secret_here")
BACKEND_NGROK_BASE_URL = os.getenv("BACKEND_NGROK_BASE_URL", "")

# Github Application
GITHUB_APP_SLUG = os.getenv("GITHUB_APP_SLUG", "your_GITHUB_APP_SLUG_here")
GITHUB_APP_ID = os.getenv("GITHUB_APP_ID", "your_GITHUB_APP_ID_here")
GITHUB_APP_CLIENT_ID = os.getenv("GITHUB_APP_CLIENT_ID", "your_GITHUB_APP_CLIENT_ID_here")
GITHUB_APP_CLIENT_SECRET = os.getenv("GITHUB_APP_CLIENT_SECRET", "your_GITHUB_APP_CLIENT_SECRET_here")
GITHUB_APP_PRIVATE_KEY = os.getenv("GITHUB_APP_PRIVATE_KEY")
GITHUB_APP_WEBHOOK_SECRET = os.getenv("GITHUB_APP_WEBHOOK_SECRET", "your_GITHUB_APP_WEBHOOK_SECRET_here")

# Frontend URL settings
FRONTEND_BASE_URL = os.getenv("FRONTEND_BASE_URL")
BACKEND_BASE_URL = os.getenv("BACKEND_BASE_URL")


# Public backend URL for use in notifications, webhooks, etc.
if ENVIRONMENT == 'production' or ENVIRONMENT == 'deployment':
    BACKEND_PUBLIC_URL = os.getenv("BACKEND_BASE_URL")
else:
    BACKEND_PUBLIC_URL = os.getenv("BACKEND_NGROK_BASE_URL")

# Show the BACKEND PUBLIC URL being used
logging.info(f"🌐 Backend Public URL: {BACKEND_PUBLIC_URL}")
logging.info("=" * 60)


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-r!ip((g8(r5+6gf4w9%%o5k42zxjf1vy@#)^2k9rm##y+5!$v*'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    # Required Django apps for AbstractUser and Admin
    'django.contrib.contenttypes',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.admin',
    
    # Static files
    'django.contrib.staticfiles',
    
    # Third-party apps
    'rest_framework',
    'corsheaders',
    'integrations',
    'knowledge_map',
    'channels',
    'vectordb',  # Temporarily disabled due to Keras compatibility issues
    'integrations.slack',
    'integrations.discord',
    'knowledge_spaces_Q_A',
    'Notifications',
    'integrations.google_drive',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'integrations.middleware.DatabaseConnectionMiddleware',  # Custom middleware for connection management
    'logger.exception_handler.ExceptionLoggingMiddleware',  # Custom exception logging middleware
]

ROOT_URLCONF = 'SageBase_Backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]
ASGI_APPLICATION = "SageBase_Backend.asgi.application"
WSGI_APPLICATION = 'SageBase_Backend.wsgi.application'

# Use Redis for Channels (WebSocket) support in production, InMemory for development
if DJANGO_ENV == 'deployment' or DJANGO_ENV == 'test':
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels_redis.core.RedisChannelLayer",
            "CONFIG": {
                "hosts": [env('REDIS_URL', default='redis://localhost:6379')],
            },
        }
    }
else:
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels.layers.InMemoryChannelLayer",
        }
    }

# Redis configuration for notifications
REDIS_URL = env('REDIS_URL', default='redis://localhost:6379')
REDIS_HOST = env('REDIS_HOST', default='localhost')  
REDIS_PORT = env('REDIS_PORT', default=6379)
REDIS_DB = env('REDIS_DB', default=0)
# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Use DATABASE_URL if provided, otherwise fall back to Azure PostgreSQL configuration
DATABASE_URL = env('DATABASE_URL', default=None)

if DATABASE_URL:
    DATABASES = {
        'default': dj_database_url.parse(DATABASE_URL, conn_max_age=5)  # Reduced from 600 to 60 seconds
    }
    # Ensure SSL is required for Azure PostgreSQL and disable GSSAPI
    if 'OPTIONS' not in DATABASES['default']:
        DATABASES['default']['OPTIONS'] = {}
    DATABASES['default']['OPTIONS'].update({
        'sslmode': 'require',
        'gssencmode': 'disable',  # Disable GSSAPI authentication
        'connect_timeout': 10,
    })
else:
    # Azure PostgreSQL configuration
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': 'sageBase_test_azure',
            'USER': 'wissem',
            'PASSWORD': 'Test123456',
            'HOST': 'qtest.postgres.database.azure.com',
            'PORT': '5432',
            'OPTIONS': {
                'sslmode': 'require',
                'gssencmode': 'disable',  # Disable GSSAPI authentication
                'connect_timeout': 10,
            },
        }
    }

# Log which database configuration is being used
if DATABASE_URL:
    logging.info(f"🗄️  Using DATABASE_URL configuration")
else:
    logging.info(f"🗄️  Using Azure PostgreSQL configuration")
    logging.info(f"🗄️  Database: {DATABASES['default']['NAME']}")
    logging.info(f"🗄️  Host: {DATABASES['default']['HOST']}")
    logging.info(f"🗄️  User: {DATABASES['default']['USER']}")
    logging.info(f"🗄️  Port: {DATABASES['default']['PORT']}")


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles') 

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom user model
AUTH_USER_MODEL = 'integrations.User'

# REST Framework configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [],
    'DEFAULT_PERMISSION_CLASSES': [],
    'UNAUTHENTICATED_USER': None,
    'UNAUTHENTICATED_TOKEN': None,
}

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # For development only, restrict in production

# URL settings  
APPEND_SLASH = True  # Automatically append trailing slashes

# ChromaDB settings
CHROMA_PERSIST_DIR = env('CHROMA_PERSIST_DIR', default=os.path.join(BASE_DIR, 'chroma_db'))
CHROMA_HOST = env('CHROMA_HOST', default=None)  # For client mode
CHROMA_PORT = env('CHROMA_PORT', default=8000, cast=int)  # For client mode
CHROMA_EMBEDDING_TYPE = env('CHROMA_EMBEDDING_TYPE', default='sentence_transformer')  # or 'openai'
CHROMA_EMBEDDING_MODEL = env('CHROMA_EMBEDDING_MODEL', default='all-MiniLM-L6-v2')

# OpenAI settings (optional, for embeddings)
OPENAI_API_KEY = env('OPENAI_API_KEY', default=None)

# Disable ChromaDB telemetry to avoid version mismatch errors
os.environ['ANONYMIZED_TELEMETRY'] = 'False'
os.environ['CHROMA_TELEMETRY'] = 'False'
os.environ['CHROMA_CLIENT_AUTH_PROVIDER'] = ''
os.environ['CHROMA_CLIENT_AUTH_CREDENTIALS'] = ''
# Note: CHROMA_API_IMPL removed to allow automatic selection between server/persistent mode
os.environ['CHROMA_SERVER_NOFILE'] = '65536'

# Ensure ChromaDB directory exists
os.makedirs(CHROMA_PERSIST_DIR, exist_ok=True)

CSRF_TRUSTED_ORIGINS = [
      "https://sagebaseserverfrontend.sagebase.tech",
      "https://sagebaseserverfrontend.sagebase.tech:8000",
      "https://app.sagebase.tech",
      "https://sagebasetestserverazure.sagebase.tech",
      "https://sagebasetestserverazure.sagebase.tech:8000",
]

# Session Configuration
SESSION_COOKIE_AGE = 1800  # 30 minutes - OAuth flow timeout
SESSION_EXPIRE_AT_BROWSER_CLOSE = True  # Clear sessions when browser closes
SESSION_COOKIE_SECURE = True if DJANGO_ENV == 'deployment' else False  # HTTPS only in production
SESSION_COOKIE_SAMESITE = 'Lax'  # Allow cross-site requests for OAuth callbacks
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookies
SESSION_SAVE_EVERY_REQUEST = True  # Refresh session on every request
SESSION_COOKIE_NAME = 'sagebase_sessionid'  # Custom session cookie name

# Logging Configuration - Enhanced with stack tracing
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'level': 'DEBUG' if DJANGO_ENV != 'deployment' else 'WARNING',
            'class': 'logging.StreamHandler',
            'formatter': 'colored',
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
            'formatter': 'detailed',
            'encoding': 'utf-8',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': 'logs/errors.log',
            'formatter': 'detailed',
            'encoding': 'utf-8',
        },
    },
    'formatters': {
        'colored': {
            '()': 'colorlog.ColoredFormatter',
            'format': '%(log_color)s%(levelname)-8s%(reset)s %(filename)s:%(lineno)d: %(message)s',
            'log_colors': {
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            },
        },
        'detailed': {
            'format': '%(asctime)s %(levelname)-8s %(filename)s:%(lineno)d: %(message)s',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG' if DJANGO_ENV != 'deployment' else 'WARNING',
    },
    'loggers': {
        'integrations': {
            'handlers': ['console'],
            'level': 'DEBUG' if DJANGO_ENV != 'deployment' else 'WARNING',
            'propagate': False,
        },
        'channels': {
            'handlers': ['console'],
            'level': 'DEBUG' if DJANGO_ENV != 'deployment' else 'WARNING',
            'propagate': False,
        },
        'django': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'knowledge_spaces_Q_A': {
            'handlers': ['console'],
            'level': 'DEBUG' if DJANGO_ENV != 'deployment' else 'WARNING',
            'propagate': False,
        },
        'konowledge_detection_agents': {
            'handlers': ['console'],
            'level': 'DEBUG' if DJANGO_ENV != 'deployment' else 'WARNING',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'openai': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'openai._base_client': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'httpx': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'urllib3': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Uvicorn and ASGI loggers
        'uvicorn': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'uvicorn.access': {
            'handlers': [],  # No handlers = no output
            'level': 'CRITICAL',  # Only show critical errors
            'propagate': False,
        },
        'uvicorn.error': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'uvicorn.asgi': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Discord bot loggers
        'discord': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'discord_bot': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'messaging.discord.discord_bot': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Daphne ASGI server loggers
        'daphne': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'daphne.http_protocol': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'daphne.ws_protocol': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
}

# Discord settings:
DISCORD_CLIENT_ID = os.getenv("DISCORD_CLIENT_ID", "your_discord_client_id_here")
DISCORD_CLIENT_SECRET = os.getenv("DISCORD_CLIENT_SECRET", "your_discord_client_secret_here")
DISCORD_REDIRECT_URI = os.getenv("DISCORD_REDIRECT_URI")
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN", "your_discord_bot_token_here")
DISCORD_WEBHOOK_URL = os.getenv("DISCORD_WEBHOOK_URL")
# Discord webhook settings
DISCORD_WEBHOOK_SECRET = os.getenv("DISCORD_WEBHOOK_SECRET", "default_secret_key")

# Discord bot auto-start settings
DISCORD_BOT_AUTO_START = os.getenv("DISCORD_BOT_AUTO_START", "true").lower() == "true"

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = env('EMAIL_HOST', default='send.one.com')
EMAIL_PORT = env('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = env('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = env('EMAIL_HOST_USER', default='<EMAIL>')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL', default='SageBase <<EMAIL>>')

# Setup colored logging
try:
    from logger.logging_utils import setup_colored_logging
    setup_colored_logging(level="DEBUG" if DJANGO_ENV != 'deployment' else "WARNING")
    
    # Force override all existing loggers to use colored format
    import logging
    root_logger = logging.getLogger()
    
    # Clear any existing handlers and force our colored handler
    if root_logger.handlers:
        root_logger.handlers.clear()
    
    # Re-apply our colored logging setup
    setup_colored_logging(level="DEBUG" if DJANGO_ENV != 'deployment' else "WARNING")
    
    # Force specific loggers to use our format
    for logger_name in ['uvicorn', 'uvicorn.access', 'uvicorn.error', 'uvicorn.asgi', 'discord', 'discord_bot', 'daphne', 'daphne.http_protocol', 'daphne.ws_protocol']:
        logger = logging.getLogger(logger_name)
        if logger.handlers:
            logger.handlers.clear()
        logger.propagate = True  # Let it propagate to root logger
    
except ImportError:
    pass  # Fallback to basic logging if colorlog not available

# Configure OpenAI client logging to WARNING level to reduce noise
logging.getLogger('openai._base_client').setLevel(logging.WARNING)
logging.getLogger('openai').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)

# Configure Daphne HTTP protocol logging to WARNING level to reduce noise
logging.getLogger('daphne').setLevel(logging.WARNING)
logging.getLogger('daphne.http_protocol').setLevel(logging.WARNING)
logging.getLogger('daphne.ws_protocol').setLevel(logging.WARNING)
logging.getLogger('uvicorn.lifespan').setLevel(logging.WARNING)
logging.getLogger('uvicorn.protocols.http.httptools_impl').setLevel(logging.WARNING)

# Completely disable uvicorn access logs (HTTP request logs)
logging.getLogger('uvicorn.access').disabled = True
logging.getLogger('uvicorn.access').handlers = []
logging.getLogger('uvicorn.access').propagate = False

# Setup enhanced exception logging
try:
    from logger.exception_handler import setup_exception_logging
    setup_exception_logging()
    logging.info("✅ Enhanced exception logging enabled with stack traces")
except ImportError as e:
    logging.warning(f"⚠️ Could not setup enhanced exception logging: {e}")


# Google Drive Integration Settings
GOOGLE_DRIVE_CREDENTIALS_FILE = os.path.join(BASE_DIR, 'interfaces_data', 'google_drive_credentials.json')
GDRIVE_OAUTH_CREDENTIALS_FILE = GOOGLE_DRIVE_CREDENTIALS_FILE
GDRIVE_REDIRECT_URI = os.getenv('GDRIVE_REDIRECT_URI')
FRONTEND_BASE_URL = os.getenv('FRONTEND_BASE_URL')
GDRIVE_ENABLE_POLLER=os.getenv('GDRIVE_ENABLE_POLLER', False)

GDRIVE_WEBHOOK_URL = os.getenv('GDRIVE_WEBHOOK_URL', 'https://40c54e6bcfb4.ngrok-free.app/api/integrations/google-drive/webhook/')

# Application versioning metadata (override via env)
APP_VERSION = os.getenv('APP_VERSION', 'dev')
GIT_COMMIT = os.getenv('GIT_COMMIT', '')
BUILD_TIME = os.getenv('BUILD_TIME', '')