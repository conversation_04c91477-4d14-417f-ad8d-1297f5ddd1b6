"""
ASGI config for SageBase_Backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
import sys
import asyncio
import logging
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
import integrations.github.api.routing
import integrations.slack.api.slack_routing
import Notifications.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')

# Setup logging
logger = logging.getLogger(__name__)

# Combine WebSocket URL patterns from GitHub, Slack, and Notifications
websocket_urlpatterns = (
    integrations.github.api.routing.websocket_urlpatterns +
    integrations.slack.api.slack_routing.websocket_urlpatterns +
    Notifications.routing.websocket_urlpatterns
)

# Discord bot instance (global to prevent multiple instances)
discord_bot_instance = None
discord_bot_task = None

def should_use_file_locking():
    """Always use file locking for Discord bot to ensure single instance."""
    # Always return True to ensure single bot instance across all scenarios
    return True

def acquire_bot_lock():
    """Acquire a file lock to ensure only one Discord bot instance across all workers."""
    import tempfile
    import time
    
    try:
        # Create a lock file in temp directory
        lock_file_path = os.path.join(tempfile.gettempdir(), 'sagebase_discord_bot.lock')
        
        logger.debug(f" Attempting to acquire bot lock: {lock_file_path}")
        
        # Try fcntl locking first (Unix/Linux/Mac)
        try:
            import fcntl
            lock_file = open(lock_file_path, 'w')
            
            # Try to acquire exclusive lock (non-blocking)
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            
            # Write process info to lock file
            lock_file.write(f"PID: {os.getpid()}\nTimestamp: {time.time()}\n")
            lock_file.flush()
            
            logger.debug(f"✅ fcntl lock acquired successfully by PID {os.getpid()}")
            return lock_file
            
        except ImportError:
            # fcntl not available on Windows, use file-based locking
            logger.debug("fcntl not available, using file-based locking")
            return acquire_bot_lock_windows(lock_file_path)
        except (IOError, OSError) as e:
            logger.debug(f"fcntl lock failed: {e}")
            # Lock is already held by another process
            return None
            
    except Exception as e:
        logger.error(f"❌ Error in acquire_bot_lock: {e}")
        # Return True to allow bot to start (fail-safe)
        return True

def acquire_bot_lock_windows(lock_file_path):
    """Windows-compatible lock using file existence check."""
    import time
    
    try:
        # Check if lock file exists and is recent (less than 60 seconds old)
        if os.path.exists(lock_file_path):
            try:
                stat = os.stat(lock_file_path)
                age = time.time() - stat.st_mtime
                if age < 60:
                    logger.debug(f"Lock file exists and is recent ({age:.1f}s old)")
                    return None  # Lock is active
                else:
                    logger.debug(f"Lock file is stale ({age:.1f}s old), removing")
                    os.remove(lock_file_path)
            except OSError:
                pass
        
        # Create lock file
        with open(lock_file_path, 'w') as f:
            f.write(f"PID: {os.getpid()}\nTimestamp: {time.time()}\n")
        
        logger.debug(f" File-based lock acquired successfully by PID {os.getpid()}")
        return lock_file_path
        
    except (IOError, OSError) as e:
        logger.debug(f"File-based lock failed: {e}")
        return None

async def start_discord_bot():
    """Start the Discord bot as a background task with multi-worker support."""
    global discord_bot_instance, discord_bot_task
    
    # Import Django settings
    from django.conf import settings
    
    # Check if auto-start is enabled
    if not getattr(settings, 'DISCORD_BOT_AUTO_START', False):
        logger.info("🤖 Discord bot auto-start is disabled")
        return
    
    # Check if bot is already running in this process
    if discord_bot_instance and not discord_bot_instance.is_closed():
        logger.info("🤖 Discord bot is already running in this process")
        return
    
    # Get bot token first
    bot_token = os.getenv('DISCORD_BOT_TOKEN')
    if not bot_token:
        logger.warning("🤖 Discord bot token not found, skipping bot startup")
        return
    
    # Always use file locking to ensure only one bot instance
    logger.info(" Using file locking to ensure single Discord bot instance")
    should_start_bot = True
    
    if should_use_file_locking():
        bot_lock = acquire_bot_lock()
        
        if bot_lock is None:
            logger.info("🤖 Discord bot is already running in another process")
            logger.info("   This process will handle Django requests only")
            should_start_bot = False
        elif bot_lock is True:
            logger.warning("⚠️  File locking failed, but continuing anyway (fail-safe mode)")
            should_start_bot = True
        else:
            logger.info(" File lock acquired, this process will run the Discord bot")
            should_start_bot = True
    
    if not should_start_bot:
        return
    
    try:
        # Add the messaging directory to Python path
        sys.path.append(os.path.join(settings.BASE_DIR, 'messaging', 'discord'))
        
        # Set environment variables for the bot
        os.environ['DISCORD_WEBHOOK_URL'] = os.getenv('DISCORD_WEBHOOK_URL')
        os.environ['DISCORD_WEBHOOK_SECRET'] = os.getenv('DISCORD_WEBHOOK_SECRET', 'default_secret_key')
        
        # Import and start the bot
        from discord_bot import DiscordBot
        
        discord_bot_instance = DiscordBot()
        
        # Log startup info
        logger.info("🤖 Starting Discord bot with file locking protection...")
        logger.info(f"   Process ID: {os.getpid()}")
        logger.info(f"   Only one bot instance will run across all processes")
        
        # Start the bot in a separate task
        discord_bot_task = asyncio.create_task(discord_bot_instance.start(bot_token))
        
        logger.info("✅ Discord bot started successfully")
        
    except ImportError as e:
        logger.error(f"❌ Failed to import Discord bot: {e}")
        logger.error(f"   Make sure discord.py is installed: pip install discord.py")
    except Exception as e:
        logger.error(f"❌ Failed to start Discord bot: {e}")
        logger.error(f"   Check bot token and network connection")

class DiscordASGIApplication:
    """ASGI application wrapper that manages Discord bot lifecycle."""
    
    def __init__(self, app):
        self.app = app
        self.bot_started = False
    
    async def __call__(self, scope, receive, send):
        # Start Discord bot on first request (only once)
        if not self.bot_started:
            self.bot_started = True
            await start_discord_bot()
        
        # Handle the request normally
        await self.app(scope, receive, send)

# Create the base ASGI application (same as team's existing structure)
base_application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    # "websocket": URLRouter(websocket_urlpatterns),  # WebSocket disabled for now
})

# Wrap with Discord bot lifecycle management
application = DiscordASGIApplication(base_application)

# Optional: add lifespan shutdown cleanup for MCP agents
try:
    from knowledge_spaces_Q_A.chat_orchestrator import _GITHUB_AGENT_CACHE  # type: ignore
    import asyncio

    async def _shutdown_cleanup():
        try:
            for agent in list(_GITHUB_AGENT_CACHE.values()):
                cleanup = getattr(agent, "mcp_cleanup", None)
                if cleanup is not None:
                    await cleanup()
            _GITHUB_AGENT_CACHE.clear()
            logger.info("[GITHUB] Cleaned up MCP agents on shutdown")
        except Exception as e:
            logger.warning(f"[GITHUB] MCP cleanup on shutdown failed: {e}")

    # Expose a callable for servers that support lifespan
    application.mcp_shutdown_cleanup = _shutdown_cleanup  # type: ignore[attr-defined]
except Exception:
    pass
