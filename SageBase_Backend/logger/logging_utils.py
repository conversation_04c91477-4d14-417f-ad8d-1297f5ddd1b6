"""
logging_utils.py - Generic colored logging setup for any Python project.

Usage:
    from logger.logging_utils import setup_colored_logging
    setup_colored_logging(level="INFO")
"""
import logging

def setup_colored_logging(level="INFO"):
    """
    Setup colored logging using colorlog if available, otherwise fallback to basic logging.
    Args:
        level (str): Logging level as string (e.g., 'DEBUG', 'INFO').
    Returns:
        bool: True if colorlog is used, False if fallback to basic logging.
    """
    try:
        import colorlog
        root_logger = logging.getLogger()
        
        # Clear all existing handlers
        if root_logger.handlers:
            root_logger.handlers.clear()
        
        # Clear handlers from common loggers that might interfere
        for logger_name in ['uvicorn', 'uvicorn.access', 'uvicorn.error', 'uvicorn.asgi', 'discord', 'discord_bot']:
            logger = logging.getLogger(logger_name)
            if logger.handlers:
                logger.handlers.clear()
            logger.propagate = True  # Ensure propagation to root logger
        
        formatter = colorlog.ColoredFormatter(
            "%(log_color)s%(levelname)-8s%(reset)s %(pathname)s:%(lineno)s: %(message_log_color)s%(message)s",
            datefmt=None,
            reset=True,
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            },
            secondary_log_colors={
                'message': {
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red',
                }
            }
        )
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        root_logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        root_logger.addHandler(handler)
        return True
    except ImportError:
        root_logger = logging.getLogger()
        if not root_logger.handlers:
            logging.basicConfig(
                level=getattr(logging, level.upper(), logging.INFO),
                format='%(levelname)-8s %(name)s:%(filename)s:%(lineno)s: %(message)s'
            )
        return False 