"""
Custom exception handler middleware for enhanced stack tracing
"""
import logging
import traceback
import sys
from django.http import HttpResponse
from django.conf import settings

logger = logging.getLogger(__name__)

class ExceptionLoggingMiddleware:
    """
    Middleware to log exceptions with full stack traces
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, request, exception):
        """
        Log exceptions with full stack trace
        """
        # Get the full stack trace
        exc_type, exc_value, exc_traceback = sys.exc_info()
        
        # Format the stack trace
        stack_trace = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # Log the exception with full context
        logger.error(
            f"Exception occurred while processing request: {request.method} {request.path}",
            extra={
                'request_method': request.method,
                'request_path': request.path,
                'request_user': getattr(request.user, 'email', 'anonymous') if hasattr(request, 'user') else 'anonymous',
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'stack_trace': stack_trace,
            },
            exc_info=True  # This will include the full stack trace
        )
        
        # Return None to let Django handle the exception normally
        return None


def log_exception_with_context(logger_name, exception, context=None):
    """
    Utility function to log exceptions with context and stack traces
    
    Args:
        logger_name (str): Name of the logger to use
        exception (Exception): The exception to log
        context (dict, optional): Additional context information
    """
    logger = logging.getLogger(logger_name)
    
    # Get the full stack trace
    exc_type, exc_value, exc_traceback = sys.exc_info()
    stack_trace = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    
    # Prepare log message
    log_message = f"Exception: {type(exception).__name__}: {str(exception)}"
    
    if context:
        log_message += f" | Context: {context}"
    
    # Log with full stack trace
    logger.error(log_message, exc_info=True, extra={
        'exception_type': type(exception).__name__,
        'exception_message': str(exception),
        'stack_trace': stack_trace,
        'context': context or {},
    })


def setup_exception_logging():
    """
    Setup enhanced exception logging for the entire application
    """
    # Configure the root logger to capture unhandled exceptions
    import logging
    
    def handle_exception(exc_type, exc_value, exc_traceback):
        """Handle unhandled exceptions"""
        if issubclass(exc_type, KeyboardInterrupt):
            # Call the default handler for keyboard interrupts
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Log the unhandled exception
        logger = logging.getLogger('unhandled_exceptions')
        logger.critical(
            "Unhandled exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    # Set the custom exception handler
    sys.excepthook = handle_exception
    
    # Create a logger for unhandled exceptions
    unhandled_logger = logging.getLogger('unhandled_exceptions')
    unhandled_logger.setLevel(logging.CRITICAL)
    
    # Add handlers if they don't exist
    if not unhandled_logger.handlers:
        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.CRITICAL)
        unhandled_logger.addHandler(console_handler)
        
        # Add file handler if logs directory exists
        try:
            file_handler = logging.FileHandler('logs/unhandled_exceptions.log')
            file_handler.setLevel(logging.CRITICAL)
            unhandled_logger.addHandler(file_handler)
        except Exception:
            pass  # File handler is optional 