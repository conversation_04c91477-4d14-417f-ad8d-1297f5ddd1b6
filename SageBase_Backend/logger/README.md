# Enhanced Logging with Stack Tracing

This module provides enhanced logging capabilities with automatic stack tracing for better debugging.

## Features

- ✅ **Automatic stack traces** for all exceptions
- ✅ **File-based logging** with separate error logs
- ✅ **Context-aware logging** with additional metadata
- ✅ **Middleware integration** for Django request/response logging
- ✅ **Unhandled exception capture** with full stack traces

## Log Files

The enhanced logging creates the following log files in the `logs/` directory:

- `django.log` - All application logs
- `errors.log` - Error and exception logs with stack traces
- `unhandled_exceptions.log` - Unhandled exceptions

## Usage

### Basic Usage

```python
from logger.enhanced_logging import get_logger, log_exception, error

# Get a logger
logger = get_logger('my_module')

# Log with context
logger.info("Processing request", extra={'user_id': 123, 'action': 'login'})

# Log exceptions with stack traces
try:
    # Your code here
    pass
except Exception as e:
    log_exception('my_module', e, {'user_id': 123})
```

### Convenience Functions

```python
from logger.enhanced_logging import info, error, warning, debug, critical

# Simple logging with context
info('my_module', 'User logged in', {'user_id': 123, 'ip': '***********'})

# Error logging with exception
try:
    # Your code here
    pass
except Exception as e:
    error('my_module', 'Failed to process request', e, {'user_id': 123})

# Critical errors
critical('my_module', 'Database connection lost', e, {'db_host': 'localhost'})
```

### Django Middleware

The `ExceptionLoggingMiddleware` automatically logs all exceptions that occur during request processing with:

- Request method and path
- User information
- Full stack trace
- Exception details

### Unhandled Exceptions

All unhandled exceptions are automatically captured and logged to `logs/unhandled_exceptions.log` with full stack traces.

## Configuration

The enhanced logging is automatically configured in `SageBase_Backend/settings.py`:

1. **File handlers** for detailed logging
2. **Error-specific logging** with stack traces
3. **Middleware integration** for request/response logging
4. **Unhandled exception capture**

## Example Output

```
2025-08-04 22:17:46,386 ERROR    my_module:views.py:35: Failed to process request
Traceback (most recent call last):
  File "/app/my_module/views.py", line 33, in my_view
    result = process_data(data)
  File "/app/my_module/utils.py", line 45, in process_data
    return data['invalid_key']
KeyError: 'invalid_key'
```

## Migration from Basic Logging

Replace:
```python
logger.error("Something went wrong")
```

With:
```python
from logger.enhanced_logging import error
error('my_module', 'Something went wrong', exception, {'context': 'data'})
```

This provides much more detailed information for debugging. 