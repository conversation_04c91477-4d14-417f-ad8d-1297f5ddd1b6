"""
Enhanced logging utilities for easy stack tracing
"""

import logging
from .exception_handler import log_exception_with_context

def get_logger(name):
    """
    Get a logger with enhanced capabilities
    
    Args:
        name (str): Logger name
        
    Returns:
        logging.Logger: Configured logger
    """
    return logging.getLogger(name)


def log_error(logger_name, message, exception=None, context=None):
    """
    Log an error with full stack trace
    
    Args:
        logger_name (str): Name of the logger
        message (str): Error message
        exception (Exception, optional): The exception that occurred
        context (dict, optional): Additional context information
    """
    logger = logging.getLogger(logger_name)
    
    if exception:
        log_exception_with_context(logger_name, exception, context)
    else:
        logger.error(message, extra={'context': context or {}})


def log_exception(logger_name, exception, context=None):
    """
    Log an exception with full stack trace
    
    Args:
        logger_name (str): Name of the logger
        exception (Exception): The exception to log
        context (dict, optional): Additional context information
    """
    log_exception_with_context(logger_name, exception, context)


def log_with_context(logger_name, level, message, context=None):
    """
    Log a message with context information
    
    Args:
        logger_name (str): Name of the logger
        level (str): Log level ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        message (str): Log message
        context (dict, optional): Additional context information
    """
    logger = logging.getLogger(logger_name)
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    logger.log(log_level, message, extra={'context': context or {}})


# Convenience functions for common logging patterns
def debug(logger_name, message, context=None):
    """Log debug message with context"""
    log_with_context(logger_name, 'DEBUG', message, context)


def info(logger_name, message, context=None):
    """Log info message with context"""
    log_with_context(logger_name, 'INFO', message, context)


def warning(logger_name, message, context=None):
    """Log warning message with context"""
    log_with_context(logger_name, 'WARNING', message, context)


def error(logger_name, message, exception=None, context=None):
    """Log error message with optional exception and context"""
    log_error(logger_name, message, exception, context)


def critical(logger_name, message, exception=None, context=None):
    """Log critical message with optional exception and context"""
    logger = logging.getLogger(logger_name)
    
    if exception:
        log_exception_with_context(logger_name, exception, context)
    else:
        logger.critical(message, extra={'context': context or {}}) 