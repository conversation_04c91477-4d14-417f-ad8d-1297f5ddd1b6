# SageBase Backend Installation Guide

## Database Setup

When setting up a new database, follow this **exact order** for migrations to avoid dependency conflicts:

### 1. Clean Start (if needed)
```bash
# Remove all existing migrations
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
```

### 2. Create Migrations (in order)
```bash
python manage.py makemigrations integrations
python manage.py makemigrations knowledge_map
python manage.py makemigrations knowledge_spaces_Q_A
```

### 3. Apply Migrations
```bash
python manage.py migrate
```

## Why This Order Matters

The migration order is critical due to model dependencies:

- **`integrations`** must be first because it contains `Company` and `User` models that other apps depend on
- **`knowledge_map`** must be second because `integrations` has models that reference `knowledge_map.Project`
- **`knowledge_spaces_Q_A`** must be last because it depends on both `integrations` and `knowledge_map`

## App Structure

- **`integrations`** - Core models: `Company`, `User`, `ProjectContributor`, `ProjectMetrics`
- **`knowledge_map`** - Knowledge map projects: `Project` model
- **`knowledge_spaces_Q_A`** - Q&A system: `Knowledge_Space` and `QA` models

## Troubleshooting

If you encounter circular dependency errors, ensure:
1. All imports use string references (e.g., `'integrations.Company'`) instead of direct imports
2. Migrations are created in the correct order
3. No old migration files remain from previous setups

## Quick Reference

For a complete fresh start:
```bash
# Clean everything
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
python manage.py flush --noinput

# Create and apply migrations
python manage.py makemigrations integrations
python manage.py makemigrations knowledge_map
python manage.py makemigrations knowledge_spaces_Q_A
python manage.py migrate
```

