# Development Configuration
DJANGO_ENV=development
DEBUG=True

# Azure PostgreSQL Database Configuration
DATABASE_URL=postgres://wissem:<EMAIL>:5432/sageBase_test_azure?sslmode=require


REDIS_URL=redis://localhost:6379/0

#Github OAuth - will be deprecated
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=7419ea81049cae46715570300257eca52bbaaf9a
GEMINI_API_KEY=AIzaSyBlz6C2q29A0eR67hir6kWE2cDoH_IrcAc

# Github App
GITHUB_APP_SLUG=sagabaseTestWissem
GITHUB_APP_ID=1790716
GITHUB_APP_CLIENT_ID=********************
GITHUB_APP_CLIENT_SECRET=2b91db98c66e95f249e1c3974bb622e5e4dbb30c
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----MIIEowIBAAKCAQEAuhNUrOraFGupgoB+oDzqkvPfzaQeBIVmKm7vWDRBQDXBW01AhZgqq+syKSuW1yMLIg21AZ0U18KslQwazNfDTUcRM29VUJODY58cgzG/8tGnaTxFqfrTCAo+dXPrrH5jESwqRMDHgxkL1T3GptcZhlnXl9cfA+pZv7JUvKWT3XxEYuaFCIqiRiMdJ9emKBoTByIPPvxYBF4jwKdVMoPIWKMrLze3+akKtyA35bVHnaKPMcr6RaTyj3WcgjyMEh6YGs4RcZqvEdmNeDChUpMNVng6nxEiVirwbfqNcNz/K4MdWfJjSu/ZSLbWhc2uEiUGLnZhldsHZVZyjEDnO/C5rQIDAQABAoIBABhtDzF/xs7lbyQFcBnixTwzLU7WW2ZmiInLdDnU6jfelppAotamqftVbYlAApYNMJnwyLEEiMbl9DfAlggLzsRnLtfcpotkQdwi9+A0+KiMYviKg+6TUX59scNDS02ibajEbmwGDmqkMAx3BovCMsSTKJzzDUw7SDrE0yDMb4OkYdhZk8H/zJ44KuS3Uh8I2tT7nUF9LrWa0eGZnMC0RFLkXCMWQu/YPKJ/ckRplc+qdi5MMYna3zZIHeruyP13fO66u3p5aF2QhTbxd2shKbTd4Flbr7LNAwL/238NGcM7Zqdp5Slc7SOopU+eT9ShxjPtVnrGMciZVsNpBHYjU1UCgYEA8Frf/FbHQabNNlXoD2j5q/XP95MX99cz6En4zhKvw164D0orvd1G5pqbyatGH7RimUoBTvSAadAKanG+0qwf9VGwMYSiJ1lBEJQ26DM8K9G1Rwbii+5VlJhHeO5raM6ggV4ItaBrRMn9H9ywBxVvSYJJ2Ru+q6FzsRUrKZ7r23cCgYEAxi/6CGUAzrF2x6NcFoNJLHsX/+R6G3lEjYVllTCj7mLKrEM+652zz1dndEXswPlXHbo/klHQngT7H+Gh8NurbFp9ax+Ab3hLRlyevrqQjhQz9mJ4LNAj+4LFPzaxuCguObO0sZYaGCcRShlXR0KmmHeTv43R1IaF5yUj9+ce1PsCgYAmpOXIY4dr5c6uwscp2x3xaPdD/fFnlsEwwtvu/hAmYE9Fng5EW5RvtUIA/bmrlUwZC04EjMksuhdCoAEnTCdjQPwMKiAMJYyRhtMBKGOvQ+tafR/lka9O0eED74lzc21Y/251DVLR34wEGG5w5SBceEc5Uf2pCK9aNGYZLgUL5wKBgE58fYaJiXJRsXqKZVO9FmN5NB3w+7KZqd5p77rPknw7v4vM7cQdpjH8qUnQz1v04t0w/zARGo8Z5eHQV3Otawv0IiskRJbgit8Imw1pkQnTUmjGwlTs8PMP7NGF9Ye60eOkGXEiwp7CCgTQWFIuQQ+se2Sm42xJAgpyGPZ6Hym1AoGBAJNH3oEBJF9T03cUJt0EsJTmNCYIhnQDAdbU8oTIsmkgkl8bHCgK0o2B1cHl7jxESkNO0jUUGmffRkWUJpBlioX0wtmuNAGrHZEJlda+xLvkNB77OVjHVhnkddpbk1zKrCmDDqMX6vEPyFZBsoUbmU8+CZGuoYTa8YAxNCEjWhTh-----END RSA PRIVATE KEY-----"
GITHUB_APP_WEBHOOK_SECRET="1test$2pass%3run!!"

# Github App
#GITHUB_APP_SLUG=sagebase-dev
#GITHUB_APP_ID=1620329
#GITHUB_APP_CLIENT_ID=********************
#GITHUB_APP_CLIENT_SECRET=a0525a224bf1145c3ad71ee0eb79931ee2d1c5ea
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
#GITHUB_APP_WEBHOOK_SECRET="1test$2pass%3run!!"


# API Keys
GEMINI_API_KEY=AIzaSyBlz6C2q29A0eR67hir6kWE2cDoH_IrcAc
OPENAI_API_KEY=********************************************************************************************************************************************************************

# ChromaDB Configuration
ANONYMIZED_TELEMETRY=False
CHROMA_TELEMETRY_CAPTURE=False
CHROMA_EMBEDDING_TYPE=azure
CHROMA_EMBEDDING_MODEL=text-embedding-3-small

CHROMA_PERSIST_DIR=./chroma_db
#chroma on the cloud, on the test server, opne to external commands
CHROMA_PORT=8011
CHROMA_HOST=http://sagebasetestserverazure.sagebase.tech
#when running locally, using your own laptop for chroma, you need to enable these and also run teh chroma server
# source .venv/bin/activate && chroma run --path ./chroma_db --host localhost --port 8000
#CHROMA_PORT=8000
#CHROMA_HOST=http://localhost



# Base URLs
BACKEND_BASE_URL=https://f5432c836377.ngrok-free.app
FRONTEND_BASE_URL=http://localhost:3000


 
# ✅ FIXED: Use {BASE_URL} placeholder for dynamic URL generation
SLACK_WEBHOOK_URL={BASE_URL}/api/integrations/slack/webhook/receive/
SLACK_REDIRECT_URI={BASE_URL}/api/integrations/slack/callback/
DJANGO_WEBHOOK_URL=

# Discord Bot Configuration
DISCORD_BOT_TOKEN=MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls
DISCORD_CLIENT_ID=1391784811173122179
DISCORD_CLIENT_SECRET=g2oUQ7o4-UNx2Fe_5oISgNr8zXbOYA1S
DISCORD_REDIRECT_URI=https://123700d18b9e.ngrok-free.app/api/integrations/discord/callback/ # set on starting
DISCORD_BOT_AUTO_START=true

# Email settings
EMAIL_HOST=send.one.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=SageBase2025*
DEFAULT_FROM_EMAIL=SageBase <<EMAIL>>


DJANGO_LOG_LEVEL=DEBUG

# Slack Configuration/ sagebase test app
SLACK_CLIENT_ID=9008663006947.9028923254790
SLACK_CLIENT_SECRET=0f1a20838581f8dd05aca15f48e27f6c
SLACK_SIGNING_SECRET=13b5d45f1e943f2be059bb2407d0d472

SLACK_APP_TOKEN=xapp-1-A0935J268UE-9118162536149-153dc6c7a64b61f4c780edc614ef92a6858e52627f0abdb17ddfffb0bd39372e


azure_gpt4_1_=gpt-4.1_france_SageBase
azure_embedding_3_small=text-embedding-3-small
AZURE_GPT4O=gpt-4o_france_sagebase
AZURE_OPENAI_ENDPOINT=https://sagebasemodels.openai.azure.com/
AZURE_ENDPOINT=https://sagebasemodels.openai.azure.com/openai/deployments/gpt-4.1_france_SageBase/chat/completions?api-version=2025-01-01-preview
AZURE_OPENAI_API_KEY=FFjJUbvnHNXAcFsMSOCKSovwgxRsEkfBFN6r2MlYzjmGi0ROXqLmJQQJ99BHAC5T7U2XJ3w3AAABACOGvgHv
api_version="2024-12-01-preview"
# Azure OpenAI Embedding Configuration
AZURE_EMBEDDING_ENDPOINT=https://sagebasemodels.openai.azure.com/
AZURE_EMBEDDING_DEPLOYMENT=text-embedding-3-small_france_sagebBase
AZURE_EMBEDDING_API_VERSION=2023-05-15


# Required variables:

AZURE_OPENAI_DEPLOYMENT=gpt-4.1_france_SageBase
AZURE_OPENAI_API_VERSION=2024-12-01-preview
# Optional variables:
AZURE_OPENAI_MODEL=gpt-4.1_france_SageBase
AZURE_GPT4O_MINI=o4-mini_france_sagebase
AZURE_GPT4O=gpt-4o_france_sagebase


# Disable polling
GOOGLE_DRIVE_POLLING_ENABLED=false
GDRIVE_REDIRECT_URI=https://123700d18b9e.ngrok-free.app/api/integrations/google-drive/callback/
GDRIVE_WEBHOOK_URL=https://123700d18b9e.ngrok-free.app/api/integrations/google-drive/webhook/
DISCORD_WEBHOOK_URL=http://localhost:8001/api/integrations/discord/webhook/


CONFLUENCE_CLIENT_ID=jz5oEkFDgcWxOFkOOo9qSp4si5vPU93x
CONFLUENCE_CLIENT_SECRET=ATOA6lolX8_m45bhgcNwnzJ2ViGlKdzwreH-GXABO0_psVi6iGSxjenw3L624famM4XrD89D0D6E
CONFLUENCE_REDIRECT_URI={your_ngrok_here}/api/integrations/confluence/callback/

#serper api https://serper.dev/api-keys
SERPER_API_KEY=154d128c185d5df7e87272b2f28df8caf7007106

# disable it when you don't want an interruption each start
BACKEND_NGROK_BASE_URL=https://f5432c836377.ngrok-free.app

