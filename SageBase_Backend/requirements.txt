aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.15
aiosignal==1.4.0
annotated-types==0.7.0
anyio==4.10.0
asgiref==3.9.1
attrs==25.3.0
autobahn==24.4.2
automat==25.4.16
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
black==25.1.0
brotli==1.1.0
build==1.3.0
cachetools==5.5.2
certifi==2025.8.3
cffi==1.17.1
channels==4.3.1
channels-redis==4.3.0
charset-normalizer==3.4.2
chromadb==1.0.16
click==8.2.1
colorama==0.4.6
colored==2.3.1
coloredlogs==15.0.1
colorlog==6.9.0
constantly==23.10.4
cryptography==45.0.6
daphne==4.2.1
debugpy==1.8.16
discord-py==2.5.2
distro==1.9.0
dj-database-url==3.0.1
django==4.2
django-cors-headers==4.4.0
django-environ==0.11.2
djangorestframework==3.15.2
durationpy==0.10
fastapi==0.116.1
feedparser==6.0.11
ffmpy==0.6.1
filelock==3.18.0
flatbuffers==25.2.10
frozenlist==1.7.0
fsspec==2025.7.0
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.178.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
gradio==5.42.0
gradio-client==1.11.1
griffe==1.11.0
groovy==0.1.2
grpcio==1.74.0
grpcio-status==1.71.2
h11==0.16.0
hf-xet==1.1.7
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.34.4
humanfriendly==10.0
hyperlink==21.0.0
idna==3.10
importlib-metadata==8.7.0
importlib-resources==6.5.2
incremental==24.7.2
jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kubernetes==33.1.0
lxml==6.0.0
markdown-it-py==3.0.0
markupsafe==3.0.2
mcp==1.12.4
mdurl==0.1.2
mmh3==5.2.0
mpmath==1.3.0
msgpack==1.1.1
multidict==6.6.3
mypy-extensions==1.1.0
nltk==3.9.1
numpy==2.3.2
oauthlib==3.3.1
onnxruntime==1.22.1
openai==1.99.1
openai-agents==0.2.5
opentelemetry-api==1.36.0
opentelemetry-exporter-otlp-proto-common==1.36.0
opentelemetry-exporter-otlp-proto-grpc==1.36.0
opentelemetry-proto==1.36.0
opentelemetry-sdk==1.36.0
opentelemetry-semantic-conventions==0.57b0
orjson==3.11.1
overrides==7.7.0
packaging==25.0
pandas==2.3.1
pathspec==0.12.1
pdfminer-six==20250506
pdfplumber==0.11.7
pillow==11.3.0
pip==24.0
platformdirs==4.3.8
posthog==5.4.0
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1-modules==0.4.2
pybase64==1.4.2
pycparser==2.22
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
pydub==0.25.1
pygments==2.19.2
pyjwt==2.10.1
pymupdf==1.26.3
pyopenssl==25.1.0
pyparsing==3.2.3
pypdf2==3.0.1
pypdfium2==4.30.0
pypika==0.48.9
pyproject-hooks==1.2.0
python-dateutil==2.9.0.post0
python-docx==1.2.0
python-dotenv==1.1.1
python-multipart==0.0.20
pytz==2025.2
pyyaml==6.0.2
redis==6.4.0
referencing==0.36.2
regex==2025.7.34
requests==2.32.4
requests-oauthlib==2.0.0
rich==14.1.0
rpds-py==0.27.0
rsa==4.9.1
ruff==0.12.8
safehttpx==0.1.6
semantic-version==2.10.0
service-identity==24.2.0
setuptools==65.5.1
sgmllib3k==1.0.0
shellingham==1.5.4
six==1.17.0
slack-sdk==3.36.0
sniffio==1.3.1
soupsieve==2.7
sqlparse==0.5.3
sse-starlette==3.0.2
starlette==0.47.2
sympy==1.14.0
tenacity==9.1.2
tiktoken==0.10.0
tokenizers==0.21.4
tomlkit==0.13.3
tqdm==4.67.1
twisted==25.5.0
txaio==25.6.1
typer==0.16.0
types-requests==2.32.4.20250611
typing-extensions==4.14.1
typing-inspection==0.4.1
tzdata==2025.2
uritemplate==4.2.0
urllib3==2.5.0
uv==0.8.7
uvicorn==0.35.0
#uvloop==0.21.0
watchdog==6.0.0
watchfiles==1.1.0
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
yarl==1.20.1
zipp==3.23.0
zope-interface==7.2
langchain==0.3.27