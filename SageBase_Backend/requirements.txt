django==4.2
django-environ==0.11.2
djangorestframework==3.15.2
django-cors-headers==4.4.0
requests
httpx
google-generativeai
google-auth
google-auth-oauthlib
google-auth-httplib2
google-api-python-client
python-dotenv
psycopg2-binary
dj-database-url
channels
uvicorn
websockets
chromadb>=0.5.0
numpy
watchdog
openai-agents
pydantic>=2.0.0
openai>=1.0.0
gradio>=4.0.0

# Document Processing Dependencies
# For PDF parsing (PyPDF2 is primary, pdfplumber as backup)
PyPDF2>=3.0.0
pdfplumber>=0.7.0

# For DOCX/Word document parsing
python-docx>=0.8.11

# For HTML parsing
beautifulsoup4>=4.12.0
lxml>=4.9.0

# For better text processing (optional but recommended)
nltk>=3.8

# For advanced PDF processing (optional)
pymupdf>=1.23.0

slack_sdk
daphne
aiohttp
debugpy  # For VS Code debugging support
discord.py==2.5.2  # Discord bot integration
redis>=4.0.0

PyJWT
colorlog
colored
tiktoken
channels_redis
feedparser

black