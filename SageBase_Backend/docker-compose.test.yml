
services:
  web:
    build: .
    ports:
      - "5678:5678"
      - "5679:5679"
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - DEBUG=0
    env_file:
      - .env.test
    depends_on:
      - redis
    command: sh -c "python manage.py collectstatic --noinput && python manage.py migrate && python -Xfrozen_modules=off manage.py runserver 0.0.0.0:8000"
 
  chromadb:
    image: chromadb/chroma:latest
    container_name: sagebase_backend-chromadb-1
    ports:
      - "8011:8000"
    networks:
      - sagebase-network
      - default
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    networks:
      - default
      - sagebase-network
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: sagebase
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - default
      - sagebase-network
    restart: unless-stopped

  frontend:
    image: node:22-alpine
    container_name: sagebase_frontend
    working_dir: /app
    volumes:
      - /data/SageBase_FrontEnd-pe:/app:rw
    env_file:
      - /data/SageBase_FrontEnd-pe/.env.test
    command: ./start.test.sh
    networks:
      - sagebase-network
      - default
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8000:8000"
    volumes:
      - /data/nginx/conf.d:/etc/nginx/conf.d
      - /etc/letsencrypt/live/sagebasetestserverazure.sagebase.tech/fullchain.pem:/etc/letsencrypt/live/sagebasetestserverazure.sagebase.tech/fullchain.pem:ro
      - /etc/letsencrypt/live/sagebasetestserverazure.sagebase.tech/privkey.pem:/etc/letsencrypt/live/sagebasetestserverazure.sagebase.tech/privkey.pem:ro
      - /data/SageBase_BackEnd/staticfiles:/data/SageBase_BackEnd/staticfiles:ro    
    networks:
      - sagebase-network
      - default
    depends_on:
      - web
      - frontend
      - db
      - redis
      - chromadb
    

volumes:
  postgres_data:

networks:
  sagebase-network:
    driver: bridge