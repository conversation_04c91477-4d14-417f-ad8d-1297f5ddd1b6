# SageBase Backend - Essential Steps

## 🚀 Quick Start

1. **Start All Services**
   - Press `F5` in VS Code
   - Select **"Django + Document Embedding (Local)"**
   - This starts: Django server + Slack listener + Document embedding monitor

2. **Add Documents**
   - Drop any file in `vectordb/tests/sageBase_doc_use_test/`
   - Files are automatically embedded and indexed
   - Supported: PDF, TXT, DOCX, HTML, JSON, CSV

3. **Ask Questions**
   - Open the frontend application
   - Ask questions in the chat interface

## 📁 Default Folder
- `vectordb/tests/sageBase_doc_use_test/`
- All files in this folder are automatically processed
- Chunk size: 400 characters (optimized for Q&A)pur
