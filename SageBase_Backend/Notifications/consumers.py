import json
import logging
from typing import Optional, Set
from channels.generic.websocket import AsyncWebso<PERSON>Consumer
from asgiref.sync import sync_to_async

from .services import notification_service
from .types import NotificationPayload
from .redis_manager import redis_connection_manager

logger = logging.getLogger(__name__)


class GenericNotificationConsumer(AsyncWebsocketConsumer):
    """
    Generic WebSocket consumer for handling all types of notifications.
    Supports user-specific, group-based, and repository-based notifications.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_email: Optional[str] = None
        self.user_id: Optional[str] = None  # Keep for backward compatibility
        self.joined_groups: Set[str] = set()
        self.joined_repositories: Set[str] = set()
        self.use_redis: bool = False
    
    async def connect(self):
        """Accept WebSocket connection and register with notification service."""
        await self.accept()
        
        # Check if Redis is available
        self.use_redis = await sync_to_async(redis_connection_manager.is_available)()
        
        # Add to broadcast group by default
        await self.channel_layer.group_add("broadcast", self.channel_name)
        self.joined_groups.add("broadcast")
        
        # Register connection (with Redis if available, fallback to in-memory)
        if self.use_redis:
            await sync_to_async(redis_connection_manager.register_connection)(
                self.channel_name, 
                self.user_email,
                self.user_id
            )
        else:
            await sync_to_async(notification_service.register_connection)(
                self.channel_name, 
                self.user_email or self.user_id
            )
        
        # Log connection with user email if available
        user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
        logger.debug(f"WebSocket connected: {self.channel_name}{user_info} (Redis: {self.use_redis})")
        
        # Automatically subscribe to broadcast for all connections
        try:
            await self.channel_layer.group_add("broadcast", self.channel_name)
            self.joined_groups.add("broadcast")
            logger.debug(f"Auto-subscribed {self.channel_name}{user_info} to broadcast group")
        except Exception as e:
            logger.exception(f"Failed to auto-subscribe to broadcast: {e}")
        
        # Send welcome notification
        await self.send(text_data=json.dumps({
            "type": "connection.established",
            "message": "WebSocket connection established",
            "redis_available": self.use_redis,
            "instance_id": redis_connection_manager.instance_id if self.use_redis else "unknown"
        }))
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection and cleanup."""
        logger.debug(f"WebSocket disconnecting: {self.channel_name} (code: {close_code})")
        
        # Leave all groups
        for group in self.joined_groups:
            await self.channel_layer.group_discard(group, self.channel_name)
        
        # Leave all repository groups
        for repo in self.joined_repositories:
            await self.channel_layer.group_discard(f"repo_{repo}", self.channel_name)
        
        # Unregister connection (from Redis or in-memory)
        if self.use_redis:
            await sync_to_async(redis_connection_manager.unregister_connection)(self.channel_name)
        else:
            await sync_to_async(notification_service.unregister_connection)(self.channel_name)
        
        # Log disconnection with user email if available
        user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
        logger.debug(f"WebSocket disconnected: {self.channel_name}{user_info}")
        
        # Clean up broadcast subscription
        try:
            await self.channel_layer.group_discard("broadcast", self.channel_name)
            logger.debug(f"Unsubscribed {self.channel_name}{user_info} from broadcast group")
        except Exception as e:
            logger.exception(f"Failed to unsubscribe from broadcast: {e}")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            action = data.get("action")
            
            if action == "subscribe":
                await self._handle_subscribe(data)
            elif action == "unsubscribe":
                await self._handle_unsubscribe(data)
            elif action == "authenticate":
                await self._handle_authenticate(data)
            elif action == "ping":
                await self._handle_ping(data)
            else:
                await self._send_error(f"Unknown action: {action}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in WebSocket message: {e}")
            await self._send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
            await self._send_error("Internal server error")
    
    async def _handle_subscribe(self, data):
        """Handle subscription requests."""
        target_type = data.get("target_type")  # 'group', 'repository', 'user'
        target_id = data.get("target_id")
        
        if not target_type or not target_id:
            await self._send_error("Missing target_type or target_id")
            return
        
        if target_type == "group":
            await self._subscribe_to_group(target_id)
        elif target_type == "repository":
            await self._subscribe_to_repository(target_id)
        elif target_type == "user":
            await self._subscribe_to_user(target_id)
        else:
            await self._send_error(f"Invalid target_type: {target_type}")
    
    async def _handle_unsubscribe(self, data):
        """Handle unsubscription requests."""
        target_type = data.get("target_type")
        target_id = data.get("target_id")
        
        if not target_type or not target_id:
            await self._send_error("Missing target_type or target_id")
            return
        
        if target_type == "group":
            await self._unsubscribe_from_group(target_id)
        elif target_type == "repository":
            await self._unsubscribe_from_repository(target_id)
        else:
            await self._send_error(f"Cannot unsubscribe from target_type: {target_type}")
    
    async def _handle_authenticate(self, data):
        """Handle user authentication."""
        user_email = data.get("user_email")
        user_id = data.get("user_id")  # Backward compatibility
        token = data.get("token")  # For future JWT/token authentication
        
        # TODO: Add proper authentication/authorization logic here
        # For now, we accept email + optional token
        
        if user_email:
            self.user_email = user_email
            self.user_id = user_id  # Keep for backward compatibility
            
            # Re-register connection with user info
            if self.use_redis:
                await sync_to_async(redis_connection_manager.register_connection)(
                    self.channel_name, 
                    self.user_email,
                    self.user_id
                )
            else:
                await sync_to_async(notification_service.register_connection)(
                    self.channel_name, 
                    self.user_email
                )
            
            await self.send(text_data=json.dumps({
                "type": "authentication.success",
                "user_email": user_email,
                "user_id": user_id
            }))
            
            logger.debug(f"User {user_email} authenticated on WebSocket {self.channel_name}")
        elif user_id:
            # Backward compatibility for user_id only
            self.user_id = user_id
            
            if self.use_redis:
                await sync_to_async(redis_connection_manager.register_connection)(
                    self.channel_name, 
                    None,  # No email
                    self.user_id
                )
            else:
                await sync_to_async(notification_service.register_connection)(
                    self.channel_name, 
                    self.user_id
                )
            
            await self.send(text_data=json.dumps({
                "type": "authentication.success",
                "user_id": user_id
            }))
            
            logger.debug(f"User {user_id} authenticated on WebSocket {self.channel_name}")
        else:
            await self._send_error("Missing user_email or user_id")
    
    async def _handle_ping(self, data):
        """Handle ping/pong for connection health."""
        await self.send(text_data=json.dumps({
            "type": "pong",
            "timestamp": data.get("timestamp")
        }))
    
    async def _subscribe_to_group(self, group_name: str):
        """Subscribe to a group."""
        try:
            await self.channel_layer.group_add(group_name, self.channel_name)
            self.joined_groups.add(group_name)
            
            await sync_to_async(notification_service.add_to_group)(
                self.channel_name, 
                group_name
            )
            
            await self.send(text_data=json.dumps({
                "type": "subscription.success",
                "target_type": "group",
                "target_id": group_name
            }))
            
            user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
            logger.debug(f"Subscribed {self.channel_name}{user_info} to group {group_name}")
            
        except Exception as e:
            logger.exception(f"Error in _subscribe_to_group: {e}")
            await self._send_error("Internal server error")
    
    async def _unsubscribe_from_group(self, group_name: str):
        """Unsubscribe from a group."""
        try:
            await self.channel_layer.group_discard(group_name, self.channel_name)
            self.joined_groups.discard(group_name)
            
            await sync_to_async(notification_service.remove_from_group)(
                self.channel_name, 
                group_name
            )
            
            await self.send(text_data=json.dumps({
                "type": "unsubscription.success",
                "target_type": "group",
                "target_id": group_name
            }))
            
            user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
            logger.debug(f"Unsubscribed {self.channel_name}{user_info} from group {group_name}")
            
        except Exception as e:
            logger.exception(f"Error in _unsubscribe_from_group: {e}")
            await self._send_error("Internal server error")
    
    async def _subscribe_to_repository(self, repository: str):
        """Subscribe to repository notifications."""
        try:
            repo_group = f"repo_{repository}"
            await self.channel_layer.group_add(repo_group, self.channel_name)
            self.joined_repositories.add(repository)
            
            await sync_to_async(notification_service.add_to_repository)(
                self.channel_name, 
                repository
            )
            
            await self.send(text_data=json.dumps({
                "type": "subscription.success",
                "target_type": "repository",
                "target_id": repository
            }))
            
            user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
            logger.debug(f"Subscribed {self.channel_name}{user_info} to repository {repository}")
            
        except Exception as e:
            logger.exception(f"Error in _subscribe_to_repository: {e}")
            await self._send_error("Internal server error")
    
    async def _unsubscribe_from_repository(self, repository: str):
        """Unsubscribe from repository notifications."""
        try:
            repo_group = f"repo_{repository}"
            await self.channel_layer.group_discard(repo_group, self.channel_name)
            self.joined_repositories.discard(repository)
            
            await sync_to_async(notification_service.remove_from_repository)(
                self.channel_name, 
                repository
            )
            
            await self.send(text_data=json.dumps({
                "type": "unsubscription.success",
                "target_type": "repository",
                "target_id": repository
            }))
            
            user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
            logger.debug(f"Unsubscribed {self.channel_name}{user_info} from repository {repository}")
            
        except Exception as e:
            logger.exception(f"Error in _unsubscribe_from_repository: {e}")
            await self._send_error("Internal server error")
    
    async def _subscribe_to_user(self, user_identifier: str):
        """Subscribe to user-specific notifications (requires authentication)."""
        try:
            # Check if user_identifier is email or user_id
            is_email = "@" in user_identifier
            
            if is_email:
                if not self.user_email:
                    await self._send_error("Authentication required for email-based user subscriptions")
                    return
                
                if self.user_email != user_identifier:
                    await self._send_error("Can only subscribe to your own user notifications")
                    return
                
                # Encode email for group name (replace @ with _at_)
                encoded_email = user_identifier.replace("@", "_at_")
                user_group = f"user_email_{encoded_email}"
            else:
                # Backward compatibility for user_id
                if not self.user_id and not self.user_email:
                    await self._send_error("Authentication required for user subscriptions")
                    return
                
                if self.user_id and self.user_id != user_identifier:
                    await self._send_error("Can only subscribe to your own user notifications")
                    return
                
                user_group = f"user_id_{user_identifier}"
            
            await self.channel_layer.group_add(user_group, self.channel_name)
            self.joined_groups.add(user_group)
            
            # Note: notification_service doesn't have user-specific methods yet
            # This is handled directly through channel_layer groups
            
            await self.send(text_data=json.dumps({
                "type": "subscription.success",
                "target_type": "user",
                "target_id": user_identifier,
                "is_email": is_email
            }))
            
            user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
            logger.debug(f"Subscribed {self.channel_name}{user_info} to user {user_identifier}")
            
        except Exception as e:
            logger.exception(f"Error in _subscribe_to_user: {e}")
            await self._send_error("Internal server error")
    
    async def _send_error(self, message: str):
        """Send error message to client."""
        await self.send(text_data=json.dumps({
            "type": "error",
            "message": message
        }))
        logger.warning(f"Sent error to {self.channel_name}: {message}")
    
    # Notification handlers
    async def send_notification(self, event):
        """Handle notification events from the notification service."""
        notification = event.get("notification", {})
        
        await self.send(text_data=json.dumps({
            "type": "notification",
            **notification
        }))
        
        user_info = f" (User: {self.user_email})" if self.user_email else " (No user authenticated)"
        logger.debug(f"Sent notification to {self.channel_name}{user_info}: {notification.get('title', 'Unknown')}")
    
    # Backward compatibility handlers
    async def notify(self, event):
        """Handle legacy GitHub notification events."""
        await self.send_notification(event)
    
    async def platform_uninstalled(self, event):
        """Handle legacy platform uninstall events."""
        await self.send(text_data=json.dumps({
            "type": "platform.uninstalled",
            "platform": event.get("platform"),
            "message": event.get("message")
        }))