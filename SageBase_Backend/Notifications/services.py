import uuid
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from channels.layers import get_channel_layer
from django.conf import settings
from asgiref.sync import async_to_sync

from .types import (
    NotificationType, 
    NotificationLevel, 
    NotificationTarget, 
    NotificationPayload,
    ConnectionInfo
)
from .redis_manager import redis_connection_manager

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for sending notifications through WebSocket channels."""
    
    def __init__(self):
        self._channel_layer = None
        self._connections: Dict[str, ConnectionInfo] = {}  # Fallback for when Redis is not available
        self.use_redis = redis_connection_manager.is_available()
    
    @property
    def channel_layer(self):
        if self._channel_layer is None:
            from channels.layers import get_channel_layer
            self._channel_layer = get_channel_layer()
        return self._channel_layer
    
    def register_connection(self, channel_name: str, user_identifier: Optional[str] = None) -> None:
        """Register a new WebSocket connection."""
        # Determine if user_identifier is email or user_id
        user_email = None
        user_id = None
        
        if user_identifier:
            if "@" in user_identifier:
                user_email = user_identifier
            else:
                user_id = user_identifier
        
        if self.channel_layer: # Only use Redis if channels is available
            if self.channel_layer: # Only use Redis if channels is available
                # Use Redis for multi-instance support
                redis_connection_manager.register_connection(
                    channel_name, 
                    user_email,
                    user_id
                )
            else:
                # Fallback to in-memory storage
                    self._connections[channel_name] = ConnectionInfo(
                        channel_name=channel_name,
                        user_email=user_email,
                        user_id=user_id
                    )
        else:
            # Fallback to in-memory storage if channels is not available
            self._connections[channel_name] = ConnectionInfo(
                channel_name=channel_name,
                user_email=user_email,
                user_id=user_id
            )
        
        logger.debug(f"Registered connection {channel_name} for user {user_email or user_id}")
    
    def unregister_connection(self, channel_name: str) -> None:
        """Unregister a WebSocket connection."""
        if self.channel_layer: # Only use Redis if channels is available
            redis_connection_manager.unregister_connection(channel_name)
        else:
            if channel_name in self._connections:
                del self._connections[channel_name]
        
        logger.debug(f"Unregistered connection {channel_name}")
    
    def add_to_group(self, channel_name: str, group_name: str) -> None:
        """Add connection to a group."""
        if channel_name in self._connections:
            if group_name not in self._connections[channel_name].groups:
                self._connections[channel_name].groups.append(group_name)
    
    def remove_from_group(self, channel_name: str, group_name: str) -> None:
        """Remove connection from a group."""
        if channel_name in self._connections:
            if group_name in self._connections[channel_name].groups:
                self._connections[channel_name].groups.remove(group_name)
    
    def add_to_repository(self, channel_name: str, repository: str) -> None:
        """Add connection to a repository group."""
        if channel_name in self._connections:
            if repository not in self._connections[channel_name].repositories:
                self._connections[channel_name].repositories.append(repository)
    
    def remove_from_repository(self, channel_name: str, repository: str) -> None:
        """Remove connection from a repository group."""
        if channel_name in self._connections:
            if repository in self._connections[channel_name].repositories:
                self._connections[channel_name].repositories.remove(repository)
    
    def create_notification(
        self,
        type: NotificationType,
        title: str,
        message: str,
        level: NotificationLevel = NotificationLevel.MEDIUM,
        target: NotificationTarget = NotificationTarget.BROADCAST,
        data: Optional[Dict[str, Any]] = None,
        source: Optional[str] = None,
        target_id: Optional[str] = None,
        action_url: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> NotificationPayload:
        """Create a new notification payload."""
        return NotificationPayload(
            id=str(uuid.uuid4()),
            type=type,
            level=level,
            target=target,
            title=title,
            message=message,
            data=data,
            timestamp=datetime.now(),
            source=source,
            target_id=target_id,
            action_url=action_url,
            expires_at=expires_at
        )
    
    def send_to_user(self, user_identifier: str, notification: NotificationPayload) -> bool:
        """Send notification to a specific user (by email or user_id)."""
        if not self.channel_layer:
            logger.warning("Channel layer not available")
            return False
        
        # Determine if user_identifier is email or user_id
        is_email = "@" in user_identifier
        
        if self.channel_layer: # Only use Redis if channels is available
            # Use Redis to get connections
            if is_email:
                user_connections = redis_connection_manager.get_connections_by_email(user_identifier)
            else:
                user_connections = redis_connection_manager.get_connections_by_user_id(user_identifier)
        else:
            # Use in-memory connections
            if is_email:
                user_connections = [
                    conn.channel_name for conn in self._connections.values()
                    if conn.user_email == user_identifier
                ]
            else:
                user_connections = [
                    conn.channel_name for conn in self._connections.values()
                    if conn.user_id == user_identifier
                ]
        
        if not user_connections:
            logger.debug(f"No active connections for user {user_identifier}")
            return False
        
        success = True
        for channel_name in user_connections:
            try:
                # Ensure channel_name is a string, not bytes
                if isinstance(channel_name, bytes):
                    channel_name = channel_name.decode('utf-8')
                
                async_to_sync(self.channel_layer.send)(
                    channel_name,
                    {
                        "type": "send_notification",
                        "notification": notification.to_dict()
                    }
                )
                logger.debug(f"Sent notification to user {user_identifier} via {channel_name}")
            except Exception as e:
                logger.error(f"Failed to send notification to {channel_name}: {e}")
                success = False
        
        return success
    
    def send_to_user_email(self, user_email: str, notification: NotificationPayload) -> bool:
        """Send notification to a specific user by email address."""
        return self.send_to_user(user_email, notification)
    
    def send_to_user_by_channel_group(self, user_identifier: str, notification: NotificationPayload) -> bool:
        """Send notification using Django Channels groups."""
        if not self.channel_layer:
            logger.warning("Channel layer not available")
            return False
        
        # Determine group name based on identifier type
        is_email = "@" in user_identifier
        if is_email:
            # Encode email for group name (replace @ with _at_)
            encoded_email = user_identifier.replace("@", "_at_")
            group_name = f"user_email_{encoded_email}"
        else:
            group_name = f"user_id_{user_identifier}"
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    "type": "send_notification", 
                    "notification": notification.to_dict()
                }
            )
            logger.debug(f"Sent notification to user group {group_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to send notification to user group {group_name}: {e}")
            return False
    
    def send_to_group(self, group_name: str, notification: NotificationPayload) -> bool:
        """Send notification to all members of a group."""
        if not self.channel_layer:
            logger.warning("Channel layer not available")
            return False
        
        try:
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    "type": "send_notification",
                    "notification": notification.to_dict()
                }
            )
            logger.debug(f"Sent notification to group {group_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to send notification to group {group_name}: {e}")
            return False
    
    def send_to_repository(self, repository: str, notification: NotificationPayload) -> bool:
        """Send notification to all subscribers of a repository."""
        return self.send_to_group(f"repo_{repository}", notification)
    
    def broadcast(self, notification: NotificationPayload) -> bool:
        """Broadcast notification to all connected clients."""
        return self.send_to_group("broadcast", notification)
    
    def send_notification(self, notification: NotificationPayload) -> bool:
        """Send notification based on its target type."""
        if notification.target == NotificationTarget.USER and notification.target_id:
            # Try both direct send and channel group send for better reliability
            success1 = self.send_to_user(notification.target_id, notification)
            success2 = self.send_to_user_by_channel_group(notification.target_id, notification)
            return success1 or success2  # Success if either method works
        elif notification.target == NotificationTarget.GROUP and notification.target_id:
            return self.send_to_group(notification.target_id, notification)
        elif notification.target == NotificationTarget.REPOSITORY and notification.target_id:
            return self.send_to_repository(notification.target_id, notification)
        elif notification.target == NotificationTarget.BROADCAST:
            return self.broadcast(notification)
        else:
            logger.warning(f"Invalid notification target: {notification.target}")
            return False
    
    def send_system_notification(
        self,
        title: str,
        message: str,
        level: NotificationLevel = NotificationLevel.MEDIUM,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send a system-wide notification."""
        notification = self.create_notification(
            type=NotificationType.SYSTEM,
            title=title,
            message=message,
            level=level,
            target=NotificationTarget.BROADCAST,
            data=data,
            source="system"
        )
        return self.broadcast(notification)
    
    def send_integration_notification(
        self,
        integration_name: str,
        title: str,
        message: str,
        level: NotificationLevel = NotificationLevel.MEDIUM,
        data: Optional[Dict[str, Any]] = None,
        user_email: Optional[str] = None,
        user_id: Optional[str] = None  # Backward compatibility
    ) -> bool:
        """Send an integration-specific notification."""
        target_identifier = user_email or user_id
        target = NotificationTarget.USER if target_identifier else NotificationTarget.BROADCAST
        
        notification = self.create_notification(
            type=NotificationType.INTEGRATION,
            title=title,
            message=message,
            level=level,
            target=target,
            target_id=target_identifier,
            data=data,
            source=integration_name
        )
        return self.send_notification(notification)
    
    def send_repository_notification(
        self,
        repository: str,
        title: str,
        message: str,
        level: NotificationLevel = NotificationLevel.MEDIUM,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send a repository-specific notification."""
        notification = self.create_notification(
            type=NotificationType.REPOSITORY,
            title=title,
            message=message,
            level=level,
            target=NotificationTarget.REPOSITORY,
            target_id=repository,
            data=data,
            source="github"
        )
        return self.send_to_repository(repository, notification)
    
    def send_error_notification(
        self,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        user_email: Optional[str] = None,
        user_id: Optional[str] = None  # Backward compatibility
    ) -> bool:
        """Send an error notification."""
        target_identifier = user_email or user_id
        target = NotificationTarget.USER if target_identifier else NotificationTarget.BROADCAST
        
        notification = self.create_notification(
            type=NotificationType.ERROR,
            title=title,
            message=message,
            level=NotificationLevel.HIGH,
            target=target,
            target_id=target_identifier,
            data=data,
            source="system"
        )
        return self.send_notification(notification)
    
    def send_success_notification(
        self,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        user_email: Optional[str] = None,
        user_id: Optional[str] = None  # Backward compatibility
    ) -> bool:
        """Send a success notification."""
        target_identifier = user_email or user_id
        target = NotificationTarget.USER if target_identifier else NotificationTarget.BROADCAST
        
        notification = self.create_notification(
            type=NotificationType.SUCCESS,
            title=title,
            message=message,
            level=NotificationLevel.MEDIUM,
            target=target,
            target_id=target_identifier,
            data=data,
            source="system"
        )
        return self.send_notification(notification)


# Global instance
notification_service = NotificationService()