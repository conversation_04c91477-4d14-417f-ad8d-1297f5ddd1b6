import logging
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

from .services import notification_service
from .types import NotificationType, NotificationLevel, NotificationTarget

logger = logging.getLogger(__name__)


@api_view(['GET'])
def notification_info(request):
    """Get information about notification system and available types."""
    return Response({
        'notification_types': [t.value for t in NotificationType],
        'notification_levels': [l.value for l in NotificationLevel],
        'notification_targets': [t.value for t in NotificationTarget],
        'websocket_endpoint': 'ws/notifications/',
        'active_connections': len(notification_service._connections),
        'supported_actions': [
            'subscribe', 'unsubscribe', 'authenticate', 'ping'
        ],
        'message_format': {
            'subscribe': {
                'action': 'subscribe',
                'target_type': 'group|repository|user',
                'target_id': 'target identifier'
            },
            'unsubscribe': {
                'action': 'unsubscribe',
                'target_type': 'group|repository',
                'target_id': 'target identifier'
            },
            'authenticate': {
                'action': 'authenticate',
                'user_id': 'user identifier'
            }
        }
    })


@api_view(['GET'])
def health_check(request):
    """Health check endpoint for the notification system."""
    try:
        channel_layer = notification_service.channel_layer
        is_healthy = channel_layer is not None
        
        return Response({
            'status': 'healthy' if is_healthy else 'unhealthy',
            'channel_layer_available': is_healthy,
            'active_connections': len(notification_service._connections)
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return Response(
            {'status': 'unhealthy', 'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )