# 🔔 Real-Time WebSocket Notifications

Send real-time notifications to users by email address using WebSockets only.

## ⚡ Quick Start (2 minutes)

### 1. Start Django Server
```bash
python manage.py runserver 0.0.0.0:8000
```

### 2. Test in Browser Console
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/notifications/');
ws.onopen = () => ws.send(JSON.stringify({action: 'authenticate', user_email: '<EMAIL>'}));
ws.onmessage = e => console.log('📩', JSON.parse(e.data));
```

### 3. Send Notification (Django Shell)
```python
from notifications.services import notification_service

notification_service.send_success_notification(
    title="🎉 It Works!",
    message="Real-time notifications are working!",
    user_email="<EMAIL>"
)
```

**You should see the notification appear instantly in your browser console!** ✨

## 🚀 How to Send Notifications

### From Any Django Code
```python
from notifications.services import notification_service

# Send to specific user by email
notification_service.send_success_notification(
    title="Export Complete",
    message="Your CSV export is ready for download",
    user_email="<EMAIL>"
)

# Send error notification  
notification_service.send_error_notification(
    title="Upload Failed",
    message="Error processing your file",
    user_email="<EMAIL>"
)

# Send system-wide notification
notification_service.send_system_notification(
    title="Maintenance Tonight",
    message="System will be offline 2-4 AM",
    level="high"
)

# Send integration notification
notification_service.send_integration_notification(
    integration_name="GitHub",
    title="PR Merged",
    message="Your pull request was merged",
    user_email="<EMAIL>"
)

# Send to repository subscribers
notification_service.send_repository_notification(
    repository="sagebase/backend", 
    title="Build Failed",
    message="Main branch build failed"
)
```

## 🌐 Frontend Integration

### JavaScript/TypeScript
```javascript
class NotificationClient {
    constructor(userEmail) {
        this.userEmail = userEmail;
        this.ws = new WebSocket('ws://localhost:8000/ws/notifications/');
        this.setupHandlers();
    }
    
    setupHandlers() {
        this.ws.onopen = () => {
            console.log('🔔 Connected');
            this.authenticate();
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            if (data.type === 'notification') {
                this.showNotification(data);
            }
        };
    }
    
    authenticate() {
        this.ws.send(JSON.stringify({
            action: 'authenticate',
            user_email: this.userEmail
        }));
    }
    
    showNotification(notification) {
        // Show toast/popup
        console.log(`📩 ${notification.title}: ${notification.message}`);
        
        // Browser notification
        if (Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message
            });
        }
    }
}

// Usage
const client = new NotificationClient('<EMAIL>');
```

### React Hook
```javascript
import { useState, useEffect } from 'react';

export const useNotifications = (userEmail) => {
    const [notifications, setNotifications] = useState([]);
    const [isConnected, setIsConnected] = useState(false);

    useEffect(() => {
        if (!userEmail) return;

        const ws = new WebSocket('ws://localhost:8000/ws/notifications/');
        
        ws.onopen = () => {
            setIsConnected(true);
            ws.send(JSON.stringify({
                action: 'authenticate',
                user_email: userEmail
            }));
        };

        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (data.type === 'notification') {
                setNotifications(prev => [data, ...prev]);
            }
        };

        ws.onclose = () => setIsConnected(false);

        return () => ws.close();
    }, [userEmail]);

    return { notifications, isConnected };
};

// Component usage
function App() {
    const { notifications, isConnected } = useNotifications('<EMAIL>');

    return (
        <div>
            <div>Status: {isConnected ? '✅ Connected' : '❌ Disconnected'}</div>
            {notifications.map(n => (
                <div key={n.id}>
                    <strong>{n.title}</strong>: {n.message}
                </div>
            ))}
        </div>
    );
}
```

## 📱 WebSocket Protocol

### Client → Server
```javascript
// Authenticate with email
{action: 'authenticate', user_email: '<EMAIL>'}

// Subscribe to user notifications  
{action: 'subscribe', target_type: 'user', target_id: '<EMAIL>'}

// Subscribe to repository notifications
{action: 'subscribe', target_type: 'repository', target_id: 'owner/repo'}

// Health check
{action: 'ping'}
```

### Server → Client
```javascript
// Authentication success
{type: 'authentication.success', user_email: '<EMAIL>'}

// Notification received
{
    type: 'notification',
    id: 'notif_123',
    title: 'Export Complete',
    message: 'Your CSV is ready',
    level: 'medium',
    timestamp: '2024-01-01T12:00:00Z',
    data: {export_id: 'exp_456'}
}
```

## 🔧 Common Integration Patterns

### Django Views
```python
@api_view(['POST'])
def process_data(request):
    user_email = request.user.email
    
    try:
        result = do_processing()
        
        notification_service.send_success_notification(
            title="Processing Complete",
            message="Your data has been processed",
            user_email=user_email
        )
        
        return Response({'success': True})
        
    except Exception as e:
        notification_service.send_error_notification(
            title="Processing Failed", 
            message=str(e),
            user_email=user_email
        )
        
        return Response({'error': str(e)}, status=400)
```

### Celery Tasks
```python
from celery import shared_task

@shared_task
def long_running_task(user_email, data):
    try:
        # Do work...
        result = process_data(data)
        
        notification_service.send_success_notification(
            title="Task Complete",
            message=f"Processing finished: {result}",
            user_email=user_email
        )
        
    except Exception as e:
        notification_service.send_error_notification(
            title="Task Failed",
            message=str(e), 
            user_email=user_email
        )
```

### Model Signals
```python
from django.db.models.signals import post_save

@receiver(post_save, sender=Document)
def document_uploaded(sender, instance, created, **kwargs):
    if created:
        notification_service.send_success_notification(
            title="Document Uploaded",
            message=f"'{instance.title}' uploaded successfully",
            user_email=instance.owner.email
        )
```

## ⚙️ Configuration

### Development (Default)
Works out of the box - no configuration needed.

### Production (Multi-instance)
Add Redis for multiple backend instances:

```bash
# Environment variable
export REDIS_URL=redis://localhost:6379

# Or in .env file  
REDIS_URL=redis://localhost:6379
```

## 🧪 Testing

### Health Check
```bash
curl http://localhost:8000/api/notifications/health/
# {"status": "healthy", "active_connections": 3}
```

### Manual Test
```python
# Django shell
python manage.py shell

from notifications.services import notification_service

notification_service.send_success_notification(
    title="Test",
    message="Testing notifications",
    user_email="<EMAIL>"  # Use your email
)
```

## 🎯 Features

- ✅ **Email targeting**: `<EMAIL>`
- ✅ **Real-time**: Instant WebSocket delivery
- ✅ **Multi-instance**: Redis support for multiple servers
- ✅ **Simple API**: Import and use
- ✅ **Rich data**: JSON payloads, action URLs
- ✅ **Framework agnostic**: Works with any frontend
- ✅ **Notification types**: Success, error, system, integration, repository
- ✅ **Auto-reconnection**: Frontend handles disconnections
- ✅ **Browser notifications**: Native browser support

## 🔍 Available Methods

```python
from notifications.services import notification_service

# User notifications
notification_service.send_success_notification(title, message, user_email)
notification_service.send_error_notification(title, message, user_email)

# System notifications  
notification_service.send_system_notification(title, message, level="medium")

# Integration notifications
notification_service.send_integration_notification(
    integration_name, title, message, user_email
)

# Repository notifications
notification_service.send_repository_notification(repository, title, message)
```

That's it! **Pure WebSocket notifications** - no REST API complexity. 🚀