from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime


class NotificationType(str, Enum):
    """Enum for different notification types."""
    SYSTEM = "system"
    USER = "user"
    INTEGRATION = "integration"
    REPOSITORY = "repository"
    WEBHOOK = "webhook"
    ERROR = "error"
    SUCCESS = "success"
    WARNING = "warning"
    INFO = "info"
    NEW_KNOWLEDGE_BASE = "new_knowledge_base"


class NotificationLevel(str, Enum):
    """Enum for notification severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class NotificationTarget(str, Enum):
    """Enum for notification target types."""
    USER = "user"
    GROUP = "group"
    BROADCAST = "broadcast"
    REPOSITORY = "repository"


@dataclass
class NotificationPayload:
    """Standard notification payload structure."""
    id: str
    type: NotificationType
    level: NotificationLevel
    target: NotificationTarget
    title: str
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    source: Optional[str] = None
    target_id: Optional[str] = None
    action_url: Optional[str] = None
    expires_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "type": self.type.value,
            "level": self.level.value,
            "target": self.target.value,
            "title": self.title,
            "message": self.message,
            "data": self.data or {},
            "timestamp": self.timestamp.isoformat() if self.timestamp else datetime.now().isoformat(),
            "source": self.source,
            "target_id": self.target_id,
            "action_url": self.action_url,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
        }


@dataclass
class ConnectionInfo:
    """Information about WebSocket connections."""
    channel_name: str
    user_email: Optional[str] = None
    user_id: Optional[str] = None  # Keep for backward compatibility
    groups: List[str] = None
    repositories: List[str] = None
    instance_id: Optional[str] = None  # Track which backend instance
    connected_at: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    
    def __post_init__(self):
        if self.groups is None:
            self.groups = []
        if self.repositories is None:
            self.repositories = []
        if self.connected_at is None:
            self.connected_at = datetime.now()
        if self.last_seen is None:
            self.last_seen = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Redis storage."""
        return {
            "channel_name": self.channel_name,
            "user_email": self.user_email,
            "user_id": self.user_id,
            "groups": self.groups,
            "repositories": self.repositories,
            "instance_id": self.instance_id,
            "connected_at": self.connected_at.isoformat() if self.connected_at else None,
            "last_seen": self.last_seen.isoformat() if self.last_seen else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConnectionInfo':
        """Create ConnectionInfo from dictionary."""
        return cls(
            channel_name=data["channel_name"],
            user_email=data.get("user_email"),
            user_id=data.get("user_id"),
            groups=data.get("groups", []),
            repositories=data.get("repositories", []),
            instance_id=data.get("instance_id"),
            connected_at=datetime.fromisoformat(data["connected_at"]) if data.get("connected_at") else None,
            last_seen=datetime.fromisoformat(data["last_seen"]) if data.get("last_seen") else None,
        )