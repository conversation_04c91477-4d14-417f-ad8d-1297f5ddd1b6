import json
import redis
import logging
import uuid
from typing import List, Optional, Dict, Set
from datetime import datetime, timedelta
from django.conf import settings

from .types import ConnectionInfo

logger = logging.getLogger(__name__)


class RedisConnectionManager:
    """
    Redis-based connection manager for multi-instance WebSocket support.
    Stores connection information across multiple backend instances.
    """
    
    def __init__(self):
        self.instance_id = str(uuid.uuid4())[:8]  # Unique instance identifier
        self.redis_client = self._get_redis_client()
        
        # Redis key prefixes
        self.CONNECTION_PREFIX = "ws:connection:"
        self.USER_EMAIL_PREFIX = "ws:user_email:"
        self.USER_ID_PREFIX = "ws:user_id:"  # Backward compatibility
        self.GROUP_PREFIX = "ws:group:"
        self.REPO_PREFIX = "ws:repo:"
        self.INSTANCE_PREFIX = "ws:instance:"
        
        logger.info(f"RedisConnectionManager initialized with instance_id: {self.instance_id}")
    
    def _get_redis_client(self) -> redis.Redis:
        """Get Redis client from Django settings or environment."""
        try:
            # Try to get Redis URL from environment or Django settings
            redis_url = getattr(settings, 'REDIS_URL', None) or \
                       getattr(settings, 'CHANNEL_LAYERS', {}).get('default', {}).get('CONFIG', {}).get('hosts', [None])[0]
            
            if redis_url and isinstance(redis_url, str):
                return redis.from_url(redis_url)
            
            # Fallback to default Redis connection
            redis_host = getattr(settings, 'REDIS_HOST', 'localhost')
            redis_port = getattr(settings, 'REDIS_PORT', 6379)
            redis_db = getattr(settings, 'REDIS_DB', 0)
            
            return redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            logger.warning("Falling back to in-memory storage (single instance only)")
            return None
    
    def is_available(self) -> bool:
        """Check if Redis is available."""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except Exception:
            return False
    
    def register_connection(self, channel_name: str, user_email: Optional[str] = None, user_id: Optional[str] = None) -> bool:
        """Register a new WebSocket connection."""
        if not self.is_available():
            logger.warning("Redis not available, connection not registered")
            return False
        
        try:
            connection_info = ConnectionInfo(
                channel_name=channel_name,
                user_email=user_email,
                user_id=user_id,
                instance_id=self.instance_id
            )
            
            # Store connection info
            connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
            self.redis_client.setex(
                connection_key,
                timedelta(hours=24),  # Expire after 24 hours
                json.dumps(connection_info.to_dict())
            )
            
            # Index by user email if provided
            if user_email:
                user_email_key = f"{self.USER_EMAIL_PREFIX}{user_email}"
                self.redis_client.sadd(user_email_key, channel_name)
                self.redis_client.expire(user_email_key, timedelta(hours=24))
            
            # Index by user ID if provided (backward compatibility)
            if user_id:
                user_id_key = f"{self.USER_ID_PREFIX}{user_id}"
                self.redis_client.sadd(user_id_key, channel_name)
                self.redis_client.expire(user_id_key, timedelta(hours=24))
            
            # Track instance connections
            instance_key = f"{self.INSTANCE_PREFIX}{self.instance_id}"
            self.redis_client.sadd(instance_key, channel_name)
            self.redis_client.expire(instance_key, timedelta(hours=24))
            
            logger.debug(f"Registered connection {channel_name} for {user_email or user_id or 'anonymous'}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register connection {channel_name}: {e}")
            return False
    
    def unregister_connection(self, channel_name: str) -> bool:
        """Unregister a WebSocket connection."""
        if not self.is_available():
            return False
        
        try:
            # Get connection info first
            connection_info = self.get_connection(channel_name)
            if not connection_info:
                return True  # Already gone
            
            # Remove from user email index
            if connection_info.user_email:
                user_email_key = f"{self.USER_EMAIL_PREFIX}{connection_info.user_email}"
                self.redis_client.srem(user_email_key, channel_name)
            
            # Remove from user ID index (backward compatibility)
            if connection_info.user_id:
                user_id_key = f"{self.USER_ID_PREFIX}{connection_info.user_id}"
                self.redis_client.srem(user_id_key, channel_name)
            
            # Remove from groups
            for group in connection_info.groups:
                group_key = f"{self.GROUP_PREFIX}{group}"
                self.redis_client.srem(group_key, channel_name)
            
            # Remove from repositories
            for repo in connection_info.repositories:
                repo_key = f"{self.REPO_PREFIX}{repo}"
                self.redis_client.srem(repo_key, channel_name)
            
            # Remove from instance
            if connection_info.instance_id:
                instance_key = f"{self.INSTANCE_PREFIX}{connection_info.instance_id}"
                self.redis_client.srem(instance_key, channel_name)
            
            # Remove connection info
            connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
            self.redis_client.delete(connection_key)
            
            logger.debug(f"Unregistered connection {channel_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister connection {channel_name}: {e}")
            return False
    
    def get_connection(self, channel_name: str) -> Optional[ConnectionInfo]:
        """Get connection information."""
        if not self.is_available():
            return None
        
        try:
            connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
            data = self.redis_client.get(connection_key)
            if data:
                return ConnectionInfo.from_dict(json.loads(data))
            return None
        except Exception as e:
            logger.error(f"Failed to get connection {channel_name}: {e}")
            return None
    
    def get_connections_by_email(self, user_email: str) -> List[str]:
        """Get all channel names for a user email."""
        if not self.is_available():
            return []
        
        try:
            user_email_key = f"{self.USER_EMAIL_PREFIX}{user_email}"
            return list(self.redis_client.smembers(user_email_key))
        except Exception as e:
            logger.error(f"Failed to get connections for email {user_email}: {e}")
            return []
    
    def get_connections_by_user_id(self, user_id: str) -> List[str]:
        """Get all channel names for a user ID (backward compatibility)."""
        if not self.is_available():
            return []
        
        try:
            user_id_key = f"{self.USER_ID_PREFIX}{user_id}"
            return list(self.redis_client.smembers(user_id_key))
        except Exception as e:
            logger.error(f"Failed to get connections for user_id {user_id}: {e}")
            return []
    
    def add_to_group(self, channel_name: str, group_name: str) -> bool:
        """Add connection to a group."""
        if not self.is_available():
            return False
        
        try:
            # Add to group set
            group_key = f"{self.GROUP_PREFIX}{group_name}"
            self.redis_client.sadd(group_key, channel_name)
            self.redis_client.expire(group_key, timedelta(hours=24))
            
            # Update connection info
            connection_info = self.get_connection(channel_name)
            if connection_info and group_name not in connection_info.groups:
                connection_info.groups.append(group_name)
                connection_info.last_seen = datetime.now()
                
                connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
                self.redis_client.setex(
                    connection_key,
                    timedelta(hours=24),
                    json.dumps(connection_info.to_dict())
                )
            
            return True
        except Exception as e:
            logger.error(f"Failed to add {channel_name} to group {group_name}: {e}")
            return False
    
    def remove_from_group(self, channel_name: str, group_name: str) -> bool:
        """Remove connection from a group."""
        if not self.is_available():
            return False
        
        try:
            # Remove from group set
            group_key = f"{self.GROUP_PREFIX}{group_name}"
            self.redis_client.srem(group_key, channel_name)
            
            # Update connection info
            connection_info = self.get_connection(channel_name)
            if connection_info and group_name in connection_info.groups:
                connection_info.groups.remove(group_name)
                connection_info.last_seen = datetime.now()
                
                connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
                self.redis_client.setex(
                    connection_key,
                    timedelta(hours=24),
                    json.dumps(connection_info.to_dict())
                )
            
            return True
        except Exception as e:
            logger.error(f"Failed to remove {channel_name} from group {group_name}: {e}")
            return False
    
    def get_group_connections(self, group_name: str) -> List[str]:
        """Get all connections in a group."""
        if not self.is_available():
            return []
        
        try:
            group_key = f"{self.GROUP_PREFIX}{group_name}"
            return list(self.redis_client.smembers(group_key))
        except Exception as e:
            logger.error(f"Failed to get group connections for {group_name}: {e}")
            return []
    
    def add_to_repository(self, channel_name: str, repository: str) -> bool:
        """Add connection to a repository."""
        if not self.is_available():
            return False
        
        try:
            # Add to repo set
            repo_key = f"{self.REPO_PREFIX}{repository}"
            self.redis_client.sadd(repo_key, channel_name)
            self.redis_client.expire(repo_key, timedelta(hours=24))
            
            # Update connection info
            connection_info = self.get_connection(channel_name)
            if connection_info and repository not in connection_info.repositories:
                connection_info.repositories.append(repository)
                connection_info.last_seen = datetime.now()
                
                connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
                self.redis_client.setex(
                    connection_key,
                    timedelta(hours=24),
                    json.dumps(connection_info.to_dict())
                )
            
            return True
        except Exception as e:
            logger.error(f"Failed to add {channel_name} to repository {repository}: {e}")
            return False
    
    def remove_from_repository(self, channel_name: str, repository: str) -> bool:
        """Remove connection from a repository."""
        if not self.is_available():
            return False
        
        try:
            # Remove from repo set
            repo_key = f"{self.REPO_PREFIX}{repository}"
            self.redis_client.srem(repo_key, channel_name)
            
            # Update connection info
            connection_info = self.get_connection(channel_name)
            if connection_info and repository in connection_info.repositories:
                connection_info.repositories.remove(repository)
                connection_info.last_seen = datetime.now()
                
                connection_key = f"{self.CONNECTION_PREFIX}{channel_name}"
                self.redis_client.setex(
                    connection_key,
                    timedelta(hours=24),
                    json.dumps(connection_info.to_dict())
                )
            
            return True
        except Exception as e:
            logger.error(f"Failed to remove {channel_name} from repository {repository}: {e}")
            return False
    
    def get_repository_connections(self, repository: str) -> List[str]:
        """Get all connections subscribed to a repository."""
        if not self.is_available():
            return []
        
        try:
            repo_key = f"{self.REPO_PREFIX}{repository}"
            return list(self.redis_client.smembers(repo_key))
        except Exception as e:
            logger.error(f"Failed to get repository connections for {repository}: {e}")
            return []
    
    def cleanup_expired_connections(self) -> int:
        """Clean up expired connections (run periodically)."""
        if not self.is_available():
            return 0
        
        try:
            cleaned = 0
            # This is a basic cleanup - in production you might want a more sophisticated approach
            # For now, we rely on Redis TTL to handle cleanup automatically
            return cleaned
        except Exception as e:
            logger.error(f"Failed to cleanup expired connections: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get connection statistics."""
        if not self.is_available():
            return {"error": "Redis not available"}
        
        try:
            stats = {}
            
            # Count total connections
            connection_keys = self.redis_client.keys(f"{self.CONNECTION_PREFIX}*")
            stats["total_connections"] = len(connection_keys)
            
            # Count connections per instance
            instance_keys = self.redis_client.keys(f"{self.INSTANCE_PREFIX}*")
            stats["instances"] = len(instance_keys)
            stats["current_instance_id"] = self.instance_id
            
            # Count current instance connections
            instance_key = f"{self.INSTANCE_PREFIX}{self.instance_id}"
            stats["current_instance_connections"] = self.redis_client.scard(instance_key)
            
            return stats
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {"error": str(e)}


# Global instance
redis_connection_manager = RedisConnectionManager()