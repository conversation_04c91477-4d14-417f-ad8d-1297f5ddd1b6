"""
Examples of how to use the generic notification system.
"""

from .services import notification_service
from .types import NotificationType, NotificationLevel, NotificationTarget


def example_system_notification():
    """Send a system-wide notification."""
    notification_service.send_system_notification(
        title="System Maintenance",
        message="The system will undergo maintenance from 2:00 AM to 4:00 AM UTC.",
        level=NotificationLevel.HIGH,
        data={"maintenance_window": "2:00-4:00 UTC", "affected_services": ["API", "WebSockets"]}
    )


def example_user_notification():
    """Send a notification to a specific user."""
    notification = notification_service.create_notification(
        type=NotificationType.USER,
        title="Profile Updated",
        message="Your profile has been successfully updated.",
        level=NotificationLevel.MEDIUM,
        target=NotificationTarget.USER,
        target_id="user_123",
        source="user_service",
        action_url="/profile"
    )
    
    notification_service.send_notification(notification)


def example_integration_notification():
    """Send a notification from an integration."""
    notification_service.send_integration_notification(
        integration_name="GitHub",
        title="New Pull Request",
        message="A new pull request has been created in repository 'sagebase/backend'",
        level=NotificationLevel.MEDIUM,
        data={
            "repository": "sagebase/backend",
            "pr_number": 42,
            "author": "john_doe",
            "url": "https://github.com/sagebase/backend/pull/42"
        },
        user_id="user_456"  # Optional: send to specific user
    )


def example_repository_notification():
    """Send a notification to repository subscribers."""
    notification_service.send_repository_notification(
        repository="sagebase/backend",
        title="Build Failed",
        message="The latest build failed on the main branch",
        level=NotificationLevel.HIGH,
        data={
            "branch": "main",
            "commit_sha": "abc123",
            "build_id": "build_789",
            "error_count": 3
        }
    )


def example_success_notification():
    """Send a success notification."""
    notification_service.send_success_notification(
        title="Deployment Complete",
        message="Your application has been successfully deployed to production",
        data={
            "environment": "production",
            "version": "1.2.3",
            "deployment_id": "deploy_456"
        },
        user_id="user_123"
    )


def example_error_notification():
    """Send an error notification."""
    notification_service.send_error_notification(
        title="Database Connection Error",
        message="Unable to connect to the primary database. Failover initiated.",
        data={
            "database": "primary_db",
            "error_code": "CONN_TIMEOUT",
            "failover_status": "initiated"
        }
    )


def example_group_notification():
    """Send a notification to a specific group."""
    notification = notification_service.create_notification(
        type=NotificationType.INFO,
        title="Team Meeting",
        message="Don't forget about the team standup meeting at 10:00 AM",
        level=NotificationLevel.MEDIUM,
        target=NotificationTarget.GROUP,
        target_id="engineering_team",
        source="calendar_service",
        data={
            "meeting_id": "meeting_123",
            "time": "10:00 AM",
            "duration": "30 minutes"
        }
    )
    
    notification_service.send_notification(notification)


# Integration with existing GitHub webhook system
def example_github_webhook_integration():
    """Example of how to integrate with existing GitHub webhooks."""
    from integrations.github.api.services import GitHubAPIService
    
    # In your webhook handler, you can now send notifications:
    def handle_push_event(webhook_data):
        repository = webhook_data.get('repository', {}).get('full_name', 'unknown')
        commits = webhook_data.get('commits', [])
        
        # Send notification to repository subscribers
        notification_service.send_repository_notification(
            repository=repository,
            title="New Commits",
            message=f"{len(commits)} new commit(s) pushed to repository",
            level=NotificationLevel.MEDIUM,
            data={
                "commit_count": len(commits),
                "branch": webhook_data.get('ref', '').replace('refs/heads/', ''),
                "pusher": webhook_data.get('pusher', {}).get('name', 'unknown'),
                "repository": repository
            }
        )


# Using the notification system programmatically
def example_programmatic_usage():
    """Example of using notifications in your Django views."""
    
    # In a Django view or service function:
    def process_user_action(user_id, action_data):
        try:
            # Process the action
            result = perform_some_operation(action_data)
            
            # Send success notification
            notification_service.send_success_notification(
                title="Action Completed",
                message=f"Your {action_data['type']} action has been completed successfully",
                data={"result": result, "action_type": action_data['type']},
                user_id=user_id
            )
            
        except Exception as e:
            # Send error notification
            notification_service.send_error_notification(
                title="Action Failed",
                message=f"Your {action_data['type']} action failed: {str(e)}",
                data={"error": str(e), "action_type": action_data['type']},
                user_id=user_id
            )


def perform_some_operation(action_data):
    """Dummy function for the example."""
    return {"success": True}


# Example of connecting different notification levels with business logic
def example_notification_levels():
    """Examples of different notification levels and when to use them."""
    
    # CRITICAL: System is down, immediate action required
    notification_service.send_system_notification(
        title="System Outage",
        message="Critical system failure detected. All services are offline.",
        level=NotificationLevel.CRITICAL
    )
    
    # HIGH: Important events that need attention
    notification_service.send_system_notification(
        title="High Memory Usage",
        message="Server memory usage is at 90%. Consider scaling up.",
        level=NotificationLevel.HIGH
    )
    
    # MEDIUM: Normal notifications
    notification_service.send_system_notification(
        title="Weekly Report",
        message="Your weekly analytics report is ready for review.",
        level=NotificationLevel.MEDIUM
    )
    
    # LOW: Informational messages
    notification_service.send_system_notification(
        title="Feature Announcement",
        message="We've added a new feature to help you manage your projects.",
        level=NotificationLevel.LOW
    )