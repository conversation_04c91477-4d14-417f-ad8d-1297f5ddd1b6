{"version": "2.0.0", "tasks": [{"label": "start-django-in-docker", "type": "shell", "command": "docker", "args": ["compose", "exec", "web", "python", "-Xfrozen_modules=off", "-m", "debugpy", "--listen", "0.0.0.0:5678", "--wait-for-client", "manage.py", "runserver", "0.0.0.0:8000"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false, "clear": false}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^(.*)$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "Performing system checks", "endsPattern": "Starting development server at"}}}, {"label": "start-document-embedding-in-docker", "type": "shell", "command": "docker", "args": ["compose", "exec", "web", "python", "-Xfrozen_modules=off", "-m", "debugpy", "--listen", "0.0.0.0:5679", "--wait-for-client", "vectordb/tests/embed_sageBaseFolder.py", "--workspace", "sagebase", "--mode", "monitor", "--non-interactive", "--verbose"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false, "clear": false}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^(.*)$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "Standalone SageBase Document Processing", "endsPattern": "Starting continuous monitoring mode"}}}]}