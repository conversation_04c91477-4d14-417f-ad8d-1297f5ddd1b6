#!/usr/bin/env python3
"""
Simple test script for local ChromaDB embedding functionality
"""

import os
import sys
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_local_chroma():
    """Test local ChromaDB with sentence transformer embeddings"""
    
    try:
        # Import the ChromaDB service
        from vectordb.services .chroma_service import ChromaDBService
        
        print("🔧 Setting up local ChromaDB service...")
        
        # Create a local ChromaDB service with sentence transformer embeddings
        chroma_service = ChromaDBService(
            persist_directory="./test_chroma_db",  # Local test directory
            embedding_type="sentence_transformer",
            embedding_model="all-MiniLM-L6-v2"  # Lightweight model for testing
        )
        
        print("✅ ChromaDB service created successfully")
        
        # Test health check
        print("\n🏥 Running health check...")
        health = chroma_service.health_check()
        print(f"Health status: {health['status']}")
        
        if health['status'] == 'healthy':
            print("✅ ChromaDB is healthy!")
        else:
            print(f"❌ ChromaDB health check failed: {health.get('error', 'Unknown error')}")
            return False
        
        # Test collection operations
        print("\n📚 Testing collection operations...")
        test_collection_name = "test_collection"
        
        # Create collection
        collection = chroma_service.get_collection(test_collection_name)
        print(f"✅ Created collection: {test_collection_name}")
        
        # Add test documents
        test_documents = [
            "This is a test document about Python programming",
            "Machine learning is a subset of artificial intelligence",
            "Django is a web framework for Python",
            "ChromaDB is a vector database for embeddings"
        ]
        
        test_ids = [f"doc_{i}" for i in range(len(test_documents))]
        test_metadata = [{"source": "test", "type": "document"} for _ in test_documents]
        
        print("📝 Adding test documents...")
        collection.add(
            documents=test_documents,
            ids=test_ids,
            metadatas=test_metadata
        )
        print(f"✅ Added {len(test_documents)} documents")
        
        # Test search
        print("\n🔍 Testing search functionality...")
        query = "Python programming"
        results = collection.query(
            query_texts=[query],
            n_results=2
        )
        
        print(f"Query: '{query}'")
        print(f"Found {len(results['ids'][0])} results:")
        for i, (doc_id, doc_text, distance) in enumerate(zip(
            results['ids'][0], 
            results['documents'][0], 
            results['distances'][0]
        )):
            print(f"  {i+1}. ID: {doc_id}")
            print(f"     Text: {doc_text[:50]}...")
            print(f"     Distance: {distance:.4f}")
        
        # Test embedding function directly
        print("\n🧠 Testing embedding function...")
        embedding_function = chroma_service.embedding_function
        test_text = "This is a test sentence for embedding"
        embedding = embedding_function([test_text])
        
        print(f"Test text: '{test_text}'")
        print(f"Embedding shape: {len(embedding[0])} dimensions")
        print(f"First 5 values: {embedding[0][:5]}")
        
        # Clean up
        print("\n🧹 Cleaning up...")
        chroma_service.delete_collection(test_collection_name)
        print("✅ Test collection deleted")
        
        # List remaining collections
        collections = chroma_service.list_collections()
        print(f"Remaining collections: {collections}")
        
        print("\n🎉 All tests passed! Local ChromaDB is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting local ChromaDB test...")
    success = test_local_chroma()
    
    if success:
        print("\n✅ Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
