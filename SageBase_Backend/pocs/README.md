# Proof of Concepts (POCs)

This directory contains scripts and demonstrations for quick ideas and proof of concepts. Each subdirectory contains a focused POC with its own documentation and examples.

## Table of Contents

| POC | Description | Status | Files |
|-----|-------------|--------|-------|
| [agent_context_visibility_test](./agent_context_visibility_test/) | Demonstrates passing sensitive context variables to tools without exposing them to OpenAI | ✅ Complete | `simple_context_test.py`, `quick_debug_test.py` |
| [parallel_tool_calls_demo](./parallel_tool_calls_demo/) | Demonstrates that parallel_tool_calls=True makes LLM call async tools in parallel | ✅ Complete | `simple_parallel_test.py` |

## Usage

Each POC subdirectory contains:
- `README.md` - Concise documentation and examples
- Python scripts demonstrating the concept
- Test files showing the functionality

## Adding New POCs

1. Create a new subdirectory with a descriptive name
2. Add a `README.md` with clear documentation
3. Include working examples and test scripts
4. Update this table of contents

## Quick Start

```bash
# Navigate to a specific POC
cd pocs/agent_context_visibility_test

# Set up environment
export OPENAI_API_KEY="your-api-key-here"

# Run the POC
python simple_context_test.py
```

## Guidelines

- Keep POCs focused and concise
- Include clear documentation
- Provide working examples
- Update the table of contents when adding new POCs
- Use descriptive directory names
- Include test scripts where appropriate 