# Agent Context Visibility Test

**Proof of Concept**: Demonstrates how to pass sensitive context variables (user tokens, IDs, etc.) to all tools systematically without exposing them to the OpenAI agent.

## Key Concept

✅ **Context is passed to all tools systematically**  
✅ **Sensitive data is NOT exposed to OpenAI**  
✅ **Tools can access tokens, IDs, and other sensitive info**  
✅ **Type-safe with generics**  

## Quick Start

```bash
export OPENAI_API_KEY="your-api-key-here"
python simple_context_test.py
```

## How It Works

### 1. Define Context Class
```python
@dataclass
class UserContext:
    # Sensitive data (NOT exposed to OpenAI)
    user_id: str
    api_token: str
    session_token: str
    
    # Non-sensitive data
    user_name: str
    preferences: List[str]
```

### 2. Create Tools with Context
```python
@function_tool
async def search_database(
    context: RunContextWrapper[UserContext],  # Must be first parameter
    query: str
) -> str:
    # Access sensitive data
    user_id = context.context.user_id
    api_token = context.context.api_token
    
    # Use tokens for authentication (NOT visible to LLM)
    return f"Search results for '{query}' in user {user_id}'s database"
```

### 3. Create Agent and Pass Context
```python
# Create agent with context type
agent = Agent[UserContext](
    name="MyAgent",
    instructions="...",
    tools=[search_database]
)

# Create context with sensitive data
context = UserContext(
    user_id="user_123",
    api_token="secret_api_token_abc123",
    session_token="secret_session_token_xyz789",
    user_name="John Doe",
    preferences=["dark_mode", "notifications"]
)

# Run agent with context
result = await Runner.run(
    agent,
    "Search my database for important files",
    context=context  # Context is passed to all tools
)
```

## What the LLM Sees vs. What's Available

### LLM Visible (in tool schema):
```json
{
  "function": "search_database(context: RunContextWrapper[UserContext], query: str) -> str",
  "docstring": "Search the user's database.",
  "parameters": {
    "query": {"type": "string", "description": "The search query to perform"}
  }
}
```

### Actual Context Data (NOT visible to LLM):
```json
{
  "user_id": "user_123",
  "user_name": "John Doe",
  "has_api_token": true,
  "has_session_token": true
}
```

## Files

- `simple_context_test.py` - Complete demonstration with multiple tools
- `quick_debug_test.py` - Minimal test showing context visibility

## Test Results

The tests demonstrate:
1. ✅ Context is passed to all tools systematically
2. ✅ Sensitive data is accessible to tools for authentication
3. ✅ LLM only sees function signatures, not sensitive data
4. ✅ Type safety works with generics
5. ✅ All tools automatically receive the same context

## Real-World Use Cases

- **User Authentication**: Pass session tokens to tools
- **Database Access**: Use database credentials without exposing them
- **API Integration**: Pass API keys for external service calls
- **User Context**: Maintain user-specific state across tool calls

## Important Notes

1. **Context parameter must be first**: `context: RunContextWrapper[YourContextType]` must be the first parameter
2. **Context is never serialized**: The context object is never sent to OpenAI
3. **All tools get context**: Every tool in the agent receives the same context
4. **Type safety**: Use generics `Agent[YourContextType]` for compile-time checking

This pattern allows you to build secure, user-aware agents that can access sensitive data without exposing it to the LLM! 