#!/usr/bin/env python3
"""
Quick debug test to show context visibility.
"""

import asyncio
import json
from dataclasses import dataclass
from typing import List

from agents import Agent, <PERSON>, function_tool, RunContextWrapper

@dataclass
class UserContext:
    user_id: str
    api_token: str
    session_token: str
    user_name: str

@function_tool
async def debug_context(context: RunContextWrapper[UserContext]) -> str:
    """
    Debug tool to show what's visible to the LLM vs what's in context.
    """
    # What the LLM can see (function signature only)
    llm_visible = {
        "function": "debug_context(context: RunContextWrapper[UserContext]) -> str",
        "docstring": "Debug tool to show what's visible to the LLM vs what's in context."
    }
    
    # What's actually in context (sensitive data)
    context_data = {
        "user_id": context.context.user.id,
        "user_name": context.context.user_name,
        "has_api_token": bool(context.context.api_token),
        "has_session_token": bool(context.context.session_token)
    }
    
    return f"""
LLM Visible (in tool schema):
{json.dumps(llm_visible, indent=2)}

Actual Context Data (NOT visible to LLM):
{json.dumps(context_data, indent=2)}

The LLM only sees the function signature, not the actual sensitive data!
"""

async def main():
    context = UserContext(
        user_id="user_123",
        api_token="secret_api_token_abc123",
        session_token="secret_session_token_xyz789",
        user_name="John Doe"
    )
    
    agent = Agent(
        name="DebugAgent",
        instructions="Run the debug tool to show context visibility.",
        tools=[debug_context]
    )
    
    result = await Runner.run(
        agent,
        "Run the debug tool to show what information is visible to you versus what's in the context",
        context=context,
        max_turns=3
    )
    
    print("Agent Response:")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main()) 