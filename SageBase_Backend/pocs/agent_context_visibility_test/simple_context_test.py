#!/usr/bin/env python3
"""
Simple test demonstrating systematic context passing to tools.

This script shows the core concept: how to pass sensitive context variables
to all tools without exposing them to the OpenAI agent.
"""

import asyncio
import json
from dataclasses import dataclass
from typing import List

from agents import Agent, Runner, function_tool, RunContextWrapper

# ============================================================================
# Context with Sensitive Information
# ============================================================================

@dataclass
class UserContext:
    """Context containing sensitive user information."""
    
    # Sensitive data (NOT exposed to OpenAI)
    user_id: str
    api_token: str
    session_token: str
    database_password: str
    
    # Non-sensitive data
    user_name: str
    preferences: List[str]

# ============================================================================
# Tools that Access Context
# ============================================================================

@function_tool
async def get_user_info(context: RunContextWrapper[UserContext]) -> str:
    """
    Get current user information.
    
    This tool demonstrates accessing sensitive context data that is NOT exposed to OpenAI.
    """
    # Access sensitive context data
    user_id = context.context.user_id
    user_name = context.context.user_name
    api_token = context.context.api_token  # This is NOT visible to the LLM
    
    return f"User: {user_name} (ID: {user_id}) - Authenticated with token"


@function_tool
async def search_database(
    context: RunContextWrapper[UserContext],
    query: str
) -> str:
    """
    Search the user's database.
    
    Args:
        query: The search query to perform
    """
    # Access sensitive authentication data
    user_id = context.context.user_id
    api_token = context.context.api_token
    db_password = context.context.database_password
    
    # In a real app, you'd use these tokens to authenticate
    return f"Search results for '{query}' in user {user_id}'s database (authenticated)"


@function_tool
async def create_document(
    context: RunContextWrapper[UserContext],
    title: str,
    content: str
) -> str:
    """
    Create a document for the current user.
    
    Args:
        title: Document title
        content: Document content
    """
    # Access sensitive session data
    user_id = context.context.user_id
    session_token = context.context.session_token
    
    return f"Document '{title}' created for user {user_id} (session authenticated)"


@function_tool
async def debug_context(context: RunContextWrapper[UserContext]) -> str:
    """
    Debug tool to show what's visible to the LLM vs what's in context.
    """
    # What the LLM can see (function signature only)
    llm_visible = {
        "function": "debug_context(context: RunContextWrapper[UserContext]) -> str",
        "docstring": "Debug tool to show what's visible to the LLM vs what's in context."
    }
    
    # What's actually in context (sensitive data)
    context_data = {
        "user_id": context.context.user_id,
        "user_name": context.context.user_name,
        "has_api_token": bool(context.context.api_token),
        "has_session_token": bool(context.context.session_token),
        "has_db_password": bool(context.context.database_password)
    }
    
    return f"""
LLM Visible (in tool schema):
{json.dumps(llm_visible, indent=2)}

Actual Context Data (NOT visible to LLM):
{json.dumps(context_data, indent=2)}

The LLM only sees the function signature, not the actual sensitive data!
"""

# ============================================================================
# Test Function
# ============================================================================

async def test_context_passing():
    """Test that context is passed to tools but not exposed to LLM."""
    
    # Create context with sensitive data
    context = UserContext(
        user_id="user_123",
        api_token="secret_api_token_abc123",
        session_token="secret_session_token_xyz789",
        database_password="secret_db_password",
        user_name="John Doe",
        preferences=["dark_mode", "notifications"]
    )
    
    # Create agent with context-aware tools
    agent = Agent(
        name="ContextTestAgent",
        instructions="""
        You are a test agent that demonstrates context passing to tools.
        
        You have access to tools that can access user data using sensitive context
        information that is NOT exposed to you.
        
        When asked to perform operations, use the appropriate tools.
        The tools will handle authentication using the context that was passed to them.
        """,
        tools=[
            get_user_info,
            search_database,
            create_document,
            debug_context
        ]
    )
    
    # Test the agent
    print("Testing context passing to tools...")
    print("="*50)
    
    result = await Runner.run(
        agent,
        """
        Please help me with the following:
        1. Get my user information
        2. Search my database for 'important files'
        3. Create a document titled 'Test Document' with content 'This is a test'
        4. Run the debug tool to show what information is available
        """,
        context=context,
        max_turns=8
    )
    
    print("Agent Response:")
    print(result.final_output)
    print("\n" + "="*50)
    print("✅ Test completed! Context was passed to tools but not exposed to LLM.")


if __name__ == "__main__":
    # Check for OpenAI API key
    import os
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Please set OPENAI_API_KEY environment variable")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        exit(1)
    
    # Run the test
    asyncio.run(test_context_passing()) 