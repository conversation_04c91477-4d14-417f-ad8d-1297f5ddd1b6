#!/usr/bin/env python3
"""
Simple Parallel Tool Calls Demo

Demonstrates that parallel_tool_calls=True makes the LLM call async tools in parallel.
"""

import asyncio
import time
from agents import Agent, Runner, function_tool, ModelSettings

# ============================================================================
# Async Tools with Clear Timing
# ============================================================================

@function_tool
async def fetch_user_data(user_id: str) -> str:
    """Fetch user data asynchronously."""
    print(f"🔄 Starting fetch_user_data for {user_id}")
    await asyncio.sleep(2)  # Simulate API call
    print(f"✅ Completed fetch_user_data for {user_id}")
    return f"User data for {user_id}: email=<EMAIL>"

@function_tool
async def fetch_user_preferences(user_id: str) -> str:
    """Fetch user preferences asynchronously."""
    print(f"🔄 Starting fetch_user_preferences for {user_id}")
    await asyncio.sleep(2)  # Simulate API call
    print(f"✅ Completed fetch_user_preferences for {user_id}")
    return f"Preferences for {user_id}: dark_mode=true"

@function_tool
async def fetch_user_activity(user_id: str) -> str:
    """Fetch user activity asynchronously."""
    print(f"🔄 Starting fetch_user_activity for {user_id}")
    await asyncio.sleep(2)  # Simulate API call
    print(f"✅ Completed fetch_user_activity for {user_id}")
    return f"Activity for {user_id}: 15 logins this month"

# ============================================================================
# Test Function
# ============================================================================

async def test_parallel_async_tools():
    """Test that parallel_tool_calls=True makes LLM call async tools in parallel."""
    print("Parallel Async Tool Calls Demo")
    print("="*50)
    print("This demo shows that parallel_tool_calls=True makes the LLM")
    print("call multiple async tools simultaneously instead of sequentially.")
    print()
    
    agent = Agent(
        name="ParallelAsyncAgent",
        instructions="""
        You are testing parallel async tool execution.
        
        IMPORTANT: When asked to fetch user information, call ALL three tools in parallel:
        - fetch_user_data
        - fetch_user_preferences
        - fetch_user_activity
        
        Each tool takes 2 seconds, so if they run in parallel, all should start at the same time.
        """,
        tools=[
            fetch_user_data,
            fetch_user_preferences,
            fetch_user_activity
        ],
        model_settings=ModelSettings(
            parallel_tool_calls=True,  # This enables parallel execution
            temperature=0.1
        )
    )
    
    print("🔄 Running agent with parallel_tool_calls=True...")
    print("Watch the console output - all tools should start simultaneously!")
    print()
    
    result = await Runner.run(
        agent,
        "Fetch all information for user 'user_123'",
        max_turns=3
    )
    
    print()
    print("Agent Response:")
    print(result.final_output)
    print()
    print("✅ Demo completed!")
    print("If you saw all three '🔄 Starting...' messages appear together,")
    print("then parallel_tool_calls=True is working correctly!")


if __name__ == "__main__":
    import os
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Please set OPENAI_API_KEY environment variable")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        exit(1)
    
    asyncio.run(test_parallel_async_tools()) 