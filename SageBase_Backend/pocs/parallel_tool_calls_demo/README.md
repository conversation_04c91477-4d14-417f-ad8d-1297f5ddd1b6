# Parallel Tool Calls Demo

**Proof of Concept**: Demonstrates that `parallel_tool_calls=True` makes the LLM call async tools in parallel.

## Key Concept

✅ **parallel_tool_calls=True** enables the LLM to call multiple async tools simultaneously  
✅ **Console output shows** all tools starting at the same time  
✅ **Simple demonstration** of the feature in action  

## Quick Start

```bash
export OPENAI_API_KEY="your-api-key-here"
python simple_parallel_test.py
```

## How It Works

### 1. Enable Parallel Tool Calls
```python
from agents import Agent, ModelSettings

agent = Agent(
    name="ParallelAgent",
    instructions="Call all tools in parallel when fetching user information.",
    tools=[fetch_user_data, fetch_user_preferences, fetch_user_activity],
    model_settings=ModelSettings(
        parallel_tool_calls=True,  # This enables parallel execution
        temperature=0.1
    )
)
```

### 2. Create Async Tools
```python
@function_tool
async def fetch_user_data(user_id: str) -> str:
    """Fetch user data asynchronously."""
    print(f"🔄 Starting fetch_user_data for {user_id}")
    await asyncio.sleep(2)  # Simulate API call
    print(f"✅ Completed fetch_user_data for {user_id}")
    return f"User data for {user_id}: email=<EMAIL>"
```

### 3. Watch the Console Output
When you run the demo, you should see all three "🔄 Starting..." messages appear together, indicating parallel execution.

## Expected Output

```
🔄 Running agent with parallel_tool_calls=True...
Watch the console output - all tools should start simultaneously!

🔄 Starting fetch_user_data for user_123
🔄 Starting fetch_user_preferences for user_123
🔄 Starting fetch_user_activity for user_123
✅ Completed fetch_user_data for user_123
✅ Completed fetch_user_preferences for user_123
✅ Completed fetch_user_activity for user_123
```

## What This Demonstrates

1. **Parallel Execution**: All three async tools start simultaneously
2. **LLM Behavior**: The LLM decides to call all tools in parallel
3. **SDK Feature**: `parallel_tool_calls=True` enables this capability

## Files

- `simple_parallel_test.py` - Simple demo showing parallel async tool execution

This feature allows the LLM to execute multiple async operations simultaneously, improving performance! 