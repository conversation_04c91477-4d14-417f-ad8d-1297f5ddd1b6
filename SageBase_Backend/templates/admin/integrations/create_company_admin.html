{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title }}{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
.form-row {
    margin-bottom: 20px;
}

.form-row label {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    display: block;
}

.form-row input[type="text"], 
.form-row input[type="email"] {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-row .help {
    font-size: 12px;
    color: #666;
    margin-top: 3px;
}

.submit-row {
    background: #f8f8f8;
    border-top: 1px solid #ddd;
    margin-top: 30px;
    padding: 15px 0;
}

.button {
    background: #417690;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
}

.button:hover {
    background: #205067;
}

.button.cancel {
    background: #6c757d;
}

.button.cancel:hover {
    background: #545b62;
}

.errorlist {
    color: #d63384;
    list-style: none;
    padding: 0;
    margin: 5px 0;
}

.errorlist li {
    margin-bottom: 5px;
}

.header-info {
    background: #e7f3ff;
    border: 1px solid #b3d7ff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 25px;
}

.header-info h3 {
    margin: 0 0 10px 0;
    color: #0066cc;
}

.header-info p {
    margin: 5px 0;
    color: #333;
}

.workflow-steps {
    background: #f8f9fa;
    border-left: 4px solid #28a745;
    padding: 15px;
    margin-bottom: 25px;
}

.workflow-steps h4 {
    margin: 0 0 10px 0;
    color: #28a745;
}

.workflow-steps ol {
    margin: 0;
    padding-left: 20px;
}

.workflow-steps li {
    margin-bottom: 5px;
    color: #333;
}
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label='integrations' %}">Integrations</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="header-info">
    <h3>🚀 One-Click Company + Admin Creation</h3>
    <p><strong>What this does:</strong> Creates a new company and admin user in one step, automatically sends welcome <NAME_EMAIL></p>
    <p><strong>Result:</strong> Admin receives professional email with Supabase login credentials and can immediately access the platform</p>
</div>

<div class="workflow-steps">
    <h4>✅ What Happens Automatically:</h4>
    <ol>
        <li>Company created in Django database</li>
        <li>Default knowledge space created for the company</li>
        <li>Admin user created in Django database</li>
        <li>Admin user created in Supabase Auth with temporary password</li>
        <li>Professional welcome email sent <NAME_EMAIL></li>
        <li>Admin can immediately login to frontend with credentials from email</li>
    </ol>
</div>

{% if messages %}
<div class="messagelist">
    {% for message in messages %}
    <div class="{% if message.tags %}{{ message.tags }}{% endif %}">{{ message|capfirst }}</div>
    {% endfor %}
</div>
{% endif %}

<form method="post" novalidate>
    {% csrf_token %}
    
    <fieldset class="module aligned">
        <h2>Company Information</h2>
        
        <div class="form-row">
            {{ form.company_name.label_tag }}
            {{ form.company_name }}
            {% if form.company_name.help_text %}
                <div class="help">{{ form.company_name.help_text }}</div>
            {% endif %}
            {% if form.company_name.errors %}
                <ul class="errorlist">
                    {% for error in form.company_name.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        
        <div class="form-row">
            {{ form.company_email.label_tag }}
            {{ form.company_email }}
            {% if form.company_email.help_text %}
                <div class="help">{{ form.company_email.help_text }}</div>
            {% endif %}
            {% if form.company_email.errors %}
                <ul class="errorlist">
                    {% for error in form.company_email.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    </fieldset>
    
    <fieldset class="module aligned">
        <h2>Admin User Information</h2>
        
        <div class="form-row">
            {{ form.admin_first_name.label_tag }}
            {{ form.admin_first_name }}
            {% if form.admin_first_name.errors %}
                <ul class="errorlist">
                    {% for error in form.admin_first_name.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        
        <div class="form-row">
            {{ form.admin_last_name.label_tag }}
            {{ form.admin_last_name }}
            {% if form.admin_last_name.errors %}
                <ul class="errorlist">
                    {% for error in form.admin_last_name.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        
        <div class="form-row">
            {{ form.admin_email.label_tag }}
            {{ form.admin_email }}
            {% if form.admin_email.help_text %}
                <div class="help">{{ form.admin_email.help_text }}</div>
            {% endif %}
            {% if form.admin_email.errors %}
                <ul class="errorlist">
                    {% for error in form.admin_email.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    </fieldset>

    <div class="submit-row">
        <input type="submit" value="🚀 Create Company + Admin" class="button" name="_save">
        <a href="{% url 'admin:integrations_company_changelist' %}" class="button cancel">Cancel</a>
    </div>
</form>

<div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
    <h4 style="color: #856404; margin: 0 0 10px 0;">💡 What happens next:</h4>
    <ul style="margin: 0; color: #856404;">
        <li>Admin receives welcome <NAME_EMAIL> within seconds</li>
        <li>Admin can login at your frontend using email + temporary password from email</li>
        <li>Admin should reset password immediately for security</li>
        <li>Admin can then invite team members through your frontend interface</li>
    </ul>
</div>

{% endblock %}