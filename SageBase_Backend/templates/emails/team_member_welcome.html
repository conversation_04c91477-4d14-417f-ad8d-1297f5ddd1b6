<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{ user.company.name|title }} on SageBase!</title>
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            margin: 0;
            padding: 0;
        }
        
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Content */
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .message {
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        
        /* CTA Button */
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }
        
        /* Features list */
        .features {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .features h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
            line-height: 1.5;
        }
        
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #3498db;
            font-weight: bold;
            font-size: 16px;
        }
        
        /* Footer */
        .footer {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 25px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer .brand {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .footer .tagline {
            opacity: 0.8;
            font-style: italic;
        }
        
        /* Responsive design */
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 4px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 25px 20px;
            }
            
            .cta-button {
                padding: 12px 25px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Welcome to {{ user.company.name|title }}!</h1>
            <p class="subtitle">You're now part of the team on SageBase</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ user.first_name }},</div>
            
            <div class="message">
                Congratulations! You've successfully joined <strong>{{ user.company.name|title }}</strong> on SageBase. 
                Welcome to your team's intelligent knowledge management workspace!
            </div>
            
            <!-- CTA Button -->
            <div class="cta-container">
                <a href="{{ login_url }}" class="cta-button">
                    🚀 Access Your Workspace
                </a>
            </div>
            
            <!-- Features -->
            <div class="features">
                <h3>As a team member, you can:</h3>
                <ul>
                    <li>Access your company's knowledge spaces and documentation</li>
                    <li>Collaborate with your team on projects and workflows</li>
                    <li>Use integrated tools and applications</li>
                    <li>Get AI-powered insights from your team's data</li>
                    <li>Contribute to your team's knowledge base</li>
                    <li>Stay updated with real-time notifications</li>
                </ul>
            </div>
            
            <div class="message">
                Your team is excited to have you aboard! If you have any questions or need assistance, 
                don't hesitate to reach out to your team admin or our support team.
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="brand">SageBase</div>
            <div class="tagline">Empowering teams with intelligent knowledge management</div>
        </div>
    </div>
</body>
</html>