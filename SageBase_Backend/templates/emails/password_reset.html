<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - SageBase</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8fafc;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-top: 40px;
            margin-bottom: 40px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            color: #ffffff;
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }
        .header .subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 10px 0 0 0;
            font-size: 16px;
            font-weight: 400;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 24px;
            color: #374151;
        }
        .main-message {
            font-size: 16px;
            margin-bottom: 30px;
            color: #6b7280;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            margin: 20px 0;
        }
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        .security-notice {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
            padding: 20px;
            margin: 30px 0;
            border-radius: 8px;
        }
        .security-notice h3 {
            color: #92400e;
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .security-notice p {
            color: #92400e;
            margin: 0;
            font-size: 14px;
        }
        .footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            color: #6b7280;
            font-size: 14px;
            margin: 0;
        }
        .footer-links {
            margin-top: 20px;
        }
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin: 0 15px;
        }
        .footer-links a:hover {
            text-decoration: underline;
        }
        .brand {
            color: #667eea;
            font-weight: 700;
            font-size: 20px;
        }
        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e5e7eb, transparent);
            margin: 30px 0;
        }
        .icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 600px) {
            .container {
                margin: 20px 10px;
                border-radius: 12px;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .header h1 {
                font-size: 28px;
            }
            .reset-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Password Reset</h1>
            <p class="subtitle">Secure access to your SageBase account</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello {{ user.first_name }},
            </div>
            
            <div class="main-message">
                You requested a password reset for your SageBase account. No worries! Click the button below to create a new password and regain access to your account.
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <a href="{{ reset_url }}" class="reset-button">
                    🔑 Reset My Password
                </a>
            </div>
            
            <div class="security-notice">
                <h3>
                    <span class="icon">⚠️</span>
                    Security Notice
                </h3>
                <p>
                    This password reset link will expire in <strong>24 hours</strong> for security reasons. 
                    If you didn't request a password reset, please ignore this email or contact our support team if you're concerned about your account security.
                </p>
            </div>
            
            <div class="divider"></div>
            
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
                If the button doesn't work, you can copy and paste this link into your browser:
            </p>
            <p style="color: #667eea; font-size: 14px; word-break: break-all; margin: 10px 0 0 0;">
                {{ reset_url }}
            </p>
        </div>
        
        <div class="footer">
            <p>
                <span class="brand">SageBase</span><br>
                Your intelligent knowledge management platform
            </p>
            
            <div class="footer-links">
                <a href="{{ login_url }}">Login</a>
                <a href="mailto:<EMAIL>">Support</a>
                <a href="https://sagebase.tech">Website</a>
            </div>
        </div>
    </div>
</body>
</html>