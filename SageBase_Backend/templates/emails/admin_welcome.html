<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to SageBase - Your Company Account is Ready!</title>
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            margin: 0;
            padding: 0;
        }
        
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Content */
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .message {
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        
        .credentials-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .credentials-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .credential-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .credential-label {
            font-weight: 600;
            color: #495057;
        }
        
        .credential-value {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 1px solid #ced4da;
            word-break: break-all;
        }
        
        /* CTA Button */
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        /* Features list */
        .features {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .features h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
            line-height: 1.5;
        }
        
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
            font-size: 16px;
        }
        
        /* Security notice */
        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            border-left: 5px solid #ffb307;
        }
        
        .security-notice .icon {
            color: #856404;
            font-size: 18px;
            font-weight: bold;
        }
        
        .security-notice p {
            margin: 5px 0 0 0;
            color: #856404;
            font-size: 14px;
        }
        
        /* Footer */
        .footer {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 25px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer .brand {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .footer .tagline {
            opacity: 0.8;
            font-style: italic;
        }
        
        /* Responsive design */
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 4px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 25px 20px;
            }
            
            .credentials-box {
                padding: 20px 15px;
            }
            
            .credential-item {
                flex-direction: column;
                align-items: stretch;
            }
            
            .credential-value {
                margin-top: 5px;
                text-align: center;
            }
            
            .cta-button {
                padding: 12px 25px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Welcome to SageBase!</h1>
            <p class="subtitle">Your company account is ready to go</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ user.first_name }},</div>
            
            <div class="message">
                Congratulations! Your company account for <strong>{{ user.company.name|title }}</strong> has been created successfully on SageBase.
            </div>
            
            <!-- Credentials Box -->
            <div class="credentials-box">
                <div class="credentials-title">🔐 Your Login Credentials</div>
                
                <div class="credential-item">
                    <span class="credential-label">Email:</span>
                    <span class="credential-value">{{ user.email }}</span>
                </div>
                
                <div class="credential-item">
                    <span class="credential-label">Temporary Password:</span>
                    <span class="credential-value">{{ temp_password }}</span>
                </div>
            </div>
            
            <!-- CTA Button -->
            <div class="cta-container">
                <a href="{{ login_url }}" class="cta-button">
                    🚀 Log In & Get Started
                </a>
            </div>
            
            <!-- Security Notice -->
            <div class="security-notice">
                <div class="icon">🛡️ Security Notice</div>
                <p><strong>Please change your password immediately</strong> after logging in for security purposes.</p>
            </div>
            
            <!-- Features -->
            <div class="features">
                <h3>As an admin, you can now:</h3>
                <ul>
                    <li>Invite team members to join your company workspace</li>
                    <li>Set up integrations with GitHub, Slack, and other essential tools</li>
                    <li>Manage your company's knowledge spaces and documentation</li>
                    <li>Access AI-powered insights and analytics</li>
                    <li>Configure team workflows and automation</li>
                    <li>Monitor project health and team collaboration</li>
                </ul>
            </div>
            
            <div class="message">
                Need help getting started? Our support team is here to assist you every step of the way.
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="brand">SageBase</div>
            <div class="tagline">Empowering teams with intelligent knowledge management</div>
        </div>
    </div>
</body>
</html>