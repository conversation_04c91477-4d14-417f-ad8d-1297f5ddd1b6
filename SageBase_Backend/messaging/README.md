# SageBase Messaging System

A unified messaging interface for integrating Slack, Microsoft Teams, and Discord into your SageBase application. This system provides a consistent API for sending messages, receiving messages, and managing group chats across multiple messaging platforms.

## 🏗️ Architecture

```
messaging/
├── __init__.py
├── base_interface.py          # Abstract base interface
├── examples.py               # Usage examples and integrations
├── README.md                # This file
├── slack/
│   ├── __init__.py
│   └── slack_service.py     # Slack implementation
├── MsTeams/
│   ├── __init__.py
│   └── teams_service.py     # Microsoft Teams implementation
└── discord/
    ├── __init__.py
    └── discord_service.py   # Discord implementation
```

## 🚀 Quick Start

### 1. Installation Requirements

Add these dependencies to your `requirements.txt`:

```txt
aiohttp>=3.8.0
asyncio
```

### 2. Basic Usage

```python
import asyncio
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType

async def main():
    # Initialize service
    slack_service = SlackService()
    
    # Authenticate
    credentials = {
        "bot_token": "xoxb-your-bot-token-here"
    }
    
    if await slack_service.authenticate(credentials):
        # Send a message
        message = Message(
            content="Hello from SageBase! 👋",
            message_type=MessageType.TEXT
        )
        
        result = await slack_service.send_message("C1234567890", message)
        print(f"Message sent: {result}")
        
        # Clean up
        await slack_service.disconnect()

asyncio.run(main())
```

## 📚 Platform-Specific Setup

### Slack Setup

1. **Create a Slack Bot:**
   - Go to [Slack API](https://api.slack.com/apps)
   - Create a new app
   - Add Bot Token Scopes: `chat:write`, `channels:read`, `users:read`, `groups:write`
   - Install the app to your workspace

2. **Authentication:**
   ```python
   credentials = {
       "bot_token": "xoxb-your-bot-token-here",
       "user_token": "xoxp-your-user-token-here"  # Optional
   }
   ```

3. **Channel ID Format:**
   - Channel IDs look like: `C1234567890`
   - Use channel ID directly

### Microsoft Teams Setup

1. **Create Azure App Registration:**
   - Go to [Azure Portal](https://portal.azure.com)
   - Create new App Registration
   - Add API permissions: `Channel.ReadWrite.All`, `Chat.ReadWrite`, `User.Read.All`
   - Create client secret

2. **Authentication:**
   ```python
   credentials = {
       "tenant_id": "your-tenant-id",
       "client_id": "your-client-id",
       "client_secret": "your-client-secret"
   }
   ```

3. **Channel ID Format:**
   - Use format: `team_id/channel_id`
   - Example: `19:abcd1234.../19:xyz789...`

### Discord Setup

1. **Create Discord Bot:**
   - Go to [Discord Developer Portal](https://discord.com/developers/applications)
   - Create new application
   - Create bot and copy token
   - Enable required intents: `MESSAGE_CONTENT_INTENT`

2. **Authentication:**
   ```python
   credentials = {
       "bot_token": "your-bot-token-here"
   }
   ```

3. **Channel ID Format:**
   - Channel IDs are numeric: `1234567890123456789`

## 🔧 Core Interface

All messaging services implement the `MessagingInterface` with these methods:

### `send_message(channel_id: str, message: Message) -> Dict[str, Any]`
Send a message to a specific channel.

```python
message = Message(
    content="Hello World!",
    message_type=MessageType.TEXT
)
result = await service.send_message("channel_id", message)
```

### `receive_messages(channel_id: str, limit: int = 10) -> List[ReceivedMessage]`
Retrieve recent messages from a channel.

```python
messages = await service.receive_messages("channel_id", limit=20)
for msg in messages:
    print(f"{msg.sender_name}: {msg.content}")
```

### `create_group_chat(name: str, members: List[str]) -> GroupChat`
Create a new group chat with specified members.

```python
group_chat = await service.create_group_chat(
    "Project Team", 
    ["user_id_1", "user_id_2"]
)
```

### `get_channels() -> List[Dict[str, Any]]`
Get list of available channels.

```python
channels = await service.get_channels()
for channel in channels:
    print(f"{channel['name']}: {channel['id']}")
```

### `get_users() -> List[Dict[str, Any]]`
Get list of available users.

```python
users = await service.get_users()
for user in users:
    print(f"{user['name']}: {user['id']}")
```

## 📨 Message Types

### Text Messages
```python
message = Message(
    content="Simple text message",
    message_type=MessageType.TEXT
)
```

### File Messages
```python
message = Message(
    content="Check out this file!",
    message_type=MessageType.FILE,
    metadata={
        "file_path": "/path/to/file.pdf",
        "filename": "document.pdf"
    }
)
```

### Rich Messages (Embeds)
```python
# Slack example
message = Message(
    content="Rich message with attachments",
    message_type=MessageType.EMBED,
    metadata={
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "Hello *World*!"
                }
            }
        ]
    }
)
```

## 🔗 Integration Examples

### GitHub Webhook Integration

```python
from messaging.examples import GitHubWebhookNotifier

# Set up notifier
slack_service = SlackService()
await slack_service.authenticate(credentials)
notifier = GitHubWebhookNotifier(slack_service)

# Handle webhook
webhook_data = {
    "repository": {"name": "SageBase_Backend"},
    "pusher": {"name": "developer"},
    "commits": [...]
}

await notifier.notify_push_event("C1234567890", webhook_data)
```

### Vector Database Search Results

```python
from messaging.examples import VectorDBSearchNotifier

# Share search results
notifier = VectorDBSearchNotifier(teams_service)
await notifier.share_search_results(
    "team_id/channel_id", 
    "search query", 
    search_results
)
```

### LLM Question Answering

```python
from messaging.examples import LLMAnswerNotifier

# Share AI answers
notifier = LLMAnswerNotifier(discord_service)
await notifier.share_llm_answer(
    "1234567890123456789",
    "What is SageBase?",
    "SageBase is a comprehensive backend system...",
    ["document1.pdf", "readme.md"]
)
```

### Multi-Platform Broadcasting

```python
from messaging.examples import MultiPlatformBroadcaster

broadcaster = MultiPlatformBroadcaster()

# Add services
await broadcaster.add_service("slack", slack_service, slack_credentials)
await broadcaster.add_service("teams", teams_service, teams_credentials)

# Broadcast message
message = Message(content="🚀 Deployment completed!")
channel_mapping = {
    "slack": "C1234567890",
    "teams": "team_id/channel_id"
}

results = await broadcaster.broadcast_message(channel_mapping, message)
```

## 🔧 Django Integration

### Settings Configuration

Add to your `settings.py`:

```python
# Slack Configuration
SLACK_BOT_TOKEN = "xoxb-your-bot-token"
SLACK_SIGNING_SECRET = "your-signing-secret"

# Teams Configuration
TEAMS_TENANT_ID = "your-tenant-id"
TEAMS_CLIENT_ID = "your-client-id"
TEAMS_CLIENT_SECRET = "your-client-secret"

# Discord Configuration
DISCORD_BOT_TOKEN = "your-bot-token"
```

### View Integration

```python
from django.http import JsonResponse
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType
import asyncio

async def send_notification_async(message_text, channel_id):
    slack_service = SlackService()
    credentials = {"bot_token": settings.SLACK_BOT_TOKEN}
    
    if await slack_service.authenticate(credentials):
        message = Message(content=message_text, message_type=MessageType.TEXT)
        result = await slack_service.send_message(channel_id, message)
        await slack_service.disconnect()
        return result
    return {"status": "error", "error": "Authentication failed"}

def send_notification(request):
    message_text = request.POST.get('message')
    channel_id = request.POST.get('channel_id')
    
    # Run async function
    result = asyncio.run(send_notification_async(message_text, channel_id))
    return JsonResponse(result)
```

### Background Task Integration

```python
from celery import shared_task
from messaging.slack.slack_service import SlackService
import asyncio

@shared_task
def send_slack_notification(message_text, channel_id):
    """Celery task to send Slack notification."""
    
    async def _send():
        slack_service = SlackService()
        credentials = {"bot_token": settings.SLACK_BOT_TOKEN}
        
        if await slack_service.authenticate(credentials):
            message = Message(content=message_text, message_type=MessageType.TEXT)
            result = await slack_service.send_message(channel_id, message)
            await slack_service.disconnect()
            return result
        return {"status": "error"}
    
    return asyncio.run(_send())
```

## 🛠️ Advanced Features

### Custom Message Handlers

```python
class CustomMessageHandler:
    def __init__(self, messaging_service):
        self.messaging_service = messaging_service
    
    async def handle_command(self, channel_id: str, command: str, args: List[str]):
        """Handle custom commands from messages."""
        if command == "status":
            await self.send_status_update(channel_id)
        elif command == "help":
            await self.send_help_message(channel_id)
    
    async def send_status_update(self, channel_id: str):
        # Get system status
        status_message = "🟢 All systems operational"
        message = Message(content=status_message, message_type=MessageType.TEXT)
        await self.messaging_service.send_message(channel_id, message)
```

### Message Scheduling

```python
from datetime import datetime, timedelta
import asyncio

class MessageScheduler:
    def __init__(self, messaging_service):
        self.messaging_service = messaging_service
        self.scheduled_messages = []
    
    def schedule_message(self, channel_id: str, message: Message, send_at: datetime):
        """Schedule a message to be sent at a specific time."""
        self.scheduled_messages.append({
            "channel_id": channel_id,
            "message": message,
            "send_at": send_at
        })
    
    async def process_scheduled_messages(self):
        """Process all scheduled messages."""
        now = datetime.now()
        messages_to_send = []
        
        for scheduled in self.scheduled_messages:
            if scheduled["send_at"] <= now:
                messages_to_send.append(scheduled)
        
        # Remove processed messages
        for msg in messages_to_send:
            self.scheduled_messages.remove(msg)
            await self.messaging_service.send_message(
                msg["channel_id"], 
                msg["message"]
            )
```

## 🐛 Error Handling

### Connection Errors

```python
try:
    result = await service.send_message(channel_id, message)
    if result["status"] == "error":
        print(f"Message failed: {result['error']}")
except Exception as e:
    print(f"Connection error: {e}")
    # Implement retry logic
```

### Rate Limiting

```python
import asyncio
from datetime import datetime, timedelta

class RateLimiter:
    def __init__(self, max_requests=30, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    async def acquire(self):
        """Acquire rate limit token."""
        now = datetime.now()
        
        # Remove old requests
        self.requests = [req for req in self.requests 
                        if now - req < timedelta(seconds=self.time_window)]
        
        if len(self.requests) >= self.max_requests:
            # Wait until we can make another request
            sleep_time = self.time_window - (now - self.requests[0]).total_seconds()
            await asyncio.sleep(sleep_time)
        
        self.requests.append(now)
```

## 🔐 Security Best Practices

### Token Management

```python
import os
from cryptography.fernet import Fernet

class SecureCredentialManager:
    def __init__(self):
        self.key = os.environ.get('ENCRYPTION_KEY', Fernet.generate_key())
        self.cipher = Fernet(self.key)
    
    def encrypt_token(self, token: str) -> str:
        """Encrypt a token for storage."""
        return self.cipher.encrypt(token.encode()).decode()
    
    def decrypt_token(self, encrypted_token: str) -> str:
        """Decrypt a token for use."""
        return self.cipher.decrypt(encrypted_token.encode()).decode()
```

### Input Validation

```python
def validate_message_content(content: str) -> bool:
    """Validate message content for security."""
    # Check for potentially harmful content
    blocked_patterns = ['<script>', 'javascript:', 'data:']
    
    for pattern in blocked_patterns:
        if pattern.lower() in content.lower():
            return False
    
    # Check message length
    if len(content) > 4000:  # Most platforms have limits
        return False
    
    return True
```

## 📊 Monitoring and Logging

### Message Logging

```python
import logging
from datetime import datetime

class MessageLogger:
    def __init__(self):
        self.logger = logging.getLogger('messaging')
        self.logger.setLevel(logging.INFO)
        
        handler = logging.FileHandler('messaging.log')
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_message_sent(self, platform: str, channel_id: str, message_id: str):
        """Log successful message send."""
        self.logger.info(f"Message sent - Platform: {platform}, Channel: {channel_id}, ID: {message_id}")
    
    def log_message_failed(self, platform: str, channel_id: str, error: str):
        """Log failed message send."""
        self.logger.error(f"Message failed - Platform: {platform}, Channel: {channel_id}, Error: {error}")
```

### Performance Metrics

```python
import time
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class MessageMetrics:
    platform: str
    channel_id: str
    message_size: int
    send_time: float
    success: bool
    error: str = None

class MetricsCollector:
    def __init__(self):
        self.metrics: List[MessageMetrics] = []
    
    async def send_with_metrics(self, service, channel_id: str, message: Message):
        """Send message and collect metrics."""
        start_time = time.time()
        
        try:
            result = await service.send_message(channel_id, message)
            send_time = time.time() - start_time
            
            metrics = MessageMetrics(
                platform=service.__class__.__name__,
                channel_id=channel_id,
                message_size=len(message.content),
                send_time=send_time,
                success=result.get("status") == "success",
                error=result.get("error")
            )
            
            self.metrics.append(metrics)
            return result
            
        except Exception as e:
            send_time = time.time() - start_time
            metrics = MessageMetrics(
                platform=service.__class__.__name__,
                channel_id=channel_id,
                message_size=len(message.content),
                send_time=send_time,
                success=False,
                error=str(e)
            )
            self.metrics.append(metrics)
            raise
    
    def get_success_rate(self, platform: str = None) -> float:
        """Calculate success rate for platform or overall."""
        relevant_metrics = self.metrics
        if platform:
            relevant_metrics = [m for m in self.metrics if m.platform == platform]
        
        if not relevant_metrics:
            return 0.0
        
        successful = sum(1 for m in relevant_metrics if m.success)
        return successful / len(relevant_metrics)
```

## 🧪 Testing

### Unit Tests

```python
import unittest
from unittest.mock import AsyncMock, patch
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType

class TestSlackService(unittest.IsolatedAsyncioTestCase):
    async def test_send_message_success(self):
        service = SlackService()
        service.authenticated = True
        service.bot_token = "test-token"
        
        # Mock the HTTP session
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {
                "ok": True,
                "ts": "1234567890.123456",
                "channel": "C1234567890"
            }
            
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
            service.session = mock_session.return_value.__aenter__.return_value
            
            message = Message(content="Test message", message_type=MessageType.TEXT)
            result = await service.send_message("C1234567890", message)
            
            self.assertEqual(result["status"], "success")
            self.assertEqual(result["message_id"], "1234567890.123456")
```

### Integration Tests

```python
import asyncio
import pytest
from messaging.slack.slack_service import SlackService

@pytest.mark.asyncio
async def test_slack_integration():
    """Integration test with real Slack API (requires valid token)."""
    service = SlackService()
    
    # Use test credentials
    credentials = {
        "bot_token": "xoxb-test-token"  # Use actual test token
    }
    
    if await service.authenticate(credentials):
        # Test sending message
        message = Message(content="Integration test message", message_type=MessageType.TEXT)
        result = await service.send_message("C1234567890", message)
        
        assert result["status"] == "success"
        assert "message_id" in result
        
        await service.disconnect()
```

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check token validity and permissions
   - Verify bot is added to channels
   - Ensure correct credential format

2. **Rate Limiting**
   - Implement exponential backoff
   - Use rate limiter class
   - Monitor API quotas

3. **Channel Access**
   - Verify bot has access to channels
   - Check channel ID format
   - Ensure proper permissions

4. **Message Formatting**
   - Validate message content
   - Check platform-specific limits
   - Handle special characters

### Debug Mode

```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('messaging')

# Add debug information to services
class DebugSlackService(SlackService):
    async def send_message(self, channel_id: str, message: Message):
        logger.debug(f"Sending message to {channel_id}: {message.content[:100]}...")
        result = await super().send_message(channel_id, message)
        logger.debug(f"Send result: {result}")
        return result
```

## 📝 Contributing

When contributing to the messaging system:

1. Follow the existing interface patterns
2. Add comprehensive error handling
3. Include unit tests for new features
4. Update documentation and examples
5. Test with real platform APIs

### Adding New Platforms

To add a new messaging platform:

1. Create a new directory: `messaging/newplatform/`
2. Implement `MessagingInterface` in `newplatform_service.py`
3. Add authentication and API interaction logic
4. Create examples in `messaging/examples.py`
5. Update this README

## 📄 License

This messaging system is part of the SageBase project and follows the same license terms.

---

**📞 Support**: For questions or issues, please refer to the main SageBase documentation or create an issue in the project repository.