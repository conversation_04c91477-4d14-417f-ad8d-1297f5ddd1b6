import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp
import json
from ..base_interface import MessagingInterface, Message, GroupChat, ReceivedMessage, MessageType
import logging

logger = logging.getLogger(__name__)


class TeamsService(MessagingInterface):
    """Microsoft Teams messaging service implementation."""
    
    def __init__(self):
        self.access_token: Optional[str] = None
        self.tenant_id: Optional[str] = None
        self.client_id: Optional[str] = None
        self.client_secret: Optional[str] = None
        self.base_url = "https://graph.microsoft.com/v1.0"
        self.session: Optional[aiohttp.ClientSession] = None
        self.authenticated = False
    
    async def authenticate(self, credentials: Dict[str, str]) -> bool:
        """Authenticate with Microsoft Graph API."""
        try:
            self.tenant_id = credentials.get("tenant_id")
            self.client_id = credentials.get("client_id")
            self.client_secret = credentials.get("client_secret")
            
            if not all([self.tenant_id, self.client_id, self.client_secret]):
                return False
            
            self.session = aiohttp.ClientSession()
            
            # Get access token
            token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
            
            data = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "scope": "https://graph.microsoft.com/.default"
            }
            
            async with self.session.post(token_url, data=data) as response:
                if response.status == 200:
                    token_data = await response.json()
                    self.access_token = token_data.get("access_token")
                    self.authenticated = bool(self.access_token)
                    return self.authenticated
                return False
                
        except Exception as e:
            logger.debug(f"Teams authentication error: {e}")
            return False
    
    async def send_message(self, channel_id: str, message: Message) -> Dict[str, Any]:
        """Send a message to a Teams channel."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Teams")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # Parse team_id and channel_id from channel_id format: team_id/channel_id
        if "/" not in channel_id:
            return {"status": "error", "error": "Invalid channel_id format. Use team_id/channel_id"}
        
        team_id, actual_channel_id = channel_id.split("/", 1)
        
        payload = {
            "body": {
                "content": message.content,
                "contentType": "text"
            }
        }
        
        # Handle different message types
        if message.message_type == MessageType.EMBED and message.metadata:
            payload["body"]["contentType"] = "html"
            payload["attachments"] = message.metadata.get("attachments", [])
        elif message.message_type == MessageType.FILE and message.metadata:
            return await self._upload_file(team_id, actual_channel_id, message)
        
        try:
            endpoint = f"{self.base_url}/teams/{team_id}/channels/{actual_channel_id}/messages"
            async with self.session.post(endpoint, headers=headers, json=payload) as response:
                data = await response.json()
                
                if response.status == 201:
                    return {
                        "message_id": data.get("id"),
                        "status": "success",
                        "channel": channel_id,
                        "web_url": data.get("webUrl")
                    }
                else:
                    return {
                        "status": "error",
                        "error": data.get("error", {}).get("message", "Unknown error")
                    }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def send_private_message(self, user_id: str, message: Message) -> Dict[str, Any]:
        """Send a private message to a Teams user using Microsoft Graph API."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Teams")
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            # Create a chat with the user
            chat_data = {
                "chatType": "oneOnOne",
                "members": [
                    {
                        "@odata.type": "#microsoft.graph.aadUser",
                        "<EMAIL>": f"https://graph.microsoft.com/v1.0/users/{user_id}"
                    }
                ]
            }
            
            # Create the chat
            chat_response = await self.session.post(
                f"{self.base_url}/chats",
                headers=headers,
                json=chat_data
            )
            
            if chat_response.status != 201:
                return {
                    "status": "error",
                    "error": f"Failed to create chat: {chat_response.status}"
                }
            
            chat_data = await chat_response.json()
            chat_id = chat_data["id"]
            
            # Send message to the chat
            message_data = {
                "body": {
                    "content": message.content,
                    "contentType": "text"
                }
            }
            
            message_response = await self.session.post(
                f"{self.base_url}/chats/{chat_id}/messages",
                headers=headers,
                json=message_data
            )
            
            if message_response.status == 201:
                data = await message_response.json()
                return {
                    "message_id": data.get("id"),
                    "status": "success",
                    "chat_id": chat_id
                }
            else:
                return {
                    "status": "error",
                    "error": f"Failed to send message: {message_response.status}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _upload_file(self, team_id: str, channel_id: str, message: Message) -> Dict[str, Any]:
        """Upload a file to Teams."""
        if not message.metadata or "file_path" not in message.metadata:
            return {"status": "error", "error": "File path not provided"}
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            # First upload the file to OneDrive
            with open(message.metadata["file_path"], "rb") as file:
                file_data = file.read()
                filename = message.metadata.get("filename", "file")
                
                # Upload to team's drive
                upload_url = f"{self.base_url}/teams/{team_id}/drive/root:/{filename}:/content"
                
                async with self.session.put(upload_url, headers=headers, data=file_data) as response:
                    if response.status not in [200, 201]:
                        return {"status": "error", "error": "File upload failed"}
                    
                    file_info = await response.json()
                    
                    # Send message with file attachment
                    payload = {
                        "body": {
                            "content": message.content,
                            "contentType": "text"
                        },
                        "attachments": [{
                            "id": file_info.get("id"),
                            "contentType": "reference",
                            "contentUrl": file_info.get("webUrl"),
                            "name": filename
                        }]
                    }
                    
                    endpoint = f"{self.base_url}/teams/{team_id}/channels/{channel_id}/messages"
                    async with self.session.post(endpoint, headers=headers, json=payload) as msg_response:
                        msg_data = await msg_response.json()
                        
                        if msg_response.status == 201:
                            return {
                                "message_id": msg_data.get("id"),
                                "status": "success",
                                "file_url": file_info.get("webUrl")
                            }
                        else:
                            return {
                                "status": "error",
                                "error": "Failed to send message with file"
                            }
                            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def receive_messages(self, channel_id: str, limit: int = 10) -> List[ReceivedMessage]:
        """Retrieve recent messages from a Teams channel."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Teams")
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        # Parse team_id and channel_id
        if "/" not in channel_id:
            return []
        
        team_id, actual_channel_id = channel_id.split("/", 1)
        
        try:
            endpoint = f"{self.base_url}/teams/{team_id}/channels/{actual_channel_id}/messages"
            params = {"$top": limit, "$orderby": "createdDateTime desc"}
            
            async with self.session.get(endpoint, headers=headers, params=params) as response:
                data = await response.json()
                
                if response.status != 200:
                    return []
                
                messages = []
                for msg in data.get("value", []):
                    # Skip system messages
                    if msg.get("messageType") == "systemEventMessage":
                        continue
                    
                    message_type = MessageType.TEXT
                    if msg.get("attachments"):
                        message_type = MessageType.FILE
                    
                    sender = msg.get("from", {})
                    sender_name = sender.get("user", {}).get("displayName", "Unknown User")
                    sender_id = sender.get("user", {}).get("id", "")
                    
                    body = msg.get("body", {})
                    content = body.get("content", "")
                    
                    messages.append(ReceivedMessage(
                        id=msg.get("id"),
                        content=content,
                        sender_id=sender_id,
                        sender_name=sender_name,
                        channel_id=channel_id,
                        timestamp=msg.get("createdDateTime"),
                        message_type=message_type,
                        metadata={
                            "attachments": msg.get("attachments", []),
                            "web_url": msg.get("webUrl", ""),
                            "content_type": body.get("contentType", "text")
                        }
                    ))
                
                return messages
                
        except Exception as e:
            logger.debug(f"Error retrieving messages: {e}")
            return []
    
    async def create_group_chat(self, name: str, members: List[str]) -> GroupChat:
        """Create a new Teams group chat."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Teams")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # Create group chat members list
        chat_members = []
        for member_id in members:
            chat_members.append({
                "@odata.type": "#microsoft.graph.aadUserConversationMember",
                "<EMAIL>": f"https://graph.microsoft.com/v1.0/users('{member_id}')",
                "roles": ["member"]
            })
        
        payload = {
            "chatType": "group",
            "topic": name,
            "members": chat_members
        }
        
        try:
            endpoint = f"{self.base_url}/chats"
            async with self.session.post(endpoint, headers=headers, json=payload) as response:
                data = await response.json()
                
                if response.status == 201:
                    return GroupChat(
                        id=data.get("id"),
                        name=name,
                        members=members,
                        metadata={
                            "chat_type": data.get("chatType"),
                            "web_url": data.get("webUrl", ""),
                            "created_date": data.get("createdDateTime")
                        }
                    )
                else:
                    raise Exception(f"Failed to create group chat: {data.get('error', {}).get('message', 'Unknown error')}")
                
        except Exception as e:
            raise Exception(f"Error creating group chat: {e}")
    
    async def get_channels(self) -> List[Dict[str, Any]]:
        """Get list of Teams channels from all teams."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Teams")
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            channels = []
            
            # First get all teams
            teams_endpoint = f"{self.base_url}/me/joinedTeams"
            async with self.session.get(teams_endpoint, headers=headers) as response:
                if response.status != 200:
                    return []
                
                teams_data = await response.json()
                
                # For each team, get its channels
                for team in teams_data.get("value", []):
                    team_id = team.get("id")
                    team_name = team.get("displayName", "Unknown Team")
                    
                    channels_endpoint = f"{self.base_url}/teams/{team_id}/channels"
                    async with self.session.get(channels_endpoint, headers=headers) as channel_response:
                        if channel_response.status == 200:
                            channels_data = await channel_response.json()
                            
                            for channel in channels_data.get("value", []):
                                channels.append({
                                    "id": f"{team_id}/{channel.get('id')}",
                                    "name": f"{team_name} - {channel.get('displayName', 'Unknown Channel')}",
                                    "team_id": team_id,
                                    "team_name": team_name,
                                    "channel_id": channel.get("id"),
                                    "channel_name": channel.get("displayName"),
                                    "description": channel.get("description", ""),
                                    "web_url": channel.get("webUrl", "")
                                })
            
            return channels
            
        except Exception as e:
            logger.debug(f"Error retrieving channels: {e}")
            return []
    
    async def get_users(self) -> List[Dict[str, Any]]:
        """Get list of users in the organization."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Teams")
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            endpoint = f"{self.base_url}/users"
            params = {"$select": "id,displayName,mail,userPrincipalName,jobTitle"}
            
            async with self.session.get(endpoint, headers=headers, params=params) as response:
                data = await response.json()
                
                if response.status != 200:
                    return []
                
                users = []
                for user in data.get("value", []):
                    users.append({
                        "id": user.get("id"),
                        "name": user.get("displayName", ""),
                        "email": user.get("mail", ""),
                        "username": user.get("userPrincipalName", ""),
                        "job_title": user.get("jobTitle", ""),
                        "status": "unknown"  # Graph API doesn't provide presence in basic user info
                    })
                
                return users
                
        except Exception as e:
            logger.debug(f"Error retrieving users: {e}")
            return []
    
    async def disconnect(self) -> None:
        """Disconnect from Teams."""
        if self.session:
            await self.session.close()
            self.session = None
        
        self.authenticated = False
        self.access_token = None
        self.tenant_id = None
        self.client_id = None
        self.client_secret = None