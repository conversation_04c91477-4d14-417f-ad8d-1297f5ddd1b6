import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
from slack_sdk import Web<PERSON>lient
from slack_sdk.errors import SlackApiError
from ..base_interface import MessagingInterface, Message, GroupChat, ReceivedMessage, MessageType
import logging

logger = logging.getLogger(__name__)


class SlackService(MessagingInterface):
    """Slack messaging service implementation using slack_sdk."""
    
    def __init__(self):
        self.bot_token: Optional[str] = None
        self.user_token: Optional[str] = None
        self.client: Optional[WebClient] = None
        self.authenticated = False
    
    async def authenticate(self, credentials: Dict[str, str]) -> bool:
        """Authenticate with Slack API using slack_sdk."""
        try:
            self.bot_token = credentials.get("bot_token")
            self.user_token = credentials.get("user_token")
            
            if not self.bot_token:
                return False
            
            # Initialize WebClient
            self.client = WebClient(token=self.bot_token)
            
            # Test authentication
            response = await asyncio.to_thread(self.client.auth_test)
            self.authenticated = response.get("ok", False)
            return self.authenticated
                
        except Exception as e:
            logger.debug(f"Slack authentication error: {e}")
            return False
    
    async def send_message(self, channel_id: str, message: Message, thread_ts: Optional[str] = None) -> Dict[str, Any]:
        """Send a message to a Slack channel using slack_sdk.
        
        Args:
            channel_id: The ID of the channel to send message to
            message: The message object to send
            thread_ts: Optional timestamp of the parent message to reply in thread
        """
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            # Handle different message types
            if message.message_type == MessageType.FILE and message.metadata:
                # For file uploads, use files.upload
                return await self._upload_file(channel_id, message)
            
            # Prepare message payload
            payload = {
                "channel": channel_id,
                "text": message.content,
                "mrkdwn": True
            }
            
            # Add thread timestamp if provided
            if thread_ts:
                payload["thread_ts"] = thread_ts
                logger.debug(f"Sending message in thread {thread_ts}")
            
            # Handle embeds
            if message.message_type == MessageType.EMBED and message.metadata:
                if "blocks" in message.metadata:
                    payload["blocks"] = message.metadata["blocks"]
                if "attachments" in message.metadata:
                    payload["attachments"] = message.metadata["attachments"]
            
            # Send message
            response = await asyncio.to_thread(self.client.chat_postMessage, **payload)
            
            if response.get("ok"):
                return {
                    "message_id": response.get("ts"),
                    "status": "success",
                    "channel": response.get("channel"),
                    "thread_ts": thread_ts
                }
            else:
                logger.error(f"Slack API error when sending message to channel {channel_id}: {response.get('error', 'Unknown error')}")
                return {
                    "status": "error",
                    "error": response.get("error", "Unknown error")
                }
                
        except SlackApiError as e:
            error_msg = e.response.get('error', 'Unknown error')
            logger.error(f"❌  Slack API error when sending message to channel {channel_id}: {error_msg}")
            
            # Log additional error details if available
            if 'response_metadata' in e.response:
                logger.error(f"   Response metadata: {e.response['response_metadata']}")
            
            # Check if it's a scope issue and log current bot scopes
            if error_msg == 'missing_scope':
                await self._log_bot_scopes()
            
            return {
                "status": "error",
                "error": f"Slack API error: {error_msg}"
            }
        except Exception as e:  
            logger.error(f"❌ Error sending message to channel {channel_id}: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def send_private_message(self, user_email: str, message: Message) -> Dict[str, Any]:
        """Send a private message to a Slack user using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            try:
                #get user_id from user_email
                res = self.client.users_lookupByEmail(email=user_email)
                external_id = res.get("user", {}).get("id")
            except SlackApiError as e:
                logger.debug(f"Could not find user_id from user_email {user_email} in slack: {e.response['error']}")
                return {
                    "status": "error",
                    "error": f"Slack Could not look up user by email: {e.response['error']}"
                }
            
            # Log the attempt
            logger.warning(f"🔄 Slack: Opening DM with user_id: '{external_id}'")
            
            # Validate external_id
            if not external_id or not external_id.strip():
                logger.error(f"❌ Slack: Invalid external_id: '{external_id}'")
                return {
                    "status": "error",
                    "error": f"Invalid id: '{external_id}'"
                }
            
            # Open a DM with the user first
            # The Slack API expects 'users' parameter, not 'user'
            response = await asyncio.to_thread(self.client.conversations_open, users=[external_id])
            
            if not response.get("ok"):
                return {
                    "status": "error",
                    "error": f"Failed to open DM: {response.get('error', 'Unknown error')}"
                }
            
            # Get the DM channel ID
            dm_channel_id = response["channel"]["id"]
            
            # Send the message to the DM
            return await self.send_message(dm_channel_id, message)
                
        except SlackApiError as e:
            logger.error(f"Error sending private message to user {external_id}  in slack: {e.response['error']}")
            return {
                "status": "error",
                "error": f"Slack API error: {e.response['error']}"
            }
        except Exception as e:
            logger.exception(f"Error sending private message in slack: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _upload_file(self, channel_id: str, message: Message) -> Dict[str, Any]:
        """Upload a file to Slack using slack_sdk."""
        if not message.metadata or "file_path" not in message.metadata:
            return {"status": "error", "error": "File path not provided"}
        
        try:
            with open(message.metadata["file_path"], "rb") as file:
                response = await asyncio.to_thread(
                    self.client.files_upload,
                    channels=channel_id,
                    initial_comment=message.content,
                    file=file,
                    filename=message.metadata.get("filename", "file")
                )
                
                if response.get("ok"):
                    return {
                        "message_id": response.get("file", {}).get("id"),
                        "status": "success",
                        "file_url": response.get("file", {}).get("url_private")
                    }
                else:
                    return {
                        "status": "error",
                        "error": response.get("error", "File upload failed")
                    }
        except SlackApiError as e:
            return {
                "status": "error",
                "error": f"Slack API error: {e.response['error']}"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def receive_messages(self, channel_id: str, limit: int = 10) -> List[ReceivedMessage]:
        """Retrieve recent messages from a Slack channel using slack_sdk, including threaded messages."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            # Use the new threaded history function from services
            from integrations.slack.services import get_channel_history_with_threads
            
            # Get bot token from the authenticated client
            bot_token = self.client.token
            
            # Get company instance from the bot token (this is a simplified approach)
            # In a real implementation, you might want to pass company info directly
            response = get_channel_history_with_threads(
                channel_id=channel_id,
                limit=limit,
                bot_token=bot_token
            )
            
            if not response.get("ok"):
                logger.debug(f"Failed to retrieve channel history with threads: {response.get('error')}")
                # Fallback to original method
                response = await asyncio.to_thread(
                    self.client.conversations_history,
                    channel=channel_id,
                    limit=limit
                )
            
            messages = []
            for msg in response.get("messages", []):
                # Skip bot messages and system messages
                if msg.get("subtype") in ["bot_message", "channel_join", "channel_leave"]:
                    continue
                
                message_type = MessageType.TEXT
                if msg.get("files"):
                    message_type = MessageType.FILE
                elif msg.get("attachments"):
                    message_type = MessageType.EMBED
                
                # Get user name synchronously since slack_sdk is sync
                sender_name = self._get_user_name_sync(msg.get("user", ""))
                
                # Check if this is a threaded message
                thread_ts = msg.get("thread_ts")
                is_thread_reply = thread_ts and thread_ts != msg.get("ts")
                parent_thread_ts = thread_ts if not is_thread_reply else None
                
                # Add thread metadata
                metadata = {
                    "is_thread_reply": is_thread_reply,
                    "parent_thread_ts": parent_thread_ts,
                    "thread_ts": thread_ts,
                    "reply_count": msg.get("reply_count", 0) if not is_thread_reply else None,
                    "reply_users_count": msg.get("reply_users_count", 0) if not is_thread_reply else None,
                    "latest_reply": msg.get("latest_reply") if not is_thread_reply else None
                }
                
                messages.append(ReceivedMessage(
                    id=msg.get("ts"),
                    content=msg.get("text", ""),
                    sender_id=msg.get("user", ""),
                    sender_name=sender_name,
                    channel_id=channel_id,
                    timestamp=msg.get("ts"),
                    message_type=message_type,
                    metadata=metadata
                ))
            
            return messages
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving messages: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving messages: {e}")
            return []

    async def get_thread_replies(self, channel_id: str, parent_ts: str) -> List[ReceivedMessage]:
        """Retrieve all replies to a specific message thread."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            from integrations.slack.services import get_thread_replies
            
            # Get bot token from the authenticated client
            bot_token = self.client.token
            
            response = get_thread_replies(
                channel_id=channel_id,
                parent_ts=parent_ts,
                bot_token=bot_token
            )
            
            if not response.get("ok"):
                logger.debug(f"Failed to retrieve thread replies: {response.get('error')}")
                return []
            
            messages = []
            thread_messages = response.get("messages", [])
            
            # Skip the first message as it's the parent (already included in main history)
            for msg in thread_messages[1:]:
                # Skip bot messages and system messages
                if msg.get("subtype") in ["bot_message", "channel_join", "channel_leave"]:
                    continue
                
                message_type = MessageType.TEXT
                if msg.get("files"):
                    message_type = MessageType.FILE
                elif msg.get("attachments"):
                    message_type = MessageType.EMBED
                
                # Get user name synchronously since slack_sdk is sync
                sender_name = self._get_user_name_sync(msg.get("user", ""))
                
                # Add thread metadata
                metadata = {
                    "is_thread_reply": True,
                    "parent_thread_ts": parent_ts,
                    "thread_ts": parent_ts,
                    "parent_user_id": msg.get("parent_user_id")
                }
                
                messages.append(ReceivedMessage(
                    id=msg.get("ts"),
                    content=msg.get("text", ""),
                    sender_id=msg.get("user", ""),
                    sender_name=sender_name,
                    channel_id=channel_id,
                    timestamp=msg.get("ts"),
                    message_type=message_type,
                    metadata=metadata
                ))
            
            return messages
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving thread replies: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving thread replies: {e}")
            return []

    async def get_thread_context(self, channel_id: str, parent_ts: str) -> Dict[str, Any]:
        """Get complete thread context including parent message and all replies."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            from integrations.slack.services import get_thread_replies
            
            # Get bot token from the authenticated client
            bot_token = self.client.token
            
            response = get_thread_replies(
                channel_id=channel_id,
                parent_ts=parent_ts,
                bot_token=bot_token
            )
            
            if not response.get("ok"):
                logger.debug(f"Failed to retrieve thread context: {response.get('error')}")
                return {}
            
            thread_messages = response.get("messages", [])
            if not thread_messages:
                return {}
            
            # First message is the parent
            parent_message = thread_messages[0]
            
            # Get parent message details
            parent_sender_name = self._get_user_name_sync(parent_message.get("user", ""))
            
            thread_context = {
                "parent_message": {
                    "id": parent_message.get("ts"),
                    "content": parent_message.get("text", ""),
                    "sender_id": parent_message.get("user", ""),
                    "sender_name": parent_sender_name,
                    "timestamp": parent_message.get("ts"),
                    "reply_count": parent_message.get("reply_count", 0),
                    "reply_users_count": parent_message.get("reply_users_count", 0),
                    "latest_reply": parent_message.get("latest_reply")
                },
                "replies": [],
                "total_replies": len(thread_messages) - 1
            }
            
            # Process replies (skip first message as it's the parent)
            for msg in thread_messages[1:]:
                sender_name = self._get_user_name_sync(msg.get("user", ""))
                
                thread_context["replies"].append({
                    "id": msg.get("ts"),
                    "content": msg.get("text", ""),
                    "sender_id": msg.get("user", ""),
                    "sender_name": sender_name,
                    "timestamp": msg.get("ts"),
                    "parent_user_id": msg.get("parent_user_id")
                })
            
            return thread_context
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving thread context: {e.response['error']}")
            return {}
        except Exception as e:
            logger.debug(f"Error retrieving thread context: {e}")
            return {}
    
    def _get_user_name_sync(self, user_id: str) -> str:
        """Get username from user ID synchronously using slack_sdk."""
        if not user_id or not self.client:
            return "Unknown User"
        
        try:
            response = self.client.users_info(user=user_id)
            if response.get("ok"):
                return response.get("user", {}).get("name", "Unknown User")
            return "Unknown User"
        except SlackApiError:
            return "Unknown User"
        except Exception:
            return "Unknown User"
    
    async def _get_user_name(self, user_id: str) -> str:
        """Get username from user ID (async wrapper for compatibility)."""
        return self._get_user_name_sync(user_id)
    
    async def create_group_chat(self, name: str, members: List[str]) -> GroupChat:
        """Create a new Slack channel (group chat) using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            # Create channel
            response = await asyncio.to_thread(
                self.client.conversations_create,
                name=name.lower().replace(" ", "-"),
                is_private=True
            )
            
            if not response.get("ok"):
                raise Exception(f"Failed to create channel: {response.get('error')}")
            
            channel_id = response.get("channel", {}).get("id")
            
            # Invite members
            for member in members:
                await self._invite_member_sync(channel_id, member)
            
            return GroupChat(
                id=channel_id,
                name=name,
                members=members,
                metadata={
                    "channel_name": response.get("channel", {}).get("name"),
                    "is_private": response.get("channel", {}).get("is_private", True)
                }
            )
            
        except SlackApiError as e:
            raise Exception(f"Slack API error creating group chat: {e.response['error']}")
        except Exception as e:
            raise Exception(f"Error creating group chat: {e}")
    
    async def _invite_member_sync(self, channel_id: str, user_id: str) -> bool:
        """Invite a member to a channel using slack_sdk."""
        try:
            response = await asyncio.to_thread(
                self.client.conversations_invite,
                channel=channel_id,
                users=user_id
            )
            return response.get("ok", False)
        except SlackApiError:
            return False
        except Exception:
            return False
    
    async def _invite_member(self, channel_id: str, user_id: str) -> bool:
        """Invite a member to a channel (async wrapper for compatibility)."""
        return self._invite_member_sync(channel_id, user_id)
    
    async def get_channels(self) -> List[Dict[str, Any]]:
        """Get list of Slack channels using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = await asyncio.to_thread(
                self.client.conversations_list,
                types="public_channel,private_channel"
            )
            
            if not response.get("ok"):
                return []
            
            channels = []
            for channel in response.get("channels", []):
                channels.append({
                    "id": channel.get("id"),
                    "name": channel.get("name"),
                    "is_private": channel.get("is_private", False),
                    "member_count": channel.get("num_members", 0),
                    "topic": channel.get("topic", {}).get("value", ""),
                    "purpose": channel.get("purpose", {}).get("value", "")
                })
            
            return channels
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving channels: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving channels: {e}")
            return []
    
    async def get_channel_info(self, channel_id: str) -> Dict[str, Any]:
        """Get channel info from Slack using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = self.client.conversations_info(channel=channel_id)
            return response.get("channel", {})
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving channel info: {e.response['error']}")
            return {}
        except Exception as e:
            logger.debug(f"Error retrieving channel info: {e}")
            return {}
            
    async def get_users(self) -> List[Dict[str, Any]]:
        """Get list of Slack users using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = await asyncio.to_thread(self.client.users_list)
            
            if not response.get("ok"):
                return []
            
            users = []
            for user in response.get("members", []):
                if not user.get("deleted") and not user.get("is_bot"):
                    users.append({
                        "id": user.get("id"),
                        "name": user.get("name"),
                        "real_name": user.get("real_name", ""),
                        "email": user.get("profile", {}).get("email", ""),
                        "status": user.get("presence", "unknown"),
                        "is_admin": user.get("is_admin", False)
                    })
            
            return users
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving users: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving users: {e}")
            return []
    
    async def disconnect(self) -> None:
        """Disconnect from Slack."""
        if self.client:
            # slack_sdk doesn't require explicit cleanup, but we can close the client
            self.client = None
        
        self.authenticated = False
        self.bot_token = None
        self.user_token = None

    async def _log_bot_scopes(self) -> None:
        """Log the current bot scopes to help debug permission issues."""
        if not self.authenticated or not self.client:
            logger.warning("⚠️ Cannot check bot scopes - not authenticated")
            return
        
        try:
            # Use auth.test to get bot information including scopes
            response = await asyncio.to_thread(self.client.auth_test)
            
            if response.get("ok"):
                bot_info = {
                    "bot_id": response.get("bot_id"),
                    "user_id": response.get("user_id"),
                    "team_id": response.get("team_id"),
                    "team_name": response.get("team"),
                    "scopes": response.get("scope", "No scopes found")
                }
                
                logger.warning("🔍 Bot Information:")
                logger.warning(f"   Bot ID: {bot_info['bot_id']}")
                logger.warning(f"   User ID: {bot_info['user_id']}")
                logger.warning(f"   Team: {bot_info['team_name']} ({bot_info['team_id']})")
                logger.warning(f"   Scopes: {bot_info['scopes']}")
                
                # Check for common required scopes
                required_scopes = [
                    "chat:write", "chat:write.public", "channels:read", 
                    "groups:read", "im:read", "mpim:read", "users:read"
                ]
                
                current_scopes = bot_info['scopes'].split(',') if bot_info['scopes'] else []
                missing_scopes = [scope for scope in required_scopes if scope not in current_scopes]
                
                if missing_scopes:
                    logger.warning("⚠️ Missing required scopes:")
                    for scope in missing_scopes:
                        logger.warning(f"   - {scope}")
                else:
                    logger.warning("✅ All required scopes are present")
                    
            else:
                logger.error(f"❌ Failed to get bot info: {response.get('error', 'Unknown error')}")
                
        except SlackApiError as e:
            logger.error(f"❌ Slack API error when checking bot scopes: {e.response.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"❌ Error checking bot scopes: {e}")