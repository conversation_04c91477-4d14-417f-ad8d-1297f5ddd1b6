import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
from slack_sdk import Web<PERSON>lient
from slack_sdk.errors import SlackApiError
from ..base_interface import MessagingInterface, Message, GroupChat, ReceivedMessage, MessageType
import logging

logger = logging.getLogger(__name__)


class SlackService(MessagingInterface):
    """Slack messaging service implementation using slack_sdk."""
    
    def __init__(self):
        self.bot_token: Optional[str] = None
        self.user_token: Optional[str] = None
        self.client: Optional[WebClient] = None
        self.authenticated = False
    
    async def authenticate(self, credentials: Dict[str, str]) -> bool:
        """Authenticate with Slack API using slack_sdk."""
        try:
            self.bot_token = credentials.get("bot_token")
            self.user_token = credentials.get("user_token")
            
            if not self.bot_token:
                return False
            
            # Initialize WebClient
            self.client = WebClient(token=self.bot_token)
            
            # Test authentication
            response = await asyncio.to_thread(self.client.auth_test)
            self.authenticated = response.get("ok", False)
            return self.authenticated
                
        except Exception as e:
            logger.debug(f"Slack authentication error: {e}")
            return False
    
    async def send_message(self, channel_id: str, message: Message) -> Dict[str, Any]:
        """Send a message to a Slack channel using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            # Handle different message types
            if message.message_type == MessageType.FILE and message.metadata:
                # For file uploads, use files.upload
                return await self._upload_file(channel_id, message)
            
            # Prepare message payload
            payload = {
                "channel": channel_id,
                "text": message.content,
                "mrkdwn": True
            }
            
            # Handle embeds
            if message.message_type == MessageType.EMBED and message.metadata:
                if "blocks" in message.metadata:
                    payload["blocks"] = message.metadata["blocks"]
                if "attachments" in message.metadata:
                    payload["attachments"] = message.metadata["attachments"]
            
            # Send message
            response = await asyncio.to_thread(self.client.chat_postMessage, **payload)
            
            if response.get("ok"):
                return {
                    "message_id": response.get("ts"),
                    "status": "success",
                    "channel": response.get("channel")
                }
            else:
                return {
                    "status": "error",
                    "error": response.get("error", "Unknown error")
                }
                
        except SlackApiError as e:
            return {
                "status": "error",
                "error": f"Slack API error: {e.response['error']}"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def send_private_message(self, user_email: str, message: Message) -> Dict[str, Any]:
        """Send a private message to a Slack user using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            try:
                #get user_id from user_email
                res = self.client.users_lookupByEmail(email=user_email)
                external_id = res.get("user", {}).get("id")
            except SlackApiError as e:
                logger.debug(f"Could not find user_id from user_email {user_email} in slack: {e.response['error']}")
                return {
                    "status": "error",
                    "error": f"Slack Could not look up user by email: {e.response['error']}"
                }
            
            # Log the attempt
            logger.info(f"🔄 Slack: Opening DM with user_id: '{external_id}'")
            
            # Validate external_id
            if not external_id or not external_id.strip():
                logger.error(f"❌ Slack: Invalid external_id: '{external_id}'")
                return {
                    "status": "error",
                    "error": f"Invalid id: '{external_id}'"
                }
            
            # Open a DM with the user first
            # The Slack API expects 'users' parameter, not 'user'
            response = await asyncio.to_thread(self.client.conversations_open, users=[external_id])
            
            if not response.get("ok"):
                return {
                    "status": "error",
                    "error": f"Failed to open DM: {response.get('error', 'Unknown error')}"
                }
            
            # Get the DM channel ID
            dm_channel_id = response["channel"]["id"]
            
            # Send the message to the DM
            return await self.send_message(dm_channel_id, message)
                
        except SlackApiError as e:
            logger.error(f"Error sending private message to user {external_id}  in slack: {e.response['error']}")
            return {
                "status": "error",
                "error": f"Slack API error: {e.response['error']}"
            }
        except Exception as e:
            logger.exception(f"Error sending private message in slack: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _upload_file(self, channel_id: str, message: Message) -> Dict[str, Any]:
        """Upload a file to Slack using slack_sdk."""
        if not message.metadata or "file_path" not in message.metadata:
            return {"status": "error", "error": "File path not provided"}
        
        try:
            with open(message.metadata["file_path"], "rb") as file:
                response = await asyncio.to_thread(
                    self.client.files_upload,
                    channels=channel_id,
                    initial_comment=message.content,
                    file=file,
                    filename=message.metadata.get("filename", "file")
                )
                
                if response.get("ok"):
                    return {
                        "message_id": response.get("file", {}).get("id"),
                        "status": "success",
                        "file_url": response.get("file", {}).get("url_private")
                    }
                else:
                    return {
                        "status": "error",
                        "error": response.get("error", "File upload failed")
                    }
        except SlackApiError as e:
            return {
                "status": "error",
                "error": f"Slack API error: {e.response['error']}"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def receive_messages(self, channel_id: str, limit: int = 10) -> List[ReceivedMessage]:
        """Retrieve recent messages from a Slack channel using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = await asyncio.to_thread(
                self.client.conversations_history,
                channel=channel_id,
                limit=limit
            )
            
            messages = []
            for msg in response.get("messages", []):
                # Skip bot messages and system messages
                if msg.get("subtype") in ["bot_message", "channel_join", "channel_leave"]:
                    continue
                
                message_type = MessageType.TEXT
                if msg.get("files"):
                    message_type = MessageType.FILE
                elif msg.get("attachments"):
                    message_type = MessageType.EMBED
                
                # Get user name synchronously since slack_sdk is sync
                sender_name = self._get_user_name_sync(msg.get("user", ""))
                
                messages.append(ReceivedMessage(
                    id=msg.get("ts"),
                    content=msg.get("text", ""),
                    sender_id=msg.get("user", ""),
                    sender_name=sender_name,
                    channel_id=channel_id,
                    timestamp=msg.get("ts"),
                    message_type=message_type,
                ))
            
            return messages
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving messages: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving messages: {e}")
            return []
    
    def _get_user_name_sync(self, user_id: str) -> str:
        """Get username from user ID synchronously using slack_sdk."""
        if not user_id or not self.client:
            return "Unknown User"
        
        try:
            response = self.client.users_info(user=user_id)
            if response.get("ok"):
                return response.get("user", {}).get("name", "Unknown User")
            return "Unknown User"
        except SlackApiError:
            return "Unknown User"
        except Exception:
            return "Unknown User"
    
    async def _get_user_name(self, user_id: str) -> str:
        """Get username from user ID (async wrapper for compatibility)."""
        return self._get_user_name_sync(user_id)
    
    async def create_group_chat(self, name: str, members: List[str]) -> GroupChat:
        """Create a new Slack channel (group chat) using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            # Create channel
            response = await asyncio.to_thread(
                self.client.conversations_create,
                name=name.lower().replace(" ", "-"),
                is_private=True
            )
            
            if not response.get("ok"):
                raise Exception(f"Failed to create channel: {response.get('error')}")
            
            channel_id = response.get("channel", {}).get("id")
            
            # Invite members
            for member in members:
                await self._invite_member_sync(channel_id, member)
            
            return GroupChat(
                id=channel_id,
                name=name,
                members=members,
                metadata={
                    "channel_name": response.get("channel", {}).get("name"),
                    "is_private": response.get("channel", {}).get("is_private", True)
                }
            )
            
        except SlackApiError as e:
            raise Exception(f"Slack API error creating group chat: {e.response['error']}")
        except Exception as e:
            raise Exception(f"Error creating group chat: {e}")
    
    async def _invite_member_sync(self, channel_id: str, user_id: str) -> bool:
        """Invite a member to a channel using slack_sdk."""
        try:
            response = await asyncio.to_thread(
                self.client.conversations_invite,
                channel=channel_id,
                users=user_id
            )
            return response.get("ok", False)
        except SlackApiError:
            return False
        except Exception:
            return False
    
    async def _invite_member(self, channel_id: str, user_id: str) -> bool:
        """Invite a member to a channel (async wrapper for compatibility)."""
        return self._invite_member_sync(channel_id, user_id)
    
    async def get_channels(self) -> List[Dict[str, Any]]:
        """Get list of Slack channels using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = await asyncio.to_thread(
                self.client.conversations_list,
                types="public_channel,private_channel"
            )
            
            if not response.get("ok"):
                return []
            
            channels = []
            for channel in response.get("channels", []):
                channels.append({
                    "id": channel.get("id"),
                    "name": channel.get("name"),
                    "is_private": channel.get("is_private", False),
                    "member_count": channel.get("num_members", 0),
                    "topic": channel.get("topic", {}).get("value", ""),
                    "purpose": channel.get("purpose", {}).get("value", "")
                })
            
            return channels
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving channels: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving channels: {e}")
            return []
    
    async def get_channel_info(self, channel_id: str) -> Dict[str, Any]:
        """Get channel info from Slack using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = self.client.conversations_info(channel=channel_id)
            return response.get("channel", {})
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving channel info: {e.response['error']}")
            return {}
        except Exception as e:
            logger.debug(f"Error retrieving channel info: {e}")
            return {}
            
    async def get_users(self) -> List[Dict[str, Any]]:
        """Get list of Slack users using slack_sdk."""
        if not self.authenticated or not self.client:
            raise Exception("Not authenticated with Slack")
        
        try:
            response = await asyncio.to_thread(self.client.users_list)
            
            if not response.get("ok"):
                return []
            
            users = []
            for user in response.get("members", []):
                if not user.get("deleted") and not user.get("is_bot"):
                    users.append({
                        "id": user.get("id"),
                        "name": user.get("name"),
                        "real_name": user.get("real_name", ""),
                        "email": user.get("profile", {}).get("email", ""),
                        "status": user.get("presence", "unknown"),
                        "is_admin": user.get("is_admin", False)
                    })
            
            return users
            
        except SlackApiError as e:
            logger.debug(f"Slack API error retrieving users: {e.response['error']}")
            return []
        except Exception as e:
            logger.debug(f"Error retrieving users: {e}")
            return []
    
    async def disconnect(self) -> None:
        """Disconnect from Slack."""
        if self.client:
            # slack_sdk doesn't require explicit cleanup, but we can close the client
            self.client = None
        
        self.authenticated = False
        self.bot_token = None
        self.user_token = None