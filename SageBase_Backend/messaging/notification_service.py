import asyncio
from typing import Dict, List, Optional, Any
from .base_interface import Message, MessageType
from .slack.slack_service import SlackService
from .discord.discord_service import DiscordService
from .MsTeams.teams_service import TeamsService
import logging
from django.utils import timezone
from datetime import timed<PERSON>ta
from integrations.models import Company,NotificationSettings
logger = logging.getLogger(__name__)


class NotificationService:
    """Service for sending notifications across multiple messaging platforms."""
    
    def __init__(self):
        self.slack_service = SlackService()
        self.discord_service = DiscordService()
        self.teams_service = TeamsService()
        self.authenticated_platforms = set()
    
    
    def _update_last_notification_time(self, company: Company):
        """Update the last notification time for the company."""
        try:
            settings,created = NotificationSettings.objects.get_or_create(company=company)
            if created:
                settings.enabled = True
                settings.save(update_fields=['enabled'])
            if settings:
                settings.last_notification_time = timezone.now()
                settings.save(update_fields=['last_notification_time'])
        except Exception as e:
            logger.error(f"❌ Error updating last notification time for {company.name}: {e}")
    
    async def authenticate_all_platforms(self, credentials: Dict[str, Dict[str, str]]) -> bool:
        """
        Authenticate with all available messaging platforms.
        
        Args:
            credentials: Dict with platform names as keys and their credentials as values
                Example: {
                    "slack": {"bot_token": "xoxb-..."},
                    "discord": {"bot_token": "..."},
                    "teams": {"tenant_id": "...", "client_id": "...", "client_secret": "..."}
                }
        
        Returns:
            True if at least one platform authenticated successfully
        """
        auth_results = {}
        
        # Authenticate Slack
        if "slack" in credentials:
            try:
                auth_result = await self.slack_service.authenticate(credentials["slack"])
                auth_results["slack"] = auth_result
                if auth_result:
                    self.authenticated_platforms.add("slack")
                    logger.info("✅ Slack authenticated successfully")
                else:
                    logger.warning("❌ Slack authentication failed")
            except Exception as e:
                logger.error(f"❌ Slack authentication error: {e}")
                auth_results["slack"] = False
        
        # Authenticate Discord
        if "discord" in credentials:
            try:
                auth_result = await self.discord_service.authenticate(credentials["discord"])
                auth_results["discord"] = auth_result
                if auth_result:
                    self.authenticated_platforms.add("discord")
                    logger.info("✅ Discord authenticated successfully")
                else:
                    logger.warning("❌ Discord authentication failed")
            except Exception as e:
                logger.error(f"❌ Discord authentication error: {e}")
                auth_results["discord"] = False
        
        # Authenticate Teams
        if "teams" in credentials:
            try:
                auth_result = await self.teams_service.authenticate(credentials["teams"])
                auth_results["teams"] = auth_result
                if auth_result:
                    self.authenticated_platforms.add("teams")
                    logger.info("✅ Teams authenticated successfully")
                else:
                    logger.warning("❌ Teams authentication failed")
            except Exception as e:
                logger.error(f"❌ Teams authentication error: {e}")
                auth_results["teams"] = False
        
        return len(self.authenticated_platforms) > 0
    
    async def send_private_message_to_user(
        self, 
        user_email: str, 
        message_content: str, 
        platforms: Optional[List[str]] = None,
        message_type: MessageType = MessageType.TEXT,
        metadata: Optional[Dict[str, Any]] = None,
        company: Company = None,
        notification_type: str = "general"
    ) -> Dict[str, Any]:
        """
        Send a private message to a user across all or specified platforms.
        
        Args:
            user_email: The user ID to send the message to
            message_content: The message content
            platforms: List of platforms to send to (e.g., ["slack", "discord"]). 
                      If None, sends to all authenticated platforms
            message_type: Type of message (TEXT, FILE, etc.)
            metadata: Additional message metadata
            company: Company instance for frequency checking
            notification_type: Type of notification for frequency control
        
        Returns:
            Dict with results from each platform
        """
        if not self.authenticated_platforms:
            return {"status": "error", "error": "No platforms authenticated"}
        
        # Determine which platforms to use
        target_platforms = platforms or list(self.authenticated_platforms)
        available_platforms = [p for p in target_platforms if p in self.authenticated_platforms]
        
        if not available_platforms:
            return {"status": "error", "error": "No requested platforms are authenticated"}
        
        # Create message object
        message = Message(
            content=message_content,
            message_type=message_type,
            metadata=metadata or {}
        )
        
        results = {}
        
        # Send to each platform
        for platform in available_platforms:
            try:
                if platform == "slack":
                    result = await self.slack_service.send_private_message(user_email, message)
                elif platform == "discord":
                    result = await self.discord_service.send_private_message(user_email, message)
                elif platform == "teams":
                    result = await self.teams_service.send_private_message(user_email, message)
                else:
                    result = {"status": "error", "error": f"Unknown platform: {platform}"}
                
                results[platform] = result
                
                if result.get("status") == "success":
                    logger.info(f"✅ Private message sent via {platform}")
                else:
                    logger.warning(f"❌ Failed to send private message via {platform}: {result.get('error')}")
                    
            except Exception as e:
                logger.error(f"❌ Error sending private message via {platform}: {e}")
                continue
        
        # Determine overall success
        successful_platforms = [p for p, r in results.items() if r.get("status") == "success"]
        failed_platforms = [p for p, r in results.items() if r.get("status") != "success"]
        
        overall_status = "success" if successful_platforms else "error"
        
        # Update last notification time if company is provided and notification was successful
        if company and overall_status == "success":
            await asyncio.to_thread(self._update_last_notification_time, company)
        
        return {
            "status": overall_status,
            "successful_platforms": successful_platforms,
            "failed_platforms": failed_platforms,
            "results": results
        }
    
    async def send_private_message_to_multiple_users(
        self, 
        users_emails: List[str], 
        message_content: str, 
        platforms: Optional[List[str]] = None,
        message_type: MessageType = MessageType.TEXT,
        metadata: Optional[Dict[str, Any]] = None,
        company: Company = None,
        notification_type: str = "general"
    ) -> Dict[str, Any]:
        """
        Send a private message to multiple users across platforms.
        
        Args:
            users_emails: List of user IDs to send the message to
            message_content: The message content
            platforms: List of platforms to send to
            message_type: Type of message
            metadata: Additional message metadata
            company: Company instance for frequency checking
            notification_type: Type of notification for frequency control
        
        Returns:
            Dict with results for each user
        """
        
        results = {}
        successful_count = 0
        
        for user_email in users_emails:
            result = await self.send_private_message_to_user(
                user_email, message_content, platforms, message_type, metadata, company, notification_type
            )
            results[user_email] = result
            if result.get("status") == "success":
                successful_count += 1
        
        # Update last notification time if company is provided and notifications were successful
        if company and successful_count > 0:
            await asyncio.to_thread(self._update_last_notification_time, company)
        
        return {
            "status": "completed",
            "total_users": len(users_emails),
            "successful_users": successful_count,
            "results": results
        }
    
    async def disconnect_all(self) -> None:
        """Disconnect from all messaging platforms."""
        try:
            await self.slack_service.disconnect()
            logger.info("✅ Disconnected from Slack")
        except Exception as e:
            logger.error(f"❌ Error disconnecting from Slack: {e}")
        
        try:
            await self.discord_service.disconnect()
            logger.info("✅ Disconnected from Discord")
        except Exception as e:
            logger.error(f"❌ Error disconnecting from Discord: {e}")
        
        try:
            await self.teams_service.disconnect()
            logger.info("✅ Disconnected from Teams")
        except Exception as e:
            logger.error(f"❌ Error disconnecting from Teams: {e}")
        
        self.authenticated_platforms.clear()
        logger.info("✅ Disconnected from all platforms")


# Example usage:
async def example_usage():
    """Example of how to use the NotificationService."""
    
    # Initialize the service
    notification_service = NotificationService()
    
    # Authenticate with all platforms
    credentials = {
        "slack": {"bot_token": "xoxb-your-slack-token"},
        #"discord": {"bot_token": "your-discord-token"},
        #"teams": {
        #    "tenant_id": "your-tenant-id",
        #    "client_id": "your-client-id", 
        #    "client_secret": "your-client-secret"
        #}
    }
    
    authenticated = await notification_service.authenticate_all_platforms(credentials)
    
    if authenticated:
        # Send a private message to a user
        result = await notification_service.send_private_message_to_user(
            user_email="admin@scsdc;com",  # Slack user ID
            message_content="Hello! This is a private message from SageBase.",
            platforms=["slack", "discord"]  # Send to Slack and Discord only
        )
        
        print(f"Message sent: {result}")
        
        # Send to multiple users
        multi_result = await notification_service.send_private_message_to_multiple_users(
            users_emails=["U1234567890", "U0987654321"],
            message_content="Hello everyone! This is a group private message.",
            platforms=["slack"]
        )
        
        print(f"Multi-user message sent: {multi_result}")
        
        # Disconnect
        await notification_service.disconnect_all()
    else:
        print("Failed to authenticate with any platforms")


if __name__ == "__main__":
    asyncio.run(example_usage()) 