from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class MessageType(Enum):
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    EMBED = "embed"


@dataclass
class Message:
    content: str
    message_type: MessageType = MessageType.TEXT
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class GroupChat:
    id: str
    name: str
    members: List[str]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ReceivedMessage:
    id: Optional[str]
    content: str
    sender_id: Optional[str]
    sender_name: str
    channel_id: Optional[str]
    timestamp: Optional[str]
    message_type: MessageType = MessageType.TEXT
    metadata: Optional[Dict[str, Any]] = None


class MessagingInterface(ABC):
    """Abstract base class for messaging platform integrations."""
    
    @abstractmethod
    async def send_message(self, channel_id: str, message: Message) -> Dict[str, Any]:
        """
        Send a message to a specific channel.
        
        Args:
            channel_id: The ID of the channel to send message to
            message: The message object to send
            
        Returns:
            Dict containing message ID and status
        """
        pass
    
    @abstractmethod
    async def send_private_message(self, user_email: str, message: Message) -> Dict[str, Any]:
        """
        Send a private message to a specific user.
        
        Args:
            user_email: The email of the user to send message to
            message: The message object to send
            
        Returns:
            Dict containing message ID and status
        """
        pass
    
    @abstractmethod
    async def receive_messages(self, channel_id: str, limit: int = 10) -> List[ReceivedMessage]:
        """
        Retrieve recent messages from a channel.
        
        Args:
            channel_id: The ID of the channel to retrieve messages from
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of received messages
        """
        pass
    
    @abstractmethod
    async def create_group_chat(self, name: str, members: List[str]) -> GroupChat:
        """
        Create a new group chat.
        
        Args:
            name: Name of the group chat
            members: List of member IDs to add to the group
            
        Returns:
            GroupChat object with details of created group
        """
        pass
    
    @abstractmethod
    async def get_channels(self) -> List[Dict[str, Any]]:
        """
        Get list of available channels.
        
        Returns:
            List of channel dictionaries with id, name, and metadata
        """
        pass
    
    @abstractmethod
    async def get_users(self) -> List[Dict[str, Any]]:
        """
        Get list of available users.
        
        Returns:
            List of user dictionaries with id, name, and metadata
        """
        pass
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, str]) -> bool:
        """
        Authenticate with the messaging platform.
        
        Args:
            credentials: Authentication credentials (tokens, keys, etc.)
            
        Returns:
            True if authentication successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """
        Disconnect from the messaging platform.
        """
        pass