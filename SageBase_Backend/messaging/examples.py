"""
Example usage of the messaging APIs from different parts of the SageBase project.
This file demonstrates how to integrate messaging services with other components.
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import messaging services
from messaging.slack.slack_service import SlackService
from messaging.MsTeams.teams_service import TeamsService
from messaging.discord.discord_service import DiscordService
from messaging.base_interface import Message, MessageType, GroupChat

# Example: Integration with GitHub webhook notifications
class GitHubWebhookNotifier:
    """Example of using messaging APIs to send GitHub webhook notifications."""
    
    def __init__(self, messaging_service):
        self.messaging_service = messaging_service
    
    async def notify_push_event(self, channel_id: str, push_data: Dict[str, Any]):
        """Send push event notification to a messaging channel."""
        repo_name = push_data.get("repository", {}).get("name", "Unknown")
        pusher = push_data.get("pusher", {}).get("name", "Unknown")
        commits = push_data.get("commits", [])
        
        message_content = f"🔄 **Push to {repo_name}** by {pusher}\n"
        message_content += f"📊 {len(commits)} commit(s) pushed\n\n"
        
        for commit in commits[:3]:  # Show first 3 commits
            message_content += f"• {commit.get('message', '')[:50]}...\n"
            message_content += f"  by {commit.get('author', {}).get('name', 'Unknown')}\n"
        
        if len(commits) > 3:
            message_content += f"... and {len(commits) - 3} more commits"
        
        message = Message(
            content=message_content,
            message_type=MessageType.TEXT
        )
        
        return await self.messaging_service.send_message(channel_id, message)


# Example: Integration with Vector DB search results
class VectorDBSearchNotifier:
    """Example of using messaging APIs to share search results from Vector DB."""
    
    def __init__(self, messaging_service):
        self.messaging_service = messaging_service
    
    async def share_search_results(self, channel_id: str, query: str, results: List[Dict]):
        """Share vector database search results in a messaging channel."""
        message_content = f"🔍 **Search Results for:** {query}\n\n"
        
        if not results:
            message_content += "No results found."
        else:
            for i, result in enumerate(results[:5], 1):  # Show top 5 results
                title = result.get("title", "Untitled")
                source = result.get("source", "Unknown")
                score = result.get("score", 0)
                
                message_content += f"{i}. **{title}**\n"
                message_content += f"   Source: {source} | Score: {score:.2f}\n"
                message_content += f"   {result.get('content', '')[:100]}...\n\n"
        
        message = Message(
            content=message_content,
            message_type=MessageType.TEXT
        )
        
        return await self.messaging_service.send_message(channel_id, message)


# Example: Integration with LLM Question Answering
class LLMAnswerNotifier:
    """Example of using messaging APIs to share LLM answers."""
    
    def __init__(self, messaging_service):
        self.messaging_service = messaging_service
    
    async def share_llm_answer(self, channel_id: str, question: str, answer: str, 
                              context_sources: List[str] = None):
        """Share LLM-generated answer in a messaging channel."""
        message_content = f"🤖 **AI Answer**\n\n"
        message_content += f"**Question:** {question}\n\n"
        message_content += f"**Answer:** {answer}\n\n"
        
        if context_sources:
            message_content += f"**Sources:**\n"
            for source in context_sources[:3]:
                message_content += f"• {source}\n"
        
        message = Message(
            content=message_content,
            message_type=MessageType.TEXT
        )
        
        return await self.messaging_service.send_message(channel_id, message)


# Example: Multi-platform message broadcaster
class MultiPlatformBroadcaster:
    """Example of broadcasting messages across multiple platforms."""
    
    def __init__(self):
        self.services = {}
    
    async def add_service(self, platform: str, service, credentials: Dict[str, str]):
        """Add a messaging service to the broadcaster."""
        await service.authenticate(credentials)
        self.services[platform] = service
    
    async def broadcast_message(self, channel_mapping: Dict[str, str], message: Message):
        """Broadcast a message to multiple platforms."""
        results = {}
        
        for platform, channel_id in channel_mapping.items():
            if platform in self.services:
                try:
                    result = await self.services[platform].send_message(channel_id, message)
                    results[platform] = result
                except Exception as e:
                    results[platform] = {"status": "error", "error": str(e)}
        
        return results


# Example: Automated notification system
class AutomatedNotificationSystem:
    """Example of an automated notification system using messaging APIs."""
    
    def __init__(self, messaging_service):
        self.messaging_service = messaging_service
        self.notification_channels = {}
    
    def configure_channel(self, event_type: str, channel_id: str):
        """Configure which channel receives which type of notifications."""
        self.notification_channels[event_type] = channel_id
    
    async def send_system_alert(self, alert_type: str, message: str, severity: str = "info"):
        """Send system alert notification."""
        if alert_type not in self.notification_channels:
            return {"status": "error", "error": "No channel configured for this alert type"}
        
        emoji_map = {
            "error": "🚨",
            "warning": "⚠️",
            "info": "ℹ️",
            "success": "✅"
        }
        
        emoji = emoji_map.get(severity, "📢")
        formatted_message = f"{emoji} **{alert_type.upper()}**\n{message}\n\n🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        notification = Message(
            content=formatted_message,
            message_type=MessageType.TEXT
        )
        
        channel_id = self.notification_channels[alert_type]
        return await self.messaging_service.send_message(channel_id, notification)


# Example usage functions
async def example_slack_usage():
    """Example of using Slack service."""
    slack_service = SlackService()
    
    # Authenticate
    credentials = {
        "bot_token": "xoxb-your-bot-token-here",
        "user_token": "xoxp-your-user-token-here"  # Optional
    }
    
    if await slack_service.authenticate(credentials):
        print("✅ Slack authentication successful")
        
        # Send a simple message
        message = Message(
            content="Hello from SageBase! 👋",
            message_type=MessageType.TEXT
        )
        
        result = await slack_service.send_message("C1234567890", message)
        print(f"Message sent: {result}")
        
        # Get recent messages
        messages = await slack_service.receive_messages("C1234567890", limit=5)
        print(f"Retrieved {len(messages)} messages")
        
        # Create a group chat
        group_chat = await slack_service.create_group_chat(
            "SageBase Team Discussion", 
            ["U1234567890", "U0987654321"]
        )
        print(f"Group chat created: {group_chat.id}")
        
        # Get channels
        channels = await slack_service.get_channels()
        print(f"Found {len(channels)} channels")
        
        await slack_service.disconnect()
    else:
        print("❌ Slack authentication failed")


async def example_teams_usage():
    """Example of using Teams service."""
    teams_service = TeamsService()
    
    # Authenticate
    credentials = {
        "tenant_id": "your-tenant-id",
        "client_id": "your-client-id",
        "client_secret": "your-client-secret"
    }
    
    if await teams_service.authenticate(credentials):
        print("✅ Teams authentication successful")
        
        # Send a message (format: team_id/channel_id)
        message = Message(
            content="Hello from SageBase! 👋",
            message_type=MessageType.TEXT
        )
        
        result = await teams_service.send_message("team_id/channel_id", message)
        print(f"Message sent: {result}")
        
        # Get channels
        channels = await teams_service.get_channels()
        print(f"Found {len(channels)} channels")
        
        await teams_service.disconnect()
    else:
        print("❌ Teams authentication failed")


async def example_discord_usage():
    """Example of using Discord service."""
    discord_service = DiscordService()
    
    # Authenticate
    credentials = {
        "bot_token": "your-bot-token-here"
    }
    
    if await discord_service.authenticate(credentials):
        print("✅ Discord authentication successful")
        
        # Send a message
        message = Message(
            content="Hello from SageBase! 👋",
            message_type=MessageType.TEXT
        )
        
        result = await discord_service.send_message("1234567890123456789", message)
        print(f"Message sent: {result}")
        
        # Get channels
        channels = await discord_service.get_channels()
        print(f"Found {len(channels)} channels")
        
        await discord_service.disconnect()
    else:
        print("❌ Discord authentication failed")


async def example_integration_with_github():
    """Example of integrating messaging with GitHub webhooks."""
    slack_service = SlackService()
    
    # Authenticate
    credentials = {"bot_token": "xoxb-your-bot-token-here"}
    
    if await slack_service.authenticate(credentials):
        # Set up GitHub webhook notifier
        notifier = GitHubWebhookNotifier(slack_service)
        
        # Example webhook data
        webhook_data = {
            "repository": {"name": "SageBase_Backend"},
            "pusher": {"name": "developer"},
            "commits": [
                {
                    "message": "Add new messaging API",
                    "author": {"name": "John Doe"}
                },
                {
                    "message": "Fix authentication bug",
                    "author": {"name": "Jane Smith"}
                }
            ]
        }
        
        # Send notification
        result = await notifier.notify_push_event("C1234567890", webhook_data)
        print(f"GitHub notification sent: {result}")
        
        await slack_service.disconnect()


async def example_multi_platform_broadcast():
    """Example of broadcasting to multiple platforms."""
    broadcaster = MultiPlatformBroadcaster()
    
    # Add services
    slack_service = SlackService()
    teams_service = TeamsService()
    
    await broadcaster.add_service("slack", slack_service, {
        "bot_token": "xoxb-your-slack-token"
    })
    
    await broadcaster.add_service("teams", teams_service, {
        "tenant_id": "your-tenant-id",
        "client_id": "your-client-id",
        "client_secret": "your-client-secret"
    })
    
    # Broadcast message
    message = Message(
        content="🚀 SageBase deployment completed successfully!",
        message_type=MessageType.TEXT
    )
    
    channel_mapping = {
        "slack": "C1234567890",
        "teams": "team_id/channel_id"
    }
    
    results = await broadcaster.broadcast_message(channel_mapping, message)
    print(f"Broadcast results: {results}")


# Django integration example
class DjangoMessagingService:
    """Example of integrating messaging services with Django models."""
    
    def __init__(self):
        self.services = {}
    
    async def setup_from_settings(self):
        """Set up messaging services from Django settings."""
        from django.conf import settings
        
        # Slack setup
        # Note: In real usage, get bot_token from company's Slack integration
        # Example: from integrations.slack.services import get_company_slack_bot_token
        #          bot_token = get_company_slack_bot_token(company)
        if hasattr(settings, 'SLACK_BOT_TOKEN'):
            slack_service = SlackService()
            await slack_service.authenticate({
                "bot_token": settings.SLACK_BOT_TOKEN  # In production: use company-specific bot_token
            })
            self.services['slack'] = slack_service
        
        # Teams setup
        if all(hasattr(settings, attr) for attr in ['TEAMS_TENANT_ID', 'TEAMS_CLIENT_ID', 'TEAMS_CLIENT_SECRET']):
            teams_service = TeamsService()
            await teams_service.authenticate({
                "tenant_id": settings.TEAMS_TENANT_ID,
                "client_id": settings.TEAMS_CLIENT_ID,
                "client_secret": settings.TEAMS_CLIENT_SECRET
            })
            self.services['teams'] = teams_service
    
    async def send_notification(self, platform: str, channel_id: str, message: str):
        """Send notification through specified platform."""
        if platform not in self.services:
            return {"status": "error", "error": f"Platform {platform} not configured"}
        
        notification = Message(content=message, message_type=MessageType.TEXT)
        return await self.services[platform].send_message(channel_id, notification)


if __name__ == "__main__":
    # Run examples
    print("🚀 Starting messaging API examples...")
    
    # Uncomment the examples you want to run
    # asyncio.run(example_slack_usage())
    # asyncio.run(example_teams_usage())
    # asyncio.run(example_discord_usage())
    # asyncio.run(example_integration_with_github())
    # asyncio.run(example_multi_platform_broadcast())
    
    print("✅ Examples completed!")