#!/usr/bin/env python3
"""
Clean Discord bot implementation with Gateway event forwarding.
Focuses on core functionality: message audit trail and real-time responses.
"""

import asyncio
import os
import sys
import logging
import json
import hmac
import hashlib
import io
from datetime import datetime, UTC

# Minimal logging configuration
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('discord_bot.log', encoding='utf-8')
    ],
    force=True
)

# Suppress noisy loggers
for name in [
    'discord', 'PIL', 'PIL.Image', 'PIL.ImageFile', 'urllib3', 'aiohttp',
    'google.protobuf', 'protobuf', 'h5py', 'h5py._conv', 'tensorflow',
    'tensorflow.python', 'asyncio'
]:
    logging.getLogger(name).setLevel(logging.ERROR)

import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="google.protobuf")
warnings.filterwarnings("ignore", category=UserWarning, module="h5py")
warnings.filterwarnings("ignore", category=FutureWarning, module="tensorflow")
warnings.filterwarnings("ignore", category=UserWarning, module="tensorflow")
warnings.filterwarnings("ignore", category=RuntimeWarning)

os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['CUDA_VISIBLE_DEVICES'] = ''
os.environ['PYTHONWARNINGS'] = 'ignore'

# Fix protobuf compatibility issues
try:
    import google.protobuf
    import warnings
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="google.protobuf")
    try:
        import google.protobuf.message
        if hasattr(google.protobuf.message, 'MessageFactory'):
            if not hasattr(google.protobuf.message.MessageFactory, 'GetPrototype'):
                def dummy_get_prototype(self, *args, **kwargs):
                    return None
                google.protobuf.message.MessageFactory.GetPrototype = dummy_get_prototype
    except ImportError:
        pass
except ImportError:
    pass

try:
    import discord
    from discord.ext import commands
    import aiohttp
except ImportError as e:
    import logging
    logging.basicConfig(level=logging.WARNING)
    logger = logging.getLogger(__name__)
    logger.error(f"Missing dependency: {e}")
    os.system("pip install discord.py aiohttp")
    import discord
    from discord.ext import commands
    import aiohttp

if sys.platform == 'win32':
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

logger = logging.getLogger(__name__)


class GatewayForwarder:
    """Forwards Discord Gateway events to Django backend."""

    def __init__(self, webhook_url: str, webhook_secret: str):
        self.webhook_url = webhook_url
        self.webhook_secret = webhook_secret
        self.session = None
        self.events_sent = 0

    async def send_event(self, event_type: str, event_data: dict) -> bool:
        try:
            gateway_event = {
                "t": event_type,
                "d": event_data,
                "op": 0,
                "s": None,
                "timestamp": datetime.now(UTC).isoformat() + "Z"
            }
            body = json.dumps(gateway_event).encode('utf-8')
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "SageBase-Discord-Bot/1.0",
                "X-Discord-Event": event_type
            }
            if self.webhook_secret:
                signature = hmac.new(
                    self.webhook_secret.encode(),
                    body,
                    hashlib.sha256
                ).hexdigest()
                headers["X-Signature"] = signature
            if not self.session:
                self.session = aiohttp.ClientSession()
            async with self.session.post(
                self.webhook_url,
                data=body,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    self.events_sent += 1
                    logger.debug(f"Gateway event sent: {event_type} ({self.events_sent})")
                    return True
                else:
                    logger.warning(f"Gateway event failed: {response.status}")
                    return False
        except Exception as e:
            logger.warning(f"Error sending Gateway event: {e}")
            return False

    async def close(self):
        if self.session:
            await self.session.close()


class DiscordBot(commands.Bot):
    """Clean Discord bot with Gateway event forwarding."""

    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.messages = True
        intents.guilds = True

        super().__init__(
            command_prefix='!',
            intents=intents,
            help_command=None
        )

        self.messages_received = 0
        self.messages_sent = 0
        self.start_time = datetime.now(UTC)
        self.forwarder = None
        self.triggers = ["hello", "hi", "bot", "assistant", "support"]
        self.processed_messages = set()
        self.max_message_cache = 1000

    async def setup_hook(self):
        webhook_url = os.getenv('DISCORD_WEBHOOK_URL')
        webhook_secret = os.getenv('DISCORD_WEBHOOK_SECRET', 'default_secret_key')
        self.forwarder = GatewayForwarder(webhook_url, webhook_secret)

    async def on_ready(self):
        logger.info(f"Bot connected as {self.user} ({len(self.guilds)} servers)")
        if self.forwarder:
            await self.forwarder.send_event("BOT_STATUS", {
                "event": "ready",
                "bot_id": str(self.user.id),
                "bot_name": self.user.name,
                "guild_count": len(self.guilds),
                "start_time": self.start_time.isoformat() + "Z",
                "process_id": os.getpid()
            })

    async def on_message(self, message):
        if message.author == self.user or message.author.bot:
            return

        message_id = str(message.id)
        if message_id in self.processed_messages:
            logger.debug(f"Duplicate message blocked: {message_id}")
            return

        self.processed_messages.add(message_id)
        if len(self.processed_messages) > self.max_message_cache:
            old_messages = list(self.processed_messages)[:self.max_message_cache // 2]
            self.processed_messages.difference_update(old_messages)

        self.messages_received += 1

        message_data = {
            "id": message_id,
            "channel_id": str(message.channel.id),
            "guild_id": str(message.guild.id) if message.guild else None,
            "author": {
                "id": str(message.author.id),
                "username": message.author.name,
                "discriminator": message.author.discriminator,
                "avatar": message.author.avatar.key if message.author.avatar else None,
                "bot": message.author.bot
            },
            "content": message.content,
            "timestamp": message.created_at.isoformat(),
            "attachments": [
                {
                    "id": str(att.id),
                    "filename": att.filename,
                    "size": att.size,
                    "url": att.url,
                    "content_type": att.content_type
                }
                for att in message.attachments
            ],
            "embeds": [embed.to_dict() for embed in message.embeds],
            "mentions": [
                {
                    "id": str(user.id),
                    "username": user.name,
                    "bot": user.bot
                }
                for user in message.mentions
            ],
            "type": message.type.value,
            "pinned": message.pinned
        }

        if self.forwarder:
            await self.forwarder.send_event("MESSAGE_CREATE", message_data)

        logger.debug(f"New message from {message.author.name}: {message.content[:50]} (ID: {message_id[:8]})")

        if any(trigger.lower() in message.content.lower() for trigger in self.triggers):
            reply_key = f"reply_{message_id}"
            if reply_key not in self.processed_messages:
                self.processed_messages.add(reply_key)
                await self.send_reply(message)

    async def send_reply(self, message):
        # No hardcoded bot responses; do nothing or implement custom logic elsewhere
        pass

    async def close(self):
        if self.forwarder:
            await self.forwarder.send_event("BOT_STATUS", {
                "event": "disconnect",
                "bot_id": str(self.user.id),
                "uptime_seconds": (datetime.now(UTC) - self.start_time).total_seconds(),
                "messages_processed": self.messages_received
            })
            await self.forwarder.close()
        await super().close()


def check_for_multiple_instances():
    import subprocess
    import sys

    try:
        current_pid = os.getpid()
        if sys.platform == 'win32':
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
                                   capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                python_processes = [line for line in lines if 'python.exe' in line.lower()]
                if len(python_processes) > 2:
                    logger.warning("Multiple Python processes detected!")
        else:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                discord_processes = [line for line in lines if 'discord_bot' in line and str(current_pid) not in line]
                if discord_processes:
                    logger.warning("Other Discord bot processes detected!")
                    return True
        return False
    except Exception as e:
        logger.warning(f"Could not check for multiple instances: {e}")
        return False


def main():
    try:
        import google.protobuf.message
        if hasattr(google.protobuf.message, 'MessageFactory'):
            original_get_prototype = getattr(google.protobuf.message.MessageFactory, 'GetPrototype', None)
            if original_get_prototype is None:
                def dummy_get_prototype(self, *args, **kwargs):
                    return None
                google.protobuf.message.MessageFactory.GetPrototype = dummy_get_prototype
    except ImportError:
        pass

    logging.getLogger('tensorflow').setLevel(logging.ERROR)
    logging.getLogger('h5py').setLevel(logging.ERROR)
    logging.getLogger('google.protobuf').setLevel(logging.ERROR)
    logging.getLogger().setLevel(logging.WARNING)

    logger.info("Starting Discord Bot...")

    current_pid = os.getpid()
    logger.info(f"Process ID: {current_pid}")

    if check_for_multiple_instances():
        logger.error("Multiple bot instances detected! This may cause duplicate message processing.")
        logger.warning("Continue anyway? (y/N): ")
        if input().lower() != 'y':
            logger.info("Exiting...")
            return

    bot_token = os.getenv('DISCORD_BOT_TOKEN')
    if not bot_token:
        bot_token = "MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls"

    if not bot_token:
        logger.error("DISCORD_BOT_TOKEN not set")
        return

    webhook_url = os.getenv('DISCORD_WEBHOOK_URL')
    webhook_secret = os.getenv('DISCORD_WEBHOOK_SECRET', 'default_secret_key')

    logger.info("Bot token: Configured")
    logger.info(f"Webhook URL: {webhook_url}")
    logger.info(f"Webhook secret: {'*' * len(webhook_secret)}")

    logger.warning("Make sure only ONE instance of this bot is running!")

    bot = DiscordBot()

    try:
        bot.run(bot_token)
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")
    finally:
        logger.info("Bot shutdown complete")


if __name__ == "__main__":
    main()
