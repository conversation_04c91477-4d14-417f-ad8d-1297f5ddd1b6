import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, UTC
import aiohttp
import json
import logging
import hmac
import hashlib
import os
from messaging.base_interface import MessagingInterface, Message, GroupChat, ReceivedMessage, MessageType

logger = logging.getLogger(__name__)


class DiscordAPIError(Exception):
    """Discord API specific error."""
    def __init__(self, status: int, message: str, code: Optional[int] = None):
        self.status = status
        self.code = code
        super().__init__(f"Discord API Error {status}: {message}")


class DiscordGatewayHandler:
    """Handles Discord Gateway events following official API format."""
    
    def __init__(self, DISCORD_WEBHOOK_URL: str, webhook_secret: str):
        self.DISCORD_WEBHOOK_URL = DISCORD_WEBHOOK_URL
        self.webhook_secret = webhook_secret
    
    async def send_gateway_event_to_django(self, event_type: str, event_data: Dict[str, Any]) -> bool:
        """
        Send Discord Gateway event to Django backend using official Discord format.
        This follows Discord's official Gateway API event structure.
        """
        try:
            # Use Discord's official Gateway event format
            gateway_event = {
                "t": event_type,  # Event type (MESSAGE_CREATE, etc.)
                "d": event_data,  # Event data in Discord's official format
                "op": 0,          # Opcode for dispatch events
                "s": None,        # Sequence number (handled by Discord)
                "timestamp": datetime.now(UTC).isoformat() + "Z"
            }
            
            body = json.dumps(gateway_event).encode('utf-8')
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "SageBase-Discord-Gateway/1.0",
                "X-Discord-Event": event_type
            }
            
            # Add HMAC signature for security
            if self.webhook_secret:
                signature = hmac.new(
                    self.webhook_secret.encode(),
                    body,
                    hashlib.sha256
                ).hexdigest()
                headers["X-Signature"] = signature
            
            # Send to Django
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.DISCORD_WEBHOOK_URL,
                    data=body,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ Gateway event sent: {event_type}")
                        return True
                    else:
                        logger.error(f"❌ Gateway event failed {response.status}: {await response.text()}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Error sending gateway event: {e}")
            return False


class DiscordService(MessagingInterface):
    """Discord messaging service implementation using Discord API v10."""
    
    API_VERSION = "10"
    BASE_URL = "https://discord.com/api/v{}"
    
    def __init__(self, DISCORD_WEBHOOK_URL: Optional[str] = None, webhook_secret: Optional[str] = None):
        self.bot_token: Optional[str] = None
        self.base_url = self.BASE_URL.format(self.API_VERSION)
        self.session: Optional[aiohttp.ClientSession] = None
        self.authenticated = False
        self.bot_user_id: Optional[str] = None
        self.bot_user_data: Optional[Dict[str, Any]] = None
        self._rate_limit_remaining = 1
        self._rate_limit_reset = 0

        DISCORD_WEBHOOK_URL = DISCORD_WEBHOOK_URL or os.getenv('DISCORD_WEBHOOK_URL', 'http://localhost:8001/api/integrations/discord/webhook/')
        webhook_secret = webhook_secret or os.getenv('DISCORD_WEBHOOK_SECRET', 'default_secret_key')
        
        # Gateway event handler for sending events to Django
        if DISCORD_WEBHOOK_URL and webhook_secret:
            self.gateway_handler = DiscordGatewayHandler(DISCORD_WEBHOOK_URL, webhook_secret)
        else:
            self.gateway_handler = None
            logger.warning("No Django webhook configuration - Gateway events disabled")
    
    async def authenticate(self, credentials: Dict[str, str]) -> bool:
        """Authenticate with Discord API and validate bot token."""
        try:
            self.bot_token = credentials.get("bot_token")
            if not self.bot_token:
                logger.error("No bot token provided")
                return False
            
            # Create session with proper headers
            connector = aiohttp.TCPConnector(limit=100, limit_per_host=10)
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={"User-Agent": "SageBase-Bot/1.0"}
            )
            
            # Validate token and get bot info
            bot_data = await self._make_request("GET", "/users/@me")
            if bot_data:
                self.bot_user_id = bot_data.get("id")
                self.bot_user_data = bot_data
                self.authenticated = True
                logger.info(f"Authenticated as {bot_data.get('username')}#{bot_data.get('discriminator')}")
                return True
            
            return False
                
        except Exception as e:
            logger.error(f"Discord authentication error: {e}")
            await self._cleanup_session()
            return False
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Union[Dict, aiohttp.FormData]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Make authenticated request to Discord API with rate limiting."""
        if not self.session or not self.bot_token:
            raise DiscordAPIError(401, "Not authenticated")
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bot {self.bot_token}",
            **kwargs.get("headers", {})
        }
        
        # Set content type for JSON requests
        if isinstance(data, dict):
            headers["Content-Type"] = "application/json"
            data = json.dumps(data)
        
        try:
            async with self.session.request(method, url, data=data, params=params, headers=headers) as response:
                # Handle rate limiting - safely parse headers that might be floats
                try:
                    self._rate_limit_remaining = int(response.headers.get("X-RateLimit-Remaining", 1))
                except (ValueError, TypeError):
                    self._rate_limit_remaining = 1
                
                try:
                    self._rate_limit_reset = int(float(response.headers.get("X-RateLimit-Reset-After", 0)))
                except (ValueError, TypeError):
                    self._rate_limit_reset = 0
                
                if response.status == 429:  # Rate limited
                    try:
                        retry_after = float(response.headers.get("Retry-After", 1))
                    except (ValueError, TypeError):
                        retry_after = 1.0
                    logger.warning(f"Rate limited, waiting {retry_after}s")
                    await asyncio.sleep(retry_after)
                    return await self._make_request(method, endpoint, data, params, **kwargs)
                
                response_data = await response.json() if response.content_type == "application/json" else {}
                
                if 200 <= response.status < 300:
                    return response_data
                
                # Handle API errors
                error_message = response_data.get("message", f"HTTP {response.status}")
                error_code = response_data.get("code")
                
                # Log detailed error for debugging
                if response.status == 400:
                    logger.debug(f"400 Bad Request details: {response_data}")
                
                raise DiscordAPIError(response.status, error_message, error_code)
                
        except aiohttp.ClientError as e:
            logger.error(f"Network error during Discord API request: {e}")
            raise DiscordAPIError(500, f"Network error: {e}")

    async def send_message(self, channel_id: str, message: Union[Message, str]) -> Dict[str, Any]:
        """
        Generic method to send a message to any Discord channel.
        
        Args:
            channel_id: Discord channel ID (snowflake)
            message: Message object or string content
            
        Returns:
            Dict with message details and status
        """
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        # Validate channel_id format (Discord snowflakes are numeric strings)
        if not channel_id or not channel_id.isdigit():
            raise Exception(f"Invalid channel ID format: {channel_id}. Must be a numeric Discord snowflake.")
        
        # Handle both Message objects and simple strings
        if isinstance(message, str):
            message_content = message
            message_type = MessageType.TEXT
            metadata = {}
        else:
            message_content = message.content
            message_type = message.message_type
            metadata = message.metadata or {}
        
        try:
            if message_type == MessageType.FILE and metadata:
                result = await self._upload_file(channel_id, message_content, metadata)
            else:
                payload = {"content": message_content}
                
                # Handle embeds
                if message_type == MessageType.EMBED and metadata:
                    payload["embeds"] = metadata.get("embeds", [])
                
                data = await self._make_request("POST", f"/channels/{channel_id}/messages", payload)
                
                result = {
                    "message_id": data.get("id"),
                    "status": "success",
                    "channel": data.get("channel_id"),
                    "timestamp": data.get("timestamp")
                }
            
            # Send Gateway event to Django (official Discord format)
            if self.gateway_handler:
                await self.gateway_handler.send_gateway_event_to_django("MESSAGE_CREATE", {
                    "id": result.get("message_id"),
                    "channel_id": channel_id,
                    "author": {
                        "id": self.bot_user_id,
                        "username": self.bot_user_data.get("username"),
                        "discriminator": self.bot_user_data.get("discriminator", "0000"),
                        "avatar": self.bot_user_data.get("avatar"),
                        "bot": True
                    },
                    "content": message_content,
                    "timestamp": result.get("timestamp", datetime.now(UTC).isoformat() + "Z"),
                    "tts": False,
                    "mention_everyone": False,
                    "mentions": [],
                    "attachments": [],
                    "embeds": payload.get("embeds", []) if 'payload' in locals() else [],
                    "type": 0  # MESSAGE_TYPE_DEFAULT
                })
            
            return result
            
        except DiscordAPIError as e:
            logger.error(f"Failed to send message: {e}")
            return {"status": "error", "error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error sending message: {e}")
            return {"status": "error", "error": str(e)}
    
    async def send_private_message(self, user_email: str, message: Message) -> Dict[str, Any]:
        """Send a private message to a Discord user using Discord API."""
        if not self.authenticated or not self.session:
            raise Exception("Not authenticated with Discord")
        
        try:
            # Create DM channel with the user
            dm_data = {"recipient_id": user_email}
            dm_response = await self._make_request("POST", "/users/@me/channels", data=dm_data)
            
            if not dm_response:
                return {
                    "status": "error",
                    "error": "Failed to create DM channel"
                }
            
            # Get the DM channel ID
            dm_channel_id = dm_response["id"]
            
            # Send the message to the DM
            return await self.send_message(dm_channel_id, message)
                
        except DiscordAPIError as e:
            return {
                "status": "error",
                "error": f"Discord API error: {e}"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _upload_file(self, channel_id: str, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Upload a file to Discord."""
        if not metadata or "file_path" not in metadata:
            return {"status": "error", "error": "File path not provided"}
        
        try:
            form_data = aiohttp.FormData()
            if content:
                form_data.add_field("content", content)
            
            file_path = metadata["file_path"]
            filename = metadata.get("filename", "file")
            
            with open(file_path, "rb") as file:
                form_data.add_field("files[0]", file, filename=filename)
                
                data = await self._make_request("POST", f"/channels/{channel_id}/messages", form_data)
                
                attachments = data.get("attachments", [])
                file_url = attachments[0].get("url") if attachments else ""
                
                return {
                    "message_id": data.get("id"),
                    "status": "success",
                    "file_url": file_url
                }
                
        except FileNotFoundError:
            return {"status": "error", "error": "File not found"}
        except DiscordAPIError as e:
            logger.error(f"Failed to upload file: {e}")
            return {"status": "error", "error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error uploading file: {e}")
            return {"status": "error", "error": str(e)}
    
    async def receive_messages(self, channel_id: str, limit: int = 10, include_bot_messages: bool = False) -> List[ReceivedMessage]:
        """Retrieve recent messages from a Discord channel."""
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        # Validate channel_id format (Discord snowflakes are numeric strings)
        if not channel_id or not channel_id.isdigit():
            logger.warning(f"Invalid channel ID format: {channel_id}")
            return []
        
        try:
            safe_limit = min(max(1, limit), 20)
            endpoint = f"/channels/{channel_id}/messages"
            
            data = await self._make_request("GET", endpoint, params={"limit": safe_limit})
            
            if data is None:
                return []
            
            # Debug: log what we actually received
            if isinstance(data, list):
                logger.debug(f"Received {len(data)} raw messages from Discord API")
                bot_count = sum(1 for msg in data if msg.get("author", {}).get("bot", False))
                logger.debug(f"Bot messages: {bot_count}, Include bot messages: {include_bot_messages}")
            
            messages = []
            for msg_data in data:
                # Skip bot messages (unless explicitly included)
                author = msg_data.get("author", {})
                if author.get("bot", False) and not include_bot_messages:
                    continue
                
                # Determine message type
                message_type = MessageType.TEXT
                if msg_data.get("attachments"):
                    message_type = MessageType.FILE
                elif msg_data.get("embeds"):
                    message_type = MessageType.EMBED
                
                messages.append(ReceivedMessage(
                    id=msg_data.get("id"),
                    content=msg_data.get("content", ""),
                    sender_id=author.get("id", ""),
                    sender_name=author.get("username", "Unknown User"),
                    channel_id=channel_id,
                    timestamp=msg_data.get("timestamp"),
                    message_type=message_type,
                    metadata={
                        "attachments": msg_data.get("attachments", []),
                        "embeds": msg_data.get("embeds", []),
                        "reactions": msg_data.get("reactions", []),
                        "author_discriminator": author.get("discriminator", ""),
                        "author_avatar": author.get("avatar", ""),
                        "guild_id": msg_data.get("guild_id")
                    }
                ))
            
            return messages
            
        except DiscordAPIError as e:
            if e.status == 403:
                logger.warning(f"No permission to read messages in channel {channel_id}")
            elif e.status == 404:
                logger.warning(f"Channel {channel_id} not found")
            elif e.status == 400:
                logger.warning(f"Bad request for channel {channel_id}: {e}")
            else:
                logger.error(f"Failed to retrieve messages from channel {channel_id}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving messages: {e}")
            return []
    
    async def create_group_chat(self, name: str, guild_id: str, members: Optional[List[str]] = None) -> GroupChat:
        """
        Create a Discord channel.
        
        Args:
            name: Channel name
            guild_id: Guild ID where to create the channel
            members: Optional list of user IDs for private channel. If None, creates public channel.
            
        Returns:
            GroupChat object with channel details
        """
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        # Validate guild_id format
        if not guild_id or not guild_id.isdigit():
            raise Exception(f"Invalid guild ID format: {guild_id}. Must be a numeric Discord snowflake.")
        
        # Validate member IDs if provided
        if members:
            for member in members:
                if not member.isdigit():
                    raise Exception(f"Invalid user ID: {member}. Must provide user IDs as numeric strings.")
        
        try:
            # Create the channel
            channel_payload = {
                "name": name.lower().replace(" ", "-").replace("_", "-"),
                "type": 0,  # Text channel
                "topic": f"Channel: {name}",
            }
            
            # If members are provided, create private channel
            if members:
                channel_payload["topic"] = f"Private channel: {name}"
                channel_payload["permission_overwrites"] = [
                    {
                        # Deny @everyone from seeing the channel
                        "id": guild_id,  # @everyone role has same ID as guild
                        "type": 0,  # Role type
                        "deny": "1024"  # VIEW_CHANNEL permission (2^10 = 1024)
                    }
                ]
                
                # Add permission overwrites for each member
                for member_id in members:
                    channel_payload["permission_overwrites"].append({
                        "id": member_id,
                        "type": 1,  # Member type
                        "allow": "1024"  # VIEW_CHANNEL permission
                    })
            
            logger.info(f"Creating {'private' if members else 'public'} channel '{name}' in guild {guild_id}")
            channel_data = await self._make_request("POST", f"/guilds/{guild_id}/channels", channel_payload)
            
            if not channel_data:
                raise Exception("Failed to create channel - no response data")
            
            channel_id = channel_data.get("id")
            
            # Send a welcome message to the channel
            try:
                if members:
                    welcome_message = f"🎉 **Welcome to your private channel!**\n\n" \
                                    f"**Members:** {', '.join([f'<@{member_id}>' for member_id in members])}\n" \
                                    f"This is a private channel - only invited members can see it."
                else:
                    welcome_message = f"🎉 **Welcome to #{channel_data.get('name')}!**\n\n" \
                                    f"This channel has been created successfully."
                
                await self.send_message(channel_id, welcome_message)
                logger.info(f"✅ Sent welcome message to channel {channel_id}")
            except Exception as e:
                logger.warning(f"⚠️  Failed to send welcome message: {e}")
            
            return GroupChat(
                id=channel_id,
                name=name,
                members=members or [],
                metadata={
                    "type": "private_channel" if members else "public_channel",
                    "guild_id": guild_id,
                    "channel_id": channel_id,
                    "channel_name": channel_data.get("name"),
                    "topic": channel_data.get("topic"),
                    "member_count": len(members) if members else 0,
                    "is_private": bool(members),
                    "instructions": f"Look for the channel '#{channel_data.get('name')}' in the server!"
                }
            )
            
        except DiscordAPIError as e:
            logger.error(f"Failed to create channel: {e}")
            
            if e.status == 403:
                raise Exception("Permission denied: Bot lacks 'Manage Channels' permission in this guild.")
            elif e.status == 400:
                raise Exception(f"Bad request creating channel: {e}. Check guild ID and member IDs.")
            elif e.status == 404:
                raise Exception("Guild not found. Verify the guild ID is correct.")
            else:
                raise Exception(f"Error creating channel: {e}")
        except Exception as e:
            logger.error(f"Unexpected error creating channel: {e}")
            raise Exception(f"Unexpected error: {e}")
    
    async def get_channels(self, guild_id: str) -> List[Dict[str, Any]]:
        """Get list of Discord channels from a specific guild."""
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        # Validate guild_id format
        if not guild_id or not guild_id.isdigit():
            raise Exception(f"Invalid guild ID format: {guild_id}. Must be a numeric Discord snowflake.")
        
        try:
            # Get guild info first
            guild_data = await self._make_request("GET", f"/guilds/{guild_id}")
            if not guild_data:
                return []
            
            guild_name = guild_data.get("name", "Unknown Server")
            
            # Get channels for the guild
            channels_data = await self._make_request("GET", f"/guilds/{guild_id}/channels")
            if not channels_data:
                return []
            
            channels = []
            for channel in channels_data:
                channel_type = channel.get("type", 0)
                
                # Only include text channels (0) and voice channels (2)
                if channel_type in [0, 2]:
                    channels.append({
                        "id": channel.get("id"),
                        "name": f"{guild_name} - {channel.get('name', 'Unknown Channel')}",
                        "guild_id": guild_id,
                        "guild_name": guild_name,
                        "channel_name": channel.get("name"),
                        "type": "text" if channel_type == 0 else "voice",
                        "topic": channel.get("topic", ""),
                        "position": channel.get("position", 0),
                        "nsfw": channel.get("nsfw", False)
                    })
            
            return channels
            
        except DiscordAPIError as e:
            logger.error(f"Failed to retrieve channels for guild {guild_id}: {e}")
            return []
    
    async def get_users(self, guild_id: str) -> List[Dict[str, Any]]:
        """Get list of users from a specific guild."""
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        # Validate guild_id format
        if not guild_id or not guild_id.isdigit():
            raise Exception(f"Invalid guild ID format: {guild_id}. Must be a numeric Discord snowflake.")
        
        try:
            # Get guild info first
            guild_data = await self._make_request("GET", f"/guilds/{guild_id}")
            if not guild_data:
                return []
            
            guild_name = guild_data.get("name", "Unknown Server")
            
            # Get guild members (limited to 100 per request)
            members_data = await self._make_request("GET", f"/guilds/{guild_id}/members", params={"limit": 100})
            if not members_data:
                return []
            
            users = []
            for member in members_data:
                user = member.get("user", {})
                user_id = user.get("id")
                
                # Skip bots
                if user.get("bot", False):
                    continue
                
                users.append({
                    "id": user_id,
                    "name": user.get("username", ""),
                    "discriminator": user.get("discriminator", "0000"),
                    "display_name": member.get("nick") or user.get("global_name") or user.get("username", ""),
                    "avatar": user.get("avatar", ""),
                    "guild_id": guild_id,
                    "guild_name": guild_name,
                    "joined_at": member.get("joined_at")
                })
            
            return users
            
        except DiscordAPIError as e:
            if e.status == 403:
                logger.warning(f"Missing Server Members Intent for guild {guild_id}. Enable 'Server Members Intent' in Discord Developer Portal.")
            else:
                logger.error(f"Failed to get members for guild {guild_id}: {e}")
            return []
    
    async def get_guild_info(self, guild_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific guild."""
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        # Validate guild_id format (Discord snowflakes are numeric strings)
        if not guild_id or not guild_id.isdigit():
            raise Exception(f"Invalid guild ID format: {guild_id}. Must be a numeric Discord snowflake.")
        
        try:
            return await self._make_request("GET", f"/guilds/{guild_id}")
        except DiscordAPIError as e:
            logger.error(f"Failed to get guild info: {e}")
            return None
    
    async def get_application_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the bot application."""
        if not self.authenticated:
            raise Exception("Not authenticated with Discord")
        
        try:
            return await self._make_request("GET", "/oauth2/applications/@me")
        except DiscordAPIError as e:
            logger.error(f"Failed to get application info: {e}")
            return None
    
    async def _cleanup_session(self):
        """Clean up the HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def disconnect(self) -> None:
        """Disconnect from Discord and clean up resources."""
        await self._cleanup_session()
        
        self.authenticated = False
        self.bot_token = None
        self.bot_user_id = None
        self.bot_user_data = None
        
        logger.info("Disconnected from Discord")