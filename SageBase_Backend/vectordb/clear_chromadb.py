#!/usr/bin/env python3
"""
Simple script to clear ChromaDB data: This will delete all documents and collections inside the chromadb
Reads configuration from .env.dev file
"""

import os
import chromadb
from dotenv import load_dotenv

def clear_chromadb():
    """Clear all ChromaDB data and collections using config from .env.dev"""
    
    print("🗑️  Clearing ChromaDB data and collections...")
    
    try:
        # Load environment variables from .env.dev
        load_dotenv('.env.test')
        
        # Get ChromaDB configuration from environment
        chroma_host = os.getenv('CHROMA_HOST')
        chroma_port = os.getenv('CHROMA_PORT')
        chroma_persist_dir = os.getenv('CHROppendMA_PERSIST_DIR', './chroma_db')
        
        print(f"📋 Configuration from .env.dev:")
        print(f"   CHROMA_HOST: {chroma_host or 'Not set'}")
        print(f"   CHROMA_PORT: {chroma_port or 'Not set'}")
        print(f"   CHROMA_PERSIST_DIR: {chroma_persist_dir}")
        
        # Connect to ChromaDB based on configuration
        if chroma_host and chroma_port:
            # Cloud/Remote ChromaDB
            print(f"☁️  Connecting to cloud ChromaDB at {chroma_host}:{chroma_port}")
            clean_host = chroma_host.replace('https://', '').replace('http://', '')
            client = chromadb.HttpClient(host=clean_host, port=int(chroma_port))
        else:
            # Local ChromaDB
            print(f"💾 Connecting to local ChromaDB at: {chroma_persist_dir}")
            client = chromadb.PersistentClient(path=chroma_persist_dir)
        
        # Get all collections
        collections = client.list_collections()
        print(f"📊 Found {len(collections)} collections")
        
        if not collections:
            print("✅ ChromaDB is already empty")
            return
        
        # Delete each collection completely
        total_deleted_collections = 0
        total_deleted_documents = 0
        
        for collection in collections:
            count = collection.count()
            print(f"🗑️  Deleting collection: {collection.name} ({count} documents)")
            
            try:
                # Delete the entire collection
                client.delete_collection(name=collection.name)
                print(f"✅ Deleted collection: {collection.name}")
                total_deleted_collections += 1
                total_deleted_documents += count
            except Exception as e:
                print(f"❌ Error deleting collection {collection.name}: {e}")
        
        print(f"🎉 Done! Deleted {total_deleted_collections} collections and {total_deleted_documents} documents")
        
        # Verify all collections are deleted
        remaining_collections = client.list_collections()
        print(f"📊 Remaining collections: {len(remaining_collections)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    clear_chromadb() 