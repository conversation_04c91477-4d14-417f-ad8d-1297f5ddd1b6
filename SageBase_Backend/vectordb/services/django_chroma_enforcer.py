"""
Django ChromaDB Enforcer
This module ensures that all ChromaDB operations go through Django's configured service
and prevents direct ChromaDB client usage outside of Django's service layer.
"""

import os
import logging
from typing import Optional, List, Dict, Any, Union
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

logger = logging.getLogger(__name__)

# Global flag to track if Django is properly configured
_django_configured = False

def ensure_django_configured():
    """Ensure Django is properly configured before using ChromaDB"""
    global _django_configured
    
    if not _django_configured:
        try:
            # Check if Django settings are available
            if not hasattr(settings, 'CHROMA_PERSIST_DIR'):
                raise ImproperlyConfigured(
                    "Django ChromaDB settings not found. "
                    "Make sure 'vectordb' is in INSTALLED_APPS and "
                    "CHROMA_PERSIST_DIR is configured in settings.py"
                )
            _django_configured = True
            logger.info("Django ChromaDB configuration verified")
        except Exception as e:
            logger.error(f"Failed to configure Django ChromaDB: {e}")
            raise

class DjangoChromaEnforcer:
    """
    Enforces Django ChromaDB usage and prevents direct ChromaDB client creation
    """
    
    def __init__(self):
        ensure_django_configured()
        self._chroma_service = None
    
    @property
    def chroma_service(self):
        """Get the Django-configured ChromaDB service"""
        if self._chroma_service is None:
            from .chroma_service import get_chroma_service
            self._chroma_service = get_chroma_service()
        return self._chroma_service
    
    def get_client(self):
        """Get ChromaDB client through Django service"""
        return self.chroma_service.client
    
    def get_collection(self, name: str, create_if_not_exists: bool = True):
        """Get collection through Django service"""
        return self.chroma_service.get_collection(name, create_if_not_exists)
    
    def list_collections(self) -> List[str]:
        """List collections through Django service"""
        return self.chroma_service.list_collections()
    
    def delete_collection(self, name: str) -> bool:
        """Delete collection through Django service"""
        return self.chroma_service.delete_collection(name)
    
    def health_check(self) -> Dict[str, Any]:
        """Health check through Django service"""
        return self.chroma_service.health_check()

# Global enforcer instance
_django_chroma_enforcer = None

def get_django_chroma_enforcer() -> DjangoChromaEnforcer:
    """Get the global Django ChromaDB enforcer instance"""
    global _django_chroma_enforcer
    
    if _django_chroma_enforcer is None:
        _django_chroma_enforcer = DjangoChromaEnforcer()
    
    return _django_chroma_enforcer

def enforce_django_chroma_usage():
    """
    Decorator to enforce Django ChromaDB usage
    Use this on functions that should only use Django's ChromaDB service
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            ensure_django_configured()
            return func(*args, **kwargs)
        return wrapper
    return decorator

# Override direct ChromaDB imports to enforce Django usage
def _block_direct_chromadb_import():
    """Block direct ChromaDB imports to enforce Django usage"""
    import sys
    import warnings
    
    original_import = __builtins__['__import__']
    
    def safe_import(name, *args, **kwargs):
        if name == 'chromadb' or name.startswith('chromadb.'):
            warnings.warn(
                f"Direct import of '{name}' is blocked. "
                "Use Django's ChromaDB service instead. "
                "Import vectordb.services.chroma_service and use get_chroma_service()",
                UserWarning,
                stacklevel=2
            )
            raise ImportError(
                f"Direct import of '{name}' is not allowed. "
                "Use Django's ChromaDB service: "
                "from vectordb.services.chroma_service import get_chroma_service"
            )
        return original_import(name, *args, **kwargs)
    
    __builtins__['__import__'] = safe_import

# Initialize the enforcer when module is imported
try:
    ensure_django_configured()
except Exception as e:
    logger.warning(f"Django ChromaDB enforcer not fully initialized: {e}") 