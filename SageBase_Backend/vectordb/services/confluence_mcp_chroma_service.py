"""
Confluence MCP ChromaDB Service - Integration of Confluence MCP server functionality into Django app
"""

import os
import logging
import numpy as np
from typing import List, Optional, Tuple, Union
from chromadb.config import Settings

from .chroma_service import get_chroma_service

logger = logging.getLogger(__name__)


class ConfluenceMCPChromaDBService:
    """
    Confluence MCP ChromaDB Service - Integrates Confluence MCP server indexing functionality
    into the Django app's vector database system
    """
    
    def __init__(self, collection_name: str = "mcp_confluence_documents", embedder=None):
        """
        Initialize Confluence MCP ChromaDB service
        
        Args:
            collection_name: Name of the collection for Confluence MCP documents (default: mcp_confluence_documents)
            embedder: Embedding function (if None, uses default)
        """
        self.collection_name = collection_name
        self.chroma_service = get_chroma_service()
        
        # Use provided embedder or create default
        if embedder is None:
            # Create a simple embedder function
            from sentence_transformers import SentenceTransformer
            model = SentenceTransformer("all-MiniLM-L6-v2")
            self.embedder = lambda texts: model.encode(texts)
        else:
            # Use the provided embedder (from MCP server)
            self.embedder = embedder
            
        self._collection = None
    
    @property
    def collection(self):
        """Get or create the Confluence MCP collection"""
        if self._collection is None:
            self._collection = self.chroma_service.get_collection(
                self.collection_name,
                create_if_not_exists=True
            )
        return self._collection
    
    def get_name(self) -> str:
        """Get collection name"""
        return self.collection_name
    
    def index_texts(self, ids: Union[List, str, int], texts: Union[List, str]):
        """
        Index Confluence texts with their corresponding IDs (MCP-style interface)
        
        Args:
            ids: List of document IDs or single ID
            texts: List of text content or single text
        """
        # Normalize inputs
        if isinstance(texts, str):
            texts = [texts]
        if isinstance(ids, (int, str)):
            ids = [str(ids)]
        else:
            ids = [str(id) for id in ids]
        
        # Generate embeddings
        embeddings = self.embedder(texts)
        
        # Add to collection
        self.collection.add(
            embeddings=embeddings.tolist(),
            documents=texts,
            ids=ids
        )
        
        logger.info(f"Indexed {len(texts)} Confluence documents in MCP collection '{self.collection_name}'")
    
    def remove_ids(self, ids: Union[List, str, int]):
        """
        Remove Confluence documents by their IDs (MCP-style interface)
        
        Args:
            ids: List of document IDs or single ID to remove
        """
        if isinstance(ids, (int, str)):
            ids = [str(ids)]
        else:
            ids = [str(id) for id in ids]
        
        self.collection.delete(ids=ids)
        logger.info(f"Removed {len(ids)} Confluence documents from MCP collection '{self.collection_name}'")
    
    def search(self, text: str, number_of_results: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        Search for similar Confluence documents (MCP-style interface)
        
        Args:
            text: Query text
            number_of_results: Number of results to return
            
        Returns:
            Tuple of (distances, ids) similar to FAISS search results
        """
        logger.debug(f"Confluence MCP ChromaDB search: '{text}', results: {number_of_results}")
        
        # Generate query embedding
        query_embedding = self.embedder([text])
        
        # Get total collection size
        total_docs = self.collection.count()
        if total_docs == 0:
            logger.debug("Confluence MCP collection is empty")
            return np.array([]), np.array([])
        
        # Ensure we don't ask for more results than available
        actual_n_results = min(number_of_results, total_docs)
        
        # Search in collection
        results = self.collection.query(
            query_embeddings=query_embedding.tolist(),
            n_results=actual_n_results,
            include=['distances', 'documents', 'metadatas']
        )
        
        # If no results found, get all documents as fallback
        if not results['ids'] or len(results['ids'][0]) == 0:
            logger.debug("No Confluence search results found, using fallback")
            all_results = self.collection.get(include=['metadatas'])
            if all_results['ids']:
                # Create artificial scores and return all documents
                distances = np.array([0.1] * len(all_results['ids']))
                ids = np.array([int(id) for id in all_results['ids']])
                return distances, ids
            else:
                return np.array([]), np.array([])
        
        # Convert to FAISS-like format
        distances = np.array(results['distances'][0]) if results['distances'] else np.array([])
        ids = np.array([int(id) for id in results['ids'][0]]) if results['ids'] else np.array([])
        
        logger.debug(f"Confluence MCP search found {len(ids)} results")
        return distances, ids
    
    def get_size(self) -> int:
        """Get the number of Confluence documents in the collection"""
        return self.collection.count()
    
    def serialize(self) -> bytes:
        """
        ChromaDB is persistent by default, so we return the collection path
        as a serialized representation
        """
        return self.collection_name.encode('utf-8')
    
    def health_check(self) -> dict:
        """Check Confluence MCP collection health"""
        try:
            count = self.get_size()
            return {
                "status": "healthy",
                "collection_name": self.collection_name,
                "document_count": count,
                "embedding_model": getattr(self.embedder, 'model_name', 'unknown'),
                "source": "confluence_mcp"
            }
        except Exception as e:
            logger.error(f"Confluence MCP collection health check failed: {e}")
            return {
                "status": "unhealthy",
                "collection_name": self.collection_name,
                "error": str(e),
                "source": "confluence_mcp"
            }


# Global Confluence MCP service instance
_confluence_mcp_chroma_service = None

def get_confluence_mcp_chroma_service(collection_name: str = "mcp_confluence_documents") -> ConfluenceMCPChromaDBService:
    """Get global Confluence MCP ChromaDB service instance"""
    global _confluence_mcp_chroma_service
    
    if _confluence_mcp_chroma_service is None:
        _confluence_mcp_chroma_service = ConfluenceMCPChromaDBService(collection_name=collection_name)
    
    return _confluence_mcp_chroma_service


def reset_confluence_mcp_chroma_service():
    """Reset the global Confluence MCP ChromaDB service instance"""
    global _confluence_mcp_chroma_service
    _confluence_mcp_chroma_service = None 