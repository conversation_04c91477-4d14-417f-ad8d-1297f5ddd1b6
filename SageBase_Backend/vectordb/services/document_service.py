import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import uuid

from ..models.document import Document, DocumentMetadata, SearchResult, SearchQuery, DataSource
from .chroma_service import get_chroma_service
from ..utils.text_utils import TextProcessor

logger = logging.getLogger(__name__)


class DocumentService:
    """
    High-level service for document operations in ChromaDB
    Provides CRUD operations and semantic search capabilities
    """
    
    def __init__(self, collection_name: str = "documents"):
        """
        Initialize document service
        
        Args:
            collection_name: Name of the ChromaDB collection to use
        """
        self.collection_name = collection_name
        self.chroma_service = get_chroma_service()
        self.text_processor = TextProcessor()
        self._collection = None
    
    @property
    def collection(self):
        """Get or create the collection"""
        if self._collection is None:
            self._collection = self.chroma_service.get_collection(
                self.collection_name, 
                create_if_not_exists=True
            )
        return self._collection
    
    def add_document(self, 
                    content: str, 
                    metadata: DocumentMetadata,
                    document_id: Optional[str] = None,
                    chunk_size: int = 400,
                    chunk_overlap: int = 100) -> List[str]:
        """
        Add a document to the vector database
        
        Args:
            content: Document content
            metadata: Document metadata
            document_id: Optional custom document ID
            chunk_size: Size of text chunks for large documents
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of document IDs that were added (one per chunk)
        """
        try:
            if not document_id:
                document_id = str(uuid.uuid4())
            
            # Process and chunk the content
            chunks = self.text_processor.chunk_text(
                content, 
                chunk_size=chunk_size, 
                overlap=chunk_overlap
            )
            
            added_ids = []
            
            if len(chunks) == 1:
                # Single document
                doc = Document(
                    id=document_id,
                    content=chunks[0],
                    metadata=metadata,
                    chunk_index=0,
                    total_chunks=1
                )
                
                chroma_data = doc.to_chroma_format()
                try:
                    self.collection.add(
                        documents=[chroma_data["document"]],
                        metadatas=[chroma_data["metadata"]],
                        ids=[chroma_data["id"]]
                    )
                except Exception as e:
                    logger.exception(f"Failed to add document: {e}")
                    return []
                
                added_ids.append(chroma_data["id"])
                
            else:
                # Multiple chunks
                documents = []
                metadatas = []
                ids = []
                
                for i, chunk in enumerate(chunks):
                    doc = Document(
                        id=document_id,
                        content=chunk,
                        metadata=metadata,
                        chunk_index=i,
                        total_chunks=len(chunks)
                    )
                    
                    chroma_data = doc.to_chroma_format()
                    documents.append(chroma_data["document"])
                    metadatas.append(chroma_data["metadata"])
                    ids.append(chroma_data["id"])
                    added_ids.append(chroma_data["id"])
                
                # Batch add all chunks
                try:
                    self.collection.add(
                        documents=documents,
                        metadatas=metadatas,
                        ids=ids
                    )
                except Exception as e:
                    logger.exception(f"Failed to add document: {e}")
                    return []
            
            logger.info(f"Added document {document_id} with {len(chunks)} chunks")
            return added_ids
            
        except Exception as e:
            logger.exception(f"Failed to add document: {e}")
            raise
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> List[str]:
        """
        Add multiple documents in batch
        
        Args:
            documents: List of documents with content, metadata, and optional id
            
        Returns:
            List of all document IDs that were added
            
            
            Here is an example of documents:
                📄 Document 48:
                🏷️  Title: What security measures does SageBase have?
                📝 Content Preview: question: Can you discuss the security and infrastructure of SageBase? answer: SageBase runs 
                entirely on your infrastructure or private cloud, ensurin...
                🎯 Score: 1.000
                🗂️  Source: other
                🆔 Source ID: qa-82e7d2ac
                🏢 Workspace: sagebase
                👤 Author: wissem Golli
                🏷️  Tags: SageBase, security, infrastructure, compliance
                🔗 URL: http://localhost:8001/api/knowledge-spaces/project/proj-id/qa/qa-82e7d2ac
                📄 Type: qa
        """
        all_added_ids = []
        
        for doc_data in documents:
            content = doc_data["content"]
            metadata = doc_data["metadata"]
            doc_id = doc_data.get("id")
            
            if isinstance(metadata, dict):
                metadata = DocumentMetadata.from_dict(metadata)
            
            added_ids = self.add_document(content, metadata, doc_id)
            all_added_ids.extend(added_ids)
        
        return all_added_ids
    
    def get_document(self, document_id: str) -> Optional[Document]:
        """
        Get a document by ID
        
        Args:
            document_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        try:
            # Try to get single document first
            results = self.collection.get(ids=[document_id])
            
            if results['ids']:
                return Document(
                    id=document_id,
                    content=results['documents'][0],
                    metadata=DocumentMetadata.from_dict(results['metadatas'][0])
                )
            
            # If not found, try to find chunks
            results = self.collection.get(
                where={"original_id": document_id}
            )
            
            if results['ids']:
                # Sort chunks by index
                chunks = list(zip(
                    results['documents'], 
                    results['metadatas'], 
                    results['ids']
                ))
                chunks.sort(key=lambda x: x[1].get('chunk_index', 0))
                
                # Combine content from all chunks
                combined_content = "\n".join([chunk[0] for chunk in chunks])
                
                return Document(
                    id=document_id,
                    content=combined_content,
                    metadata=DocumentMetadata.from_dict(chunks[0][1]),
                    total_chunks=len(chunks)
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get document {document_id}: {e}")
            return None
    
    def update_document(self, 
                       document_id: str, 
                       content: Optional[str] = None,
                       metadata: Optional[DocumentMetadata] = None) -> bool:
        """
        Update a document
        
        Args:
            document_id: Document ID
            content: New content (optional)
            metadata: New metadata (optional)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete existing document/chunks
            self.delete_document(document_id)
            
            # Re-add with new content/metadata
            if content is not None:
                if metadata is None:
                    # Need to get existing metadata
                    logger.warning(f"Updating content without metadata for {document_id}")
                    metadata = DocumentMetadata(
                        source=DataSource.LOCAL,
                        source_id=document_id,
                        title="Updated Document",
                        updated_at=datetime.now()
                    )
                
                self.add_document(content, metadata, document_id)
                return True
            
            # TODO: Implement metadata-only updates
            logger.warning("Metadata-only updates not yet implemented")
            return False
            
        except Exception as e:
            logger.error(f"Failed to update document {document_id}: {e}")
            return False
    
    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document and all its chunks
        
        Args:
            document_id: Document ID (can be source_id or original_id)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            all_ids = set()  # Use set to avoid duplicates
            
            # Get all chunks for this document by original_id (which should match source_id)
            results = self.collection.get(
                where={"original_id": document_id}
            )
            all_ids.update(results['ids'])
            
            # Also try by source_id
            source_results = self.collection.get(
                where={"source_id": document_id}
            )
            all_ids.update(source_results['ids'])
            
            # Also try direct ID (for single documents)
            try:
                direct_results = self.collection.get(ids=[document_id])
                all_ids.update(direct_results['ids'])
            except Exception:
                # Document ID might not exist as direct ID, that's okay
                pass
            
            if all_ids:
                all_ids_list = list(all_ids)
                self.collection.delete(ids=all_ids_list)
                logger.info(f"Deleted document {document_id} and {len(all_ids_list)} chunks")
                return True
            else:
                logger.warning(f"Document {document_id} not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            return False
    
    def search(self, query: SearchQuery) -> List[SearchResult]:
        """
        Search documents using semantic similarity, textual search, or hybrid approach
        
        Args:
            query: Search query with filters
            
        Returns:
            List of search results
        """
        try:
            if query.search_mode == "semantic":
                return self._semantic_search(query)
            elif query.search_mode == "textual":
                return self._textual_search(query)
            else:  # hybrid
                return self._hybrid_search(query)
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def _semantic_search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform semantic search using vector embeddings"""
        try:
            # Build ChromaDB query
            where_filter = query.to_chroma_filter()
            
            # Perform semantic search
            results = self.collection.query(
                query_texts=[query.query],
                n_results=query.limit,
                where=where_filter,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Convert to SearchResult objects
            search_results = []
            
            if results['ids'][0]:  # Check if we have results
                for i in range(len(results['ids'][0])):
                    chroma_id = results['ids'][0][i]
                    content = results['documents'][0][i]
                    metadata = results['metadatas'][0][i]
                    distance = results['distances'][0][i]
                    
                    # Filter by similarity threshold
                    similarity_score = max(0, 1 - distance)
                    if similarity_score >= query.similarity_threshold:
                        search_result = SearchResult.from_chroma_result(
                            chroma_id, content, metadata, distance
                        )
                        search_results.append(search_result)
            
            logger.info(f"Semantic search for '{query.query}' returned {len(search_results)} results")
            return search_results
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []
    
    def _textual_search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform textual search using keyword matching"""
        try:
            # Build ChromaDB query
            where_filter = query.to_chroma_filter()
            
            # Get all documents that match the filters
            results = self.collection.get(
                where=where_filter,
                include=['documents', 'metadatas'],
                limit=query.limit * 5  # Get more results to filter through
            )
            
            # Perform textual search
            search_results = []
            query_terms = query.query.lower().split()
            
            if results['ids']:
                for i in range(len(results['ids'])):
                    chroma_id = results['ids'][i]
                    content = results['documents'][i]
                    metadata = results['metadatas'][i]
                    
                    # Calculate textual relevance score
                    content_lower = content.lower()
                    title_lower = metadata.get('title', '').lower()
                    
                    # Score based on exact matches and partial matches
                    score = 0.0
                    
                    # Exact phrase match gets highest score
                    if query.query.lower() in content_lower:
                        score += 0.8
                    if query.query.lower() in title_lower:
                        score += 0.9
                    
                    # Individual term matches
                    for term in query_terms:
                        if term in content_lower:
                            score += 0.3
                        if term in title_lower:
                            score += 0.4
                    
                    # Normalize score
                    score = min(score, 1.0)
                    
                    # Only include if score is above threshold
                    if score >= query.similarity_threshold:
                        # Create SearchResult with textual score
                        search_result = SearchResult.from_chroma_result(
                            chroma_id, content, metadata, 1.0 - score  # Convert score to distance
                        )
                        search_results.append(search_result)
            
            # Sort by relevance score
            search_results.sort(key=lambda x: x.score, reverse=True)
            
            # Limit results
            search_results = search_results[:query.limit]
            
            logger.info(f"Textual search for '{query.query}' returned {len(search_results)} results")
            return search_results
            
        except Exception as e:
            logger.error(f"Textual search failed: {e}")
            return []
    
    def _hybrid_search(self, query: SearchQuery) -> List[SearchResult]:
        """Combine semantic and textual search results"""
        try:
            # Get semantic results
            semantic_query = SearchQuery(
                query=query.query,
                sources=query.sources,
                workspace=query.workspace,
                content_type=query.content_type,
                tags=query.tags,
                author=query.author,
                date_from=query.date_from,
                date_to=query.date_to,
                limit=query.limit,
                similarity_threshold=query.similarity_threshold * 0.7,  # Lower threshold for semantic
                metadata_filters=query.metadata_filters,
                search_mode="semantic"
            )
            semantic_results = self._semantic_search(semantic_query)
            
            # Get textual results
            textual_query = SearchQuery(
                query=query.query,
                sources=query.sources,
                workspace=query.workspace,
                content_type=query.content_type,
                tags=query.tags,
                author=query.author,
                date_from=query.date_from,
                date_to=query.date_to,
                limit=query.limit,
                similarity_threshold=query.similarity_threshold * 0.7,  # Lower threshold for textual
                metadata_filters=query.metadata_filters,
                search_mode="textual"
            )
            textual_results = self._textual_search(textual_query)
            
            # Merge and boost results
            combined_results = {}
            
            # Add semantic results with boost
            for result in semantic_results:
                result_key = f"{result.metadata.source_id}_{result.chunk_index}"
                boosted_score = min(1.0, result.score * query.semantic_boost)
                combined_results[result_key] = SearchResult(
                    id=result.id,
                    content=result.content,
                    metadata=result.metadata,
                    distance=1.0 - boosted_score,
                    score=boosted_score,
                    chunk_index=result.chunk_index,
                    total_chunks=result.total_chunks
                )
            
            # Add textual results with boost (combine if already exists)
            for result in textual_results:
                result_key = f"{result.metadata.source_id}_{result.chunk_index}"
                boosted_score = min(1.0, result.score * query.textual_boost)
                
                if result_key in combined_results:
                    # Combine scores (weighted average)
                    existing_score = combined_results[result_key].score
                    combined_score = (existing_score + boosted_score) / 2
                    combined_results[result_key].score = combined_score
                    combined_results[result_key].distance = 1.0 - combined_score
                else:
                    combined_results[result_key] = SearchResult(
                        id=result.id,
                        content=result.content,
                        metadata=result.metadata,
                        distance=1.0 - boosted_score,
                        score=boosted_score,
                        chunk_index=result.chunk_index,
                        total_chunks=result.total_chunks
                    )
            
            # Sort by combined score and limit
            final_results = list(combined_results.values())
            final_results.sort(key=lambda x: x.score, reverse=True)
            final_results = final_results[:query.limit]
            
            logger.info(f"Hybrid search for '{query.query}' returned {len(final_results)} results (semantic: {len(semantic_results)}, textual: {len(textual_results)})")
            return final_results
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []
    
    def search_by_metadata(self, filters: Dict[str, Any], limit: int = 10) -> List[SearchResult]:
        """
        Search documents by metadata only (no semantic search)
        
        Args:
            filters: Metadata filters
            limit: Maximum number of results
            
        Returns:
            List of documents matching the filters
        """
        try:
            results = self.collection.get(
                where=filters,
                limit=limit,
                include=['documents', 'metadatas']
            )
            
            search_results = []
            
            if results['ids']:
                for i in range(len(results['ids'])):
                    chroma_id = results['ids'][i]
                    content = results['documents'][i]
                    metadata = results['metadatas'][i]
                    
                    search_result = SearchResult.from_chroma_result(
                        chroma_id, content, metadata, 0.0  # No distance for metadata search
                    )
                    search_results.append(search_result)
            
            return search_results
            
        except Exception as e:
            logger.error(f"Metadata search failed: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get collection statistics
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            count = self.collection.count()
            
            # Get source distribution
            source_stats = {}
            for source in DataSource:
                source_count = len(self.collection.get(
                    where={"source": source.value},
                    include=[]
                )['ids'])
                if source_count > 0:
                    source_stats[source.value] = source_count
            
            return {
                "total_documents": count,
                "collection_name": self.collection_name,
                "source_distribution": source_stats,
                "embedding_model": self.chroma_service.embedding_model_name
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}
    
    def clear_collection(self) -> bool:
        """
        Clear all documents from the collection
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get all IDs and delete them
            all_results = self.collection.get(include=[])
            if all_results['ids']:
                self.collection.delete(ids=all_results['ids'])
                logger.info(f"Cleared {len(all_results['ids'])} documents from collection")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear collection: {e}")
            return False