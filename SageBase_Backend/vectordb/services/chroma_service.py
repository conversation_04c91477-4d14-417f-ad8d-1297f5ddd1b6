# Import telemetry suppressor first to disable ChromaDB telemetry
from ..utils.chroma_telemetry_suppressor import suppress_chroma_warnings

import os
import logging
from typing import Optional, List, Dict, Any, Union
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
import sys

# Import generic colored logging setup
from logger.logging_utils import setup_colored_logging

# Setup logging
colorlog_available = setup_colored_logging()
logger = logging.getLogger(__name__)

if not colorlog_available:
    logger.warning("colorlog not available - using basic logging. Install with: pip install colorlog")


class ChromaDBService:
    """
    Base service for ChromaDB operations and connection management
    """
    
    def __init__(self, 
                 persist_directory: Optional[str] = None,
                 embedding_type: str = "sentence_transformer",
                 embedding_model: str = "all-MiniLM-L6-v2",
                 openai_api_key: Optional[str] = None,
                 host: Optional[str] = None,
                 port: Optional[int] = None,
                 use_server: bool = True):
        """
        Initialize ChromaDB service
        
        Args:
            persist_directory: Local directory for persistent storage
            embedding_type: "sentence_transformer" or "openai"
            embedding_model: Model name (ST model or OpenAI model)
            openai_api_key: OpenAI API key (required for OpenAI embeddings)
            host: ChromaDB server host (for client mode)
            port: ChromaDB server port (for client mode)
        """
        # Use constructor parameters directly (environment variable reading handled by get_configured_chroma_service)
        self.persist_directory = persist_directory or "./chroma_db"
        self.embedding_type = embedding_type
        self.embedding_model_name = embedding_model
        self.openai_api_key = openai_api_key
        self.host = host
        self.port = port
        self.use_server = use_server
        
        # Validation
        if self.embedding_type == "openai" and not self.openai_api_key:
            raise ValueError("OpenAI API key is required for OpenAI embeddings. Set OPENAI_API_KEY environment variable or pass openai_api_key parameter.")
        
        self._client = None
        self._embedding_function = None
        
        # Ensure persist directory exists
        if not self.host:
            os.makedirs(self.persist_directory, exist_ok=True)
    
    @property
    def client(self) -> chromadb.Client:
        """Get or create ChromaDB client"""
        if self._client is None:
            self._client = self._create_client()
        return self._client
    
    @property
    def embedding_function(self):
        """Get or create embedding function"""
        if self._embedding_function is None:
            self._embedding_function = self._create_embedding_function()
        return self._embedding_function
    
    def _create_client(self) -> chromadb.Client:
        """Create ChromaDB client"""
        try:
            if self.use_server and (self.host or self.port):
                # Client mode - connect to ChromaDB server
                host = self.host or "localhost"
                port = self.port or 8000
                logger.debug(f"Connecting to ChromaDB server at {host}:{port}")
                try:
                    # Clean host name (remove protocol if present)
                    clean_host = host
                    if clean_host.startswith('https://'):
                        clean_host = clean_host.replace('https://', '')
                    elif clean_host.startswith('http://'):
                        clean_host = clean_host.replace('http://', '')
                    
                    # Create client using new v2 API
                    client = chromadb.HttpClient(host=clean_host, port=port)
                    
                    # Test the connection by listing collections
                    client.list_collections()
                    logger.debug("✅ Successfully connected to ChromaDB server")
                    return client
                    
                except Exception as e:
                    logger.exception(f"Failed to connect to ChromaDB server at {host}:{port}")
                    print(f"\033[91m[CRITICAL] Could not connect to ChromaDB server at {host}:{port}\033[0m", file=sys.stderr)
                    print(f"\033[93m[INFO] Please check:\033[0m", file=sys.stderr)
                    print(f"  - Server is running and accessible", file=sys.stderr)
                    print(f"  - Host and port are correct", file=sys.stderr)
                    print(f"  - Network connectivity and firewall settings", file=sys.stderr)
                    print(f"  - For Azure: Check if the service is deployed and accessible", file=sys.stderr)
                    print(f"  - Ensure ChromaDB client version >= 0.5.0", file=sys.stderr)
                    sys.exit(1)
            else:
                # Default to local server if no host/port specified
                logger.debug(f"Using local ChromaDB server at localhost:8000")
                
                try:
                    # For ChromaDB 1.0+, use the new client format
                    # Connect to the local server
                    client = chromadb.HttpClient(
                        host="localhost",
                        port=8000
                    )
                    return client
                except Exception as e:
                    logger.exception(f"Failed to create persistent ChromaDB client: {e}")
                    print(f"\033[91m[CRITICAL] Could not initialize persistent ChromaDB client.\033[0m", file=sys.stderr)
                    sys.exit(1)
        except Exception as e:
            logger.exception(f"Failed to create ChromaDB client: {e}")
            print("\033[91m[CRITICAL] Could not initialize ChromaDB client. The application will now exit.\033[0m", file=sys.stderr)
            sys.exit(1)
    
    def _create_embedding_function(self):
        """Create embedding function based on type"""
        try:
            if self.embedding_type in ["openai", "azure"]:
                # Use Azure OpenAI embedding function
                logger.debug(f"Using Azure OpenAI embeddings with model: {self.embedding_model_name}")
                
                # Get Azure-specific configuration for embeddings
                azure_base_endpoint = os.getenv("AZURE_EMBEDDING_ENDPOINT", "https://sagebasemodels.openai.azure.com/")
                azure_deployment_id = os.getenv("AZURE_EMBEDDING_DEPLOYMENT", "text-embedding-3-small_france_sagebBase")
                azure_api_version = os.getenv("AZURE_EMBEDDING_API_VERSION", "2023-05-15")
                
                logger.debug(f"Using Azure base endpoint: {azure_base_endpoint}")
                logger.debug(f"Using Azure deployment ID: {azure_deployment_id}")
                logger.debug(f"Using Azure API version: {azure_api_version}")
                
                return embedding_functions.OpenAIEmbeddingFunction(
                    api_key=self.openai_api_key,
                    model_name=self.embedding_model_name,
                    api_type="azure",
                    api_base=azure_base_endpoint,
                    api_version=azure_api_version,
                    deployment_id=azure_deployment_id
                )
            else:
                # Use SentenceTransformer embedding function (default)
                logger.debug(f"Using SentenceTransformer embeddings with model: {self.embedding_model_name}")
                return embedding_functions.SentenceTransformerEmbeddingFunction(
                    model_name=self.embedding_model_name
                )
        except Exception as e:
            logger.error(f"Failed to create {self.embedding_type} embedding function: {e}")
            raise
    
    def get_collection(self, name: str, create_if_not_exists: bool = True):
        """
        Get or create a collection
        
        Args:
            name: Collection name
            create_if_not_exists: Create collection if it doesn't exist
            
        Returns:
            ChromaDB collection
        """
        try:
            return self.client.get_collection(
                name=name,
                embedding_function=self.embedding_function
            )
        except Exception as e:
            if create_if_not_exists:
                logger.debug(f"Creating new collection: {name}")
                return self.client.create_collection(
                    name=name,
                    embedding_function=self.embedding_function,
                    metadata={"hnsw:space": "cosine"}  # Use cosine similarity
                )
            else:
                logger.error(f"Collection {name} not found: {e}")
                raise
    
    def list_collections(self) -> List[str]:
        """List all collections"""
        try:
            collections = self.client.list_collections()
            return [col.name for col in collections]
        except Exception as e:
            logger.error(f"Failed to list collections: {e}")
            return []
    
    def delete_collection(self, name: str) -> bool:
        """
        Delete a collection
        
        Args:
            name: Collection name
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.client.delete_collection(name=name)
            logger.debug(f"Deleted collection: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection {name}: {e}")
            return False
    
    def collection_exists(self, name: str) -> bool:
        """Check if collection exists"""
        try:
            self.client.get_collection(name=name)
            return True
        except:
            return False
    
    def get_collection_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get collection information"""
        try:
            collection = self.get_collection(name, create_if_not_exists=False)
            count = collection.count()
            return {
                "name": name,
                "count": count,
                "metadata": collection.metadata
            }
        except Exception as e:
            logger.error(f"Failed to get collection info for {name}: {e}")
            return None
    
    def reset_database(self) -> bool:
        """
        Reset entire database (DANGER: deletes all data)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.client.reset()
            logger.warning("Database reset completed - all data deleted")
            return True
        except Exception as e:
            logger.error(f"Failed to reset database: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """
        Check ChromaDB health and connectivity
        
        Returns:
            Health status information
        """
        try:
            # Test basic operations
            test_collection_name = "health_check_test"
            
            # Create test collection
            collection = self.client.create_collection(
                name=test_collection_name,
                embedding_function=self.embedding_function
            )
            
            # Add test document
            try:
                collection.add(
                    documents=["test document"],
                    ids=["test_id"],
                    metadatas=[{"test": True}]
                )
            except Exception as e:
                logger.exception(f"Failed to add test document: {e}")
                return {
                    "status": "error",
                    "error": str(e),
                    "embedding_type": self.embedding_type,
                    "embedding_model": self.embedding_model_name,
                    "persist_directory": self.persist_directory
                }
                
            # Query test document
            results = collection.query(
                query_texts=["test"],
                n_results=1
            )
            
            # Clean up
            self.client.delete_collection(name=test_collection_name)
            
            return {
                "status": "healthy",
                "embedding_type": self.embedding_type,
                "embedding_model": self.embedding_model_name,
                "persist_directory": self.persist_directory,
                "collections_count": len(self.list_collections()),
                "test_query_success": len(results['ids'][0]) > 0
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "embedding_type": self.embedding_type,
                "embedding_model": self.embedding_model_name,
                "persist_directory": self.persist_directory
            }
    
    def close(self):
        """Close the ChromaDB client connection"""
        if self._client:
            # ChromaDB doesn't have explicit close method for PersistentClient
            # but we can reset our reference
            self._client = None
            logger.debug("ChromaDB client connection closed")


# Global ChromaDB service instance and its configuration
_chroma_service = None
_chroma_service_config = None

def reset_chroma_service():
    """Reset the global ChromaDB service instance to pick up new configuration"""
    global _chroma_service, _chroma_service_config
    _chroma_service = None
    _chroma_service_config = None

def get_chroma_service() -> ChromaDBService:
    """Get global ChromaDB service instance with environment configuration"""
    global _chroma_service, _chroma_service_config
    import os
    from dotenv import load_dotenv
    
    # Load environment variables from .env.dev
    load_dotenv('.env.dev')
    
    # Read current environment configuration
    current_config = {
        'persist_directory': os.getenv("CHROMA_PERSIST_DIR", "./chroma_db"),
        'embedding_type': os.getenv("CHROMA_EMBEDDING_TYPE", "sentence_transformer"),
        'embedding_model': os.getenv("CHROMA_EMBEDDING_MODEL", "all-MiniLM-L6-v2"),
        'openai_api_key': os.getenv("AZURE_OPENAI_API_KEY"),  # Use Azure OpenAI API key
        'host': os.getenv("CHROMA_HOST"),
        'port': int(os.getenv("CHROMA_PORT", 8000)) if os.getenv("CHROMA_PORT") else None
    }
    
    # Check if we need to recreate the service due to config changes
    if _chroma_service is None or _chroma_service_config != current_config:
        logger.debug("Creating new ChromaDB service due to configuration changes")
        _chroma_service = ChromaDBService(**current_config)
        _chroma_service_config = current_config.copy()
    
    return _chroma_service


# ============================================================================
# Helper Functions for Easy Embedding Type Selection
# ============================================================================

def create_openai_chroma_service(api_key: str = None, model: str = "text-embedding-3-small") -> ChromaDBService:
    """
    Create ChromaDB service with Azure OpenAI embeddings
    
    Args:
        api_key: Azure OpenAI API key (defaults to AZURE_OPENAI_API_KEY environment variable)
        model: Azure OpenAI embedding model (text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002)
    
    Returns:
        ChromaDBService configured for Azure OpenAI embeddings
    """
    return ChromaDBService(
        embedding_type="openai",
        embedding_model=model,
        openai_api_key=api_key
    )

def create_sentence_transformer_chroma_service(model: str = "all-MiniLM-L6-v2") -> ChromaDBService:
    """
    Create ChromaDB service with SentenceTransformer embeddings
    
    Args:
        model: SentenceTransformer model name
    
    Returns:
        ChromaDBService configured for SentenceTransformer embeddings
    """
    return ChromaDBService(
        embedding_type="sentence_transformer",
        embedding_model=model
    )

def get_configured_chroma_service() -> ChromaDBService:
    """
    Get ChromaDB service configured based on environment variables
    
    Returns:
        ChromaDBService configured based on environment settings (same as get_chroma_service)
    """
    # Since get_chroma_service now also uses environment configuration,
    # we can just return the global instance
    return get_chroma_service()