from django.core.management.base import BaseCommand
from vectordb.examples.usage_examples import VectorDBExamples


class Command(BaseCommand):
    help = 'Test ChromaDB vector database functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--example',
            type=int,
            help='Run specific example (1-5), or run all if not specified',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting ChromaDB vector database tests...')
        )
        
        examples = VectorDBExamples()
        
        example_num = options.get('example')
        
        try:
            if example_num == 1:
                examples.example_1_basic_document_operations()
            elif example_num == 2:
                examples.example_2_multiple_data_sources()
            elif example_num == 3:
                examples.example_3_advanced_search_with_filters()
            elif example_num == 4:
                examples.example_4_bulk_operations()
            elif example_num == 5:
                examples.example_5_health_check_and_diagnostics()
            else:
                examples.run_all_examples()
            
            self.stdout.write(
                self.style.SUCCESS('ChromaDB tests completed successfully!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'ChromaDB tests failed: {str(e)}')
            )
            raise