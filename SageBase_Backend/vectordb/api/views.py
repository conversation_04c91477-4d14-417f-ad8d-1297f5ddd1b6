from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
import traceback
from datetime import datetime

from ..models.document import DataSource, DocumentMetadata, SearchQuery
from ..collections.collection_manager import get_collection_manager
from ..services.chroma_service import get_chroma_service


class VectorDBHealthView(APIView):
    """
    Health check endpoint for the vector database
    """
    
    def get(self, request):
        """Get health status of vector database"""
        try:
            # Use default collection for health check
            collection_manager = get_collection_manager('default')
            health_data = collection_manager.health_check()
            
            return Response(health_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {"error": f"Health check failed: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class VectorDBStatsView(APIView):
    """
    Statistics endpoint for the vector database
    """
    
    def get(self, request):
        """Get statistics for all collections"""
        try:
            # Use default collection for stats
            collection_manager = get_collection_manager('default')
            stats = collection_manager.get_all_stats()
            
            return Response(stats, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {"error": f"Failed to get stats: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentAddView(APIView):
    """
    Add documents to the vector database
    """
    
    def post(self, request):
        """Add a single document"""
        try:
            data = request.data
            
            # Validate required fields
            required_fields = ['content', 'source', 'source_id', 'title']
            for field in required_fields:
                if field not in data:
                    return Response(
                        {"error": f"Missing required field: {field}"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Validate source
            try:
                source = DataSource(data['source'])
            except ValueError:
                return Response(
                    {"error": f"Invalid source. Must be one of: {[s.value for s in DataSource]}"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get collection name from workspace or use default
            collection_name = data.get('workspace', 'default')
            collection_manager = get_collection_manager(collection_name)
            
            # Add document
            document_ids = collection_manager.add_document(
                content=data['content'],
                source=source,
                source_id=data['source_id'],
                title=data['title'],
                author=data.get('author'),
                workspace=data.get('workspace'),
                tags=data.get('tags', []),
                url=data.get('url'),
                content_type=data.get('content_type', 'text'),
                permissions=data.get('permissions', []),
                custom_fields=data.get('custom_fields', {}),
                document_id=data.get('document_id'),
                chunk_size=data.get('chunk_size', 400),
                chunk_overlap=data.get('chunk_overlap', 100)
            )
            
            return Response({
                "success": True,
                "message": "Document added successfully",
                "document_ids": document_ids,
                "chunks_created": len(document_ids)
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {"error": f"Failed to add document: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentBulkAddView(APIView):
    """
    Bulk add documents to the vector database
    """
    
    def post(self, request):
        """Bulk add documents"""
        try:
            data = request.data
            
            if 'source' not in data or 'documents' not in data:
                return Response(
                    {"error": "Missing required fields: source, documents"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate source
            try:
                source = DataSource(data['source'])
            except ValueError:
                return Response(
                    {"error": f"Invalid source. Must be one of: {[s.value for s in DataSource]}"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get collection name from data or use default
            collection_name = data.get('collection_name', 'default') 
            collection_manager = get_collection_manager(collection_name)
            result = collection_manager.bulk_import(source, data['documents'])
            
            return Response(result, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {"error": f"Bulk import failed: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentSearchView(APIView):
    """
    Search documents in the vector database
    """
    
    def post(self, request):
        """Search documents"""
        try:
            data = request.data
            
            if 'query' not in data:
                return Response(
                    {"error": "Missing required field: query"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get collection name from data or use default
            collection_name = data.get('workspace', 'default')
            collection_manager = get_collection_manager(collection_name)
            
            # Parse sources if provided
            sources = None
            if 'sources' in data:
                try:
                    sources = [DataSource(s) for s in data['sources']]
                except ValueError as e:
                    return Response(
                        {"error": f"Invalid source in sources list: {str(e)}"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Search documents
            results = collection_manager.search_across_sources(
                query=data['query'],
                sources=sources,
                limit=data.get('limit', 10),
                similarity_threshold=data.get('similarity_threshold', 0.4),
                workspace=data.get('workspace'),
                content_type=data.get('content_type'),
                tags=data.get('tags'),
                author=data.get('author'),
                metadata_filters=data.get('metadata_filters')
            )
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "id": result.id,
                    "content": result.content,
                    "similarity_score": result.score,
                    "distance": result.distance,
                    "metadata": {
                        "source": result.metadata.source.value,
                        "source_id": result.metadata.source_id,
                        "title": result.metadata.title,
                        "author": result.metadata.author,
                        "workspace": result.metadata.workspace,
                        "tags": result.metadata.tags,
                        "url": result.metadata.url,
                        "content_type": result.metadata.content_type,
                        "created_at": result.metadata.created_at.isoformat() if result.metadata.created_at else None,
                        "updated_at": result.metadata.updated_at.isoformat() if result.metadata.updated_at else None
                    },
                    "chunk_info": {
                        "chunk_index": result.chunk_index,
                        "total_chunks": result.total_chunks
                    }
                })
            
            return Response({
                "query": data['query'],
                "results_count": len(formatted_results),
                "results": formatted_results
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {"error": f"Search failed: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentDeleteView(APIView):
    """
    Delete documents from the vector database
    """
    
    def delete(self, request):
        """Delete a document"""
        try:
            data = request.data
            
            if 'source' not in data or 'document_id' not in data:
                return Response(
                    {"error": "Missing required fields: source, document_id"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate source
            try:
                source = DataSource(data['source'])
            except ValueError:
                return Response(
                    {"error": f"Invalid source. Must be one of: {[s.value for s in DataSource]}"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get collection name from data or use default  
            collection_name = data.get('collection_name', 'default')
            collection_manager = get_collection_manager(collection_name)
            success = collection_manager.delete_document(source, data['document_id'])
            
            if success:
                return Response({
                    "success": True,
                    "message": "Document deleted successfully"
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "success": False,
                    "message": "Document not found or deletion failed"
                }, status=status.HTTP_404_NOT_FOUND)
            
        except Exception as e:
            return Response(
                {"error": f"Delete failed: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )