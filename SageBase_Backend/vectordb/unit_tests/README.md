# Vector Database Embedding Quality Tests

This directory contains comprehensive unit tests for evaluating the quality of embeddings and retrieval performance in the vector database system.

## Overview

The test suite is designed to assess:
- **Semantic similarity detection** - How well embeddings capture semantic relationships
- **Retrieval accuracy** - Precision and recall of search results  
- **Ranking quality** - Whether more relevant documents are ranked higher
- **Cross-domain performance** - How well the system works across different content types
- **Edge case handling** - Behavior with unusual or challenging queries

## Test Files

### `test_embedding_quality.py`
Tests basic embedding functionality and semantic understanding:
- Semantic similarity between related documents
- Filtering by metadata (author, tags, content type)
- Search mode comparison (semantic, textual, hybrid)
- Performance and consistency testing
- Irrelevant content filtering

### `test_retrieval_quality.py`  
Advanced tests for retrieval accuracy and ranking:
- Precision/recall calculation with known relevance
- NDCG (Normalized Discounted Cumulative Gain) ranking metrics
- Query specificity impact on results
- Retrieval diversity assessment
- Cross-domain query performance
- Recall@K evaluation

### `run_tests.py`
Test runner script with options for different test subsets and verbosity levels.

## Usage

### Run All Tests
```bash
cd vectordb/unit_tests
python manage.py test vectordb.unit_tests
```

### Run Specific Test Suite
```bash
# Run only embedding quality tests
python run_tests.py --test embedding

# Run only retrieval quality tests  
python run_tests.py --test retrieval
```

### Verbose Output
```bash
python run_tests.py --verbose
```

### Run Individual Test Files
```bash
# Run embedding tests directly
python -m unittest test_embedding_quality.py

# Run retrieval tests directly
python -m unittest test_retrieval_quality.py
```

## Test Data

All tests use the workspace `"test"` for isolation. The test suite creates a controlled dataset with:

- **Machine Learning cluster** - Documents about deep learning, supervised/unsupervised learning
- **Software Engineering cluster** - TDD, microservices architecture  
- **Data Science cluster** - Data preprocessing, feature engineering
- **DevOps cluster** - CI/CD, Docker/Kubernetes
- **Business/Product cluster** - Product management, customer feedback
- **Unrelated content** - Cooking recipes for negative testing

This allows testing semantic relationships and retrieval accuracy with known relevance mappings.

## Key Metrics Evaluated

### Precision and Recall
- **Precision**: Percentage of retrieved documents that are relevant
- **Recall**: Percentage of relevant documents that were retrieved

### NDCG (Normalized Discounted Cumulative Gain)  
- Measures ranking quality by giving higher weight to relevant documents at top positions
- Values closer to 1.0 indicate better ranking

### Recall@K
- Measures recall when retrieving top-K results
- Should generally increase as K increases

### Search Performance
- Response time should be under 3-5 seconds
- Consistent results across repeated searches

## Expected Results

### Good Embedding Quality Indicators
- **High semantic similarity**: Related documents (e.g., Python and JavaScript programming guides) cluster together
- **Low irrelevant matches**: Cooking recipes don't appear in technical queries
- **Consistent ranking**: More specific documents rank higher for specific queries
- **Cross-modal retrieval**: Can find documents using synonyms or related concepts

### Poor Embedding Quality Indicators  
- **Random clustering**: Unrelated documents appear together
- **Low precision**: Many irrelevant results in top positions
- **Inconsistent ranking**: Results vary significantly between similar queries
- **Poor recall**: Relevant documents not found even with relaxed thresholds

## Troubleshooting

### If Tests Fail
1. **Check vector database health**: Ensure ChromaDB is running and accessible
2. **Verify test data**: Confirm documents were added successfully to "test" workspace
3. **Review embedding model**: Check if the SentenceTransformer model is working properly
4. **Check external API dependencies**: Ensure OpenAI or other embedding APIs are accessible
5. **Examine specific failures**: Look at assertion errors for clues about embedding quality issues

### Common Issues
- **Timeout errors**: Vector database may be slow or overloaded
- **Empty results**: Check if documents were properly indexed
- **API errors**: OpenAI API issues may cause semantic search to fail (tests include fallback to hybrid/textual modes)
- **Low scores**: May indicate poor embedding model or need for parameter tuning
- **Inconsistent results**: Could suggest non-deterministic behavior in embeddings

### API Dependencies
The tests are designed to handle embedding API failures gracefully:
- **Primary**: Semantic search using external embedding API
- **Fallback 1**: Hybrid search (combines semantic + textual)  
- **Fallback 2**: Textual search (keyword-based, no external API required)

If you see errors like "Error code: 400 - {'error': {'message': "'$.input' is invalid", this indicates an issue with the OpenAI API configuration or availability. The tests will automatically fall back to alternative search modes.

## Interpreting Results

### Success Criteria
- **Precision > 0.1**: At least 10% of results should be relevant
- **Recall > 0.3**: Should find at least 30% of relevant documents  
- **NDCG > 0.3**: Reasonable ranking quality
- **Response time < 5s**: Acceptable performance
- **No irrelevant docs in top 3**: Good filtering of unrelated content

### Performance Benchmarks
- **Search time**: < 3 seconds for typical queries
- **Precision**: > 0.2 for well-defined queries
- **Recall**: > 0.5 for broad category searches
- **Consistency**: Same top result across repeated identical searches

## Next Steps

If embedding quality tests reveal issues:

1. **Tune similarity thresholds** - Adjust minimum similarity scores
2. **Experiment with embedding models** - Try different SentenceTransformer models
3. **Improve text preprocessing** - Better tokenization, stop word removal  
4. **Optimize chunk size** - Adjust document chunking for better granularity
5. **Add more training data** - Include domain-specific documents for better embeddings
6. **Implement hybrid approaches** - Combine semantic and keyword-based search

The goal is to achieve consistently high precision and recall while maintaining fast response times across diverse query types.