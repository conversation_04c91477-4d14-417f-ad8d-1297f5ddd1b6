#!/usr/bin/env python3
"""
Test runner for vector database embedding quality tests.

This script runs the embedding quality and retrieval tests,
providing detailed output and summary statistics.

Usage:
    python vectordb/unit_tests/run_tests.py
    python vectordb/unit_tests/run_tests.py --verbose
    python vectordb/unit_tests/run_tests.py --test embedding  # Run only embedding tests
    python vectordb/unit_tests/run_tests.py --test retrieval  # Run only retrieval tests
"""

import sys
import os
import unittest
import argparse
import time
from io import StringIO

# Add the project root to Python path for Django imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')

try:
    import django
    django.setup()
except ImportError:
    print("Warning: Django not available. Some imports may fail.")
except Exception as e:
    print(f"Warning: Django setup failed: {e}")


def run_embedding_tests(verbose=False):
    """Run embedding quality tests."""
    print("=" * 60)
    print("RUNNING EMBEDDING QUALITY TESTS")
    print("=" * 60)
    
    # Import and run embedding tests
    from vectordb.unit_tests.test_embedding_quality import TestEmbeddingQuality
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestEmbeddingQuality)
    
    # Set up test runner
    if verbose:
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    else:
        runner = unittest.TextTestRunner(verbosity=1, stream=sys.stdout)
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    print(f"\nEmbedding tests completed in {end_time - start_time:.2f} seconds")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    return result


def run_retrieval_tests(verbose=False):
    """Run retrieval quality tests."""
    print("=" * 60)
    print("RUNNING RETRIEVAL QUALITY TESTS")
    print("=" * 60)
    
    # Import and run retrieval tests
    from vectordb.unit_tests.test_retrieval_quality import TestRetrievalQuality
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestRetrievalQuality)
    
    # Set up test runner
    if verbose:
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    else:
        runner = unittest.TextTestRunner(verbosity=1, stream=sys.stdout)
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    print(f"\nRetrieval tests completed in {end_time - start_time:.2f} seconds")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    return result


def run_all_tests(verbose=False):
    """Run all vector database quality tests."""
    print("🧪 Vector Database Embedding Quality Test Suite")
    print("=" * 60)
    
    total_start_time = time.time()
    
    # Run embedding tests
    embedding_result = run_embedding_tests(verbose)
    
    print("\n")
    
    # Run retrieval tests  
    retrieval_result = run_retrieval_tests(verbose)
    
    total_end_time = time.time()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUITE SUMMARY")
    print("=" * 60)
    
    total_tests = embedding_result.testsRun + retrieval_result.testsRun
    total_failures = len(embedding_result.failures) + len(retrieval_result.failures)
    total_errors = len(embedding_result.errors) + len(retrieval_result.errors)
    
    print(f"Total execution time: {total_end_time - total_start_time:.2f} seconds")
    print(f"Total tests run: {total_tests}")
    print(f"Total failures: {total_failures}")
    print(f"Total errors: {total_errors}")
    
    success_rate = ((total_tests - total_failures - total_errors) / total_tests * 100) if total_tests > 0 else 0
    print(f"Success rate: {success_rate:.1f}%")
    
    if total_failures > 0 or total_errors > 0:
        print("\n❌ Some tests failed. Check the output above for details.")
        
        # Print failure details
        if embedding_result.failures or embedding_result.errors:
            print("\nEmbedding test issues:")
            for test, traceback in embedding_result.failures + embedding_result.errors:
                print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'Error'}")
        
        if retrieval_result.failures or retrieval_result.errors:
            print("\nRetrieval test issues:")
            for test, traceback in retrieval_result.failures + retrieval_result.errors:
                print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'Error'}")
        
        return False
    else:
        print("\n✅ All tests passed successfully!")
        return True


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description="Run vector database embedding quality tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--test", "-t", choices=["embedding", "retrieval", "all"], 
                       default="all", help="Which tests to run")
    
    args = parser.parse_args()
    
    try:
        if args.test == "embedding":
            result = run_embedding_tests(args.verbose)
            success = len(result.failures) == 0 and len(result.errors) == 0
        elif args.test == "retrieval":
            result = run_retrieval_tests(args.verbose)
            success = len(result.failures) == 0 and len(result.errors) == 0
        else:  # all
            success = run_all_tests(args.verbose)
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\nTest execution interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error running tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()