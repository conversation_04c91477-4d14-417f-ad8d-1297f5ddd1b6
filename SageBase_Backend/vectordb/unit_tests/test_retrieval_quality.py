"""
Advanced unit tests for evaluating retrieval quality and ranking performance.

This test suite focuses on detailed retrieval quality assessment:
1. Precision and recall evaluation
2. Ranking quality metrics
3. Query-document relevance scoring
4. Edge case handling
5. Performance benchmarking

All tests use workspace="test" as specified.
"""

import unittest
import time
import statistics
from typing import List, Dict, Any, Tuple
from collections import defaultdict

# Import the main interface functions
from vectordb.interfaces import (
    add_document, search, delete_documents_in_workspace, 
    get_stats, health_check
)
from vectordb.models.document import DataSource


class TestRetrievalQuality(unittest.TestCase):
    """Advanced test suite for retrieval quality assessment."""
    
    WORKSPACE = "test"
    
    @classmethod
    def setUpClass(cls):
        """Set up comprehensive test dataset for retrieval quality evaluation."""
        # Clean workspace before testing
        delete_documents_in_workspace(collection_name=cls.WORKSPACE)
        time.sleep(1)
        
        # Create a more comprehensive dataset with known relevance relationships
        cls.test_dataset = [
            # Machine Learning cluster (highly related)
            {
                "content": "Deep learning is a subset of machine learning that uses neural networks with multiple layers. These networks can automatically learn hierarchical representations of data, making them particularly effective for image recognition, natural language processing, and speech recognition tasks.",
                "source": DataSource.CONFLUENCE,
                "source_id": "deep_learning_001",
                "title": "Deep Learning Fundamentals",
                "author": "ml_expert",
                "workspace": cls.WORKSPACE,
                "tags": ["deep-learning", "neural-networks", "ai"],
                "content_type": "tutorial",
                "relevance_clusters": ["ml_advanced", "ai_core"]
            },
            {
                "content": "Supervised learning involves training algorithms on labeled data to make predictions on new, unseen data. Common supervised learning algorithms include linear regression, decision trees, random forests, and support vector machines.",
                "source": DataSource.NOTION,
                "source_id": "supervised_learning_002",
                "title": "Supervised Learning Algorithms",
                "author": "ml_expert",
                "workspace": cls.WORKSPACE,
                "tags": ["supervised-learning", "algorithms", "machine-learning"],
                "content_type": "tutorial",
                "relevance_clusters": ["ml_basics", "algorithms"]
            },
            {
                "content": "Unsupervised learning discovers hidden patterns in data without labeled examples. Clustering algorithms like K-means and hierarchical clustering group similar data points, while dimensionality reduction techniques like PCA help visualize high-dimensional data.",
                "source": DataSource.NOTION,
                "source_id": "unsupervised_learning_003",
                "title": "Unsupervised Learning Techniques",
                "author": "ml_expert",
                "workspace": cls.WORKSPACE,
                "tags": ["unsupervised-learning", "clustering", "pca"],
                "content_type": "tutorial",
                "relevance_clusters": ["ml_basics", "data_analysis"]
            },
            
            # Software Engineering cluster (moderately related)
            {
                "content": "Test-driven development (TDD) is a software development approach where tests are written before the actual code. This practice ensures better code quality, reduces bugs, and makes refactoring safer by providing a safety net of automated tests.",
                "source": DataSource.CONFLUENCE,
                "source_id": "tdd_practices_004",
                "title": "Test-Driven Development Best Practices",
                "author": "senior_dev",
                "workspace": cls.WORKSPACE,
                "tags": ["tdd", "testing", "development"],
                "content_type": "best-practices",
                "relevance_clusters": ["software_eng", "testing"]
            },
            {
                "content": "Microservices architecture breaks down large applications into smaller, independent services that communicate through APIs. Each microservice can be developed, deployed, and scaled independently, improving system resilience and development velocity.",
                "source": DataSource.CONFLUENCE,
                "source_id": "microservices_005",
                "title": "Microservices Architecture Guide",
                "author": "architect",
                "workspace": cls.WORKSPACE,
                "tags": ["microservices", "architecture", "distributed-systems"],
                "content_type": "architecture",
                "relevance_clusters": ["software_eng", "architecture"]
            },
            
            # Data Science cluster (related to ML but distinct focus)
            {
                "content": "Data preprocessing is crucial for successful machine learning projects. This includes data cleaning, handling missing values, feature scaling, and encoding categorical variables. Poor data quality can significantly impact model performance.",
                "source": DataSource.NOTION,
                "source_id": "data_preprocessing_006",
                "title": "Data Preprocessing for ML",
                "author": "data_scientist",
                "workspace": cls.WORKSPACE,
                "tags": ["data-preprocessing", "data-cleaning", "feature-engineering"],
                "content_type": "tutorial",
                "relevance_clusters": ["data_science", "ml_basics"]
            },
            {
                "content": "Feature engineering involves creating new features from existing data to improve model performance. Techniques include polynomial features, interaction terms, binning continuous variables, and domain-specific transformations.",
                "source": DataSource.NOTION,
                "source_id": "feature_engineering_007",
                "title": "Advanced Feature Engineering",
                "author": "data_scientist",
                "workspace": cls.WORKSPACE,
                "tags": ["feature-engineering", "model-performance", "data-science"],
                "content_type": "advanced",
                "relevance_clusters": ["data_science", "ml_advanced"]
            },
            
            # DevOps cluster (distantly related)
            {
                "content": "Continuous Integration and Continuous Deployment (CI/CD) automates the software delivery process. Automated testing, building, and deployment pipelines ensure code quality and enable rapid, reliable releases.",
                "source": DataSource.CONFLUENCE,
                "source_id": "cicd_pipeline_008",
                "title": "CI/CD Pipeline Implementation",
                "author": "devops_engineer",
                "workspace": cls.WORKSPACE,
                "tags": ["cicd", "automation", "deployment"],
                "content_type": "implementation",
                "relevance_clusters": ["devops", "automation"]
            },
            {
                "content": "Docker containerization packages applications with their dependencies into lightweight, portable containers. Container orchestration with Kubernetes manages deployment, scaling, and operation of containerized applications.",
                "source": DataSource.CONFLUENCE,
                "source_id": "docker_kubernetes_009",
                "title": "Docker and Kubernetes Guide",
                "author": "devops_engineer",
                "workspace": cls.WORKSPACE,
                "tags": ["docker", "kubernetes", "containerization"],
                "content_type": "guide",
                "relevance_clusters": ["devops", "infrastructure"]
            },
            
            # Business/Product cluster (unrelated to technical content)
            {
                "content": "Product roadmap planning involves prioritizing features based on customer needs, business value, and technical feasibility. Regular stakeholder communication and iterative planning help align development with business objectives.",
                "source": DataSource.CONFLUENCE,
                "source_id": "product_roadmap_010",
                "title": "Product Roadmap Planning",
                "author": "product_manager",
                "workspace": cls.WORKSPACE,
                "tags": ["product-management", "roadmap", "planning"],
                "content_type": "process",
                "relevance_clusters": ["business", "product"]
            },
            {
                "content": "Customer feedback collection through surveys, interviews, and analytics helps understand user needs and pain points. This feedback drives product decisions and feature prioritization for better user experience.",
                "source": DataSource.LOCAL,
                "source_id": "customer_feedback_011",
                "title": "Customer Feedback Collection",
                "author": "product_manager",
                "workspace": cls.WORKSPACE,
                "tags": ["customer-feedback", "user-research", "product"],
                "content_type": "process",
                "relevance_clusters": ["business", "research"]
            },
            
            # Completely unrelated content for negative testing
            {
                "content": "Italian cuisine features fresh ingredients like tomatoes, basil, olive oil, and garlic. Traditional pasta dishes include carbonara, bolognese, and pesto. Regional variations showcase different ingredients and cooking techniques.",
                "source": DataSource.LOCAL,
                "source_id": "italian_cuisine_012",
                "title": "Italian Cooking Traditions",
                "author": "chef",
                "workspace": cls.WORKSPACE,
                "tags": ["cooking", "italian", "cuisine"],
                "content_type": "recipe",
                "relevance_clusters": ["food", "culture"]
            }
        ]
        
        # Define expected relevance relationships for testing
        cls.relevance_map = {
            "machine learning": {
                "highly_relevant": ["deep_learning_001", "supervised_learning_002", "unsupervised_learning_003"],
                "moderately_relevant": ["data_preprocessing_006", "feature_engineering_007"],
                "low_relevant": ["tdd_practices_004", "microservices_005"],
                "irrelevant": ["product_roadmap_010", "customer_feedback_011", "italian_cuisine_012"]
            },
            "neural networks": {
                "highly_relevant": ["deep_learning_001"],
                "moderately_relevant": ["supervised_learning_002", "feature_engineering_007"],
                "low_relevant": ["data_preprocessing_006", "unsupervised_learning_003"],
                "irrelevant": ["tdd_practices_004", "cicd_pipeline_008", "italian_cuisine_012"]
            },
            "software testing": {
                "highly_relevant": ["tdd_practices_004"],
                "moderately_relevant": ["cicd_pipeline_008"],
                "low_relevant": ["microservices_005"],
                "irrelevant": ["deep_learning_001", "product_roadmap_010", "italian_cuisine_012"]
            },
            "data preprocessing": {
                "highly_relevant": ["data_preprocessing_006"],
                "moderately_relevant": ["feature_engineering_007", "supervised_learning_002"],
                "low_relevant": ["unsupervised_learning_003", "deep_learning_001"],
                "irrelevant": ["docker_kubernetes_009", "italian_cuisine_012"]
            }
        }
        
        # Add all test documents
        for doc in cls.test_dataset:
            # Remove relevance_clusters before adding (not part of the interface)
            doc_copy = {k: v for k, v in doc.items() if k != "relevance_clusters"}
            add_document(**doc_copy, collection_name=cls.WORKSPACE)
        
        # Wait for indexing to complete
        time.sleep(3)
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test data after all tests."""
        delete_documents_in_workspace(collection_name=cls.WORKSPACE)
    
    def calculate_precision_recall(self, query: str, results: List[Any], expected_relevant: List[str]) -> Tuple[float, float]:
        """Calculate precision and recall for a given query and results."""
        if not results:
            return 0.0, 0.0
        
        retrieved_ids = [r.metadata.source_id for r in results]
        relevant_retrieved = len(set(retrieved_ids).intersection(set(expected_relevant)))
        
        precision = relevant_retrieved / len(retrieved_ids) if retrieved_ids else 0.0
        recall = relevant_retrieved / len(expected_relevant) if expected_relevant else 0.0
        
        return precision, recall
    
    def calculate_ndcg(self, results: List[Any], relevance_scores: Dict[str, float]) -> float:
        """Calculate Normalized Discounted Cumulative Gain (NDCG)."""
        if not results:
            return 0.0
        
        # Calculate DCG
        dcg = 0.0
        for i, result in enumerate(results):
            relevance = relevance_scores.get(result.metadata.source_id, 0.0)
            dcg += relevance / (1.0 + i)  # Simple DCG formula
        
        # Calculate ideal DCG
        ideal_relevances = sorted(relevance_scores.values(), reverse=True)
        idcg = sum(rel / (1.0 + i) for i, rel in enumerate(ideal_relevances))
        
        return dcg / idcg if idcg > 0 else 0.0
    
    def test_precision_recall_machine_learning(self):
        """Test precision and recall for machine learning queries."""
        query = "machine learning algorithms and techniques"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=10,
            search_mode="semantic"
        )
        
        expected_relevant = self.relevance_map["machine learning"]["highly_relevant"]
        precision, recall = self.calculate_precision_recall(query, results, expected_relevant)
        
        # Should achieve reasonable precision and recall
        self.assertGreater(precision, 0.1, f"Precision too low: {precision:.3f}")
        self.assertGreater(recall, 0.3, f"Recall too low: {recall:.3f}")
        
        print(f"ML Query - Precision: {precision:.3f}, Recall: {recall:.3f}")
    
    def test_precision_recall_neural_networks(self):
        """Test precision and recall for specific neural networks query."""
        query = "neural networks deep learning"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=10,
            search_mode="semantic"
        )
        
        expected_relevant = self.relevance_map["neural networks"]["highly_relevant"]
        precision, recall = self.calculate_precision_recall(query, results, expected_relevant)
        
        # Should achieve high precision for specific query
        self.assertGreater(precision, 0.1, f"Precision too low: {precision:.3f}")
        self.assertGreater(recall, 0.5, f"Recall too low: {recall:.3f}")
        
        print(f"Neural Networks Query - Precision: {precision:.3f}, Recall: {recall:.3f}")
    
    def test_ranking_quality_relevance_order(self):
        """Test that more relevant documents are ranked higher."""
        query = "machine learning"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=8,
            search_mode="semantic"
        )
        
        highly_relevant = self.relevance_map["machine learning"]["highly_relevant"]
        moderately_relevant = self.relevance_map["machine learning"]["moderately_relevant"]
        
        # Find positions of highly and moderately relevant docs
        highly_relevant_positions = []
        moderately_relevant_positions = []
        
        for i, result in enumerate(results):
            if result.metadata.source_id in highly_relevant:
                highly_relevant_positions.append(i)
            elif result.metadata.source_id in moderately_relevant:
                moderately_relevant_positions.append(i)
        
        # Highly relevant docs should generally appear before moderately relevant ones
        if highly_relevant_positions and moderately_relevant_positions:
            avg_highly_pos = statistics.mean(highly_relevant_positions)
            avg_moderately_pos = statistics.mean(moderately_relevant_positions)
            
            self.assertLess(avg_highly_pos, avg_moderately_pos,
                           f"Highly relevant docs should rank higher. Highly: {avg_highly_pos:.1f}, Moderately: {avg_moderately_pos:.1f}")
    
    def test_irrelevant_content_exclusion(self):
        """Test that completely irrelevant content is not highly ranked."""
        query = "machine learning algorithms"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=10,
            search_mode="semantic"
        )
        
        irrelevant_docs = self.relevance_map["machine learning"]["irrelevant"]
        
        # Check if irrelevant docs appear in top 3 results
        top_3_ids = [r.metadata.source_id for r in results[:3]]
        irrelevant_in_top_3 = len(set(top_3_ids).intersection(set(irrelevant_docs)))
        
        self.assertEqual(irrelevant_in_top_3, 0,
                        f"Irrelevant docs found in top 3: {set(top_3_ids).intersection(set(irrelevant_docs))}")
    
    def test_ndcg_ranking_quality(self):
        """Test ranking quality using NDCG metric."""
        query = "machine learning"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=8,
            search_mode="semantic"
        )
        
        # Assign relevance scores
        relevance_scores = {}
        for doc_id in self.relevance_map["machine learning"]["highly_relevant"]:
            relevance_scores[doc_id] = 3.0
        for doc_id in self.relevance_map["machine learning"]["moderately_relevant"]:
            relevance_scores[doc_id] = 2.0
        for doc_id in self.relevance_map["machine learning"]["low_relevant"]:
            relevance_scores[doc_id] = 1.0
        for doc_id in self.relevance_map["machine learning"]["irrelevant"]:
            relevance_scores[doc_id] = 0.0
        
        ndcg = self.calculate_ndcg(results, relevance_scores)
        
        # NDCG should be reasonably high (> 0.3 for a decent ranking)
        self.assertGreater(ndcg, 0.3, f"NDCG too low: {ndcg:.3f}")
        
        print(f"ML Query NDCG: {ndcg:.3f}")
    
    def test_query_specificity_impact(self):
        """Test that more specific queries return more precise results."""
        broad_query = "learning"
        specific_query = "deep learning neural networks"
        
        broad_results = search(
            query=broad_query,
            collection_name=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        specific_results = search(
            query=specific_query,
            collection_name=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        # Specific query should find the deep learning document more prominently
        specific_top_ids = [r.metadata.source_id for r in specific_results[:2]]
        self.assertIn("deep_learning_001", specific_top_ids,
                     f"Deep learning doc should be top result for specific query. Got: {specific_top_ids}")
    
    def test_search_mode_comparison(self):
        """Compare different search modes for the same query."""
        query = "test driven development"
        
        semantic_results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        textual_results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=5,
            search_mode="textual"
        )
        
        hybrid_results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=5,
            search_mode="hybrid"
        )
        
        # TDD document should be found by all methods
        tdd_doc_id = "tdd_practices_004"
        
        semantic_found = any(r.metadata.source_id == tdd_doc_id for r in semantic_results)
        textual_found = any(r.metadata.source_id == tdd_doc_id for r in textual_results)
        hybrid_found = any(r.metadata.source_id == tdd_doc_id for r in hybrid_results)
        
        # At least hybrid should find it
        self.assertTrue(hybrid_found, "Hybrid search should find TDD document")
        
        print(f"TDD Query - Semantic: {semantic_found}, Textual: {textual_found}, Hybrid: {hybrid_found}")
    
    def test_score_consistency(self):
        """Test that similarity scores are consistent and meaningful."""
        query = "deep learning neural networks"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=10,
            search_mode="semantic"
        )
        
        if len(results) > 1:
            # Scores should be in descending order
            for i in range(len(results) - 1):
                self.assertGreaterEqual(results[i].score, results[i + 1].score,
                                      f"Scores not in descending order at position {i}")
            
            # Top result should have reasonable score
            self.assertGreater(results[0].score, 0.1,
                             f"Top result score too low: {results[0].score:.3f}")
    
    def test_retrieval_diversity(self):
        """Test that results include diverse relevant content, not just duplicates."""
        query = "machine learning"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=8,
            search_mode="semantic"
        )
        
        # Check diversity by counting unique authors and content types
        authors = set(r.metadata.author for r in results if r.metadata.author)
        content_types = set(r.metadata.content_type for r in results if r.metadata.content_type)
        
        self.assertGreater(len(authors), 1, "Results should include diverse authors")
        
        print(f"ML Query Diversity - Authors: <AUTHORS>
    
    def test_filter_combination_quality(self):
        """Test retrieval quality when combining multiple filters."""
        query = "machine learning"
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            author="ml_expert",
            content_type="tutorial",
            limit=5
        )
        
        # All results should match the filters
        for result in results:
            self.assertEqual(result.metadata.author, "ml_expert",
                           f"Author filter failed: {result.metadata.author}")
            self.assertEqual(result.metadata.content_type, "tutorial",
                           f"Content type filter failed: {result.metadata.content_type}")
        
        # Should still find relevant ML content
        ml_relevant_docs = self.relevance_map["machine learning"]["highly_relevant"]
        found_relevant = any(r.metadata.source_id in ml_relevant_docs for r in results)
        self.assertTrue(found_relevant, "Should find relevant ML docs even with filters")
    
    def test_empty_and_edge_case_queries(self):
        """Test behavior with edge case queries."""
        test_cases = [
            ("", "empty query"),
            ("a", "single character"),
            ("xyz123nonexistent", "nonexistent terms"),
            ("the and or", "stop words only")
        ]
        
        for query, description in test_cases:
            with self.subTest(query=description):
                results = search(
                    query=query,
                    collection_name=self.WORKSPACE,
                    limit=5
                )
                
                # Should handle gracefully without errors
                self.assertIsInstance(results, list, f"Should return list for {description}")
    
    def test_performance_benchmarks(self):
        """Test retrieval performance benchmarks."""
        query = "machine learning algorithms deep learning"
        
        # Measure search time
        start_time = time.time()
        results = search(
            query=query,
            collection_name=self.WORKSPACE,
            limit=10,
            search_mode="semantic"
        )
        end_time = time.time()
        
        search_time = end_time - start_time
        
        # Performance benchmarks
        self.assertLess(search_time, 3.0, f"Search too slow: {search_time:.2f}s")
        self.assertGreater(len(results), 0, "Should return results within time limit")
        
        print(f"Search Performance: {search_time:.3f}s for {len(results)} results")
    
    def test_recall_at_k(self):
        """Test recall at different k values (top-k retrieval)."""
        query = "machine learning"
        relevant_docs = self.relevance_map["machine learning"]["highly_relevant"]
        
        k_values = [3, 5, 8, 10]
        recalls = []
        
        for k in k_values:
            results = search(
                query=query,
                collection_name=self.WORKSPACE,
                limit=k,
                search_mode="semantic"
            )
            
            _, recall = self.calculate_precision_recall(query, results, relevant_docs)
            recalls.append(recall)
            
            print(f"Recall@{k}: {recall:.3f}")
        
        # Recall should generally increase or stay same as k increases
        for i in range(1, len(recalls)):
            self.assertGreaterEqual(recalls[i], recalls[i-1] - 0.1,  # Allow small decrease due to noise
                                  f"Recall should not decrease significantly with higher k")
    
    def test_cross_domain_retrieval(self):
        """Test retrieval quality across different domains."""
        domain_queries = [
            ("software testing methodologies", "software engineering"),
            ("data preprocessing techniques", "data science"),
            ("container orchestration", "devops"),
            ("product roadmap planning", "business")
        ]
        
        for query, domain in domain_queries:
            with self.subTest(domain=domain):
                results = search(
                    query=query,
                    collection_name=self.WORKSPACE,
                    limit=5,
                    search_mode="semantic"
                )
                
                self.assertGreater(len(results), 0, f"Should find results for {domain} query")
                
                # Check if top result is from expected domain by checking tags/content
                if results:
                    top_result = results[0]
                    print(f"{domain} query top result: {top_result.metadata.title}")


if __name__ == '__main__':
    unittest.main()