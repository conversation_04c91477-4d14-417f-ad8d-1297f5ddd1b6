"""
Unit tests for evaluating embedding quality and retrieval performance.

This test suite focuses on assessing the quality of embeddings by testing:
1. Semantic similarity detection
2. Retrieval accuracy for different query types
3. Performance across different content types
4. Cross-language and domain transfer capabilities

All tests use workspace="test" as specified.
"""

import unittest
import time
from typing import List, Dict, Any
from datetime import datetime

# Import the main interface functions
from vectordb.interfaces import (
    get_chroma_collection, delete_documents_in_workspace, 
    get_stats, health_check
)
from vectordb.models.document import DataSource


class TestEmbeddingQuality(unittest.TestCase):
    """Test suite for evaluating embedding quality and retrieval performance."""
    
    WORKSPACE = "test"
    
    def search_with_fallback(self, query, **kwargs):
        """Search with fallback from semantic to hybrid if API fails."""
        # Default to semantic mode
        search_mode = kwargs.get('search_mode', 'semantic')
        
        # Get collection manager for the test workspace
        collection = get_chroma_collection(self.WORKSPACE)
        results = collection.search_across_sources(query=query, **kwargs)
        
        # If semantic search fails and returns no results, try hybrid
        if not results and search_mode == 'semantic':
            kwargs['search_mode'] = 'hybrid'
            results = collection.search_across_sources(query=query, **kwargs)
            
        # If hybrid also fails, try textual
        if not results and search_mode in ['semantic', 'hybrid']:
            kwargs['search_mode'] = 'textual'
            results = collection.search_across_sources(query=query, **kwargs)
        
        return results
    
    @classmethod
    def setUpClass(cls):
        """Set up test data once for all tests."""
        # Clean workspace before testing
        delete_documents_in_workspace(cls.WORKSPACE)
        
        # Wait a moment for deletion to complete
        time.sleep(1)
        
        # Test documents with known semantic relationships
        cls.test_documents = [
            # Technical documentation cluster
            {
                "content": "Python is a high-level programming language known for its simplicity and readability. It supports multiple programming paradigms including object-oriented, functional, and procedural programming.",
                "source": DataSource.CONFLUENCE,
                "source_id": "python_intro_001",
                "title": "Introduction to Python Programming",
                "author": "tech_writer",
                "workspace": cls.WORKSPACE,
                "tags": ["python", "programming", "tutorial"],
                "content_type": "documentation"
            },
            {
                "content": "JavaScript is a versatile programming language primarily used for web development. It runs in browsers and on servers through Node.js, enabling full-stack development.",
                "source": DataSource.CONFLUENCE,
                "source_id": "javascript_intro_002",
                "title": "JavaScript Fundamentals",
                "author": "tech_writer",
                "workspace": cls.WORKSPACE,
                "tags": ["javascript", "programming", "web"],
                "content_type": "documentation"
            },
            {
                "content": "Machine learning algorithms enable computers to learn patterns from data without explicit programming. Popular techniques include supervised learning, unsupervised learning, and reinforcement learning.",
                "source": DataSource.NOTION,
                "source_id": "ml_basics_003",
                "title": "Machine Learning Basics",
                "author": "data_scientist",
                "workspace": cls.WORKSPACE,
                "tags": ["machine-learning", "ai", "algorithms"],
                "content_type": "research"
            },
            # Business process cluster
            {
                "content": "Agile methodology emphasizes iterative development, collaboration, and customer feedback. Scrum and Kanban are popular agile frameworks used in software development teams.",
                "source": DataSource.CONFLUENCE,
                "source_id": "agile_process_004",
                "title": "Agile Development Process",
                "author": "project_manager",
                "workspace": cls.WORKSPACE,
                "tags": ["agile", "scrum", "process"],
                "content_type": "process"
            },
            {
                "content": "Code review is a systematic examination of source code intended to find bugs and improve code quality. Best practices include reviewing small changes frequently and providing constructive feedback.",
                "source": DataSource.CONFLUENCE,
                "source_id": "code_review_005",
                "title": "Code Review Best Practices",
                "author": "senior_dev",
                "workspace": cls.WORKSPACE,
                "tags": ["code-review", "quality", "best-practices"],
                "content_type": "guidelines"
            },
            # API documentation cluster
            {
                "content": "REST API design follows principles of representational state transfer. Use HTTP methods (GET, POST, PUT, DELETE) appropriately and return meaningful status codes for different operations.",
                "source": DataSource.CONFLUENCE,
                "source_id": "rest_api_006",
                "title": "REST API Design Guidelines",
                "author": "api_architect",
                "workspace": cls.WORKSPACE,
                "tags": ["api", "rest", "design"],
                "content_type": "guidelines"
            },
            {
                "content": "GraphQL is a query language for APIs that allows clients to request exactly the data they need. It provides a single endpoint and strong typing system for better developer experience.",
                "source": DataSource.CONFLUENCE,
                "source_id": "graphql_intro_007",
                "title": "GraphQL Introduction",
                "author": "api_architect",
                "workspace": cls.WORKSPACE,
                "tags": ["graphql", "api", "query-language"],
                "content_type": "documentation"
            },
            # Testing and QA cluster
            {
                "content": "Unit testing involves testing individual components or modules in isolation. Write tests that are fast, reliable, and independent. Use mocks to isolate the unit under test from external dependencies.",
                "source": DataSource.CONFLUENCE,
                "source_id": "unit_testing_008",
                "title": "Unit Testing Guidelines",
                "author": "qa_engineer",
                "workspace": cls.WORKSPACE,
                "tags": ["testing", "unit-tests", "quality"],
                "content_type": "guidelines"
            },
            {
                "content": "Integration testing verifies that different modules or services work together correctly. Test the interfaces between components and data flow across system boundaries.",
                "source": DataSource.CONFLUENCE,
                "source_id": "integration_testing_009",
                "title": "Integration Testing Strategies",
                "author": "qa_engineer",
                "workspace": cls.WORKSPACE,
                "tags": ["testing", "integration", "quality"],
                "content_type": "guidelines"
            },
            # Unrelated content for negative testing
            {
                "content": "Cooking pasta requires boiling water with salt, adding pasta when water reaches rolling boil, and cooking for the time specified on package. Drain and serve with your favorite sauce.",
                "source": DataSource.LOCAL,
                "source_id": "pasta_recipe_011",
                "title": "How to Cook Perfect Pasta",
                "author": "chef",
                "workspace": cls.WORKSPACE,
                "tags": ["cooking", "pasta", "recipe"],
                "content_type": "recipe"
            }
        ]
        
        # Add all test documents to the test workspace
        collection = get_chroma_collection(cls.WORKSPACE)
        for doc in cls.test_documents:
            collection.add_document(**doc)
        
        # Wait for indexing to complete
        time.sleep(2)
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test data after all tests."""
        delete_documents_in_workspace(cls.WORKSPACE)
    
    def test_health_check_before_tests(self):
        """Ensure the vector database is healthy before running embedding tests."""
        health = health_check()
        self.assertEqual(health.get("overall_status"), "healthy", 
                        f"Vector database not healthy: {health}")
    
    def test_semantic_similarity_programming_languages(self):
        """Test that programming language documents are semantically similar."""
        # Try semantic search first, fallback to hybrid if it fails
        results = search(
            query="programming language for beginners",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        # If semantic search returns no results (API issue), try hybrid
        if not results:
            results = search(
                query="programming language for beginners",
                workspace=self.WORKSPACE,
                limit=5,
                search_mode="hybrid"
            )
        
        self.assertGreater(len(results), 0, "Should find at least one result")
        
        # Should find Python and JavaScript docs in top results
        found_sources = [r.metadata.source_id for r in results[:3]]
        programming_docs = [doc["source_id"] for doc in self.test_documents 
                          if any(tag in ["python", "javascript", "programming"] 
                                for tag in doc["tags"])]
        
        overlap = len(set(found_sources).intersection(set(programming_docs)))
        self.assertGreater(overlap, 0, 
                          f"Should find programming docs in top results. Found: {found_sources}")
    
    def test_semantic_similarity_api_design(self):
        """Test that API-related documents cluster together."""
        results = search(
            query="API design and development",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        # If semantic search fails, try hybrid
        if not results:
            results = search(
                query="API design and development",
                workspace=self.WORKSPACE,
                limit=5,
                search_mode="hybrid"
            )
        
        self.assertGreater(len(results), 0, "Should find at least one result")
        
        # Should find REST and GraphQL docs
        found_titles = [r.metadata.title for r in results[:3]]
        api_keywords = ["REST", "GraphQL", "API"]
        
        found_api_docs = sum(1 for title in found_titles 
                           if any(keyword in title for keyword in api_keywords))
        
        self.assertGreater(found_api_docs, 0, 
                          f"Should find API-related docs. Found titles: {found_titles}")
    
    def test_semantic_similarity_testing_concepts(self):
        """Test that testing-related documents are grouped together."""
        results = search(
            query="software testing methodologies",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        # If semantic search fails, try hybrid
        if not results:
            results = search(
                query="software testing methodologies",
                workspace=self.WORKSPACE,
                limit=5,
                search_mode="hybrid"
            )
        
        self.assertGreater(len(results), 0, "Should find at least one result")
        
        # Should find unit testing and integration testing docs
        found_sources = [r.metadata.source_id for r in results[:3]]
        testing_docs = ["unit_testing_008", "integration_testing_009"]
        
        overlap = len(set(found_sources).intersection(set(testing_docs)))
        self.assertGreater(overlap, 0, 
                          f"Should find testing docs. Found: {found_sources}")
    
    def test_irrelevant_content_filtering(self):
        """Test that unrelated content (cooking) doesn't appear in tech queries."""
        results = search(
            query="software development best practices",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        # If semantic search fails, try hybrid
        if not results:
            results = search(
                query="software development best practices",
                workspace=self.WORKSPACE,
                limit=5,
                search_mode="hybrid"
            )
        
        # Should find some results
        self.assertGreater(len(results), 0, "Should find at least one result")
        
        # Cooking recipe should not be in top results for tech query
        found_sources = [r.metadata.source_id for r in results[:3]]
        self.assertNotIn("pasta_recipe_011", found_sources,
                        f"Cooking recipe should not appear in tech query results: {found_sources}")
    
    def test_textual_search_exact_matches(self):
        """Test textual search for exact keyword matches."""
        results = search(
            query="GraphQL",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="textual"
        )
        
        self.assertGreater(len(results), 0, "Should find GraphQL document")
        
        # First result should be the GraphQL document
        self.assertEqual(results[0].metadata.source_id, "graphql_intro_007",
                        f"Expected GraphQL doc first, got: {results[0].metadata.title}")
    
    def test_hybrid_search_performance(self):
        """Test that hybrid search combines semantic and textual effectively."""
        # Search for a specific term that appears in content
        results_hybrid = search(
            query="Python programming",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="hybrid"
        )
        
        results_semantic = search(
            query="Python programming",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        results_textual = search(
            query="Python programming",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="textual"
        )
        
        # Hybrid should find results
        self.assertGreater(len(results_hybrid), 0, "Hybrid search should find results")
        
        # Python document should be highly ranked in all modes
        python_found_hybrid = any(r.metadata.source_id == "python_intro_001" 
                                for r in results_hybrid[:2])
        self.assertTrue(python_found_hybrid, "Python doc should be top result in hybrid search")
    
    def test_search_with_boosting(self):
        """Test that boosting parameters affect result ranking."""
        # Search with textual boost
        results_textual_boost = search(
            query="Python programming language",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="hybrid",
            textual_boost=2.0,
            semantic_boost=0.5
        )
        
        # Search with semantic boost
        results_semantic_boost = search(
            query="Python programming language",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="hybrid",
            textual_boost=0.5,
            semantic_boost=2.0
        )
        
        self.assertGreater(len(results_textual_boost), 0, "Should find results with textual boost")
        self.assertGreater(len(results_semantic_boost), 0, "Should find results with semantic boost")
        
        # Results may differ based on boosting
        textual_top = results_textual_boost[0].metadata.source_id if results_textual_boost else None
        semantic_top = results_semantic_boost[0].metadata.source_id if results_semantic_boost else None
        
        # At least one should find the Python document
        python_in_results = textual_top == "python_intro_001" or semantic_top == "python_intro_001"
        self.assertTrue(python_in_results, 
                       f"Python doc should be found with boosting. Textual: {textual_top}, Semantic: {semantic_top}")
    
    def test_author_based_retrieval(self):
        """Test retrieval filtering by author."""
        results = search(
            query="development",
            workspace=self.WORKSPACE,
            author="qa_engineer",
            limit=5
        )
        
        # All results should be from qa_engineer
        for result in results:
            self.assertEqual(result.metadata.author, "qa_engineer",
                           f"Expected qa_engineer, got: {result.metadata.author}")
    
    def test_tag_based_retrieval(self):
        """Test retrieval filtering by tags."""
        results = search(
            query="software",
            workspace=self.WORKSPACE,
            tags=["testing"],
            limit=5
        )
        
        # All results should have 'testing' tag
        for result in results:
            self.assertIn("testing", result.metadata.tags,
                         f"Expected 'testing' tag in: {result.metadata.tags}")
    
    def test_content_type_filtering(self):
        """Test retrieval filtering by content type."""
        results = search(
            query="guide",
            workspace=self.WORKSPACE,
            content_type="guidelines",
            limit=5
        )
        
        # All results should be guidelines
        for result in results:
            self.assertEqual(result.metadata.content_type, "guidelines",
                           f"Expected guidelines, got: {result.metadata.content_type}")
    
    def test_similarity_threshold_filtering(self):
        """Test that similarity threshold effectively filters results."""
        # High threshold should return fewer, more relevant results
        results_high_threshold = search(
            query="machine learning algorithms",
            workspace=self.WORKSPACE,
            similarity_threshold=0.8,
            limit=10
        )
        
        # Low threshold should return more results
        results_low_threshold = search(
            query="machine learning algorithms",
            workspace=self.WORKSPACE,
            similarity_threshold=0.1,
            limit=10
        )
        
        # High threshold should return fewer results
        self.assertLessEqual(len(results_high_threshold), len(results_low_threshold),
                           "High threshold should return fewer results")
        
        # All high threshold results should have good scores
        for result in results_high_threshold:
            self.assertGreaterEqual(result.score, 0.8,
                                  f"Result score {result.score} below threshold 0.8")
    
    def test_cross_source_search(self):
        """Test searching across different data sources."""
        # Search in specific sources
        confluence_results = search(
            query="programming",
            workspace=self.WORKSPACE,
            sources=[DataSource.CONFLUENCE],
            limit=5
        )
        
        notion_results = search(
            query="machine learning",
            workspace=self.WORKSPACE,
            sources=[DataSource.NOTION],
            limit=5
        )
        
        # Results should only come from specified sources
        for result in confluence_results:
            self.assertEqual(result.metadata.source, DataSource.CONFLUENCE,
                           f"Expected Confluence source, got: {result.metadata.source}")
        
        for result in notion_results:
            self.assertEqual(result.metadata.source, DataSource.NOTION,
                           f"Expected Notion source, got: {result.metadata.source}")
    
    def test_empty_query_handling(self):
        """Test behavior with empty or very short queries."""
        results = search(
            query="",
            workspace=self.WORKSPACE,
            limit=5
        )
        
        # Should handle empty query gracefully
        self.assertIsInstance(results, list, "Should return list for empty query")
    
    def test_very_specific_query(self):
        """Test retrieval with very specific technical queries."""
        results = search(
            query="HTTP methods GET POST PUT DELETE status codes",
            workspace=self.WORKSPACE,
            limit=5,
            search_mode="semantic"
        )
        
        # Should find the REST API document
        rest_found = any(r.metadata.source_id == "rest_api_006" for r in results[:3])
        self.assertTrue(rest_found, 
                       f"Should find REST API doc for HTTP methods query. Found: {[r.metadata.title for r in results[:3]]}")
    
    def test_embedding_consistency(self):
        """Test that repeated searches return consistent results."""
        query = "programming language tutorial"
        
        # Run same search multiple times
        collection = get_chroma_collection(self.WORKSPACE)
        results1 = collection.search_across_sources(query=query, limit=3, search_mode="semantic")
        time.sleep(0.1)  # Small delay
        results2 = collection.search_across_sources(query=query, limit=3, search_mode="semantic")
        
        # Results should be consistent (same order for top results)
        if results1 and results2:
            self.assertEqual(results1[0].metadata.source_id, results2[0].metadata.source_id,
                           "Top result should be consistent across searches")
    
    def test_retrieval_speed_performance(self):
        """Test that search operations complete within reasonable time."""
        start_time = time.time()
        
        results = search(
            query="software development best practices and methodologies",
            workspace=self.WORKSPACE,
            limit=10
        )
        
        end_time = time.time()
        search_time = end_time - start_time
        
        # Search should complete within 5 seconds
        self.assertLess(search_time, 5.0, 
                       f"Search took too long: {search_time:.2f} seconds")
        
        # Should still return results
        self.assertGreater(len(results), 0, "Should return results within time limit")


if __name__ == '__main__':
    unittest.main()