"""
Vector database unit tests package.

This package contains comprehensive unit tests for evaluating the quality 
of embeddings and retrieval performance in the vector database system.

Test modules:
- test_embedding_quality.py: Tests for semantic similarity and embedding quality
- test_retrieval_quality.py: Advanced tests for retrieval accuracy and ranking

All tests use workspace="test" for isolation.
"""