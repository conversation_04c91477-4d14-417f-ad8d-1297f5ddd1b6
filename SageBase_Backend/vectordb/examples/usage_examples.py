"""
Example usage of the ChromaDB infrastructure for SageBase
This file demonstrates how to use the vector database for different data sources
"""

import os
import sys
from datetime import datetime
from typing import List, Dict, Any

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from vectordb.models.document import DataSource, DocumentMetadata
from vectordb.interfaces import get_chroma_collection
from vectordb.services.chroma_service import get_chroma_service


class VectorDBExamples:
    """
    Example class demonstrating vector database usage
    """
    
    def __init__(self, collection_name="example"):
        self.collection_manager = get_chroma_collection(collection_name)
        self.chroma_service = get_chroma_service()
    
    def example_1_basic_document_operations(self):
        """
        Example 1: Basic document operations - add, search, delete
        """
        print("=== Example 1: Basic Document Operations ===")
        
        # Add a document from Confluence
        document_ids = self.collection_manager.add_document(
            content="This is a comprehensive guide to setting up our development environment. "
                   "It covers Docker installation, database setup, and environment variables.",
            source=DataSource.CONFLUENCE,
            source_id="conf_123",
            title="Development Environment Setup Guide",
            author="<EMAIL>",
            tags=["development", "setup", "docker"],
            url="https://company.atlassian.net/wiki/spaces/ENG/pages/123456"
        )
        print(f"Added Confluence document with IDs: {document_ids}")
        
        # Search for documents
        results = self.collection_manager.search_across_sources(
            query="Docker setup environment",
            limit=5
        )
        
        print(f"Found {len(results)} results:")
        for result in results:
            print(f"- {result.metadata.title} (score: {result.score:.3f})")
            print(f"  Source: {result.metadata.source}")
            print(f"  Content preview: {result.content[:100]}...")
            print()
        
        # Clean up
        self.collection_manager.delete_document(DataSource.CONFLUENCE, "conf_123")
        print("Cleaned up test document")
    
    def example_2_multiple_data_sources(self):
        """
        Example 2: Working with multiple data sources
        """
        print("=== Example 2: Multiple Data Sources ===")
        
        # Add documents from different sources
        sources_data = [
            {
                "source": DataSource.NOTION,
                "content": "Our product roadmap for Q1 includes new user authentication, "
                          "improved dashboard performance, and mobile app enhancements.",
                "source_id": "notion_roadmap_q1",
                "title": "Q1 Product Roadmap",
                "workspace": "product"
            },
            {
                "source": DataSource.GOOGLE_DRIVE,
                "content": "Meeting notes from the quarterly planning session. "
                          "Discussed budget allocation, team restructuring, and project priorities.",
                "source_id": "gdrive_meeting_notes",
                "title": "Q1 Planning Meeting Notes",
                "workspace": "leadership"
            },
            {
                "source": DataSource.SLACK,
                "content": "Hey team, the authentication service is experiencing some latency issues. "
                          "We should investigate the database connection pool settings.",
                "source_id": "slack_msg_auth_issue",
                "title": "Authentication Service Alert",
                "workspace": "engineering"
            }
        ]
        
        # Add all documents
        for data in sources_data:
            self.collection_manager.add_document(
                content=data["content"],
                source=data["source"],
                source_id=data["source_id"],
                title=data["title"],
                workspace=data["workspace"],
                created_at=datetime.now()
            )
            print(f"Added {data['source'].value} document: {data['title']}")
        
        # Search across all sources
        print("\nSearching for 'authentication' across all sources:")
        results = self.collection_manager.search_across_sources(
            query="authentication service issues",
            limit=5
        )
        
        for result in results:
            print(f"- {result.metadata.title} from {result.metadata.source}")
            print(f"  Score: {result.score:.3f}")
            print()
        
        # Search within specific source
        print("Searching only in Slack:")
        slack_results = self.collection_manager.search_in_source(
            query="authentication",
            source=DataSource.SLACK,
            limit=5
        )
        
        for result in slack_results:
            print(f"- {result.metadata.title} (score: {result.score:.3f})")
        
        # Clean up
        for data in sources_data:
            self.collection_manager.delete_document(data["source"], data["source_id"])
        print("\nCleaned up test documents")
    
    def example_3_advanced_search_with_filters(self):
        """
        Example 3: Advanced search with metadata filters
        """
        print("=== Example 3: Advanced Search with Filters ===")
        
        # Add documents with rich metadata
        documents = [
            {
                "content": "Implementation guide for OAuth 2.0 integration with third-party services.",
                "source": DataSource.CONFLUENCE,
                "source_id": "oauth_guide",
                "title": "OAuth 2.0 Integration Guide",
                "author": "<EMAIL>",
                "workspace": "security",
                "tags": ["oauth", "security", "integration"],
                "content_type": "documentation"
            },
            {
                "content": "Security audit findings and recommendations for improving our authentication system.",
                "source": DataSource.GOOGLE_DRIVE,
                "source_id": "security_audit_2024",
                "title": "2024 Security Audit Report",
                "author": "<EMAIL>",
                "workspace": "security",
                "tags": ["audit", "security", "authentication"],
                "content_type": "report"
            },
            {
                "content": "Design specifications for the new user dashboard with improved UX.",
                "source": DataSource.NOTION,
                "source_id": "dashboard_design",
                "title": "Dashboard UX Design Specs",
                "author": "<EMAIL>",
                "workspace": "product",
                "tags": ["design", "ux", "dashboard"],
                "content_type": "design"
            }
        ]
        
        # Add documents
        for doc in documents:
            self.collection_manager.add_document(**doc)
        
        print("Added test documents with rich metadata")
        
        # Search with author filter
        print("\nSearching for security content by security team:")
        results = self.collection_manager.search_across_sources(
            query="security authentication",
            author="<EMAIL>",
            limit=5
        )
        
        for result in results:
            print(f"- {result.metadata.title} by {result.metadata.author}")
            print(f"  Tags: {result.metadata.tags}")
        
        # Search with workspace filter
        print("\nSearching in 'security' workspace:")
        results = self.collection_manager.search_across_sources(
            query="authentication",
            workspace="security",
            limit=5
        )
        
        for result in results:
            print(f"- {result.metadata.title} (workspace: {result.metadata.workspace})")
        
        # Search with content type filter
        print("\nSearching for 'report' content type:")
        results = self.collection_manager.search_across_sources(
            query="security",
            content_type="report",
            limit=5
        )
        
        for result in results:
            print(f"- {result.metadata.title} (type: {result.metadata.content_type})")
        
        # Clean up
        for doc in documents:
            self.collection_manager.delete_document(doc["source"], doc["source_id"])
        print("\nCleaned up test documents")
    
    def example_4_bulk_operations(self):
        """
        Example 4: Bulk import and statistics
        """
        print("=== Example 4: Bulk Operations ===")
        
        # Prepare bulk data for GitHub source
        github_documents = [
            {
                "content": f"This is README content for repository {i}. It contains setup instructions and usage examples.",
                "metadata": {
                    "source": DataSource.GITHUB.value,
                    "source_id": f"repo_{i}_readme",
                    "title": f"Repository {i} README",
                    "author": f"developer{i}@company.com",
                    "workspace": "engineering",
                    "tags": ["readme", "documentation"],
                    "content_type": "markdown",
                    "url": f"https://github.com/company/repo_{i}"
                },
                "id": f"github_readme_{i}"
            }
            for i in range(1, 6)
        ]
        
        # Bulk import
        result = self.collection_manager.bulk_import(
            source=DataSource.GITHUB,
            documents=github_documents
        )
        
        print(f"Bulk import result: {result}")
        
        # Get statistics
        print("\nCollection statistics:")
        stats = self.collection_manager.get_all_stats()
        print(f"Total collections: {stats['total_collections']}")
        print(f"Total documents: {stats['total_documents']}")
        
        for source_name, source_stats in stats['sources'].items():
            print(f"- {source_name}: {source_stats['total_documents']} documents")
        
        # Get GitHub-specific stats
        github_stats = self.collection_manager.get_source_stats(DataSource.GITHUB)
        print(f"\nGitHub collection stats: {github_stats}")
        
        # Clean up
        self.collection_manager.clear_source(DataSource.GITHUB)
        print("Cleared GitHub collection")
    
    def example_5_health_check_and_diagnostics(self):
        """
        Example 5: Health checks and system diagnostics
        """
        print("=== Example 5: Health Checks and Diagnostics ===")
        
        # Check ChromaDB service health
        chroma_health = self.chroma_service.health_check()
        print(f"ChromaDB service health: {chroma_health['status']}")
        print(f"Embedding model: {chroma_health['embedding_model']}")
        print(f"Collections count: {chroma_health['collections_count']}")
        
        # Check collection manager health
        manager_health = self.collection_manager.health_check()
        print(f"\nCollection manager health: {manager_health['overall_status']}")
        
        for source, health_info in manager_health['collections'].items():
            print(f"- {source}: {health_info['status']} ({health_info.get('document_count', 0)} docs)")
        
        # List all collections
        collections = self.chroma_service.list_collections()
        print(f"\nActive collections: {collections}")
    
    def run_all_examples(self):
        """
        Run all examples in sequence
        """
        print("Running ChromaDB Vector Database Examples\n")
        print("=" * 50)
        
        try:
            self.example_1_basic_document_operations()
            print("\n" + "=" * 50)
            
            self.example_2_multiple_data_sources()
            print("\n" + "=" * 50)
            
            self.example_3_advanced_search_with_filters()
            print("\n" + "=" * 50)
            
            self.example_4_bulk_operations()
            print("\n" + "=" * 50)
            
            self.example_5_health_check_and_diagnostics()
            print("\n" + "=" * 50)
            
            print("All examples completed successfully!")
            
        except Exception as e:
            print(f"Error running examples: {e}")
            import traceback
            traceback.print_exc()


def main():
    """
    Main function to run examples
    """
    examples = VectorDBExamples()
    examples.run_all_examples()


if __name__ == "__main__":
    main()