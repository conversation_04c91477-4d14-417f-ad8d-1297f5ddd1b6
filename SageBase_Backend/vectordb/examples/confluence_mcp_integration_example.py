"""
Example: Confluence MCP ChromaDB Service Integration
"""

from vectordb.services.confluence_mcp_chroma_service import get_confluence_mcp_chroma_service
from vectordb.services.chroma_service import get_chroma_service
import logging

logger = logging.getLogger(__name__)


def confluence_mcp_integration_example():
    """
    Example showing how to use the Confluence MCP ChromaDB service
    """
    print("🔗 Confluence MCP ChromaDB Integration Example")
    print("=" * 50)
    
    # Get the Confluence MCP service
    confluence_mcp = get_confluence_mcp_chroma_service()
    
    # Check health
    health = confluence_mcp.health_check()
    print(f"📊 Confluence MCP Collection Health: {health['status']}")
    print(f"📄 Document Count: {health['document_count']}")
    print(f"🔗 Source: {health['source']}")
    
    # Example: Index Confluence documents
    confluence_documents = [
        "API documentation for user authentication endpoints",
        "Deployment guide for production environment setup", 
        "Database schema documentation and migration guide",
        "User onboarding process and workflow documentation",
        "Development environment setup and configuration guide",
        "Security best practices and authentication protocols",
        "API rate limiting and throttling documentation",
        "Error handling and logging standards"
    ]
    
    confluence_ids = [f"conf_{i}" for i in range(1, len(confluence_documents) + 1)]
    
    print(f"\n📝 Indexing {len(confluence_documents)} Confluence documents...")
    confluence_mcp.index_texts(confluence_ids, confluence_documents)
    
    # Check updated count
    new_count = confluence_mcp.get_size()
    print(f"✅ Updated Confluence document count: {new_count}")
    
    # Example: Search for Confluence documents
    search_queries = [
        "API documentation",
        "deployment setup", 
        "database configuration",
        "user authentication",
        "security protocols"
    ]
    
    print(f"\n🔍 Searching Confluence documents...")
    for query in search_queries:
        distances, ids = confluence_mcp.search(query, number_of_results=3)
        print(f"Query: '{query}'")
        print(f"  Found {len(ids)} Confluence results")
        if len(ids) > 0:
            print(f"  Top result ID: {ids[0]}, Distance: {distances[0]:.3f}")
        print()
    
    # Example: Remove a Confluence document
    print(f"🗑️ Removing Confluence document 'conf_1'...")
    confluence_mcp.remove_ids("conf_1")
    
    final_count = confluence_mcp.get_size()
    print(f"✅ Final Confluence document count: {final_count}")
    
    # Show all collections in the system
    print(f"\n📚 All ChromaDB Collections:")
    chroma_service = get_chroma_service()
    collections = chroma_service.list_collections()
    for collection in collections:
        info = chroma_service.get_collection_info(collection)
        if info:
            print(f"  - {collection}: {info['count']} documents")
    
    print(f"\n✅ Confluence MCP Integration Example Complete!")


def confluence_mcp_vs_regular_comparison():
    """
    Example showing the difference between Confluence MCP and regular services
    """
    print("\n🔄 Comparing Confluence MCP vs Regular Services")
    print("=" * 50)
    
    # Confluence MCP Service (Confluence-specific)
    confluence_mcp = get_confluence_mcp_chroma_service()
    
    # Regular Document Service (multi-source)
    from vectordb.services.document_service import DocumentService
    doc_service = DocumentService("general_docs")
    
    print("Confluence MCP Service Features:")
    print("  ✅ FAISS-like search interface (distances, ids)")
    print("  ✅ Simple indexing with IDs and texts")
    print("  ✅ Confluence-specific collection (mcp_confluence_documents)")
    print("  ✅ Serialization support")
    print("  ✅ Confluence document logging")
    
    print("\nRegular Document Service Features:")
    print("  ✅ Rich metadata support")
    print("  ✅ Multi-source document management")
    print("  ✅ Advanced search with filters")
    print("  ✅ Bulk operations")
    print("  ✅ Notification system integration")
    
    print("\n💡 Use Confluence MCP service for Confluence integration")
    print("💡 Use Document service for general document management")


def confluence_environment_example():
    """
    Example showing Confluence MCP with different environments
    """
    print("\n🌍 Confluence Environment Example")
    print("=" * 50)
    
    # Create Confluence MCP services for different environments
    confluence_prod = get_confluence_mcp_chroma_service("mcp_confluence_documents_prod")
    confluence_dev = get_confluence_mcp_chroma_service("mcp_confluence_documents_dev")
    confluence_staging = get_confluence_mcp_chroma_service("mcp_confluence_documents_staging")
    
    print("🌍 Created Confluence MCP services for:")
    print(f"  - Production: {confluence_prod.get_name()}")
    print(f"  - Development: {confluence_dev.get_name()}")
    print(f"  - Staging: {confluence_staging.get_name()}")
    
    # Example: Add environment-specific documents
    prod_docs = ["Production API documentation", "Live deployment procedures"]
    dev_docs = ["Development API documentation", "Local setup guide"]
    staging_docs = ["Staging environment documentation", "Testing procedures"]
    
    confluence_prod.index_texts(["prod_1", "prod_2"], prod_docs)
    confluence_dev.index_texts(["dev_1", "dev_2"], dev_docs)
    confluence_staging.index_texts(["staging_1", "staging_2"], staging_docs)
    
    print(f"\n📊 Environment-specific Confluence document counts:")
    print(f"  - Production: {confluence_prod.get_size()} documents")
    print(f"  - Development: {confluence_dev.get_size()} documents")
    print(f"  - Staging: {confluence_staging.get_size()} documents")


if __name__ == "__main__":
    confluence_mcp_integration_example()
    confluence_mcp_vs_regular_comparison()
    confluence_environment_example() 