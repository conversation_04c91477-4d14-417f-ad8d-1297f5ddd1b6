# ChromaDB Vector Database Infrastructure

A comprehensive vector database solution for SageBase using ChromaDB with semantic search capabilities across multiple data sources.

## 🚀 Quick Start

### Installation
```bash
# Install dependencies
pip install chromadb sentence-transformers

# Or use requirements.txt (already includes ChromaDB)
pip install -r requirements.txt
```

## 🚀 Getting Started

### Step 1: Run the Embedding Script
Start the embedding script to process your documents:

```bash
python vectordb/tests/embed_sageBaseFolder.py
```

### Step 2: Choose Your Documents Folder
The script will prompt you to enter a path where your documents are located:
- **Default option**: Points to the SageBase documentation folder
- **Custom path**: Enter your own folder path containing the files you want to embed

### Step 3: Database Creation
The script will create a ChromaDB database folder at:
```
SageBase/SageBase_BackEnd.git/chroma_db/
```

This folder contains the embedding database with your processed documents.








### Basic Usage
```python
from vectordb.collections.collection_manager import get_collection_manager
from vectordb.models.document import DataSource

# Initialize manager
manager = get_collection_manager()

# Add a document
document_ids = manager.add_document(
    content="Your document content here",
    source=DataSource.CONFLUENCE,
    source_id="unique_source_id",
    title="Document Title",
    workspace="team_name",
    author="<EMAIL>",
    tags=["documentation", "api"]
)

# Search across all sources
results = manager.search_across_sources(
    query="your search terms",
    limit=10
)

# Print results
for result in results:
    print(f"📄 {result.metadata.title}")
    print(f"🎯 Score: {result.score:.3f}")
    print(f"📝 Content: {result.content[:100]}...")
    print("---")
```

## 📁 Architecture Overview

```
vectordb/
├── models/          # Data models and schemas
│   └── document.py  # Document, Metadata, SearchQuery models
├── services/        # Core services
│   ├── chroma_service.py    # ChromaDB connection management
│   └── document_service.py  # High-level document operations
├── collections/     # Multi-source management
│   └── collection_manager.py # Unified interface for all sources
├── utils/          # Utilities
│   ├── text_utils.py       # Text processing and chunking
│   └── embedding_utils.py  # Embedding operations
├── api/            # REST API endpoints
│   ├── views.py    # API views
│   └── urls.py     # URL routing
├── examples/       # Usage examples
│   └── usage_examples.py
└── management/     # Django commands
    └── commands/
        └── test_vectordb.py
```

## 🗂️ Supported Data Sources

The system supports multiple data sources with dedicated collections:

| Data Source | Collection Name | Use Case |
|-------------|----------------|----------|
| `DataSource.CONFLUENCE` | `confluence_documents` | Wiki pages, documentation |
| `DataSource.GOOGLE_DRIVE` | `google_drive_documents` | Documents, spreadsheets |
| `DataSource.NOTION` | `notion_documents` | Notes, project pages |
| `DataSource.SLACK` | `slack_messages` | Messages, threads |
| `DataSource.JIRA` | `jira_issues` | Tickets, issues |
| `DataSource.LOCAL` | `general_documents` | Any other content |

## 🔧 Core Components

### 1. Collection Manager
The main interface for all operations:

```python
from vectordb.collections.collection_manager import get_collection_manager

manager = get_collection_manager()

# Add document to specific source
manager.add_document(
    content="Document content",
    source=DataSource.NOTION,
    source_id="notion_page_123",
    title="Project Notes",
    workspace="product",
    author="<EMAIL>",
    tags=["project", "planning"],
    url="https://notion.so/page/123"
)

# Search within specific source
results = manager.search_in_source(
    query="project planning",
    source=DataSource.NOTION,
    limit=5
)

# Search across multiple sources
results = manager.search_across_sources(
    query="project planning",
    sources=[DataSource.NOTION, DataSource.CONFLUENCE],
    workspace="product",
    limit=10
)
```

### 2. Confluence MCP Service
Specialized service for Confluence MCP server integration:

```python
from vectordb.services.confluence_mcp_chroma_service import get_confluence_mcp_chroma_service

# Get Confluence MCP service
confluence_mcp = get_confluence_mcp_chroma_service()

# Index Confluence documents
confluence_mcp.index_texts(["doc1", "doc2"], ["content1", "content2"])

# Search Confluence documents (FAISS-like interface)
distances, ids = confluence_mcp.search("API documentation", number_of_results=10)

# Remove Confluence documents
confluence_mcp.remove_ids(["doc1"])

# Check health
health = confluence_mcp.health_check()
print(f"Confluence documents: {health['document_count']}")
```

### 2. Document Service
Low-level operations for a single collection:

```python
from vectordb.services.document_service import DocumentService
from vectordb.models.document import DocumentMetadata, DataSource

service = DocumentService("my_collection")

# Create metadata
metadata = DocumentMetadata(
    source=DataSource.CONFLUENCE,
    source_id="conf_123",
    title="API Documentation",
    author="<EMAIL>"
)

# Add document
document_ids = service.add_document(
    content="API documentation content...",
    metadata=metadata
)

# Search
from vectordb.models.document import SearchQuery

query = SearchQuery(
    query="API documentation",
    limit=5,
    similarity_threshold=0.7
)
results = service.search(query)
```

### 3. ChromaDB Service
Base connection management:

```python
from vectordb.services.chroma_service import get_chroma_service

chroma = get_chroma_service()

# Health check
health = chroma.health_check()
print(f"Status: {health['status']}")

# List collections
collections = chroma.list_collections()
print(f"Collections: {collections}")
```

## 🔍 Advanced Search Features

### Metadata Filtering
```python
results = manager.search_across_sources(
    query="machine learning",
    workspace="engineering",      # Filter by workspace
    author="<EMAIL>",  # Filter by author
    content_type="documentation",   # Filter by content type
    tags=["ml", "ai"],             # Filter by tags
    similarity_threshold=0.8,      # High similarity only
    limit=10
)
```

### Date Range Filtering
```python
from datetime import datetime, timedelta

last_month = datetime.now() - timedelta(days=30)

results = manager.search_across_sources(
    query="project updates",
    date_from=last_month,
    date_to=datetime.now(),
    limit=5
)
```

### Custom Metadata Filters
```python
results = manager.search_across_sources(
    query="search terms",
    metadata_filters={
        "project_id": "proj_123",
        "status": "active"
    }
)
```

## 📊 Bulk Operations

### Bulk Import
```python
documents = [
    {
        "content": "Content of document 1",
        "metadata": {
            "source": "confluence",
            "source_id": "conf_1",
            "title": "Document 1",
            "workspace": "engineering"
        }
    },
    {
        "content": "Content of document 2", 
        "metadata": {
            "source": "confluence",
            "source_id": "conf_2",
            "title": "Document 2",
            "workspace": "engineering"
        }
    }
]

result = manager.bulk_import(
    source=DataSource.CONFLUENCE,
    documents=documents
)

print(f"Imported {result['documents_processed']} documents")
print(f"Created {result['chunks_created']} chunks")
```

### Statistics and Monitoring
```python
# Get overall statistics
stats = manager.get_all_stats()
print(f"Total documents: {stats['total_documents']}")
print(f"Active collections: {stats['total_collections']}")

# Get source-specific stats
confluence_stats = manager.get_source_stats(DataSource.CONFLUENCE)
print(f"Confluence documents: {confluence_stats['total_documents']}")

# Health check
health = manager.health_check()
print(f"Overall status: {health['overall_status']}")
```

## 🌐 REST API Usage

### Start Django Server
```bash
python manage.py runserver 0.0.0.0:8000
```

### API Endpoints

#### Health Check
```bash
curl -X GET http://localhost:8000/api/vectordb/health/
```

#### Add Document
```bash
curl -X POST http://localhost:8000/api/vectordb/documents/add/ \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Document content here",
    "source": "confluence",
    "source_id": "conf_123",
    "title": "API Guide",
    "workspace": "engineering",
    "author": "<EMAIL>",
    "tags": ["api", "documentation"]
  }'
```

#### Search Documents
```bash
curl -X POST http://localhost:8000/api/vectordb/documents/search/ \
  -H "Content-Type: application/json" \
  -d '{
    "query": "API documentation",
    "sources": ["confluence", "notion"],
    "workspace": "engineering", 
    "limit": 5,
    "similarity_threshold": 0.7
  }'
```

#### Get Statistics
```bash
curl -X GET http://localhost:8000/api/vectordb/stats/
```

## ⚙️ Configuration

### Django Settings
Add to your `settings.py`:

```python
INSTALLED_APPS = [
    # ... other apps
    'vectordb',
]

# ChromaDB Configuration
CHROMA_PERSIST_DIR = os.path.join(BASE_DIR, 'chroma_db')
CHROMA_HOST = None  # For local mode
CHROMA_PORT = 8000  # For client mode
CHROMA_EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
```

### Environment Variables
```bash
# Optional: Customize storage location
export CHROMA_PERSIST_DIR="/path/to/chroma/storage"

# Optional: Use remote ChromaDB server
export CHROMA_HOST="your-chroma-server.com"
export CHROMA_PORT="8000"

# Optional: Use different embedding model
export CHROMA_EMBEDDING_MODEL="all-mpnet-base-v2"
```

## 🧪 Testing

### Quick Test
```bash
python test_vectordb_quick.py
```

### Django Management Command
```bash
python manage.py test_vectordb
```

### Run Specific Example
```bash
python manage.py test_vectordb --example 1  # Basic operations
python manage.py test_vectordb --example 2  # Multi-source
python manage.py test_vectordb --example 3  # Advanced search
```

### Django Shell Testing
```bash
python manage.py shell

# In shell:
from vectordb.examples.usage_examples import VectorDBExamples
examples = VectorDBExamples()
examples.example_1_basic_document_operations()
```

## 📝 Text Processing Features

### Automatic Chunking
Large documents are automatically split into chunks:

```python
# Documents > 1000 characters are automatically chunked
manager.add_document(
    content="Very long document content...",  # 5000+ characters
    source=DataSource.CONFLUENCE,
    source_id="long_doc",
    title="Long Document",
    chunk_size=400,    # Customize chunk size
    chunk_overlap=100   # Customize overlap
)
```

### Text Cleaning and Processing
```python
from vectordb.utils.text_utils import TextProcessor

processor = TextProcessor()

# Clean text
clean_text = processor.clean_text(raw_text)

# Extract metadata
metadata = processor.extract_metadata_from_text(text)
print(f"Extracted emails: {metadata.get('emails', [])}")
print(f"Extracted URLs: {metadata.get('urls', [])}")

# Detect language
language = processor.detect_language(text)
print(f"Detected language: {language}")
```

## 🔧 Advanced Usage

### Custom Embedding Models
```python
from vectordb.services.chroma_service import ChromaDBService

# Use a different embedding model
service = ChromaDBService(embedding_model="all-mpnet-base-v2")
```

### Collection Management
```python
# Create custom collection
from vectordb.services.document_service import DocumentService

custom_service = DocumentService("my_custom_collection")

# Clear a collection
manager.clear_source(DataSource.CONFLUENCE)

# Get collection info
info = chroma.get_collection_info("confluence_documents")
print(f"Collection has {info['count']} documents")
```

### Error Handling
```python
try:
    results = manager.search_across_sources(
        query="search terms",
        limit=10
    )
except Exception as e:
    print(f"Search failed: {e}")
    # Handle error appropriately
```

## 🚀 Integration Examples

### With GitHub Integration
```python
# In your GitHub webhook handler
from vectordb.collections.collection_manager import get_collection_manager

def handle_github_push(repo_name, commit_data):
    manager = get_collection_manager()
    
    # Add commit information to vector database
    manager.add_document(
        content=f"Commit: {commit_data['message']}\nFiles: {', '.join(commit_data['files'])}",
        source=DataSource.GITHUB,
        source_id=commit_data['sha'],
        title=f"Commit in {repo_name}",
        workspace="engineering",
        author=commit_data['author'],
        custom_fields={
            "repo_name": repo_name,
            "commit_sha": commit_data['sha']
        }
    )
```

### With Existing Services
```python
# In your existing service classes
class DocumentationService:
    def __init__(self):
        self.vector_manager = get_collection_manager()
    
    def add_documentation(self, content, metadata):
        # Store in your primary database
        doc_id = self.save_to_database(content, metadata)
        
        # Also store in vector database for search
        self.vector_manager.add_document(
            content=content,
            source=DataSource.LOCAL,
            source_id=str(doc_id),
            title=metadata['title'],
            workspace=metadata.get('workspace'),
            author=metadata.get('author')
        )
    
    def search_documentation(self, query, filters=None):
        # Use vector search for semantic matching
        return self.vector_manager.search_across_sources(
            query=query,
            **filters if filters else {}
        )
```

## 🎯 Performance Tips

1. **Batch Operations**: Use `bulk_import()` for adding multiple documents
2. **Chunk Size**: Adjust chunk size based on your content (default: 1000 chars)
3. **Similarity Threshold**: Lower threshold (0.1-0.3) for broader results, higher (0.7-0.9) for precise matches
4. **Embedding Model**: Use larger models for better accuracy, smaller for speed
5. **Filtering**: Use metadata filters to reduce search space

## 🐛 Troubleshooting

### Common Issues

**ChromaDB Connection Error**
```bash
# Check if directory exists and has permissions
ls -la ./chroma_db/
```

**Import Errors**
```bash
# Install missing dependencies
pip install chromadb sentence-transformers
```

**Search Returns No Results**
```python
# Lower the similarity threshold
results = manager.search_across_sources(
    query="your search",
    similarity_threshold=0.1  # Try lower threshold
)
```

**Memory Issues with Large Documents**
```python
# Use smaller chunk sizes
manager.add_document(
    content=large_content,
    source=DataSource.CONFLUENCE,
    source_id="doc_id",
    title="Large Doc",
    chunk_size=400,  # Smaller chunks
    chunk_overlap=50
)
```

## 📚 Additional Resources

- [ChromaDB Documentation](https://docs.trychroma.com/)
- [SentenceTransformers Models](https://huggingface.co/sentence-transformers)
- [Examples Directory](./examples/) - Complete usage examples
- [API Documentation](./api/) - REST API details
- [Testing Guide](../TESTING_GUIDE.md) - Comprehensive testing instructions

## 🤝 Contributing

When adding new features:

1. Update models in `models/document.py`
2. Add business logic in `services/` 
3. Update the collection manager in `collections/`
4. Add API endpoints in `api/views.py`
5. Include tests and examples
6. Update this README

---

**Ready to use!** The ChromaDB infrastructure provides production-ready semantic search capabilities for your SageBase backend. Start with the Quick Start section and explore the examples for your specific use case.