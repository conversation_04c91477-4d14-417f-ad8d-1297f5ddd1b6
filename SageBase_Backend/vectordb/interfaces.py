"""
Main interface for the ChromaDB vector database module.
This module provides a clean, simple interface for other modules to interact with the vector database.
"""

import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from uuid import UUID

from .models.document import DataSource, DocumentMetadata, SearchResult, SearchQuery
from .collections.collection_manager import get_collection_manager

# New simplified interface function
def get_chroma_collection(collection_name: str):
    """
    Get a ChromaDB collection manager for a specific workspace.
    
    Args:
        workspace: The workspace name (will be used as collection name)
    
    Returns:
        CollectionManager instance for the workspace
        
    Example:
        >>> collection = get_chroma_collection("sagebase")
        >>> results = collection.search_across_sources(query="API docs", limit=10)
    """
    return get_collection_manager(collection_name)


def convert_sources_to_enum(sources_to_convert: Optional[List[str]]) -> List[DataSource]:
    """
    Convert a list of string sources to DataSource enum values.
    Removes duplicates to ensure unique sources.
    If sources_to_convert is None, returns all available DataSource enum values.
    
    Args:
        sources_to_convert: List of string source names to convert, or None for all sources
        
    Returns:
        List of unique DataSource enum values
        
    Example:
        >>> sources = convert_sources_to_enum(["google_drive", "confluence", "jira", "google_drive"])
        >>> print([s.value for s in sources])  # ['google_drive', 'confluence', 'jira']
        >>> all_sources = convert_sources_to_enum(None)
        >>> print([s.value for s in all_sources])  # ['confluence', 'google_drive', 'jira', 'notion', 'local', 'other']
    """
    # If None, return all available DataSource enum values
    if sources_to_convert is None:
        return list(DataSource)
    
    converted_sources = []
    seen_sources = set()  # Track seen sources to avoid duplicates
    
    for source in sources_to_convert:
        try:
            # Map string sources to DataSource enum
            if source == "google_drive":
                enum_source = DataSource.GOOGLE_DRIVE
            elif source == "confluence":
                enum_source = DataSource.CONFLUENCE
            elif source == "jira":
                enum_source = DataSource.JIRA
            elif source == "notion":
                enum_source = DataSource.NOTION
            elif source == "local":
                enum_source = DataSource.LOCAL
            else:
                # For unknown sources, use OTHER
                enum_source = DataSource.OTHER
                
            # Only add if not already seen
            if enum_source not in seen_sources:
                converted_sources.append(enum_source)
                seen_sources.add(enum_source)
                
        except Exception as e:
            logger.warning(f"⚠️ Could not convert source '{source}' to DataSource enum: {e}")
            # Only add OTHER if not already present
    
    return converted_sources


logger = logging.getLogger(__name__)


def add_document(
    collection_name: str,
    content: str,
    source: Union[DataSource, str],
    source_id: Union[str, UUID],
    title: str,
    author: Optional[str] = None,
    tags: Optional[List[str]] = None,
    url: Optional[str] = None,
    content_type: Optional[str] = None,
    content_hash: Optional[str] = None,
    **kwargs
) -> List[str]:
    """
    Add a document to the vector database.
    
    Args:
        content: The document content to store, the source_id is the unique identifier for the document, best if it the url of the file. Then it is unique.
        source: Data source (DataSource enum or string)
        source_id: Unique identifier from the source system
        title: Document title
        author: Optional author information
        workspace: Optional workspace/team identifier (will be converted to lowercase for consistency)
        tags: Optional list of tags
        url: Optional URL to the original document
        content_type: Optional content type (e.g., 'text', 'pdf', 'markdown')
        content_hash: Optional SHA-256 hash of content (auto-generated if not provided)
        **kwargs: Additional metadata fields
        
    Returns:
        List of document IDs that were added (one per chunk if document is large)
        
    Example:
        >>> doc_ids = add_document(
        ...     content="This is a guide to using our API",
        ...     chunk_size=300,
        ...     source=DataSource.CONFLUENCE,
        ...     source_id="conf_123",
        ...     title="API Usage Guide",
        ...     author="<EMAIL>",
        ...     workspace="engineering",
        ...     tags=["api", "documentation"]
        ... )
        >>> print(f"Added document with IDs: {doc_ids}")
    """
    try:
        # Convert string source to DataSource enum if needed
        if isinstance(source, str):
            source = DataSource(source.lower())
        
        # Generate content hash if not provided
        if content_hash is None:
            import hashlib
            content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
            logger.debug(f"Generated content hash: {content_hash[:8]}...")
        
        # Normalize collection_name to lowercase for consistency
        #collection_name is a UUID
        collection_name = str(collection_name)
        logger.info(f"Adding document to collection: '{collection_name}'")
        
        # Use get_chroma_collection internally
        collection_manager = get_chroma_collection(collection_name)
        
        # Add document
        return collection_manager.add_document(
            content=content,
            source=source,
            source_id=source_id,
            title=title,
            author=author,
            tags=tags or [],
            url=url,
            content_type=content_type,
            content_hash=content_hash,
            **kwargs
        )
        
    except Exception as e:
        logger.error(f"Failed to add document: {e}")
        raise


def delete_document(source: Union[DataSource, str], source_id: str, collection_name: str) -> bool:
    """
    Delete a document from the vector database.
    
    Args:
        source: Data source (DataSource enum or string)
        source_id: Unique identifier from the source system,the source_id is the unique identifier for the document, best if it the url of the file. Then it is unique.
        
    Returns:
        True if deletion was successful, False otherwise
        
    Example:
        >>> success = delete_document(DataSource.CONFLUENCE, "conf_123")
        >>> print(f"Document deleted: {success}")
    """
    try:
        # Convert UUID to string if needed
        if hasattr(source_id, 'hex'):
            source_id = str(source_id)
        
        # Convert string source to DataSource enum if needed
        if isinstance(source, str):
            source = DataSource(source.lower())
        
        # Normalize collection_name
        collection_name = collection_name.lower()
        
        # Use get_chroma_collection internally
        collection_manager = get_chroma_collection(collection_name)
        
        # Delete document
        return collection_manager.delete_document(source, source_id)
        
    except Exception as e:
        logger.error(f"Failed to delete document: {e}")
        return False


def delete_by_metadata(collection_name:str, filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Delete documents from the vector database based on metadata filters.
    
    This function searches for documents using metadata filtering,
    then deletes the found documents. This is a generic function that
    can be used to delete documents by any metadata criteria.
    
    Args:
        filters: Dictionary of metadata filters (e.g., {"url": "http://example.com", "workspace": "sagebase"})
        
    Returns:
        Dictionary with deletion results including count of deleted documents
        
    Examples:
        >>> # Delete by URL
        >>> result = delete_by_metadata({"url": "http://localhost:3000/qa/proj-123/qa-456"})
        
        >>> # Delete by workspace
        >>> result = delete_by_metadata({"workspace": "sagebase"})
        
        >>> # Delete by source
        >>> result = delete_by_metadata({"source": "confluence"})
        
        >>> # Delete by multiple criteria
        >>> result = delete_by_metadata({
        ...     "collection_name": "sagebase",
        ...     "content_type": "qa"
        ... })
        
        >>> print(f"Deleted {result['deleted_count']} documents")
    """
    try:
        logger.info(f"Searching for documents with metadata filters: {filters}")
        
        # Get collection manager
        manager = get_collection_manager(collection_name)
        
        deleted_count = 0
        failed_count = 0
        errors = []
        

        try:
            service = manager.get_service()
            
            # Search for documents with these metadata filters
            matching_documents = service.search_by_metadata(
                filters=filters,
                limit=10000  # High limit to get all documents
            )
            
            logger.info(f"Found {len(matching_documents)} documents in {collection_name} matching filters: {filters}")
            
            # Delete each document found
            for doc in matching_documents:
                try:
                    success = manager.delete_document(
                        doc.metadata.source,
                        doc.metadata.source_id
                    )
                    if success:
                        deleted_count += 1
                        logger.info(f"Deleted document: {doc.metadata.title} from {collection_name}")
                    else:
                        failed_count += 1
                        errors.append(f"Failed to delete {doc.metadata.source_id} from {collection_name}")
                except Exception as e:
                    logger.exception(f"Error deleting {doc.metadata.source_id} from {collection_name}: {str(e)}")
                    failed_count += 1
                    errors.append(f"Error deleting {doc.metadata.source_id} from {collection_name}: {str(e)}")
                    
        except Exception as e:
            logger.exception(f"Error processing source {collection_name}: {e}")
            errors.append(f"Error processing source {collection_name}: {str(e)}")
        
        return {
            "success": len(errors) == 0,  # Success is False if there are any errors
            "filters": filters,
            "deleted_count": deleted_count,
            "failed_count": failed_count,
            "errors": errors
        }
        
    except Exception as e:
        logger.exception(f"Failed to delete documents by metadata: {e}")
        return {
            "success": False,
            "filters": filters,
            "error": str(e),
            "deleted_count": 0,
            "failed_count": 0,
            "errors": [str(e)]
        }


def delete_documents_in_workspace(collection_name: str) -> Dict[str, Any]:
    """
    Delete all documents in a specific workspace by deleting the entire ChromaDB collection.
    
    This function uses ChromaDB's native collection deletion which is more efficient
    than deleting documents one by one.
    
    Args:
        collection_name: Collection name to delete (workspace identifier)
        
    Returns:
        Dictionary with deletion results
        
    Example:
        >>> result = delete_documents_in_workspace("sagebase")
        >>> print(f"Collection deleted: {result['success']}")
    """
    try:
        logger.info(f"Deleting ChromaDB collection: '{collection_name}'")
        
        # Get collection manager
        manager = get_collection_manager(collection_name)
        
        # Delete all documents using the collection manager's method
        try:
            result = manager.delete_all_documents()
            
            if result["success"]:
                logger.info(f"Successfully deleted all documents from collection: '{collection_name}'")
                return result
            else:
                logger.error(f"Failed to delete documents from collection '{collection_name}': {result.get('error', 'Unknown error')}")
                return result
            
        except Exception as e:
            logger.exception(f"Error deleting ChromaDB collection '{collection_name}': {e}")
            return {
                "success": False,
                "collection_name": collection_name,
                "error": str(e),
                "deleted_count": 0,
                "failed_count": 1,
                "errors": [str(e)]
            }
        
    except Exception as e:
        logger.exception(f"Failed to delete collection '{collection_name}': {e}")
        return {
            "success": False,
            "collection_name": collection_name,
            "error": str(e),
            "deleted_count": 0,
            "failed_count": 1,
            "errors": [str(e)]
        }


def update_document(
    collection_name: str,
    source: Union[DataSource, str],
    source_id: str,
    content: Optional[str] = None,
    title: Optional[str] = None,
    author: Optional[str] = None,
    workspace: Optional[str] = None,
    tags: Optional[List[str]] = None,
    url: Optional[str] = None,
    content_type: Optional[str] = None,
    **kwargs
) -> bool:
    """
    Update a document in the vector database.
    
    Args:
        source: Data source (DataSource enum or string)
        source_id: Unique identifier from the source system
        content: Optional new content
        title: Optional new title
        author: Optional new author
        workspace: Optional new workspace (will be converted to lowercase for consistency)
        tags: Optional new tags
        url: Optional new URL
        content_type: Optional new content type
        **kwargs: Additional metadata fields to update
        
    Returns:
        True if update was successful, False otherwise
        
    Example:
        >>> success = update_document(
        ...     source=DataSource.CONFLUENCE,
        ...     source_id="conf_123",
        ...     content="Updated content for the API guide",
        ...     title="Updated API Usage Guide",
        ...     tags=["api", "documentation", "updated"]
        ... )
        >>> print(f"Document updated: {success}")
    """
    try:
        # Convert string source to DataSource enum if needed
        if isinstance(source, str):
            source = DataSource(source.lower())
        
        
        # Get collection manager
        manager = get_collection_manager(collection_name)
        
        # For updating, we need to delete and re-add since ChromaDB doesn't support direct updates
        # First check if document exists
        existing_doc = manager.get_document(source, source_id)
        if not existing_doc:
            logger.warning(f"Document {source_id} not found for update")
            return False
        
        # If no content provided, we can't update (need content for re-adding)
        if content is None:
            logger.warning("Cannot update document without new content")
            return False
        
        # Create updated metadata
        updated_metadata = DocumentMetadata(
            source=source,
            source_id=source_id,
            title=title or "Updated Document",
            author=author,
            workspace=workspace,
            tags=tags or [],
            url=url,
            content_type=content_type,
            updated_at=datetime.now(),
            **kwargs
        )
        
        # Delete existing document
        if not manager.delete_document(source, source_id):
            logger.error(f"Failed to delete existing document for update: {source_id}")
            return False
        
        # Add updated document
        manager.add_document(
            content=content,
            source=source,
            source_id=source_id,
            title=title or "Updated Document",
            author=author,
            workspace=workspace,
            tags=tags or [],
            url=url,
            content_type=content_type,
            updated_at=datetime.now(),
            **kwargs
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to update document: {e}")
        return False


def search(
    query: str,
    collection_name: str,
    sources: Optional[List[Union[DataSource, str]]] = None,
    limit: int = 10,
    content_type: Optional[str] = None,
    tags: Optional[List[str]] = None,
    author: Optional[str] = None,
    similarity_threshold: float = 0.4,
    search_mode: str = "hybrid",
    textual_boost: float = 1.0,
    semantic_boost: float = 1.0,
    **kwargs
) -> List[SearchResult]:
    """
    Search for documents in the vector database using hybrid search (semantic + textual).
    
    Args:
        query: Search query string
        sources: Optional list of data sources to search (None for all sources)
        limit: Maximum number of results to return
        workspace: Optional workspace filter (will be converted to lowercase for consistency)
        content_type: Optional content type filter
        tags: Optional tags filter
        author: Optional author filter
        similarity_threshold: Minimum similarity score (0.0 to 1.0)
        search_mode: Search mode - "semantic", "textual", or "hybrid" (default)
        textual_boost: Boost factor for textual search results (default: 1.0)
        semantic_boost: Boost factor for semantic search results (default: 1.0)
        **kwargs: Additional search filters
        
    Returns:
        List of SearchResult objects containing matching documents
        
    Examples:
        >>> # Hybrid search (default) - combines semantic and textual search
        >>> results = search(
        ...     query="API authentication",
        ...     sources=[DataSource.CONFLUENCE, DataSource.GITHUB],
        ...     workspace="engineering",
        ...     limit=5
        ... )
        
        >>> # Semantic-only search
        >>> results = search(
        ...     query="machine learning concepts",
        ...     search_mode="semantic",
        ...     limit=10
        ... )
        
        >>> # Textual-only search for exact matches
        >>> results = search(
        ...     query="Problems SageBase Solves",
        ...     search_mode="textual",
        ...     limit=5
        ... )
        
        >>> # Hybrid search with boosted textual results
        >>> results = search(
        ...     query="deployment guide",
        ...     search_mode="hybrid",
        ...     textual_boost=1.5,
        ...     semantic_boost=0.8
        ... )
        
        >>> for result in results:
        ...     print(f"Found: {result.metadata.title} (score: {result.score:.3f})")
    """
    try:
        # Convert string sources to DataSource enums if needed
        if sources:
            converted_sources = []
            for source in sources:
                if isinstance(source, str):
                    converted_sources.append(DataSource(source.lower()))
                else:
                    converted_sources.append(source)
            sources = converted_sources
        
        # Normalize collection_name to lowercase for consistency
        collection_name = collection_name.lower()
        logger.info(f"Searching in collection: '{collection_name}'")
        
        # Use get_chroma_collection internally
        collection_manager = get_chroma_collection(collection_name)
        
        # Search across sources with new parameters
        return collection_manager.search_across_sources(
            query=query,
            sources=sources,
            limit=limit,
            # Note: workspace parameter removed since it's handled by collection_manager
            content_type=content_type,
            tags=tags,
            author=author,
            similarity_threshold=similarity_threshold,
            search_mode=search_mode,
            textual_boost=textual_boost,
            semantic_boost=semantic_boost,
            **kwargs
        )
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        return []


def get_stats(collection_name: str) -> Dict[str, Any]:
    """
    Get statistics about the vector database.
    
    Returns:
        Dictionary containing statistics about collections and documents
        
    Example:
        >>> stats = get_stats()
        >>> print(f"Total documents: {stats['total_documents']}")
        >>> print(f"Total collections: {stats['total_collections']}")
    """
    try:
        manager = get_collection_manager(collection_name)
        return manager.get_all_stats()
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        return {"error": str(e)}


def get_document(source: Union[DataSource, str], source_id: str, collection_name: str) -> Optional[Dict[str, Any]]:
    """
    Get a specific document from the vector database.
    
    Args:
        source: Data source (DataSource enum or string)
        source_id: Unique identifier from the source system
        
    Returns:
        Document data if found, None otherwise
        
    Example:
        >>> doc = get_document(DataSource.CONFLUENCE, "conf_123")
        >>> if doc:
        ...     print(f"Document title: {doc.metadata.title}")
    """
    try:
        # Convert string source to DataSource enum if needed
        if isinstance(source, str):
            source = DataSource(source.lower())
        
        # Get collection manager
        manager = get_collection_manager(collection_name)
        
        # Get document
        return manager.get_document(source, source_id)
        
    except Exception as e:
        logger.error(f"Failed to get document: {e}")
        return None


def health_check(collection_name: str) -> Dict[str, Any]:
    """
    Check the health of the vector database system.
    
    Returns:
        Dictionary containing health status information
        
    Example:
        >>> health = health_check()
        >>> print(f"System status: {health['overall_status']}")
    """
    try:
        manager = get_collection_manager()
        return manager.health_check()
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"overall_status": "error", "error": str(e)}


def list_workspace_documents_by_metadata(collection_name: str) -> List[SearchResult]:
    """
    List all documents in a specific workspace using metadata filtering (no embeddings).
    
    This function uses ChromaDB's native metadata filtering instead of semantic search,
    which is more efficient for workspace-based operations.
    
    Args:
        workspace: Workspace name to list documents from (will be converted to lowercase for consistency)
        
    Returns:
        List of SearchResult objects containing documents in the workspace
        
    Example:
        >>> documents = list_workspace_documents_by_metadata("sagebase")
        >>> print(f"Found {len(documents)} documents in workspace")
    """
    try:
        # Normalize workspace to lowercase for consistency (minimal letters convention)
        logger.info(f"Searching for workspace '{collection_name}' ")
        
        # Get collection manager
        manager = get_collection_manager(collection_name)
        
        all_documents = []
        
        # Get all document services (collections)
        for source in DataSource:
            try:
                service = manager.get_service()
                
                # Search for documents in this workspace using metadata filtering
                workspace_documents = service.search_by_metadata(
                    limit=10000  # High limit to get all documents
                )
                
                logger.info(f"Found {len(workspace_documents)} documents in {source.value} for workspace '{collection_name}'")
                all_documents.extend(workspace_documents)
                        
            except Exception as e:
                logger.exception(f"Error processing source {source.value}: {e}")
                continue
        
        return all_documents
        
    except Exception as e:
        logger.exception(f"Failed to list workspace documents: {e}")
        return []