import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class TextProcessor:
    """
    Utility class for text processing and chunking
    """
    
    def __init__(self):
        self.chunk_separators = [
            "\n\n\n",  # Multiple line breaks
            "\n\n",    # Double line breaks
            "\n",      # Single line breaks
            ". ",      # Sentences
            "! ",      # Exclamations
            "? ",      # Questions
            " ",       # Words
        ]
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text
        
        Args:
            text: Raw text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might interfere with processing
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # Normalize unicode
        text = text.encode('utf-8', errors='ignore').decode('utf-8')
        
        return text
    
    def chunk_text(self, 
                   text: str, 
                   chunk_size: int = 400, 
                   overlap: int = 100,
                   min_chunk_size: int = 50) -> List[str]:
        """
        Split text into chunks with overlap
        
        Args:
            text: Text to chunk
            chunk_size: Maximum size of each chunk
            overlap: Number of characters to overlap between chunks
            min_chunk_size: Minimum size of a chunk
            
        Returns:
            List of text chunks
        """
        if not text or len(text) <= chunk_size:
            return [self.clean_text(text)] if text else []
        
        text = self.clean_text(text)
        chunks = []
        
        # Try to split at natural boundaries
        current_chunks = [text]
        
        for separator in self.chunk_separators:
            new_chunks = []
            
            for chunk in current_chunks:
                if len(chunk) <= chunk_size:
                    new_chunks.append(chunk)
                else:
                    # Split by current separator
                    parts = chunk.split(separator)
                    current_chunk = ""
                    
                    for part in parts:
                        if len(current_chunk + separator + part) <= chunk_size:
                            if current_chunk:
                                current_chunk += separator + part
                            else:
                                current_chunk = part
                        else:
                            if current_chunk:
                                new_chunks.append(current_chunk)
                                # Add overlap
                                overlap_text = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
                                current_chunk = overlap_text + separator + part if overlap_text else part
                            else:
                                current_chunk = part
                    
                    if current_chunk:
                        new_chunks.append(current_chunk)
            
            current_chunks = new_chunks
            
            # Check if all chunks are now small enough
            if all(len(chunk) <= chunk_size for chunk in current_chunks):
                break
        
        # Filter out chunks that are too small
        final_chunks = [chunk for chunk in current_chunks if len(chunk) >= min_chunk_size]
        
        # If we still have oversized chunks, force split them
        final_final_chunks = []
        for chunk in final_chunks:
            if len(chunk) <= chunk_size:
                final_final_chunks.append(chunk)
            else:
                # Force split large chunks
                for i in range(0, len(chunk), chunk_size - overlap):
                    sub_chunk = chunk[i:i + chunk_size]
                    if len(sub_chunk) >= min_chunk_size:
                        final_final_chunks.append(sub_chunk)
        
        logger.debug(f"Split text of {len(text)} chars into {len(final_final_chunks)} chunks")
        return final_final_chunks
    
    def extract_metadata_from_text(self, text: str) -> Dict[str, Any]:
        """
        Extract metadata from text content
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with extracted metadata
        """
        metadata = {}
        
        # Extract potential title (first line if it looks like a title)
        lines = text.split('\n')
        if lines:
            first_line = lines[0].strip()
            if len(first_line) < 100 and not first_line.endswith('.'):
                metadata['extracted_title'] = first_line
        
        # Extract email addresses
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        if emails:
            metadata['emails'] = list(set(emails))
        
        # Extract URLs
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
        if urls:
            metadata['urls'] = list(set(urls))
        
        # Extract dates (simple patterns)
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
            r'\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
        ]
        
        dates = []
        for pattern in date_patterns:
            dates.extend(re.findall(pattern, text))
        
        if dates:
            metadata['extracted_dates'] = list(set(dates))
        
        # Basic text statistics
        metadata['char_count'] = len(text)
        metadata['word_count'] = len(text.split())
        metadata['line_count'] = len(text.split('\n'))
        
        # Extract potential hashtags
        hashtags = re.findall(r'#\w+', text)
        if hashtags:
            metadata['hashtags'] = list(set(hashtags))
        
        # Extract potential mentions
        mentions = re.findall(r'@\w+', text)
        if mentions:
            metadata['mentions'] = list(set(mentions))
        
        return metadata
    
    def detect_language(self, text: str) -> str:
        """
        Simple language detection (basic heuristics)
        
        Args:
            text: Text to analyze
            
        Returns:
            Detected language code
        """
        # Very basic language detection
        # In production, you might want to use a proper language detection library
        
        if not text:
            return "unknown"
        
        # Count common words in different languages
        english_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an']
        spanish_words = ['el', 'la', 'y', 'o', 'pero', 'en', 'con', 'por', 'para', 'de', 'que', 'es', 'un', 'una']
        french_words = ['le', 'la', 'et', 'ou', 'mais', 'dans', 'sur', 'avec', 'par', 'pour', 'de', 'que', 'est', 'un', 'une']
        
        text_lower = text.lower()
        words = text_lower.split()
        
        if len(words) < 10:
            return "unknown"
        
        english_count = sum(1 for word in words if word in english_words)
        spanish_count = sum(1 for word in words if word in spanish_words)
        french_count = sum(1 for word in words if word in french_words)
        
        total_words = len(words)
        english_ratio = english_count / total_words
        spanish_ratio = spanish_count / total_words
        french_ratio = french_count / total_words
        
        if english_ratio > 0.05:
            return "en"
        elif spanish_ratio > 0.05:
            return "es"
        elif french_ratio > 0.05:
            return "fr"
        else:
            return "unknown"
    
    def summarize_text(self, text: str, max_length: int = 200) -> str:
        """
        Create a simple extractive summary
        
        Args:
            text: Text to summarize
            max_length: Maximum length of summary
            
        Returns:
            Text summary
        """
        if len(text) <= max_length:
            return text
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return text[:max_length] + "..."
        
        # Take first few sentences that fit within max_length
        summary = ""
        for sentence in sentences:
            if len(summary + sentence + ". ") <= max_length:
                summary += sentence + ". "
            else:
                break
        
        if not summary:
            # If no complete sentences fit, truncate the first sentence
            summary = sentences[0][:max_length-3] + "..."
        
        return summary.strip()