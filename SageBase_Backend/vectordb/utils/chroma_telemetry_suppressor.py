"""
ChromaDB Telemetry Suppressor
This module suppresses ChromaDB telemetry to avoid version compatibility errors.
Import this module before any ChromaDB imports to disable telemetry completely.
"""

import os
import warnings
import logging

# Suppress ChromaDB telemetry environment variables
os.environ['ANONYMIZED_TELEMETRY'] = 'False'
os.environ['CHROMA_TELEMETRY'] = 'False'
os.environ['CHROMA_CLIENT_AUTH_PROVIDER'] = ''
os.environ['CHROMA_CLIENT_AUTH_CREDENTIALS'] = ''
os.environ['CHROMA_API_IMPL'] = 'chromadb.api.fastapi.FastAPI'
os.environ['CHROMA_SERVER_NOFILE'] = '65536'

# Additional telemetry suppression
os.environ['CHROMA_TELEMETRY_IMPL'] = 'chromadb.telemetry.product.posthog.Posthog'
os.environ['CHROMA_TELEMETRY_DISABLED'] = 'True'

def suppress_chroma_warnings():
    """Suppress specific ChromaDB telemetry warnings"""
    # Completely disable ChromaDB telemetry loggers
    logging.getLogger('chromadb.telemetry').setLevel(logging.CRITICAL)
    logging.getLogger('chromadb.telemetry.product').setLevel(logging.CRITICAL)
    logging.getLogger('chromadb.telemetry.product.posthog').setLevel(logging.CRITICAL)
    
    # Also disable any posthog-related loggers
    logging.getLogger('posthog').setLevel(logging.CRITICAL)
    logging.getLogger('chromadb').setLevel(logging.WARNING)  # Only show warnings and above for main chromadb
    
    # Create a custom filter to completely block telemetry messages
    class TelemetryFilter(logging.Filter):
        def filter(self, record):
            # Block any message containing telemetry-related keywords
            message = record.getMessage().lower()
            blocked_keywords = [
                'telemetry', 'capture()', 'posthog', 'clientstartevent', 
                'clientcreatecollectionevent', 'collectionaddevent', 
                'collectionqueryevent', 'collectiongetevent'
            ]
            return not any(keyword in message for keyword in blocked_keywords)
    
    # Apply the filter to the root logger
    root_logger = logging.getLogger()
    telemetry_filter = TelemetryFilter()
    root_logger.addFilter(telemetry_filter)
    
    # Filter out telemetry-related warnings
    def warning_filter(message, category, filename, lineno, file=None, line=None):
        message_str = str(message)
        if any(keyword in message_str.lower() for keyword in ['telemetry', 'capture()', 'posthog']):
            return  # Suppress telemetry warnings
        # Show all other warnings
        return True
    
    warnings.showwarning = warning_filter

# Auto-suppress when module is imported
suppress_chroma_warnings()

def create_chroma_client_with_suppressed_telemetry(*args, **kwargs):
    """Create ChromaDB client with telemetry completely suppressed"""
    import chromadb
    from chromadb.config import Settings
    
    # Ensure telemetry is disabled in settings
    if 'settings' not in kwargs:
        kwargs['settings'] = Settings()
    
    # Force disable telemetry in settings
    kwargs['settings'].anonymized_telemetry = False
    kwargs['settings'].allow_reset = True
    
    # Create client with suppressed warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        try:
            return chromadb.PersistentClient(*args, **kwargs)
        except Exception as e:
            # If telemetry error, try without problematic settings
            error_msg = str(e)
            if "telemetry" in error_msg.lower() or "capture()" in error_msg:
                print(f"[INFO] ChromaDB telemetry warning suppressed: {error_msg}")
                # Try with minimal settings
                try:
                    return chromadb.PersistentClient(path=kwargs.get('path', './chroma_db'))
                except Exception as fallback_error:
                    print(f"[ERROR] Failed to create ChromaDB client: {fallback_error}")
                    raise
            else:
                raise

print("[INFO] ChromaDB telemetry suppression loaded") 