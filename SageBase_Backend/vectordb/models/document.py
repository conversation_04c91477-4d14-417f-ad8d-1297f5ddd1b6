from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum


class DataSource(str, Enum):
    CONFLUENCE = "confluence"
    GOOGLE_DRIVE = "google_drive"
    NOTION = "notion"
    JIRA = "jira"
    LOCAL = "local"
    OTHER = "other"


@dataclass
class DocumentMetadata:
    """Metadata for documents stored in ChromaDB"""
    source: DataSource
    source_id: str  # Unique ID from the source system
    title: str
    author: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    url: Optional[str] = None
    content_type: Optional[str] = None  # text, pdf, doc, etc.
    tags: List[str] = field(default_factory=list)
    parent_id: Optional[str] = None  # For hierarchical content
    workspace: Optional[str] = None  # Team/workspace identifier
    permissions: List[str] = field(default_factory=list)  # Access control
    custom_fields: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for ChromaDB metadata (filters out None values)"""
        data = {
            "source": self.source.value,
            "source_id": self.source_id,
            "title": self.title,
        }
        
        # Only add non-None values
        if self.author:
            data["author"] = self.author
        if self.created_at:
            data["created_at"] = self.created_at.isoformat()
        if self.updated_at:
            data["updated_at"] = self.updated_at.isoformat()
        if self.url:
            data["url"] = self.url
        if self.content_type:
            data["content_type"] = self.content_type
        if self.tags:
            data["tags"] = ",".join(self.tags)  # Convert list to comma-separated string
        if self.parent_id:
            data["parent_id"] = self.parent_id
        if self.workspace:
            data["workspace"] = self.workspace
        if self.permissions:
            data["permissions"] = ",".join(self.permissions)  # Convert list to comma-separated string
        
        # Add custom fields (filter out None values)
        for key, value in self.custom_fields.items():
            if value is not None:
                data[key] = value
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DocumentMetadata':
        """Create DocumentMetadata from dictionary"""
        custom_fields = {k: v for k, v in data.items() if k not in {
            'source', 'source_id', 'title', 'author', 'created_at', 'updated_at',
            'url', 'content_type', 'tags', 'parent_id', 'workspace', 'permissions'
        }}
        
        return cls(
            source=DataSource(data['source']),
            source_id=data['source_id'],
            title=data['title'],
            author=data.get('author'),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None,
            url=data.get('url'),
            content_type=data.get('content_type'),
            tags=data.get('tags', '').split(',') if data.get('tags') else [],
            parent_id=data.get('parent_id'),
            workspace=data.get('workspace'),
            permissions=data.get('permissions', '').split(',') if data.get('permissions') else [],
            custom_fields=custom_fields
        )


@dataclass
class Document:
    """Document for ChromaDB storage"""
    id: str
    content: str
    metadata: DocumentMetadata
    chunk_index: int = 0  # For chunked documents
    total_chunks: int = 1
    
    def get_chroma_id(self) -> str:
        """Generate ChromaDB ID"""
        if self.total_chunks > 1:
            return f"{self.id}_chunk_{self.chunk_index}"
        return self.id
    
    def to_chroma_format(self) -> Dict[str, Any]:
        """Convert to ChromaDB format"""
        return {
            "id": self.get_chroma_id(),
            "document": self.content,
            "metadata": {
                **self.metadata.to_dict(),
                "chunk_index": self.chunk_index,
                "total_chunks": self.total_chunks,
                "original_id": self.id
            }
        }


@dataclass
class SearchResult:
    """Search result from ChromaDB"""
    id: str
    content: str
    metadata: DocumentMetadata
    distance: float
    score: float  # Similarity score (1 - distance)
    chunk_index: int = 0
    total_chunks: int = 1
    
    @classmethod
    def from_chroma_result(cls, chroma_id: str, content: str, metadata: Dict[str, Any], distance: float) -> 'SearchResult':
        """Create SearchResult from ChromaDB query result"""
        # Extract original ID and chunk info
        original_id = metadata.get('original_id', chroma_id.split('_chunk_')[0])
        chunk_index = metadata.get('chunk_index', 0)
        total_chunks = metadata.get('total_chunks', 1)
        
        # Remove internal fields from metadata and map fields correctly
        clean_metadata = {k: v for k, v in metadata.items() 
                         if k not in ['chunk_index', 'total_chunks', 'original_id']}
        
        # Map original_id to source_id for DocumentMetadata
        if 'original_id' in metadata:
            clean_metadata['source_id'] = metadata['original_id']
        elif 'chunk_id' in metadata:
            clean_metadata['source_id'] = metadata['chunk_id']
        else:
            clean_metadata['source_id'] = chroma_id
        
        return cls(
            id=original_id,
            content=content,
            metadata=DocumentMetadata.from_dict(clean_metadata),
            distance=distance,
            score=max(0, 1 - distance),  # Convert distance to similarity score
            chunk_index=chunk_index,
            total_chunks=total_chunks
        )


@dataclass
class SearchQuery:
    """Search query parameters"""
    query: str
    sources: Optional[List[DataSource]] = None
    workspace: Optional[str] = None
    content_type: Optional[str] = None
    tags: Optional[List[str]] = None
    author: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = 10
    similarity_threshold: float = 0.4
    metadata_filters: Optional[Dict[str, Any]] = None
    # New parameters for hybrid search
    search_mode: str = "hybrid"  # "semantic", "textual", "hybrid"
    textual_boost: float = 1.0  # Boost factor for textual search results
    semantic_boost: float = 1.0  # Boost factor for semantic search results
    
    def to_chroma_filter(self) -> Optional[Dict[str, Any]]:
        """Convert to ChromaDB where filter"""
        filter_conditions = []
        
        if self.sources:
            # Handle both DataSource enums and strings
            source_values = []
            for source in self.sources:
                if hasattr(source, 'value'):
                    # It's a DataSource enum
                    source_values.append(source.value)
                else:
                    # It's a string, use it directly
                    source_values.append(str(source))
            
            if len(source_values) == 1:
                filter_conditions.append({"source": {"$eq": source_values[0]}})
            else:
                filter_conditions.append({"source": {"$in": source_values}})
        
        if self.workspace:
            filter_conditions.append({"workspace": {"$eq": self.workspace}})
        
        if self.content_type:
            filter_conditions.append({"content_type": {"$eq": self.content_type}})
        
        if self.author:
            filter_conditions.append({"author": {"$eq": self.author}})
        
        if self.tags:
            # ChromaDB supports array contains operations
            for tag in self.tags:
                filter_conditions.append({"tags": {"$contains": tag}})
        
        if self.date_from or self.date_to:
            if self.date_from and self.date_to:
                filter_conditions.append({
                    "updated_at": {
                        "$gte": self.date_from.isoformat(),
                        "$lte": self.date_to.isoformat()
                    }
                })
            elif self.date_from:
                filter_conditions.append({"updated_at": {"$gte": self.date_from.isoformat()}})
            elif self.date_to:
                filter_conditions.append({"updated_at": {"$lte": self.date_to.isoformat()}})
        
        if self.metadata_filters:
            for key, value in self.metadata_filters.items():
                if isinstance(value, dict):
                    filter_conditions.append({key: value})
                else:
                    filter_conditions.append({key: {"$eq": value}})
        
        # Return properly formatted filter
        if not filter_conditions:
            return None
        elif len(filter_conditions) == 1:
            return filter_conditions[0]
        else:
            return {"$and": filter_conditions}