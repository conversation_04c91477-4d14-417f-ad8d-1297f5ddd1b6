#!/usr/bin/env python3
"""
ChromaDB Inspection Script
-------------------------
This script inspects the contents of the ChromaDB vector database instance used by SageBase.
It prints collection stats, lists documents, and shows metadata and sample content for each collection.

Usage:
  python scripts/inspect_chromadb.py [--workspace WORKSPACE] [--limit N]

- Default workspace: 'sagebase'
- Default sample limit: 5 documents per collection

This script is safe to run and does not modify any data.
"""
import sys
import argparse
from pprint import pprint
from vectordb.interfaces import get_stats, list_workspace_documents_by_metadata
from vectordb.services.chroma_service import get_chroma_service

def main():
    parser = argparse.ArgumentParser(description="Inspect ChromaDB contents for a workspace.")
    parser.add_argument('--workspace', type=str, default='sagebase', help='Workspace to inspect (default: sagebase)')
    parser.add_argument('--limit', type=int, default=5, help='Number of sample documents to show per collection (default: 5)')
    args = parser.parse_args()

    print("\n🔍 Inspecting ChromaDB for workspace:", args.workspace)
    print("="*60)

    # Print overall stats
    stats = get_stats(collection_name=args.workspace)
    print("\n📊 ChromaDB Stats:")
    pprint(stats)

    # Get ChromaDB service and list collections
    chroma = get_chroma_service()
    collections = chroma.list_collections()
    print(f"\n📚 Collections found: {collections}")

    for col in collections:
        print(f"\n--- Collection: {col} ---")
        info = chroma.get_collection_info(col)
        pprint(info)
        print(f"\nSample documents in collection '{col}':")
        # List documents by workspace (may include all collections)
        docs = list_workspace_documents_by_metadata(collection_name=args.workspace)
        # Filter for this collection
        docs_in_col = [d for d in docs if getattr(d, 'metadata', None) and d.metadata.source.value.lower() == col.lower()]
        for i, doc in enumerate(docs_in_col[:args.limit]):
            print(f"\n  Document {i+1}:")
            print(f"    Title: {getattr(doc.metadata, 'title', None)}")
            print(f"    Author: {getattr(doc.metadata, 'author', None)}")
            print(f"    Source ID: {getattr(doc.metadata, 'source_id', None)}")
            print(f"    Content Type: {getattr(doc.metadata, 'content_type', None)}")
            print(f"    Tags: {getattr(doc.metadata, 'tags', None)}")
            print(f"    URL: {getattr(doc.metadata, 'url', None)}")
            print(f"    Custom Fields: {getattr(doc.metadata, 'custom_fields', None)}")
            print(f"    Content (truncated): {doc.content[:200]}{'...' if len(doc.content) > 200 else ''}")
        if not docs_in_col:
            print("    (No documents found in this collection for this workspace)")
    print("\n✅ Inspection complete.")
    print("="*60)
    print("\nSummary:")
    print(f"  Total collections: {len(collections)}")
    print(f"  Workspace inspected: {args.workspace}")
    print(f"  Sample limit per collection: {args.limit}")

if __name__ == "__main__":
    main() 