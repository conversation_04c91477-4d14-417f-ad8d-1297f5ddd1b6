import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..models.document import DataSource, DocumentMetadata, SearchQuery, SearchResult
from ..services.document_service import DocumentService
from ..services.chroma_service import get_configured_chroma_service

logger = logging.getLogger(__name__)


class CollectionManager:
    """
    Manages a single workspace collection and provides unified operations across all data sources
    """
    
    def __init__(self, collection_name: str):
        self.chroma_service = get_configured_chroma_service()
        self.collection_name = collection_name
        self._service: Optional[DocumentService] = None
        logger.info(f"Initialized CollectionManager for collection: '{collection_name}'")
    
    def get_service(self) -> DocumentService:
        """
        Get or create the document service for this workspace collection
        
        Returns:
            DocumentService instance for the workspace collection
        """
        if self._service is None:
            self._service = DocumentService(self.collection_name)
            logger.info(f"Created document service for collection: '{self.collection_name}'")
        
        return self._service
    
    def add_document(self, 
                    content: str,
                    source: DataSource,
                    source_id: str,
                    title: str,
                    **metadata_kwargs) -> List[str]:
        """
        Add a document to the appropriate collection
        
        Args:
            content: Document content
            source: Data source
            source_id: Source-specific ID
            title: Document title
            **metadata_kwargs: Additional metadata fields
            
        Returns:
            List of document IDs that were added
        """
        try:
            # Normalize workspace to lowercase for consistency (minimal letters convention)
            
            # Create metadata
            metadata = DocumentMetadata(
                source=source,
                source_id=source_id,
                title=title,
                created_at=metadata_kwargs.get('created_at'),
                updated_at=metadata_kwargs.get('updated_at', datetime.now()),
                author=metadata_kwargs.get('author'),
                url=metadata_kwargs.get('url'),
                content_type=metadata_kwargs.get('content_type', 'text'),
                tags=metadata_kwargs.get('tags', []),
                parent_id=metadata_kwargs.get('parent_id'),
                permissions=metadata_kwargs.get('permissions', []),
                custom_fields=metadata_kwargs.get('custom_fields', {})
            )
            
            # Get workspace collection service and add document
            service = self.get_service()
            document_id = metadata_kwargs.get('document_id', source_id)
            
            return service.add_document(
                content=content,
                metadata=metadata,
                document_id=document_id,
                chunk_size=metadata_kwargs.get('chunk_size', 400),
                chunk_overlap=metadata_kwargs.get('chunk_overlap', 100)
            )
            
        except Exception as e:
            logger.error(f"Failed to add document from {source.value}: {e}")
            raise
    
    def search_across_sources(self, 
                            query: str,
                            sources: Optional[List[DataSource]] = None,
                            limit: int = 10,
                            search_mode: str = "hybrid",
                            textual_boost: float = 1.0,
                            semantic_boost: float = 1.0,
                            **filter_kwargs) -> List[SearchResult]:
        """
        Search across multiple data sources using hybrid search within this workspace collection
        
        Args:
            query: Search query
            sources: List of sources to search (None for all)
            limit: Maximum total results
            search_mode: Search mode - "semantic", "textual", or "hybrid" (default)
            textual_boost: Boost factor for textual search results
            semantic_boost: Boost factor for semantic search results
            **filter_kwargs: Additional search filters
            
        Returns:
            Combined search results from all sources in this workspace collection
        """
        # Search in the workspace collection across all sources
        try:
            service = self.get_service()
            
            #always add LOCAL source to the search
            if sources and DataSource.LOCAL not in sources:
                sources.append(DataSource.LOCAL)
            
            # Create search query with source filtering if specified
            search_query = SearchQuery(
                query=query,
                sources=sources,  # Filter by sources if specified
                limit=limit * 2 if sources else limit,  # Get more results if filtering by sources
                similarity_threshold=filter_kwargs.get('similarity_threshold', 0.4),
                content_type=filter_kwargs.get('content_type'),
                tags=filter_kwargs.get('tags'),
                author=filter_kwargs.get('author'),
                date_from=filter_kwargs.get('date_from'),
                date_to=filter_kwargs.get('date_to'),
                metadata_filters=filter_kwargs.get('metadata_filters'),
                search_mode=search_mode,
                textual_boost=textual_boost,
                semantic_boost=semantic_boost
            )
            
            results = service.search(search_query)
            
            # Filter by sources if specified
            if sources:
                results = [r for r in results if r.metadata.source in sources]
            
            # Sort by similarity score and limit
            results.sort(key=lambda x: x.score, reverse=True)
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Search failed for collection {self.collection_name}: {e}")
            return []
    
    def search_in_source(self, 
                        query: str,
                        source: DataSource,
                        limit: int = 10,
                        search_mode: str = "hybrid",
                        textual_boost: float = 1.0,
                        semantic_boost: float = 1.0,
                        **filter_kwargs) -> List[SearchResult]:
        """
        Search within a specific data source using hybrid search
        
        Args:
            query: Search query
            source: Data source to search
            limit: Maximum results
            search_mode: Search mode - "semantic", "textual", or "hybrid" (default)
            textual_boost: Boost factor for textual search results
            semantic_boost: Boost factor for semantic search results
            **filter_kwargs: Additional search filters
            
        Returns:
            Search results from the specified source
        """
        try:
            service = self.get_service()
            
            # Normalize workspace to lowercase for consistency (minimal letters convention)
            workspace = filter_kwargs.get('workspace')
            if workspace:
                workspace = workspace.lower()
                filter_kwargs['workspace'] = workspace
                logger.info(f"Collection manager normalizing workspace for source search: '{workspace}'")
            
            search_query = SearchQuery(
                query=query,
                sources=[source],
                limit=limit,
                similarity_threshold=filter_kwargs.get('similarity_threshold', 0.4),
                workspace=workspace,
                content_type=filter_kwargs.get('content_type'),
                tags=filter_kwargs.get('tags'),
                author=filter_kwargs.get('author'),
                date_from=filter_kwargs.get('date_from'),
                date_to=filter_kwargs.get('date_to'),
                metadata_filters=filter_kwargs.get('metadata_filters'),
                search_mode=search_mode,
                textual_boost=textual_boost,
                semantic_boost=semantic_boost
            )
            
            results = service.search(search_query)
            # Filter by specific source
            return [r for r in results if r.metadata.source == source]
            
        except Exception as e:
            logger.error(f"Search failed for source {source.value} in collection {self.collection_name}: {e}")
            return []
    
    def get_document(self, source: DataSource, document_id: str) -> Optional[Any]:
        """
        Get a specific document from a data source
        
        Args:
            source: Data source
            document_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        try:
            service = self.get_service()
            return service.get_document(document_id)
        except Exception as e:
            logger.error(f"Failed to get document {document_id} from {source.value} in collection {self.collection_name}: {e}")
            return None
    
    def delete_document(self, source: DataSource, document_id: str) -> bool:
        """
        Delete a document from a data source
        
        Args:
            source: Data source
            document_id: Document ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            service = self.get_service()
            return service.delete_document(document_id)
        except Exception as e:
            logger.exception(f"Failed to delete document {document_id} from {source.value} in collection {self.collection_name}: {e}")
            return False
    
    def delete_all_documents(self) -> Dict[str, Any]:
        """
        Delete all documents from the collection using ChromaDB's native functionality
        
        Returns:
            Dictionary with deletion results
        """
        try:
            logger.info(f"Deleting all documents from collection: '{self.collection_name}'")
            
            # Get the document service which has access to the ChromaDB collection
            service = self.get_service()
            
            # Get the ChromaDB client from the service
            chroma_client = service.chroma_service.client
            
            # Delete the entire collection
            chroma_client.delete_collection(name=self.collection_name)
            
            # Reset the service's collection reference since it's now deleted
            service._collection = None
            
            logger.info(f"Successfully deleted all documents from collection: '{self.collection_name}'")
            
            return {
                "success": True,
                "collection_name": self.collection_name,
                "message": f"All documents deleted from collection '{self.collection_name}'",
                "deleted_count": "all",
                "errors": []
            }
            
        except Exception as e:
            logger.exception(f"Failed to delete all documents from collection '{self.collection_name}': {e}")
            return {
                "success": False,
                "collection_name": self.collection_name,
                "error": str(e),
                "deleted_count": 0,
                "errors": [str(e)]
            }
        
    
    def get_source_stats(self, source: DataSource) -> Dict[str, Any]:
        """
        Get statistics for a specific data source
        
        Args:
            source: Data source
            
        Returns:
            Statistics dictionary
        """
        try:
            service = self.get_service()
            stats = service.get_collection_stats()
            # Filter stats by source if needed - for now return full stats
            # In the future, we could add source-specific stats here
            return stats
        except Exception as e:
            logger.error(f"Failed to get stats for {source.value} in collection {self.collection_name}: {e}")
            return {"error": str(e)}
    
    def get_all_stats(self) -> Dict[str, Any]:
        """
        Get statistics for this workspace collection
        
        Returns:
            Collection statistics
        """
        try:
            service = self.get_service()
            collection_stats = service.get_collection_stats()
            
            return {
                "collection_name": self.collection_name,
                "total_documents": collection_stats.get("total_documents", 0),
                "sources": collection_stats.get("sources", {}),
                "collection_stats": collection_stats
            }
        except Exception as e:
            logger.error(f"Failed to get stats for collection {self.collection_name}: {e}")
            return {"error": str(e)}
    
    def clear_source(self, source: DataSource) -> bool:
        """
        Clear all documents from a data source
        
        Args:
            source: Data source to clear
            
        Returns:
            True if successful, False otherwise
        """
        try:
            service = self.get_service()
            return service.clear_collection()
        except Exception as e:
            logger.error(f"Failed to clear collection {self.collection_name}: {e}")
            return False
    
    def bulk_import(self, 
                   source: DataSource,
                   documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Bulk import documents to a data source
        
        Args:
            source: Target data source
            documents: List of document dictionaries
            
        Returns:
            Import results
        """
        try:
            service = self.get_service()
            
            # Convert dictionaries to proper format
            formatted_docs = []
            for doc in documents:
                # Ensure metadata is properly formatted
                metadata = doc.get('metadata', {})
                if not isinstance(metadata, DocumentMetadata):
                    # Add required fields if missing
                    metadata.setdefault('source', source.value)
                    metadata.setdefault('source_id', doc.get('id', str(datetime.now().timestamp())))
                    metadata.setdefault('title', doc.get('title', 'Untitled'))
                    
                formatted_docs.append({
                    'content': doc['content'],
                    'metadata': metadata,
                    'id': doc.get('id')
                })
            
            added_ids = service.add_documents(formatted_docs)
            
            return {
                "success": True,
                "source": source.value,
                "documents_processed": len(documents),
                "chunks_created": len(added_ids),
                "added_ids": added_ids
            }
            
        except Exception as e:
            logger.error(f"Bulk import failed for {source.value}: {e}")
            return {
                "success": False,
                "source": source.value,
                "error": str(e)
            }
    
    def health_check(self) -> Dict[str, Any]:
        """
        Check health of all collections and services
        
        Returns:
            Health status
        """
        health = {
            "overall_status": "healthy",
            "chroma_service": self.chroma_service.health_check(),
            "collection": {},
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            if self.chroma_service.collection_exists(self.collection_name):
                stats = self.get_all_stats()
                if "error" not in stats:
                    health["collection"] = {
                        "name": self.collection_name,
                        "status": "healthy",
                        "document_count": stats.get("total_documents", 0)
                    }
                else:
                    health["collection"] = {
                        "name": self.collection_name,
                        "status": "error",
                        "error": stats["error"]
                    }
                    health["overall_status"] = "degraded"
            else:
                health["collection"] = {
                    "name": self.collection_name,
                    "status": "not_initialized",
                    "document_count": 0
                }
        except Exception as e:
            health["collection"] = {
                "name": self.collection_name,
                "status": "error",
                "error": str(e)
            }
            health["overall_status"] = "degraded"
        
        return health


# Global collection managers per workspace
_collection_managers: Dict[str, CollectionManager] = {}

def get_collection_manager(collection_name) -> CollectionManager:
    """Get ChromaDB collection manager instance for a specific workspace/collection
    
    Args:
        collection_name: Name of the workspace/collection (REQUIRED)
    
    Returns:
        CollectionManager instance for the specified workspace
    """
    global _collection_managers
    collection_name = str(collection_name)
    
  
    logger.info(f"Getting collection manager for workspace: '{collection_name}'")
    
    if collection_name not in _collection_managers:
        _collection_managers[collection_name] = CollectionManager(collection_name)
        logger.info(f"Created new CollectionManager for workspace: '{collection_name}'")
    
    return _collection_managers[collection_name]