#!/usr/bin/env python3
"""
Simple script to show all ChromaDB collections with their documents and tags
Reads configuration from .env.dev file
"""

import os
import chromadb
from dotenv import load_dotenv

def show_collections_with_documents():
    """Show all ChromaDB collections with their individual documents"""
    
    print("📊 Showing ChromaDB collections with documents...")
    
    try:
        # Load environment variables from .env.dev
        load_dotenv('.env.dev')
        
        # Get ChromaDB configuration from environment
        chroma_host = os.getenv('CHROMA_HOST')
        chroma_port = os.getenv('CHROMA_PORT')
        chroma_persist_dir = os.getenv('CHROMA_PERSIST_DIR', './chroma_db')
        
        print(f"📋 Configuration from .env.dev:")
        print(f"   CHROMA_HOST: {chroma_host or 'Not set'}")
        print(f"   CHROMA_PORT: {chroma_port or 'Not set'}")
        print(f"   CHROMA_PERSIST_DIR: {chroma_persist_dir}")
        print()
        
        # Connect to ChromaDB based on configuration
        if chroma_host and chroma_port:
            # Cloud/Remote ChromaDB
            print(f"☁️  Connecting to cloud ChromaDB at {chroma_host}:{chroma_port}")
            clean_host = chroma_host.replace('https://', '').replace('http://', '')
            client = chromadb.HttpClient(host=clean_host, port=int(chroma_port))
        else:
            # Local ChromaDB
            print(f"💾 Connecting to local ChromaDB at: {chroma_persist_dir}")
            client = chromadb.PersistentClient(path=chroma_persist_dir)
        
        # Get all collections
        collections = client.list_collections()
        print(f"📊 Found {len(collections)} collections")
        print()
        
        if not collections:
            print("ℹ️  ChromaDB is empty - no collections found")
            return
        
        # Show each collection with its documents
        total_documents = 0
        collections_with_documents = 0
        
        for collection in collections:
            count = collection.count()
            total_documents += count
            
            print(f"📁 Collection: {collection.name}")
            print(f"   📄 Documents: {count}")
            
            # Get collection metadata
            metadata = collection.metadata or {}
            print(f"   🏷️  Metadata: {metadata}")
            print()
            
            if count > 0:
                collections_with_documents += 1
                try:
                    # Get all documents in the collection
                    results = collection.get(limit=count)
                    
                    # Check if results is valid and contains the expected data
                    if results is None:
                        print(f"   ❌ Error: collection.get() returned None for {collection.name}")
                        continue
                    
                    if not isinstance(results, dict):
                        print(f"   ❌ Error: collection.get() returned unexpected type {type(results)} for {collection.name}")
                        continue
                    
                    if 'ids' not in results or results['ids'] is None:
                        print(f"   ❌ Error: No 'ids' found in results for {collection.name}")
                        continue
                    
                    if not results['ids']:
                        print(f"   ℹ️  No documents found in {collection.name}")
                        continue
                    
                    print(f"   📋 Documents in {collection.name}:")
                    print("   " + "="*50)
                    
                    for i, doc_id in enumerate(results['ids'], 1):
                        print(f"   📄 Document {i}:")
                        print(f"      ID: {doc_id}")
                        
                        # Get document content
                        if 'documents' in results and results['documents'] and i-1 < len(results['documents']):
                            content = results['documents'][i-1]
                            # Truncate content if too long
                            if content and len(content) > 200:
                                content = content[:200] + "..."
                            print(f"      Content: {content}")
                        
                        # Get document metadata
                        if 'metadatas' in results and results['metadatas'] and i-1 < len(results['metadatas']):
                            doc_metadata = results['metadatas'][i-1]
                            if doc_metadata:
                                print(f"      Metadata: {doc_metadata}")
                                
                                # Show tags if present
                                if 'tags' in doc_metadata:
                                    tags = doc_metadata['tags']
                                    if isinstance(tags, list):
                                        print(f"      Tags: {tags}")
                                    elif isinstance(tags, str):
                                        print(f"      Tags: {tags}")
                        
                        # Get document embeddings info
                        if 'embeddings' in results and results['embeddings'] and i-1 < len(results['embeddings']):
                            embedding = results['embeddings'][i-1]
                            if embedding:
                                print(f"      Embedding: {len(embedding)} dimensions")
                        
                        print()
                    
                    print(f"   ✅ Total documents in {collection.name}: {len(results['ids'])}")
                        
                except Exception as e:
                    print(f"   ❌ Error reading documents from {collection.name}: {e}")
            else:
                print(f"   ℹ️  Empty collection - no documents to show")
            
            print("   " + "="*60)
            print()
        
        print(f"📊 Summary:")
        print(f"   Total collections: {len(collections)}")
        print(f"   Collections with documents: {collections_with_documents}")
        print(f"   Total documents: {total_documents}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    show_collections_with_documents() 