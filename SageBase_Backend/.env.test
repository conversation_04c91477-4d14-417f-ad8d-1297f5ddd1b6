# test:
# Development Configuration
DJANGO_ENV=test
DEBUG=True

# Azure PostgreSQL Database Configuration
DATABASE_URL=postgres://wissem:<EMAIL>:5432/sageBase_test_azure?sslmode=require

REDIS_HOST=sagebase_backend-redis-1
REDIS_PORT=6379
REDIS_DB=0
REDIS_URL=redis://sagebase_backend-redis-1:6379/0

#Github OAuth - will be deprecated
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=7419ea81049cae46715570300257eca52bbaaf9a
GEMINI_API_KEY=AIzaSyBlz6C2q29A0eR67hir6kWE2cDoH_IrcAc

# Github App


# Github App(SageBase.tech)
# GITHUB_APP_SLUG=sagebase-tech
# GITHUB_APP_ID=1646108
# GITHUB_APP_CLIENT_ID=********************
# GITHUB_APP_CLIENT_SECRET=faef13c2aef190cfe7b5b50de8fa3fc6060b46bc
# GITHUB_APP_WEBHOOK_SECRET="1test$2pass%3run!!"
GITHUB_APP_SLUG=sagebase-dev
GITHUB_APP_ID=1620329
GITHUB_APP_CLIENT_ID=********************
GITHUB_APP_CLIENT_SECRET=a0525a224bf1145c3ad71ee0eb79931ee2d1c5ea
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GITHUB_APP_WEBHOOK_SECRET="1test$2pass%3run!!"

# API Keys
GEMINI_API_KEY=AIzaSyBlz6C2q29A0eR67hir6kWE2cDoH_IrcAc
OPENAI_API_KEY=********************************************************************************************************************************************************************

# ChromaDB Configuration
ANONYMIZED_TELEMETRY=False
CHROMA_TELEMETRY_CAPTURE=False
CHROMA_EMBEDDING_TYPE=azure
CHROMA_EMBEDDING_MODEL=text-embedding-3-small

CHROMA_PERSIST_DIR=./chroma_db

# Internal chroma db container on the server, not accessible from external
CHROMA_PORT=8000
CHROMA_HOST=http://chromadb



# Base URLs
BACKEND_BASE_URL=https://sagebasetestserverazure.sagebase.tech:8000
FRONTEND_BASE_URL=https://sagebasetestserverazure.sagebase.tech


 
# ✅ FIXED: Use {BASE_URL} placeholder for dynamic URL generation
SLACK_WEBHOOK_URL={BASE_URL}/api/integrations/slack/webhook/receive/
SLACK_REDIRECT_URI={BASE_URL}/api/integrations/slack/callback/
DJANGO_WEBHOOK_URL=

# Discord Bot Configuration
DISCORD_BOT_TOKEN=MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls
DISCORD_CLIENT_ID=1391784811173122179
DISCORD_CLIENT_SECRET=g2oUQ7o4-UNx2Fe_5oISgNr8zXbOYA1S
DISCORD_REDIRECT_URI=https://sagebasetestserverazure.sagebase.tech/api/integrations/discord/callback/ # set on starting
DISCORD_BOT_AUTO_START=true

# Email settings
EMAIL_HOST=send.one.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=SageBase2025*
DEFAULT_FROM_EMAIL=SageBase


DJANGO_LOG_LEVEL=DEBUG

# Slack Configuration/ sagebase test app
SLACK_CLIENT_ID=9008663006947.9028923254790
SLACK_CLIENT_SECRET=0f1a20838581f8dd05aca15f48e27f6c
SLACK_SIGNING_SECRET=13b5d45f1e943f2be059bb2407d0d472
SLACK_BOT_TOKEN=*********************************************************
SLACK_APP_TOKEN=xapp-1-A0935J268UE-9118162536149-153dc6c7a64b61f4c780edc614ef92a6858e52627f0abdb17ddfffb0bd39372e


azure_gpt4_1_=gpt-4.1_france_SageBase
AZURE_GPT4O_MINI=o4-mini_france_sagebase
AZURE_GPT4O=gpt-4o_france_sagebase
azure_embedding_3_small=text-embedding-3-small
AZURE_OPENAI_ENDPOINT=https://sagebasemodels.openai.azure.com/
AZURE_ENDPOINT=https://sagebasemodels.openai.azure.com/openai/deployments/gpt-4.1_france_SageBase/chat/completions?api-version=2025-01-01-preview
AZURE_OPENAI_API_KEY=FFjJUbvnHNXAcFsMSOCKSovwgxRsEkfBFN6r2MlYzjmGi0ROXqLmJQQJ99BHAC5T7U2XJ3w3AAABACOGvgHv
api_version="2024-12-01-preview"
# Azure OpenAI Embedding Configuration
AZURE_EMBEDDING_ENDPOINT=https://sagebasemodels.openai.azure.com/
AZURE_EMBEDDING_DEPLOYMENT=text-embedding-3-small_france_sagebBase
AZURE_EMBEDDING_API_VERSION=2023-05-15


# Required variables:

AZURE_OPENAI_DEPLOYMENT=gpt-4.1_france_SageBase
AZURE_OPENAI_API_VERSION=2024-12-01-preview
# Optional variables:
AZURE_OPENAI_MODEL=gpt-4.1_france_SageBase


# Disable polling
GDRIVE_ENABLE_POLLER=True
GDRIVE_REDIRECT_URI=https://sagebasetestserverazure.sagebase.tech/api/integrations/google-drive/callback/
DISCORD_WEBHOOK_URL=https://sagebasetestserverazure.sagebase.tech/api/integrations/discord/webhook/


CONFLUENCE_CLIENT_ID=jz5oEkFDgcWxOFkOOo9qSp4si5vPU93x
CONFLUENCE_CLIENT_SECRET=ATOA6lolX8_m45bhgcNwnzJ2ViGlKdzwreH-GXABO0_psVi6iGSxjenw3L624famM4XrD89D0D6E
CONFLUENCE_REDIRECT_URI=https://sagebasetestserverazure.sagebase.tech/api/integrations/confluence/callback/

# disable it when you don't want an interruption each start
#BACKEND_NGROK_BASE_URL=https://sagebasetestserverazure.sagebase.tech
 