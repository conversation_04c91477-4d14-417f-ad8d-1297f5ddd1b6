"""
Examples of using the auto-async ORM in different contexts.
This shows how the same code works in both sync and async contexts.
"""

from integrations.models import User, Company, SlackUserProfile


# Example 1: Sync context (regular Django views, management commands, etc.)
def sync_example():
    """This function runs in sync context - no await needed"""
    
    # Regular ORM operations work as usual
    users = User.objects.filter(is_active=True)
    company = Company.objects.get(name="SageBase")
    
    # Create operations
    new_user = User.objects.create(
        email="<EMAIL>",
        company=company
    )
    
    # Update operations
    new_user.first_name = "Test"
    new_user.save()
    
    print(f"Created user: {new_user.email}")
    return new_user


# Example 2: Async context (webhooks, async views, etc.)
async def async_example():
    """This function runs in async context - await is required"""
    
    # Same ORM operations, but with await
    users = await User.objects.filter(is_active=True)
    company = await Company.objects.get(name="SageBase")
    
    # Create operations
    new_user = await User.objects.create(
        email="<EMAIL>",
        company=company
    )
    
    # Update operations  
    new_user.first_name = "Test"
    await new_user.save()
    
    print(f"Created user: {new_user.email}")
    return new_user


# Example 3: Mixed context function that works in both
def get_user_company_name(user_id):
    """This works in both sync and async contexts automatically!"""
    try:
        # The auto-async ORM detects the context and handles it
        user = User.objects.get(id=user_id)
        return user.company.name.lower().replace(' ', '_')
    except User.DoesNotExist:
        return None


# Example 4: Using in Slack webhook (async context)
async def slack_webhook_example(slack_user_id):
    """Example of how to use in async Slack webhook handlers"""
    
    # Find Slack profile
    profile = await SlackUserProfile.objects.get(
        slack_user_id=slack_user_id, 
        is_active=True
    )
    
    # Get related data
    user = profile.user
    company = user.company
    
    # Complex queries work too
    team_members = await User.objects.filter(
        company=company,
        is_active=True
    ).select_related('company')
    
    return {
        'user': user.email,
        'company': company.name,
        'team_size': len(team_members)
    }


# Example 5: Error handling in async context
async def safe_user_lookup(email):
    """Safe user lookup with error handling"""
    try:
        user = await User.objects.get(email=email)
        return user
    except User.DoesNotExist:
        print(f"User not found: {email}")
        return None
    except Exception as e:
        print(f"Error looking up user: {e}")
        return None


if __name__ == "__main__":
    # This demonstrates that the same function works in both contexts
    import asyncio
    
    # Sync usage
    print("=== Sync Context ===")
    result = get_user_company_name(1)
    print(f"Sync result: {result}")
    
    # Async usage  
    print("\n=== Async Context ===")
    async def test_async():
        result = await get_user_company_name(1)  # Same function!
        print(f"Async result: {result}")
    
    asyncio.run(test_async()) 