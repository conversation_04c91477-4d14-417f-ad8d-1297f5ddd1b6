# How to Test Discord Integration Locally

Please make a virtual environment if you don't have one, and install the requirements-dev.txt
If your have a virtual environment :

1. **Install the `discord.py` library** into your `.venv`:
    ```bash
    uv pip install discord.py
    ```

2. Inside **.env.dev** (BACKEND).
    ```
    BACKEND_NGROK_BASE_URL=YOUR NGROK URL
    DISCORD_REDIRECT_URI=https://af252a205051.ngrok-free.app/api/integrations/discord/callback/ #change to your ngrok
    ```

3. **Update DISCORD settings from https://discord.com/developers/applications** Ask the one responsible to fix it

4. **Start the backend server**:
    ```bash
    python manage.py runserver
    ```

5. **Run the frontend application**.

6. **Test the discord functionality:** Add discord, send message in the guild from discord