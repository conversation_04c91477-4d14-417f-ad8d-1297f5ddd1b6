# Question Answering Agent with Internet Search & Local Tools

A simple AI agent that answers user questions by searching both **the internet** and **local knowledge base**. Built with OpenAI Agents SDK.

## 🌟 Key Features

✅ **Internet Search** - Uses Serper API for current information  
✅ **Local Knowledge** - SQLite database with customizable knowledge base  
✅ **Company-Specific Info** - Add your own company/project knowledge  
✅ **Question Answering** - Natural language Q&A interface  
✅ **Source Citation** - Clear attribution of information sources  
✅ **Answer Storage** - Save Q&A for future reference  


## 🚀 Getting Started

### Step 1: Embed Your Data
First, ensure you have embedded your documents using the embedding script "SageBase/SageBase_BackEnd.git/vectordb/tests/embed_sageBaseFolder.py"

### Step 2: Launch the Chat Interface
Once your data is embedded, run the question answering script:

```bash
python konowledge_detection_agents/question_answering.py
```

This will launch a simple chat interface where you can interact with the AI agent.

## 🛠️ How It Works

```
User Question
     ↓
Question Agent
     ↓
┌─────────────┬─────────────┬─────────────┐
│   Internet  │    Local    │  Company    │
│   Search    │  Knowledge  │ Specific    │
│             │   Base      │    Info     │
└─────────────┴─────────────┴─────────────┘
     ↓
Combined Answer with Sources
```

## 🚀 Quick Start

### Installation
```bash
pip install openai-agents pydantic>=2.0.0 openai>=1.0.0 requests python-dotenv
```

### Environment Setup

**Option 1: Using .env File (Recommended)**
```bash
# Create .env file in the konowledge_detection_agents/ directory
echo "OPENAI_API_KEY=your-openai-api-key-here" > .env
```

**Option 2: Environment Variable**
```bash
export OPENAI_API_KEY="your-api-key-here"
```

### .env File Configuration
Create a `.env` file with the following content:
```bash
# Required: OpenAI API Key
OPENAI_API_KEY=your-openai-api-key-here

# Optional: Model settings
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.3
OPENAI_TIMEOUT=30
```

🔑 **Get your OpenAI API key**: [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)



### 3. Interactive Session
```bash
python konowledge_detection_agents/question_answering.py
# Choose option 2 for interactive mode
```

## 🛠️ Available Tools

### Internet Search Tool
- **Function**: `search_internet(query, max_results=3)`
- **Purpose**: Search Google for current information (using Serper API)
- **Returns**: Formatted internet search results

### Local Knowledge Tool  
- **Function**: `search_local_knowledge(query)`
- **Purpose**: Provides hardcoded local knowledge response
- **Returns**: Generic internal knowledge information about the topic

### Answer Storage Tool
- **Function**: `save_answer(question, answer, references)`
- **Purpose**: Print Q&A summary to console
- **Returns**: Confirmation of successful print

## 📚 Local Knowledge

The system provides hardcoded local knowledge responses for any topic:

### Coverage Areas:
- **Programming**: Best practices and implementation guidance
- **AI/ML**: General concepts and methodology  
- **Web Development**: Common patterns and approaches
- **Security**: Standard practices and recommendations

### Local Knowledge Response:
The `search_local_knowledge()` function returns generic but helpful information about any topic, providing:
- Best practices overview
- Implementation details reference  
- Troubleshooting guidance
- Step-by-step documentation pointers

This complements internet search results with consistent, reliable internal knowledge.

## 📋 Structured Output Format

The agent returns structured responses using Pydantic models:

### StructuredAnswer Model
```python
class StructuredAnswer(BaseModel):
    answer: str = Field(description="The comprehensive answer to the user's question")
    references: List[str] = Field(description="List of sources and references used")
```

### Example Output
```python
result = await qa_system.answer_question("How do React hooks work?")
structured_answer = result.final_output

print(f"Answer: {structured_answer.answer}")
print(f"References: {', '.join(structured_answer.references)}")
```

### Sample Response
```
Answer: React hooks are functions that let you use state and lifecycle features in functional components...

References: Internet Search - React Documentation, Local Knowledge, https://reactjs.org/docs/hooks-intro.html
```

## 🧪 Testing & Examples

### Run Examples
```bash
python konowledge_detection_agents/example_usage.py
```

### Test Options:
1. **Sample Questions** - Pre-defined questions across categories
2. **Interactive Q&A** - Ask your own questions in real-time  
3. **Comprehensive Demo** - All tests

### Sample Questions:
- "What are the latest developments in artificial intelligence?"
- "How do I implement JWT authentication in Python?"
- "What is the difference between REST and GraphQL?"
- "What are React hooks and how do they work?"
- "What are database design best practices?"

## ⚙️ Configuration

### User Context
```python
context = KnowledgeContext(
    user_id="your_user_id",
    preferences=["programming", "ai", "databases"]
)
```

### Model Settings & Tracing
```python
# Customize in the agent creation
ModelSettings(
    temperature=0.3,  # Lower = more focused
    max_tokens=1000   # Response length
)

# Agent with structured output
Agent(
    name="Question Answering Agent",
    instructions="...",
    model="gpt-4.1",
    output_type=StructuredAnswer  # Structured response format
)

# Tracing with context manager for organized output
with trace("Agent Operation"):
    result = await Runner.run(agent, message, context=context)
```

### Search Parameters
```python
# Internet search
search_internet(query="your question", max_results=5)

# Local knowledge search  
search_local_knowledge(query="your search term")
```

## 🔧 Advanced Usage

### Custom Local Knowledge
```python
# Modify the search_local_knowledge function in question_answering.py
@function_tool
def search_local_knowledge(query: str) -> str:
    """Search local knowledge base for information."""
    # Customize this response for your specific domain
    local_response = f"📚 Your Custom Knowledge: For '{query}' - add your specific information here..."
    return local_response
```

### Custom Tools
```python
from agents import function_tool

@function_tool
def your_custom_tool(query: str) -> str:
    """Your custom search/knowledge function"""
    # Your implementation
    return "Custom result"

# Add to agent tools when creating the agent
```

## 🛡️ Error Handling

The system includes robust error handling:
- **Internet connection issues** - Falls back to local knowledge
- **API rate limits** - Graceful degradation
- **Empty results** - Clear messaging to user
- **Invalid queries** - Input validation and suggestions

## 🔍 Tracing & Debugging

The system includes comprehensive tracing for monitoring agent behavior:

### Tracing Features:
- **Organized Output** - Trace context groups related operations together
- **Tool Usage Tracking** - See which tools the agent calls and why
- **Decision Process** - View the agent's reasoning for each step
- **Error Handling** - Detailed error traces for debugging
- **Performance Monitoring** - Track response times and API calls


# Manual tracing usage
from agents import trace

with trace("Question Answering"):
    result = await Runner.run(agent, message, context=context)
```

### Tracing Output:
```
🔧 [TRACE] Creating Question Answering Agent with tracing enabled...
🔍 [TRACE] Starting question processing: What are the latest AI developments?

[Question Answering] Starting agent execution...
[Question Answering] Calling tool: search_internet with query: "latest AI developments"
[Question Answering] Tool response received, processing...
[Question Answering] Calling tool: search_local_knowledge with query: "AI developments"
[Question Answering] Synthesizing final answer...
[Question Answering] Agent execution complete

✅ [TRACE] Question processing complete
🤖 Answer: AI has seen remarkable developments recently, including...
📚 References: Internet Search - AI News, Local Knowledge, https://openai.com/blog
```

## 📈 Performance Tips

- **Customize local knowledge** for your domain-specific needs
- **Monitor API usage** to stay within limits
- **Use appropriate contexts** for better results
- **Optimize internet search queries** for better results
- **Use tracing output** to optimize agent performance

## 🔒 Security & Privacy

- **No persistent storage** - Answers are printed to console only
- **API key security** - Environment variable based
- **No data sharing** - No external data transmission beyond OpenAI API
- **Minimal data handling** - No local database or file storage

## 🚨 Troubleshooting

### Common Issues:

**❌ ModuleNotFoundError: No module named 'agents'**
```bash
pip install openai-agents
```

**❌ OpenAI API key not found**
```bash
# Option 1: Create .env file (recommended)
echo "OPENAI_API_KEY=your-key-here" > .env

# Option 2: Export environment variable
export OPENAI_API_KEY="your-key-here"
```

**❌ Could not load .env file**
```bash
# Install python-dotenv if missing
pip install python-dotenv

# Check .env file exists in correct directory
ls -la .env

# Verify .env file format (no spaces around =)
cat .env
```

**❌ Internet search not working**
- Check internet connection
- Verify `requests` library: `pip install requests`

**❌ Local knowledge errors**
- Check that the function is properly defined
- Verify no syntax errors in the hardcoded response

## 🎯 Use Cases

### 1. Technical Support
- Answer programming questions
- Provide API documentation
- Troubleshoot technical issues

### 2. Knowledge Management
- Corporate knowledge base
- FAQ automation  
- Information retrieval

### 3. Research Assistant
- Combine internal and external knowledge
- Fact-checking and verification
- Information synthesis

### 4. Customer Support
- Product information queries
- Technical documentation lookup
- Issue resolution guidance

## 🤝 Contributing

### Adding Features:
1. Fork the repository
2. Add new tools in `question_answering.py`
3. Update tests in `example_usage.py`
4. Submit a pull request

### Extending Knowledge:
- Add entries to the local database
- Customize company-specific information
- Create domain-specific tools

## 📄 License

MIT License - see LICENSE file for details.

---

**🤖 Simple, powerful question answering with internet search and local knowledge!**

 