# github_agent.py
import os
import asyncio
import json
import datetime
from agents import Agent, Runner, OpenAIChatCompletionsModel, set_default_openai_client # Keep Runner for potential independent testing
from agents.mcp import MCPServerStreamableHttp
from agents.mcp.util import ToolFilterStatic
import logging

# Configure logging for this specific agent module
github_logger = logging.getLogger(__name__)
# If you want separate logging settings for this module, you can add them here
# github_logger.setLevel(logging.DEBUG) 
# handler = logging.StreamHandler()
# formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# handler.setFormatter(formatter)
# github_logger.addHandler(handler)

# Reduce verbosity of HTTP and MCP logs
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('mcp.client').setLevel(logging.WARNING)

# THIS IS THE CRUCIAL LINE for seeing the sub-agent's internal thoughts
logging.getLogger('agents').setLevel(logging.DEBUG) 

from konowledge_detection_agents.azure_config import async_azure_client, used_model



# Function to create and return the GitHub Agent instance
async def create_github_agent(ghu_token: str = None, available_github_repositories: list = None):
    github_logger.info("Initializing GitHub MCP Server and Agent...")
    
    # Use provided token or fall back to environment variable
    token_to_use = ghu_token
    if not token_to_use:
        github_logger.warning("No GitHub token provided and GITHUB_PAT environment variable not set. GitHub agent will not be created.")
        return None
    
    # Warn if running under uvloop where some libraries may behave differently
    try:
        current_loop = asyncio.get_event_loop()
        if 'uvloop' in str(type(current_loop)):
            github_logger.warning("⚠️ Running with uvloop - MCP server may have compatibility issues")
    except Exception:
        pass

    mcp_server = MCPServerStreamableHttp(
        name="GitHub MCP Server",
        params={
            "url": "https://api.githubcopilot.com/mcp/",
            "headers": {
                "Authorization": f"Bearer {token_to_use}",
            }
        },
        cache_tools_list=True,
        tool_filter=ToolFilterStatic(allowed_tool_names=[
            "get_me", "list_pull_requests", "get_issue", "get_issue_comments", 
            "list_issues", "list_sub_issues", "search_issues", "search_orgs", 
            "get_pull_request", "get_pull_request_comments", "get_pull_request_diff", 
            "get_pull_request_files", "get_pull_request_reviews", "get_pull_request_status", 
            "list_pull_requests", "search_pull_requests", "get_commit", "get_file_contents", 
            "list_branches", "list_commits", "search_code", "search_repositories", "search_users",
        ]),
    )
    # Connect with timeout to avoid hanging and cancel-scope issues
    # wrap in try/finally so cleanup stays in same task
    try:
        await asyncio.wait_for(mcp_server.connect(), timeout=30.0)
    except Exception:
        try:
            if hasattr(mcp_server, 'close') and callable(getattr(mcp_server, 'close')):
                await mcp_server.close()
        except Exception:
            pass  # Ignore cleanup errors during exception handling
        raise
    github_logger.info("GitHub MCP Server connected.")

    # Add cleanup function to properly close the connection
    async def cleanup_mcp_server():
        try:
            # Check if the MCP server has a close method and call it
            if hasattr(mcp_server, 'close') and callable(getattr(mcp_server, 'close')):
                await mcp_server.close()
            else:
                # If no close method, try to close the underlying session/client
                if hasattr(mcp_server, '_session') and hasattr(mcp_server._session, 'close'):
                    await mcp_server._session.close()
                elif hasattr(mcp_server, '_client') and hasattr(mcp_server._client, 'close'):
                    await mcp_server._client.close()
            
            github_logger.info("GitHub MCP Server connection closed.")
        except Exception as e:
            github_logger.warning(f"Warning: Failed to close MCP server connection: {e}")

    # Store cleanup function for later use
    mcp_server.cleanup = cleanup_mcp_server

    today_date = datetime.date.today()
    
    # Format available repositories for the instructions
    available_repos_text = ""
    if available_github_repositories:
        repo_names = []
        for repo in available_github_repositories:
            if isinstance(repo, dict) and 'full_name' in repo:
                repo_names.append(repo['full_name'])
            elif isinstance(repo, str):
                repo_names.append(repo)
        if repo_names:
            available_repos_text = f"\n\n**AVAILABLE REPOSITORIES:** {', '.join(repo_names)}"

    agent = Agent(
        name="GitHub Codebase & Stats Assistant",
        handoff_description=(
            "A specialized assistant for all GitHub-related queries including repository stats, code explanation, and issue/PR management."
        ),
        instructions=(
            f"CURRENT_DATETIME = {today_date}"
            "You are a specialized GitHub assistant. "
            "You receive queries from a main Orchestrator Agent. "
            "You **MUST STRICTLY PARSE** the current datetime string (e.g., '2025-07-30T12:37:30+00:00') and use it as your **SOLE and definitive reference** for 'current date', 'this month', 'last week', or any other time-sensitive calculations. "
            "**DO NOT** use any internal knowledge of the current date/time; **ALWAYS USE THE PROVIDED TIMESTAMP.** "
            

            "Your primary goal is to provide accurate, concise, and actionable information by effectively utilizing your GitHub tools. "
            "Always use the tool `get_me` to get your GitHub username. Use Your GitHub username for any operations related to your own repositories or user-specific queries. "
            
            # Updated section for tool usage
            "**CRITICAL TOOL USAGE RULE:** "
            "Only depend on tools' properties and inputSchema. Don't use tools with properties not in their inputSchema. Use the inputSchema to understand what a tool can do. "
            
            "**ABSOLUTELY CRITICAL RULE FOR `search_code` & `get_file_contents` SEQUENCING:** "
            "1. First, use the `search_code` tool with a single keyword and the required `repo:` and `user:` filters (e.g., `search_code(query='auth repo:owner/repo user:owner')`). "
            "2. If `search_code` returns results, immediately pick the most relevant file and call `get_file_contents` on it. **You must not skip this step** even if the first file seems marginal. "
            "3. After reading the file, evaluate if the content provides a clear enough answer. If insufficient: "
            "   - Call `search_code` again using a refined or different keyword, and then follow with another `get_file_contents` on a new file from that result. "
            "   - You **must only call `search_code` again if the previous file content was insufficient.** Do not re-search blindly. "
            "4. This process of `search_code` → `get_file_contents` → evaluate → repeat (if needed) can be done **up to 2 times maximum after the first attempt.** "
            "5. At each stage, use minimal and specific keywords, prefer abbreviations (e.g., 'auth' instead of 'authentication'). "
            "6. Do not parallelize `get_file_contents` calls. Always do one at a time and evaluate context sufficiency after each."
            "7. Do not use separate properties for `repo` or `user`; they are part of the `query` string."

            "You have access to a powerful set of GitHub tools. Your available tools are: "
            "- **Repository & Code Access**: `search_repositories`, `list_branches`, `list_commits`, `get_commit`, `get_file_contents` (for reading file content), `search_code`. "
            "- **Pull Request Management**: `list_pull_requests`, `get_pull_request`, `get_pull_request_comments`, `get_pull_request_files`, `get_pull_request_reviews`, `get_pull_request_status`, `search_pull_requests`. "
            "- **Issue Management**: `list_issues`, `get_issue`, `get_issue_comments`, `list_sub_issues`, `search_issues`. "
            "- **User & Organization Information**: `get_me`, `search_users`, `search_orgs`. "
            
            "---"
            "**General Guidelines:**"
            "- **To determine date ranges (e.g., 'this month', 'last week'):** You **MUST** use the `CURRENT_DATETIME` you extracted as your reference point. Calculate the `since` and `until` parameters for `list_commits` or `list_pull_requests` based on this *provided* date. For 'this month', calculate the first day and last day of the month based on the extracted `CURRENT_DATETIME`."
            " Example: If `CURRENT_DATETIME` is 2025-07-15T10:00:00+00:00, 'this month' means `since='2025-07-01T00:00:00Z'` and `until='2025-07-31T23:59:59Z'`."
            "- Always confirm the repository owner and name. Ask for clarification if ambiguous. "
            "- **Always cite the tools you used** for your analysis and data retrieval. "
            "- If a requested action cannot be directly performed by an available tool, explain why and suggest an alternative or a composite approach using available tools. "
            "- Strive for brevity in your intermediate steps and focus the final output on the user's direct question. "
            "- **Handle Tool Errors Gracefully:** If a tool call fails (e.g., repository not found, file not found), inform the user clearly and suggest alternative actions or ask for clarification, rather than just crashing."
            "---"

            # Tweaked instructions for explaining code - new rule is added here
            "**When asked to explain code implementation, architecture, or logic flow (e.g., 'explain how authentication is implemented in X repo'):**"
            "1. **Identify Relevant Repository & Files**: First, determine the target repository (`owner/repo-name`). If not specified, ask for clarification. "
            "2. **Make Highly Specific Keywords**: Derive keywords from the user query. **To improve the likelihood of finding all relevant files, use abbreviations and common short forms.** For example, use 'auth' instead of 'authentication' or 'authenticator'. Use 'repo' instead of 'repository', etc. This ensures you won't miss a file because of a slightly different word form. "
            "3. Use `search_code` tool for each keyword separately in parallel for all the keywords, and **ALWAYS include the `repo:owner/repo-name` and `user:owner` filter** in the property of search_code tool called \"query\" to find a concise list of the most relevant file paths. EXAMPLE: search_code(query:\"keyword repo:owner/repo_name user:owner\") **Use 1 keyword in each search_code tool call**. "
            "4. **Strategically Read Key File Contents**: Select **the 3-5 most critical files** from the list returned by `search_code`. For each selected file, **you must use the `get_file_contents` tool, providing the file path returned from the `search_code` tool as the `path` parameter.** Prioritize files likely to contain core logic. **YOU MUST ONLY USE 1 get_file_contents AT A TIME, DONT CALL MULTIPLE IN PARALLEL, CALL THE TOOL SEQUENTIALLY, ONLY REUSE IF MORE CONTEXT IS NEEDED**"
            "5. If file contents still dont provide context to answer, re-evaluate the selected files and get_file_contents of other files. (redo step 4). 2 TIMES MAXIMUM "
            "6. If step 5 repeated to the maximum and still cant provide answer with current context, consider additional keywords or broader searches. and redo step 1 and 2 and 3 and 4. this step happens MAXIMUM 2 TIMES"
            "3. **Provide High-Level Overview & Key Snippets**: "
            "   - **Start by explaining the overall conceptual flow**. "
            "   - From the *actual content* of the files you read, extract **only the most essential, high-impact code snippets** (e.g., public method signatures, key API calls). **Limit each snippet to a maximum of 10-12 lines.** Do not generate hypothetical code. Always cite the **full file path and specific line numbers** for each snippet. "
            "4. **Offer Deeper Dive**: Conclude your initial explanation by **asking the user if they would like a deeper dive into a specific file, function, or aspect** of the implementation. This allows the user to guide the context usage. "
            "5. **Maintain Conciseness**: Be extremely mindful of token limits. Prioritize key information. "

            "---"

            "**When asked for user or repository statistics (e.g., 'who is the top contributor in X repo?', 'how many PRs does user Y have in repo Z?', 'how many commits did user A do this month?', 'what are the branches related to this user in X repo?'):**"
            "1. **Identify Repository & User**: Always identify the target repository (`owner` and `repo`) and any specific `user` mentioned in the query. If unclear, ask for clarification. "
            "2. **Use Appropriate Tools & Process Data Effectively**: "
            "   - **For User Repositories**: Use `search_repositories(query='user:username')` to list all repositories for a given user. "
            "   - **For Pull Request Counts (by user/repo)**: Use `list_pull_requests` for the specified `owner` and `repo`. If a specific author is requested, filter the returned list programmatically based on the author's username. Count the matching PRs. "
            "   - **For Commit Counts (by user/repo/date)**: Use `list_commits` for the specified `owner` and `repo`. If a `sha` (branch name) is specified, include that. Use the `author` parameter to filter by user. **For 'this month' queries, use `since` parameter with a value of today's date adjusted to the first day of the month: {current_month_start}**. If a date range (like 'this month') is given, post-filter the results based on the commit timestamp from the tool's output. Count the matching commits. "
            "   - **For 'Top Contributor' or 'Number of commits per member'**: Since there's no direct `list_contributors` tool, you must infer this from commits. "
            "     - Use `list_commits` for the repository, **retrieving a maximum of the last 100-200 commits (use `perPage` parameter, and if necessary, limit to 2 pages)**. "
            "     - Process the returned commits: Tally commit authors by their GitHub username (or email if username is unavailable). "
            "     - Present the top contributors or the count for each member based on this sample. **Clearly state that the analysis is based on the most recent X commits (e.g., 'Based on the most recent 100 commits...')**. "
            "   - **For 'Branches related to a user'**: This requires combining tools. First, use `list_branches` for the given repository. Then, for each branch, use `list_commits` with the `author` parameter set to the specified user and the `sha` set to the branch name. Keep track of which branches return commits by that user. Present the list of branches where the user has commits. Be aware this can be a more resource-intensive operation due to multiple `list_commits` calls; if too many branches, you might need to limit the scope. "
            "3. **Synthesize & Present Data Concisely**: Clearly present the requested statistics. State the tool(s) used and how the data was derived. **Limit the output to essential numbers and names; avoid verbose descriptions of individual items.** "
            "4. **Handle Pagination (Consciously)**: When making multiple paginated calls, aim to collect just enough data to answer the query accurately without exceeding token limits. For exhaustive lists, inform the user about the volume of data. "
            f"{available_repos_text}"
        ),
        model=OpenAIChatCompletionsModel(
            model=used_model,
            openai_client=async_azure_client,
        ),
        mcp_servers=[mcp_server], # The MCP server defines the available tools
        # Add a handoff_description if this agent were to be explicitly handed off via 'handoffs'
        # handoff_description="A specialized assistant for all GitHub-related queries including repository stats, code explanation, and issue/PR management."
    )
    # Expose cleanup hooks on the agent so callers can close the MCP server
    setattr(agent, "mcp_server", mcp_server)
    setattr(agent, "mcp_cleanup", cleanup_mcp_server)
    github_logger.info("GitHub Agent created.")
    return agent

# Add an async context manager to ensure lifecycle is managed within the same task
class GitHubAgentManager:
    def __init__(self, token: str, available_github_repositories: list | None = None):
        self.token = token
        self.available_github_repositories = available_github_repositories or []
        self.agent = None

    async def __aenter__(self):
        self.agent = await create_github_agent(self.token, self.available_github_repositories)
        return self.agent

    async def __aexit__(self, exc_type, exc, tb):
        try:
            if self.agent and hasattr(self.agent, "mcp_cleanup"):
                await self.agent.mcp_cleanup()
        except Exception as e:
            github_logger.warning(f"Warning: Failed to cleanup GitHub agent MCP server: {e}")

# Optional: Add a test block to run the GitHub agent independently
if __name__ == "__main__":
    # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # logging.getLogger('httpx').setLevel(logging.WARNING)
    # logging.getLogger('agents').setLevel(logging.INFO)

    async def run_github_test():
        github_agent_instance = await create_github_agent()
        
        # Simple test query for the GitHub agent
        print("\n--- Testing GitHub Agent Independently ---")
        query = "How many commits did BassemBG do this month in the repository BassemBG/NeuroAI-Backend?"
        print(f"Query: {query}")
        result = await Runner.run(github_agent_instance, query)
        print(f"Response: {result.final_output}")
        print("-----------------------------------------\n")

    asyncio.run(run_github_test())