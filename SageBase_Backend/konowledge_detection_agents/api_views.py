import logging
from typing import List
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

import asyncio
from integrations.models import User, Company, CompanyIntegration, IntegrationTool
from django.core.exceptions import ObjectDoesNotExist
from knowledge_spaces_Q_A.chat_response_formatting_agent import ChatOutputFormat
from dataclasses import dataclass
from .utils import (
    get_user_and_company_by_email,
    get_github_repositories_and_installation_id,
)
from knowledge_spaces_Q_A.chat_orchestrator import ChatOrchestrator
from knowledge_spaces_Q_A.chat_models import(
    ChatRequest,
    ChatUserContext,
    ConversationMessage,
    ConversationType
)
logger = logging.getLogger(__name__)
from .question_answering import get_ghu_token_and_allowed_repos


def map_frontend_platforms_to_datasources(platforms: List[str]) -> List[str]:
    """
    Map frontend platform IDs to backend DataSource values
    
    Frontend sends: ["google-drive", "github", "confluence"]
    Backend expects: ["google_drive", "github", "confluence"]
    """
    platform_mapping = {
        "google-drive": "google_drive",
        "github": "github", 
        "confluence": "confluence",
        "jira": "jira",
        "notion": "notion",
        "slack": "slack",
        "teams": "teams",
        "discord": "discord",
        "gitlab": "gitlab",
        "email": "email",
        "internet": "internet",
        "local": "local"
    }
    
    mapped_sources = []
    for platform in platforms:
        mapped_source = platform_mapping.get(platform, platform)
        mapped_sources.append(mapped_source)
    
    return mapped_sources


def build_chat_context(
    context_data, history, user_instance
):
    company_instance = user_instance.company
    
    # Build collections to search - always include company collection
    collections_to_search = [str(company_instance.id)]
    
    # Add user-specific collections if needed (for private knowledge spaces)
    # This could be expanded to include user-specific collections
    # collections_to_search.append(f"user_{user_instance.email}")
    
    # sources to search is a list of strings, like ["google_drive", "github", "confluence"]
    sources_to_search = context_data.get("sources_to_search", [])
    
    #NOTE : api_views.py : this is to be updated as the default source, include all, or I don't know
    # If no platforms selected, search all sources
    if not sources_to_search:
        sources_to_search = ["internet"]  # Default to Google Drive
    
    kwargs = {
        "user_email": context_data.get("user_email"),
        "history": history or [],
        "user": user_instance,
        "company": company_instance,
        "collections_to_search": collections_to_search,
        "sources_to_search": sources_to_search,
    }
    return ChatUserContext(**kwargs)

@dataclass
class FrontHistElement:
    user: str
    answer: str


class AgentOrchestratorAskView(APIView):
    def _run_async_in_thread(self, orchestrator: ChatOrchestrator, chat_request: ChatRequest):
        """Run async code in a separate thread with its own event loop"""
        import asyncio
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Run the async operation with a timeout shorter than typical API timeout
            return loop.run_until_complete(
                asyncio.wait_for(
                    orchestrator.process_chat_request(chat_request),timeout=500.0
                )
            )
        except asyncio.TimeoutError:
            # Return a structured timeout-like response
            return {
                "response": "Request timed out. The AI is taking longer than expected to process your query. Please try again with a simpler question.",
                "references": ["Timeout Error"],
            }
        finally:
            loop.close()
    def post(self, request):
        question = request.data.get("question")
        context_data = request.data.get("context", {})
        history: List[FrontHistElement] = context_data.get("history", [])
        user_instance, company = get_user_and_company_by_email(
            context_data.get("user_email")
        )
        if not question or not user_instance or not user_instance.company:
            return Response(
                {"error": "Missing question"}, status=status.HTTP_400_BAD_REQUEST
            )
        # Frontend sends selected platforms as `filters`; backend context expects `sources_to_search`
        filters_from_frontend = context_data.get("filters", [])
        # Normalize and map to backend datasource identifiers
        mapped_sources = map_frontend_platforms_to_datasources([
            (p or "").lower() for p in filters_from_frontend
        ])
        # Store mapped sources on context so the orchestrator can initialize handoff agents (e.g., GitHub)
        context_data["sources_to_search"] = mapped_sources
      


         # Get GitHub token and repositories (only if GitHub is among selected sources)
        available_github_repositories = []
        if "github" in [p.lower() for p in mapped_sources]:
            try:
                _, available_github_repositories, _ = get_ghu_token_and_allowed_repos(context_data.get("user_email"))
            except Exception as e:
                # Log the error but continue without GitHub integration
                print(f"Failed to get GitHub token: {e}")
                available_github_repositories = []

        context = build_chat_context(
            context_data, history, user_instance
        )

        try:
            chat_orchestrator = ChatOrchestrator(context=context)

            mapped_history = []
            # Map the history to ConversationMessage format
            if history and len(history) > 0:
                for item in history:
                    if "user" in item:
                        mapped_history.append(ConversationMessage(content=item["user"], sender_name="User"))
                    if "answer" in item:
                        mapped_history.append(ConversationMessage(content=item["answer"], sender_name="AI assistant"))

            chat_request = ChatRequest(
                message=question,
                user=context.user,
                session_type=ConversationType.SINGLE,
                history=mapped_history,
                output_format=ChatOutputFormat.HTML,  # API responses should be formatted as HTML for frontend,
                needs_reply=True
            )
            # Run async code in a separate thread to avoid nested loop/cancel scope issues
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self._run_async_in_thread, chat_orchestrator, chat_request)
                try:
                    result = future.result(timeout=300.0)
                    # result may be a dict from timeout branch or ChatResponse TypedDict
                    answer = result.get("response") if isinstance(result, dict) else getattr(result, "response", "")
                    references = result.get("references") if isinstance(result, dict) else getattr(result, "references", [])
                    return Response(
                        {
                            "answer": answer,
                            "references": references or [],
                            "available_github_repositories": available_github_repositories,
                        }
                    )
                except concurrent.futures.TimeoutError:
                    return Response({
                        "error": "Request timed out. The AI is taking longer than expected to process your query. Please try again with a simpler question."
                    }, status=status.HTTP_504_GATEWAY_TIMEOUT)
        except Exception as e:
            # Log the error for debugging
            logger.exception(f"Error occurred: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
