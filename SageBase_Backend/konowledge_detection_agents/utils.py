from integrations.models import User, Company, CompanyIntegration, IntegrationTool
from django.core.exceptions import ObjectDoesNotExist
import logging

import asyncio
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)

def get_user_and_company_by_email(email):
    try:
        user_instance = User.objects.get(email=email)
        return user_instance, user_instance.company
    except ObjectDoesNotExist:
        return None, None

def get_user_id_from_email(email: str) -> str:
    """
    Get user ID from email address using Django ORM.
    
    This function looks up a user by their email address and returns their ID.
    It's useful for converting email-based authentication to user ID for database operations.
    
    Args:
        email (str): The email address of the user
        
    Returns:
        str: The user ID as a string, or None if user not found
        
    Example:
        >>> user_id = get_user_id_from_email("<EMAIL>")
        >>> # Returns: "123" or None if user doesn't exist
    """
    try:
        from integrations.models import User
        user = User.objects.get(email=email)
        return str(user.id)
    except User.DoesNotExist:
        return None
    except Exception as e:
        logger.error(f"❌ Error getting user ID from email {email}: {e}")
        return None

async def safe_async_call(func, *args, **kwargs):
    """
    Safely call a synchronous function from an async context,
    handling Django database operations and avoiding CurrentThreadExecutor issues.
    """
    try:
        # Always use sync_to_async with thread_sensitive=False to avoid CurrentThreadExecutor
        # This ensures Django database operations work properly from async contexts
        return await sync_to_async(func, thread_sensitive=False)(*args, **kwargs)
    except Exception as e:
        logger.error(f"Error in safe_async_call: {e}")
        raise

def get_github_repositories_and_installation_id(company):
    """
    Returns (list_of_repo_names, installation_id) for the company's GitHub integration.
    """
    if not company:
        return [], None
    try:
        github_tool = IntegrationTool.objects.get(slug="github")  # type: ignore
        integration = CompanyIntegration.objects.filter(company=company, tool=github_tool, is_active=True).first()  # type: ignore
        if integration and integration.config:
            # Handle both possible formats for installation_id
            raw_installation_id = integration.config.get("installation_id")
            if isinstance(raw_installation_id, dict):
                installation_id = raw_installation_id.get("id")
            else:
                installation_id = raw_installation_id

            # Handle both possible formats for repositories
            repos = integration.config.get("repositories", [])
            # If repos are objects, extract the 'full_name' field (backend saves as {"id": ..., "full_name": ...})
            if repos and isinstance(repos[0], dict) and "full_name" in repos[0]:
                repo_names = [r["full_name"] for r in repos]
            elif repos and isinstance(repos[0], dict) and "name" in repos[0]:
                repo_names = [r["name"] for r in repos]
            else:
                repo_names = repos
            return repo_names, installation_id
    except Exception as e:
        logger.debug(f"[WARN] Could not fetch available GitHub repos: {e}")
    return [], None 