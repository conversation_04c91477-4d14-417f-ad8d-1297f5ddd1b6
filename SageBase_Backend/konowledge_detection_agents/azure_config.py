"""
Azure OpenAI Configuration Module
This module handles Azure OpenAI client configuration to avoid circular imports.
"""

import os
import logging
from openai import AzureOpenAI, AsyncAzureOpenAI

logger = logging.getLogger(__name__)


async_azure_client=None
gpt4o_mini_model=None
embedding_model=None
gpt4o_model=None


def configure_async_azure_openai():
    """Configure Async Azure OpenAI client from environment variables."""
    global AZURE_ENDPOINT, AZURE_MODEL_NAME, AZURE_DEPLOYMENT, AZURE_API_VERSION
    global gpt4o_mini_model, embedding_model, gpt4o_model
    
    # Read Azure OpenAI configuration from environment variables
    AZURE_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL")
    AZURE_DEPLOYMENT = os.getenv("AZURE_OPENAI_DEPLOYMENT")
    AZURE_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
    
    # Read model configurations from environment variables
    gpt4o_mini_model = os.getenv("AZURE_GPT4O_MINI")
    embedding_model = os.getenv("CHROMA_EMBEDDING_MODEL")
    gpt4o_model = os.getenv("AZURE_GPT4O")
    
    # Validate required Azure OpenAI settings
    if not AZURE_ENDPOINT:
        logger.error("❌ AZURE_OPENAI_ENDPOINT not set")
        return None
    
    if not AZURE_MODEL_NAME:
        logger.error("❌ AZURE_OPENAI_MODEL not set")
        return None
    
    if not AZURE_DEPLOYMENT:
        logger.error("❌ AZURE_OPENAI_DEPLOYMENT not set")
        return None
    
    if not AZURE_API_VERSION:
        logger.error("❌ AZURE_OPENAI_API_VERSION not set")
        return None
    
    if not gpt4o_model:
        logger.error("❌ AZURE_GPT4O not set")
        return None
    

    try:
        # Create Async Azure OpenAI client
        client = AsyncAzureOpenAI(
            azure_endpoint=AZURE_ENDPOINT,
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=AZURE_API_VERSION
        )
        
        # Test the client with a simple request
        logger.debug(f" Async Azure OpenAI client configured successfully")
        logger.debug(f"   Endpoint: {AZURE_ENDPOINT}")
        logger.debug(f"   Model: {AZURE_MODEL_NAME}")
        logger.debug(f"   Deployment: {AZURE_DEPLOYMENT}")
        logger.debug(f"   API Version: {AZURE_API_VERSION}")
        
        return client
        
    except Exception as e:
        logger.error(f"❌ Failed to configure Async Azure OpenAI client: {e}")
        return None

# Initialize the clients
async_azure_client = configure_async_azure_openai()
used_model = AZURE_MODEL_NAME if async_azure_client else None
azure_gpt4o_mini_model = gpt4o_mini_model if async_azure_client else None
gpt4o_model = gpt4o_model if async_azure_client else None

# Export the configuration
__all__ = [ 'async_azure_client', 'used_model', 'AZURE_ENDPOINT', 'AZURE_MODEL_NAME', 'AZURE_DEPLOYMENT', 'AZURE_API_VERSION', 'gpt4o_mini_model', 'embedding_model'] 