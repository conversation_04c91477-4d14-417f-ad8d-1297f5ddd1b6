"""
Question Answering Agent with Internet Search and Local Tools
A simple AI agent that can answer user questions by:
1. Searching the internet for current information
2. Using local database/function tools for internal knowledge
3. Combining mutiple platforms to provide comprehensive answers
4. Using persistent session management and intelligent memory retrieval
"""
import json
import os
import sys
import requests
import logging
from asgiref.sync import async_to_sync, sync_to_async

from knowledge_spaces_Q_A.function_tools import search_local_knowledge
from .github_agent import create_github_agent

from knowledge_spaces_Q_A.chat_models import ChatUserContext

# Disable tracing before importing agents to avoid API key issues
os.environ.setdefault('AGENTS_DISABLE_TRACING', 'true')
os.environ.setdefault('OPENAI_DISABLE_TRACING', 'true')
os.environ.setdefault('OPENAI_TRACING_ENABLED', 'false')
os.environ.setdefault('AGENTS_TRACING_DISABLED', 'true')

# Set up logger
logger = logging.getLogger(__name__)
from dataclasses import dataclass, field
from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from agents import Agent, Runner, function_tool, ModelSettings, RunContextWrapper, OpenAIChatCompletionsModel

from agents import Agent, Runner, function_tool, ModelSettings, trace, RunContextWrapper, OpenAIChatCompletionsModel
import gradio as gr
from vectordb.models.document import DataSource

# Check if Django is available
try:
    import django
    DJANGO_AVAILABLE = True
except ImportError:
    DJANGO_AVAILABLE = False

# Import Django utilities if available
if DJANGO_AVAILABLE:
    try:
        from asgiref.sync import sync_to_async
    except ImportError:
        sync_to_async = None
else:
    sync_to_async = None

# Import utility functions if Django is available
if DJANGO_AVAILABLE:
    try:
        from .utils import get_user_and_company_by_email, get_github_repositories_and_installation_id
        from integrations.models import CompanyIntegration, IntegrationTool
    except ImportError:
        get_user_and_company_by_email = None
        get_github_repositories_and_installation_id = None
        CompanyIntegration = None
        IntegrationTool = None
else:
    get_user_and_company_by_email = None
    get_github_repositories_and_installation_id = None
    CompanyIntegration = None
    IntegrationTool = None


from knowledge_spaces_Q_A.chat_orchestrator import ChatUserContext

# Import Azure configuration from separate module to avoid circular imports
try:
    from konowledge_detection_agents.azure_config import (
        azure_client, async_azure_client, used_model, AZURE_ENDPOINT, AZURE_MODEL_NAME, 
        AZURE_DEPLOYMENT, AZURE_API_VERSION, gpt4o_mini_model, embedding_model
    )
except ImportError as e:
    logger.warning(f"Could not import Azure configuration: {e}")
    # Fallback values
    azure_client = None
    async_azure_client = None
    used_model = None
    AZURE_ENDPOINT = None
    AZURE_MODEL_NAME = None
    AZURE_DEPLOYMENT = None
    AZURE_API_VERSION = None
    gpt4o_mini_model = None
    embedding_model = None

# Azure client is now imported from azure_config module

# Azure client is now configured and set as default for agents library
if azure_client:
    logger.info(" Azure OpenAI client configured and set as default for agents library")
    logger.info(f"   Using model: {used_model}")
    logger.info(f"   GPT4O Mini: {gpt4o_mini_model}")
    logger.info(f"   Embedding Model: {embedding_model}")
else:
    logger.error("❌ Azure OpenAI client not configured - missing required environment variables")
    logger.error("Please set the following variables in your .env.dev file:")
    logger.error("   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/")
    logger.error("   AZURE_OPENAI_API_KEY=your-azure-api-key")
    logger.error("   AZURE_OPENAI_DEPLOYMENT=your-deployment-name")
    logger.error("   AZURE_OPENAI_API_VERSION=2024-12-01-preview")
    logger.error("   AZURE_OPENAI_MODEL=gpt-4.1 (optional, defaults to deployment name)")
    
    # Don't create fallback client - let it fail clearly
    azure_client = None



def create_internal_search_agent(question: str, answer: str) -> Agent:

    # Import functions locally to avoid circular import
    # These functions are defined later in this file
    
    # Always include local knowledge as base
    tools = [search_local_knowledge]
    
    # Add platform-specific tools based on available platforms

    return Agent(
        name="Search_Agent",
        instructions=f"""
        
        You are a helpful search assistant that can search multiple knowledge platforms to find if an information exists.
        
        Your role is to detect if the information exists, and if it exists, does it have a contardictory previous answer.

        Use existing tools to search if the info exists.
        Most important is to search in the local knowledge base, and if it exists, check if the answer is up to date.
        If the information does not exists, then don't set updated_question and updated_knowledge
        If the information exists in the knowledge base, and the answer is not up to date, then set the updated_question,updated_knowledge and the url of the existing knowledge.
        
        """,
        model=OpenAIChatCompletionsModel(
            model=used_model,
            openai_client=async_azure_client,
        ),
        model_settings=ModelSettings(temperature=0.3),
        tools=tools,  # Use the dynamically selected tools
        output_type=knowledge_Exist_Update_Result,  # Structured output with answer and references
    )
    
        
        
class knowledge_Exist_Update_Result(BaseModel):
    """result if knowledge exisst, or if it needs to be updated"""
    exists: bool = Field(description="True if the information exists, False if it does not exist")
    needs_update: Optional[bool] = Field(default=None, description="True if the knowledge needs to be updated, False if it does not need to be updated")
    url: Optional[str] = Field(default=None, description="The url of the knowledge if it exists, and needs an update.")
    updated_question: Optional[str] = Field(default=None, description="The new related question if it needs to be updated")
    updated_knowledge: Optional[str] = Field(default=None, description="The new knowledge if it needs to be updated")




# Import vectordb interfaces
import sys
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__))))
from vectordb.interfaces import get_chroma_collection
from vectordb.models.document import DataSource
# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    import os
    from pathlib import Path
    
    # Check environment type
    ENVIRONMENT = os.getenv('DJANGO_ENV', 'development')  # Default to development
    
    # Try multiple .env file locations based on environment
    current_dir = Path(__file__).parent  # konowledge_detection_agents/
    project_root = current_dir.parent    # SageBase_BackEnd.git/
    
    if ENVIRONMENT == 'deployment':
        env_locations = [
            current_dir / ".env.deployment",       # konowledge_detection_agents/.env.deployment
            project_root / ".env.deployment",      # SageBase_BackEnd.git/.env.deployment
            Path.cwd() / ".env.deployment"         # Current working directory/.env.deployment
        ]
        logger.info(f"🌐 Loading deployment environment files...")
    else:
        env_locations = [
            current_dir / ".env.dev",       # konowledge_detection_agents/.env.dev
            project_root / ".env.dev",      # SageBase_BackEnd.git/.env.dev
            Path.cwd() / ".env.dev"         # Current working directory/.env.dev
        ]
        logger.info(f"Loading development environment files...")
    env_file_found = False
    for env_path in env_locations:
        if env_path.exists():
            logger.info(f"Found .env file at: {env_path}")
            load_dotenv(env_path)
            env_file_found = True
            break
    if not env_file_found:
        logger.warning("⚠️  No .env file found in any of these locations:")
        for env_path in env_locations:
            logger.warning(f"   - {env_path}")
        logger.warning("   Please create a .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here")
    # Check if Azure OpenAI API key is loaded
    if os.getenv('AZURE_OPENAI_API_KEY'):
        logger.info("AZURE_OPENAI_API_KEY loaded successfully")
        # Set ChromaDB to use OpenAI embeddings to match existing collections
        if not os.getenv('CHROMA_EMBEDDING_TYPE'):
            os.environ['CHROMA_EMBEDDING_TYPE'] = 'openai'
            logger.info("🔧 Set CHROMA_EMBEDDING_TYPE=openai")
        if not os.getenv('CHROMA_EMBEDDING_MODEL'):
            os.environ['CHROMA_EMBEDDING_MODEL'] = 'text-embedding-3-small'
            logger.info("🔧 Set CHROMA_EMBEDDING_MODEL=text-embedding-3-small")
    else:
        logger.error("❌ AZURE_OPENAI_API_KEY not found in environment variables")
except ImportError:
    logger.warning("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    logger.warning(f"⚠️  Could not load .env file: {str(e)}")
# Check if Azure OpenAI API key is available
if not os.getenv('AZURE_OPENAI_API_KEY'):
    logger.error("❌ AZURE_OPENAI_API_KEY not found!")
    logger.error("   Please create a .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here")
    logger.error("   Or set environment variable: export AZURE_OPENAI_API_KEY='your-azure-api-key-here'")
    logger.error("   Get your Azure OpenAI API key from your Azure portal")
# Set your OpenAI API key
# os.environ['OPENAI_API_KEY'] = 'your-api-key-here'
# ============================================================================
# Data Models
# ============================================================================
class SearchResult(BaseModel):
    """Search result from internet or local source"""
    source: str  # "internet" or "local"
    title: str
    content: str
    url: Optional[str] = None
    relevance_score: float = 0.0
class StructuredAnswer(BaseModel):
    """Structured answer output from the agent"""
    answer: str = Field(description="The comprehensive answer to the user's question")
    references: List[str] = Field(description="List of sources and references used to compile the answer, use url when available, otheriwise, use document title or source name")
class Answer(BaseModel):
    """Complete answer to user question"""
    question: str
    answer: str
    sources: List[SearchResult]
    confidence: float
    timestamp: str

class NewCreatedQA(BaseModel):
    """Structured answer output from the agent"""
    question: str
    answer: str
    references: List[str]

class NewCreatedQAResponse(BaseModel):
    """Response containing multiple Q&A instances"""
    all_qa: List[NewCreatedQA]

@dataclass
class KnowledgeContext:
    """Simple context for question answering"""
    user_id: str
    user_email: Optional[str] = None
    preferences: List[str] = field(default_factory=lambda: ["general"])
    github_token: Optional[str] = None  # Add GitHub token to context
    repo: Optional[str] = None          # Add repo to context
    history: List[dict] = field(default_factory=list)  # Add conversation history
    available_github_repositories: List[str] = field(default_factory=list)  # Add available repos
    installation_id: Optional[str] = None # Add installation_id to context
    company: Any = None # Add company to context
    sources: List[str] = field(default_factory=list) # Add sources to context
    
    def __post_init__(self):
        if self.user_email is None:
            self.user_email = "<EMAIL>"






@function_tool
def search_github_repo(
    wrapper: RunContextWrapper[KnowledgeContext],
    question: str = "",
    method: str = "llm"
) -> str:
    """
    Search a GitHub repository for answers using the internal /github/ask/ API.
    Generates access token from installation_id and uses available_github_repositories from context.
    """
    logger.info(f"@function_tool ->  Calling tool search_github_repo for question: {question}")
    if not DJANGO_AVAILABLE:
        return "GitHub repository search is not available in standalone mode. Please run this within the Django environment."
    
    import requests
    
    try:
        from django.conf import settings
        from integrations.github.api.services import get_installation_access_token
    except Exception as e:
        return f"GitHub integration not available - Django error: {str(e)}"
    
    context = wrapper.context
    logger.warning(f" Calling tool search_github_repo for query: {question}")
    
    if not context or not context.installation_id or not context.available_github_repositories:
        logger.error("Error")
        return "Error: installation_id and available_github_repositories must be provided in context."
    
    
    # Extract repo names from the objects
    repos_to_query = []
    for repo_obj in context.available_github_repositories:
        if isinstance(repo_obj, dict) and 'full_name' in repo_obj:
            repos_to_query.append(repo_obj['full_name'])  # type: ignore
        elif isinstance(repo_obj, str):
            repos_to_query.append(repo_obj)
    
    logger.info("repos to query: ", repos_to_query)
    if not repos_to_query:
        return "No accessible GitHub repositories found for this user."
    
    # Generate installation access token
    try:
        access_token = get_installation_access_token(context.installation_id)
        logger.info(f"✅ Generated access token for installation {context.installation_id}")
    except Exception as e:
        return f"Failed to generate installation access token: {e}"
    
    # Query each repo using your /ask endpoint
    answers = []
    for repo in repos_to_query:
        url = f"{settings.BACKEND_PUBLIC_URL}/api/integrations/github/api/ask/"
        payload = {
            "repo": repo,
            "question": question,
            "method": method,
            "github_token": access_token,  # Pass the generated access token
        }
        try:
            logger.warning(f"🔍 Querying /ask endpoint for repo: {repo}")
            response = requests.post(url, json=payload, timeout=20)
            response.raise_for_status()
            data = response.json()
            answer = data.get("answer", "No answer returned from GitHub API.")
            answers.append(f"**{repo}:**\n{answer}")
        except Exception as e:
            answers.append(f"**{repo}:**\nError querying GitHub API: {str(e)}")
    
    # Combine all answers
    combined_answer = "\n\n".join(answers)
    references = [
        f"GitHub Repository Search: {', '.join(repos_to_query)}"
    ]
    
    return {
        "answer": combined_answer,
        "references": references
    }






@function_tool
async def update_local_knowledge(qa_id: str, question_content: str = None, answer_content: str = None, question_title: str = None) -> str:
    """Update existing knowledge in the local knowledge base."""
    from knowledge_spaces_Q_A.models import QA
    from knowledge_spaces_Q_A.utils import embed_q_a
    
    try:
        logger.info(f"@function_tool ->  Calling tool update_local_knowledge for qa_id: {qa_id}")
        # Get the existing QA instance
        qa_instance = await sync_to_async(lambda: QA.objects.get(id=qa_id), thread_sensitive=False)()
        
        if qa_instance is None:
            return f"❌ QA instance with ID {qa_id} not found"
        
        # Update only the fields that are provided
        updated_fields = []
        if question_content is not None:
            qa_instance.question_content = question_content
            updated_fields.append("question_content")
        
        if answer_content is not None:
            qa_instance.answer_content = answer_content
            updated_fields.append("answer_content")
        
        if question_title is not None:
            qa_instance.question_title = question_title
            updated_fields.append("question_title")
        
        # Save the updated instance
        await sync_to_async(qa_instance.save, thread_sensitive=False)()
        
        # Re-embed the updated knowledge
        success = await sync_to_async(embed_q_a, thread_sensitive=False)(qa_instance)
        
        if success:
            logger.info(f"📚 Successfully updated and embedded knowledge: {qa_instance.question_title}")
            return f"✅ Knowledge updated successfully: {qa_instance.question_title} (Updated fields: {', '.join(updated_fields)})"
        else:
            logger.error(f"❌ Failed to embed updated knowledge: {qa_instance.question_title}")
            return f"❌ Failed to embed updated knowledge: {qa_instance.question_title}"
            
    except Exception as e:
        logger.exception(f"❌ Error updating knowledge: {e}")
        return f"❌ Error updating knowledge: {str(e)}"


# ============================================================================
# Gradio Chat Interface
# ============================================================================

def get_ghu_token_and_allowed_repos(user_email: str):
    """Fetch ghu_token and allowed repos for a user by email."""
    if not DJANGO_AVAILABLE:
        logger.warning("⚠️ Django not available - GitHub integration disabled")
        return None, [], None
    
    try:
        user_instance, company = get_user_and_company_by_email(user_email)
        if not company:
            raise Exception("No company found for this user.")
        allowed_repos, installation_id = get_github_repositories_and_installation_id(company)
        if not installation_id or not allowed_repos:
            raise Exception("No GitHub App installation or allowed repos found for this user.")
        github_tool = IntegrationTool.objects.get(slug="github")  # type: ignore
        integration = CompanyIntegration.objects.filter(company=company, tool=github_tool).first()  # type: ignore
        if not integration or not integration.config:
            raise Exception("No GitHub integration found for this company.")
        
        # Use the new automatic token refresh functionality
        try:
            ghu_token = integration.get_valid_github_token()
            return ghu_token, allowed_repos, installation_id
        except Exception as token_error:
            logger.warning(f"⚠️ Error refreshing GitHub token: {token_error}")
            # Fallback to old method for backward compatibility
            ghu_token = integration.config.get("ghu_token")
            if not ghu_token:
                raise Exception("No user access token (ghu_token) found. Please complete the OAuth flow first.")
            return ghu_token, allowed_repos, installation_id
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting GitHub token: {e}")
        return None, [], None

# Updated initialize_qa_system to accept user_email and set up MCP server
qa_system = None

def initialize_qa_system(workspace, user_email, platforms: Optional[list] = None):
    """Initialize the QA system once, with MCP server for the user if email is provided."""
    global qa_system
    if qa_system is None:
        # Check if Azure OpenAI is configured
        if not azure_client:
            logger.error("❌ Azure OpenAI client not configured")
            return False
        
        if not os.getenv('AZURE_OPENAI_API_KEY'):
            return False
        
        # Adjust platforms based on availability
        if platforms is None:
            if DJANGO_AVAILABLE:
                platforms = ["local", "github"]
            else:
                platforms = ["local"]  # Only local knowledge in standalone mode
                logger.warning("⚠️  GitHub integration disabled in standalone mode")
        
        return True
    return True

async def process_question_async(question: str, context: Optional[KnowledgeContext] = None) -> dict:
    """Process question asynchronously and return structured result"""
    if not initialize_qa_system():
        return {
            "answer": "⚠️ Azure OpenAI API key not found. Please create a .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here\nOr set environment variable: export AZURE_OPENAI_API_KEY='your-azure-api-key-here'\nGet your Azure OpenAI API key from your Azure portal",
            "references": ["Configuration Error"]
        }

    try:
        if qa_system is not None:
            result = await qa_system.answer_question(question, context)
            structured_answer = result.final_output
            return {
                "answer": structured_answer.answer,
                "references": structured_answer.references
            }
        else:
            return {
                "answer": "QA system is not initialized.",
                "references": ["System Error"]
            }
    except Exception as e:
        return {
            "answer": f"❌ Error processing question: {str(e)}",
            "references": ["System Error"]
        }
def chatbot_response(message, history, github_token, github_repo):
    """Process user message and return chatbot response"""
    if not message.strip():
        return "Please ask me a question! I can search the internet and local knowledge to help you."
    # Build context
    if github_token and github_repo:
        context = KnowledgeContext(
            user_id="gradio_user",
            preferences=["github"],
            github_token=github_token,
            repo=github_repo
        )
    else:
        context = KnowledgeContext(user_id="gradio_user", preferences=["general"],user_email="<EMAIL>")

    # Run the async function
    try:
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(process_question_async(message, context))
        response = ""
        if result['answer'] is not None:
            response = f"**Answer:**\n{result['answer']}\n\n"
            response += f"**Sources:** {', '.join(result['references'])}"
 
        return response
    except Exception as e:
        return f"❌ Error: {str(e)}"

def create_chat_interface():
    """Create and configure the Gradio chat interface"""
    custom_css = """
    .gradio-container {
        max-width: 1200px;
        margin: auto;
    }
    .chat-message {
        padding: 10px;
        margin: 5px 0;
        border-radius: 10px;
    }
    """
    with gr.Blocks(title="SageBase Knowledge Assistant", css=custom_css) as demo:
        gr.Markdown("""
        # �� SageBase Knowledge Assistant
        
        Ask me anything! I can search through:
        - 🌐 **Internet** - Current information and general knowledge
        - 📚 **Local Knowledge** - Internal company information and documentation
        - 🔍 **Document Sources** - Confluence, Notion, GitHub, Slack, JIRA, etc.
        - 👥 **Workspaces** - Team-specific information
        **Examples you can try:**
        - "What are the latest developments in AI?"
        - "How do React hooks work?"
        - "I have a babel preset-env error, how do I fix it?"
        - "Search for API documentation in confluence"
        - "Find deployment guides in the engineering workspace"
        - "What is JWT authentication and how do I implement it?"
        - "Search for user research in the product workspace"
        """)
        chatbot = gr.Chatbot(
            value=[],
            height=500,
            show_label=False,
            container=True,
            avatar_images=("👤", "🤖")
        )
        with gr.Row():
            msg = gr.Textbox(
                placeholder="Ask me anything...",
                container=False,
                scale=7,
                show_label=False,
                autofocus=True
            )
            github_token = gr.Textbox(
                placeholder="GitHub Token (optional)",
                type="password",
                label="GitHub Token",
                scale=2
            )
            github_repo = gr.Textbox(
                placeholder="GitHub Repo (e.g. owner/repo, optional)",
                label="GitHub Repo",
                scale=2
            )
            submit_btn = gr.Button("🚀 Ask", variant="primary", scale=1)
            clear_btn = gr.Button("🗑️ Clear", variant="secondary", scale=1)
        
        def user_message(user_input, history, github_token, github_repo):
            return "", history + [[user_input, None]], github_token, github_repo
        def bot_message(history, github_token, github_repo):
            if history and history[-1][1] is None:
                user_input = history[-1][0]
                bot_response = chatbot_response(user_input, history, github_token, github_repo)
                history[-1][1] = bot_response
            return history
        msg.submit(user_message, [msg, chatbot, github_token, github_repo], [msg, chatbot, github_token, github_repo], queue=False).then(
            bot_message, [chatbot, github_token, github_repo], chatbot
        )
        submit_btn.click(user_message, [msg, chatbot, github_token, github_repo], [msg, chatbot, github_token, github_repo], queue=False).then(
            bot_message, [chatbot, github_token, github_repo], chatbot
        )
        clear_btn.click(lambda: ([], "", "", ""), outputs=[chatbot, msg, github_token, github_repo])
        # Example questions
        gr.Examples(
            examples=[
                "What are the latest developments in artificial intelligence?",
                "How do React hooks work?",
                "I have a babel preset-env error, how do I fix it?",
                "Search for API documentation in confluence",
                "Find deployment guides in the engineering workspace",
                "What is JWT authentication and how do I implement it?",
                "Search for user research in the product workspace"
            ],
            inputs=msg,
            label="💡 Example Questions"
        )
        gr.Markdown("""
        ---
        **🔧 Configuration:**
        - Create a `.env` file with: `AZURE_OPENAI_API_KEY=your-azure-api-key-here`
        - Or set environment variable: `export AZURE_OPENAI_API_KEY='your-azure-api-key-here'`
        - Get your Azure OpenAI API key from your Azure portal
        - The system searches multiple platforms and combines information for comprehensive answers
        - All searches are traced and platforms are cited in responses
        **📚 Search Capabilities:**
        - Internet search via Serper API (Google)
        - Local knowledge base with company-specific information
        - Document source search (Confluence, Notion, GitHub, Slack, JIRA, Google Drive)
        - Workspace-specific search for team information
        """)
    return demo
def main():
    """Main function to launch the Gradio chat interface or CLI Q&A"""
    print("🚀 Starting SageBase Knowledge Assistant")
    print("=" * 50)
    
    # Check if running in standalone mode
    if not DJANGO_AVAILABLE:
        print("⚠️  Running in standalone mode without Django integration")
        print("   - GitHub repository search will be disabled")
        print("   - Some advanced features may not be available")
        print("   - For full functionality, run within Django environment")
        print()

    # Check API key
    if not os.getenv('AZURE_OPENAI_API_KEY'):
        print("⚠️  Azure OpenAI API key not found!")
        print("   Please set your AZURE_OPENAI_API_KEY environment variable:")
        print("   Option 1: Create .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here")
        print("   Option 2: Set environment variable: export AZURE_OPENAI_API_KEY='your-azure-api-key-here'")
        print("   Get your Azure OpenAI API key from your Azure portal")
        print("\n   The chat interface will still launch, but you'll need to set the API key to use it.")

    """Main function to launch the Gradio chat interface"""
    logger.info("🚀 Starting SageBase Knowledge Assistant")
    logger.info("=" * 50)
    
    # Check if running in standalone mode
    if not DJANGO_AVAILABLE:
        logger.warning("⚠️  Running in standalone mode without Django integration")
        logger.warning("   - GitHub repository search will be disabled")
        logger.warning("   - Some advanced features may not be available")
        logger.warning("   - For full functionality, run within Django environment")
        logger.warning("")
    
    # Check API key
    if not os.getenv('AZURE_OPENAI_API_KEY'):
        logger.warning("⚠️  Azure OpenAI API key not found!")
        logger.warning("   Please set your AZURE_OPENAI_API_KEY environment variable:")
        logger.warning("   Option 1: Create .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here")
        logger.warning("   Option 2: Set environment variable: export AZURE_OPENAI_API_KEY='your-azure-api-key-here'")
        logger.warning("   Get your Azure OpenAI API key from your Azure portal")
        logger.warning("\n   The chat interface will still launch, but you'll need to set the API key to use it.")
    
    # Initialize QA system
    logger.info("🔧 Initializing knowledge assistant...")
    
    # Check if Azure OpenAI is configured
    if not azure_client:
        logger.error("❌ Azure OpenAI client not configured")
        print("⚠️  Knowledge assistant will need Azure OpenAI API key configuration")
    else:
        logger.info(" Azure OpenAI client configured successfully")
    
    user_email = "<EMAIL>" if DJANGO_AVAILABLE else "<EMAIL>"
    if initialize_qa_system(workspace="sagebase", user_email=user_email): #workspace is the name of the database
        logger.info("✅ Knowledge assistant ready!")
    else:
        print("⚠️  Knowledge assistant will need API key configuration")

    # Create and launch the interface
    logger.info("🌐 Launching chat interface...")
    demo = create_chat_interface()
    # Launch with configuration
    demo.launch(
        server_name="127.0.0.1",  # Local access only
        server_port=7860,         # Default Gradio port
        share=False,              # No public link
        debug=True,               # Enable debug mode
        show_error=True,          # Show errors in the interface
        quiet=False               # Show launch information
    )
if __name__ == "__main__":
    main()
