"""
Question Answering Agent with Internet Search and Local Tools
A simple AI agent that can answer user questions by:
1. Searching the internet for current information
2. Using local database/function tools for internal knowledge
3. Combining mutiple platforms to provide comprehensive answers
4. Using persistent session management and intelligent memory retrieval
"""
import json
import os
import sys
import requests
import logging
from asgiref.sync import async_to_sync, sync_to_async

from knowledge_spaces_Q_A.function_tools import search_local_knowledge
from .github_agent import create_github_agent

from knowledge_spaces_Q_A.chat_models import ChatUserContext

# Enable tracing for OpenAI platform logs
# os.environ.setdefault('AGENTS_DISABLE_TRACING', 'true')  # Commented out to enable tracing
# os.environ.setdefault('OPENAI_DISABLE_TRACING', 'true')  # Commented out to enable tracing
# os.environ.setdefault('OPENAI_TRACING_ENABLED', 'false')  # Commented out to enable tracing
# os.environ.setdefault('AGENTS_TRACING_DISABLED', 'true')  # Commented out to enable tracing

# Set up logger
logger = logging.getLogger(__name__)
from dataclasses import dataclass, field
from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from agents import Agent, function_tool, ModelSettings, RunContextWrapper, OpenAIChatCompletionsModel

import gradio as gr

# Check if Django is available
try:
    import django
    DJANGO_AVAILABLE = True
except ImportError:
    DJANGO_AVAILABLE = False

# Import Django utilities if available
if DJANGO_AVAILABLE:
    try:
        from asgiref.sync import sync_to_async
    except ImportError:
        sync_to_async = None
else:
    sync_to_async = None

# Import utility functions if Django is available
if DJANGO_AVAILABLE:
    try:
        from .utils import get_user_and_company_by_email, get_github_repositories_and_installation_id
        from integrations.models import CompanyIntegration, IntegrationTool
    except ImportError:
        get_user_and_company_by_email = None
        get_github_repositories_and_installation_id = None
        CompanyIntegration = None
        IntegrationTool = None
else:
    get_user_and_company_by_email = None
    get_github_repositories_and_installation_id = None
    CompanyIntegration = None
    IntegrationTool = None


from knowledge_spaces_Q_A.chat_orchestrator import ChatUserContext

# Import Azure configuration from separate module to avoid circular imports
try:
    from konowledge_detection_agents.azure_config import (
         async_azure_client, used_model, AZURE_ENDPOINT, AZURE_MODEL_NAME, 
        AZURE_DEPLOYMENT, AZURE_API_VERSION, gpt4o_mini_model, embedding_model
    )
except ImportError as e:
    logger.warning(f"Could not import Azure configuration: {e}")
    # Fallback values
    async_azure_client = None
    used_model = None
    AZURE_ENDPOINT = None
    AZURE_MODEL_NAME = None
    AZURE_DEPLOYMENT = None
    AZURE_API_VERSION = None
    gpt4o_mini_model = None
    embedding_model = None

# Azure client is now imported from azure_config module


from typing import Optional
from pydantic import BaseModel, Field

# --- Structured output -------------------------------------------------------

class KnowledgeExistUpdateResult(BaseModel):
    """
    Verdict on whether the knowledge already exists and whether it should be updated.
    Invariants:
      - If exists == False: needs_update, url, updated_question, updated_knowledge MUST be null.
      - If exists == True and needs_update == False: url SHOULD be set if available; updated_* MUST be null.
      - If exists == True and needs_update == True: url MUST be set; updated_* MUST be set.
    """
    exists: bool = Field(
        description="True if the information already exists in the knowledge base; False if not found."
    )
    needs_update: Optional[bool] = Field(
        default=None,
        description="If exists is True: True when the stored answer conflicts or is outdated; False when it matches."
    )
    url: Optional[str] = Field(
        default=None,
        description="URL (or identifier) of the existing knowledge entry when applicable."
    )
    updated_question: Optional[str] = Field(
        default=None,
        description="Proposed revised question/title if an update is required."
    )
    updated_knowledge: Optional[str] = Field(
        default=None,
        description="Proposed revised knowledge/answer if an update is required."
    )

# --- Agent factory -----------------------------------------------------------

def create_internal_search_agent(question: str, answer: str) -> Agent:
    """
    Create an agent that:
      1) Searches local knowledge for entries matching `question`/`answer`.
      2) Decides whether the information exists and if an update is needed.
      3) Returns a strict KnowledgeExistUpdateResult.
    """
    tools = [search_local_knowledge]

    # Compose a crisp instruction with an explicit decision tree + invariants.
    instructions = f"""
You are the Internal Knowledge Auditor.

Goal:
- Determine whether the following information already exists in our local knowledge base.
- If it exists, check whether the stored content conflicts with or differs materially from the new answer.
- If needed, propose a precise, minimal update.

New candidate item:
- Question: {question!r}
- Answer (proposed): {answer!r}

Available tool:
- search_local_knowledge(query: str) -> entries[]
  Each entry typically includes: title/question, answer/content, url/id, and optional metadata.

Procedure (follow exactly):
1) Call search_local_knowledge at least once using a precise query derived from the provided question.
   - If needed, try a second query using key terms extracted from the proposed answer.
2) Compare the best-matching existing entry (if any) with the proposed answer.
   - Consider meaning, not only wording. Minor phrasing differences that do not change meaning are NOT conflicts.
   - Treat differences in numbers, URLs, API params, or step-by-step procedures as conflicts.
3) Decide and output one JSON object matching the schema, obeying these invariants:
   - If nothing relevant is found:
       exists = false
       needs_update = null
       url = null
       updated_question = null
       updated_knowledge = null
   - If a relevant entry exists and matches (no meaningful difference):
       exists = true
       needs_update = false
       url = the entry URL/ID if available
       updated_question = null
       updated_knowledge = null
   - If a relevant entry exists but conflicts or is outdated:
       exists = true
       needs_update = true
       url = the entry URL/ID (REQUIRED)
       updated_question = the best revised question/title (REQUIRED)
       updated_knowledge = the best revised knowledge/answer (REQUIRED)
4) Be concise and deterministic. Do not include any extra keys.

Notes:
- If multiple entries match, pick the single most authoritative or most recent one.
- Preserve existing good content; only change what’s necessary.
- Use neutral, company-agnostic phrasing in updated content.
"""

    return Agent(
        name="Internal_Search_Agent",
        instructions=instructions.strip(),
        model=OpenAIChatCompletionsModel(
            model=used_model,
            openai_client=async_azure_client,
        ),
        model_settings=ModelSettings(
            temperature=0.0,   # deterministic comparisons
            max_tokens=512
        ),
        tools=tools,
        output_type=KnowledgeExistUpdateResult,
    )



# Import vectordb interfaces
import sys
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__))))
from vectordb.interfaces import get_chroma_collection
from vectordb.models.document import DataSource
# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    import os
    from pathlib import Path
    
    # Check environment type
    ENVIRONMENT = os.getenv('DJANGO_ENV', 'development')  # Default to development
    
    # Try multiple .env file locations based on environment
    current_dir = Path(__file__).parent  # konowledge_detection_agents/
    project_root = current_dir.parent    # SageBase_BackEnd.git/
    
    if ENVIRONMENT == 'deployment':
        env_locations = [
            current_dir / ".env.deployment",       # konowledge_detection_agents/.env.deployment
            project_root / ".env.deployment",      # SageBase_BackEnd.git/.env.deployment
            Path.cwd() / ".env.deployment"         # Current working directory/.env.deployment
        ]
        logger.info(f"🌐 Loading deployment environment files...")
    else:
        env_locations = [
            current_dir / ".env.dev",       # konowledge_detection_agents/.env.dev
            project_root / ".env.dev",      # SageBase_BackEnd.git/.env.dev
            Path.cwd() / ".env.dev"         # Current working directory/.env.dev
        ]
        logger.info(f"Loading development environment files...")
    env_file_found = False
    for env_path in env_locations:
        if env_path.exists():
            logger.info(f"Found .env file at: {env_path}")
            load_dotenv(env_path)
            env_file_found = True
            break
    if not env_file_found:
        logger.warning("⚠️  No .env file found in any of these locations:")
        for env_path in env_locations:
            logger.warning(f"   - {env_path}")
        logger.warning("   Please create a .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here")
    # Check if Azure OpenAI API key is loaded
    if os.getenv('AZURE_OPENAI_API_KEY'):
        logger.info("AZURE_OPENAI_API_KEY loaded successfully")
        # Set ChromaDB to use OpenAI embeddings to match existing collections
        if not os.getenv('CHROMA_EMBEDDING_TYPE'):
            os.environ['CHROMA_EMBEDDING_TYPE'] = 'openai'
            logger.info("🔧 Set CHROMA_EMBEDDING_TYPE=openai")
        if not os.getenv('CHROMA_EMBEDDING_MODEL'):
            os.environ['CHROMA_EMBEDDING_MODEL'] = 'text-embedding-3-small'
            logger.info("🔧 Set CHROMA_EMBEDDING_MODEL=text-embedding-3-small")
    else:
        logger.error("❌ AZURE_OPENAI_API_KEY not found in environment variables")
except ImportError:
    logger.warning("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    logger.warning(f"⚠️  Could not load .env file: {str(e)}")
# Check if Azure OpenAI API key is available
if not os.getenv('AZURE_OPENAI_API_KEY'):
    logger.error("❌ AZURE_OPENAI_API_KEY not found!")
    logger.error("   Please create a .env file with: AZURE_OPENAI_API_KEY=your-azure-api-key-here")
    logger.error("   Or set environment variable: export AZURE_OPENAI_API_KEY='your-azure-api-key-here'")
    logger.error("   Get your Azure OpenAI API key from your Azure portal")
# Set your OpenAI API key
# os.environ['OPENAI_API_KEY'] = 'your-api-key-here'
# ============================================================================
# Data Models
# ============================================================================
class SearchResult(BaseModel):
    """Search result from internet or local source"""
    source: str  # "internet" or "local"
    title: str
    content: str
    url: Optional[str] = None
    relevance_score: float = 0.0
class StructuredAnswer(BaseModel):
    """Structured answer output from the agent"""
    answer: str = Field(description="The comprehensive answer to the user's question")
    references: List[str] = Field(description="List of sources and references used to compile the answer, use url when available, otheriwise, use document title or source name")
class Answer(BaseModel):
    """Complete answer to user question"""
    question: str
    answer: str
    sources: List[SearchResult]
    confidence: float
    timestamp: str

class NewCreatedQA(BaseModel):
    """Structured answer output from the agent"""
    question: str
    answer: str
    references: List[str]

class NewCreatedQAResponse(BaseModel):
    """Response containing multiple Q&A instances"""
    all_qa: List[NewCreatedQA]

@dataclass
class KnowledgeContext:
    """Simple context for question answering"""
    user_id: str
    user_email: Optional[str] = None
    preferences: List[str] = field(default_factory=lambda: ["general"])
    github_token: Optional[str] = None  # Add GitHub token to context
    repo: Optional[str] = None          # Add repo to context
    history: List[dict] = field(default_factory=list)  # Add conversation history
    available_github_repositories: List[str] = field(default_factory=list)  # Add available repos
    installation_id: Optional[str] = None # Add installation_id to context
    company: Any = None # Add company to context
    sources: List[str] = field(default_factory=list) # Add sources to context
    
    def __post_init__(self):
        if self.user_email is None:
            self.user_email = "<EMAIL>"






@function_tool
def search_github_repo(
    wrapper: RunContextWrapper[KnowledgeContext],
    question: str = "",
    method: str = "llm"
) -> str:
    """
    Search a GitHub repository for answers using the internal /github/ask/ API.
    Generates access token from installation_id and uses available_github_repositories from context.
    """
    logger.info(f"@function_tool ->  Calling tool search_github_repo for question: {question}")
    if not DJANGO_AVAILABLE:
        return "GitHub repository search is not available in standalone mode. Please run this within the Django environment."
    
    import requests
    
    try:
        from django.conf import settings
        from integrations.github.api.services import get_installation_access_token
    except Exception as e:
        return f"GitHub integration not available - Django error: {str(e)}"
    
    context = wrapper.context
    logger.warning(f" Calling tool search_github_repo for query: {question}")
    
    if not context or not context.installation_id or not context.available_github_repositories:
        logger.error("Error")
        return "Error: installation_id and available_github_repositories must be provided in context."
    
    
    # Extract repo names from the objects
    repos_to_query = []
    for repo_obj in context.available_github_repositories:
        if isinstance(repo_obj, dict) and 'full_name' in repo_obj:
            repos_to_query.append(repo_obj['full_name'])  # type: ignore
        elif isinstance(repo_obj, str):
            repos_to_query.append(repo_obj)
    
    logger.info("repos to query: ", repos_to_query)
    if not repos_to_query:
        return "No accessible GitHub repositories found for this user."
    
    # Generate installation access token
    try:
        access_token = get_installation_access_token(context.installation_id)
        logger.info(f"✅ Generated access token for installation {context.installation_id}")
    except Exception as e:
        return f"Failed to generate installation access token: {e}"
    
    # Query each repo using your /ask endpoint
    answers = []
    for repo in repos_to_query:
        url = f"{settings.BACKEND_PUBLIC_URL}/api/integrations/github/api/ask/"
        payload = {
            "repo": repo,
            "question": question,
            "method": method,
            "github_token": access_token,  # Pass the generated access token
        }
        try:
            logger.warning(f"🔍 Querying /ask endpoint for repo: {repo}")
            response = requests.post(url, json=payload, timeout=20)
            response.raise_for_status()
            data = response.json()
            answer = data.get("answer", "No answer returned from GitHub API.")
            answers.append(f"**{repo}:**\n{answer}")
        except Exception as e:
            answers.append(f"**{repo}:**\nError querying GitHub API: {str(e)}")
    
    # Combine all answers
    combined_answer = "\n\n".join(answers)
    references = [
        f"GitHub Repository Search: {', '.join(repos_to_query)}"
    ]
    
    return {
        "answer": combined_answer,
        "references": references
    }





@function_tool
async def update_local_knowledge(qa_id: str, question_content: str = None, answer_content: str = None, question_title: str = None) -> str:
    """Update existing knowledge in the local knowledge base."""
    from knowledge_spaces_Q_A.models import QA
    from knowledge_spaces_Q_A.utils import embed_q_a
    
    try:
        logger.info(f"@function_tool ->  Calling tool update_local_knowledge for qa_id: {qa_id}")
        # Get the existing QA instance
        qa_instance = await sync_to_async(lambda: QA.objects.get(id=qa_id), thread_sensitive=False)()
        
        if qa_instance is None:
            return f"❌ QA instance with ID {qa_id} not found"
        
        # Update only the fields that are provided
        updated_fields = []
        if question_content is not None:
            qa_instance.question_content = question_content
            updated_fields.append("question_content")
        
        if answer_content is not None:
            qa_instance.answer_content = answer_content
            updated_fields.append("answer_content")
        
        if question_title is not None:
            qa_instance.question_title = question_title
            updated_fields.append("question_title")
        
        # Save the updated instance
        await sync_to_async(qa_instance.save, thread_sensitive=False)()
        
        # Re-embed the updated knowledge
        success = await sync_to_async(embed_q_a, thread_sensitive=False)(qa_instance)
        
        if success:
            logger.info(f"📚 Successfully updated and embedded knowledge: {qa_instance.question_title}")
            return f"✅ Knowledge updated successfully: {qa_instance.question_title} (Updated fields: {', '.join(updated_fields)})"
        else:
            logger.error(f"❌ Failed to embed updated knowledge: {qa_instance.question_title}")
            return f"❌ Failed to embed updated knowledge: {qa_instance.question_title}"
            
    except Exception as e:
        logger.exception(f"❌ Error updating knowledge: {e}")
        return f"❌ Error updating knowledge: {str(e)}"


# ============================================================================
# Gradio Chat Interface
# ============================================================================

def get_ghu_token_and_allowed_repos(user_email: str):
    """Fetch ghu_token and allowed repos for a user by email."""
    if not DJANGO_AVAILABLE:
        logger.warning("⚠️ Django not available - GitHub integration disabled")
        return None, [], None
    
    try:
        user_instance, company = get_user_and_company_by_email(user_email)
        if not company:
            raise Exception("No company found for this user.")
        allowed_repos, installation_id = get_github_repositories_and_installation_id(company)
        if not installation_id or not allowed_repos:
            raise Exception("No GitHub App installation or allowed repos found for this user.")
        github_tool = IntegrationTool.objects.get(slug="github")  # type: ignore
        integration = CompanyIntegration.objects.filter(company=company, tool=github_tool).first()  # type: ignore
        if not integration or not integration.config:
            raise Exception("No GitHub integration found for this company.")
        
        # Use the new automatic token refresh functionality
        try:
            ghu_token = integration.get_valid_github_token()
            return ghu_token, allowed_repos, installation_id
        except Exception as token_error:
            logger.warning(f"⚠️ Error refreshing GitHub token: {token_error}")
            # Fallback to old method for backward compatibility
            ghu_token = integration.config.get("ghu_token")
            if not ghu_token:
                raise Exception("No user access token (ghu_token) found. Please complete the OAuth flow first.")
            return ghu_token, allowed_repos, installation_id
            
    except Exception as e:
        logger.warning(f"⚠️ Error getting GitHub token: {e}")
        return None, [], None

# Updated initialize_qa_system to accept user_email and set up MCP server
qa_system = None
