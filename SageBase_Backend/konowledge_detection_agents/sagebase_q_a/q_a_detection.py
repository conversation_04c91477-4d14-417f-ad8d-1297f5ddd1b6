#!/usr/bin/env python3
"""
Document Processing Module for SageBase API
This module processes individual documents using the unified interfaces.py API.
Used by the API for file uploads.
"""
import os
import sys
import logging

from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import hashlib
from agents import Agent, Runner, function_tool, ModelSettings, trace, RunContextWrapper, OpenAIChatCompletionsModel
from pydantic import BaseModel, Field
from django.contrib.auth.models import User
from konowledge_detection_agents.question_answering import create_internal_search_agent, used_model, async_azure_client
from knowledge_spaces_Q_A.utils import get_datasources_to_use_for_search
from knowledge_spaces_Q_A.utils import create_qa_instance
from knowledge_spaces_Q_A.chat_models import ChatUserContext
from knowledge_spaces_Q_A.models import Knowledge_Space


# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Configure Django settings for standalone script
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')

import django
django.setup()

# Import generic colored logging setup
from logger.logging_utils import setup_colored_logging

# Setup logging
colorlog_available = setup_colored_logging()
logger = logging.getLogger(__name__)

if not colorlog_available:
    logger.warning("colorlog not available - using basic logging. Install with: pip install colorlog")

# Import the vectordb interfaces
from vectordb.models.document import DataSource

def load_env_file():
    """Load environment variables from .env.dev file"""
    try:
        from dotenv import load_dotenv
        
        # Define possible env file locations in order of preference
        base_dir = Path(__file__).parent.parent.parent
        env_files = [
            base_dir / ".env.dev",
            base_dir / ".env.development", 
            base_dir / ".env",
            Path.cwd() / ".env.dev",
            Path.cwd() / ".env"
        ]
        
        # Load the first existing env file
        env_file_loaded = None
        for env_file in env_files:
            if env_file.exists():
                load_dotenv(env_file)
                env_file_loaded = env_file
                break
        
        if env_file_loaded:
            logger.info(f"📁 Loaded environment file: {env_file_loaded}")
            
            # Log key environment variables (without sensitive data)
            django_env = os.getenv('DJANGO_ENV', 'not set')
            debug_mode = os.getenv('DEBUG', 'not set')
            logger.debug(f"   DJANGO_ENV: {django_env}")
            logger.debug(f"   DEBUG: {debug_mode}")
            
            # Check for database configuration
            if os.getenv('DATABASE_URL'):
                logger.debug("   DATABASE_URL: [configured]")
            else:
                logger.debug("   DATABASE_URL: [not set]")
                
        else:
            logger.warning("⚠️  No .env file found - using system environment variables only")
            
    except ImportError:
        logger.warning("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
        logger.warning("   Environment variables will not be loaded from .env files")




class SimpleTextProcessor:
    """Simple text processing utility without Django dependencies"""
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        
        import re
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might interfere with processing
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # Normalize unicode
        text = text.encode('utf-8', errors='ignore').decode('utf-8')
        
        return text
    
    def extract_metadata_from_text(self, text: str) -> Dict[str, Any]:
        """Extract basic metadata from text"""
        if not text:
            return {}
        
        import re
        
        metadata = {
            'char_count': len(text),
            'word_count': len(text.split()),
            'line_count': len(text.splitlines())
        }
        
        # Extract potential title (first line if it looks like a title)
        lines = text.split('\n')
        if lines:
            first_line = lines[0].strip()
            if len(first_line) < 100 and not first_line.endswith('.'):
                metadata['extracted_title'] = first_line
        
        # Extract email addresses
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        if emails:
            metadata['emails'] = list(set(emails))
        
        # Extract URLs
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
        if urls:
            metadata['urls'] = list(set(urls))
        
        return metadata


class DocumentParser:
    """Parser for different document types"""
    
    def __init__(self):
        self.text_processor = SimpleTextProcessor()
        self.supported_extensions = {'.txt', '.md', '.pdf', '.docx', '.html', '.htm', '.json', '.csv'}
    
    def parse_text_file(self, file_path: Path) -> str:
        """Parse plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
    
    def parse_pdf_file(self, file_path: Path) -> str:
        """Parse PDF file"""
        try:
            import PyPDF2
            text = ""
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
            return text
        except ImportError:
            logger.warning("PyPDF2 not installed. Install with: pip install PyPDF2")
            try:
                # Try alternative PDF parser
                import pdfplumber
                text = ""
                with pdfplumber.open(file_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                return text
            except ImportError:
                logger.error("No PDF parser available. Install PyPDF2 or pdfplumber")
                return f"Could not parse PDF file: {file_path.name}"
        except Exception as e:
            logger.error(f"Error parsing PDF {file_path}: {e}")
            return f"Error parsing PDF: {str(e)}"
    
    def parse_docx_file(self, file_path: Path) -> str:
        """Parse DOCX file"""
        try:
            from docx import Document
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except ImportError:
            logger.warning("python-docx not installed. Install with: pip install python-docx")
            return f"Could not parse DOCX file: {file_path.name}"
        except Exception as e:
            logger.error(f"Error parsing DOCX {file_path}: {e}")
            return f"Error parsing DOCX: {str(e)}"
    
    def parse_html_file(self, file_path: Path) -> str:
        """Parse HTML file"""
        try:
            from bs4 import BeautifulSoup
            with open(file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                return soup.get_text()
        except ImportError:
            logger.warning("BeautifulSoup not installed. Install with: pip install beautifulsoup4")
            return self.parse_text_file(file_path)
        except Exception as e:
            logger.error(f"Error parsing HTML {file_path}: {e}")
            return f"Error parsing HTML: {str(e)}"
    
    def parse_json_file(self, file_path: Path) -> str:
        """Parse JSON file"""
        try:
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert JSON to readable text
                return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error parsing JSON {file_path}: {e}")
            return f"Error parsing JSON: {str(e)}"
    
    def parse_csv_file(self, file_path: Path) -> str:
        """Parse CSV file"""
        try:
            import csv
            text = ""
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    text += " | ".join(row) + "\n"
            return text
        except Exception as e:
            logger.error(f"Error parsing CSV {file_path}: {e}")
            return f"Error parsing CSV: {str(e)}"
    
    def parse_file(self, file_path: Path) -> str:
        """Parse file based on extension"""
        extension = file_path.suffix.lower()
        
        if extension in {'.txt', '.md'}:
            return self.parse_text_file(file_path)
        elif extension == '.pdf':
            return self.parse_pdf_file(file_path)
        elif extension == '.docx':
            return self.parse_docx_file(file_path)
        elif extension in {'.html', '.htm'}:
            return self.parse_html_file(file_path)
        elif extension == '.json':
            return self.parse_json_file(file_path)
        elif extension == '.csv':
            return self.parse_csv_file(file_path)
        else:
            logger.warning(f"Unsupported file type: {extension}")
            return f"Unsupported file type: {extension}"
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if file type is supported"""
        return file_path.suffix.lower() in self.supported_extensions




    
    
class StandaloneSageBaseDocumentProcessor:
    """Document processor for SageBase documents using the unified interfaces.py API"""
    
    def __init__(self, tags: List[str] = None, author: str = None):
        """
        Initialize document processor using interfaces.py API
        
        Args:
            tags: List of tags for documents
            author: Author name for documents
        """
        self.tags = tags or []
        self.author = author
        
        # Initialize services
        self.parser = DocumentParser()
        self.text_processor = SimpleTextProcessor()
        
        logger.info(f"Initialized SageBase document processor")
        logger.info("Using unified interfaces.py API for vector database operations")
    
    def get_file_hash(self, file_path: Path) -> str:
        """Generate hash of file content for deduplication"""
        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
    
 
    def process_document(self, file_path: Path, knowledge_space_instance: Knowledge_Space, user: Any) -> Dict[str, Any]:
        """Process a single document using the interfaces.py API"""
        logger.info(f"Processing document: {file_path.name}")
        
        try:
            # Parse document content
            content = self.parser.parse_file(file_path)
            
            if not content.strip():
                logger.warning(f"Empty content for file: {file_path}")
                return {"success": False, "error": "Empty content"}
            
            class StructuredAnswerQuestion(BaseModel):
                """Structured Q&A output from the agent for SageBase QA model"""
                question_title: str = Field(description="The title/summary of the question")
                question_content: str = Field(description="The detailed question content")
                question_tags: List[str] = Field(description="Tags related to the question (e.g., project_name, device_name, etc.)")
                answer_content: str = Field(description="The main answer content")

            
            class QADetectionResult(BaseModel):
                """Result containing a list of detected Q&As from the document"""
                qa_list: List[StructuredAnswerQuestion] = Field(description="List of Q&A pairs detected in the document")
            
            #create openai agent to extract the content of the document
            instructions = """
            You are SageBase, the software architect of the company and the one responsable for the knowledge base of the company.
            Here you receive a document, could be a transcript, some manual notes,...The data inside it could be unsorted, but you will be looking for any information that can be used for software development, like updates, bug fixes, debugging tips...

            You will extract the knowledge as a list of questions and answers.
            For each question and answer you find, extract:
                1. question_title: A concise title/summary of the question
                2. question_content: The detailed question content
                3. question_tags: Relevant tags (e.g., project_name, device_name, technology, etc.) 
                4. answer_content: The main answer content

            
            Look for patterns like:
            - FAQ sections
            - Q&A formats
            - Problem-solution pairs
            - How-to guides with questions
            - Troubleshooting sections
            
            Make the queston concise and to the point, example: How to fix this error "error details", how to do this, how to do that, etc.
            Be careful to not extract any information that is not related to the software development, like personal information, company information, etc.
            Return a list of structured Q&A objects.

            """


            agent = Agent(
                name="Document Q&A Extractor",
                instructions=instructions,
                tools=[],
                model= OpenAIChatCompletionsModel(
                    model=used_model,
                    openai_client=async_azure_client,
                ),
                output_type=QADetectionResult,  # Structured output with list of Q&As
            )

            # Run the Q&A detection agent on the document content
            try:
                logger.info(f"🔍 Running Q&A detection on document: {file_path.name}")
                
                # Use synchronous runner instead of async
                import asyncio
                result = asyncio.run(Runner.run(
                    agent,
                    input=content,
                    max_turns=10
                ))
                
                qa_result = result.final_output

                company = user.company 
                collections_to_search = [str(company.id)]

                if qa_result and qa_result.qa_list:
                    logger.info(f"✅ Detected {len(qa_result.qa_list)} Q&A pairs from document")
                    
                    # Import QA model and create Q&As
                    from knowledge_spaces_Q_A.models import  Knowledge_Space
                    
                    # Synchronous database operations to search for any duplicate
                    try:

                        if knowledge_space_instance.is_user_owned:
                            # Add private collection for users owned knowledge spaces
                            collections_to_search.append(str(user.id))
                        
                        # Create QA objects for each detected Q&A
                        created_qas = []

                        sources_to_search = None # search all sources
 
 
                        knowledge_Context = ChatUserContext(
                            user=user,
                            knowledge_space_name=knowledge_space_instance.name,
                            company=company,
                            collections_to_search=collections_to_search,
                            sources_to_search=sources_to_search,
                        )
                  
                        for qa_data in qa_result.qa_list:
                            agent = create_internal_search_agent(qa_data.question_content, qa_data.answer_content)
                            # Let's first check if the question and answer already exist in the vector database
                            # Run the agent synchronously using asyncio.run()
                            import asyncio
                            #we need to pass the context to the agent
                            result = asyncio.run(Runner.run(agent, qa_data.question_content,max_turns=10,context=knowledge_Context))
                            
                            structured_answer = result.final_output
                            
                            if structured_answer.exists and not structured_answer.needs_update:
                                logger.info(f"🔍 QA already exists: {qa_data.question_content}")
                                continue
                            elif structured_answer and structured_answer.needs_update and (structured_answer.updated_question or structured_answer.updated_knowledge ):
                                logger.warning(f"🔍 Detecetd some info to get updated {company.name} knowledge_space_name: {knowledge_space_instance.name} Question: {qa_data.question_content} : {qa_data.question_content}")
                                #lets create a notification for the user
                                from knowledge_spaces_Q_A.models import Notification
                                Notification.objects.create(
                                    title=f"Q&A needs update!",
                                    details=f"""{structured_answer.url} 
                                    Suggested new question: {structured_answer.updated_question} 
                                    Suggested new answer : {structured_answer.updated_knowledge}""",  
                                    type="warning",
                                    severity="high",
                                    source="Q&A",
                                    user=user,
                                    company=company,
                                    status="pending",
                                    accepted_at=None,
                                    ignored_at=None,
                                )
                                continue
                                
                                
                            qa = create_qa_instance(
                                company,
                                qa_data.question_content,
                                qa_data.answer_content,
                                qa_data.question_title,
                                knowledge_space_instance,
                                user.id,
                                auto_approve=False,
                                created_by=(user.username if hasattr(user, 'username') and user.username else (user.email if hasattr(user, 'email') else 'Unknown'))
                            )    

                            if qa:
                                created_qas.append(qa.id)
                                logger.info(f"📝 Created QA: {qa.question_title}, they need to to be approved to be embedded in the vector database")
                            else:
                                logger.error(f"❌ Failed to create QA: {qa_data.question_content}")
                        
                        logger.info(f"🎉 Successfully created {len(created_qas)} Q&As from document")
                        
                        return {
                            "success": True,
                            "detected_qas_count": len(created_qas),
                            "message": f"Successfully detected {len(created_qas)} new Q&As ready for approval"
                        }
                        
                    except Exception as e:
                        logger.exception(f"Error creating Q&As: {e}")
                        return {
                            "success": False,
                            "error": str(e)
                        }
                        
                else:
                    logger.info(f"ℹ️ No Q&A pairs detected in document: {file_path.name}")
                    return {
                        "success": True,
                        "detected_qas_count": 0,
                        "message": "No Q&As detected in this document"
                    }
                    
            except Exception as e:
                logger.exception(f"❌ Error during Q&A detection: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
            
            return {
                "success": True,
                "detected_qas_count": 0,
                "message": "Document processed but no Q&As detected"
            }
            
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}")
            return {
                "success": False,
                "file_path": str(file_path),
                "error": str(e)
            }
    
