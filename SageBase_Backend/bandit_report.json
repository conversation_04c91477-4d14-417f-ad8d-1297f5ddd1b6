{"errors": [], "generated_at": "2025-09-01T19:41:28Z", "metrics": {"./Notifications/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./Notifications/apps.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "./Notifications/consumers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 308, "nosec": 0, "skipped_tests": 0}, "./Notifications/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 156, "nosec": 0, "skipped_tests": 0}, "./Notifications/redis_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 286, "nosec": 0, "skipped_tests": 0}, "./Notifications/routing.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "./Notifications/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 329, "nosec": 0, "skipped_tests": 0}, "./Notifications/types.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 104, "nosec": 0, "skipped_tests": 0}, "./Notifications/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "./Notifications/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 53, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/asgi.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 151, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/async_generator_suppression.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/constants.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/management/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/management/commands/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/settings.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 554, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/views.py": {"CONFIDENCE.HIGH": 7, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 7, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 78, "nosec": 0, "skipped_tests": 0}, "./SageBase_Backend/wsgi.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "./examples/auto_async_usage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 74, "nosec": 0, "skipped_tests": 0}, "./integrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/admin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 222, "nosec": 0, "skipped_tests": 0}, "./integrations/admin_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 98, "nosec": 0, "skipped_tests": 0}, "./integrations/apps.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "./integrations/async_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 91, "nosec": 0, "skipped_tests": 0}, "./integrations/auth_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 151, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 16, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_cloud_document_converter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 46, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_cloud_document_reader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 125, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_document_converter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 83, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_document_reader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 126, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_oauth2_document_reader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 116, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/utils/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/utils/batch.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/mcp_server/documents-vector-search/main/utils/retry.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 38, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/oauth_providers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 141, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/services/django_confluence_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 732, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "./integrations/confluence/views.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 544, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/apps.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 70, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/management/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/management/commands/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/management/commands/start_discord_bot.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 51, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "./integrations/discord/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 510, "nosec": 0, "skipped_tests": 0}, "./integrations/django_auth_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 134, "nosec": 0, "skipped_tests": 0}, "./integrations/django_native_auth_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 529, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/app_webhook_receiver.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 457, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/consumers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 36, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/routing.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/serializers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 2, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 240, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 22, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 184, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/webhook_receiver.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 168, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/webhook_services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 39, "nosec": 0, "skipped_tests": 0}, "./integrations/github/api/webhooks.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 89, "nosec": 0, "skipped_tests": 0}, "./integrations/github/feed_polling_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 81, "nosec": 0, "skipped_tests": 0}, "./integrations/github/llm/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/github/llm/cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}, "./integrations/github/llm/prompts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "./integrations/github/llm/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 71, "nosec": 0, "skipped_tests": 0}, "./integrations/github/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 5, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 5, "SEVERITY.UNDEFINED": 0, "loc": 118, "nosec": 0, "skipped_tests": 0}, "./integrations/github/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "./integrations/github/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 71, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/admin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 114, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/apps.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 52, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/embedding/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/embedding/embedding_service.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 347, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/embedding/tasks.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 161, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/embedding/utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 296, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/management/commands/check_embeddable_files.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/0001_initial.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 58, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/0002_googledriveworkspace_page_token_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/0003_drivechannel.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 22, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/0004_delete_drivechannel.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/0005_googledrivefilepreference_file_state.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 21, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/0006_alter_googledrivefilepreference_file_state.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/migrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 80, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/polling/poller.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 170, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 21, "nosec": 0, "skipped_tests": 0}, "./integrations/google_drive/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1077, "nosec": 0, "skipped_tests": 0}, "./integrations/invitation_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 284, "nosec": 0, "skipped_tests": 0}, "./integrations/management/commands/create_sample_knowledge_data.py": {"CONFIDENCE.HIGH": 13, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 13, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 166, "nosec": 0, "skipped_tests": 0}, "./integrations/management/commands/fetch_project_contributors.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 226, "nosec": 0, "skipped_tests": 0}, "./integrations/management/commands/refresh_github_tokens.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "./integrations/management/commands/test_auto_async.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 90, "nosec": 0, "skipped_tests": 0}, "./integrations/middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0001_initial.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 178, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0002_initial.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 44, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0003_teaminvitation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 29, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0004_repochange_crossrepomonitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0005_repochange_cross_repo_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0006_notificationsettings.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0007_alter_notificationsettings_frequency_hours.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 12, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0008_update_project_contributor_user_foreign_key.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0009_update_team_invitation_invited_by_foreign_key.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0010_atlassianuserprofile.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 113, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/0011_add_active_search_platforms.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 57, "nosec": 0, "skipped_tests": 0}, "./integrations/migrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/models.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "./integrations/services/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/services/django_auth_service.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 397, "nosec": 0, "skipped_tests": 0}, "./integrations/services/invitation_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 169, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/api/slack_consumers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 29, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/api/slack_routing.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/api/slack_webhook_receiver.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 963, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/api/slack_webhook_services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 63, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/asgi.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/oauth_providers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 79, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/services.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 307, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 12, "nosec": 0, "skipped_tests": 0}, "./integrations/slack/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 329, "nosec": 0, "skipped_tests": 0}, "./integrations/smart_orm.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 158, "nosec": 0, "skipped_tests": 0}, "./integrations/tests.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./integrations/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 41, "nosec": 0, "skipped_tests": 0}, "./integrations/views.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 2, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 911, "nosec": 0, "skipped_tests": 0}, "./integrations/views_test.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/base_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 294, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 3, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/drive_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 355, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/google_drive_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 716, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/management_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 375, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/subscription_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 174, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/test_drive_client_full.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 67, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/test_drive_client_unit.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/google_drive/webhook_handler.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 275, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/notification_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 291, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/setup_google_auth.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 88, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/vector_db_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 462, "nosec": 0, "skipped_tests": 0}, "./interfaces_data/webhook_server.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 239, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/admin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/apps.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/management/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/management/commands/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/management/commands/clear_projects.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 59, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/management/commands/fix_project_constraints.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 78, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0001_initial.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 37, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0002_alter_project_doc_responsible_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0003_update_project_user_foreign_keys.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0004_rename_repo_type_project_repository_provider_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 70, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0005_projectcontributors.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 42, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0006_delete_projectcontributors.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/0007_add_contributions_history.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 95, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/migrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 141, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/project_members_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 317, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/serializers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 347, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/signals.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 169, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "./knowledge_map/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 650, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/admin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 119, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/api_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 218, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/apps.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/async_operations.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 92, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/async_task_queue.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 128, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/chat_models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 49, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/chat_orchestrator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 390, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/chat_response_formatting_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 161, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/comment_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 221, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/function_tools/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/function_tools/save_new_knowledge.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 69, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/function_tools/search_internet.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 101, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/function_tools/search_local_knowledge.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 122, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0001_initial.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 83, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0002_knowledge_space_user_alter_knowledge_space_company.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 21, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0005_chatsession_conversationmessage_remove_qa_edited_at_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 168, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0006_merge_20250730_1910.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0007_alter_qa_table_notification.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0008_alter_knowledge_space_unique_together_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 18, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0009_alter_chatsession_session_type_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 122, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0010_qapageview_qapageview_qa_page_vie_qa_id_5dd8e9_idx_and_more.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 57, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0011_add_responsible_users_to_knowledge_space.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 36, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0012_update_knowledge_space_id_generator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0013_update_notification_user_foreign_key.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0014_alter_notification_status.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/0015_add_created_by_to_qa.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 12, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/migrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 455, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/notification_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/serializers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 398, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/session_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 470, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/sync_notification_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 98, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 26, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 420, "nosec": 0, "skipped_tests": 0}, "./knowledge_spaces_Q_A/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 989, "nosec": 0, "skipped_tests": 0}, "./konowledge_detection_agents/api_views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 154, "nosec": 0, "skipped_tests": 0}, "./konowledge_detection_agents/azure_config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 58, "nosec": 0, "skipped_tests": 0}, "./konowledge_detection_agents/github_agent.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 180, "nosec": 0, "skipped_tests": 0}, "./konowledge_detection_agents/question_answering.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 382, "nosec": 0, "skipped_tests": 0}, "./konowledge_detection_agents/sagebase_q_a/q_a_detection.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 383, "nosec": 0, "skipped_tests": 0}, "./konowledge_detection_agents/utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 70, "nosec": 0, "skipped_tests": 0}, "./logger/enhanced_logging.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 68, "nosec": 0, "skipped_tests": 0}, "./logger/exception_handler.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 84, "nosec": 0, "skipped_tests": 0}, "./logger/logging_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 59, "nosec": 0, "skipped_tests": 0}, "./manage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "./messaging/MsTeams/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./messaging/MsTeams/teams_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 344, "nosec": 0, "skipped_tests": 0}, "./messaging/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./messaging/base_interface.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 108, "nosec": 0, "skipped_tests": 0}, "./messaging/discord/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./messaging/discord/discord_bot.py": {"CONFIDENCE.HIGH": 7, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 8, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 291, "nosec": 0, "skipped_tests": 0}, "./messaging/discord/discord_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 518, "nosec": 0, "skipped_tests": 0}, "./messaging/examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 261, "nosec": 0, "skipped_tests": 0}, "./messaging/notification_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 230, "nosec": 0, "skipped_tests": 0}, "./messaging/slack/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./messaging/slack/slack_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 490, "nosec": 0, "skipped_tests": 0}, "./pocs/agent_context_visibility_test/quick_debug_test.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 58, "nosec": 0, "skipped_tests": 0}, "./pocs/agent_context_visibility_test/simple_context_test.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 132, "nosec": 0, "skipped_tests": 0}, "./pocs/parallel_tool_calls_demo/simple_parallel_test.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 77, "nosec": 0, "skipped_tests": 0}, "./tests/unit_test/integrations_test/add_delete_ask_QA.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 348, "nosec": 0, "skipped_tests": 0}, "./tests/unit_test/integrations_test/common_utils_tests.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 215, "nosec": 0, "skipped_tests": 0}, "./tests/unit_test/integrations_test/test_discord_message_event.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 166, "nosec": 0, "skipped_tests": 0}, "./tests/unit_test/integrations_test/test_slack_message_event.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 70, "nosec": 0, "skipped_tests": 0}, "./tests/unit_test/integrations_test/test_upload_and_process_file.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "./utils/common_functions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 30, "nosec": 0, "skipped_tests": 0}, "./utils/remove_all_qas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 154, "nosec": 0, "skipped_tests": 0}, "./utils/token_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 11, "nosec": 0, "skipped_tests": 0}, "./vectordb/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 2, "nosec": 0, "skipped_tests": 0}, "./vectordb/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./vectordb/api/urls.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 17, "nosec": 0, "skipped_tests": 0}, "./vectordb/api/views.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 226, "nosec": 0, "skipped_tests": 0}, "./vectordb/clear_chromadb.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 50, "nosec": 0, "skipped_tests": 0}, "./vectordb/collections/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 2, "nosec": 0, "skipped_tests": 0}, "./vectordb/collections/collection_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 382, "nosec": 0, "skipped_tests": 0}, "./vectordb/examples/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./vectordb/examples/confluence_mcp_integration_example.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 110, "nosec": 0, "skipped_tests": 0}, "./vectordb/examples/usage_examples.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 258, "nosec": 0, "skipped_tests": 0}, "./vectordb/inspect_chromadb.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 56, "nosec": 0, "skipped_tests": 0}, "./vectordb/interfaces.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 523, "nosec": 0, "skipped_tests": 0}, "./vectordb/management/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./vectordb/management/commands/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./vectordb/management/commands/test_vectordb.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 37, "nosec": 0, "skipped_tests": 0}, "./vectordb/models/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 2, "nosec": 0, "skipped_tests": 0}, "./vectordb/models/document.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 233, "nosec": 0, "skipped_tests": 0}, "./vectordb/services/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "./vectordb/services/chroma_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 324, "nosec": 0, "skipped_tests": 0}, "./vectordb/services/confluence_mcp_chroma_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 148, "nosec": 0, "skipped_tests": 0}, "./vectordb/services/django_chroma_enforcer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 100, "nosec": 0, "skipped_tests": 0}, "./vectordb/services/document_service.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 502, "nosec": 0, "skipped_tests": 0}, "./vectordb/setup.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "./vectordb/show_all_collections_in_chromaDb.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 99, "nosec": 0, "skipped_tests": 0}, "./vectordb/unit_tests/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "./vectordb/unit_tests/run_tests.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 126, "nosec": 0, "skipped_tests": 0}, "./vectordb/unit_tests/test_embedding_quality.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 414, "nosec": 0, "skipped_tests": 0}, "./vectordb/unit_tests/test_retrieval_quality.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 466, "nosec": 0, "skipped_tests": 0}, "./vectordb/utils/chroma_telemetry_suppressor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 66, "nosec": 0, "skipped_tests": 0}, "./vectordb/utils/text_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 187, "nosec": 0, "skipped_tests": 0}, "_totals": {"CONFIDENCE.HIGH": 54, "CONFIDENCE.LOW": 17, "CONFIDENCE.MEDIUM": 11, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 63, "SEVERITY.MEDIUM": 18, "SEVERITY.UNDEFINED": 0, "loc": 36095, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "227     application.mcp_shutdown_cleanup = _shutdown_cleanup  # type: ignore[attr-defined]\n228 except Exception:\n229     pass\n", "col_offset": 0, "end_col_offset": 8, "filename": "./SageBase_Backend/asgi.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 228, "line_range": [228, 229], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "34             print(\"Installing pysqlite3-binary for ChromaDB compatibility...\")\n35             import subprocess\n36             try:\n", "col_offset": 12, "end_col_offset": 29, "filename": "./SageBase_Backend/settings.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 35, "line_range": [35], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "36             try:\n37                 subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", \"pysqlite3-binary\"], \n38                                     capture_output=True, text=True)\n39                 import pysqlite3\n", "col_offset": 16, "end_col_offset": 67, "filename": "./SageBase_Backend/settings.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 37, "line_range": [37, 38], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "94                     stream.reconfigure(encoding='utf-8')\n95                 except Exception:\n96                     pass\n97         # Best-effort hint for Python I/O encoding\n", "col_offset": 16, "end_col_offset": 24, "filename": "./SageBase_Backend/settings.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 95, "line_range": [95, 96], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "98         os.environ.setdefault('PYTHONIOENCODING', 'utf-8')\n99 except Exception:\n100     pass\n101 \n", "col_offset": 0, "end_col_offset": 8, "filename": "./SageBase_Backend/settings.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 99, "line_range": [99, 100], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "268 # SECURITY WARNING: keep the secret key used in production secret!\n269 SECRET_KEY = 'django-insecure-r!ip((g8(r5+6gf4w9%%o5k42zxjf1vy@#)^2k9rm##y+5!$v*'\n270 \n", "col_offset": 13, "end_col_offset": 81, "filename": "./SageBase_Backend/settings.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'django-insecure-r!ip((g8(r5+6gf4w9%%o5k42zxjf1vy@#)^2k9rm##y+5!$v*'", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "15     if _VERSION_INFO is None:\n16         import subprocess\n17         import logging\n", "col_offset": 8, "end_col_offset": 25, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 16, "line_range": [16], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "25             try:\n26                 subprocess.run(\n27                     ['git', 'config', '--global', '--add', 'safe.directory', '/app'],\n28                     capture_output=True,\n29                     text=True,\n30                     timeout=5\n31                 )\n32                 logger.info(\"✅ Git safe directory configured\")\n", "col_offset": 16, "end_col_offset": 17, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 26, "line_range": [26, 27, 28, 29, 30, 31], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "25             try:\n26                 subprocess.run(\n27                     ['git', 'config', '--global', '--add', 'safe.directory', '/app'],\n28                     capture_output=True,\n29                     text=True,\n30                     timeout=5\n31                 )\n32                 logger.info(\"✅ Git safe directory configured\")\n", "col_offset": 16, "end_col_offset": 17, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 26, "line_range": [26, 27, 28, 29, 30, 31], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "36             # Ensure we're in the correct directory\n37             result = subprocess.run(\n38                 ['git', 'rev-parse', '--abbrev-ref', 'HEAD'], \n39                 capture_output=True,\n40                 text=True,\n41                 cwd='/app',\n42                 timeout=10\n43             )\n44             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 13, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 37, "line_range": [37, 38, 39, 40, 41, 42, 43], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "36             # Ensure we're in the correct directory\n37             result = subprocess.run(\n38                 ['git', 'rev-parse', '--abbrev-ref', 'HEAD'], \n39                 capture_output=True,\n40                 text=True,\n41                 cwd='/app',\n42                 timeout=10\n43             )\n44             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 13, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 37, "line_range": [37, 38, 39, 40, 41, 42, 43], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "56             try:\n57                 result = subprocess.run(\n58                     ['git', 'describe', '--tags', '--abbrev=0'], \n59                     capture_output=True,\n60                     text=True,\n61                     cwd='/app',\n62                     timeout=10\n63                 )\n64                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 17, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 57, "line_range": [57, 58, 59, 60, 61, 62, 63], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "56             try:\n57                 result = subprocess.run(\n58                     ['git', 'describe', '--tags', '--abbrev=0'], \n59                     capture_output=True,\n60                     text=True,\n61                     cwd='/app',\n62                     timeout=10\n63                 )\n64                 if result.returncode == 0:\n", "col_offset": 25, "end_col_offset": 17, "filename": "./SageBase_Backend/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 57, "line_range": [57, 58, 59, 60, 61, 62, 63], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "124         def do_request():\n125             response = requests.get(url=url, \n126                                     headers={\n127                                         \"Accept\": \"application/json\",\n128                                         \"Content-Type\": \"application/json\"\n129                                     }, \n130                                     params=params, \n131                                     auth=(self.email, self.api_token))\n132             response.raise_for_status()\n", "col_offset": 23, "end_col_offset": 70, "filename": "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_cloud_document_reader.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 125, "line_range": [125, 126, 127, 128, 129, 130, 131], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "123         def do_request():\n124             response = requests.get(url=url, \n125                                     headers={\n126                                         \"Accept\": \"application/json\",\n127                                         \"Content-Type\": \"application/json\"\n128                                     }, \n129                                     params=params, \n130                                     auth=(self.email, self.api_token))\n131             response.raise_for_status()\n", "col_offset": 23, "end_col_offset": 70, "filename": "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_document_reader.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 124, "line_range": [124, 125, 126, 127, 128, 129, 130], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "135             \n136             response = requests.get(url=url, \n137                                     headers={\n138                                         \"Accept\": \"application/json\",\n139                                         \"Authorization\": f\"Bearer {self.access_token}\"\n140                                     }, \n141                                     params=params)\n142             response.raise_for_status()\n", "col_offset": 23, "end_col_offset": 50, "filename": "./integrations/confluence/mcp_server/documents-vector-search/main/sources/confluence/confluence_oauth2_document_reader.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 136, "line_range": [136, 137, 138, 139, 140, 141], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "11 import requests\n12 import subprocess\n13 import threading\n", "col_offset": 0, "end_col_offset": 17, "filename": "./integrations/confluence/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 12, "line_range": [12], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "523     url = f'https://api.github.com/search/code?q={requests.utils.quote(query)}'\n524     resp = requests.get(url, headers=headers)\n525     if resp.status_code == 200:\n", "col_offset": 11, "end_col_offset": 45, "filename": "./integrations/github/api/app_webhook_receiver.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 524, "line_range": [524], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "76     logger.debug(f\"Making request to: {url}\")\n77     response = requests.post(url, headers=headers)\n78     logger.debug(f\"Response status: {response.status_code}\")\n", "col_offset": 15, "end_col_offset": 50, "filename": "./integrations/github/api/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "95         logger.debug(f\"Making request to: {url}\")\n96         response = requests.get(url, headers=headers)\n97         logger.debug(f\"Response status: {response.status_code}\")\n", "col_offset": 19, "end_col_offset": 53, "filename": "./integrations/github/api/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 96, "line_range": [96], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "43                     return Response({\"error\": e.response.text}, status=e.response.status_code)\n44                 except Exception:\n45                     pass\n46             return Response({\"error\": str(e)}, status=500)\n", "col_offset": 16, "end_col_offset": 24, "filename": "./integrations/github/api/webhooks.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 44, "line_range": [44, 45], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "68                     return Response({\"error\": e.response.text}, status=e.response.status_code)\n69                 except Exception:\n70                     pass\n71             return Response({\"error\": str(e)}, status=500)\n", "col_offset": 16, "end_col_offset": 24, "filename": "./integrations/github/api/webhooks.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 69, "line_range": [69, 70], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "92                     return Response({\"error\": e.response.text}, status=e.response.status_code)\n93                 except Exception:\n94                     pass\n95             return Response({\"error\": str(e)}, status=500) \n", "col_offset": 16, "end_col_offset": 24, "filename": "./integrations/github/api/webhooks.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 93, "line_range": [93, 94], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "23 \n24     response = requests.post(url, headers=headers, data=data)\n25     return response.json()\n", "col_offset": 15, "end_col_offset": 61, "filename": "./integrations/github/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 24, "line_range": [24], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "42     try:\n43         response = requests.post(url, headers=headers, data=data)\n44         response.raise_for_status()\n", "col_offset": 19, "end_col_offset": 65, "filename": "./integrations/github/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 43, "line_range": [43], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "75         }\n76         response = requests.get(\"https://api.github.com/user\", headers=headers)\n77         return response.status_code == 200\n", "col_offset": 19, "end_col_offset": 79, "filename": "./integrations/github/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "146     \n147     response = requests.delete(url, auth=auth, json=data)\n148     return response.status_code == 204\n", "col_offset": 15, "end_col_offset": 57, "filename": "./integrations/github/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 147, "line_range": [147], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "160     \n161     response = requests.delete(url, auth=auth, json=data)\n162     return response.status_code == 204\n", "col_offset": 15, "end_col_offset": 57, "filename": "./integrations/github/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 161, "line_range": [161], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "33                 # Acquire a cross-process file lock so only one poller starts\n34                 lock_path = getattr(settings, \"GDRIVE_POLLER_LOCK_FILE\", \"/tmp/gdrive_poller.lock\")\n35                 os.makedirs(os.path.dirname(lock_path), exist_ok=True)\n", "col_offset": 73, "end_col_offset": 98, "filename": "./integrations/google_drive/apps.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 377, "link": "https://cwe.mitre.org/data/definitions/377.html"}, "issue_severity": "MEDIUM", "issue_text": "Probable insecure usage of temp file/directory.", "line_number": 34, "line_range": [34], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b108_hardcoded_tmp_directory.html", "test_id": "B108", "test_name": "hardcoded_tmp_directory"}, {"code": "50                         fcntl.flock(_poller_file_lock_fh.fileno(), fcntl.LOCK_UN)\n51                     except Exception:\n52                         pass\n53                     try:\n", "col_offset": 20, "end_col_offset": 28, "filename": "./integrations/google_drive/apps.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 51, "line_range": [51, 52], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "54                         _poller_file_lock_fh.close()\n55                     except Exception:\n56                         pass\n57 \n", "col_offset": 20, "end_col_offset": 28, "filename": "./integrations/google_drive/apps.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 55, "line_range": [55, 56], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "117                 preference.save(update_fields=['file_state', 'updated_at'])\n118             except Exception:\n119                 pass\n120             return {\n", "col_offset": 12, "end_col_offset": 20, "filename": "./integrations/google_drive/embedding/embedding_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 118, "line_range": [118, 119], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "149                         pref.save(update_fields=['file_state', 'updated_at'])\n150                     except Exception:\n151                         pass\n152             except Exception as e:  # noqa: BLE001\n", "col_offset": 20, "end_col_offset": 28, "filename": "./integrations/google_drive/embedding/tasks.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 150, "line_range": [150, 151], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "158                     pref.save(update_fields=['file_state', 'updated_at'])\n159                 except Exception:\n160                     pass\n161 \n", "col_offset": 16, "end_col_offset": 24, "filename": "./integrations/google_drive/embedding/tasks.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 159, "line_range": [159, 160], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "121             import requests\n122             response = requests.post(\n123                 'https://oauth2.googleapis.com/revoke',\n124                 params={'token': creds.token},\n125                 headers={'content-type': 'application/x-www-form-urlencoded'}\n126             )\n127             \n", "col_offset": 23, "end_col_offset": 13, "filename": "./integrations/google_drive/services.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 122, "line_range": [122, 123, 124, 125, 126], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "122                     'repo_type': project_data['repo_type'],\n123                     'doc_responsible': random.choice(users),\n124                     'secondary_responsible': random.choice(users),\n", "col_offset": 39, "end_col_offset": 59, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 123, "line_range": [123], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "123                     'doc_responsible': random.choice(users),\n124                     'secondary_responsible': random.choice(users),\n125                     'total_contributors': random.randint(3, 12)\n", "col_offset": 45, "end_col_offset": 65, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 124, "line_range": [124], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "124                     'secondary_responsible': random.choice(users),\n125                     'total_contributors': random.randint(3, 12)\n126                 }\n", "col_offset": 42, "end_col_offset": 63, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 125, "line_range": [125], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "134                     project=project,\n135                     health_score=random.randint(60, 95),\n136                     risk_level=random.choice(['Low', 'Medium', 'High']),\n", "col_offset": 33, "end_col_offset": 55, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 135, "line_range": [135], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "135                     health_score=random.randint(60, 95),\n136                     risk_level=random.choice(['Low', 'Medium', 'High']),\n137                     documentation_coverage=random.randint(40, 98),\n", "col_offset": 31, "end_col_offset": 71, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 136, "line_range": [136], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "136                     risk_level=random.choice(['Low', 'Medium', 'High']),\n137                     documentation_coverage=random.randint(40, 98),\n138                     active_contributors=random.randint(3, 10)\n", "col_offset": 43, "end_col_offset": 65, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 137, "line_range": [137], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "137                     documentation_coverage=random.randint(40, 98),\n138                     active_contributors=random.randint(3, 10)\n139                 )\n", "col_offset": 40, "end_col_offset": 61, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 138, "line_range": [138], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "141                 # Create contributors for each project\n142                 project_users = random.sample(users, k=random.randint(3, len(users)))\n143                 for user in project_users:\n", "col_offset": 32, "end_col_offset": 85, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 142, "line_range": [142], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "141                 # Create contributors for each project\n142                 project_users = random.sample(users, k=random.randint(3, len(users)))\n143                 for user in project_users:\n", "col_offset": 55, "end_col_offset": 84, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 142, "line_range": [142], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "146                         user=user,\n147                         commits=random.randint(5, 150),\n148                         reviews=random.randint(1, 50),\n", "col_offset": 32, "end_col_offset": 54, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 147, "line_range": [147], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "147                         commits=random.randint(5, 150),\n148                         reviews=random.randint(1, 50),\n149                         docs=random.randint(0, 25),\n", "col_offset": 32, "end_col_offset": 53, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 148, "line_range": [148], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "148                         reviews=random.randint(1, 50),\n149                         docs=random.randint(0, 25),\n150                         contributions=random.randint(10, 200)\n", "col_offset": 29, "end_col_offset": 50, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 149, "line_range": [149], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "149                         docs=random.randint(0, 25),\n150                         contributions=random.randint(10, 200)\n151                     )\n", "col_offset": 38, "end_col_offset": 61, "filename": "./integrations/management/commands/create_sample_knowledge_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 150, "line_range": [150], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "34             colors = [\"#3B82F6\", \"#10B981\", \"#F59E0B\", \"#EF4444\", \"#8B5CF6\", \"#06B6D4\", \"#84CC16\", \"#F97316\"]\n35             color = random.choice(colors)\n36             \n", "col_offset": 20, "end_col_offset": 41, "filename": "./integrations/models.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 35, "line_range": [35], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "177                     colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16']\n178                     color = random.choice(colors)\n179                     \n", "col_offset": 28, "end_col_offset": 49, "filename": "./integrations/models.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 178, "line_range": [178], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "373                 colors = [\"#3B82F6\", \"#10B981\", \"#F59E0B\", \"#EF4444\", \"#8B5CF6\", \"#06B6D4\", \"#84CC16\", \"#F97316\"]\n374                 color = random.choice(colors)\n375                 knowledge_space = Knowledge_Space.objects.create(\n", "col_offset": 24, "end_col_offset": 45, "filename": "./integrations/services/django_auth_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 374, "line_range": [374], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "393     try:\n394         response = requests.post(\n395             \"https://slack.com/api/auth.revoke\",\n396             data={\"token\": token},\n397             headers={\"Content-Type\": \"application/x-www-form-urlencoded\"}\n398         )\n399         data = response.json()\n", "col_offset": 19, "end_col_offset": 9, "filename": "./integrations/slack/views.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 394, "line_range": [394, 395, 396, 397, 398], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "102             return user\n103     except:\n104         pass\n105     \n", "col_offset": 4, "end_col_offset": 12, "filename": "./integrations/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 103, "line_range": [103, 104], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "144     try:\n145         r = requests.get(github_api_url, headers=headers)\n146     except Exception as e:\n", "col_offset": 12, "end_col_offset": 57, "filename": "./integrations/views.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 145, "line_range": [145], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "167         try:\n168             r2 = requests.get(diff_url, headers=headers)\n169         except Exception as e:\n", "col_offset": 17, "end_col_offset": 56, "filename": "./integrations/views.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 168, "line_range": [168], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "851                 normalized.append({\"id\": rid, \"full_name\": full_name, \"name\": name})\n852         except Exception:\n853             continue\n854 \n", "col_offset": 8, "end_col_offset": 20, "filename": "./integrations/views.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 852, "line_range": [852, 853], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "56     \n57     def __init__(self, credentials_file: str, token_file: str = 'token.json'):\n58         \"\"\"\n59         Initialize Google Drive client\n60         \n61         Args:\n62             credentials_file: Path to the OAuth2 credentials JSON file\n63             token_file: Path to store the access token\n64         \"\"\"\n65         self.credentials_file = credentials_file\n66         self.token_file = token_file\n67         self.service = None\n68         self.credentials = None\n69         self._initialize_service()\n70     \n", "col_offset": 4, "end_col_offset": 34, "filename": "./interfaces_data/google_drive/drive_client.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'token.json'", "line_number": 57, "line_range": [57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b107_hardcoded_password_default.html", "test_id": "B107", "test_name": "hardcoded_password_default"}, {"code": "4 CREDENTIALS_FILE = r\"A:\\Sagebase_Latest_git\\SageBase_BackEnd\\interfaces_data\\google_drive_credentials.json\"\n5 TOKEN_FILE = r\"A:\\Sagebase_Latest_git\\SageBase_BackEnd\\interfaces_data\\gdrive_token_SageBase_Taha_test.json\"\n6 \n", "col_offset": 13, "end_col_offset": 108, "filename": "./interfaces_data/google_drive/test_drive_client_full.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'A:\\Sagebase_Latest_git\\SageBase_BackEnd\\interfaces_data\\gdrive_token_SageBase_Taha_test.json'", "line_number": 5, "line_range": [5], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "3 CREDENTIALS_FILE = r\"A:\\Sagebase_Latest_git\\SageBase_BackEnd\\interfaces_data\\google_drive_credentials.json\"\n4 TOKEN_FILE = r\"A:\\Sagebase_Latest_git\\SageBase_BackEnd\\interfaces_data\\gdrive_token_SageBase_Taha_test.json\"  # Use your actual token file name\n5             \n", "col_offset": 13, "end_col_offset": 108, "filename": "./interfaces_data/google_drive/test_drive_client_unit.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'A:\\Sagebase_Latest_git\\SageBase_BackEnd\\interfaces_data\\gdrive_token_SageBase_Taha_test.json'", "line_number": 4, "line_range": [4], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "11             import knowledge_map.signals  # noqa: F401\n12         except Exception:\n13             # Avoid crashing app startup on optional dependencies\n14             pass\n", "col_offset": 8, "end_col_offset": 16, "filename": "./knowledge_map/apps.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 12, "line_range": [12, 13, 14], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "39             full_name = '/'.join(parts[-2:])\n40         except Exception:\n41             pass\n42     if not full_name or '/' not in full_name:\n", "col_offset": 8, "end_col_offset": 16, "filename": "./knowledge_map/signals.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 40, "line_range": [40, 41], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "143                                 pr_info['lines_deleted'] = pr_stats.get('deletions', 0)\n144                         except Exception:\n145                             pass  # Skip stats if unavailable\n146                         \n", "col_offset": 24, "end_col_offset": 32, "filename": "./knowledge_map/signals.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 144, "line_range": [144, 145], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "46             github_logger.warning(\"⚠️ Running with uvloop - MCP server may have compatibility issues\")\n47     except Exception:\n48         pass\n49 \n", "col_offset": 4, "end_col_offset": 12, "filename": "./konowledge_detection_agents/github_agent.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 47, "line_range": [47, 48], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "75                 await mcp_server.close()\n76         except Exception:\n77             pass  # Ignore cleanup errors during exception handling\n78         raise\n", "col_offset": 8, "end_col_offset": 16, "filename": "./konowledge_detection_agents/github_agent.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 76, "line_range": [76, 77], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "120         instructions=(\n121             f\"CURRENT_DATETIME = {today_date}\"\n122             \"You are a specialized GitHub assistant. \"\n123             \"You receive queries from a main Orchestrator Agent. \"\n124             \"You **MUST STRICTLY PARSE** the current datetime string (e.g., '2025-07-30T12:37:30+00:00') and use it as your **SOLE and definitive reference** for 'current date', 'this month', 'last week', or any other time-sensitive calculations. \"\n125             \"**DO NOT** use any internal knowledge of the current date/time; **ALWAYS USE THE PROVIDED TIMESTAMP.** \"\n126             \n127 \n128             \"Your primary goal is to provide accurate, concise, and actionable information by effectively utilizing your GitHub tools. \"\n129             \"Always use the tool `get_me` to get your GitHub username. Use Your GitHub username for any operations related to your own repositories or user-specific queries. \"\n130             \n131             # Updated section for tool usage\n132             \"**CRITICAL TOOL USAGE RULE:** \"\n133             \"Only depend on tools' properties and inputSchema. Don't use tools with properties not in their inputSchema. Use the inputSchema to understand what a tool can do. \"\n134             \n135             \"**ABSOLUTELY CRITICAL RULE FOR `search_code` & `get_file_contents` SEQUENCING:** \"\n136             \"1. First, use the `search_code` tool with a single keyword and the required `repo:` and `user:` filters (e.g., `search_code(query='auth repo:owner/repo user:owner')`). \"\n137             \"2. If `search_code` returns results, immediately pick the most relevant file and call `get_file_contents` on it. **You must not skip this step** even if the first file seems marginal. \"\n138             \"3. After reading the file, evaluate if the content provides a clear enough answer. If insufficient: \"\n139             \"   - Call `search_code` again using a refined or different keyword, and then follow with another `get_file_contents` on a new file from that result. \"\n140             \"   - You **must only call `search_code` again if the previous file content was insufficient.** Do not re-search blindly. \"\n141             \"4. This process of `search_code` → `get_file_contents` → evaluate → repeat (if needed) can be done **up to 2 times maximum after the first attempt.** \"\n142             \"5. At each stage, use minimal and specific keywords, prefer abbreviations (e.g., 'auth' instead of 'authentication'). \"\n143             \"6. Do not parallelize `get_file_contents` calls. Always do one at a time and evaluate context sufficiency after each.\"\n144             \"7. Do not use separate properties for `repo` or `user`; they are part of the `query` string.\"\n145 \n146             \"You have access to a powerful set of GitHub tools. Your available tools are: \"\n147             \"- **Repository & Code Access**: `search_repositories`, `list_branches`, `list_commits`, `get_commit`, `get_file_contents` (for reading file content), `search_code`. \"\n148             \"- **Pull Request Management**: `list_pull_requests`, `get_pull_request`, `get_pull_request_comments`, `get_pull_request_files`, `get_pull_request_reviews`, `get_pull_request_status`, `search_pull_requests`. \"\n149             \"- **Issue Management**: `list_issues`, `get_issue`, `get_issue_comments`, `list_sub_issues`, `search_issues`. \"\n150             \"- **User & Organization Information**: `get_me`, `search_users`, `search_orgs`. \"\n151             \n152             \"---\"\n153             \"**General Guidelines:**\"\n154             \"- **To determine date ranges (e.g., 'this month', 'last week'):** You **MUST** use the `CURRENT_DATETIME` you extracted as your reference point. Calculate the `since` and `until` parameters for `list_commits` or `list_pull_requests` based on this *provided* date. For 'this month', calculate the first day and last day of the month based on the extracted `CURRENT_DATETIME`.\"\n155             \" Example: If `CURRENT_DATETIME` is 2025-07-15T10:00:00+00:00, 'this month' means `since='2025-07-01T00:00:00Z'` and `until='2025-07-31T23:59:59Z'`.\"\n156             \"- Always confirm the repository owner and name. Ask for clarification if ambiguous. \"\n157             \"- **Always cite the tools you used** for your analysis and data retrieval. \"\n158             \"- If a requested action cannot be directly performed by an available tool, explain why and suggest an alternative or a composite approach using available tools. \"\n159             \"- Strive for brevity in your intermediate steps and focus the final output on the user's direct question. \"\n160             \"- **Handle Tool Errors Gracefully:** If a tool call fails (e.g., repository not found, file not found), inform the user clearly and suggest alternative actions or ask for clarification, rather than just crashing.\"\n161             \"---\"\n162 \n163             # Tweaked instructions for explaining code - new rule is added here\n164             \"**When asked to explain code implementation, architecture, or logic flow (e.g., 'explain how authentication is implemented in X repo'):**\"\n165             \"1. **Identify Relevant Repository & Files**: First, determine the target repository (`owner/repo-name`). If not specified, ask for clarification. \"\n166             \"2. **Make Highly Specific Keywords**: Derive keywords from the user query. **To improve the likelihood of finding all relevant files, use abbreviations and common short forms.** For example, use 'auth' instead of 'authentication' or 'authenticator'. Use 'repo' instead of 'repository', etc. This ensures you won't miss a file because of a slightly different word form. \"\n167             \"3. Use `search_code` tool for each keyword separately in parallel for all the keywords, and **ALWAYS include the `repo:owner/repo-name` and `user:owner` filter** in the property of search_code tool called \\\"query\\\" to find a concise list of the most relevant file paths. EXAMPLE: search_code(query:\\\"keyword repo:owner/repo_name user:owner\\\") **Use 1 keyword in each search_code tool call**. \"\n168             \"4. **Strategically Read Key File Contents**: Select **the 3-5 most critical files** from the list returned by `search_code`. For each selected file, **you must use the `get_file_contents` tool, providing the file path returned from the `search_code` tool as the `path` parameter.** Prioritize files likely to contain core logic. **YOU MUST ONLY USE 1 get_file_contents AT A TIME, DONT CALL MULTIPLE IN PARALLEL, CALL THE TOOL SEQUENTIALLY, ONLY REUSE IF MORE CONTEXT IS NEEDED**\"\n169             \"5. If file contents still dont provide context to answer, re-evaluate the selected files and get_file_contents of other files. (redo step 4). 2 TIMES MAXIMUM \"\n170             \"6. If step 5 repeated to the maximum and still cant provide answer with current context, consider additional keywords or broader searches. and redo step 1 and 2 and 3 and 4. this step happens MAXIMUM 2 TIMES\"\n171             \"3. **Provide High-Level Overview & Key Snippets**: \"\n172             \"   - **Start by explaining the overall conceptual flow**. \"\n173             \"   - From the *actual content* of the files you read, extract **only the most essential, high-impact code snippets** (e.g., public method signatures, key API calls). **Limit each snippet to a maximum of 10-12 lines.** Do not generate hypothetical code. Always cite the **full file path and specific line numbers** for each snippet. \"\n174             \"4. **Offer Deeper Dive**: Conclude your initial explanation by **asking the user if they would like a deeper dive into a specific file, function, or aspect** of the implementation. This allows the user to guide the context usage. \"\n175             \"5. **Maintain Conciseness**: Be extremely mindful of token limits. Prioritize key information. \"\n176 \n177             \"---\"\n178 \n179             \"**When asked for user or repository statistics (e.g., 'who is the top contributor in X repo?', 'how many PRs does user Y have in repo Z?', 'how many commits did user A do this month?', 'what are the branches related to this user in X repo?'):**\"\n180             \"1. **Identify Repository & User**: Always identify the target repository (`owner` and `repo`) and any specific `user` mentioned in the query. If unclear, ask for clarification. \"\n181             \"2. **Use Appropriate Tools & Process Data Effectively**: \"\n182             \"   - **For User Repositories**: Use `search_repositories(query='user:username')` to list all repositories for a given user. \"\n183             \"   - **For Pull Request Counts (by user/repo)**: Use `list_pull_requests` for the specified `owner` and `repo`. If a specific author is requested, filter the returned list programmatically based on the author's username. Count the matching PRs. \"\n184             \"   - **For Commit Counts (by user/repo/date)**: Use `list_commits` for the specified `owner` and `repo`. If a `sha` (branch name) is specified, include that. Use the `author` parameter to filter by user. **For 'this month' queries, use `since` parameter with a value of today's date adjusted to the first day of the month: {current_month_start}**. If a date range (like 'this month') is given, post-filter the results based on the commit timestamp from the tool's output. Count the matching commits. \"\n185             \"   - **For 'Top Contributor' or 'Number of commits per member'**: Since there's no direct `list_contributors` tool, you must infer this from commits. \"\n186             \"     - Use `list_commits` for the repository, **retrieving a maximum of the last 100-200 commits (use `perPage` parameter, and if necessary, limit to 2 pages)**. \"\n187             \"     - Process the returned commits: Tally commit authors by their GitHub username (or email if username is unavailable). \"\n188             \"     - Present the top contributors or the count for each member based on this sample. **Clearly state that the analysis is based on the most recent X commits (e.g., 'Based on the most recent 100 commits...')**. \"\n189             \"   - **For 'Branches related to a user'**: This requires combining tools. First, use `list_branches` for the given repository. Then, for each branch, use `list_commits` with the `author` parameter set to the specified user and the `sha` set to the branch name. Keep track of which branches return commits by that user. Present the list of branches where the user has commits. Be aware this can be a more resource-intensive operation due to multiple `list_commits` calls; if too many branches, you might need to limit the scope. \"\n190             \"3. **Synthesize & Present Data Concisely**: Clearly present the requested statistics. State the tool(s) used and how the data was derived. **Limit the output to essential numbers and names; avoid verbose descriptions of individual items.** \"\n191             \"4. **Handle Pagination (Consciously)**: When making multiple paginated calls, aim to collect just enough data to answer the query accurately without exceeding token limits. For exhaustive lists, inform the user about the volume of data. \"\n192             f\"{available_repos_text}\"\n193         ),\n", "col_offset": 14, "end_col_offset": 33, "filename": "./konowledge_detection_agents/github_agent.py", "issue_confidence": "LOW", "issue_cwe": {"id": 89, "link": "https://cwe.mitre.org/data/definitions/89.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible SQL injection vector through string-based query construction.", "line_number": 121, "line_range": [121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b608_hardcoded_sql_expressions.html", "test_id": "B608", "test_name": "hardcoded_sql_expressions"}, {"code": "305         \"\"\"Generate hash of file content for deduplication\"\"\"\n306         hasher = hashlib.md5()\n307         with open(file_path, 'rb') as f:\n", "col_offset": 17, "end_col_offset": 30, "filename": "./konowledge_detection_agents/sagebase_q_a/q_a_detection.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 306, "line_range": [306], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "120             unhandled_logger.addHandler(file_handler)\n121         except Exception:\n122             pass  # File handler is optional \n", "col_offset": 8, "end_col_offset": 16, "filename": "./logger/exception_handler.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 121, "line_range": [121, 122], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "72     logger.error(f\"Missing dependency: {e}\")\n73     os.system(\"pip install discord.py aiohttp\")\n74     import discord\n", "col_offset": 4, "end_col_offset": 47, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a shell: Seems safe, but may be changed in the future, consider rewriting without shell", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b605_start_process_with_a_shell.html", "test_id": "B605", "test_name": "start_process_with_a_shell"}, {"code": "72     logger.error(f\"Missing dependency: {e}\")\n73     os.system(\"pip install discord.py aiohttp\")\n74     import discord\n", "col_offset": 4, "end_col_offset": 47, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "258 def check_for_multiple_instances():\n259     import subprocess\n260     import sys\n", "col_offset": 4, "end_col_offset": 21, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 259, "line_range": [259], "more_info": "https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "264         if sys.platform == 'win32':\n265             result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],\n266                                    capture_output=True, text=True)\n267             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 66, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 265, "line_range": [265, 266], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "264         if sys.platform == 'win32':\n265             result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],\n266                                    capture_output=True, text=True)\n267             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 66, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 265, "line_range": [265, 266], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "272         else:\n273             result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)\n274             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 82, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 273, "line_range": [273], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "272         else:\n273             result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)\n274             if result.returncode == 0:\n", "col_offset": 21, "end_col_offset": 82, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 273, "line_range": [273], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "316     if not bot_token:\n317         bot_token = \"MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls\"\n318 \n", "col_offset": 20, "end_col_offset": 94, "filename": "./messaging/discord/discord_bot.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls'", "line_number": 317, "line_range": [317], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "49 async def main():\n50     context = UserContext(\n51         user_id=\"user_123\",\n52         api_token=\"secret_api_token_abc123\",\n53         session_token=\"secret_session_token_xyz789\",\n54         user_name=\"<PERSON>\"\n55     )\n56     \n", "col_offset": 14, "end_col_offset": 5, "filename": "./pocs/agent_context_visibility_test/quick_debug_test.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'secret_api_token_abc123'", "line_number": 50, "line_range": [50, 51, 52, 53, 54, 55], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "130     # Create context with sensitive data\n131     context = UserContext(\n132         user_id=\"user_123\",\n133         api_token=\"secret_api_token_abc123\",\n134         session_token=\"secret_session_token_xyz789\",\n135         database_password=\"secret_db_password\",\n136         user_name=\"<PERSON>\",\n137         preferences=[\"dark_mode\", \"notifications\"]\n138     )\n139     \n", "col_offset": 14, "end_col_offset": 5, "filename": "./pocs/agent_context_visibility_test/simple_context_test.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'secret_api_token_abc123'", "line_number": 131, "line_range": [131, 132, 133, 134, 135, 136, 137, 138], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "65         self.email = \"<EMAIL>\"\n66         self.password = \"Test1234\"\n67         \n", "col_offset": 24, "end_col_offset": 34, "filename": "./tests/unit_test/integrations_test/add_delete_ask_QA.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'Test1234'", "line_number": 66, "line_range": [66], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "20         self.email = \"<EMAIL>\"\n21         self.password = \"nour\"\n22         self._authenticate()\n", "col_offset": 24, "end_col_offset": 30, "filename": "./tests/unit_test/integrations_test/test_slack_message_event.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'nour'", "line_number": 21, "line_range": [21], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "52         self.email = \"<EMAIL>\"\n53         self.password = \"Test1234\"\n54         if knowledge_space_name is None:\n", "col_offset": 24, "end_col_offset": 34, "filename": "./tests/unit_test/integrations_test/test_upload_and_process_file.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'Test1234'", "line_number": 53, "line_range": [53], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "275                         failed_count += 1\n276                         errors.append(f\"Failed to delete {doc.metadata.source_id} from {collection_name}\")\n277                 except Exception as e:\n", "col_offset": 40, "end_col_offset": 57, "filename": "./vectordb/interfaces.py", "issue_confidence": "LOW", "issue_cwe": {"id": 89, "link": "https://cwe.mitre.org/data/definitions/89.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible SQL injection vector through string-based query construction.", "line_number": 276, "line_range": [276], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b608_hardcoded_sql_expressions.html", "test_id": "B608", "test_name": "hardcoded_sql_expressions"}, {"code": "309                 all_ids.update(direct_results['ids'])\n310             except Exception:\n311                 # Document ID might not exist as direct ID, that's okay\n312                 pass\n313             \n", "col_offset": 12, "end_col_offset": 20, "filename": "./vectordb/services/document_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 310, "line_range": [310, 311, 312], "more_info": "https://bandit.readthedocs.io/en/1.8.6/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}]}