# Development Configuration
DJANGO_ENV=deployment
DEBUG=False

#test database in azure
DATABASE_ENGINE=django.db.backends.postgresql_psycopg2

GEMINI_API_KEY=AIzaSyBlz6C2q29A0eR67hir6kWE2cDoH_IrcAc

# Github App
# Github App(SageBase.tech)
GITHUB_APP_SLUG=sagebase-tech
GITHUB_APP_ID=1543357
GITHUB_APP_CLIENT_ID=********************
GITHUB_APP_CLIENT_SECRET=0296cf7b4e8bb6aa9d1f372667c41339512caf98
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GITHUB_APP_WEBHOOK_SECRET="1test$2pass%3run!!"


# API Keys
OPENAI_API_KEY=********************************************************************************************************************************************************************

# ChromaDB Configuration
ANONYMIZED_TELEMETRY=False
CHROMA_TELEMETRY_CAPTURE=False
CHROMA_EMBEDDING_TYPE=openai
CHROMA_EMBEDDING_MODEL=text-embedding-3-small
CHROMA_PERSIST_DIR=./chroma_db
#chroma on the cloud
# Internal chroma db container on the server, not accessible from external
CHROMA_PORT=8000
CHROMA_HOST=http://chromadb

#Make sure the port is correct
FRONTEND_BASE_URL=https://app.sagebase.tech/

# Slack Configuration:sageBase.tech
SLACK_CLIENT_ID=9008663006947.9263016442325
SLACK_CLIENT_SECRET=5f998191477d8535b8cd0882c4adf399
SLACK_SIGNING_SECRET=44c986cc37495b127ec4333cdba4ca4c
SLACK_BOT_TOKEN=*********************************************************


SLACK_WEBHOOK_URL={BASE_URL}/api/integrations/slack/webhook/receive/
SLACK_REDIRECT_URI={BASE_URL}/api/integrations/slack/callback/

BACKEND_BASE_URL=https://sagebaseserverfrontend.sagebase.tech

# Discord Bot Configuration
DISCORD_BOT_TOKEN=MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls
DISCORD_CLIENT_ID=1391784811173122179
DISCORD_CLIENT_SECRET=g2oUQ7o4-UNx2Fe_5oISgNr8zXbOYA1S
DISCORD_REDIRECT_URI=https://sagebaseserverfrontend.sagebase.tech:8000/api/integrations/discord/callback/ # set on starting
DISCORD_BOT_AUTO_START=true


GDRIVE_REDIRECT_URI=https://sagebaseserverfrontend.sagebase.tech/api/integrations/google-drive/callback/
GDRIVE_WEBHOOK_URL=https://sagebaseserverfrontend.sagebase.tech/integrations/google-drive/webhook/
DISCORD_WEBHOOK_URL=https://sagebaseserverfrontend.sagebase.tech/api/integrations/discord/webhook/




# Email settings
EMAIL_HOST=send.one.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=SageBase2025*
DEFAULT_FROM_EMAIL="SageBase <<EMAIL>>"

azure_gpt4_1_=gpt-4.1_france_SageBase
AZURE_GPT4O_MINI=o4-mini_france_sagebase
AZURE_GPT4O=gpt-4o_france_sagebase
azure_embedding_3_small=text-embedding-3-small
AZURE_OPENAI_ENDPOINT=https://sagebasemodels.openai.azure.com/
AZURE_ENDPOINT=https://sagebasemodels.openai.azure.com/openai/deployments/gpt-4.1_france_SageBase/chat/completions?api-version=2025-01-01-preview
AZURE_OPENAI_API_KEY=FFjJUbvnHNXAcFsMSOCKSovwgxRsEkfBFN6r2MlYzjmGi0ROXqLmJQQJ99BHAC5T7U2XJ3w3AAABACOGvgHv
api_version="2024-12-01-preview"
# Azure OpenAI Embedding Configuration
AZURE_EMBEDDING_ENDPOINT=https://sagebasemodels.openai.azure.com/
AZURE_EMBEDDING_DEPLOYMENT=text-embedding-3-small_france_sagebBase
AZURE_EMBEDDING_API_VERSION=2023-05-15


# Required variables:

AZURE_OPENAI_DEPLOYMENT=gpt-4.1_france_SageBase
AZURE_OPENAI_API_VERSION=2024-12-01-preview
# Optional variables:
AZURE_OPENAI_MODEL=gpt-4.1_france_SageBase

# Redis Configuration
REDIS_HOST=sagebase_backend-redis-1
REDIS_PORT=6379
REDIS_DB=0
REDIS_URL=redis://sagebase_backend-redis-1:6379/0


CONFLUENCE_CLIENT_ID=eEu59a6XJn3Rkra4JBj2kpoxHRBdjj8Y
CONFLUENCE_CLIENT_SECRET=ATOAT8ui85Il2KNRi82Csclc9HSRG2DlDb3DSotm-UPWLIs1-sgQgIURwJg2SzI30CPm6480340C
CONFLUENCE_REDIRECT_URI=https://sagebaseserverfrontend.sagebase.tech/api/integrations/confluence/callback/


# Backend Slack Webhook URL for notifications, used to send notfixaton to the slack logins channel
SLACK_WEBHOOK_URL_NOTIFS=*********************************************************************************
