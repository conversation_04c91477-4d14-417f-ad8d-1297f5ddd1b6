# 🚀 Auto-Async ORM

This project includes an **automatic async/sync ORM detection system** that eliminates the need for manual `sync_to_async` calls throughout your codebase.

## ✨ Features

- **🤖 Automatic Context Detection**: Detects whether you're in sync or async context
- **📝 Write Once, Use Everywhere**: Same ORM code works in both contexts
- **⚡ Performance Optimized**: Uses `thread_sensitive=False` by default
- **🛡️ Safe**: Can be enabled/disabled without breaking existing code
- **🎯 Transparent**: No changes needed to existing Django ORM code

## 🔧 How It Works

The system monkey patches Django ORM methods to automatically detect the execution context:

- **Sync Context**: Methods work normally (no `await` needed)
- **Async Context**: Methods automatically return awaitables

## 📋 Setup

The auto-async ORM is automatically enabled when the `integrations` app loads. You can control this in `integrations/apps.py`:

```python
def ready(self):
    """Called when the app is ready - enable auto async ORM."""
    try:
        from .auto_async import enable_auto_async
        enable_auto_async()
    except ImportError:
        pass
```

## 💡 Usage Examples

### Sync Context (Regular Django Views, Management Commands)

```python
def my_sync_view(request):
    # Regular ORM operations - no await needed
    users = User.objects.filter(is_active=True)
    company = Company.objects.get(name="SageBase")
    
    new_user = User.objects.create(
        email="<EMAIL>",
        company=company
    )
    
    new_user.save()
    return JsonResponse({'user_id': new_user.id})
```

### Async Context (Webhooks, Async Views)

```python
async def my_async_webhook(request):
    # Same ORM operations - but with await
    users = await User.objects.filter(is_active=True)
    company = await Company.objects.get(name="SageBase")
    
    new_user = await User.objects.create(
        email="<EMAIL>",
        company=company
    )
    
    await new_user.save()
    return JsonResponse({'user_id': new_user.id})
```

### Mixed Context Functions

```python
def get_user_company(user_id):
    """This function works in BOTH sync and async contexts!"""
    try:
        user = User.objects.get(id=user_id)  # Auto-detects context
        return user.company.name
    except User.DoesNotExist:
        return None

# Usage in sync context:
company_name = get_user_company(123)

# Usage in async context:
company_name = await get_user_company(123)
```

## 🎯 Real-World Example: Slack Webhook

```python
async def handle_slack_message(slack_user_id, message):
    """Process Slack message with auto-async ORM"""
    
    # Find user profile - automatically async
    profile = await SlackUserProfile.objects.get(
        slack_user_id=slack_user_id, 
        is_active=True
    )
    
    # Get company info - automatically async
    company = profile.user.company
    
    # Complex queries work too - automatically async
    team_members = await User.objects.filter(
        company=company,
        is_active=True
    ).select_related('company')
    
    # Process message...
    return f"Processed message for {company.name}"
```

## 🧪 Testing

Test the auto-async functionality with the included management command:

```bash
# Test sync context only
python manage.py test_auto_async

# Test both sync and async contexts
python manage.py test_auto_async --test-async
```

## 🔧 Supported Operations

The following Django ORM operations are automatically made context-aware:

### QuerySet Methods
- `get()`, `filter()`, `exclude()`, `first()`, `last()`
- `count()`, `exists()`, `create()`, `update()`, `delete()`
- `bulk_create()`, `bulk_update()`
- `values()`, `values_list()`, `distinct()`
- `order_by()`, `reverse()`, `aggregate()`, `annotate()`

### Manager Methods
- `get()`, `filter()`, `exclude()`, `first()`, `last()`
- `count()`, `exists()`, `create()`, `get_or_create()`
- `update_or_create()`, `bulk_create()`, `all()`, `none()`

### Model Instance Methods
- `save()`, `delete()`, `refresh_from_db()`
- `clean()`, `full_clean()`

## ⚠️ Important Notes

1. **Async Views**: When using async views, all ORM operations must be awaited
2. **Error Handling**: Exception handling works the same in both contexts
3. **Transactions**: Django transactions work normally in both contexts
4. **Performance**: Uses `thread_sensitive=False` for better async performance

## 🛠️ Manual Control

You can manually enable/disable the auto-async functionality:

```python
from integrations.auto_async import enable_auto_async, disable_auto_async

# Enable
enable_auto_async()

# Disable (restores original Django ORM)
disable_auto_async()
```

## 🚫 Fallback Options

If you need manual control, you can still use the utility classes:

```python
from integrations.async_utils import AsyncDB

# Manual async execution
result = await AsyncDB.run(lambda: User.objects.get(id=123))

# Thread-sensitive operations
result = await AsyncDB.run_sensitive(lambda: some_thread_local_operation())
```

## 🎉 Benefits

1. **🔥 No Code Duplication**: Write ORM code once, use everywhere
2. **🤖 Automatic**: No manual `sync_to_async` calls needed
3. **📝 Clean Code**: Same syntax in sync and async contexts
4. **⚡ Performance**: Optimized for async operations
5. **🛡️ Safe**: Doesn't break existing code
6. **🎯 Transparent**: Works with existing Django patterns

This system eliminates the complexity of managing sync/async ORM operations and makes your code more maintainable and readable! 🚀 