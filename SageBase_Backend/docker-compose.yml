version: '3.8'
services:
  web:
    build: .
    ports:
      - "5678:5678"  # debugpy port for VS Code debugging (Django)
      - "5679:5679"  # debugpy port for Document Embedding debugging
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - DEBUG=0
    env_file:
      - .env.deployment
    depends_on:
      - redis
    command: sh -c "python manage.py migrate && python -Xfrozen_modules=off manage.py runserver 0.0.0.0:8000"

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"

  # Optional: Local PostgreSQL for testing
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: sagebase
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
