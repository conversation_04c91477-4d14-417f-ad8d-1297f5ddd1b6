#!/usr/bin/env python3
"""
Test script for GitHub token refresh functionality.
Run this script to test the token refresh system.
"""

import os
import sys
import django
from datetime import datetime

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')
django.setup()

from integrations.models import CompanyIntegration, IntegrationTool
from integrations.github.services import (
    get_valid_github_token, 
    validate_github_token, 
    refresh_github_token,
    is_token_expired
)
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_github_token_refresh():
    """Test the GitHub token refresh functionality."""
    print("🧪 Testing GitHub Token Refresh Functionality")
    print("=" * 50)
    
    # Get GitHub tool
    try:
        github_tool = IntegrationTool.objects.get(slug="github")
    except IntegrationTool.DoesNotExist:
        print("❌ GitHub integration tool not found")
        return False
    
    # Get all GitHub integrations
    integrations = CompanyIntegration.objects.filter(
        tool=github_tool,
        is_active=True
    )
    
    if not integrations.exists():
        print("❌ No active GitHub integrations found")
        return False
    
    print(f"📊 Found {integrations.count()} GitHub integration(s)")
    
    for integration in integrations:
        company_name = integration.company.name
        print(f"\n🏢 Testing company: {company_name}")
        
        if not integration.config:
            print(f"  ⚠️  No configuration found")
            continue
        
        ghu_token = integration.config.get("ghu_token")
        ghr_token = integration.config.get("ghr_token")
        
        if not ghu_token:
            print(f"  ❌ No access token found")
            continue
        
        if not ghr_token:
            print(f"  ❌ No refresh token found")
            continue
        
        print(f"  ✅ Found both access and refresh tokens")
        
        # Test current token validation
        print(f"  🔍 Validating current token...")
        is_valid = validate_github_token(ghu_token)
        
        if is_valid:
            print(f"  ✅ Current token is valid")
        else:
            print(f"  ⚠️  Current token is invalid or expired")
        
        # Test token refresh functionality
        print(f"  🔄 Testing token refresh...")
        try:
            valid_token = get_valid_github_token(integration.config)
            print(f"  ✅ Successfully obtained valid token")
            
            # Check if config was updated
            if integration.config.get("last_token_refresh"):
                print(f"  📝 Token was refreshed at: {integration.config['last_token_refresh']}")
                # Save the updated integration
                integration.save()
                print(f"  💾 Updated integration saved to database")
            else:
                print(f"  ℹ️  Token was already valid, no refresh needed")
                
        except Exception as e:
            print(f"  ❌ Failed to refresh token: {e}")
            return False
    
    print(f"\n✅ Token refresh test completed successfully!")
    return True

def test_token_validation():
    """Test token validation with invalid token."""
    print("\n🧪 Testing Token Validation with Invalid Token")
    print("=" * 50)
    
    # Test with an obviously invalid token
    invalid_token = "invalid_token_12345"
    is_valid = validate_github_token(invalid_token)
    
    if not is_valid:
        print("✅ Correctly identified invalid token")
    else:
        print("❌ Incorrectly identified invalid token as valid")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting GitHub Token Refresh Tests")
    print("=" * 50)
    
    try:
        # Test token validation
        if not test_token_validation():
            print("❌ Token validation test failed")
            sys.exit(1)
        
        # Test token refresh
        if not test_github_token_refresh():
            print("❌ Token refresh test failed")
            sys.exit(1)
        
        print("\n🎉 All tests passed!")
        print("✅ GitHub token refresh functionality is working correctly")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1) 