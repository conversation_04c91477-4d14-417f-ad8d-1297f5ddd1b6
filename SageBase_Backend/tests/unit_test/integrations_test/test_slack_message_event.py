import requests
import logging
import json
from datetime import datetime
import os

# Configuration
BASE_URL = "http://localhost:8001"
if not BASE_URL:
    raise ValueError("BASE_URL environment variable is required. Please set it in your configuration.")

os.environ['SLACK_TEST_MODE'] = 'true'

class SlackMessageTestRunner:
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.logger = logging.getLogger("test_logger")
        logging.basicConfig(level=logging.INFO)
        self.email = "<EMAIL>"
        self.password = "nour"
        self._authenticate()

    def _authenticate(self):
        """Authenticate with the backend server"""
        login_url = f"{self.base_url}/api/integrations/auth/login/"
        login_data = {
            "email": self.email,
            "password": self.password
        }
        response = self.session.post(login_url, json=login_data)
        if response.status_code == 200 and response.json().get('success'):
            token = response.json().get('token')
            self.session.headers.update({'Authorization': f'Bearer {token}'})
            self.logger.info("✅ Authentication successful")
        else:
            raise Exception(f"Authentication failed: {response.text}")

    def send_slack_message(self, event_payload):
        """Send a Slack message event to the webhook endpoint"""
        url = f"{self.base_url}/api/integrations/slack/webhook/receive/"
        headers = {}
        try:
            self.logger.info(f"📤 Sending message to webhook: {url}")
            self.logger.info(f"📤 Payload: {json.dumps(event_payload, indent=2)}")
            response = self.session.post(url, json=event_payload, headers=headers)
            if response.status_code == 200:
                self.logger.info("✅ Slack message processed successfully")
                self.logger.info("Response: %s", response.json())
                self.logger.info("⏳ Waiting 5 seconds for background processing logs...")
                import time
                time.sleep(5)
                self.logger.info("⏳ Background processing wait complete")
            else:
                self.logger.error("❌ Failed to process Slack message")
                self.logger.error("Response: %s", response.text)
        except Exception as e:
            self.logger.error(f"❌ Exception during Slack message processing: {e}")

if __name__ == "__main__":
    # Initialize the test runner
    runner = SlackMessageTestRunner()

    # Mock event payload - needs to match Slack's event_callback format
    mock_event = {
        "type": "message",  # This triggers message processing
        "channel": "C098E78KCD6",
        "user": "U0908KH070B",
        "text": "The Aurora Archive system was upgraded to version 2.5 on August 15, 2025, introducing advanced search capabilities and improved data security.",
        "ts": "1627384957.000200",
        "team": "T0908KH06TV",
        "event_id": "Ev1234567890"
    }
    mock_payload = {
        "type": "event_callback",  # This tells the webhook to process the event
        "event": mock_event,
        "event_id": "Ev1234567890",
        "team_id": "T0908KH06TV"
    }

    # Send the Slack message
    runner.send_slack_message(mock_payload)
