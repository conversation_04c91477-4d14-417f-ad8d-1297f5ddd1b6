import requests
import logging
import json
from datetime import datetime
import uuid 
import os

# Configuration
BASE_URL = "http://localhost:8001"
if not BASE_URL:
    raise ValueError("BASE_URL environment variable is required. Please set it in your configuration.")

# Set test mode environment variable
os.environ['DISCORD_TEST_MODE'] = 'true'

class DiscordMessageTestRunner:
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.logger = logging.getLogger("test_logger")
        logging.basicConfig(level=logging.INFO)

    def send_discord_message(self, event_payload):
        """Send a Discord message event to the webhook endpoint"""
        url = f"{self.base_url}/api/integrations/discord/webhook/"
        
        headers = {
            "Content-Type": "application/json",
            "X-Discord-Event": "MESSAGE_CREATE"
        }
        
        try:
            response = self.session.post(url, json=event_payload, headers=headers)
            self.logger.info(f"Discord webhook response: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"✅ Success: {result}")
                return result
            else:
                self.logger.error(f"❌ Error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Request failed: {e}")
            return None

    def create_discord_message_event(self, content="what is sagebase", **kwargs):
        """Create a Discord Gateway event payload"""
        
        unique_message_id = str(uuid.uuid4())
        # Default values
        defaults = {
            "message_id": unique_message_id,
            "channel_id": "1409585978691031122", 
            "guild_id": "1409585977848238254",  # Server ID - must match your CompanyIntegration
            "user_id": "1234567890123456789",
            "username": "testuser",
            "content": content,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        
        # Override with any provided values
        defaults.update(kwargs)
        
        # Discord Gateway event format
        event_data = {
            "id": defaults["message_id"],
            "channel_id": defaults["channel_id"],
            "guild_id": defaults["guild_id"],
            "content": defaults["content"],
            "timestamp": defaults["timestamp"],
            "author": {
                "id": defaults["user_id"],
                "username": defaults["username"],
                "bot": False
            },
            "attachments": [],
            "embeds": [],
            "mentions": []
        }
        
        gateway_event = {
            "t": "MESSAGE_CREATE",  # Event type
            "d": event_data,        # Event data
            "op": 0,               # Opcode
            "s": None,             # Sequence
            "timestamp": defaults["timestamp"]
        }
        
        return gateway_event

    def test_basic_message(self):
        """Test basic Discord message"""
        self.logger.info("🧪 Testing basic Discord message...")
        
        event = self.create_discord_message_event(
            content="Hello Can you help me with a question?"
        )
        
        return self.send_discord_message(event)

    def test_mention_message(self):
        """Test Discord message with bot mention"""
        self.logger.info("🧪 Testing Discord message with mention...")
        
        event = self.create_discord_message_event(
            content="Hey @SageBase, what's the weather like today?"
        )
        
        return self.send_discord_message(event)

    def test_question_message(self):
        """Test Discord message with a question"""
        self.logger.info("🧪 Testing Discord question message...")
        
        event = self.create_discord_message_event(
            content="What are the main features of SageBase? Can you explain?"
        )
        
        return self.send_discord_message(event)

    def test_long_message(self):
        """Test Discord message with longer content"""
        self.logger.info("🧪 Testing Discord long message...")
        
        long_content = """
        I have a complex question about our project setup. 
        We're using multiple integrations including Slack, Discord, and GitHub.
        Can you help me understand how these work together and what the best practices are?
        Also, what should I do if I encounter any issues?
        """
        
        event = self.create_discord_message_event(
            content=long_content.strip()
        )
        
        return self.send_discord_message(event)

    def run_all_tests(self):
        """Run all Discord message tests"""
        self.logger.info("🚀 Starting Discord message tests...")
        
        tests = [
            ("Basic Message", self.test_basic_message),
            ("Mention Message", self.test_mention_message),
            ("Question Message", self.test_question_message),
            ("Long Message", self.test_long_message)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"Running: {test_name}")
            self.logger.info(f"{'='*50}")
            
            try:
                result = test_func()
                results[test_name] = "✅ PASSED" if result else "❌ FAILED"
            except Exception as e:
                self.logger.error(f"❌ Test {test_name} failed with exception: {e}")
                results[test_name] = "💥 ERROR"
        
        # Summary
        self.logger.info(f"\n{'='*50}")
        self.logger.info("TEST SUMMARY")
        self.logger.info(f"{'='*50}")
        
        for test_name, result in results.items():
            self.logger.info(f"{test_name}: {result}")
        
        return results

def main():
    """Main function to run Discord message tests"""
    
    # Check prerequisites
    print("🔍 Checking prerequisites...")
    
    # Check if BASE_URL is accessible
    try:
        response = requests.get(f"{BASE_URL}/api/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is accessible")
        else:
            print(f"⚠️ Backend responded with status {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot connect to backend at {BASE_URL}: {e}")
        print("Please ensure your Django backend is running on the correct port.")
        return
    
    # Check if Discord integration is set up
    try:
        response = requests.get(f"{BASE_URL}/api/integrations/discord/", timeout=5)
        if response.status_code == 200:
            print("✅ Discord integration endpoint is accessible")
        else:
            print(f"⚠️ Discord integration endpoint responded with status {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot access Discord integration: {e}")
    
    print("\n🚀 Starting Discord message tests...")
    print("Note: Set DISCORD_TEST_MODE=true to bypass signature verification")
    
    # Run tests
    test_runner = DiscordMessageTestRunner()
    results = test_runner.run_all_tests()
    
    # Final status
    passed = sum(1 for result in results.values() if "PASSED" in result)
    total = len(results)
    
    print(f"\n🎯 Final Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Discord message tests passed!")
    else:
        print("⚠️ Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
