"""
End-to-End QA Management Test
Tests the complete QA lifecycle using REST API calls to a running backend server
it will add/detele/serach the QA: teh search include the agentic flow.

User credentials for testing:
- Email: <EMAIL>  
- Password: Test1234

Test flow:
1. Add a QA to the database via POST to knowledge-spaces/knowledge-space/<knowledge_space_id>
2. GET the endpoint to verify the QA was added
3. Approve the QA via POST to knowledge-spaces/qa/<qa_id>/approve
4. GET the endpoint to verify the QA is approved
5. Delete the QA via DELETE to knowledge-spaces/knowledge-space/<knowledge_space_id>/qa/<qa_id>
6. GET the endpoint to verify the QA is deleted
7. Add a QA with the same title and content
8. GET the endpoint to verify the QA was added (testing duplication handling)
9. Delete the QA
10. GET the endpoint to verify the QA is deleted
11. Add a QA with the same title and content again
12. GET the endpoint to verify the QA was added
"""

# Configuration
import os
import json
import requests
from requests.auth import HTTPBasicAuth
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env.dev, .env.test, or .env.deployment file
# Try .env.dev first, then .env.test, then .env.deployment as fallback
env_files = [ '.env.test']
env_loaded = False

for env_file in env_files:
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ Loaded environment from {env_file}")
        env_loaded = True
        break

if not env_loaded:
    print("⚠️  No environment file found (.env.dev, .env.test, or .env.deployment)")
    print("   Using system environment variables only")

# Read BASE_URL from environment variable FRONTEND_BASE_URL
BASE_URL = os.getenv("FRONTEND_BASE_URL")
if not BASE_URL:
    raise ValueError("FRONTEND_BASE_URL environment variable is required. Please set it in your .env file.")

KNOWLEDGE_SPACE_NAME = "sagebase"   # We'll find the knowledge space with this name

class QAAPITestRunner:
    """End-to-end QA management test using REST API calls"""
    
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.knowledge_space_id = None  # Will be set by finding the knowledge space
        
        # Test user credentials
        self.email = "<EMAIL>"
        self.password = "Test1234"
        
        # Test QA data with current date/time
        current_datetime = datetime.now()
        current_date_str = current_datetime.strftime("%B %d, %Y")
        current_time_str = current_datetime.strftime("%I:%M %p")
        
        self.qa_data = {
            "question_title": "SageBase Awards and Recognition",
            "question_content": "What was the last time SageBase received an award?",
            "answer_content": f"SageBase received the 'Best AI Innovation Award' at the TechCrunch Disrupt conference on {current_date_str} at {current_time_str} for our revolutionary knowledge management and Q&A system that helps companies organize and search their internal documentation using advanced AI technologies.",
            "question_tags": ["awards", "recognition", "company", "achievements"]
        }
        
        # Store date components for later verification
        self.test_date = current_date_str
        self.test_time = current_time_str
        
        # Set up authentication and find knowledge space
        self._authenticate()
        self._find_knowledge_space()
    
    def _authenticate(self):
        """Authenticate with the backend server"""
        try:
            # Call the login endpoint
            login_url = f"{self.base_url}/api/integrations/auth/login/"
            login_data = {
                "email": self.email,
                "password": self.password
            }
            
            # Set basic headers first
            self.session.headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            })
            
            print(f"🔐 Attempting to authenticate as {self.email}...")
            
            auth_response = self.session.post(login_url, json=login_data)
            
            if auth_response.status_code == 200:
                response_data = auth_response.json()
                if response_data.get('success'):
                    token = response_data.get('token')
                    if token:
                        # Update session headers with JWT token
                        self.session.headers.update({'Authorization': f'Bearer {token}'})
                        print(f"✅ Successfully authenticated as {self.email}")
                        return
                    else:
                        print("❌ No token received from login response")
                else:
                    print(f"❌ Login failed: {response_data.get('message', 'Unknown error')}")
            else:
                print(f"❌ Login request failed with status {auth_response.status_code}")
                print(f"Response: {auth_response.text}")
                
            raise Exception(f"Authentication failed for {self.email}")
            
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            raise
    
    def _find_knowledge_space(self):
        """Find the knowledge space with the specified name"""
        try:
            # Call the knowledge-spaces endpoint to get all knowledge spaces
            spaces_url = f"{self.base_url}/api/knowledge-spaces"
            response = self.session.get(spaces_url)
            
            if response.status_code != 200:
                print(f"❌ Failed to get knowledge spaces. Status: {response.status_code}")
                print(f"Response: {response.text}")
                raise Exception("Failed to fetch knowledge spaces")
            
            spaces_data = response.json()
            if not spaces_data.get('success'):
                print(f"❌ API returned error: {spaces_data}")
                raise Exception("API returned unsuccessful response")
            
            # Find knowledge space with the specified name
            spaces = spaces_data.get('data', [])
            target_space = None
            
            for space in spaces:
                if space.get('name', '').lower() == KNOWLEDGE_SPACE_NAME.lower():
                    target_space = space
                    break
            
            if not target_space:
                print(f"❌ Knowledge space '{KNOWLEDGE_SPACE_NAME}' not found")
                print(f"Available knowledge spaces: {[space.get('name') for space in spaces]}")
                raise Exception(f"Knowledge space '{KNOWLEDGE_SPACE_NAME}' not found")
            
            self.knowledge_space_id = target_space['id']
            print(f"✅ Found knowledge space '{KNOWLEDGE_SPACE_NAME}' with ID: {self.knowledge_space_id}")
            
        except Exception as e:
            print(f"❌ Error finding knowledge space: {e}")
            raise
    
    def _get_qa_endpoint_url(self):
        """Get the QA endpoint URL for the knowledge space"""
        if not self.knowledge_space_id:
            raise ValueError("knowledge_space_id must be provided")
        return f"{self.base_url}/api/knowledge-spaces/knowledge-space/{self.knowledge_space_id}"
    
    def _get_approve_url(self, qa_id):
        """Get the approve endpoint URL for a QA"""
        return f"{self.base_url}/api/knowledge-spaces/qa/{qa_id}/approve"
    
    def _get_qa_detail_url(self, qa_id):
        """Get the QA detail endpoint URL"""
        return f"{self.base_url}/api/knowledge-spaces/knowledge-space/{self.knowledge_space_id}/qa/{qa_id}"
    
    def _get_agent_ask_url(self):
        """Get the Agent Ask endpoint URL"""
        return f"{self.base_url}/api/agent/ask/"
    
    def _test_agent_ask_api(self):
        """Test the Agent Ask API to verify it can find the QA with injected date"""
        try:
            agent_ask_url = self._get_agent_ask_url()
            
            # Query for SageBase awards to see if it finds our injected QA data
            query_data = {
                "question": "When did SageBase receive the Best AI Innovation Award?",
                "context": {
                    "user_email": self.email,
                    "history": [],
                    "filters": ["local"]  # Search local knowledge spaces
                }
            }
            
            print(f"🔍 Querying Agent API: {query_data['question']}")
            
            response = self.session.post(agent_ask_url, json=query_data)
            
            if response.status_code != 200:
                print(f"❌ Agent Ask API call failed. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            response_data = response.json()
            answer = response_data.get('answer', '')
            
            print(f"🤖 Agent API Response: {answer}")
            
            # Check if the response contains the injected date and time
            if self.test_date in answer and self.test_time in answer:
                print(f"✅ Agent found the injected date ({self.test_date}) and time ({self.test_time}) in response!")
                return True
            elif self.test_date in answer:
                print(f"✅ Agent found the injected date ({self.test_date}) in response!")
                return True
            else:
                print(f"❌ Agent response doesn't contain the injected date ({self.test_date})")
                print(f"Expected to find: {self.test_date}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing Agent Ask API: {e}")
            return False
    
    def test_qa_lifecycle(self):
        """Run the complete QA lifecycle test"""
        try:
            qa_endpoint = self._get_qa_endpoint_url()
            
            # Step 1: Add a QA to the database
            print("\n🚀 Step 1: Adding QA to knowledge space...")
            response = self.session.post(qa_endpoint, json=self.qa_data)
            
            if response.status_code != 201:
                print(f"❌ Failed to create QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            qa_id = response.json()['data']['created_qa']['id']
            print(f"✅ QA created with ID: {qa_id}")
            
            # Step 2: GET to verify the QA was added
            print("\n📋 Step 2: Verifying QA was added...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            qa_found = any(qa['id'] == qa_id for qa in qas)
            
            if not qa_found:
                print("❌ QA not found in knowledge space")
                return False
            
            print("✅ QA found in knowledge space")
            
            # Step 3: Approve the QA
            print("\n👍 Step 3: Approving the QA...")
            approve_url = self._get_approve_url(qa_id)
            response = self.session.post(approve_url, json={})
            
            if response.status_code != 200:
                print(f"❌ Failed to approve QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            print("✅ QA approved successfully")
            
            # Step 4: GET to verify the QA is approved
            print("\n✔️ Step 4: Verifying QA is approved...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            approved_qa = next((qa for qa in qas if qa['id'] == qa_id), None)
            
            if not approved_qa:
                print("❌ Approved QA not found")
                return False
            
            if approved_qa['metadata']['approvalStatus'] != 'approved':
                print(f"❌ QA not approved. Status: {approved_qa['metadata']['approvalStatus']}")
                return False
            
            print("✅ QA approval status verified")
            
            # Step 4.5: Test Agent Ask API before deletion
            print("\n🤖 Step 4.5: Testing Agent Ask API with approved QA...")
            if self._test_agent_ask_api():
                print("✅ Agent Ask API test passed!")
            else:
                print("❌ Agent Ask API test failed!")
                return False
            
            # Step 5: Delete the QA
            print("\n🗑️ Step 5: Deleting the QA...")
            delete_url = self._get_qa_detail_url(qa_id)
            response = self.session.delete(delete_url)
            
            if response.status_code != 200:
                print(f"❌ Failed to delete QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            print("✅ QA deleted successfully")
            
            # Step 6: GET to verify the QA is deleted
            print("\n🔍 Step 6: Verifying QA is deleted...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            qa_found = any(qa['id'] == qa_id for qa in qas)
            
            if qa_found:
                print("❌ QA still found in knowledge space after deletion")
                return False
            
            print("✅ QA deletion verified")
            
            # Step 7: Add a QA with the same title and content (test duplication handling)
            print("\n🔄 Step 7: Adding QA with same title and content...")
            response = self.session.post(qa_endpoint, json=self.qa_data)
            
            if response.status_code != 201:
                print(f"❌ Failed to create duplicate QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            qa_id_2 = response.json()['data']['created_qa']['id']
            print(f"✅ Duplicate QA created with ID: {qa_id_2}")
            
            # Step 8: GET to verify the QA was added
            print("\n📋 Step 8: Verifying duplicate QA was added...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            qa_found = any(qa['id'] == qa_id_2 for qa in qas)
            
            if not qa_found:
                print("❌ Duplicate QA not found in knowledge space")
                return False
            
            print("✅ Duplicate QA found in knowledge space")
            
            # Step 9: Delete the QA
            print("\n🗑️ Step 9: Deleting the duplicate QA...")
            delete_url_2 = self._get_qa_detail_url(qa_id_2)
            response = self.session.delete(delete_url_2)
            
            if response.status_code != 200:
                print(f"❌ Failed to delete duplicate QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            print("✅ Duplicate QA deleted successfully")
            
            # Step 10: GET to verify the QA is deleted
            print("\n🔍 Step 10: Verifying duplicate QA is deleted...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            qa_found = any(qa['id'] == qa_id_2 for qa in qas)
            
            if qa_found:
                print("❌ Duplicate QA still found in knowledge space after deletion")
                return False
            
            print("✅ Duplicate QA deletion verified")
            
            # Step 11: Add a QA with the same title and content again
            print("\n🔄 Step 11: Adding QA with same title and content again...")
            response = self.session.post(qa_endpoint, json=self.qa_data)
            
            if response.status_code != 201:
                print(f"❌ Failed to create third QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            qa_id_3 = response.json()['data']['created_qa']['id']
            print(f"✅ Third QA created with ID: {qa_id_3}")
            
            # Step 12: GET to verify the QA was added
            print("\n📋 Step 12: Verifying third QA was added...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            qa_found = any(qa['id'] == qa_id_3 for qa in qas)
            
            if not qa_found:
                print("❌ Third QA not found in knowledge space")
                return False
            
            print("✅ Third QA found in knowledge space")
            
            # Step 13: Delete the third QA to clean up
            print("\n🗑️ Step 13: Deleting the third QA...")
            delete_url_3 = self._get_qa_detail_url(qa_id_3)
            response = self.session.delete(delete_url_3)
            
            if response.status_code != 200:
                print(f"❌ Failed to delete third QA. Status: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            print("✅ Third QA deleted successfully")
            
            # Step 14: GET to verify the third QA is deleted
            print("\n🔍 Step 14: Verifying third QA is deleted...")
            response = self.session.get(qa_endpoint)
            
            if response.status_code != 200:
                print(f"❌ Failed to get QAs. Status: {response.status_code}")
                return False
            
            qas = response.json()['data']
            qa_found = any(qa['id'] == qa_id_3 for qa in qas)
            
            if qa_found:
                print("❌ Third QA still found in knowledge space after deletion")
                return False
            
            print("✅ Third QA deletion verified")
            
            print("\n🎉 All QA lifecycle tests completed successfully!")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False

def run_qa_tests(base_url=BASE_URL):
    """Run the QA management tests"""
    print("=" * 60)
    print("🧪 Starting QA Management End-to-End Tests")
    print(f"🌐 Backend URL: {base_url}")
    print(f"📝 Looking for knowledge space: '{KNOWLEDGE_SPACE_NAME}'")
    print("=" * 60)
    
    try:
        tester = QAAPITestRunner(base_url)
        success = tester.test_qa_lifecycle()
        
        print("\n" + "=" * 60)
        if success:
            print("🎊 All tests passed successfully!")
        else:
            print("💥 Some tests failed!")
        print("=" * 60)
        
    except Exception as e:
        print("\n" + "=" * 60)
        print(f"💥 Test setup failed: {e}")
        print("=" * 60)

# Example usage:
if __name__ == "__main__":
    # Run the tests - will automatically find the 'sagebase' knowledge space
    run_qa_tests()