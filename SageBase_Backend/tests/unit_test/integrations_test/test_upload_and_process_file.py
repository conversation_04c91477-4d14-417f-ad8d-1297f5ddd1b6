"""
End-to-End File Upload and Processing Test
Tests the file upload and processing functionality using REST API calls to a running backend server.

User credentials for testing:
- Email: <EMAIL>
- Password: Test1234

Test flow:
1. Authenticate with the backend.
2. Upload a file to the `upload_and_process_files` endpoint.
3. Verify the response to ensure the file was processed successfully.
4. Log the results, including any detected Q&A pairs.
"""

import os
import sys
import requests
from dotenv import load_dotenv
from pathlib import Path

# Add current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

import common_utils_tests

#change this one to .env.dev or .env.test or .env.deployment
env_files = [ '.env.dev']
env_loaded = False

for env_file in env_files:
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ Loaded environment from {env_file}")
        env_loaded = True
        break

if not env_loaded:
    print("⚠️  No environment file found (.env.dev, .env.test, or .env.deployment)")
    print("   Using system environment variables only")

# Read BASE_URL from environment variable FRONTEND_BASE_URL
BASE_URL = os.getenv("BACKEND_BASE_URL")
if not BASE_URL:
    raise ValueError("BACKEND_BASE_URL environment variable is required. Please set it in your .env file.")

class FileUploadTestRunner:
    def __init__(self, base_url=BASE_URL, knowledge_space_name=None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.email = "<EMAIL>"
        self.password = "Test1234"
        if knowledge_space_name is None:
            self.knowledge_space_name = "test"
        else:
            self.knowledge_space_name = knowledge_space_name
        self._authenticate()

    def _authenticate(self):
        """Authenticate with the backend server"""
        login_url = f"{self.base_url}/api/integrations/auth/login/"
        login_data = {
            "email": self.email,
            "password": self.password
        }

        response = self.session.post(login_url, json=login_data)
        if response.status_code == 200 and response.json().get('success'):
            token = response.json().get('token')
            self.session.headers.update({'Authorization': f'Bearer {token}'})
            print("✅ Authentication successful")
        else:
            raise Exception(f"Authentication failed: {response.text}")

    def upload_and_process_file(self, file_path, knowledge_space_id, knowledge_space_name=None):
        """Upload and process a file"""
        url = f"{self.base_url}/api/knowledge-spaces/upload/process-files"
        files = {"files[]": open(file_path, "rb")}
        data = {
            "knowledge_space_id": knowledge_space_id,
            "name_of_knowledge_space": knowledge_space_name
        }

        try:
            response = self.session.post(url, files=files, data=data)
            if response.status_code == 200 and response.json().get('success'):
                print("✅ File uploaded and processed successfully")
                print("Response:", response.json())
                print("Knowledge space in response:", response.json().get('knowledge_space'))
                return True
            else:
                print("❌ File upload or processing failed")
                print("Response:", response.text)
                raise Exception(f"File upload failed with status {response.status_code}: {response.text}")
        except Exception as e:
            print(f"❌ Exception during file upload: {e}")
            raise

    def verify_qa_extraction(self, knowledge_space_id):
        """Verify that Q&A pairs were extracted and stored correctly"""
        url = f"{self.base_url}/api/knowledge-spaces/knowledge-space/{knowledge_space_id}"
        try:
            response = self.session.get(url)

            if response.status_code == 200 and response.json().get('success'):
                qas = response.json().get('data', [])
                print(f"✅ Retrieved {len(qas)} Q&A pairs from knowledge space {knowledge_space_id}")
                return qas
            else:
                print("❌ Failed to retrieve Q&A pairs")
                print("Response:", response.text)
                raise Exception(f"Q&A retrieval failed with status {response.status_code}: {response.text}")
        except Exception as e:
            print(f"❌ Exception during Q&A retrieval: {e}")
            raise

    def _find_knowledge_space(self, knowledge_space_name=None):
        if knowledge_space_name is None:
            knowledge_space_name = self.knowledge_space_name
        """Find the knowledge space with the specified name"""
        url = f"{self.base_url}/api/knowledge-spaces"
        try:
            response = self.session.get(url)

            if response.status_code == 200 and response.json().get('success'):
                spaces = response.json().get('data', [])
                for space in spaces:
                    if space.get('name', '').lower() == knowledge_space_name.lower():
                        print(f"✅ Found knowledge space '{knowledge_space_name}' with ID: {space['id']}")
                        return space['id']
                print(f"❌ Knowledge space '{knowledge_space_name}' not found")
                raise Exception(f"Knowledge space '{knowledge_space_name}' not found")
            else:
                print("❌ Failed to fetch knowledge spaces")
                print("Response:", response.text)
                raise Exception(f"Failed to fetch knowledge spaces with status {response.status_code}: {response.text}")
        except Exception as e:
            print(f"❌ Exception during knowledge space search: {e}")
            raise

    def _create_knowledge_space(self, knowledge_space_name=None):
        """Create a new knowledge space"""
        if knowledge_space_name is None:
            knowledge_space_name = self.knowledge_space_name
        success, new_knowledge_space, status_code = common_utils_tests.create_knowledge_space_api(
            self.base_url, 
            self.session.headers['Authorization'], 
            {"name": knowledge_space_name, "color": "#000000", "initial": "N"}
        )
        if success:
            print(f"✅ New knowledge space created: {new_knowledge_space}")
            return new_knowledge_space.get('data', {}).get('id') if isinstance(new_knowledge_space, dict) else None
        else:
            print(f"❌ Failed to create new knowledge space: {new_knowledge_space}")
            print(f"Status code: {status_code}")
            print(f"Response: {new_knowledge_space}")
            raise Exception(f"Failed to create knowledge space '{knowledge_space_name}' with status {status_code}: {new_knowledge_space}")
            
    def _delete_knowledge_space(self, knowledge_space_id):
        """Delete a knowledge space"""
        url = f"{self.base_url}/api/knowledge-spaces/{knowledge_space_id}"
        response = self.session.delete(url)
        if response.status_code == 200 and response.json().get('success'):
            print(f"✅ Knowledge space deleted successfully: {knowledge_space_id}")
            return True

if __name__ == "__main__":
    try:
        print("🚀 Starting End-to-End File Upload and Processing Test")
        
        # Initialize the test runner
        print("📋 Step 1: Initializing test runner and authenticating...")
        runner = FileUploadTestRunner(knowledge_space_name="test")

        # Test create new knowledge space for testing
        print("📋 Step 2: Creating test knowledge space...")
        test_knowledge_space_id = runner._create_knowledge_space()
        
        if test_knowledge_space_id:
            print(f"✅ Test knowledge space created with ID: {test_knowledge_space_id}")
        else:
            print("⚠️  Knowledge space created but ID not returned, continuing with existing space search...")

        # Fetch the knowledge space ID dynamically
        print("📋 Step 3: Finding knowledge space ")
        knowledge_space_id = runner._find_knowledge_space()

        # Path to the file to upload
        print("📋 Step 4: Validating test file...")
        test_file_path = Path("tests/unit_test/integrations_test/file1.txt")
        if not test_file_path.exists():
            raise FileNotFoundError(f"Test file not found: {test_file_path}")
        print(f"✅ Test file found: {test_file_path}")

        # Run the upload and process test
        print("📋 Step 5: Uploading and processing file...")
        runner.upload_and_process_file(test_file_path, knowledge_space_id)

        # Verify Q&A extraction
        print("📋 Step 6: Verifying Q&A extraction...")
        qas = runner.verify_qa_extraction(knowledge_space_id)

        #run delete knowledge space
        print("📋 Step 7: Deleting knowledge space...")
        runner.delete_knowledge_space(knowledge_space_id)
        
        print(f"🎉 TEST COMPLETED SUCCESSFULLY!")
        print(f"✅ All steps passed")
        print(f"✅ File uploaded and processed")
        print(f"✅ {len(qas) if qas else 0} Q&A pairs extracted")
        
    except Exception as e:
        print(f"💥 TEST FAILED: {str(e)}")
        print("❌ Test execution stopped due to failure")
        exit(1)
