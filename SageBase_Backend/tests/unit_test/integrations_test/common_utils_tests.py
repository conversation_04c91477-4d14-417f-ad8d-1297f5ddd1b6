
"""
Common utility functions for creating knowledge spaces via API endpoints
"""

import requests
import json
import logging
import sys
import os

# Add the project root to the path so we can import our modules
# Go up 3 levels: tests/unit_test/integrations_test -> tests/unit_test -> tests -> project_root
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

# Import generic colored logging setup
from logger.logging_utils import setup_colored_logging

# Setup logging
colorlog_available = setup_colored_logging()
logger = logging.getLogger(__name__)

def create_knowledge_space_api(base_url, auth_token, knowledge_space_data):
    """
    Create a new knowledge space by calling the API endpoint like a frontend user.
    
    Args:
        base_url (str): The base URL of the API (e.g., 'http://localhost:8000')
        auth_token (str): Authentication token for the user
        knowledge_space_data (dict): Dictionary containing knowledge space data
            Required fields: name, color, initial
            Optional fields: doc_responsible_id, secondary_responsible_id
    
    Returns:
        tuple: (success: bool, response_data: dict, status_code: int)
            - If successful: (True, response_json, 201)
            - If failed: (False, error_response, error_status_code)
    
    Example usage:
        success, data, status_code = create_knowledge_space_api(
            base_url='http://localhost:8000',
            auth_token='your-auth-token-here',
            knowledge_space_data={
                'name': 'Project Documentation',
                'color': '#3B82F6',
                'initial': 'P',
                'doc_responsible_id': 123,
                'secondary_responsible_id': None
            }
        )
    """
    try:
        # Prepare the API endpoint
        endpoint = f"{base_url.rstrip('/')}/api/knowledge-spaces"
        
        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': auth_token if auth_token.startswith('Bearer ') else f'Bearer {auth_token}'
        }
        
        logger.info(f"🚀 Making POST request to create knowledge space: {endpoint}")
        logger.debug(f"📤 Request data: {knowledge_space_data}")
        logger.debug(f"🔑 Using Authorization header: {headers['Authorization'][:20]}...")
        
        # Make the POST request
        response = requests.post(
            endpoint,
            headers=headers,
            data=json.dumps(knowledge_space_data),
            timeout=30
        )
        
        # Parse response
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            response_data = {'error': 'Invalid JSON response', 'raw_response': response.text}
        
        # Log the response
        if response.status_code == 201:
            logger.info(f"✅ Knowledge space created successfully: {response.status_code}")
            logger.debug(f"📥 Response data: {response_data}")
            return True, response_data, response.status_code
        else:
            logger.error(f"❌ Failed to create knowledge space: {response.status_code}")
            logger.error(f"📥 Error response: {response_data}")
            return False, response_data, response.status_code
            
    except requests.exceptions.Timeout:
        error_msg = "Request timed out after 30 seconds"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 408
        
    except requests.exceptions.ConnectionError:
        error_msg = "Failed to connect to the API server"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 503
        
    except requests.exceptions.RequestException as e:
        error_msg = f"Request failed: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 500
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 500


def create_knowledge_space_with_session(session, base_url, knowledge_space_data):
    """
    Create a new knowledge space using an existing requests session (useful for tests with authentication).
    
    Args:
        session (requests.Session): Authenticated requests session
        base_url (str): The base URL of the API
        knowledge_space_data (dict): Dictionary containing knowledge space data
    
    Returns:
        tuple: (success: bool, response_data: dict, status_code: int)
    """
    try:
        endpoint = f"{base_url.rstrip('/')}/api/knowledge-spaces"
        
        logger.info(f"🚀 Making POST request with session to create knowledge space: {endpoint}")
        logger.debug(f"📤 Request data: {knowledge_space_data}")
        
        # Make the POST request using the session
        response = session.post(
            endpoint,
            json=knowledge_space_data,  # Using json parameter for automatic content-type
            timeout=30
        )
        
        # Parse response
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            response_data = {'error': 'Invalid JSON response', 'raw_response': response.text}
        
        # Log the response
        if response.status_code == 201:
            logger.info(f"✅ Knowledge space created successfully: {response.status_code}")
            logger.debug(f"📥 Response data: {response_data}")
            return True, response_data, response.status_code
        else:
            logger.error(f"❌ Failed to create knowledge space: {response.status_code}")
            logger.error(f"📥 Error response: {response_data}")
            return False, response_data, response.status_code
            
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 500


def delete_knowledge_space_api(base_url, auth_token, knowledge_space_id):
    """
    Delete a knowledge space by calling the API endpoint like a frontend user.
    
    Args:
        base_url (str): The base URL of the API (e.g., 'http://localhost:8000')
        auth_token (str): Authentication token for the user
        knowledge_space_id (str): ID of the knowledge space to delete
    
    Returns:
        tuple: (success: bool, response_data: dict, status_code: int)
            - If successful: (True, response_json, 200)
            - If failed: (False, error_response, error_status_code)
    
    Example usage:
        success, data, status_code = delete_knowledge_space_api(
            base_url='http://localhost:8000',
            auth_token='your-auth-token-here',
            knowledge_space_id='know-12345678'
        )
    """
    try:
        # Prepare the API endpoint
        endpoint = f"{base_url.rstrip('/')}/api/knowledge-spaces"
        
        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': auth_token if auth_token.startswith('Bearer ') else f'Bearer {auth_token}'
        }
        
        # Prepare data
        data = {'knowledge_space_id': knowledge_space_id}
        
        logger.info(f"🗑️ Making DELETE request to delete knowledge space: {endpoint}")
        logger.debug(f"📤 Request data: {data}")
        logger.debug(f"🔑 Using Authorization header: {headers['Authorization'][:20]}...")
        
        # Make the DELETE request
        response = requests.delete(
            endpoint,
            headers=headers,
            data=json.dumps(data),
            timeout=30
        )
        
        # Parse response
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            response_data = {'error': 'Invalid JSON response', 'raw_response': response.text}
        
        # Log the response
        if response.status_code == 200:
            logger.info(f"✅ Knowledge space deleted successfully: {response.status_code}")
            logger.debug(f"📥 Response data: {response_data}")
            return True, response_data, response.status_code
        else:
            logger.error(f"❌ Failed to delete knowledge space: {response.status_code}")
            logger.error(f"📥 Error response: {response_data}")
            return False, response_data, response.status_code
            
    except requests.exceptions.Timeout:
        error_msg = "Request timed out after 30 seconds"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 408
        
    except requests.exceptions.ConnectionError:
        error_msg = "Failed to connect to the API server"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 503
        
    except requests.exceptions.RequestException as e:
        error_msg = f"Request failed: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 500
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 500


def delete_knowledge_space_with_session(session, base_url, knowledge_space_id):
    """
    Delete a knowledge space using an existing requests session (useful for tests with authentication).
    
    Args:
        session (requests.Session): Authenticated requests session
        base_url (str): The base URL of the API
        knowledge_space_id (str): ID of the knowledge space to delete
    
    Returns:
        tuple: (success: bool, response_data: dict, status_code: int)
    """
    try:
        endpoint = f"{base_url.rstrip('/')}/api/knowledge-spaces"
        
        # Prepare data
        data = {'knowledge_space_id': knowledge_space_id}
        
        logger.info(f"🗑️ Making DELETE request with session to delete knowledge space: {endpoint}")
        logger.debug(f"📤 Request data: {data}")
        
        # Make the DELETE request using the session
        response = session.delete(
            endpoint,
            json=data,  # Using json parameter for automatic content-type
            timeout=30
        )
        
        # Parse response
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            response_data = {'error': 'Invalid JSON response', 'raw_response': response.text}
        
        # Log the response
        if response.status_code == 200:
            logger.info(f"✅ Knowledge space deleted successfully: {response.status_code}")
            logger.debug(f"📥 Response data: {response_data}")
            return True, response_data, response.status_code
        else:
            logger.error(f"❌ Failed to delete knowledge space: {response.status_code}")
            logger.error(f"📥 Error response: {response_data}")
            return False, response_data, response.status_code
            
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, {'error': error_msg}, 500