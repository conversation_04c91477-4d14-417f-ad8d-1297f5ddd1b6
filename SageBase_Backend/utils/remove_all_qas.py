#!/usr/bin/env python3
"""
Script to remove all Q&As from the database and their embeddings from the vector database.
This script will delete all QA records and their corresponding embeddings.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the path so we can import our modules
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

# Configure Django settings for standalone script
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')

import django
django.setup()

# Import generic colored logging setup
from logger.logging_utils import setup_colored_logging

# Setup logging
colorlog_available = setup_colored_logging()
logger = logging.getLogger(__name__)

if not colorlog_available:
    logger.warning("colorlog not available - using basic logging. Install with: pip install colorlog")

# Import Django models and utilities
from knowledge_spaces_Q_A.models import QA
from knowledge_spaces_Q_A.utils import delete_embedding_q_a
from django.db import transaction
from django.conf import settings

def load_env_file():
    """Load environment variables from .env.dev file"""
    try:
        from dotenv import load_dotenv
        
        # Define possible env file locations in order of preference
        base_dir = Path(__file__).parent
        env_files = [
            base_dir / ".env.dev",
            base_dir / ".env.development", 
            base_dir / ".env",
            Path.cwd() / ".env.dev",
            Path.cwd() / ".env"
        ]
        
        # Load the first existing env file
        env_file_loaded = None
        for env_file in env_files:
            if env_file.exists():
                load_dotenv(env_file)
                env_file_loaded = env_file
                break
        
        if env_file_loaded:
            logger.info(f"📁 Loaded environment file: {env_file_loaded}")
        else:
            logger.warning("⚠️  No .env file found - using system environment variables only")
            
    except ImportError:
        logger.warning("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")

def remove_all_qas():
    """
    Remove all Q&As from the database and their embeddings from the vector database.
    """
    try:
        # Get all Q&As
        all_qas = QA.objects.all()
        total_count = all_qas.count()
        
        if total_count == 0:
            logger.info("ℹ️  No Q&As found in the database")
            return {
                "success": True,
                "message": "No Q&As to remove",
                "deleted_count": 0,
                "embedding_deleted_count": 0
            }
        
        logger.warning(f"🗑️  Found {total_count} Q&As to delete")
        logger.warning("⚠️  This will permanently delete all Q&As and their embeddings!")
        
        # Ask for confirmation
        try:
            confirm = input("Are you sure you want to delete ALL Q&As? (yes/no): ").strip().lower()
            if confirm != 'yes':
                logger.info("❌ Operation cancelled by user")
                return {
                    "success": False,
                    "message": "Operation cancelled by user",
                    "deleted_count": 0,
                    "embedding_deleted_count": 0
                }
        except EOFError:
            logger.info("❌ Operation cancelled (no input available)")
            return {
                "success": False,
                "message": "Operation cancelled (no input available)",
                "deleted_count": 0,
                "embedding_deleted_count": 0
            }
        
        deleted_count = 0
        embedding_deleted_count = 0
        errors = []
        
        # Process Q&As in batches to avoid memory issues
        batch_size = 100
        for i in range(0, total_count, batch_size):
            batch_qas = all_qas[i:i + batch_size]
            
            logger.info(f"🔄 Processing batch {i//batch_size + 1}/{(total_count + batch_size - 1)//batch_size}")
            
            for qa in batch_qas:
                try:
                    # First, try to delete the embedding
                    try:
                        delete_embedding_q_a(qa)
                        embedding_deleted_count += 1
                        logger.debug(f"✅ Deleted embedding for Q&A: {qa.question_title} (ID: {qa.id})")
                    except Exception as e:
                        logger.warning(f"⚠️  Failed to delete embedding for Q&A {qa.id}: {e}")
                        errors.append(f"Embedding deletion failed for {qa.id}: {str(e)}")
                    
                    # Then delete the Q&A record
                    qa.delete()
                    deleted_count += 1
                    logger.debug(f"✅ Deleted Q&A: {qa.question_title} (ID: {qa.id})")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to delete Q&A {qa.id}: {e}")
                    errors.append(f"Q&A deletion failed for {qa.id}: {str(e)}")
        
        logger.info(f"✅ Successfully deleted {deleted_count} Q&As")
        logger.info(f"✅ Successfully deleted {embedding_deleted_count} embeddings")
        
        if errors:
            logger.warning(f"⚠️  {len(errors)} errors occurred during deletion")
            for error in errors[:10]:  # Show first 10 errors
                logger.warning(f"   - {error}")
            if len(errors) > 10:
                logger.warning(f"   ... and {len(errors) - 10} more errors")
        
        return {
            "success": True,
            "message": f"Successfully deleted {deleted_count} Q&As and {embedding_deleted_count} embeddings",
            "deleted_count": deleted_count,
            "embedding_deleted_count": embedding_deleted_count,
            "errors": errors
        }
        
    except Exception as e:
        logger.exception(f"❌ Failed to remove Q&As: {e}")
        return {
            "success": False,
            "message": f"Failed to remove Q&As: {str(e)}",
            "deleted_count": 0,
            "embedding_deleted_count": 0,
            "errors": [str(e)]
        }

def main():
    """Main function to run the Q&A removal script"""
    logger.info("🚀 Q&A Removal Script")
    logger.info("=" * 50)
    
    # Load environment variables
    load_env_file()
    
    logger.info("🔍 Checking database for Q&As...")
    
    # Get initial count
    initial_count = QA.objects.count()
    logger.info(f"📊 Found {initial_count} Q&As in the database")
    
    if initial_count == 0:
        logger.info("ℹ️  No Q&As to remove")
        return
    
    # Remove all Q&As
    result = remove_all_qas()
    
    # Show final results
    logger.info("=" * 50)
    logger.info("📊 Final Results:")
    logger.info(f"   Q&As deleted: {result['deleted_count']}")
    logger.info(f"   Embeddings deleted: {result['embedding_deleted_count']}")
    logger.info(f"   Success: {result['success']}")
    
    if result['success']:
        logger.info("✅ Q&A removal completed successfully!")
    else:
        logger.error("❌ Q&A removal failed!")
        if result.get('errors'):
            logger.error("Errors:")
            for error in result['errors'][:5]:
                logger.error(f"   - {error}")

if __name__ == "__main__":
    main() 