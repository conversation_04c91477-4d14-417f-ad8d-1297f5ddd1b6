import asyncio, threading
from asgiref.sync import async_to_sync
import logging
from bs4 import BeautifulSoup

def spawn_function(coro_fn, *args, **kwargs):
    """Run an async function in the background without blocking the request.
    This is useful for slack,discord.... wher we can reply the request and open a background task to run the real request.
    """
    try:
        loop = asyncio.get_running_loop()
        loop.create_task(coro_fn(*args, **kwargs))
    except RuntimeError:
        # No running loop (sync context) → run in a daemon thread
        def runner():
            async_to_sync(coro_fn)(*args, **kwargs)
        threading.Thread(target=runner, daemon=True).start()
        
        
def clean_html(html_content: str) -> str:
    """
    Clean html content by:
    1. Removing markdown code block syntax (```)
    2. Removing HTML tags and unescaping content using BeautifulSoup
    """
    try:
        # Remove ```html and ``` markers
        cleaned_content = html_content.replace("```html", "").replace("```", "").strip()
        # Check if the content is HTML (contains any HTML tags)
        if not any(tag in cleaned_content for tag in ("<", ">")):
            return cleaned_content
        # Parse and prettify HTML
        soup = BeautifulSoup(cleaned_content, "lxml")
        return soup.prettify()
    except Exception as e:
        logging.exception(f"Failed to clean html content: {e}")
        return html_content