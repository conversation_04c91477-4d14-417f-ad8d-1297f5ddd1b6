# utils/token_utils.py

def get_token_count(text, model="text-embedding-3-small"):
    """
    Returns the number of tokens in the given text for the specified OpenAI model.
    Falls back to word count if tiktoken is not available.
    """
    try:
        import tiktoken
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text or ""))
    except Exception:
        # Fallback: simple word count
        return len((text or "").split()) 