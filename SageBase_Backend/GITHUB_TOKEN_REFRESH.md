# GitHub Token Refresh System

This document explains the automatic GitHub token refresh functionality implemented in the SageBase backend.

## Overview

The GitHub integration stores two types of tokens:
- **GHU Token** (`ghu_token`): Access token for GitHub API calls
- **GHR Token** (`ghr_token`): Refresh token used to get new access tokens when they expire

The system automatically refreshes expired access tokens using the refresh token, ensuring continuous GitHub API access.

## How It Works

### 1. Token Storage
Tokens are stored in the `CompanyIntegration.config` JSO<PERSON>ield:
```json
{
  "ghu_token": "ghu_...",
  "ghr_token": "ghr_...",
  "token_data": {
    "access_token": "ghu_...",
    "refresh_token": "ghr_...",
    "expires_in": 28800,
    "token_type": "bearer"
  },
  "last_token_refresh": "2024-01-15T10:30:00"
}
```

### 2. Automatic Refresh Process
When a GitHub API call is made:

1. **Token Validation**: The system first validates the current access token
2. **Automatic Refresh**: If the token is invalid/expired, it uses the refresh token to get a new one
3. **Database Update**: The new tokens are automatically saved to the database
4. **Transparent Usage**: The calling code receives a valid token without any changes

### 3. Key Functions

#### `get_valid_github_token(integration_config)`
- Main function that returns a valid access token
- Automatically refreshes if needed
- Updates the config with new tokens

#### `validate_github_token(access_token)`
- Makes a test API call to GitHub to validate the token
- Returns `True` if valid, `False` if invalid

#### `refresh_github_token(refresh_token)`
- Uses GitHub's OAuth API to exchange refresh token for new access token
- Returns the new token data

### 4. Integration Points

The token refresh is integrated into:

1. **Question Answering System** (`question_answering.py`)
   - Used when searching GitHub repositories
   - Automatically refreshes tokens before API calls

2. **Feed Polling Service** (`feed_polling_service.py`)
   - Used for monitoring external repository commits
   - Refreshes tokens before making API calls

3. **CompanyIntegration Model** (`models.py`)
   - Provides `get_valid_github_token()` method
   - Automatically saves updated tokens to database

## Usage Examples

### Basic Usage
```python
from integrations.models import CompanyIntegration

# Get a valid token (automatically refreshes if needed)
integration = CompanyIntegration.objects.get(company=company, tool__slug="github")
valid_token = integration.get_valid_github_token()
```

### Direct Service Usage
```python
from integrations.github.services import get_valid_github_token

# Get valid token from config
config = integration.config
valid_token = get_valid_github_token(config)
```

### Manual Refresh (for testing)
```python
from integrations.github.services import refresh_github_token

# Manually refresh a token
refresh_data = refresh_github_token(refresh_token)
new_access_token = refresh_data["access_token"]
```

## Management Commands

### Refresh All Tokens
```bash
python manage.py refresh_github_tokens
```

### Dry Run (see what would be refreshed)
```bash
python manage.py refresh_github_tokens --dry-run
```

### Force Refresh (even if token appears valid)
```bash
python manage.py refresh_github_tokens --force
```

### Refresh Specific Company
```bash
python manage.py refresh_github_tokens --company-id <company-uuid>
```

## Testing

### Run Test Script
```bash
python test_github_token_refresh.py
```

### Test Token Validation
```python
from integrations.github.services import validate_github_token

# Test if token is valid
is_valid = validate_github_token(access_token)
```

## Error Handling

The system includes comprehensive error handling:

1. **Token Validation Failures**: Logs warnings and falls back to refresh
2. **Refresh Failures**: Logs errors and raises exceptions
3. **Missing Tokens**: Clear error messages for missing access or refresh tokens
4. **Network Issues**: Handles GitHub API timeouts and connection errors

## Logging

The system logs important events:

- `INFO`: Successful token refreshes
- `WARNING`: Token validation failures
- `ERROR`: Refresh failures and network issues
- `DEBUG`: Detailed token validation steps

## Security Considerations

1. **Token Storage**: Tokens are stored in the database (consider encryption for production)
2. **Refresh Token Security**: Refresh tokens have longer lifespans and should be protected
3. **API Rate Limits**: The system respects GitHub's API rate limits
4. **Error Exposure**: Error messages don't expose sensitive token information

## Troubleshooting

### Common Issues

1. **"No refresh token available"**
   - Solution: Re-authenticate the GitHub integration
   - Cause: Refresh token was not saved during initial setup

2. **"Failed to refresh GitHub token"**
   - Solution: Check network connectivity and GitHub API status
   - Cause: Network issues or GitHub API problems

3. **"Token validation failed"**
   - Solution: The system will automatically refresh the token
   - Cause: Token has expired or been revoked

### Debug Steps

1. **Check token validity**:
   ```python
   from integrations.github.services import validate_github_token
   is_valid = validate_github_token(access_token)
   ```

2. **Test manual refresh**:
   ```python
   from integrations.github.services import refresh_github_token
   refresh_data = refresh_github_token(refresh_token)
   ```

3. **Check integration config**:
   ```python
   integration = CompanyIntegration.objects.get(company=company, tool__slug="github")
   print(integration.config)
   ```

## Future Enhancements

1. **Scheduled Refresh**: Automatically refresh tokens before they expire
2. **Token Encryption**: Encrypt tokens in the database
3. **Multiple Token Support**: Support for multiple GitHub accounts per company
4. **Token Rotation**: Implement automatic token rotation for security
5. **Monitoring**: Add metrics and alerts for token refresh failures 