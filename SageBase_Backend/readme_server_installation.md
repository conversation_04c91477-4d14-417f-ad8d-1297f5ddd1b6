# SageBase Deployment Documentation

This document contains the deployment setup and configuration for the SageBase application with SSL certificates, Docker containers, and Nginx reverse proxy.

## Table of Contents

1. [SSL Certificate Setup](#ssl-certificate-setup)
2. [Access URLs](#access-urls)
3. [SSH Configuration](#ssh-configuration)
4. [Project Setup](#project-setup)
5. [Container Deployment](#container-deployment)
6. [Nginx Configuration](#nginx-configuration)
7. [Pending Changes](#pending-changes)

## SSL Certificate Setup

Install Certbot and generate SSL certificates for the domain:

```bash
apt install certbot
certbot certonly --standalone -d <YOUR_DOMAIN_NAME>
```

## Access URLs

All services are accessible via HTTPS:

- **Frontend**: [https://<YOUR_DOMAIN_NAME>](https://<YOUR_DOMAIN_NAME>)
- **Django Admin**: [https://<YOUR_DOMAIN_NAME>:8000/admin](https://<YOUR_DOMAIN_NAME>:8000/admin)
- **<PERSON>ainer**: [https://<YOUR_DOMAIN_NAME>:9443/#!/auth](https://<YOUR_DOMAIN_NAME>:9443/#!/auth)
  - **Credentials**: admin / m8Wq0v1g6VK1W0ImzxeEzNei

## SSH Configuration

Configure SSH keys for GitHub repositories:

```bash
nano /root/.ssh/config
chmod 600 /root/.ssh/config
```

Add the following SSH configuration:

```ssh-config
Host github-backend
HostName github.com
User git
IdentityFile /root/.ssh/sagebase_backend
IdentitiesOnly yes

Host github-frontend
HostName github.com
User git
IdentityFile /root/.ssh/sagebase_frontend
IdentitiesOnly yes
```

## Project Setup

Create project directory and clone repositories:

```bash
mkdir /data
cd /data
git clone git@github-backend:wiswis15/SageBase_BackEnd.git
git clone git@github-frontend:wiswis15/SageBase_FrontEnd-pe.git

cd SageBase_BackEnd
git checkout dev
git config core.fileMode false
cd ../SageBase_FrontEnd-pe
git checkout dev
git config core.fileMode false
```

## Container Deployment

### Docker Installation

```bash
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
```

### Portainer Container

```bash
docker run -d -p 9443:9443 \
  --name portainer \
  --restart=always \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v portainer_data:/data \
  -v /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/fullchain.pem:/certs/cert.pem \
  -v /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/privkey.pem:/certs/key.pem \
  portainer/portainer-ce:sts \
  --sslcert /certs/cert.pem \
  --sslkey /certs/key.pem \
  --host unix:///var/run/docker.sock
```

### Frontend Container

A `start.sh` script has been added to the frontend repository for automated deployment and updates:

**File**: `start.sh`

```bash
npm i --f && npm run dev
cd /data/SageBase_BackEnd && git reset --hard && git pull
docker restart sagebase_backend-web-1
cd /app && git reset --hard && git pull
npm i --f && npm run dev
```

Deploy the frontend container:

```bash
docker rm -f sagebase_frontend
docker run -d \
    -v /data/SageBase_FrontEnd-pe:/app:rw \
    -v /data/SageBase_BackEnd:/data/SageBase_BackEnd:rw \
    --name sagebase_frontend \
    -w /app \
    node:22-alpine \
    /bin/sh -c "./start.sh"
docker network create sagebase-network
```

Connect frontend to the Docker network:

```bash
docker network connect sagebase-network sagebase_frontend
```

**Note**: The `start.sh` script automates the process of:

1. Installing dependencies and starting the development server for nextjs
2. Pulling latest changes from both backend and frontend repositories
3. Restarting the backend container to apply updates

## Nginx Configuration

### Create Nginx Configuration Directory

```bash
mkdir -p /data/nginx/conf.d
```

### Create Nginx Configuration File

/data/nginx/conf.d/default.conf

```nginx
# Block IP-based access on HTTP only
server {
    listen 80 default_server;
    server_name _;  # Matches any unmatched requests (e.g., IP address)

    return 301 https://<YOUR_DOMAIN_NAME>$request_uri;  # Redirect to proper domain
}

# Backend server on port 8000 with SSL
server {
    listen 8000 ssl;
    server_name <YOUR_DOMAIN_NAME>;
    client_max_body_size 100M;

    ssl_certificate /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/privkey.pem;

    # SSL configurations
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Serve static files for backend
    location /static/ {
        alias /data/SageBase_BackEnd/staticfiles/;
        autoindex on;
    }

    # WebSocket endpoint for GitHub notifications
    location /ws/github/notifications/ {
        proxy_pass http://sagebase_backend-web-1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 60s;
    }

    # Proxy Django application
    location / {
        proxy_pass http://sagebase_backend-web-1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Frontend server on main domain - HTTP redirect
server {
    listen 80;
    server_name <YOUR_DOMAIN_NAME>;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

# Frontend server on main domain - HTTPS
server {
    listen 443 ssl;
    server_name <YOUR_DOMAIN_NAME>;

    ssl_certificate /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/privkey.pem;

    # SSL configurations
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # AI API endpoint with extended timeouts
    location /api/agent/ask/ {
        proxy_pass http://sagebase_backend-web-1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Extended timeouts for AI operations (5 minutes)
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        proxy_buffering off;
        proxy_request_buffering off;

        # Error handling for timeouts
        proxy_intercept_errors on;
        error_page 504 = @ai_timeout;
    }

    # Timeout error handler for AI operations
    location @ai_timeout {
        return 504 '{"error": "AI operation timed out. The request is taking longer than expected. Please try again with a simpler query."}';
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
    }

    # Regular API endpoints with standard timeouts
    location /api/ {
        proxy_pass http://sagebase_backend-web-1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # increase timeouts for regular API calls to 5 minutes too
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Proxy Django admin to backend
    location /admin/ {
        proxy_pass http://sagebase_backend-web-1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
    }

    # Proxy NextJS application (everything else)
    location / {
        proxy_pass http://sagebase_frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
    }

    # Handle NextJS hot reload WebSocket (for development)
    location /_next/webpack-hmr {
        proxy_pass http://sagebase_frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

```

### Deploy Nginx Container

```bash
docker run -d \
  --name nginx \
  --restart=always \
  --network sagebase-network \
  -p 80:80 \
  -p 443:443 \
  -p 8000:8000 \
  -v /data/nginx/conf.d:/etc/nginx/conf.d \
  -v /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/fullchain.pem:/etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/fullchain.pem:ro \
  -v /etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/privkey.pem:/etc/letsencrypt/live/<YOUR_DOMAIN_NAME>/privkey.pem:ro \
  -v /data/SageBase_BackEnd/staticfiles:/data/SageBase_BackEnd/staticfiles:ro \
  nginx:alpine

docker network connect bridge nginx
```

## Notes

- All services run with SSL enabled (HTTPS)
- Nginx acts as a reverse proxy for the backend services
- Docker containers are connected via the `sagebase-network` for internal communication
- Static files are served directly by Nginx for better performance
