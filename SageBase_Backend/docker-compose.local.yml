version: '3.8'
services:
  web:
    build: .
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - DEBUG=1
    env_file:
      - .env
    depends_on:
      - redis
    command: sh -c "while true; do sleep 3600; done"  # Keep the container running for local development
    networks:
      - default
      - sagebase-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    networks:
      - default
      - sagebase-network
    restart: unless-stopped

  # Optional: Local PostgreSQL for testing
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: sagebase
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - default
      - sagebase-network
    restart: unless-stopped

  frontend:
    image: node:22-bullseye
    container_name: sagebase_frontend
    volumes:
      - ../SageBase_FrontEnd-pe:/app:rw
    working_dir: /app
    command: /bin/sh -c "./start.local.sh"
    env_file:
      - ../SageBase_FrontEnd-pe/.env.local
    networks:
      - default
      - sagebase-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  default:
  sagebase-network:
    external: true
