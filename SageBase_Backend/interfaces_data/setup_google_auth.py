#!/usr/bin/env python3
"""
Google Drive Authentication Setup

This script helps set up Google Drive OAuth authentication.
It will open a browser window for consent and save the token for future use.

Usage:
======
python interfaces_data/setup_google_auth.py
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')
import django
django.setup()

from interfaces_data.google_drive.drive_client import GoogleDriveClient

def setup_authentication():
    """Set up Google Drive authentication"""
    print("🔐 GOOGLE DRIVE AUTHENTICATION SETUP")
    print("=" * 50)
    print("This will open a browser window for OAuth consent.")
    print("After consent, authentication token will be saved.")
    print("=" * 50)
    
    # Configuration
    workspace = "SageBase_Taha_test"
    credentials_file = str(project_root / "interfaces_data" / "google_drive_credentials.json")
    token_file = f"gdrive_token_{workspace}.json"
    
    print(f"📁 Credentials file: {credentials_file}")
    print(f"💾 Token will be saved to: {token_file}")
    print()
    
    if not os.path.exists(credentials_file):
        print(f"❌ Credentials file not found: {credentials_file}")
        print("Please ensure you have downloaded the OAuth2 credentials file.")
        return False
    
    try:
        print("🚀 Initializing Google Drive client...")
        client = GoogleDriveClient(
            credentials_file=credentials_file,
            token_file=token_file
        )
        
        if client.is_authenticated():
            print("✅ Authentication successful!")
            
            # Get user info to verify
            user_info = client.get_user_info()
            if user_info:
                print(f"👤 Authenticated as: {user_info.get('displayName', 'Unknown')}")
                print(f"📧 Email: {user_info.get('emailAddress', 'Unknown')}")
            
            print(f"💾 Token saved to: {token_file}")
            
            # Test basic functionality
            print("\n🧪 Testing basic functionality...")
            
            try:
                # Test folder access with the configured folder ID
                folder_id = "1Xvq0PMCp6BWPp30g-7fbibMm0Cm2KBNL" #CHANGE THIS TO YOUR FOLDER ID
                print(f"📁 Testing access to folder: {folder_id}")
                
                files = list(client.list_files(folder_id=folder_id, page_size=5))
                print(f"✅ Found {len(files)} files in folder")
                
                if files:
                    print("📄 Sample files:")
                    for i, file in enumerate(files[:3]):
                        print(f"  {i+1}. {file.get('name', 'Unknown')} ({file.get('mimeType', 'Unknown type')})")
                
            except Exception as e:
                print(f"⚠️  Warning: Could not access folder: {e}")
                print("This might be due to folder permissions.")
            
            return True
        else:
            print("❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during authentication setup: {e}")
        return False

def main():
    """Main setup function"""
    print("🔐 Google Drive Authentication Setup")
    print()
    
    success = setup_authentication()
    
    if success:
        print("\n🎉 SUCCESS! Google Drive authentication is configured.")
        print("\n🚀 Next steps:")
        print("1. Run full test: python interfaces_data/test_google.py")
        print("2. Or use the interface in your code:")
        print("   from interfaces_data.google_drive.google_drive_interface import GoogleDriveInterface")
        print("   interface = GoogleDriveInterface(workspace='test', folder_id='...')")
    else:
        print("\n❌ Authentication setup failed.")
        print("Please check your credentials file and try again.")
        print("\n💡 Troubleshooting:")
        print("1. Ensure Google Drive API is enabled in Google Cloud Console")
        print("2. Check that OAuth2 credentials are valid")
        print("3. Make sure you have access to the target folder")

if __name__ == "__main__":
    main()