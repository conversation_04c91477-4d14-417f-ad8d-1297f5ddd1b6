import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from threading import Lock
from collections import defaultdict
import json
import uuid

from .base_interface import NotificationEvent, NotificationLevel, OperationType
from vectordb.models.document import DataSource

logger = logging.getLogger(__name__)


class NotificationManager:
    """
    Manages notifications for document changes across all integrations.
    
    This class provides centralized notification handling, allowing
    different systems to subscribe to document change events and
    receive notifications based on their interests.
    """
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self._event_history: List[NotificationEvent] = []
        self._lock = Lock()
        self._max_history_size = 1000
        
        # Subscription filters
        self._source_filters: Dict[str, List[DataSource]] = {}
        self._workspace_filters: Dict[str, List[str]] = {}
        self._operation_filters: Dict[str, List[OperationType]] = {}
        self._level_filters: Dict[str, List[NotificationLevel]] = {}
    
    def subscribe(self, 
                 callback: Callable[[NotificationEvent], None],
                 subscriber_id: Optional[str] = None,
                 sources: Optional[List[DataSource]] = None,
                 workspaces: Optional[List[str]] = None,
                 operations: Optional[List[OperationType]] = None,
                 levels: Optional[List[NotificationLevel]] = None) -> str:
        """
        Subscribe to document change notifications
        
        Args:
            callback: Function to call when events occur
            subscriber_id: Unique identifier for the subscriber
            sources: List of data sources to monitor (None for all)
            workspaces: List of workspaces to monitor (None for all)
            operations: List of operations to monitor (None for all)
            levels: List of notification levels to monitor (None for all)
            
        Returns:
            Subscriber ID
        """
        if not subscriber_id:
            subscriber_id = str(uuid.uuid4())
        
        with self._lock:
            # Add callback
            self._subscribers[subscriber_id].append(callback)
            
            # Store filters
            if sources:
                self._source_filters[subscriber_id] = sources
            if workspaces:
                self._workspace_filters[subscriber_id] = workspaces
            if operations:
                self._operation_filters[subscriber_id] = operations
            if levels:
                self._level_filters[subscriber_id] = levels
        
        logger.info(f"Subscriber {subscriber_id} registered with filters: sources={sources}, workspaces={workspaces}, operations={operations}, levels={levels}")
        return subscriber_id
    
    def unsubscribe(self, subscriber_id: str, callback: Optional[Callable] = None):
        """
        Unsubscribe from notifications
        
        Args:
            subscriber_id: Subscriber ID
            callback: Specific callback to remove (None to remove all)
        """
        with self._lock:
            if subscriber_id in self._subscribers:
                if callback:
                    # Remove specific callback
                    if callback in self._subscribers[subscriber_id]:
                        self._subscribers[subscriber_id].remove(callback)
                    
                    # Clean up if no callbacks left
                    if not self._subscribers[subscriber_id]:
                        del self._subscribers[subscriber_id]
                        self._cleanup_filters(subscriber_id)
                else:
                    # Remove all callbacks for this subscriber
                    del self._subscribers[subscriber_id]
                    self._cleanup_filters(subscriber_id)
        
        logger.info(f"Subscriber {subscriber_id} unsubscribed")
    
    def _cleanup_filters(self, subscriber_id: str):
        """Clean up filters for a subscriber"""
        for filter_dict in [self._source_filters, self._workspace_filters, 
                           self._operation_filters, self._level_filters]:
            if subscriber_id in filter_dict:
                del filter_dict[subscriber_id]
    
    def notify(self, event: NotificationEvent):
        """
        Send notification to all matching subscribers
        
        Args:
            event: Notification event to send
        """
        with self._lock:
            # Add to event history
            self._event_history.append(event)
            
            # Trim history if too large
            if len(self._event_history) > self._max_history_size:
                self._event_history = self._event_history[-self._max_history_size:]
            
            # Send to matching subscribers
            for subscriber_id, callbacks in self._subscribers.items():
                if self._matches_filters(event, subscriber_id):
                    for callback in callbacks:
                        try:
                            callback(event)
                        except Exception as e:
                            logger.error(f"Callback failed for subscriber {subscriber_id}: {e}")
        
        logger.debug(f"Notification sent: {event.level.value} - {event.message}")
    
    def _matches_filters(self, event: NotificationEvent, subscriber_id: str) -> bool:
        """Check if event matches subscriber filters"""
        
        # Check source filter
        if subscriber_id in self._source_filters:
            if event.operation.source not in self._source_filters[subscriber_id]:
                return False
        
        # Check workspace filter
        if subscriber_id in self._workspace_filters:
            if event.operation.workspace not in self._workspace_filters[subscriber_id]:
                return False
        
        # Check operation filter
        if subscriber_id in self._operation_filters:
            if event.operation.operation_type not in self._operation_filters[subscriber_id]:
                return False
        
        # Check level filter
        if subscriber_id in self._level_filters:
            if event.level not in self._level_filters[subscriber_id]:
                return False
        
        return True
    
    def get_event_history(self, 
                         limit: int = 100,
                         sources: Optional[List[DataSource]] = None,
                         workspaces: Optional[List[str]] = None,
                         operations: Optional[List[OperationType]] = None,
                         levels: Optional[List[NotificationLevel]] = None,
                         since: Optional[datetime] = None) -> List[NotificationEvent]:
        """
        Get notification event history with optional filters
        
        Args:
            limit: Maximum number of events to return
            sources: Filter by data sources
            workspaces: Filter by workspaces
            operations: Filter by operation types
            levels: Filter by notification levels
            since: Filter by timestamp (events after this time)
            
        Returns:
            List of notification events
        """
        with self._lock:
            # Apply filters
            filtered_events = []
            
            for event in reversed(self._event_history):  # Most recent first
                # Time filter
                if since and event.timestamp < since:
                    continue
                
                # Source filter
                if sources and event.operation.source not in sources:
                    continue
                
                # Workspace filter
                if workspaces and event.operation.workspace not in workspaces:
                    continue
                
                # Operation filter
                if operations and event.operation.operation_type not in operations:
                    continue
                
                # Level filter
                if levels and event.level not in levels:
                    continue
                
                filtered_events.append(event)
                
                # Limit check
                if len(filtered_events) >= limit:
                    break
            
            return filtered_events
    
    def get_stats(self) -> Dict[str, Any]:
        """Get notification manager statistics"""
        with self._lock:
            return {
                "total_subscribers": len(self._subscribers),
                "total_callbacks": sum(len(callbacks) for callbacks in self._subscribers.values()),
                "event_history_size": len(self._event_history),
                "max_history_size": self._max_history_size,
                "subscribers": {
                    subscriber_id: {
                        "callback_count": len(callbacks),
                        "source_filters": self._source_filters.get(subscriber_id, []),
                        "workspace_filters": self._workspace_filters.get(subscriber_id, []),
                        "operation_filters": self._operation_filters.get(subscriber_id, []),
                        "level_filters": self._level_filters.get(subscriber_id, [])
                    }
                    for subscriber_id, callbacks in self._subscribers.items()
                }
            }
    
    def clear_history(self):
        """Clear event history"""
        with self._lock:
            self._event_history.clear()
        
        logger.info("Event history cleared")
    
    def export_history(self, format: str = "json") -> str:
        """
        Export event history in specified format
        
        Args:
            format: Export format ("json" or "csv")
            
        Returns:
            Exported data as string
        """
        with self._lock:
            if format.lower() == "json":
                return self._export_json()
            elif format.lower() == "csv":
                return self._export_csv()
            else:
                raise ValueError(f"Unsupported export format: {format}")
    
    def _export_json(self) -> str:
        """Export history as JSON"""
        events_data = []
        
        for event in self._event_history:
            events_data.append({
                "event_id": event.event_id,
                "timestamp": event.timestamp.isoformat(),
                "level": event.level.value,
                "message": event.message,
                "operation": {
                    "type": event.operation.operation_type.value,
                    "source": event.operation.source.value,
                    "document_id": event.operation.document_id,
                    "workspace": event.operation.workspace,
                    "timestamp": event.operation.timestamp.isoformat()
                },
                "details": event.details
            })
        
        return json.dumps({
            "export_timestamp": datetime.now().isoformat(),
            "total_events": len(events_data),
            "events": events_data
        }, indent=2)
    
    def _export_csv(self) -> str:
        """Export history as CSV"""
        import csv
        from io import StringIO
        
        output = StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "event_id", "timestamp", "level", "message", "operation_type",
            "source", "document_id", "workspace", "operation_timestamp"
        ])
        
        # Write events
        for event in self._event_history:
            writer.writerow([
                event.event_id,
                event.timestamp.isoformat(),
                event.level.value,
                event.message,
                event.operation.operation_type.value,
                event.operation.source.value,
                event.operation.document_id,
                event.operation.workspace,
                event.operation.timestamp.isoformat()
            ])
        
        return output.getvalue()


# Global notification manager instance
_notification_manager = None


def get_notification_manager() -> NotificationManager:
    """Get global notification manager instance"""
    global _notification_manager
    
    if _notification_manager is None:
        _notification_manager = NotificationManager()
    
    return _notification_manager


def create_console_logger_callback(min_level: NotificationLevel = NotificationLevel.INFO) -> Callable:
    """
    Create a callback that logs notifications to console
    
    Args:
        min_level: Minimum notification level to log
        
    Returns:
        Callback function
    """
    level_order = {
        NotificationLevel.INFO: 0,
        NotificationLevel.SUCCESS: 1,
        NotificationLevel.WARNING: 2,
        NotificationLevel.ERROR: 3
    }
    
    min_level_value = level_order.get(min_level, 0)
    
    def log_callback(event: NotificationEvent):
        if level_order.get(event.level, 0) >= min_level_value:
            timestamp = event.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] {event.level.value.upper()}: {event.message} (Source: {event.operation.source.value}, Doc: {event.operation.document_id})")
    
    return log_callback


def create_file_logger_callback(filepath: str, min_level: NotificationLevel = NotificationLevel.INFO) -> Callable:
    """
    Create a callback that logs notifications to a file
    
    Args:
        filepath: Path to log file
        min_level: Minimum notification level to log
        
    Returns:
        Callback function
    """
    level_order = {
        NotificationLevel.INFO: 0,
        NotificationLevel.SUCCESS: 1,
        NotificationLevel.WARNING: 2,
        NotificationLevel.ERROR: 3
    }
    
    min_level_value = level_order.get(min_level, 0)
    
    def log_callback(event: NotificationEvent):
        if level_order.get(event.level, 0) >= min_level_value:
            timestamp = event.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {event.level.value.upper()}: {event.message} (Source: {event.operation.source.value}, Doc: {event.operation.document_id})\n"
            
            try:
                with open(filepath, 'a', encoding='utf-8') as f:
                    f.write(log_entry)
            except Exception as e:
                logger.error(f"Failed to write to log file {filepath}: {e}")
    
    return log_callback