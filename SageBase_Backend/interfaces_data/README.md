# Common Document Interface System

This package provides a unified interface for document management operations across different data sources (Google Drive, Notion, Confluence, GitHub, Slack, etc.). It includes a comprehensive notification system for tracking document changes and a vector database implementation for efficient document storage and retrieval.

## Features

- **Unified Interface**: Common API for all document operations regardless of source
- **Notification System**: Real-time notifications for document changes with filtering
- **Vector Database Integration**: Efficient storage and semantic search using ChromaDB
- **Bulk Operations**: Optimized batch processing for large document sets
- **Error Handling**: Comprehensive error reporting and recovery
- **Testing**: Complete test suite with examples

## Architecture

### Core Components

1. **BaseDocumentInterface** (`base_interface.py`)
   - Abstract base class defining the common interface
   - All integrations must implement this interface
   - Provides metadata validation and notification framework

2. **VectorDBInterface** (`vector_db_interface.py`)
   - Concrete implementation using the existing ChromaDB vectordb system via `vectordb.interfaces`
   - Provides semantic search and document storage capabilities  
   - Integrates with the notification system
   - Uses the official vectordb interface functions for all operations

3. **NotificationManager** (`notification_manager.py`)
   - Centralized notification handling system
   - Supports filtering by source, workspace, operation type, and level
   - Provides event history and export capabilities

## Quick Start

### Basic Usage

```python
from interfaces_data.vector_db_interface import VectorDBInterface
from interfaces_data.notification_manager import get_notification_manager, create_console_logger_callback
from vectordb.models.document import DataSource

# Create an interface for Confluence documents
confluence_interface = VectorDBInterface(
    source=DataSource.CONFLUENCE,
    workspace="engineering_team"
)

# Set up console notifications
console_callback = create_console_logger_callback()
confluence_interface.register_notification_callback(console_callback)

# Add a document
document_id = confluence_interface.add_document(
    content="API documentation content here...",
    metadata={
        "title": "API Guide",
        "author": "<EMAIL>",
        "tags": ["api", "documentation"],
        "url": "https://confluence.company.com/api-guide"
    }
)

# Search for documents
from vectordb.models.document import SearchQuery

results = confluence_interface.search_documents(SearchQuery(
    query="API documentation",
    workspace="engineering_team",
    limit=10
))

# Update a document
confluence_interface.update_document(
    document_id=document_id,
    content="Updated API documentation...",
    metadata={"title": "API Guide (Updated)"}
)

# Delete a document
confluence_interface.delete_document(document_id)
```

### Bulk Operations

```python
# Bulk add documents
documents = [
    {
        "content": "Document 1 content",
        "metadata": {"title": "Doc 1", "author": "<EMAIL>"}
    },
    {
        "content": "Document 2 content", 
        "metadata": {"title": "Doc 2", "author": "<EMAIL>"}
    }
]

result = confluence_interface.bulk_add_documents(documents)
print(f"Success rate: {result.success_rate:.1f}%")

# Bulk update documents
updates = [
    {
        "id": result.document_ids[0],
        "content": "Updated content",
        "metadata": {"title": "Updated Doc 1"}
    }
]

update_result = confluence_interface.bulk_update_documents(updates)

# Bulk delete documents
delete_result = confluence_interface.bulk_delete_documents(result.document_ids)
```

### Notification System

```python
from interfaces_data.notification_manager import get_notification_manager
from interfaces_data.base_interface import NotificationLevel, OperationType

# Get global notification manager
notification_manager = get_notification_manager()

# Custom callback function
def my_callback(event):
    print(f"Document {event.operation.document_id} was {event.operation.operation_type.value}")

# Subscribe with filters
subscriber_id = notification_manager.subscribe(
    callback=my_callback,
    sources=[DataSource.CONFLUENCE],
    operations=[OperationType.CREATE, OperationType.UPDATE],
    levels=[NotificationLevel.SUCCESS]
)

# Register with interface to receive notifications
confluence_interface.register_notification_callback(
    lambda event: notification_manager.notify(event)
)

# Get notification history
history = notification_manager.get_event_history(limit=50)

# Export history
json_export = notification_manager.export_history("json")
csv_export = notification_manager.export_history("csv")

# Unsubscribe when done
notification_manager.unsubscribe(subscriber_id)
```

## Creating Custom Integrations

To create a new integration (e.g., for SharePoint, Dropbox, etc.), implement the `BaseDocumentInterface`:

```python
from interfaces_data.base_interface import BaseDocumentInterface, BulkOperationResult
from vectordb.models.document import DataSource

class SharePointInterface(BaseDocumentInterface):
    def __init__(self, workspace: str = None):
        super().__init__(DataSource.LOCAL, workspace)  # Use OTHER or add new DataSource
        # Initialize SharePoint client here
        
    def add_document(self, content: str, metadata: Dict[str, Any], document_id: str = None) -> str:
        # Implement SharePoint-specific document addition
        validated_metadata = self.validate_metadata(metadata)
        
        # Your SharePoint API calls here
        # ...
        
        # Send notification
        operation = DocumentOperation(
            operation_type=OperationType.CREATE,
            source=self.source,
            document_id=document_id,
            workspace=self.workspace,
            metadata=validated_metadata
        )
        
        self._notify_change(
            operation=operation,
            level=NotificationLevel.SUCCESS,
            message=f"Document '{validated_metadata['title']}' added to SharePoint"
        )
        
        return document_id
    
    # Implement other required methods...
    def update_document(self, document_id: str, content: str = None, metadata: Dict[str, Any] = None) -> bool:
        # Implementation here
        pass
    
    def delete_document(self, document_id: str) -> bool:
        # Implementation here  
        pass
    
    # ... etc
```

## Data Models

### DocumentOperation
Represents a document operation with metadata:
- `operation_type`: CREATE, UPDATE, DELETE, etc.
- `source`: Data source (CONFLUENCE, GOOGLE_DRIVE, etc.)
- `document_id`: Unique document identifier
- `workspace`: Team/workspace identifier
- `metadata`: Document metadata
- `timestamp`: When the operation occurred

### NotificationEvent
Represents a notification event:
- `event_id`: Unique event identifier
- `operation`: Associated DocumentOperation
- `level`: INFO, SUCCESS, WARNING, ERROR
- `message`: Human-readable message
- `details`: Additional event details

### BulkOperationResult
Result of bulk operations:
- `total_processed`: Total number of documents processed
- `successful`: Number of successful operations
- `failed`: Number of failed operations
- `errors`: List of error messages
- `document_ids`: List of document IDs
- `success_rate`: Calculated success percentage

## Data Sources

The system supports the following data sources (defined in `vectordb.models.document.DataSource`):
- `CONFLUENCE`
- `GOOGLE_DRIVE`
- `NOTION`
- `GITHUB`
- `SLACK`
- `JIRA`
- `OTHER`

## Error Handling

The system uses `DocumentOperationError` for operation failures:

```python
try:
    document_id = interface.add_document(content, metadata)
except DocumentOperationError as e:
    print(f"Operation failed: {e.message}")
    print(f"Source: {e.source}")
    print(f"Details: {e.details}")
```

## Testing

Run the complete test suite:

```bash
python -m pytest interfaces_data/tests.py -v
```

Run specific test classes:

```bash
python -m pytest interfaces_data/tests.py::TestVectorDBInterface -v
python -m pytest interfaces_data/tests.py::TestNotificationManager -v
```

## Examples

See `examples.py` for comprehensive usage examples including:
- Basic document operations
- Bulk operations
- Notification system usage
- Custom integration creation

Run the examples:

```bash
python -c "from interfaces_data.examples import main; main()"
```

## Configuration

The VectorDBInterface uses the existing vectordb configuration. Make sure the following environment variables are set:

- `CHROMA_PERSIST_DIR`: ChromaDB storage directory
- `CHROMA_HOST` / `CHROMA_PORT`: For remote ChromaDB (optional)
- `CHROMA_EMBEDDING_MODEL`: SentenceTransformer model name

## Integration with Existing Systems

This interface system is designed to work alongside the existing:
- **Vector Database System** (`vectordb/`) - Uses `vectordb.interfaces` for all operations
- **GitHub Integration** (`integrations/github/`) - Can be adapted to use this interface
- **WebSocket Notifications** - Can integrate with the notification system

The `VectorDBInterface` specifically uses the official `vectordb.interfaces` module to ensure proper encapsulation and adherence to the vectordb's public API.

## Future Enhancements

Potential future improvements:
- **Async Operations**: Support for asynchronous document operations
- **Caching Layer**: Document-level caching for performance
- **Webhook Integration**: Automatic sync based on external webhooks
- **Access Control**: Fine-grained permission management
- **Metrics**: Detailed operation metrics and monitoring

## License

This code is part of the SageBase Backend system and follows the same licensing terms.