import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid

from .base_interface import (
    BaseDocumentInterface, 
    DocumentOperation, 
    OperationType, 
    NotificationLevel,
    BulkOperationResult,
    DocumentOperationError
)
from vectordb.models.document import DataSource, SearchResult, SearchQuery
import vectordb.interfaces as vectordb_interface

logger = logging.getLogger(__name__)


class VectorDBInterface(BaseDocumentInterface):
    """
    VectorDB implementation of the BaseDocumentInterface.
    
    This class provides a concrete implementation using the existing
    ChromaDB vectordb system for document storage and retrieval.
    """
    
    def __init__(self, source: DataSource, workspace: Optional[str] = None):
        super().__init__(source, workspace)
    
    def add_document(self, 
                    collection_name: str,
                    content: str, 
                    metadata: Dict[str, Any],
                    document_id: Optional[str] = None) -> str:
        """Add a single document to the vectordb"""
        try:
            # Validate and normalize metadata
            normalized_metadata = self.validate_metadata(metadata)
            
            # Generate document ID if not provided
            if not document_id:
                document_id = str(uuid.uuid4())
            
            # Add source_id to metadata
            normalized_metadata['source_id'] = document_id
            
            # Add document through vectordb interface
            added_ids = vectordb_interface.add_document(
                collection_name=collection_name,
                content=content,
                source=self.source,
                source_id=document_id,
                title=normalized_metadata['title'],
                author=normalized_metadata.get('author'),
                tags=normalized_metadata.get('tags', []),
                url=normalized_metadata.get('url'),
                content_type=normalized_metadata.get('content_type'),
                created_at=normalized_metadata.get('created_at'),
                updated_at=normalized_metadata.get('updated_at'),
                parent_id=normalized_metadata.get('parent_id'),
                permissions=normalized_metadata.get('permissions', []),
                custom_fields=normalized_metadata.get('custom_fields', {})
            )
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.CREATE,
                source=self.source,
                document_id=document_id,
                workspace=self.workspace,
                metadata=normalized_metadata,
                content=content[:100] + "..." if len(content) > 100 else content
            )
            
            # Send notification
            self._notify_change(
                operation=operation,
                level=NotificationLevel.SUCCESS,
                message=f"Document '{normalized_metadata['title']}' added successfully",
                details={"chunks_created": len(added_ids)}
            )
            
            logger.info(f"Added document {document_id} to {self.source.value}")
            return document_id
            
        except Exception as e:
            error_msg = f"Failed to add document: {str(e)}"
            logger.error(error_msg)
            
            # Send error notification
            operation = DocumentOperation(
                operation_type=OperationType.CREATE,
                source=self.source,
                document_id=document_id or "unknown",
                workspace=self.workspace,
                metadata=metadata
            )
            
            self._notify_change(
                operation=operation,
                level=NotificationLevel.ERROR,
                message=error_msg,
                details={"error": str(e)}
            )
            
            raise DocumentOperationError(
                message=error_msg,
                operation="add_document",
                source=self.source,
                details={"original_error": str(e)}
            )
    
    def update_document(self, 
                       collection_name: str,
                       document_id: str,
                       content: Optional[str] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Update an existing document"""
        try:
            # Update document through vectordb interface
            if metadata:
                normalized_metadata = self.validate_metadata(metadata)
                normalized_metadata['updated_at'] = datetime.now()
            else:
                normalized_metadata = {}
            
            success = vectordb_interface.update_document(
                collection_name=collection_name,
                source=self.source,
                source_id=document_id,
                content=content,
                title=normalized_metadata.get('title'),
                author=normalized_metadata.get('author'),
                workspace=normalized_metadata.get('workspace'),
                tags=normalized_metadata.get('tags'),
                url=normalized_metadata.get('url'),
                content_type=normalized_metadata.get('content_type'),
                **normalized_metadata.get('custom_fields', {})
            )
            
            if success:
                # Create operation record
                operation = DocumentOperation(
                    operation_type=OperationType.UPDATE,
                    source=self.source,
                    document_id=document_id,
                    workspace=self.workspace,
                    metadata=metadata,
                    content=content[:100] + "..." if content and len(content) > 100 else content
                )
                
                # Send notification
                self._notify_change(
                    operation=operation,
                    level=NotificationLevel.SUCCESS,
                    message=f"Document {document_id} updated successfully"
                )
                
                logger.info(f"Updated document {document_id} in {self.source.value}")
            
            return success
            
        except Exception as e:
            error_msg = f"Failed to update document {document_id}: {str(e)}"
            logger.error(error_msg)
            
            # Send error notification
            operation = DocumentOperation(
                operation_type=OperationType.UPDATE,
                source=self.source,
                document_id=document_id,
                workspace=self.workspace,
                metadata=metadata
            )
            
            self._notify_change(
                operation=operation,
                level=NotificationLevel.ERROR,
                message=error_msg,
                details={"error": str(e)}
            )
            
            raise DocumentOperationError(
                message=error_msg,
                operation="update_document",
                source=self.source,
                details={"original_error": str(e)}
            )
    
    def delete_document(self, document_id: str) -> bool:
        """Delete a document from the vectordb"""
        try:
            # Get document info for notification (optional since it might not exist)
            existing_doc = self.get_document(document_id)
            
            # Delete document through vectordb interface
            success = vectordb_interface.delete_document(
                source=self.source,
                source_id=document_id,
                collection_name=self.workspace or 'default'
            )
            
            if success:
                # Create operation record
                operation = DocumentOperation(
                    operation_type=OperationType.DELETE,
                    source=self.source,
                    document_id=document_id,
                    workspace=self.workspace
                )
                
                # Send notification
                title = existing_doc.get('title', 'Unknown') if existing_doc else 'Unknown'
                self._notify_change(
                    operation=operation,
                    level=NotificationLevel.SUCCESS,
                    message=f"Document '{title}' deleted successfully"
                )
                
                logger.info(f"Deleted document {document_id} from {self.source.value}")
            
            return success
            
        except Exception as e:
            error_msg = f"Failed to delete document {document_id}: {str(e)}"
            logger.error(error_msg)
            
            # Send error notification
            operation = DocumentOperation(
                operation_type=OperationType.DELETE,
                source=self.source,
                document_id=document_id,
                workspace=self.workspace
            )
            
            self._notify_change(
                operation=operation,
                level=NotificationLevel.ERROR,
                message=error_msg,
                details={"error": str(e)}
            )
            
            raise DocumentOperationError(
                message=error_msg,
                operation="delete_document",
                source=self.source,
                details={"original_error": str(e)}
            )
    
    def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a document by ID"""
        try:
            document = vectordb_interface.get_document(
                source=self.source,
                source_id=document_id,
                collection_name=self.workspace or 'default'
            )
            
            if document:
                return {
                    "id": document.id,
                    "content": document.content,
                    "metadata": document.metadata.to_dict(),
                    "chunk_index": document.chunk_index,
                    "total_chunks": document.total_chunks
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get document {document_id}: {e}")
            return None
    
    def search_documents(self, query: SearchQuery) -> List[SearchResult]:
        """Search for documents"""
        try:
            # Ensure the query is set to search in this source
            sources = [self.source] if not query.sources or self.source not in query.sources else query.sources
            
            # Add workspace filter if specified
            workspace = self.workspace if self.workspace and not query.workspace else query.workspace
            
            # Perform search through vectordb interface
            results = vectordb_interface.search(
                query=query.query,
                sources=sources,
                limit=query.limit,
                collection_name=workspace or self.workspace or 'default',
                content_type=query.content_type,
                tags=query.tags,
                author=query.author,
                similarity_threshold=query.similarity_threshold,
                search_mode=query.search_mode,
                textual_boost=query.textual_boost,
                semantic_boost=query.semantic_boost
            )
            
            logger.info(f"Search for '{query.query}' in {self.source.value} returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Search failed for {self.source.value}: {e}")
            return []
    
    def bulk_add_documents(self, documents: List[Dict[str, Any]]) -> BulkOperationResult:
        """Add multiple documents in a single operation"""
        try:
            # Process documents individually using vectordb interface
            successful = 0
            failed = 0
            errors = []
            document_ids = []
            
            for doc in documents:
                try:
                    # Validate metadata
                    metadata = doc.get('metadata', {})
                    normalized_metadata = self.validate_metadata(metadata)
                    
                    # Generate document ID if not provided
                    doc_id = doc.get('id', str(uuid.uuid4()))
                    
                    # Add document through vectordb interface
                    added_ids = vectordb_interface.add_document(
                        content=doc['content'],
                        source=self.source,
                        source_id=doc_id,
                        title=normalized_metadata['title'],
                        collection_name=normalized_metadata.get('workspace') or self.workspace or 'default',
                        author=normalized_metadata.get('author'),
                        tags=normalized_metadata.get('tags', []),
                        url=normalized_metadata.get('url'),
                        content_type=normalized_metadata.get('content_type'),
                        **normalized_metadata.get('custom_fields', {})
                    )
                    
                    if added_ids:
                        successful += 1
                        document_ids.append(doc_id)
                    else:
                        failed += 1
                        errors.append(f"Failed to add document {doc_id}")
                        
                except Exception as e:
                    failed += 1
                    errors.append(f"Error adding document {doc.get('id', 'unknown')}: {str(e)}")
            
            # Create result object
            bulk_result = BulkOperationResult(
                total_processed=len(documents),
                successful=successful,
                failed=failed,
                errors=errors,
                document_ids=document_ids,
                operation_type=OperationType.BULK_CREATE,
                source=self.source,
                workspace=self.workspace
            )
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.BULK_CREATE,
                source=self.source,
                document_id=f"bulk_operation_{len(documents)}_docs",
                workspace=self.workspace,
                metadata={"document_count": len(documents)}
            )
            
            # Send notification
            if bulk_result.successful > 0:
                self._notify_change(
                    operation=operation,
                    level=NotificationLevel.SUCCESS,
                    message=f"Bulk import completed: {bulk_result.successful}/{bulk_result.total_processed} documents added",
                    details={"result": bulk_result.__dict__}
                )
            
            if bulk_result.failed > 0:
                self._notify_change(
                    operation=operation,
                    level=NotificationLevel.WARNING,
                    message=f"Bulk import partially failed: {bulk_result.failed} documents failed",
                    details={"errors": bulk_result.errors}
                )
            
            logger.info(f"Bulk import to {self.source.value}: {bulk_result.successful}/{bulk_result.total_processed} successful")
            return bulk_result
            
        except Exception as e:
            error_msg = f"Bulk import failed: {str(e)}"
            logger.error(error_msg)
            
            # Create failed result
            bulk_result = BulkOperationResult(
                total_processed=len(documents),
                successful=0,
                failed=len(documents),
                errors=[error_msg],
                document_ids=[],
                operation_type=OperationType.BULK_CREATE,
                source=self.source,
                workspace=self.workspace
            )
            
            # Send error notification
            operation = DocumentOperation(
                operation_type=OperationType.BULK_CREATE,
                source=self.source,
                document_id=f"bulk_operation_{len(documents)}_docs",
                workspace=self.workspace
            )
            
            self._notify_change(
                operation=operation,
                level=NotificationLevel.ERROR,
                message=error_msg,
                details={"error": str(e)}
            )
            
            return bulk_result
    
    def bulk_update_documents(self, updates: List[Dict[str, Any]]) -> BulkOperationResult:
        """Update multiple documents in a single operation"""
        successful = 0
        failed = 0
        errors = []
        document_ids = []
        
        for update in updates:
            try:
                doc_id = update['id']
                content = update.get('content')
                metadata = update.get('metadata')
                
                success = self.update_document(doc_id, content, metadata)
                if success:
                    successful += 1
                    document_ids.append(doc_id)
                else:
                    failed += 1
                    errors.append(f"Failed to update document {doc_id}")
                    
            except Exception as e:
                failed += 1
                errors.append(f"Error updating document {update.get('id', 'unknown')}: {str(e)}")
        
        # Create result object
        bulk_result = BulkOperationResult(
            total_processed=len(updates),
            successful=successful,
            failed=failed,
            errors=errors,
            document_ids=document_ids,
            operation_type=OperationType.BULK_UPDATE,
            source=self.source,
            workspace=self.workspace
        )
        
        # Send notification
        operation = DocumentOperation(
            operation_type=OperationType.BULK_UPDATE,
            source=self.source,
            document_id=f"bulk_update_{len(updates)}_docs",
            workspace=self.workspace,
            metadata={"document_count": len(updates)}
        )
        
        if successful > 0:
            self._notify_change(
                operation=operation,
                level=NotificationLevel.SUCCESS,
                message=f"Bulk update completed: {successful}/{len(updates)} documents updated",
                details={"result": bulk_result.__dict__}
            )
        
        if failed > 0:
            self._notify_change(
                operation=operation,
                level=NotificationLevel.WARNING,
                message=f"Bulk update partially failed: {failed} documents failed",
                details={"errors": errors}
            )
        
        logger.info(f"Bulk update in {self.source.value}: {successful}/{len(updates)} successful")
        return bulk_result
    
    def bulk_delete_documents(self, document_ids: List[str]) -> BulkOperationResult:
        """Delete multiple documents in a single operation"""
        successful = 0
        failed = 0
        errors = []
        successful_ids = []
        
        for doc_id in document_ids:
            try:
                success = self.delete_document(doc_id)
                if success:
                    successful += 1
                    successful_ids.append(doc_id)
                else:
                    failed += 1
                    errors.append(f"Failed to delete document {doc_id}")
                    
            except Exception as e:
                failed += 1
                errors.append(f"Error deleting document {doc_id}: {str(e)}")
        
        # Create result object
        bulk_result = BulkOperationResult(
            total_processed=len(document_ids),
            successful=successful,
            failed=failed,
            errors=errors,
            document_ids=successful_ids,
            operation_type=OperationType.BULK_DELETE,
            source=self.source,
            workspace=self.workspace
        )
        
        # Send notification
        operation = DocumentOperation(
            operation_type=OperationType.BULK_DELETE,
            source=self.source,
            document_id=f"bulk_delete_{len(document_ids)}_docs",
            workspace=self.workspace,
            metadata={"document_count": len(document_ids)}
        )
        
        if successful > 0:
            self._notify_change(
                operation=operation,
                level=NotificationLevel.SUCCESS,
                message=f"Bulk delete completed: {successful}/{len(document_ids)} documents deleted",
                details={"result": bulk_result.__dict__}
            )
        
        if failed > 0:
            self._notify_change(
                operation=operation,
                level=NotificationLevel.WARNING,
                message=f"Bulk delete partially failed: {failed} documents failed",
                details={"errors": errors}
            )
        
        logger.info(f"Bulk delete in {self.source.value}: {successful}/{len(document_ids)} successful")
        return bulk_result
    
    def get_workspace_stats(self) -> Dict[str, Any]:
        """Get statistics for the workspace"""
        try:
            stats = vectordb_interface.get_stats(collection_name=self.workspace or 'default')
            
            # Extract stats for this specific source
            source_stats = stats.get('sources', {}).get(self.source.value, {})
            
            if self.workspace:
                # Add workspace information
                source_stats['workspace'] = self.workspace
            
            # Add source information
            source_stats['source'] = self.source.value
            
            return source_stats
            
        except Exception as e:
            logger.error(f"Failed to get workspace stats for {self.source.value}: {e}")
            return {"error": str(e)}
    
    def sync_workspace(self, force: bool = False) -> Dict[str, Any]:
        """
        Synchronize the workspace with the external source
        
        Note: This is a placeholder implementation. Each integration
        (Google Drive, Notion, etc.) should override this method
        to implement their specific synchronization logic.
        """
        return {
            "status": "not_implemented",
            "message": "Sync functionality should be implemented by specific integrations",
            "source": self.source.value,
            "workspace": self.workspace,
            "timestamp": datetime.now().isoformat()
        }