import logging
import io
import json
from typing import List, Dict, Any, Optional, Iterator
from datetime import datetime, timezone
from pathlib import Path

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseDownload

logger = logging.getLogger(__name__)

# Google Drive API scopes
SCOPES = [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/drive.metadata'
]

# Supported file types for content extraction
SUPPORTED_MIME_TYPES = {
    # Google Workspace files
    'application/vnd.google-apps.document': 'text/plain',  # Google Docs
    'application/vnd.google-apps.spreadsheet': 'text/csv',  # Google Sheets
    'application/vnd.google-apps.presentation': 'text/plain',  # Google Slides
    
    # Microsoft Office files
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': None,  # Word
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': None,  # Excel
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': None,  # PowerPoint
    
    # Text files
    'text/plain': None,
    'text/csv': None,
    'text/html': None,
    'text/markdown': None,
    
    # PDF
    'application/pdf': None,
    
    # Other formats
    'application/rtf': None,
    'application/json': None,
}

class GoogleDriveClient:
    """
    Google Drive API client for accessing and monitoring files.
    
    This client handles authentication, file listing, content downloading,
    and change monitoring for Google Drive integration.
    """
    
    def __init__(self, credentials_file: str, token_file: str = 'token.json'):
        """
        Initialize Google Drive client
        
        Args:
            credentials_file: Path to the OAuth2 credentials JSON file
            token_file: Path to store the access token
        """
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.service = None
        self.credentials = None
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize the Google Drive service with authentication"""
        try:
            self.credentials = self._get_credentials()
            self.service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Google Drive service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Google Drive service: {e}")
            raise
    
    def _get_credentials(self) -> Credentials:
        """Get and refresh OAuth2 credentials"""
        creds = None
        
        # Load existing token
        if Path(self.token_file).exists():
            creds = Credentials.from_authorized_user_file(self.token_file, SCOPES)
        
        # If no valid credentials, get new ones
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("Refreshed Google Drive credentials")
                except Exception as e:
                    logger.warning(f"Failed to refresh credentials: {e}")
                    creds = None
            
            if not creds:
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.credentials_file, SCOPES
                )
                creds = flow.run_local_server(port=0)
                logger.info("Obtained new Google Drive credentials")
            
            # Save credentials for next run
            with open(self.token_file, 'w') as token:
                token.write(creds.to_json())
        
        return creds
    
    def list_files(self, 
                  folder_id: Optional[str] = None,
                  mime_types: Optional[List[str]] = None,
                  modified_after: Optional[datetime] = None,
                  page_size: int = 100) -> Iterator[Dict[str, Any]]:
        """
        List files from Google Drive
        
        Args:
            folder_id: ID of folder to search in (None for all files)
            mime_types: List of MIME types to filter by
            modified_after: Only return files modified after this date
            page_size: Number of files per page
            
        Yields:
            File metadata dictionaries
        """
        try:
            # Build query
            query_parts = []
            
            if folder_id:
                query_parts.append(f"'{folder_id}' in parents")
            
            if mime_types:
                mime_queries = [f"mimeType='{mime_type}'" for mime_type in mime_types]
                query_parts.append(f"({' or '.join(mime_queries)})")
            
            if modified_after:
                timestamp = modified_after.isoformat()
                query_parts.append(f"modifiedTime > '{timestamp}'")
            
            # Exclude trashed files
            query_parts.append("trashed=false")
            
            query = ' and '.join(query_parts) if query_parts else "trashed=false"
            
            # File fields to retrieve
            fields = (
                "nextPageToken, files(id, name, mimeType, size, createdTime, "
                "modifiedTime, lastModifyingUser, parents, webViewLink, "
                "exportLinks, capabilities, owners)"
            )
            
            page_token = None
            
            while True:
                results = self.service.files().list(
                    q=query,
                    pageSize=page_size,
                    fields=fields,
                    pageToken=page_token,
                    orderBy='modifiedTime desc'
                ).execute()
                
                files = results.get('files', [])
                
                for file in files:
                    yield file
                
                page_token = results.get('nextPageToken')
                if not page_token:
                    break
                    
        except HttpError as e:
            logger.error(f"Failed to list files: {e}")
            raise
    
    def get_file_content(self, file_id: str, mime_type: str) -> Optional[str]:
        """
        Download and extract text content from a file
        
        Args:
            file_id: Google Drive file ID
            mime_type: MIME type of the file
            
        Returns:
            Text content of the file, or None if not extractable
        """
        try:
            if mime_type not in SUPPORTED_MIME_TYPES:
                logger.debug(f"Unsupported MIME type: {mime_type}")
                return None
            
            # Get export format for Google Workspace files
            export_mime_type = SUPPORTED_MIME_TYPES[mime_type]
            
            if export_mime_type:
                # Export Google Workspace file
                request = self.service.files().export_media(
                    fileId=file_id,
                    mimeType=export_mime_type
                )
            else:
                # Download regular file
                request = self.service.files().get_media(fileId=file_id)
            
            # Download content
            file_io = io.BytesIO()
            downloader = MediaIoBaseDownload(file_io, request)
            
            done = False
            while not done:
                status, done = downloader.next_chunk()
            
            # Extract text content
            content = file_io.getvalue()
            
            # Handle different content types
            if export_mime_type == 'text/plain' or mime_type.startswith('text/'):
                return content.decode('utf-8', errors='ignore')
            elif mime_type == 'application/json':
                try:
                    data = json.loads(content.decode('utf-8'))
                    return json.dumps(data, indent=2)
                except:
                    return content.decode('utf-8', errors='ignore')
            elif mime_type == 'application/pdf':
                # For PDF, we would need a PDF parser library
                # For now, return None - can be extended later
                logger.info(f"PDF content extraction not implemented for file {file_id}")
                return None
            else:
                # Try to decode as text
                return content.decode('utf-8', errors='ignore')
                
        except HttpError as e:
            if e.resp.status == 403:
                logger.warning(f"No permission to download file {file_id}")
            else:
                logger.error(f"Failed to download file {file_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error extracting content from file {file_id}: {e}")
            return None
    
    def get_file_metadata(self, file_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed metadata for a specific file
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            File metadata dictionary
        """
        try:
            fields = (
                "id, name, mimeType, size, createdTime, modifiedTime, "
                "lastModifyingUser, parents, webViewLink, exportLinks, "
                "capabilities, owners, description, properties"
            )
            
            file = self.service.files().get(
                fileId=file_id,
                fields=fields
            ).execute()
            
            return file
            
        except HttpError as e:
            logger.error(f"Failed to get file metadata for {file_id}: {e}")
            return None
    
    def watch_changes(self, 
                     channel_id: str,
                     notification_url: str,
                     folder_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Set up a webhook to monitor file changes
        
        Args:
            channel_id: Unique identifier for the watch channel
            notification_url: URL to receive change notifications
            folder_id: Optional folder to monitor (None for all files)
            
        Returns:
            Watch response with channel information
        """
        try:
            body = {
                'id': channel_id,
                'type': 'web_hook',
                'address': notification_url,
                'payload': True
            }
            
            if folder_id:
                # Watch specific folder
                result = self.service.files().watch(
                    fileId=folder_id,
                    body=body
                ).execute()
            else:
                # Watch all changes using changes.watch
                result = self.service.changes().watch(
                    pageToken=self.get_start_page_token(),
                    body=body
                ).execute()
            
            logger.info(f"Set up watch channel {channel_id}")
            return result
            
        except HttpError as e:
            logger.error(f"Failed to set up watch channel: {e}")
            raise
    
    def stop_watching(self, channel_id: str, resource_id: str):
        """
        Stop watching for changes
        
        Args:
            channel_id: Channel ID to stop
            resource_id: Resource ID from the watch response
        """
        try:
            body = {
                'id': channel_id,
                'resourceId': resource_id
            }
            
            self.service.channels().stop(body=body).execute()
            logger.info(f"Stopped watch channel {channel_id}")
            
        except HttpError as e:
            logger.error(f"Failed to stop watch channel: {e}")
    
    def get_start_page_token(self) -> str:
        """
        Get the start page token for change monitoring
        
        Returns:
            Page token for starting change monitoring
        """
        try:
            response = self.service.changes().getStartPageToken().execute()
            return response.get('startPageToken')
        except HttpError as e:
            logger.error(f"Failed to get start page token: {e}")
            raise
    
    def get_changes(self, page_token: str) -> Dict[str, Any]:
        """
        Get changes since the given page token
        
        Args:
            page_token: Token from which to get changes
            
        Returns:
            Changes response with new files and next page token
        """
        try:
            response = self.service.changes().list(
                pageToken=page_token,
                fields="nextPageToken, newStartPageToken, changes(fileId, file(id, name, mimeType, modifiedTime, trashed))"
            ).execute()
            
            return response
            
        except HttpError as e:
            logger.error(f"Failed to get changes: {e}")
            raise
    
    def search_files(self, query: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """
        Search for files using Google Drive search
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of matching files
        """
        try:
            # Build search query
            search_query = f"fullText contains '{query}' and trashed=false"
            
            results = self.service.files().list(
                q=search_query,
                pageSize=max_results,
                fields="files(id, name, mimeType, modifiedTime, webViewLink)"
            ).execute()
            
            return results.get('files', [])
            
        except HttpError as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def is_authenticated(self) -> bool:
        """Check if the client is properly authenticated"""
        return self.credentials is not None and self.credentials.valid
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the authenticated user"""
        try:
            user = self.service.about().get(fields="user").execute()
            return user.get('user')
        except HttpError as e:
            logger.error(f"Failed to get user info: {e}")
            return None

    def update_file_content(self, file_id: str, content: str, mime_type: str = 'text/plain') -> bool:
        """
        Update the content of a specific file
        
        Args:
            file_id: Google Drive file ID
            content: New content for the file
            mime_type: MIME type of the content
            
        Returns:
            True if successful, False otherwise
        """
        try:
            from googleapiclient.http import MediaIoBaseUpload
            
            # Create media body
            media_body = MediaIoBaseUpload(
                io.BytesIO(content.encode('utf-8')),
                mimetype=mime_type
            )
            
            # Update file
            updated_file = self.service.files().update(
                fileId=file_id,
                media_body=media_body
            ).execute()
            
            logger.info(f"Updated file {file_id}: {updated_file.get('name')}")
            return True
            
        except HttpError as e:
            logger.error(f"Failed to update file {file_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error updating file {file_id}: {e}")
            return False

    def get_folder_contents(self, folder_id: str) -> List[Dict[str, Any]]:
        """
        Get all contents of a specific folder
        
        Args:
            folder_id: Google Drive folder ID
            
        Returns:
            List of files and folders in the specified folder
        """
        try:
            files = list(self.list_files(folder_id=folder_id))
            return files
        except Exception as e:
            logger.error(f"Failed to get folder contents for {folder_id}: {e}")
            return []