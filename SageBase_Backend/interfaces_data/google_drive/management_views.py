import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime

from django.http import JsonResponse, HttpRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View

from .google_drive_interface import GoogleDriveInterface
from .webhook_handler import (
    get_webhook_handler, 
    register_workspace_webhook,
    unregister_workspace_webhook,
    get_webhook_status
)
from ..notification_manager import get_notification_manager, create_console_logger_callback
from vectordb.models.document import SearchQuery

logger = logging.getLogger(__name__)


class GoogleDriveManagementView(View):
    """
    Django view for managing Google Drive integrations
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._interfaces: Dict[str, GoogleDriveInterface] = {}
    
    def get_interface(self, workspace: str, **kwargs) -> Optional[GoogleDriveInterface]:
        """
        Get or create a Google Drive interface for a workspace
        
        Args:
            workspace: Workspace identifier
            **kwargs: Additional interface configuration
            
        Returns:
            GoogleDriveInterface instance or None if creation fails
        """
        if workspace not in self._interfaces:
            try:
                # Configuration should come from environment or settings
                credentials_file = kwargs.get('credentials_file', 'google_drive_credentials.json')
                folder_id = kwargs.get('folder_id')
                token_file = kwargs.get('token_file', f'google_drive_token_{workspace}.json')
                
                interface = GoogleDriveInterface(
                    workspace=workspace,
                    folder_id=folder_id,
                    credentials_file=credentials_file,
                    token_file=token_file
                )
                
                # Set up console logging for notifications
                console_callback = create_console_logger_callback()
                notification_manager = get_notification_manager()
                notification_manager.subscribe(console_callback, f"gdrive_console_{workspace}")
                
                self._interfaces[workspace] = interface
                logger.info(f"Created Google Drive interface for workspace: {workspace}")
                
            except Exception as e:
                logger.error(f"Failed to create Google Drive interface for {workspace}: {e}")
                return None
        
        return self._interfaces[workspace]
    
    @method_decorator(csrf_exempt)
    def dispatch(self, request, *args, **kwargs):
        """Allow CSRF exemption for API calls"""
        return super().dispatch(request, *args, **kwargs)
    
    def post(self, request: HttpRequest, action: str, workspace: Optional[str] = None) -> JsonResponse:
        """
        Handle POST requests for Google Drive management actions
        
        Args:
            request: HTTP request
            action: Action to perform
            workspace: Optional workspace identifier
            
        Returns:
            JSON response
        """
        try:
            # Parse request body
            try:
                data = json.loads(request.body) if request.body else {}
            except json.JSONDecodeError:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid JSON in request body'
                }, status=400)
            
            # Route to appropriate action handler
            if action == 'sync':
                return self._handle_sync(workspace, data)
            elif action == 'setup_webhook':
                return self._handle_setup_webhook(workspace, data)
            elif action == 'search':
                return self._handle_search(workspace, data)
            elif action == 'get_stats':
                return self._handle_get_stats(workspace, data)
            elif action == 'authenticate':
                return self._handle_authenticate(workspace, data)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': f'Unknown action: {action}'
                }, status=400)
                
        except Exception as e:
            logger.error(f"Error handling Google Drive action {action}: {e}")
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    def get(self, request: HttpRequest, action: str, workspace: Optional[str] = None) -> JsonResponse:
        """
        Handle GET requests for Google Drive management
        
        Args:
            request: HTTP request
            action: Action to perform
            workspace: Optional workspace identifier
            
        Returns:
            JSON response
        """
        try:
            if action == 'status':
                return self._handle_get_status(workspace)
            elif action == 'webhook_status':
                return self._handle_webhook_status()
            elif action == 'list_workspaces':
                return self._handle_list_workspaces()
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': f'Unknown GET action: {action}'
                }, status=400)
                
        except Exception as e:
            logger.error(f"Error handling Google Drive GET action {action}: {e}")
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    def _handle_sync(self, workspace: str, data: Dict[str, Any]) -> JsonResponse:
        """Handle workspace synchronization"""
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': 'Workspace is required for sync operation'
            }, status=400)
        
        interface = self.get_interface(workspace, **data)
        if not interface:
            return JsonResponse({
                'status': 'error',
                'message': f'Failed to initialize Google Drive interface for workspace {workspace}'
            }, status=500)
        
        # Get folder contents (equivalent to sync)
        folder_contents = interface.get_folder_contents()
        sync_result = {
            'status': 'success',
            'files_found': len(folder_contents),
            'timestamp': datetime.now().isoformat()
        }
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'sync_result': sync_result
        })
    
    def _handle_setup_webhook(self, workspace: str, data: Dict[str, Any]) -> JsonResponse:
        """Handle webhook setup"""
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': 'Workspace is required for webhook setup'
            }, status=400)
        
        notification_url = data.get('notification_url')
        if not notification_url:
            return JsonResponse({
                'status': 'error',
                'message': 'notification_url is required for webhook setup'
            }, status=400)
        
        interface = self.get_interface(workspace, **data)
        if not interface:
            return JsonResponse({
                'status': 'error',
                'message': f'Failed to initialize Google Drive interface for workspace {workspace}'
            }, status=500)
        
        webhook_result = register_workspace_webhook(workspace, interface, notification_url)
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'webhook_result': webhook_result
        })
    
    def _handle_search(self, workspace: str, data: Dict[str, Any]) -> JsonResponse:
        """Handle document search"""
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': 'Workspace is required for search operation'
            }, status=400)
        
        query = data.get('query')
        if not query:
            return JsonResponse({
                'status': 'error',
                'message': 'Query is required for search operation'
            }, status=400)
        
        interface = self.get_interface(workspace, **data)
        if not interface:
            return JsonResponse({
                'status': 'error',
                'message': f'Failed to initialize Google Drive interface for workspace {workspace}'
            }, status=500)
        
        # Use Google Drive search functionality
        results = interface.drive_client.search_files(
            query=query, 
            max_results=data.get('limit', 10)
        )
        
        # Convert results to JSON-serializable format
        search_results = []
        for result in results:
            search_results.append({
                'id': result.get('id'),
                'name': result.get('name'),
                'mimeType': result.get('mimeType'),
                'modifiedTime': result.get('modifiedTime'),
                'webViewLink': result.get('webViewLink')
            })
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'query': query,
            'results': search_results,
            'total_results': len(search_results)
        })
    
    def _handle_get_stats(self, workspace: str, data: Dict[str, Any]) -> JsonResponse:
        """Handle workspace statistics request"""
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': 'Workspace is required for stats operation'
            }, status=400)
        
        interface = self.get_interface(workspace, **data)
        if not interface:
            return JsonResponse({
                'status': 'error',
                'message': f'Failed to initialize Google Drive interface for workspace {workspace}'
            }, status=500)
        
        # Get subscription and folder statistics  
        subscription_status = interface.get_subscription_status()
        folder_contents = interface.get_folder_contents()
        
        stats = {
            'total_files': len(folder_contents),
            'active_subscriptions': subscription_status.get('active_subscriptions', 0),
            'folder_id': subscription_status.get('folder_id'),
            'last_sync': subscription_status.get('last_sync'),
            'authenticated': subscription_status.get('authenticated', False)
        }
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'stats': stats
        })
    
    def _handle_authenticate(self, workspace: str, data: Dict[str, Any]) -> JsonResponse:
        """Handle authentication check"""
        if not workspace:
            return JsonResponse({
                'status': 'error',
                'message': 'Workspace is required for authentication check'
            }, status=400)
        
        interface = self.get_interface(workspace, **data)
        if not interface:
            return JsonResponse({
                'status': 'error',
                'message': f'Failed to initialize Google Drive interface for workspace {workspace}'
            }, status=500)
        
        authenticated = interface.is_authenticated()
        user_info = interface.get_user_info() if authenticated else None
        
        return JsonResponse({
            'status': 'success',
            'workspace': workspace,
            'authenticated': authenticated,
            'user_info': user_info
        })
    
    def _handle_get_status(self, workspace: Optional[str]) -> JsonResponse:
        """Handle status request"""
        if workspace:
            # Get status for specific workspace
            interface = self._interfaces.get(workspace)
            if not interface:
                return JsonResponse({
                    'status': 'error',
                    'message': f'No interface found for workspace {workspace}'
                }, status=404)
            
            subscription_status = interface.get_subscription_status()
            status = {
                'workspace': workspace,
                'authenticated': interface.is_authenticated(),
                'folder_id': interface.folder_id,
                'active_subscriptions': subscription_status.get('active_subscriptions', 0),
                'last_sync': subscription_status.get('last_sync')
            }
            
            return JsonResponse({
                'status': 'success',
                'workspace_status': status
            })
        else:
            # Get status for all workspaces
            workspaces_status = {}
            for ws, interface in self._interfaces.items():
                subscription_status = interface.get_subscription_status()
                workspaces_status[ws] = {
                    'authenticated': interface.is_authenticated(),
                    'folder_id': interface.folder_id,
                    'active_subscriptions': subscription_status.get('active_subscriptions', 0),
                    'last_sync': subscription_status.get('last_sync')
                }
            
            return JsonResponse({
                'status': 'success',
                'total_workspaces': len(self._interfaces),
                'workspaces': workspaces_status
            })
    
    def _handle_webhook_status(self) -> JsonResponse:
        """Handle webhook status request"""
        webhook_status = get_webhook_status()
        
        return JsonResponse({
            'status': 'success',
            'webhook_status': webhook_status
        })
    
    def _handle_list_workspaces(self) -> JsonResponse:
        """Handle list workspaces request"""
        workspaces = list(self._interfaces.keys())
        
        return JsonResponse({
            'status': 'success',
            'workspaces': workspaces,
            'total_workspaces': len(workspaces)
        })


# Utility functions for easier API access

@csrf_exempt
@require_http_methods(["POST"])
def sync_workspace(request: HttpRequest, workspace: str) -> JsonResponse:
    """
    Simplified endpoint for workspace synchronization
    
    Args:
        request: HTTP request
        workspace: Workspace identifier
        
    Returns:
        JSON response
    """
    view = GoogleDriveManagementView()
    return view.post(request, 'sync', workspace)


@csrf_exempt
@require_http_methods(["POST"])
def setup_webhook(request: HttpRequest, workspace: str) -> JsonResponse:
    """
    Simplified endpoint for webhook setup
    
    Args:
        request: HTTP request
        workspace: Workspace identifier
        
    Returns:
        JSON response
    """
    view = GoogleDriveManagementView()
    return view.post(request, 'setup_webhook', workspace)


@csrf_exempt
@require_http_methods(["POST"])
def search_documents(request: HttpRequest, workspace: str) -> JsonResponse:
    """
    Simplified endpoint for document search
    
    Args:
        request: HTTP request
        workspace: Workspace identifier
        
    Returns:
        JSON response
    """
    view = GoogleDriveManagementView()
    return view.post(request, 'search', workspace)


@require_http_methods(["GET"])
def get_status(request: HttpRequest, workspace: Optional[str] = None) -> JsonResponse:
    """
    Simplified endpoint for status retrieval
    
    Args:
        request: HTTP request
        workspace: Optional workspace identifier
        
    Returns:
        JSON response
    """
    view = GoogleDriveManagementView()
    return view.get(request, 'status', workspace)


@require_http_methods(["GET"])
def get_webhook_status_endpoint(request: HttpRequest) -> JsonResponse:
    """
    Simplified endpoint for webhook status
    
    Args:
        request: HTTP request
        
    Returns:
        JSON response
    """
    view = GoogleDriveManagementView()
    return view.get(request, 'webhook_status')