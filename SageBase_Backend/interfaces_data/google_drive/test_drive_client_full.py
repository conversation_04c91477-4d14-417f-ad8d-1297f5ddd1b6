from drive_client import GoogleDriveClient
from datetime import datetime, timedelta

CREDENTIALS_FILE = r"A:\Sagebase_Latest_git\SageBase_BackEnd\interfaces_data\google_drive_credentials.json"
TOKEN_FILE = r"A:\Sagebase_Latest_git\SageBase_BackEnd\interfaces_data\gdrive_token_SageBase_Taha_test.json"

def print_separator():
    print("\n" + "="*60 + "\n")

def main():
    print("=== Google Drive Full Method Test ===")
    client = GoogleDriveClient(CREDENTIALS_FILE, TOKEN_FILE)

    # 1. Test Authentication
    print_separator()
    print("Authenticated:", client.is_authenticated())
    user = client.get_user_info()
    print("User info:", user)

    # 2. List Files in Root
    # print_separator()
    # print("Listing first 5 files in root:")
    # files = list(client.list_files(page_size=5))
    # for i, file in enumerate(files):
    #     print(f"{i+1}. {file['name']} ({file['id']}) - {file['mimeType']}")

    # 2b. List Files in Specific Folder
    print_separator()
    print("Listing first 5 files in folder 1Xvq0PMCp6BWPp30g-7fbibMm0Cm2KBNL:")
    try:
        folder_id = "1Xvq0PMCp6BWPp30g-7fbibMm0Cm2KBNL"
        folder_files = []
        for i, file in enumerate(client.list_files(folder_id=folder_id, page_size=5)):
            print(f"{i+1}. {file['name']} ({file['id']}) - {file['mimeType']}")
            folder_files.append(file)
            if i >= 4:
                break
        if not folder_files:
            print("No files found in this folder.")
    except Exception as e:
        print("Error while listing files in folder:", e)

    # 3. Get File Metadata
    print_separator()
    if folder_files:
        file = folder_files[0]
        print(f"Getting metadata for: {file['name']} ({file['id']})")
        metadata = client.get_file_metadata(file['id'])
        print("Metadata:", metadata)

    # 4. Get File Content
    print_separator()
    if folder_files:
        file = folder_files[0]
        print(f"Getting content for: {file['name']} ({file['id']})")
        content = client.get_file_content(file['id'], file['mimeType'])
        if content:
            print("Content (first 300 chars):\n", content[:300])
        else:
            print("No content extracted (unsupported type or empty).")

    # 5. Search Files
    print_separator()
    print("Searching for files containing 'test':")
    search_results = client.search_files("test", max_results=3)
    for i, file in enumerate(search_results):
        print(f"{i+1}. {file['name']} ({file['id']}) - {file['mimeType']}")

    # 6. Update File Content (only for text files, be careful!)
    print_separator()
    for file in folder_files:
        if file['mimeType'].startswith('text/'):
            print(f"Updating content for: {file['name']} ({file['id']})")
            success = client.update_file_content(file['id'], "This is a test update from the API.", mime_type=file['mimeType'])
            print("Update success:", success)
            break

    # 7. Get Folder Contents (use root or a known folder ID)
    print_separator()
    print("Getting contents of root folder:")
    root_folder_id = '1Xvq0PMCp6BWPp30g-7fbibMm0Cm2KBNL'
    folder_contents = client.get_folder_contents(root_folder_id)
    print(f"Found {len(folder_contents)} items in root folder.")

    # 8. List files modified in the last 7 days
    print_separator()
    print("Listing files modified in the last 7 days:")
    since = datetime.utcnow() - timedelta(days=7)
    recent_files = list(client.list_files(modified_after=since, page_size=5))
    for i, file in enumerate(recent_files):
        print(f"{i+1}. {file['name']} ({file['id']}) - Modified: {file['modifiedTime']}")

    # 9. (Optional) Test watch_changes and get_changes (requires webhook endpoint)
    # Skipped in this script for safety.

if __name__ == "__main__":
    main() 