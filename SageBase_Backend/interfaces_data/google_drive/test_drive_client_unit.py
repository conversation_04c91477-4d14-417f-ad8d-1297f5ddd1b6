from drive_client import GoogleDriveClient

CREDENTIALS_FILE = r"A:\Sagebase_Latest_git\SageBase_BackEnd\interfaces_data\google_drive_credentials.json"
TOKEN_FILE = r"A:\Sagebase_Latest_git\SageBase_BackEnd\interfaces_data\gdrive_token_SageBase_Taha_test.json"  # Use your actual token file name
            
def main():
    print("=== Google Drive Manual Test ===")
    client = GoogleDriveClient(CREDENTIALS_FILE, TOKEN_FILE)

    # 1. Test Authentication
    print("Authenticated:", client.is_authenticated())
    user = client.get_user_info()
    print("User info:", user)

    # 2. List Files in Root
    print("\nListing first 10 files in root:")
    files = list(client.list_files(page_size=10))
    for i, file in enumerate(files):
        print(f"{i+1}. {file['name']} ({file['id']}) - {file['mimeType']}")

    # 3. Try to get content of the first file (if possible)
    if files:
        file = files[0]
        print(f"\nGetting content for: {file['name']} ({file['id']})")
        content = client.get_file_content(file['id'], file['mimeType'])
        if content:
            print("Content (first 300 chars):\n", content[:300])
        else:
            print("No content extracted (unsupported type or empty).")

if __name__ == "__main__":
    main()