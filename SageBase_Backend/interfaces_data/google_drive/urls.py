from django.urls import path, include
from . import management_views, webhook_handler

app_name = 'google_drive'

urlpatterns = [
    # Management API endpoints
    path('manage/<str:action>/', management_views.GoogleDriveManagementView.as_view(), name='manage_global'),
    path('manage/<str:action>/<str:workspace>/', management_views.GoogleDriveManagementView.as_view(), name='manage_workspace'),
    
    # Simplified endpoints
    path('sync/<str:workspace>/', management_views.sync_workspace, name='sync'),
    path('webhook/setup/<str:workspace>/', management_views.setup_webhook, name='setup_webhook'),
    path('search/<str:workspace>/', management_views.search_documents, name='search'),
    path('status/', management_views.get_status, name='status_global'),
    path('status/<str:workspace>/', management_views.get_status, name='status_workspace'),
    path('webhook/status/', management_views.get_webhook_status_endpoint, name='webhook_status'),
    
    # Webhook receiver endpoints
    path('webhook/receive/', webhook_handler.GoogleDriveWebhookView.as_view(), name='webhook_receive_global'),
    path('webhook/receive/<str:workspace>/', webhook_handler.GoogleDriveWebhookView.as_view(), name='webhook_receive_workspace'),
]