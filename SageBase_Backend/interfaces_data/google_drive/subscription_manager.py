"""
Google Drive subscription manager for handling folder notifications.
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from .drive_client import GoogleDriveClient

logger = logging.getLogger(__name__)

class GoogleDriveSubscriptionManager:
    """
    Manages Google Drive push notifications subscriptions for folder monitoring.
    """
    
    def __init__(self, drive_client: GoogleDriveClient, webhook_url: str = None):
        self.drive_client = drive_client
        self.cache_prefix = "gdrive_subscription:"
        self.webhook_url = webhook_url or getattr(settings, 'GOOGLE_DRIVE_WEBHOOK_URL', 
                                                'https://your-domain.com/api/google-drive/webhook/')
    
    def subscribe_to_folder(self, folder_id: str, user_id: str) -> Dict[str, Any]:
        """
        Subscribe to notifications for a specific folder.
        
        Args:
            folder_id: Google Drive folder ID
            user_id: User ID for the subscription
            
        Returns:
            Dictionary containing subscription details
        """
        try:
            # Generate unique channel ID
            channel_id = f"folder_{folder_id}_{user_id}_{uuid.uuid4().hex[:8]}"
            
            # Set up watch channel
            watch_response = self.drive_client.watch_changes(
                channel_id=channel_id,
                notification_url=self.webhook_url,
                folder_id=folder_id
            )
            
            # Store subscription info in cache
            subscription_data = {
                'channel_id': channel_id,
                'resource_id': watch_response.get('resourceId'),
                'folder_id': folder_id,
                'user_id': user_id,
                'created_at': datetime.now().isoformat(),
                'expires_at': watch_response.get('expiration'),
                'webhook_url': self.webhook_url
            }
            
            cache_key = f"{self.cache_prefix}{channel_id}"
            cache.set(cache_key, subscription_data, timeout=60*60*24*7)  # 7 days
            
            # Also store folder -> channel mapping
            folder_cache_key = f"{self.cache_prefix}folder_{folder_id}"
            existing_channels = cache.get(folder_cache_key, [])
            existing_channels.append(channel_id)
            cache.set(folder_cache_key, existing_channels, timeout=60*60*24*7)
            
            logger.info(f"Subscribed to folder {folder_id} with channel {channel_id}")
            return subscription_data
            
        except Exception as e:
            logger.error(f"Failed to subscribe to folder {folder_id}: {e}")
            raise
    
    def unsubscribe_from_folder(self, channel_id: str) -> bool:
        """
        Unsubscribe from folder notifications.
        
        Args:
            channel_id: Channel ID to unsubscribe
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get subscription data
            cache_key = f"{self.cache_prefix}{channel_id}"
            subscription_data = cache.get(cache_key)
            
            if not subscription_data:
                logger.warning(f"Subscription data not found for channel {channel_id}")
                return False
            
            # Stop watching
            self.drive_client.stop_watching(
                channel_id=channel_id,
                resource_id=subscription_data['resource_id']
            )
            
            # Remove from cache
            cache.delete(cache_key)
            
            # Remove from folder mapping
            folder_id = subscription_data['folder_id']
            folder_cache_key = f"{self.cache_prefix}folder_{folder_id}"
            existing_channels = cache.get(folder_cache_key, [])
            if channel_id in existing_channels:
                existing_channels.remove(channel_id)
                cache.set(folder_cache_key, existing_channels, timeout=60*60*24*7)
            
            logger.info(f"Unsubscribed from channel {channel_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from channel {channel_id}: {e}")
            return False
    
    def get_subscription_info(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """
        Get subscription information for a channel.
        
        Args:
            channel_id: Channel ID
            
        Returns:
            Subscription data or None if not found
        """
        cache_key = f"{self.cache_prefix}{channel_id}"
        return cache.get(cache_key)
    
    def get_folder_subscriptions(self, folder_id: str) -> List[str]:
        """
        Get all active subscriptions for a folder.
        
        Args:
            folder_id: Google Drive folder ID
            
        Returns:
            List of channel IDs subscribed to the folder
        """
        folder_cache_key = f"{self.cache_prefix}folder_{folder_id}"
        return cache.get(folder_cache_key, [])
    
    def refresh_subscription(self, channel_id: str) -> bool:
        """
        Refresh an existing subscription to extend its expiration.
        
        Args:
            channel_id: Channel ID to refresh
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current subscription
            subscription_data = self.get_subscription_info(channel_id)
            if not subscription_data:
                logger.warning(f"No subscription found for channel {channel_id}")
                return False
            
            # Stop current subscription
            self.unsubscribe_from_folder(channel_id)
            
            # Create new subscription
            new_subscription = self.subscribe_to_folder(
                folder_id=subscription_data['folder_id'],
                user_id=subscription_data['user_id']
            )
            
            logger.info(f"Refreshed subscription for folder {subscription_data['folder_id']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to refresh subscription {channel_id}: {e}")
            return False
    
    def cleanup_expired_subscriptions(self) -> int:
        """
        Clean up expired subscriptions.
        
        Returns:
            Number of subscriptions cleaned up
        """
        try:
            # This is a simplified cleanup - in production you'd want to
            # iterate through all subscriptions and check expiration
            cleaned_count = 0
            
            # Get all subscription keys from cache
            # This is cache-backend specific - adjust as needed
            if hasattr(cache, 'keys'):
                keys = cache.keys(f"{self.cache_prefix}*")
                for key in keys:
                    if key.startswith(self.cache_prefix) and not key.endswith('folder_'):
                        subscription_data = cache.get(key)
                        if subscription_data and subscription_data.get('expires_at'):
                            expires_at = datetime.fromisoformat(subscription_data['expires_at'])
                            if expires_at < datetime.now():
                                self.unsubscribe_from_folder(
                                    subscription_data['channel_id']
                                )
                                cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} expired subscriptions")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired subscriptions: {e}")
            return 0
    
    def list_all_subscriptions(self) -> List[Dict[str, Any]]:
        """
        List all active subscriptions.
        
        Returns:
            List of subscription data dictionaries
        """
        subscriptions = []
        
        try:
            # Get all subscription keys from cache
            if hasattr(cache, 'keys'):
                keys = cache.keys(f"{self.cache_prefix}*")
                for key in keys:
                    if key.startswith(self.cache_prefix) and not key.endswith('folder_'):
                        subscription_data = cache.get(key)
                        if subscription_data:
                            subscriptions.append(subscription_data)
            
            logger.info(f"Found {len(subscriptions)} active subscriptions")
            return subscriptions
            
        except Exception as e:
            logger.error(f"Failed to list subscriptions: {e}")
            return []