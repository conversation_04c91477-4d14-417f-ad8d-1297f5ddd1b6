"""
Google Drive interface implementation for SageBase.
"""

import logging
import os
import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from django.conf import settings

from .drive_client import GoogleDriveClient
from .subscription_manager import GoogleDriveSubscriptionManager
from ..notification_manager import get_notification_manager
from ..base_interface import BaseDocumentInterface, NotificationEvent, NotificationLevel, OperationType, DocumentOperation
from vectordb.models.document import DataSource

logger = logging.getLogger(__name__)


class GoogleDriveInterface(BaseDocumentInterface):
    """
    Google Drive interface for folder monitoring and file management.
    
    This interface provides:
    1. Subscription to Google Drive folder notifications
    2. Real-time webhook processing for file changes
    3. Folder content retrieval and management
    4. File update functionality
    """
    
    def __init__(self, 
                 collection_name: str,
                 folder_id: str,
                 credentials_file: str = None,
                 token_file: Optional[str] = None):
        """
        Initialize Google Drive interface
        
        Args:
            collection_name: uid of the company
            folder_id: Google Drive folder ID to monitor
            credentials_file: Path to Google OAuth credentials file (defaults to interfaces_data/google_drive_credentials.json)
            token_file: Path to store access token (optional)
        """
        super().__init__(DataSource.GOOGLE_DRIVE, collection_name)
        
        self.folder_id = folder_id
        # Use the credentials file in the interfaces_data directory
        if credentials_file is None:
            # Get the directory of this file
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            self.credentials_file = os.path.join(parent_dir, "google_drive_credentials.json")
        else:
            self.credentials_file = credentials_file
        self.token_file = token_file or f"gdrive_token_{collection_name}.json"
        
        # Initialize clients
        self.drive_client = GoogleDriveClient(
            credentials_file=self.credentials_file,
            token_file=self.token_file
        )
        
        # Will be set when webhook URL is provided
        self.subscription_manager = None
        
        # Notification manager
        self.notification_manager = get_notification_manager()
        
        # Internal state
        self._last_sync_time: Optional[datetime] = None
        self._active_subscriptions: List[str] = []
        
        logger.info(f"Google Drive interface initialized for workspace {collection_name}, folder {folder_id}")
    
    def is_authenticated(self) -> bool:
        """Check if Google Drive client is authenticated"""
        return self.drive_client.is_authenticated()
    
    def get_folder_contents(self) -> List[Dict[str, Any]]:
        """
        Get complete contents of the monitored folder
        
        Returns:
            List of file metadata dictionaries
        """
        try:
            contents = self.drive_client.get_folder_contents(self.folder_id)
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.READ,
                source=DataSource.GOOGLE_DRIVE,
                document_id=f"folder_{self.folder_id}",
                workspace=self.workspace,
                metadata={
                    'folder_id': self.folder_id,
                    'file_count': len(contents)
                }
            )
            
            # Send notification
            event = NotificationEvent(
                event_id=self._generate_event_id(),
                operation=operation,
                level=NotificationLevel.SUCCESS,
                message=f"Retrieved {len(contents)} files from Google Drive folder",
                details={'files': contents}
            )
            
            self.notification_manager.notify(event)
            
            return contents
            
        except Exception as e:
            logger.error(f"Failed to get folder contents: {e}")
            self._send_error_notification(f"Failed to retrieve folder contents: {str(e)}")
            return []
    
    def subscribe_to_folder(self, user_id: str, webhook_url: str = None) -> Dict[str, Any]:
        """
        Subscribe to folder change notifications
        
        Args:
            user_id: User ID for the subscription
            webhook_url: Webhook URL for notifications
            
        Returns:
            Subscription information
        """
        try:
            # Initialize subscription manager with webhook URL if provided
            if webhook_url and not self.subscription_manager:
                self.subscription_manager = GoogleDriveSubscriptionManager(
                    drive_client=self.drive_client,
                    webhook_url=webhook_url
                )
            elif not self.subscription_manager:
                self.subscription_manager = GoogleDriveSubscriptionManager(
                    drive_client=self.drive_client
                )
            
            subscription = self.subscription_manager.subscribe_to_folder(
                folder_id=self.folder_id,
                user_id=user_id
            )
            
            # Track active subscription
            channel_id = subscription['channel_id']
            self._active_subscriptions.append(channel_id)
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.CREATE,
                source=DataSource.GOOGLE_DRIVE,
                document_id=f"subscription_{channel_id}",
                workspace=self.workspace,
                metadata={
                    'folder_id': self.folder_id,
                    'channel_id': channel_id,
                    'user_id': user_id
                }
            )
            
            # Send notification
            event = NotificationEvent(
                event_id=self._generate_event_id(),
                operation=operation,
                level=NotificationLevel.SUCCESS,
                message=f"Successfully subscribed to Google Drive folder notifications",
                details=subscription
            )
            
            self.notification_manager.notify(event)
            
            return subscription
            
        except Exception as e:
            logger.error(f"Failed to subscribe to folder: {e}")
            self._send_error_notification(f"Failed to subscribe to folder: {str(e)}")
            raise
    
    def unsubscribe_from_folder(self, channel_id: str) -> bool:
        """
        Unsubscribe from folder notifications
        
        Args:
            channel_id: Channel ID to unsubscribe
            
        Returns:
            True if successful, False otherwise
        """
        try:
            success = self.subscription_manager.unsubscribe_from_folder(channel_id)
            
            if success and channel_id in self._active_subscriptions:
                self._active_subscriptions.remove(channel_id)
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.DELETE,
                source=DataSource.GOOGLE_DRIVE,
                document_id=f"subscription_{channel_id}",
                workspace=self.workspace,
                metadata={
                    'folder_id': self.folder_id,
                    'channel_id': channel_id
                }
            )
            
            # Send notification
            level = NotificationLevel.SUCCESS if success else NotificationLevel.ERROR
            message = "Successfully unsubscribed from folder notifications" if success else "Failed to unsubscribe from folder notifications"
            
            event = NotificationEvent(
                event_id=self._generate_event_id(),
                operation=operation,
                level=level,
                message=message,
                details={'success': success}
            )
            
            self.notification_manager.notify(event)
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from folder: {e}")
            self._send_error_notification(f"Failed to unsubscribe from folder: {str(e)}")
            return False
    
    def update_file(self, file_id: str, content: str, mime_type: str = 'text/plain') -> bool:
        """
        Update content of a specific file
        
        Args:
            file_id: Google Drive file ID
            content: New content for the file
            mime_type: MIME type of the content
            
        Returns:
            True if successful, False otherwise
        """
        try:
            success = self.drive_client.update_file_content(
                file_id=file_id,
                content=content,
                mime_type=mime_type
            )
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.UPDATE,
                source=DataSource.GOOGLE_DRIVE,
                document_id=file_id,
                workspace=self.workspace,
                metadata={
                    'file_id': file_id,
                    'mime_type': mime_type,
                    'content_length': len(content)
                }
            )
            
            # Send notification
            level = NotificationLevel.SUCCESS if success else NotificationLevel.ERROR
            message = f"Successfully updated file {file_id}" if success else f"Failed to update file {file_id}"
            
            event = NotificationEvent(
                event_id=self._generate_event_id(),
                operation=operation,
                level=level,
                message=message,
                details={'success': success, 'file_id': file_id}
            )
            
            self.notification_manager.notify(event)
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update file {file_id}: {e}")
            self._send_error_notification(f"Failed to update file {file_id}: {str(e)}")
            return False
    
    def process_webhook_notification(self, headers: Dict[str, str], body: str) -> Dict[str, Any]:
        """
        Process incoming webhook notification from Google Drive
        
        Args:
            headers: HTTP headers from webhook request
            body: Request body
            
        Returns:
            Processing result
        """
        try:
            # Extract webhook information
            channel_id = headers.get('X-Goog-Channel-ID', '')
            resource_id = headers.get('X-Goog-Resource-ID', '')
            change_type = headers.get('X-Goog-Resource-State', 'unknown')
            
            # Check if this is our subscription
            if channel_id not in self._active_subscriptions:
                logger.debug(f"Received webhook for untracked channel: {channel_id}")
                return {'status': 'ignored', 'reason': 'untracked_channel'}
            
            # Get subscription info
            subscription_info = self.subscription_manager.get_subscription_info(channel_id)
            if not subscription_info:
                logger.warning(f"No subscription info found for channel {channel_id}")
                return {'status': 'error', 'reason': 'subscription_not_found'}
            
            # Process the change
            result = self._process_folder_change(change_type, subscription_info)
            
            # Update last sync time
            self._last_sync_time = datetime.now()
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process webhook notification: {e}")
            self._send_error_notification(f"Failed to process webhook notification: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def _process_folder_change(self, change_type: str, subscription_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a folder change notification
        
        Args:
            change_type: Type of change (sync, update, etc.)
            subscription_info: Subscription information
            
        Returns:
            Processing result
        """
        try:
            # Get current folder contents
            folder_contents = self.get_folder_contents()
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.UPDATE,
                source=DataSource.GOOGLE_DRIVE,
                document_id=f"folder_change_{self.folder_id}",
                workspace=self.workspace,
                metadata={
                    'change_type': change_type,
                    'folder_id': self.folder_id,
                    'channel_id': subscription_info['channel_id'],
                    'file_count': len(folder_contents)
                }
            )
            
            # Send notification
            event = NotificationEvent(
                event_id=self._generate_event_id(),
                operation=operation,
                level=NotificationLevel.INFO,
                message=f"Google Drive folder change detected: {change_type}",
                details={
                    'change_type': change_type,
                    'folder_id': self.folder_id,
                    'file_count': len(folder_contents)
                }
            )
            
            self.notification_manager.notify(event)
            
            return {
                'status': 'processed',
                'change_type': change_type,
                'folder_id': self.folder_id,
                'file_count': len(folder_contents),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to process folder change: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get authenticated user information"""
        return self.drive_client.get_user_info()
    
    def get_subscription_status(self) -> Dict[str, Any]:
        """Get status of all subscriptions"""
        return {
            'active_subscriptions': len(self._active_subscriptions),
            'subscription_ids': self._active_subscriptions,
            'folder_id': self.folder_id,
            'workspace': self.workspace,
            'last_sync': self._last_sync_time.isoformat() if self._last_sync_time else None,
            'authenticated': self.is_authenticated()
        }
    
    def setup_webhook(self, notification_url: str) -> Dict[str, Any]:
        """
        Set up webhook for the interface
        
        Args:
            notification_url: URL to receive notifications
            
        Returns:
            Webhook setup information
        """
        try:
            # This would typically be called during interface setup
            # For now, return basic info
            return {
                'webhook_url': notification_url,
                'folder_id': self.folder_id,
                'workspace': self.workspace,
                'status': 'configured'
            }
            
        except Exception as e:
            logger.error(f"Failed to setup webhook: {e}")
            raise
    
    def _send_error_notification(self, message: str):
        """Send error notification"""
        operation = DocumentOperation(
            operation_type=OperationType.ERROR,
            source=DataSource.GOOGLE_DRIVE,
            document_id=f"error_{self.workspace}",
            workspace=self.workspace,
            metadata={'error_message': message}
        )
        
        event = NotificationEvent(
            event_id=self._generate_event_id(),
            operation=operation,
            level=NotificationLevel.ERROR,
            message=message,
            details={'workspace': self.workspace, 'folder_id': self.folder_id}
        )
        
        self.notification_manager.notify(event)
    
    def _generate_event_id(self) -> str:
        """Generate unique event ID"""
        import uuid
        return str(uuid.uuid4())
    
    def cleanup(self):
        """Clean up resources"""
        try:
            # Unsubscribe from all active subscriptions
            for channel_id in self._active_subscriptions.copy():
                self.unsubscribe_from_folder(channel_id)
            
            logger.info(f"Cleaned up Google Drive interface for workspace {self.workspace}")
            
        except Exception as e:
            logger.error(f"Failed to cleanup Google Drive interface: {e}")
    
    # Implementation of abstract methods from BaseDocumentInterface
    
    def add_document(self, content: str, metadata: Dict[str, Any], document_id: Optional[str] = None) -> str:
        """Add a document - not supported for Google Drive (read-only)"""
        raise NotImplementedError("Adding documents to Google Drive is not supported in this interface")
    
    def update_document(self, document_id: str, content: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Update a document using Google Drive API"""
        if content is not None:
            return self.update_file(document_id, content)
        return True
    
    def delete_document(self, document_id: str) -> bool:
        """Delete a document - not supported (safety feature)"""
        raise NotImplementedError("Deleting documents from Google Drive is not supported for safety")
    
    def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get document metadata from Google Drive"""
        return self.drive_client.get_file_metadata(document_id)
    
    def search_documents(self, query) -> List:
        """Search documents in Google Drive"""
        if hasattr(query, 'query'):
            search_text = query.query
        else:
            search_text = str(query)
        
        return self.drive_client.search_files(search_text, max_results=10)
    
    def bulk_add_documents(self, documents: List[Dict[str, Any]]):
        """Bulk add documents - not supported"""
        raise NotImplementedError("Bulk adding documents to Google Drive is not supported")
    
    def bulk_update_documents(self, updates: List[Dict[str, Any]]):
        """Bulk update documents - not supported"""
        raise NotImplementedError("Bulk updating documents in Google Drive is not supported")
    
    def bulk_delete_documents(self, document_ids: List[str]):
        """Bulk delete documents - not supported"""
        raise NotImplementedError("Bulk deleting documents from Google Drive is not supported")
    
    def get_workspace_stats(self) -> Dict[str, Any]:
        """Get workspace statistics"""
        subscription_status = self.get_subscription_status()
        folder_contents = self.get_folder_contents()
        
        return {
            'total_files': len(folder_contents),
            'active_subscriptions': subscription_status.get('active_subscriptions', 0),
            'folder_id': subscription_status.get('folder_id'),
            'last_sync': subscription_status.get('last_sync'),
            'authenticated': subscription_status.get('authenticated', False),
            'workspace': self.workspace,
            'source': 'google_drive'
        }
    
    def sync_workspace(self, force: bool = False) -> Dict[str, Any]:
        """Sync workspace by getting current folder contents"""
        try:
            folder_contents = self.get_folder_contents()
            
            return {
                'status': 'success',
                'files_found': len(folder_contents),
                'timestamp': datetime.now().isoformat(),
                'workspace': self.workspace,
                'folder_id': self.folder_id,
                'force': force
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'workspace': self.workspace
            }

    # ============================================================================
    # FILE FILTERING METHODS (NEW IMPLEMENTATION)
    # ============================================================================
    
    def _should_process_file(self, file_metadata: Dict) -> bool:
        """
        Determine if a file should be processed based on user preferences.
        Follows the priority order from requirements:
        1. Explicit file exclusion (highest priority) 
        2. Explicit file inclusion
        3. Pattern-based exclusion rules
        4. Folder-level default (include by default)
        
        Args:
            file_metadata: Google Drive file metadata
            
        Returns:
            True if file should be processed, False otherwise
        """
        # Import here to avoid circular imports
        try:
            from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
        except ImportError:
            # Fallback if models not available (during development/testing)
            logger.warning("GoogleDrive models not available, defaulting to include all files")
            return True
            
        try:
            # Get workspace for this interface
            workspace = GoogleDriveWorkspace.objects.filter(
                workspace=self.workspace,
                is_active=True
            ).first()
            
            if not workspace:
                logger.warning(f"No active workspace found for {self.workspace}, defaulting to include")
                return True
            
            file_id = file_metadata.get('id')
            file_name = file_metadata.get('name', '')
            parents = file_metadata.get('parents', [])
            
            # Get all preferences for this workspace
            preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace)
            
            # 1. Check explicit file exclusion (highest priority)
            explicit_exclusion = preferences.filter(
                file_id=file_id,
                preference_type='EXPLICIT',
                is_included=False
            ).first()
            
            if explicit_exclusion:
                logger.debug(f"File {file_name} explicitly excluded")
                return False
            
            # 2. Check explicit file inclusion
            explicit_inclusion = preferences.filter(
                file_id=file_id,
                preference_type='EXPLICIT', 
                is_included=True
            ).first()
            
            if explicit_inclusion:
                logger.debug(f"File {file_name} explicitly included")
                return True
            
            # 3. Check pattern-based exclusion rules
            pattern_exclusions = preferences.filter(
                preference_type='PATTERN',
                is_included=False
            )
            
            for pattern_pref in pattern_exclusions:
                if pattern_pref.pattern_rule and self._matches_pattern(file_name, pattern_pref.pattern_rule):
                    logger.debug(f"File {file_name} excluded by pattern: {pattern_pref.pattern_rule}")
                    return False
            
            # 4. Check pattern-based inclusion rules
            pattern_inclusions = preferences.filter(
                preference_type='PATTERN',
                is_included=True
            )
            
            for pattern_pref in pattern_inclusions:
                if pattern_pref.pattern_rule and self._matches_pattern(file_name, pattern_pref.pattern_rule):
                    logger.debug(f"File {file_name} included by pattern: {pattern_pref.pattern_rule}")
                    return True
            
            # 5. Check folder-level defaults
            for parent_id in parents:
                folder_default = preferences.filter(
                    folder_id=parent_id,
                    preference_type='FOLDER_DEFAULT'
                ).first()
                
                if folder_default:
                    logger.debug(f"File {file_name} {'included' if folder_default.is_included else 'excluded'} by folder default")
                    return folder_default.is_included
            
            # 6. Default behavior: include all files
            logger.debug(f"File {file_name} included by default")
            return True
            
        except Exception as e:
            logger.error(f"Error checking file preferences for {file_metadata.get('name', 'unknown')}: {e}")
            # On error, default to including the file
            return True
    
    def _matches_pattern(self, filename: str, pattern: str) -> bool:
        """
        Check if filename matches a pattern rule.
        Supports basic wildcard patterns (* and ?).
        
        Args:
            filename: Name of the file
            pattern: Pattern to match against
            
        Returns:
            True if filename matches pattern, False otherwise
        """
        try:
            # Convert shell-style wildcards to regex
            # Escape special regex chars except * and ?
            escaped = re.escape(pattern)
            # Convert \* to .* and \? to .
            regex_pattern = escaped.replace(r'\*', '.*').replace(r'\?', '.')
            
            # Case-insensitive matching
            return bool(re.match(f"^{regex_pattern}$", filename, re.IGNORECASE))
            
        except Exception as e:
            logger.error(f"Error matching pattern '{pattern}' against '{filename}': {e}")
            return False
    
    def get_folder_files_with_preferences(self, folder_id: Optional[str] = None) -> List[Dict]:
        """
        Get files in a folder with their current preference settings.
        
        Args:
            folder_id: Google Drive folder ID (defaults to workspace folder)
            
        Returns:
            List of file dictionaries with preference information
        """
        try:
            target_folder_id = folder_id or self.folder_id
            files = self.drive_client.list_files(folder_id=target_folder_id)
            
            # Import here to avoid circular imports
            from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
            
            # Get workspace
            workspace = GoogleDriveWorkspace.objects.filter(
                workspace=self.workspace,
                is_active=True
            ).first()
            
            if not workspace:
                # Return files without preference info
                return [{**file, 'is_included': False, 'has_preference': False} for file in files]
            
            # Get existing preferences
            preferences = {
                pref.file_id: pref for pref in 
                GoogleDriveFilePreference.objects.filter(workspace=workspace)
                if pref.file_id
            }
            
            # Add preference information to each file
            files_with_preferences = []
            for file in files:
                file_id = file['id']
                preference = preferences.get(file_id)
                
                file_with_pref = {
                    **file,
                    'is_included': preference.is_included if preference else False,  # Default to excluded
                    'has_preference': preference is not None
                }
                files_with_preferences.append(file_with_pref)
            
            return files_with_preferences
            
        except Exception as e:
            logger.error(f"Error getting folder files with preferences: {e}")
            return []
    
    def update_file_preference(self, file_id: str, file_name: str, is_included: bool) -> bool:
        """
        Update preference for a specific file.
        
        Args:
            file_id: Google Drive file ID
            file_name: Human-readable file name
            is_included: Whether to include (True) or exclude (False) the file
            
        Returns:
            True if preference was updated successfully, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
            
            workspace = GoogleDriveWorkspace.objects.filter(
                workspace=self.workspace,
                is_active=True
            ).first()
            
            if not workspace:
                logger.error(f"No active workspace found for {self.workspace}")
                return False
            
            # Create or update file preference
            preference, created = GoogleDriveFilePreference.objects.update_or_create(
                workspace=workspace,
                file_id=file_id,
                defaults={
                    'file_path': file_name,
                    'is_included': is_included,
                    'preference_type': 'EXPLICIT',
                    'folder_id': self.folder_id or 'root'
                }
            )
            
            action = "created" if created else "updated"
            logger.info(f"File preference {action} for {file_name}: {'include' if is_included else 'exclude'}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating file preference for {file_name}: {e}")
            return False
    
    def update_folder_preference(self, folder_id: str, folder_name: str, is_included: bool) -> bool:
        """
        Update default preference for a folder.
        
        Args:
            folder_id: Google Drive folder ID
            folder_name: Human-readable folder name  
            is_included: Whether to include (True) or exclude (False) files in this folder by default
            
        Returns:
            True if preference was updated successfully, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
            
            workspace = GoogleDriveWorkspace.objects.filter(
                workspace=self.workspace,
                is_active=True
            ).first()
            
            if not workspace:
                logger.error(f"No active workspace found for {self.workspace}")
                return False
            
            # Create or update folder preference
            preference, created = GoogleDriveFilePreference.objects.update_or_create(
                workspace=workspace,
                folder_id=folder_id,
                file_id=None,  # Folder rules don't have file IDs
                defaults={
                    'file_path': f"Folder: {folder_name}",
                    'is_included': is_included,
                    'preference_type': 'FOLDER_DEFAULT'
                }
            )
            
            action = "created" if created else "updated"
            logger.info(f"Folder preference {action} for {folder_name}: {'include' if is_included else 'exclude'} by default")
            return True
            
        except Exception as e:
            logger.error(f"Error updating folder preference for {folder_name}: {e}")
            return False
    
    def add_pattern_rule(self, pattern: str, is_included: bool, description: str = None) -> bool:
        """
        Add a pattern-based inclusion/exclusion rule.
        
        Args:
            pattern: Wildcard pattern (e.g., "*.pdf", "temp_*", "*.tmp")
            is_included: Whether files matching this pattern should be included (True) or excluded (False)
            description: Optional description of the rule
            
        Returns:
            True if rule was added successfully, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
            
            workspace = GoogleDriveWorkspace.objects.filter(
                workspace=self.workspace,
                is_active=True
            ).first()
            
            if not workspace:
                logger.error(f"No active workspace found for {self.workspace}")
                return False
            
            # Create pattern rule
            rule_description = description or f"Pattern: {pattern}"
            preference = GoogleDriveFilePreference.objects.create(
                workspace=workspace,
                file_id=None,  # Pattern rules don't have specific file IDs
                folder_id=self.folder_id or 'root',
                file_path=rule_description,
                is_included=is_included,
                preference_type='PATTERN',
                pattern_rule=pattern
            )
            
            logger.info(f"Pattern rule added: {pattern} -> {'include' if is_included else 'exclude'}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding pattern rule {pattern}: {e}")
            return False
    
    def _process_folder_change(self, folder_contents: List[Dict], change_type: str = 'unknown'):
        """
        Process folder changes with file filtering.
        Only processes files that pass the _should_process_file check.
        
        Args:
            folder_contents: List of file metadata from Google Drive
            change_type: Type of change that triggered this processing
        """
        try:
            processed_count = 0
            skipped_count = 0
            
            for file in folder_contents:
                if self._should_process_file(file):
                    # Process the file (integrate with existing processing logic)
                    self._process_single_file(file, change_type)
                    processed_count += 1
                    
                    # Log the processing
                    self._log_file_processing(file, 'processed', None)
                else:
                    skipped_count += 1
                    logger.debug(f"Skipped file {file.get('name')} due to user preferences")
                    
                    # Log the skip
                    self._log_file_processing(file, 'skipped', 'user_excluded')
            
            logger.info(f"Folder change processed: {processed_count} files processed, {skipped_count} files skipped")
            
        except Exception as e:
            logger.error(f"Error processing folder change: {e}")
    
    def _process_single_file(self, file_metadata: Dict, change_type: str):
        """
        Process a single file (placeholder for actual file processing logic).
        This would integrate with your vector database and content extraction.
        
        Args:
            file_metadata: Google Drive file metadata
            change_type: Type of change that triggered this processing
        """
        # This is a placeholder - integrate with existing file processing logic
        file_name = file_metadata.get('name', 'unknown')
        logger.info(f"Processing file: {file_name} (change: {change_type})")
        
        # TODO: Add actual file processing logic here:
        # 1. Extract file content using drive_client.get_file_content()
        # 2. Process content for vector database
        # 3. Update search indexes
        # 4. Send notifications if needed
    
    def _log_file_processing(self, file_metadata: Dict, action: str, reason: str = None):
        """
        Log file processing for audit trail.
        
        Args:
            file_metadata: Google Drive file metadata
            action: Action taken (processed, skipped, error, deleted)
            reason: Optional reason for the action
        """
        try:
            # Import here to avoid circular imports
            from integrations.google_drive.models import GoogleDriveWorkspace
            
            workspace = GoogleDriveWorkspace.objects.filter(
                workspace=self.workspace,
                is_active=True
            ).first()
            
            if workspace:
                # Log processing action (removed GoogleDriveProcessingLog)
                logger.info(f"File processing: {file_metadata.get('name', '')} - {action} - {reason}")
            
        except Exception as e:
            logger.error(f"Error logging file processing: {e}")


def create_google_drive_interface(workspace: str, 
                                folder_id: str, 
                                credentials_file: str = None,
                                token_file: Optional[str] = None) -> GoogleDriveInterface:
    """
    Factory function to create Google Drive interface
    
    Args:
        workspace: Workspace identifier
        folder_id: Google Drive folder ID to monitor
        credentials_file: Path to Google OAuth credentials file (defaults to same directory)
        token_file: Path to store access token (optional)
        
    Returns:
        Configured GoogleDriveInterface instance
    """
    return GoogleDriveInterface(
        workspace=workspace,
        folder_id=folder_id,
        credentials_file=credentials_file,
        token_file=token_file
    )