import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View

from .google_drive_interface import GoogleDriveInterface
from ..notification_manager import get_notification_manager
from ..base_interface import NotificationLevel, OperationType, DocumentOperation

logger = logging.getLogger(__name__)


class GoogleDriveWebhookHandler:
    """
    Handler for Google Drive webhook notifications.
    
    This class processes incoming webhook notifications from Google Drive
    and triggers appropriate synchronization actions.
    """
    
    def __init__(self):
        self.notification_manager = get_notification_manager()
        self._interfaces: Dict[str, GoogleDriveInterface] = {}
    
    def register_interface(self, workspace: str, interface: GoogleDriveInterface):
        """
        Register a Google Drive interface for a specific workspace
        
        Args:
            workspace: Workspace identifier
            interface: GoogleDriveInterface instance
        """
        self._interfaces[workspace] = interface
        logger.info(f"Registered Google Drive interface for workspace: {workspace}")
    
    def unregister_interface(self, workspace: str):
        """
        Unregister a Google Drive interface
        
        Args:
            workspace: Workspace identifier
        """
        if workspace in self._interfaces:
            del self._interfaces[workspace]
            logger.info(f"Unregistered Google Drive interface for workspace: {workspace}")
    
    def process_notification(self, 
                           headers: Dict[str, str], 
                           body: str,
                           workspace: Optional[str] = None) -> Dict[str, Any]:
        """
        Process incoming webhook notification
        
        Args:
            headers: HTTP headers from the webhook request
            body: Request body
            workspace: Optional workspace to target (if None, notify all)
            
        Returns:
            Processing result
        """
        try:
            # Extract webhook information
            channel_id = headers.get('X-Goog-Channel-ID', '')
            resource_id = headers.get('X-Goog-Resource-ID', '')
            change_type = headers.get('X-Goog-Resource-State', 'unknown')
            message_number = headers.get('X-Goog-Message-Number', '0')
            
            logger.info(f"Processing Google Drive webhook: {change_type} (channel: {channel_id})")
            
            # Process for specific workspace or all registered workspaces
            results = {}
            
            if workspace and workspace in self._interfaces:
                # Process for specific workspace
                interface = self._interfaces[workspace]
                result = interface.process_webhook_notification(headers, body)
                results[workspace] = result
                
                # Send notification
                self._send_change_notification(workspace, change_type, result)
                
            else:
                # Process for all workspaces
                for ws, interface in self._interfaces.items():
                    try:
                        result = interface.process_webhook_notification(headers, body)
                        results[ws] = result
                        
                        # Send notification
                        self._send_change_notification(ws, change_type, result)
                        
                    except Exception as e:
                        error_msg = f"Failed to process webhook for workspace {ws}: {str(e)}"
                        logger.error(error_msg)
                        results[ws] = {'status': 'error', 'error': str(e)}
            
            return {
                'status': 'success',
                'channel_id': channel_id,
                'change_type': change_type,
                'message_number': message_number,
                'workspaces_processed': len(results),
                'results': results
            }
            
        except Exception as e:
            error_msg = f"Failed to process Google Drive webhook: {str(e)}"
            logger.error(error_msg)
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _send_change_notification(self, 
                                workspace: str, 
                                change_type: str, 
                                result: Dict[str, Any]):
        """
        Send notification about the webhook processing
        
        Args:
            workspace: Workspace that was processed
            change_type: Type of change from Google Drive
            result: Processing result
        """
        try:
            from vectordb.models.document import DataSource
            
            # Create operation record
            operation = DocumentOperation(
                operation_type=OperationType.UPDATE,  # Webhook represents a change/update
                source=DataSource.GOOGLE_DRIVE,
                document_id=f"webhook_{change_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                workspace=workspace,
                metadata={
                    'change_type': change_type,
                    'webhook_result': result
                }
            )
            
            # Determine notification level based on result
            if result.get('status') == 'error':
                level = NotificationLevel.ERROR
                message = f"Google Drive webhook processing failed for {workspace}"
            elif result.get('status') == 'sync':
                level = NotificationLevel.INFO
                message = f"Google Drive sync notification received for {workspace}"
            else:
                level = NotificationLevel.SUCCESS
                message = f"Google Drive changes processed for {workspace}"
            
            # Send notification through notification manager
            from ..base_interface import NotificationEvent
            import uuid
            
            event = NotificationEvent(
                event_id=str(uuid.uuid4()),
                operation=operation,
                level=level,
                message=message,
                details=result
            )
            
            self.notification_manager.notify(event)
            
        except Exception as e:
            logger.error(f"Failed to send change notification: {e}")


# Global webhook handler instance
_webhook_handler = GoogleDriveWebhookHandler()


def get_webhook_handler() -> GoogleDriveWebhookHandler:
    """Get the global webhook handler instance"""
    return _webhook_handler


@method_decorator(csrf_exempt, name='dispatch')
class GoogleDriveWebhookView(View):
    """
    Django view for handling Google Drive webhook notifications
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.webhook_handler = get_webhook_handler()
    
    def post(self, request: HttpRequest, workspace: Optional[str] = None) -> HttpResponse:
        """
        Handle POST requests from Google Drive webhooks
        
        Args:
            request: Django HTTP request
            workspace: Optional workspace identifier from URL
            
        Returns:
            HTTP response
        """
        try:
            # Extract headers
            headers = {
                'X-Goog-Channel-ID': request.headers.get('X-Goog-Channel-ID', ''),
                'X-Goog-Resource-ID': request.headers.get('X-Goog-Resource-ID', ''),
                'X-Goog-Resource-State': request.headers.get('X-Goog-Resource-State', ''),
                'X-Goog-Message-Number': request.headers.get('X-Goog-Message-Number', '0'),
                'X-Goog-Channel-Token': request.headers.get('X-Goog-Channel-Token', ''),
                'X-Goog-Channel-Expiration': request.headers.get('X-Goog-Channel-Expiration', ''),
            }
            
            # Get request body
            body = request.body.decode('utf-8') if request.body else ''
            
            # Process notification
            result = self.webhook_handler.process_notification(headers, body, workspace)
            
            # Log the processing result
            logger.info(f"Google Drive webhook processed: {result}")
            
            # Return success response
            return JsonResponse({
                'status': 'success',
                'message': 'Webhook processed successfully',
                'result': result
            })
            
        except Exception as e:
            error_msg = f"Failed to handle Google Drive webhook: {str(e)}"
            logger.error(error_msg)
            
            return JsonResponse({
                'status': 'error',
                'message': error_msg
            }, status=500)
    
    def get(self, request: HttpRequest, workspace: Optional[str] = None) -> HttpResponse:
        """
        Handle GET requests (webhook verification)
        
        Some webhook systems send GET requests for verification.
        """
        return JsonResponse({
            'status': 'ok',
            'message': 'Google Drive webhook endpoint is active',
            'timestamp': datetime.now().isoformat()
        })


# Utility functions for webhook management

def register_workspace_webhook(workspace: str, 
                             interface: GoogleDriveInterface,
                             notification_url: str) -> Dict[str, Any]:
    """
    Register a workspace for webhook notifications
    
    Args:
        workspace: Workspace identifier
        interface: GoogleDriveInterface instance
        notification_url: URL for receiving notifications
        
    Returns:
        Webhook setup information
    """
    try:
        # Register with handler
        handler = get_webhook_handler()
        handler.register_interface(workspace, interface)
        
        # Set up webhook with Google Drive
        webhook_info = interface.setup_webhook(notification_url)
        
        logger.info(f"Registered webhook for workspace {workspace}: {webhook_info}")
        
        return {
            'status': 'success',
            'workspace': workspace,
            'webhook_info': webhook_info
        }
        
    except Exception as e:
        error_msg = f"Failed to register webhook for workspace {workspace}: {str(e)}"
        logger.error(error_msg)
        return {
            'status': 'error',
            'workspace': workspace,
            'error': str(e)
        }


def unregister_workspace_webhook(workspace: str) -> Dict[str, Any]:
    """
    Unregister a workspace from webhook notifications
    
    Args:
        workspace: Workspace identifier
        
    Returns:
        Unregistration result
    """
    try:
        handler = get_webhook_handler()
        handler.unregister_interface(workspace)
        
        logger.info(f"Unregistered webhook for workspace {workspace}")
        
        return {
            'status': 'success',
            'workspace': workspace,
            'message': 'Webhook unregistered successfully'
        }
        
    except Exception as e:
        error_msg = f"Failed to unregister webhook for workspace {workspace}: {str(e)}"
        logger.error(error_msg)
        return {
            'status': 'error',
            'workspace': workspace,
            'error': str(e)
        }


def get_webhook_status() -> Dict[str, Any]:
    """
    Get status of all registered webhooks
    
    Returns:
        Status information for all webhooks
    """
    try:
        handler = get_webhook_handler()
        
        status = {
            'active_workspaces': list(handler._interfaces.keys()),
            'total_webhooks': len(handler._interfaces),
            'handler_status': 'active',
            'timestamp': datetime.now().isoformat()
        }
        
        # Get individual workspace status
        workspace_status = {}
        for workspace, interface in handler._interfaces.items():
            workspace_status[workspace] = {
                'authenticated': interface.is_authenticated(),
                'folder_id': interface.folder_id,
                'last_sync': getattr(interface, '_last_sync_time', None)
            }
        
        status['workspaces'] = workspace_status
        
        return status
        
    except Exception as e:
        logger.error(f"Failed to get webhook status: {e}")
        return {
            'handler_status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }