# Google Drive Real-time Webhook Setup Guide

This guide will help you set up Google Drive real-time webhook notifications for instant file change detection (no polling).

## 🎯 What You'll Achieve

- **Real-time notifications** when files are added, modified, deleted, or renamed
- **No polling** - true push notifications from Google Drive
- **Instant alerts** (typically within 30 seconds of changes)
- **Professional webhook system** for production use

## 📋 Prerequisites

- Python environment with Django
- Google Cloud Platform account
- ngrok account (free tier works)
- Access to a Google Drive folder

## 🔧 Step 1: Google Cloud Setup

### 1.1 Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "New Project" or select existing project
3. Note your project ID

### 1.2 Enable Google Drive API
1. In Google Cloud Console, navigate to "APIs & Services" > "Library"
2. Search for "Google Drive API"
3. Click on it and press "Enable"

### 1.3 Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. **Application type**: Desktop application
4. **Name**: Give it a descriptive name (e.g., "SageBase Google Drive Integration")
5. Click "Create"
6. **Download the JSON file** and save as `interfaces_data/google_drive_credentials.json`

### 1.4 Configure OAuth Consent Screen
1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in required fields:
   - **App name**: Your application name
   - **User support email**: Your email
   - **Developer contact**: Your email
4. **Add scopes**: Add these Google Drive scopes:
   - `https://www.googleapis.com/auth/drive`
   - `https://www.googleapis.com/auth/drive.metadata`
5. **Add test users** (IMPORTANT):
   - Scroll to "Test users" section
   - Click "ADD USERS"
   - **Add your email address** and any colleague emails
   - Save

> **🔑 Critical**: If you don't add test users, you'll get "Error 403: access_denied"

## 🔧 Step 2: Environment Setup

### 2.1 Install Dependencies
```bash
pip install google-auth google-auth-oauthlib google-api-python-client django
```

### 2.2 Install ngrok
```bash
# Mac
brew install ngrok

# Or download from https://ngrok.com/
# Create free account and get auth token
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

### 2.3 Get Google Drive Folder ID
1. Open Google Drive in browser
2. Navigate to the folder you want to monitor
3. Copy the folder ID from the URL:
   ```
   https://drive.google.com/drive/folders/YOUR_FOLDER_ID_HERE
   ```
4. Note this folder ID - you'll need it later

## 🚀 Step 3: Setup Webhook System

### 3.1 Verify Setup
```bash
# Check all components are working
python interfaces_data/test_google_dry_run.py
```
You should see: `✅ All checks passed!`

### 3.2 Set Up Authentication
```bash
# This will open browser for OAuth consent
python interfaces_data/setup_google_auth.py
```

**Important**: 
- Sign in with the Google account you added as test user
- Grant all requested permissions
- If you get "Error 403: access_denied", go back to Step 1.4

### 3.3 Start Webhook Server
In **Terminal 1**:
```bash
python interfaces_data/webhook_server.py
```
You should see:
```
🚀 GOOGLE DRIVE WEBHOOK SERVER
✅ Server started successfully!
```
**Keep this running!**

### 3.4 Start ngrok Tunnel  
In **Terminal 2**:
```bash
ngrok http 8081
```
Copy the **HTTPS URL** (e.g., `https://abc123.ngrok-free.app`)
**Keep this running!**

### 3.5 Test Webhook Endpoint
In **Terminal 3**:
```bash
python interfaces_data/test_webhook_endpoint.py https://your-ngrok-url.ngrok-free.app
```
You should see:
```
✅ Health endpoint is working!
✅ Webhook endpoint is working!
```

### 3.6 Create Webhook Subscription
```bash
python interfaces_data/create_webhook_fixed.py
```

Update the script with your folder ID and ngrok URL:
```python
# Edit these values in the script:
folder_id = "YOUR_FOLDER_ID_HERE"  
webhook_url = "https://your-ngrok-url.ngrok-free.app/webhook/"
```

You should see:
```
✅ NEW subscription is active!
🔔 Channel ID: folder_YOUR_FOLDER_ID_user_123_abc123
```

## 🧪 Step 4: Test Real-time Notifications

1. **Keep webhook server running** (Terminal 1)
2. **Keep ngrok running** (Terminal 2)
3. **Open your Google Drive folder** in browser
4. **Make changes**:
   - Upload a new file
   - Edit an existing file
   - Delete a file
   - Rename a file

5. **Watch webhook server terminal** for notifications:
```bash
[21:05:42] 📨 WEBHOOK RECEIVED!
   🔖 Google Headers:
      • X-Goog-Channel-Id: folder_YOUR_FOLDER_ID_user_123_abc123
      • X-Goog-Resource-State: sync
   ✅ Processing result: success
```

## ⚡ Quick Setup Script

For faster setup, create this script (`quick_setup.sh`):

```bash
#!/bin/bash
echo "🚀 Starting Google Drive Webhook Setup..."

# Terminal 1: Start webhook server
osascript -e 'tell app "Terminal" to do script "cd $(pwd) && python interfaces_data/webhook_server.py"'

# Wait a moment
sleep 2

# Terminal 2: Start ngrok
osascript -e 'tell app "Terminal" to do script "cd $(pwd) && ngrok http 8081"'

echo "✅ Servers started!"
echo "📋 Next steps:"
echo "1. Copy ngrok HTTPS URL"
echo "2. Update create_webhook_fixed.py with your folder ID and ngrok URL"
echo "3. Run: python interfaces_data/create_webhook_fixed.py"
```

## 🔧 Configuration for Different Folders

To monitor different folders, create separate configurations:

```python
# For each colleague/folder, use different workspace and folder_id
configurations = {
    "colleague1": {
        "workspace": "colleague1_workspace",
        "folder_id": "1ABC123...",
        "token_file": "gdrive_colleague1_token.json"
    },
    "colleague2": {
        "workspace": "colleague2_workspace", 
        "folder_id": "1XYZ789...",
        "token_file": "gdrive_colleague2_token.json"
    }
}
```

## 🚨 Troubleshooting

### Issue: "Error 403: access_denied"
**Solution**: 
1. Go to Google Cloud Console > OAuth consent screen
2. Add your email to "Test users"
3. Or click "Publish app" to make it public

### Issue: "No webhook notifications"
**Solutions**:
1. Check webhook server is running
2. Verify ngrok tunnel is active
3. Confirm subscription was created: `python interfaces_data/debug_webhook.py`
4. Wait 1-5 minutes (Google Drive webhook delay)

### Issue: "Webhook endpoint returning 400"
**Solutions**:
1. Ensure webhook server is running on port 8081
2. Use correct ngrok URL with `/webhook/` path
3. Add `ngrok-skip-browser-warning: true` header

### Issue: "Subscription not found"
**Solution**:
```bash
# Clean up and recreate
python interfaces_data/create_webhook_fixed.py
```

## 📊 Monitoring and Production

### Check Webhook Status
```bash
python interfaces_data/debug_webhook.py
```

### View Notification History
```python
from interfaces_data.notification_manager import get_notification_manager

notification_manager = get_notification_manager()
events = notification_manager.get_event_history(limit=10)
for event in events:
    print(f"{event.timestamp}: {event.message}")
```

### Production Deployment
1. **Replace ngrok** with permanent HTTPS endpoint
2. **Set environment variables**:
   ```bash
   export GOOGLE_DRIVE_WEBHOOK_URL=https://your-domain.com/webhook/
   export GOOGLE_DRIVE_CREDENTIALS_FILE=/secure/path/credentials.json
   ```
3. **Use process manager** (systemd, supervisor, PM2)
4. **Set up monitoring** and alerting
5. **Configure SSL certificates**

## 🔒 Security Best Practices

1. **Store credentials securely** - never commit to version control
2. **Use environment variables** for sensitive data
3. **Implement webhook authentication** for production
4. **Rotate OAuth tokens** regularly
5. **Monitor webhook endpoints** for unauthorized access
6. **Use HTTPS only** for webhook URLs

## 📚 Additional Resources

- [Google Drive API Documentation](https://developers.google.com/drive/api)
- [OAuth 2.0 Setup Guide](https://developers.google.com/identity/protocols/oauth2)
- [ngrok Documentation](https://ngrok.com/docs)

## 🆘 Support

If you encounter issues:
1. Run diagnostic script: `python interfaces_data/test_google_dry_run.py`
2. Check webhook status: `python interfaces_data/debug_webhook.py`
3. Verify all prerequisites are met
4. Check Google Cloud Console for API quotas and errors

---

**🎉 Congratulations!** You now have a fully functional Google Drive real-time webhook notification system!