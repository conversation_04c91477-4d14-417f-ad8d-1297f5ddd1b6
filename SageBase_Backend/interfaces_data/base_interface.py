from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from vectordb.models.document import DataSource, DocumentMetadata, SearchResult, SearchQuery


class OperationType(str, Enum):
    """Types of operations that can be performed on documents"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    READ = "read"
    ERROR = "error"
    BULK_CREATE = "bulk_create"
    BULK_UPDATE = "bulk_update"
    BULK_DELETE = "bulk_delete"


class NotificationLevel(str, Enum):
    """Notification levels for document changes"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


@dataclass
class DocumentOperation:
    """Represents a document operation"""
    operation_type: OperationType
    source: DataSource
    document_id: str
    workspace: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    content: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class NotificationEvent:
    """Represents a notification event for document changes"""
    event_id: str
    operation: DocumentOperation
    level: NotificationLevel
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class BulkOperationResult:
    """Result of a bulk operation"""
    total_processed: int
    successful: int
    failed: int
    errors: List[str]
    document_ids: List[str]
    operation_type: OperationType
    source: DataSource
    workspace: Optional[str] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage"""
        if self.total_processed == 0:
            return 0.0
        return (self.successful / self.total_processed) * 100


class BaseDocumentInterface(ABC):
    """
    Base interface for all document management integrations.
    
    This interface defines the common operations that all document sources
    (Google Drive, Notion, Confluence, etc.) must implement.
    Collection name is the UID of the company
    """
    
    def __init__(self, source: DataSource, collection_name: Optional[str] = None):
        self.source = source
        self.collection_name = collection_name
        self._notification_callbacks: List[callable] = []
    
    @abstractmethod
    def add_document(self, 
                    collection_name: str,
                    content: str, 
                    metadata: Dict[str, Any],
                    document_id: Optional[str] = None) -> str:
        """
        Add a single document to the system
        
        Args:
            collection_name: uid of the company
            content: Document content
            metadata: Document metadata including title, author, etc.
            document_id: Optional document ID
            
        Returns:
            Document ID of the added document
            
        Raises:
            DocumentOperationError: If the operation fails
        """
        pass
    
    @abstractmethod
    def update_document(self, 
                       collection_name: str,
                       document_id: str,
                       content: Optional[str] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Update an existing document
        
        Args:
            collection_name: uid of the company
            document_id: Document ID to update
            content: New content (optional)
            metadata: New metadata (optional)
            
        Returns:
            True if successful, False otherwise
            
        Raises:
            DocumentOperationError: If the operation fails
        """
        pass
    
    @abstractmethod
    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document from the system
        
        Args:
            document_id: Document ID to delete
            
        Returns:
            True if successful, False otherwise
            
        Raises:
            DocumentOperationError: If the operation fails
        """
        pass
    
    @abstractmethod
    def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a document by ID
        
        Args:
            document_id: Document ID
            
        Returns:
            Document data if found, None otherwise
        """
        pass
    
    @abstractmethod
    def search_documents(self, query: SearchQuery) -> List[SearchResult]:
        """
        Search for documents
        
        Args:
            query: Search query with filters
            
        Returns:
            List of search results
        """
        pass
    
    @abstractmethod
    def bulk_add_documents(self, documents: List[Dict[str, Any]]) -> BulkOperationResult:
        """
        Add multiple documents in a single operation
        
        Args:
            documents: List of documents with content and metadata
            
        Returns:
            Result of bulk operation
        """
        pass
    
    @abstractmethod
    def bulk_update_documents(self, updates: List[Dict[str, Any]]) -> BulkOperationResult:
        """
        Update multiple documents in a single operation
        
        Args:
            updates: List of document updates with IDs and new data
            
        Returns:
            Result of bulk operation
        """
        pass
    
    @abstractmethod
    def bulk_delete_documents(self, document_ids: List[str]) -> BulkOperationResult:
        """
        Delete multiple documents in a single operation
        
        Args:
            document_ids: List of document IDs to delete
            
        Returns:
            Result of bulk operation
        """
        pass
    
    @abstractmethod
    def get_workspace_stats(self) -> Dict[str, Any]:
        """
        Get statistics for the workspace
        
        Returns:
            Dictionary with workspace statistics
        """
        pass
    
    @abstractmethod
    def sync_workspace(self, force: bool = False) -> Dict[str, Any]:
        """
        Synchronize the workspace with the external source
        
        Args:
            force: Force full sync even if not needed
            
        Returns:
            Sync result information
        """
        pass
    
    def register_notification_callback(self, callback: callable):
        """
        Register a callback for document change notifications
        
        Args:
            callback: Function to call when document changes occur
                     Should accept NotificationEvent as parameter
        """
        self._notification_callbacks.append(callback)
    
    def unregister_notification_callback(self, callback: callable):
        """
        Unregister a notification callback
        
        Args:
            callback: Function to remove from callbacks
        """
        if callback in self._notification_callbacks:
            self._notification_callbacks.remove(callback)
    
    def _notify_change(self, operation: DocumentOperation, 
                      level: NotificationLevel = NotificationLevel.INFO,
                      message: str = "",
                      details: Optional[Dict[str, Any]] = None):
        """
        Send notification about document changes
        
        Args:
            operation: Document operation that occurred
            level: Notification level
            message: Notification message
            details: Additional details
        """
        import uuid
        
        event = NotificationEvent(
            event_id=str(uuid.uuid4()),
            operation=operation,
            level=level,
            message=message,
            details=details
        )
        
        for callback in self._notification_callbacks:
            try:
                callback(event)
            except Exception as e:
                # Log error but don't break other callbacks
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Notification callback failed: {e}")
    
    def validate_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and normalize metadata
        
        Args:
            metadata: Raw metadata dictionary
            
        Returns:
            Validated and normalized metadata
            
        Raises:
            ValueError: If required fields are missing
        """
        required_fields = ['title']
        
        for field in required_fields:
            if field not in metadata:
                raise ValueError(f"Required field '{field}' missing from metadata")
        
        # Normalize metadata
        normalized = {
            'source': self.source.value,
            'workspace': self.workspace,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            **metadata
        }
        
        # Ensure lists are properly formatted
        if 'tags' in normalized and isinstance(normalized['tags'], str):
            normalized['tags'] = [tag.strip() for tag in normalized['tags'].split(',')]
        
        if 'permissions' in normalized and isinstance(normalized['permissions'], str):
            normalized['permissions'] = [perm.strip() for perm in normalized['permissions'].split(',')]
        
        return normalized
    
    def create_document_metadata(self, **kwargs) -> DocumentMetadata:
        """
        Create DocumentMetadata object from kwargs
        
        Args:
            **kwargs: Metadata fields
            
        Returns:
            DocumentMetadata object
        """
        validated_metadata = self.validate_metadata(kwargs)
        
        return DocumentMetadata(
            source=self.source,
            source_id=validated_metadata.get('source_id', ''),
            title=validated_metadata['title'],
            author=validated_metadata.get('author'),
            created_at=validated_metadata.get('created_at'),
            updated_at=validated_metadata.get('updated_at'),
            url=validated_metadata.get('url'),
            content_type=validated_metadata.get('content_type', 'text'),
            tags=validated_metadata.get('tags', []),
            parent_id=validated_metadata.get('parent_id'),
            workspace=validated_metadata.get('workspace'),
            permissions=validated_metadata.get('permissions', []),
            custom_fields=validated_metadata.get('custom_fields', {})
        )


class DocumentOperationError(Exception):
    """Exception raised for document operation errors"""
    
    def __init__(self, message: str, operation: str, source: DataSource, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.operation = operation
        self.source = source
        self.details = details or {}
        super().__init__(self.message)