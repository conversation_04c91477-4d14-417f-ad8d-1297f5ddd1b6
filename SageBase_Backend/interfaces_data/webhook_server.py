#!/usr/bin/env python3
"""
Google Drive Webhook Server

This script runs a webhook server that receives real-time notifications from Google Drive.
It's designed to work with ngrok for local testing.

Usage:
======
# Terminal 1: Start the webhook server
python interfaces_data/webhook_server.py

# Terminal 2: Start ngrok tunnel
ngrok http 8080

# Terminal 3: Set up Google Drive subscription with ngrok URL
python interfaces_data/setup_webhook_subscription.py

Features:
- Receives webhook notifications from Google Drive
- Processes file change events in real-time
- Beautiful console output with notifications
- Integration with the notification system
"""

import os
import sys
import json
import signal
import threading
from datetime import datetime
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_Backend.settings')
import django
django.setup()

from interfaces_data.google_drive.webhook_handler import GoogleDriveWebhookHandler
from interfaces_data.google_drive.google_drive_interface import GoogleDriveInterface
from interfaces_data.notification_manager import get_notification_manager


class ColoredOutput:
    """Helper class for colored console output"""
    
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    BOLD = '\033[1m'
    RESET = '\033[0m'
    
    @classmethod
    def success(cls, text):
        return f"{cls.GREEN}{text}{cls.RESET}"
    
    @classmethod
    def error(cls, text):
        return f"{cls.RED}{text}{cls.RESET}"
    
    @classmethod
    def warning(cls, text):
        return f"{cls.YELLOW}{text}{cls.RESET}"
    
    @classmethod
    def info(cls, text):
        return f"{cls.BLUE}{text}{cls.RESET}"
    
    @classmethod
    def highlight(cls, text):
        return f"{cls.BOLD}{cls.CYAN}{text}{cls.RESET}"
    
    @classmethod
    def timestamp(cls):
        return f"{cls.MAGENTA}[{datetime.now().strftime('%H:%M:%S')}]{cls.RESET}"


class GoogleDriveWebhookServer:
    """Webhook server for Google Drive notifications"""
    
    def __init__(self, port=8081):
        self.port = port
        self.server = None
        self.webhook_handler = GoogleDriveWebhookHandler()
        self.notification_manager = get_notification_manager()
        self.requests_received = 0
        self.start_time = datetime.now()
        
        # Set up notification display
        self.setup_notification_display()
    
    def setup_notification_display(self):
        """Set up notification callback to display events"""
        def display_notification(event):
            timestamp = ColoredOutput.timestamp()
            
            if event.operation.source.value == "google_drive":
                print(f"\n{timestamp} 📧 {ColoredOutput.success('DRIVE NOTIFICATION')}")
                print(f"   📝 {event.message}")
                print(f"   🔧 Operation: {event.operation.operation_type.value}")
                print(f"   📁 Workspace: {event.operation.workspace}")
                
                if event.details:
                    print(f"   📋 Details:")
                    for key, value in event.details.items():
                        print(f"      • {key}: {value}")
        
        self.notification_manager.subscribe(
            callback=display_notification,
            subscriber_id="webhook_server_display"
        )
    
    def create_request_handler(self):
        """Create request handler class with access to webhook handler"""
        webhook_handler = self.webhook_handler
        server_instance = self
        
        class WebhookRequestHandler(BaseHTTPRequestHandler):
            def log_message(self, format, *args):
                """Override to use our custom logging"""
                pass  # Suppress default logging
            
            def do_GET(self):
                """Handle GET requests (health check)"""
                server_instance.requests_received += 1
                timestamp = ColoredOutput.timestamp()
                
                print(f"{timestamp} 🔍 {ColoredOutput.info('GET request received')}")
                print(f"   🔗 Path: {self.path}")
                print(f"   🌐 Client: {self.client_address[0]}")
                
                # Send health check response
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                
                response = {
                    'status': 'active',
                    'service': 'Google Drive Webhook Server',
                    'timestamp': datetime.now().isoformat(),
                    'requests_received': server_instance.requests_received,
                    'uptime_seconds': (datetime.now() - server_instance.start_time).total_seconds()
                }
                
                self.wfile.write(json.dumps(response, indent=2).encode())
            
            def do_POST(self):
                """Handle POST requests (webhook notifications)"""
                server_instance.requests_received += 1
                timestamp = ColoredOutput.timestamp()
                
                try:
                    # Read request body
                    content_length = int(self.headers.get('Content-Length', 0))
                    body = self.rfile.read(content_length).decode('utf-8') if content_length > 0 else ''
                    
                    # Extract headers
                    headers = dict(self.headers)
                    
                    print(f"\n{timestamp} 📨 {ColoredOutput.highlight('WEBHOOK RECEIVED!')}")
                    print(f"   🔗 Path: {self.path}")
                    print(f"   🌐 Client: {self.client_address[0]}")
                    print(f"   📏 Content Length: {content_length}")
                    
                    # Show Google-specific headers
                    google_headers = {k: v for k, v in headers.items() if k.startswith('X-Goog-')}
                    if google_headers:
                        print(f"   🔖 Google Headers:")
                        for key, value in google_headers.items():
                            print(f"      • {key}: {value}")
                    
                    # Process webhook
                    workspace = None
                    path_parts = self.path.strip('/').split('/')
                    if len(path_parts) > 1:
                        workspace = path_parts[-1]
                    
                    result = webhook_handler.process_notification(headers, body, workspace)
                    
                    print(f"   ✅ {ColoredOutput.success('Processing result:')}")
                    print(f"      • Status: {result.get('status', 'unknown')}")
                    print(f"      • Channel: {result.get('channel_id', 'unknown')}")
                    print(f"      • Change Type: {result.get('change_type', 'unknown')}")
                    print(f"      • Workspaces: {result.get('workspaces_processed', 0)}")
                    
                    # Send success response
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    
                    response = {
                        'status': 'success',
                        'message': 'Webhook processed successfully',
                        'timestamp': datetime.now().isoformat(),
                        'result': result
                    }
                    
                    self.wfile.write(json.dumps(response, indent=2).encode())
                    
                except Exception as e:
                    print(f"   ❌ {ColoredOutput.error(f'Error processing webhook: {e}')}")
                    
                    # Send error response
                    self.send_response(500)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    
                    error_response = {
                        'status': 'error',
                        'message': str(e),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.wfile.write(json.dumps(error_response, indent=2).encode())
        
        return WebhookRequestHandler
    
    def start(self):
        """Start the webhook server"""
        print(f"{ColoredOutput.highlight('🚀 GOOGLE DRIVE WEBHOOK SERVER')}")
        print(f"{ColoredOutput.highlight('=' * 50)}")
        print(f"🌐 Starting server on port {self.port}")
        print(f"📡 Webhook endpoint: http://localhost:{self.port}/webhook/")
        print(f"🔍 Health check: http://localhost:{self.port}/health/")
        print(f"🕒 Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{ColoredOutput.highlight('=' * 50)}")
        
        try:
            # Create and start server
            handler_class = self.create_request_handler()
            self.server = HTTPServer(('', self.port), handler_class)
            
            print(f"{ColoredOutput.success('✅ Server started successfully!')}")
            print(f"{ColoredOutput.warning('💡 To set up ngrok tunnel:')}")
            print(f"   1. Install ngrok: https://ngrok.com/")
            print(f"   2. Run: ngrok http {self.port}")
            print(f"   3. Copy the HTTPS URL for webhook setup")
            print(f"{ColoredOutput.warning('⚠️  Press Ctrl+C to stop server')}")
            print()
            
            # Start serving
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print(f"\n{ColoredOutput.warning('⚠️  Server shutdown requested')}")
        except Exception as e:
            print(f"{ColoredOutput.error(f'❌ Server error: {e}')}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the webhook server"""
        if self.server:
            print(f"{ColoredOutput.info('🛑 Stopping server...')}")
            self.server.shutdown()
            self.server.server_close()
            
            # Cleanup notification subscription
            self.notification_manager.unsubscribe("webhook_server_display")
            
            elapsed = datetime.now() - self.start_time
            print(f"{ColoredOutput.success('✅ Server stopped')}")
            print(f"📊 Requests processed: {self.requests_received}")
            print(f"🕒 Uptime: {elapsed}")


def show_setup_instructions():
    """Show setup instructions"""
    print(f"{ColoredOutput.highlight('📋 WEBHOOK SERVER SETUP INSTRUCTIONS')}")
    print(f"{ColoredOutput.highlight('=' * 60)}")
    print(f"{ColoredOutput.info('This server receives real-time notifications from Google Drive.')}")
    print()
    print(f"{ColoredOutput.warning('🔧 Setup Steps:')}")
    print(f"   1. Start this webhook server")
    print(f"   2. Set up ngrok tunnel (see instructions below)")
    print(f"   3. Configure Google Drive subscription with ngrok URL")
    print(f"   4. Make changes to your Google Drive folder")
    print(f"   5. Watch notifications appear in real-time!")
    print()
    print(f"{ColoredOutput.warning('🌐 Ngrok Setup:')}")
    print(f"   # Install ngrok")
    print(f"   brew install ngrok  # Mac")
    print(f"   # or download from https://ngrok.com/")
    print()
    print(f"   # Start tunnel (in new terminal)")
    print(f"   ngrok http 8080")
    print()
    print(f"   # Copy the HTTPS URL (e.g., https://abc123.ngrok.io)")
    print(f"   # Use this URL for webhook subscription")
    print()
    print(f"{ColoredOutput.warning('📁 Folder being monitored:')}")
    print(f"   ID: 1AuNxXtxXPJYabIslI0n8UeT19GBHTFju")
    print(f"   URL: https://drive.google.com/drive/folders/1AuNxXtxXPJYabIslI0n8UeT19GBHTFju")
    print(f"{ColoredOutput.highlight('=' * 60)}")


def main():
    """Main function"""
    show_setup_instructions()
    
    try:
        input(f"\n{ColoredOutput.success('Press Enter to start webhook server...')}")
    except KeyboardInterrupt:
        print(f"\n{ColoredOutput.warning('Server startup cancelled')}")
        return
    
    # Start webhook server
    server = GoogleDriveWebhookServer(port=8081)
    server.start()


if __name__ == "__main__":
    main()