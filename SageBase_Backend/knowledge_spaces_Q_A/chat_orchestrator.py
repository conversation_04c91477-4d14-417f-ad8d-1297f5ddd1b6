"""
Chat Orchestrator
"""

import logging
import json
import uuid
from typing import List, Dict, Optional, Any
from typing_extensions import TypedDict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from agents import Agent, Runner, function_tool, ModelSettings
from agents import OpenAIChatCompletionsModel
from integrations.models import User
from vectordb.interfaces import search
from konowledge_detection_agents.github_agent import GitHubAgentManager
from integrations.models import CompanyIntegration, IntegrationTool
from .chat_response_formatting_agent import ChatResponseFormattingAgent, ChatOutputFormat
import asyncio
from konowledge_detection_agents.azure_config import async_azure_client, used_model, azure_gpt4o_mini_model
from knowledge_spaces_Q_A.function_tools import search_internet, search_local_knowledge, save_new_knowledge
from SageBase_Backend import constants


# Set up logger
logger = logging.getLogger(__name__)


# Data Models and Enums
from knowledge_spaces_Q_A.chat_models import ChatUserContext, ConversationType, ChatRequest, ChatResponse, ConversationMessage




instructions_need_an_answer="""You must provide an answer to the user's question."""
instructions_no_answer="""If you think this is a noise message,or not related to the company, you must not provide an answer.
This is very important, dont 't reply to say that you don't know or you can't find any information, just respond with an empty string.
If you respond, it must be to bring a real value to the user, not just a placeholder.
If you executed the tools at your disposal and you coun't find anythin useful xthat can bring value to the user, just answer with an empty string.
"""


# ============================================================================
# Main Chat Orchestrator
# ============================================================================

class ChatOrchestrator:
	
	def __init__(self, context: ChatUserContext = None):

		self.context = context
		
		# Initialize formatting agent
		self.formatting_agent = ChatResponseFormattingAgent()

		# Create model using async Azure client; avoid creating a new loop here
		model = OpenAIChatCompletionsModel(
			model=used_model,
			openai_client=async_azure_client,
		)
		logger.debug(f"✅ Using Async Azure OpenAI client with model: {used_model}")

		# Base tools; we will assemble final tools per request
		self.base_tools = [
			search_internet,
			search_local_knowledge,
		]
		self.include_save_tool = True # always include the save_new_knowledge tool

		# Store model and instructions; build the agent per request to control MCP lifecycle
		self.model = model
		self.main_instructions = """
  
You are SageBase, a senior software engineer responsible for the company knowledge base.

TOOLS (call exactly as defined)
A/  search_local_knowledge  :   This is a primary tool that you will most likely always call first, to check if the answer is already in the internal knowledge base of the company.

	CORE BEHAVIOR
	- BEFORE any tool call, generate 4–8 SHORT keyword queries. No full sentences; no filler words.
	- Prioritize nouns, technologies, versions, error codes, file paths, function names, action+object pairs.
	- Expand with synonyms/aliases and acronyms (install|setup|configure|deploy; auth|oauth|oidc|sso; ssl|tls).
	- If a topic includes product + feature + error, create permutations (e.g., django cors preflight; vercel cors; fetch cors).
	- Use MULTIPLE search_local_knowledge calls (one per keyword set). Merge, deduplicate, and summarize top findings.
	- If many hits, synthesize a consolidated answer referencing the 3–5 most relevant local items (by title/section/url).
	- If any local knowledge contradicts the user’s claim, clearly flag the contradiction and ask a focused follow-up.

	SEARCH HEURISTICS
	- Each query = 2–6 tokens max. Drop stopwords (how, the, can, I, etc.).
	- Include at least some of: product, feature,...
	- Try 1–2 broad queries + 2–4 narrow queries.
	- For install/upgrade topics: include install|setup|configure|upgrade|migrate variants.
	- For networking/auth: include cors|preflight|oauth|oidc|sso|token|jwt|redirect|callback|tls|ssl|proxy.
	- For infra: include docker|k8s|nginx|gunicorn|uvicorn|systemd|service|port|healthcheck.
	- Prefer exact package/API names (e.g., django-cors-headers, axios, fetch, Next.js, Vercel).

	CONSTRAINTS
	- Never pass full sentences to search_local_knowledge; only short keyword strings.
	- Prefer multiple targeted searches instead of one broad query.
	- Keep answers concise; include precise commands, file paths, config keys, and versions where relevant.



B/  save_new_knowledge   : This is when you decide to add a new info to the knowledge base.

	WHEN TO SAVE
	- Call save_new_knowledge ONLY if:
		(a) the info is new and useful,
		(b) not already present or directly contradicted, and
		(c) you can craft a crisp Q&A entry.
	- Execute the tool (do not ask for permission). 
	When you create a new knowledge base entry, you MUST reply with this strcuture:
	     A new knowledge base entry has been created:
	     <url>
	     <title>
	     <description>
	     <tags>

		One admin should approve it before it is visible to the rest of the team.
 
	AFTER SAVING THE NEW INFO:
    - you MUST answer informing that you have created a new knowledge base entry that can be found by the rest of the team and you MUST provide 
    the new url of the created knowledge base entry.

{sentence_need_answer_or_not}

{more_instructions}

⚠️ IMPORTANT: After executing tools, you must always provide a final human-friendly answer.  
- Summarize the results into natural language.  
- You must finish all your tasks in a single round, never ask for more information and never say that you will do it later.
- Never say that you are planning or doing some actions, Just do it and provide the final answer.
- Cite sources using hyperlinks.  
- output format is in text.
- Never end the run with only tool calls.
"""
			
		# --- GITHUB INTEGRATION (defer MCP lifecycle to request time) --- even if it fails, only github will not be available
		self.github_token: Optional[str] = None
		self.available_github_repos: List[Any] = []
		self._github_loaded = False  # Track if GitHub integration has been loaded
		
		logger.info(" Chat Orchestrator initialized successfully")

	async def _load_github_integration(self):
		"""Load GitHub integration asynchronously using thread offloading for ORM calls"""
		if self._github_loaded:
			return  # Already loaded
			
		try:
			if self.context and self.context.sources_to_search and constants.github in self.context.sources_to_search:
				# Offload ORM calls to a background thread to avoid CurrentThreadExecutor issues
				company = self.context.company
				try:
					github_tool = await asyncio.to_thread(IntegrationTool.objects.get, slug="github")
				except IntegrationTool.DoesNotExist:
					logger.debug("[GITHUB] GitHub integration is not done yet")
					self._github_loaded = True
					return
				
				integration = await asyncio.to_thread(lambda: CompanyIntegration.objects.filter(
					company=company, tool=github_tool, is_active=True
				).first())
				
				if integration:
					# Offload method call that may hit DB/config
					self.github_token = await asyncio.to_thread(integration.get_valid_github_token)
					self.available_github_repos = integration.config.get("repositories", [])
					logger.debug(f"[GITHUB] Token present: {bool(self.github_token)}")
					logger.debug(f"[GITHUB] Repos: {self.available_github_repos}")
				else:
					logger.warning("[GITHUB] No active GitHub integration found for company.")
					
				self._github_loaded = True
				
		except Exception as e:
			logger.exception(f"[GITHUB] Error reading GitHub integration: {e}--> github tools will not be available")
			self._github_loaded = True  # Mark as loaded to prevent retries
	
	def build_history_string(self, history: List[ConversationMessage]) -> str:
		if not history or len(history) == 0:
			return ""

		# Build a context string from the last 10 messages
		history_context = "\n\nCONVERSATION HISTORY:\n"
		for msg in history[-10:]:  # Last 10 messages
			timestamp = msg.get('timestamp', datetime.now().isoformat())
			sender = msg.get('sender_name',)
			content = msg.get('content')
			history_context += f"[{timestamp}] {sender}: {content}\n" if content else ""
		return history_context

	async def format_response(self, response_text: str, output_format:ChatOutputFormat) -> str:
		"""
		Format the response text using the formatting agent
		
		Args:
			response_text: The original response text to format
			output_format: The desired output format ("html" or "markdown")
			
		Returns:
			Formatted response text in the specified format
		"""
		try:
			# Use the formatting agent to format the response
			formatted_response = await self.formatting_agent.format_response(
				response_text, 
				output_format
			)
			
			return formatted_response
			
		except Exception as e:
			logger.error(f"Error formatting response: {e}")
			# Return original response if formatting fails
			return response_text

	async def process_chat_request(self, request: ChatRequest) -> ChatResponse:

		"""This is the main function that treats any message from a user.
			The message could be from the frontend, slack,discord,etc.
		"""

		logger.info(f"Processing chat request: {request['message']}")

		# Note: avoid per-request event-loop exception handler and per-request teardown of MCP
		need_an_answer =  request.get("needs_reply", False)

		# Skip noise messages in group conversations
		if not need_an_answer and request.get("session_type")== ConversationType.GROUP:
			is_noise = await self.skip_noise_messages(request)
			if is_noise:
				logger.info(f"Skipping noise message: {request['message']}")
				return ChatResponse(
					response="",  # Empty response for noise
					no_action_needed=True  # Indicate no action needed
				)

		try:		
			# Check if conversation history is provided
			history = request.get("history", [])		
			# Build context string from conversation history
			history_context = self.build_history_string(history)
		
			# Create comprehensive input for the agent
			conversation_input = [
				{"role": "user", "content": f"""
				{history_context}
				NEW MESSAGE: {request['message']}
				"""}
			]

			# Assemble tools per request
			tools: List[Any] = list(self.base_tools)
			if self.include_save_tool:
				tools.append(save_new_knowledge)

			# Load GitHub integration if needed (async-safe)
			if self.context and self.context.sources_to_search and constants.github in self.context.sources_to_search:
				await self._load_github_integration()

			#messages from groups don't require and answer, but personal messages do
			if need_an_answer:
				sentence_need_answer_or_not = instructions_need_an_answer
			else:
				sentence_need_answer_or_not = instructions_no_answer

			# Decide whether to include a GitHub handoff; if so, manage its lifecycle within this task
			use_github = bool(self.github_token and self.context and self.context.sources_to_search and constants.github in self.context.sources_to_search)
			if use_github:
				logger.warning(f"Using GitHub to answer the question: {request['message']}")
				async with GitHubAgentManager(self.github_token, self.available_github_repos) as github_agent:
					self
					main_agent = Agent(
						name="Chat Orchestrator",
						instructions=self.main_instructions.format(more_instructions="""
						**ALWAYS HANDOFF TO GITHUB ASSISTANT IF GITHUB IS AN AVAILABLE PLATFORM AND BELOW TOPICS ARE MENTIONED:**
For any question that includes topics such as:
	- repositories (e.g., "repo", "repository", "project", "file")
	- commits, pull requests, issues, or branches
	- GitHub usernames or repo names (like `BassemBG/NeuroAI-Backend`)
	- code explanations or file contents
	- GitHub metrics or contributions
	...you MUST DELEGATE to the GitHub Codebase & Stats Assistant, as long as 'github' is in the available platforms.Always include sources in the response when found in Q&A or local knowledge.
""",sentence_need_answer_or_not=sentence_need_answer_or_not),
						model=self.model,
						model_settings=ModelSettings(parallel_tool_calls=True),
						tools=tools,
						handoffs=[github_agent]
					)
					result = await Runner.run(
						main_agent,
						conversation_input,
						max_turns=10,
						context=self.context
					)
			else:
				main_agent = Agent(
					name="Chat Orchestrator",
					instructions=self.main_instructions.format(more_instructions="",sentence_need_answer_or_not=sentence_need_answer_or_not),
					model=self.model,
					model_settings=ModelSettings(parallel_tool_calls=True),
					tools=tools,
					handoffs=[]
				)
				result = await Runner.run(
					main_agent,
					conversation_input,
					max_turns=10,
					context=self.context
				)

			if request.get("session_type") == ConversationType.GROUP:
				response_quality_result = await self.skip_useless_responses_group_chat(request, result.final_output)
				if response_quality_result == "no_useful_response":
					logger.info(f"⚠️  Response Quality Filter filtered out this answer: {result.final_output}")
					return ChatResponse(
						response="no_useful_response",  # Return override string for useless responses
						no_action_needed=True  # Indicate no action needed
					)
				# If response is useful, use the quality result (which should be the original response)
				result.final_output = response_quality_result
			
			# Get the output format from request, default to markdown
			output_format = request.get("output_format", "markdown")

			formatted_response = None
			
			# Format the response using the formatting agent
			try:
				formatted_response = await asyncio.wait_for(
					self.format_response(result.final_output, output_format),
					timeout=30.0  # 30 second timeout
				)
			except asyncio.TimeoutError:
				logger.warning("Formatting agent timed out after 30 seconds, using raw output")
				if not formatted_response:
					formatted_response = result.final_output
			
			# Build response
			response = ChatResponse(
				message_id=str(uuid.uuid4()),
				response=formatted_response,
			)
			
			return response
			
		except Exception as e:
			logger.error(f"❌ Error processing chat request: {e}")
			return ChatResponse(
				message_id=str(uuid.uuid4()),
				response="Something went wrong, if the problem persists, please contact <NAME_EMAIL>" if need_an_answer else "",
				references=[],
				context_used={},
				tokens_used=0,
				agent_decisions=["Error occurred"]
			)
		finally:
			# Do not close MCP per request; reuse for lifetime of process
			pass




	async def skip_noise_messages(self, request) -> bool:
		"""
		Returns True if the message is noise (skip),
		False if it needs intervention (not noise).
		Uses JSON output from the agent; falls back to yes/no parsing.
		"""
		try:
			import re
			# Model & agent defined inside (same structure)
			noise_model = OpenAIChatCompletionsModel(
				model=azure_gpt4o_mini_model,
				openai_client=async_azure_client,
			)

			noise_filter_agent = Agent(
				name="Noise Filter",
				instructions=(
					"You read a conversation history and a new message and decide if it requires a response.\n\n"
					"Return EXACTLY this JSON object (and nothing else):\n"
					'{ "needs_intervention": true|false }\n'
					"- true  => the conversation requires intervention/response\n"
					"- false => it does NOT require intervention (noise: greetings, small talk, trivial updates, etc.)\n"
					"No extra keys, no explanations, no surrounding text."
				),
				model=noise_model,
				model_settings=ModelSettings(
					# If your wrapper supports JSON mode:
					response_format={"type": "json_object"},
					# max_output_tokens=10,
				),
				tools=[],
			)

			history_list = request.get("history", [])
			history_text = self.build_history_string(history_list)
			new_message = request.get("message", "")

			conversation_input = [
				{
					"role": "user",
					"content": (
						"CONVERSATION HISTORY:\n"
						f"{history_text}\n\n"
						"NEW MESSAGE:\n"
						f"{new_message}\n\n"
						"Respond with the JSON object specified above."
					),
				}
			]

			result = await asyncio.wait_for(
				Runner.run(
					noise_filter_agent,
					conversation_input,
					max_turns=1,
				),
				timeout=8.0,
			)

			# Prefer output_text; fallback to final_output; then reconstruct from messages
			text = (
				getattr(result, "output_text", None)
				or getattr(result, "final_output", None)
				or ""
			).strip()


			if not text:
				logger.warning("NoiseFilter: empty output; defaulting to NOT skipping.")
				return False

			# Try JSON first
			needs_intervention = None
			try:
				obj = json.loads(text)
				if isinstance(obj, dict) and "needs_intervention" in obj:
					needs_intervention = bool(obj["needs_intervention"])
			except Exception:
				needs_intervention = None

			# Fallback: accept plain yes/no (if instructions not followed)
			if needs_intervention is None:
				m = re.search(r"\b(yes|no)\b", text.lower())
				if m:
					needs_intervention = (m.group(1) == "yes")

			if needs_intervention is None:
				logger.warning("NoiseFilter: unparseable output %r; defaulting to NOT skipping.", text)
				return False

			# Function contract: True => noise/skip, False => needs intervention
			return not needs_intervention

		except asyncio.TimeoutError:
			logger.error("NoiseFilter: timeout; defaulting to NOT skipping.")
			return False
		except Exception as e:
			logger.exception("NoiseFilter: error: %s", e)
			return False


		
	async def skip_useless_responses_group_chat(self, request: ChatRequest, response_text: str) -> str:
		"""Filter out non-actionable responses in group chat conversations.
		Returns 'no_useful_response' if response is useless, otherwise returns the original response."""
		
		# Create response quality filter agent with Async Azure client
		quality_model = OpenAIChatCompletionsModel(
			model=azure_gpt4o_mini_model,
			openai_client=async_azure_client,
		)
		
		quality_filter_agent = Agent(
			name="Response Quality Filter",
			instructions="""ROLE: You are a response quality evaluator for a group chat conversation.

TASK: Decide if a response provides actionable value or if it is a non-informative placeholder.

EVALUATION RULES:

Mark send if the response:

Contains useful, accurate, or actionable information

Provides insights, explanations, or guidance

Directly contributes meaningfully to the ongoing discussion

Mark skip if the response:

Lacks actionable content (e.g., "I couldn't find any information", "no updates")

Is vague, off-topic, or irrelevant to the conversation

OUTPUT FORMAT:
Respond with only one word: send or skip.
No additional text, punctuation, or explanation is allowed.
			""",
			model=quality_model,
			model_settings=ModelSettings(parallel_tool_calls=True),
			tools=[]  # No tools needed for filtering
		)
		
		# give the agent the question and the response to evaluate
		conversation_input = [
			{"role": "user", "content": f"""
			USER REQUEST: {request['message']}
			RESPONSE TO EVALUATE: {response_text}
			"""}
		]
		
		# Run the agent to decide if intervention is needed
		result = await Runner.run(
			quality_filter_agent,
			conversation_input,
			max_turns=3  # Quick evaluation, max 3 turns
		)
		
		# Check the response - use final_output instead of accessing as dict
		agent_response = result.final_output.strip().lower()
		if agent_response == "skip":
			return "no_useful_response"  # Return override string for useless responses
		else:
			return response_text  # Return original response if useful