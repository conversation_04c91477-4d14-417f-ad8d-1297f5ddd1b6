"""
Chat Orchestrator
"""

import logging
import json
import uuid
from typing import List, Dict, Optional, Any
from typing_extensions import TypedDict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from agents import Agent, Runner, function_tool, ModelSettings
from agents import OpenAIChatCompletionsModel
from integrations.models import User
from vectordb.interfaces import search
from konowledge_detection_agents.github_agent import create_github_agent
from integrations.models import CompanyIntegration, IntegrationTool
from .chat_response_formatting_agent import ChatResponseFormattingAgent, ChatOutputFormat
import asyncio
from konowledge_detection_agents.azure_config import async_azure_client, used_model, azure_gpt4o_mini
from knowledge_spaces_Q_A.function_tools import search_internet, search_local_knowledge, save_new_knowledge
from SageBase_Backend import constants


# Set up logger
logger = logging.getLogger(__name__)


# Data Models and Enums
from knowledge_spaces_Q_A.chat_models import ChatUserContext, ConversationType, ChatRequest, ChatResponse, ConversationMessage


# ============================================================================
# Main Chat Orchestrator
# ============================================================================

# Cache for long-lived handoff agents (keyed by token)
_GITHUB_AGENT_CACHE: Dict[str, Any] = {}

class ChatOrchestrator:
    
    def __init__(self, context: ChatUserContext = None):

        self.context = context
        
        # Initialize formatting agent
        self.formatting_agent = ChatResponseFormattingAgent()

        model = OpenAIChatCompletionsModel(
            model=used_model,
            openai_client=async_azure_client,
        )
        logger.debug(f"✅ Using Async Azure OpenAI client with model: {used_model}")

        tools = [
                search_internet,
                search_local_knowledge,
            ]
        if context and context.user_id and context.user.company:
            tools.extend([save_new_knowledge])

        # --- GITHUB AGENT INTEGRATION (singleton cache per token) ---
        self.github_agent = None
        if context and context.sources_to_search and constants.github in context.sources_to_search:
            try:
                company = context.company
                github_tool = IntegrationTool.objects.get(slug="github")
                integration = CompanyIntegration.objects.filter(company=company, tool=github_tool, is_active=True).first()
                if integration:
                    github_token = integration.get_valid_github_token()
                    available_github_repos = integration.config.get("repositories", [])
                    logger.info(f"[GITHUB] Token: {github_token}")
                    logger.info(f"[GITHUB] Repos: {available_github_repos}")
                    # Reuse existing agent if already created for this token
                    cached = _GITHUB_AGENT_CACHE.get(github_token)
                    if cached is not None:
                        self.github_agent = cached
                        logger.info("[GITHUB] Reusing cached GitHub agent")
                    else:
                        # create_github_agent is async, call from sync context with timeout once
                        from asgiref.sync import async_to_sync
                        async def _create_with_timeout():
                            import asyncio
                            return await asyncio.wait_for(
                                create_github_agent(github_token, available_github_repos),
                                timeout=30.0
                            )
                        created = async_to_sync(_create_with_timeout)()
                        if created is not None:
                            _GITHUB_AGENT_CACHE[github_token] = created
                            self.github_agent = created
                            logger.info("[GITHUB] GitHub agent created and cached")
                        else:
                            logger.warning("[GITHUB] GitHub agent creation returned None")
                else:
                    logger.warning("[GITHUB] No active GitHub integration found for company.")
            except Exception as e:
                logger.error(f"[GITHUB] Error initializing GitHub agent: {e}")
                self.github_agent = None
            # --- END GITHUB AGENT INTEGRATION ---

        self.main_agent = Agent(
            name="Chat Orchestrator",
            instructions="""You are a helpful assistant of the SageBase platform that can answer user questions by combining information from various sources.
Always call search_local_knowledge and search_internet first systematically to find relevant information. These tools will provide knowledge that must be prioritized over other sources.
Never add a new knowledge that was already found in the local knowledge or similar Q&A.

**ALWAYS HANDOFF TO GITHUB ASSISTANT IF GITHUB IS AN AVAILABLE PLATFORM AND BELOW TOPICS ARE MENTIONED:**
For any question that includes topics such as:
    - repositories (e.g., "repo", "repository", "project", "file")
    - commits, pull requests, issues, or branches
    - GitHub usernames or repo names (like `BassemBG/NeuroAI-Backend`)
    - code explanations or file contents
    - GitHub metrics or contributions
...you MUST DELEGATE to the GitHub Codebase & Stats Assistant, as long as 'github' is in the available platforms.Always include sources in the response when found in Q&A or local knowledge.

When combining information, specifically mention the source of each piece of information.
When you use any resources, you shall always include the source of the information, preferably its uri, or the name of the source.
If no relevant information is found, use search_internet to find answers.
Choose the best tool(s) to answer the user's question.
You must alert the user about duplicated or conflicting information when the user provides new knowledge.
You must finish all your tasks in a single round, never ask for more information and never say that you will do it later.
You must always provide a final useful human friendly answer to the user after executing all the necessary tools and handing off to other agents and getting responses from them.
The answer shall be formatted with nice colors and formatting in markdown format.
            """,
            model=model,
            model_settings=ModelSettings(parallel_tool_calls=True),
            tools=tools,
            handoffs=[self.github_agent] if self.github_agent is not None else []
        )
        
        logger.info(" Chat Orchestrator initialized successfully")
    
    def build_history_string(self, history: List[ConversationMessage]) -> str:
        if not history or len(history) == 0:
            return ""

        # Build a context string from the last 10 messages
        history_context = "\n\nCONVERSATION HISTORY:\n"
        for msg in history[-10:]:  # Last 10 messages
            timestamp = msg.get('timestamp', datetime.now().isoformat())
            sender = msg.get('sender_name',)
            content = msg.get('content')
            history_context += f"[{timestamp}] {sender}: {content}\n" if content else ""
        return history_context

    async def format_response(self, response_text: str, output_format:ChatOutputFormat) -> str:
        """
        Format the response text using the formatting agent
        
        Args:
            response_text: The original response text to format
            output_format: The desired output format ("html" or "markdown")
            
        Returns:
            Formatted response text in the specified format
        """
        try:
            # Use the formatting agent to format the response
            formatted_response = await self.formatting_agent.format_response(
                response_text, 
                output_format
            )
            
            return formatted_response
            
        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            # Return original response if formatting fails
            return response_text

    async def process_chat_request(self, request: ChatRequest) -> ChatResponse:

        """This is the main function that treats any message from a user.
            The message could be from the frontend, slack,discord,etc.
        """

        logger.info(f"Processing chat request: {request['message']}")

        # Note: avoid per-request event-loop exception handler and per-request teardown of MCP

        # Skip noise messages in group conversations
        if request.get("session_type") == ConversationType.GROUP:
            is_noise = await self.skip_noise_messages(request)
            if is_noise:
                logger.info(f"Skipping noise message: {request['message']}")
                return ChatResponse(
                    response="",  # Empty response for noise
                    no_action_needed=True  # Indicate no action needed
                )

        try:        
            # Check if conversation history is provided
            history = request.get("history", [])        
            # Build context string from conversation history
            history_context = self.build_history_string(history)
        
            # Create comprehensive input for the agent
            conversation_input = [
                {"role": "user", "content": f"""
                {history_context}
                NEW MESSAGE: {request['message']}
                """}
            ]

            # Run the agent with all tools available
            result = await Runner.run(
                self.main_agent,
                conversation_input,
                context=self.context
            )
            
            # Get the output format from request, default to markdown
            output_format = request.get("output_format", "markdown")
            
            # Format the response using the formatting agent
            formatted_response = await self.format_response(result.final_output, output_format)
            
            # Build response
            response = ChatResponse(
                message_id=str(uuid.uuid4()),
                response=formatted_response,
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing chat request: {e}")
            return ChatResponse(
                message_id=str(uuid.uuid4()),
                response=f"Something went wrong, please contact support.",
                references=[],
                context_used={},
                tokens_used=0,
                agent_decisions=["Error occurred"]
            )
        finally:
            # Do not close MCP per request; reuse for lifetime of process
            pass


    async def skip_noise_messages(self, request: ChatRequest) -> bool:
        """Filter out noise messages in group chat conversations."""
        
        # Create noise filter agent with Async Azure client            # Use Async Azure OpenAI client
        noise_model = OpenAIChatCompletionsModel(
            model=azure_gpt4o_mini,
            openai_client=async_azure_client,
        )
   
        noise_filter_agent = Agent(
            name="Noise Filter",
            instructions="""You are a simple helper with a single task.
            You read a conversation history and decide if it requires an intervention/response or not.
CRUCIAL STRICT RULE: Your only possible responses are:
            - 'yes' if the conversation requires intervention
            - 'no' if the conversation does not require intervention
Greetings, small talk, short messages, discontinued inquiries/ideas, or any other messages that do not require a response should be considered noise.
            """,
            model=noise_model,
            model_settings=ModelSettings(parallel_tool_calls=True),
            tools=[]  # No tools needed for filtering
        )

        try:
        
            # Convert conversation history to serializable format
            history = request.get('history', [])
            history = self.build_history_string(history)
            # Prepare input for the agent
            conversation_input = [
                {"role": "user", "content": f"""
                {history}
                NEW MESSAGE: {request['message']}
                """}
            ]
            
            # Run the agent to decide if intervention is needed
            result = await Runner.run(
                noise_filter_agent,
                conversation_input,
            )
            
            # Check the response - use final_output instead of accessing as dict
            response_text = result.final_output.strip().lower()
            if response_text == "yes":
                return False  # Intervention needed (i.e., not noise)
            else:
                return True  # No intervention needed (i.e., noise)
        except Exception as e:
            logger.exception(f"Error in noise filtering: {e}")
            return False

        
