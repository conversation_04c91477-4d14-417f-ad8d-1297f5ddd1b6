from rest_framework import serializers
from .models import Knowledge_Space, QA, UserVote
from integrations.models import User
from .models import QAComment


class AuthorSerializer(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.CharField()
    avatar = serializers.URLField(allow_blank=True, allow_null=True)


class QuestionSerializer(serializers.Serializer):
    title = serializers.CharField(source='question_title')
    content = serializers.CharField(source='question_content')
    tags = serializers.ListField(source='question_tags', child=serializers.Char<PERSON>ield())
    author = serializers.SerializerMethodField()
    timestamp = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()
    
    def get_author(self, obj):
        return {
            'id': obj.question_author_id,
            'name': "",
            # 'name': , TODO: Add logic to get author name from User model
            'avatar': obj.question_author_avatar
        }
    
    def get_timestamp(self, obj):
        return obj.created_at.strftime("%H:%M")
    
    def get_date(self, obj):
        from django.utils import timezone
        today = timezone.now().date()
        if obj.created_at.date() == today:
            return "Today"
        return obj.created_at.strftime("%B %d, %Y")


class AnswerSerializer(serializers.Serializer):
    content = serializers.CharField(source='answer_content', allow_blank=True, allow_null=True)
    code = serializers.CharField(source='answer_code', allow_blank=True, allow_null=True)
    explanation = serializers.CharField(source='answer_explanation', allow_blank=True, allow_null=True)
    author = serializers.SerializerMethodField()
    timestamp = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()
    isVerified = serializers.BooleanField(source='answer_is_verified')
    
    def get_author(self, obj):
        return {
            'id': obj.answer_author_id,
            'name': obj.answer_author_name,
            'avatar': obj.answer_author_avatar
        }
    
    def get_timestamp(self, obj):
        return obj.updated_at.strftime("%H:%M")
    
    def get_date(self, obj):
        from django.utils import timezone
        today = timezone.now().date()
        if obj.updated_at.date() == today:
            return "Today"
        return obj.updated_at.strftime("%B %d, %Y")


class VotesSerializer(serializers.Serializer):
    upvotes = serializers.IntegerField()
    downvotes = serializers.IntegerField()


class MetadataSerializer(serializers.Serializer):
    views = serializers.IntegerField()
    helpful = serializers.IntegerField(source='helpful_count')
    editedBy = serializers.SerializerMethodField()
    approvedBy = serializers.SerializerMethodField()
    approvalStatus = serializers.CharField(source='approval_status')
    approvalReason = serializers.CharField(source='approval_reason', allow_null=True)
    
    def get_editedBy(self, obj):
        # These fields were removed in migration 0005
        # Return None since editing functionality is not currently implemented
        return None
    
    def get_approvedBy(self, obj):
        if obj.approved_by and obj.approved_at:
            return {
                "name": obj.approved_by.first_name or obj.approved_by.email,
                "avatar": getattr(obj.approved_by, 'avatar', None),
                "date": f"Today at {obj.approved_at.strftime('%H:%M')}" if obj.approved_at.date() == obj.updated_at.date() else obj.approved_at.strftime("%B %d at %H:%M")
            }
        return None


class KnowledgeSpaceSerializer(serializers.ModelSerializer):
    qaCount = serializers.IntegerField(source='qa_count', read_only=True)
    doc_responsible = serializers.SerializerMethodField()
    secondary_responsible = serializers.SerializerMethodField()
    
    # Fields for creation/update - these will be used for writing
    doc_responsible_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False,
        allow_null=True,
        write_only=True,
        source='doc_responsible',
        help_text="Primary responsible user ID for this knowledge space"
    )
    secondary_responsible_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False,
        allow_null=True,
        write_only=True,
        source='secondary_responsible',
        help_text="Secondary responsible user ID for this knowledge space"
    )
    
    class Meta:
        model = Knowledge_Space
        fields = ['id', 'name', 'color', 'initial', 'qaCount', 'doc_responsible', 'secondary_responsible', 'doc_responsible_id', 'secondary_responsible_id', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
        # Note: 'company' field is excluded from serializer as it's set automatically by the view
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically set the queryset for responsible user fields based on the current user's company
        if 'context' in kwargs and 'user' in kwargs['context']:
            current_user = kwargs['context']['user']
            user_company = getattr(current_user, 'company', None)
            if user_company:
                # Filter users by the same company
                company_users = User.objects.filter(company=user_company)
                self.fields['doc_responsible_id'].queryset = company_users
                self.fields['secondary_responsible_id'].queryset = company_users
    
    def get_doc_responsible(self, obj):
        """Return user details for doc_responsible"""
        if obj.doc_responsible:
            return {
                'id': obj.doc_responsible.id,
                'email': obj.doc_responsible.email,
                'name': f"{obj.doc_responsible.first_name or ''} {obj.doc_responsible.last_name or ''}".strip() or obj.doc_responsible.email
            }
        return None
    
    def get_secondary_responsible(self, obj):
        """Return user details for secondary_responsible"""
        if obj.secondary_responsible:
            return {
                'id': obj.secondary_responsible.id,
                'email': obj.secondary_responsible.email,
                'name': f"{obj.secondary_responsible.first_name or ''} {obj.secondary_responsible.last_name or ''}".strip() or obj.secondary_responsible.email
            }
        return None
    
    def validate(self, data):
        """
        Validate that the responsible users belong to the same company as the current user.
        """
        # Get the current user from the context
        request = self.context.get('request')
        current_user = None
        
        # Try to get user from context first (for custom auth)
        if 'user' in self.context:
            current_user = self.context['user']
        # If not found, try to get from request.user (for standard Django auth)
        elif request and hasattr(request, 'user') and request.user and hasattr(request.user, 'is_authenticated') and request.user.is_authenticated:
            current_user = request.user
        
        if not current_user:
            # If no user found, skip validation (this shouldn't happen in normal flow)
            return data
        
        user_company = getattr(current_user, 'company', None)
        
        # Validate doc_responsible
        if 'doc_responsible' in data and data['doc_responsible']:
            # Get the user object by UUID and check their company
            assigned_user = data['doc_responsible']
            assigned_user_company = getattr(assigned_user, 'company', None)
            
            if user_company != assigned_user_company:
                raise serializers.ValidationError(
                    f"Primary responsible user ({assigned_user.email}) must belong to the same company as the current user. "
                    f"Current user company: {user_company.name if user_company else 'None'}, "
                    f"Assigned user company: {assigned_user_company.name if assigned_user_company else 'None'}"
                )
        
        # Validate secondary_responsible
        if 'secondary_responsible' in data and data['secondary_responsible']:
            # Get the user object by UUID and check their company
            assigned_user = data['secondary_responsible']
            assigned_user_company = getattr(assigned_user, 'company', None)
            
            if user_company != assigned_user_company:
                raise serializers.ValidationError(
                    f"Secondary responsible user ({assigned_user.email}) must belong to the same company as the current user. "
                    f"Current user company: {user_company.name if user_company else 'None'}, "
                    f"Assigned user company: {assigned_user_company.name if assigned_user_company else 'None'}"
                )
        
        return data


class KnowledgeSpaceResponsibleUsersSerializer(serializers.ModelSerializer):
    doc_responsible = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False,
        allow_null=True,
        help_text="Primary responsible user for this knowledge space"
    )
    secondary_responsible = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        required=False,
        allow_null=True,
        help_text="Secondary responsible user for this knowledge space"
    )
    
    class Meta:
        model = Knowledge_Space
        fields = ['doc_responsible', 'secondary_responsible']
    
    def to_representation(self, instance):
        """Custom representation to include user details"""
        data = super().to_representation(instance)
        
        # Replace IDs with user details for doc_responsible
        if instance.doc_responsible:
            data['doc_responsible'] = {
                'id': instance.doc_responsible.id,
                'email': instance.doc_responsible.email,
                'name': f"{instance.doc_responsible.first_name or ''} {instance.doc_responsible.last_name or ''}".strip() or instance.doc_responsible.email
            }
        else:
            data['doc_responsible'] = None
        
        # Replace IDs with user details for secondary_responsible
        if instance.secondary_responsible:
            data['secondary_responsible'] = {
                'id': instance.secondary_responsible.id,
                'email': instance.secondary_responsible.email,
                'name': f"{instance.secondary_responsible.first_name or ''} {instance.secondary_responsible.last_name or ''}".strip() or instance.secondary_responsible.email
            }
        else:
            data['secondary_responsible'] = None
        
        return data
    
    def validate(self, data):
        """
        Validate that the responsible users belong to the same company as the knowledge space.
        """
        knowledge_space = self.instance
        if not knowledge_space:
            return data
        
        # Check if knowledge space is company-owned
        if not knowledge_space.company:
            raise serializers.ValidationError("Responsible users can only be assigned to company-owned knowledge spaces.")
        
        # Validate doc_responsible
        if 'doc_responsible' in data and data['doc_responsible']:
            if not hasattr(data['doc_responsible'], 'company') or data['doc_responsible'].company != knowledge_space.company:
                raise serializers.ValidationError("Primary responsible user must belong to the same company as the knowledge space.")
        
        # Validate secondary_responsible
        if 'secondary_responsible' in data and data['secondary_responsible']:
            if not hasattr(data['secondary_responsible'], 'company') or data['secondary_responsible'].company != knowledge_space.company:
                raise serializers.ValidationError("Secondary responsible user must belong to the same company as the knowledge space.")
        
        return data


class QASerializer(serializers.ModelSerializer):
    knowledgeSpaceId = serializers.CharField(source='knowledge_space.id', read_only=True)
    knowledgeSpaceName = serializers.CharField(source='knowledge_space.name', read_only=True)
    question = serializers.SerializerMethodField()
    answer = serializers.SerializerMethodField()
    votes = VotesSerializer(source='*', read_only=True)
    metadata = MetadataSerializer(source='*', read_only=True)
    comments = serializers.SerializerMethodField()
    
    class Meta:
        model = QA
        fields = ['id', 'knowledgeSpaceId', 'knowledgeSpaceName', 'question', 'answer', 'votes', 'metadata', 'comments', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_question(self, obj):
        # Use QuestionSerializer directly on the QA object
        question_serializer = QuestionSerializer(obj)
        return question_serializer.data
    
    def get_answer(self, obj):
        if obj.answer_content:
            # Use AnswerSerializer directly on the QA object
            answer_serializer = AnswerSerializer(obj)
            return answer_serializer.data
        return None
    
    def get_comments(self, obj):
        # Get comments for this QA, ordered by creation date
        comments = obj.comments.all().order_by('created_at')
        comment_serializer = CommentSerializer(comments, many=True)
        return comment_serializer.data


class QACreateSerializer(serializers.Serializer):
    # Question fields
    question_title = serializers.CharField()
    question_content = serializers.CharField()
    question_tags = serializers.ListField(child=serializers.CharField())
    
    # Answer fields (optional)
    answer_content = serializers.CharField(required=False, allow_blank=True)
    answer_code = serializers.CharField(required=False, allow_blank=True)
    answer_explanation = serializers.CharField(required=False, allow_blank=True)
    
    # Token count fields
    question_tokens_count = serializers.IntegerField(required=False, default=0)
    answer_tokens_count = serializers.IntegerField(required=False, default=0)
    
    def create(self, validated_data):
        from utils.token_utils import get_token_count
        
        # Get user from context (should be set in view)
        user_id = self.context.get('user_id')
        user_instance = User.objects.get(id=user_id)
        user_avatar = self.context.get('user_avatar', None)

        # Calculate token counts
        question_content = validated_data['question_content']
        answer_content = validated_data.get('answer_content')
        question_tokens_count = get_token_count(question_content)
        answer_tokens_count = get_token_count(answer_content) if answer_content else 0

        knowledge_space = self.context['knowledge_space']
        
        qa = QA.objects.create(
            knowledge_space=knowledge_space,
            question_title=validated_data['question_title'],
            question_content=validated_data['question_content'],
            question_tags=validated_data['question_tags'],
            question_author_id=user_id,
            question_author_name=user_instance.first_name if user_instance.first_name else user_instance.email,
            question_author_avatar=user_avatar,
            answer_content=validated_data.get('answer_content'),
            answer_code=validated_data.get('answer_code'),
            answer_explanation=validated_data.get('answer_explanation'),
            answer_author_id=user_instance.id if validated_data.get('answer_content') else None,
            answer_author_name=user_instance.first_name if user_instance.first_name else user_instance.email if validated_data.get('answer_content') else None,
            answer_author_avatar=user_avatar if validated_data.get('answer_content') else None,
            answer_tokens_count=answer_tokens_count,
            question_tokens_count=question_tokens_count
        )
        return qa


class QAUpdateSerializer(serializers.Serializer):
    # Question fields (optional for updates)
    question_title = serializers.CharField(required=False)
    question_content = serializers.CharField(required=False)
    question_tags = serializers.ListField(child=serializers.CharField(), required=False)
    
    # Answer fields (optional for updates)
    answer_content = serializers.CharField(required=False, allow_blank=True)
    answer_code = serializers.CharField(required=False, allow_blank=True)
    answer_explanation = serializers.CharField(required=False, allow_blank=True)
    
    # Token count fields (optional for updates)
    question_tokens_count = serializers.IntegerField(required=False)
    answer_tokens_count = serializers.IntegerField(required=False)
    
    def update(self, instance, validated_data):
        # Get user from context
        user_id = self.context.get('user_id', 'unknown')
        user_name = self.context.get('user_name', 'Unknown User')
        user_avatar = self.context.get('user_avatar', None)
        
        # Update question fields if provided
        if 'question_title' in validated_data:
            instance.question_title = validated_data['question_title']
        if 'question_content' in validated_data:
            instance.question_content = validated_data['question_content']
        if 'question_tags' in validated_data:
            instance.question_tags = validated_data['question_tags']
        
        # Update answer fields if provided
        if 'answer_content' in validated_data:
            instance.answer_content = validated_data['answer_content']
        if 'answer_code' in validated_data:
            instance.answer_code = validated_data['answer_code']
        if 'answer_explanation' in validated_data:
            instance.answer_explanation = validated_data['answer_explanation']
        
        # Set answer author if answer content is provided and not already set
        if validated_data.get('answer_content') and not instance.answer_author_id:
            instance.answer_author_id = user_id
            instance.answer_author_name = user_name
            instance.answer_author_avatar = user_avatar
        
        # Update token counts if provided
        if 'question_tokens_count' in validated_data:
            instance.question_tokens_count = validated_data['question_tokens_count']
        if 'answer_tokens_count' in validated_data:
            instance.answer_tokens_count = validated_data['answer_tokens_count']
        
        # Mark as edited
        from django.utils import timezone
        instance.edited_by_name = user_name
        instance.edited_by_avatar = user_avatar
        instance.edited_at = timezone.now()
        
        instance.save()
        return instance


class VoteSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=['up', 'down'])


class VoteResponseSerializer(serializers.Serializer):
    votes = VotesSerializer()
    userVote = serializers.CharField()


class CommentAuthorSerializer(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.CharField(allow_blank=True, allow_null=True)
    avatar = serializers.URLField(allow_blank=True, allow_null=True)


class CommentSerializer(serializers.ModelSerializer):
    author = serializers.SerializerMethodField()
    timestamp = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()
    votes = VotesSerializer(source='*', read_only=True)
    is_edited = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = QAComment
        fields = [
            'id', 'content', 'author', 'timestamp', 'date', 'votes', 
            'is_edited', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_author(self, obj):
        # Fetch user information from User model
        try:
            user = User.objects.get(id=obj.author_id)
            return {
                'id': obj.author_id,
                'name': user.first_name or user.email,
                'avatar': getattr(user, 'avatar', None)
            }
        except User.DoesNotExist:
            return {
                'id': obj.author_id,
                'name': 'Unknown User',
                'avatar': None
            }
    
    def get_timestamp(self, obj):
        return obj.created_at.strftime("%H:%M")
    
    def get_date(self, obj):
        from django.utils import timezone
        today = timezone.now().date()
        if obj.created_at.date() == today:
            return "Today"
        return obj.created_at.strftime("%B %d, %Y")


class CommentCreateSerializer(serializers.Serializer):
    content = serializers.CharField()
    
    def create(self, validated_data):
        # Get user from context (should be set in view)
        user_id = self.context.get('user_id')
        
        # Get QA from context
        qa = self.context['qa']
        
        comment = QAComment.objects.create(
            qa=qa,
            content=validated_data['content'],
            author_id=user_id
        )
        return comment


class CommentUpdateSerializer(serializers.Serializer):
    content = serializers.CharField()
    
    def update(self, instance, validated_data):
        instance.content = validated_data['content']
        instance.mark_as_edited()
        
        return instance


class CommentVoteSerializer(serializers.Serializer):
    vote_type = serializers.ChoiceField(choices=['up', 'down'])


class CommentVoteResponseSerializer(serializers.Serializer):
    votes = VotesSerializer()
    user_vote = serializers.CharField()