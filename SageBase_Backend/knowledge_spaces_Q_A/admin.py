from django.contrib import admin
from .models import Knowledge_Space, QA, UserVote, Notification, QAComment


@admin.register(Knowledge_Space)
class KnowledgeSpaceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'color', 'initial', 'company', 'doc_responsible', 'secondary_responsible', 'created_at')
    list_filter = ('created_at', 'company')
    search_fields = ('name', 'id', 'doc_responsible__email', 'secondary_responsible__email')
    autocomplete_fields = ['doc_responsible', 'secondary_responsible']


@admin.register(QA)
class QAAdmin(admin.ModelAdmin):
    list_display = ('id', 'knowledge_space', 'question_title', 'upvotes', 'downvotes', 'views', 'created_at')
    list_filter = ('knowledge_space', 'answer_is_verified', 'created_at')
    search_fields = ('question_title', 'question_content', 'answer_content')
    readonly_fields = ('views', 'upvotes', 'downvotes')


@admin.register(UserVote)
class UserVoteAdmin(admin.ModelAdmin):
    list_display = ('user_id', 'qa', 'vote_type', 'created_at')
    list_filter = ('vote_type', 'created_at')
    search_fields = ('user_id', 'qa__question_title')


@admin.register(QAComment)
class QACommentAdmin(admin.ModelAdmin):
    list_display = ('id', 'qa', 'author_id', 'content_preview', 'upvotes', 'downvotes', 'is_edited', 'created_at')
    list_filter = ('is_edited', 'created_at', 'qa__knowledge_space')
    search_fields = ('content', 'author_id', 'qa__question_title')
    readonly_fields = ('upvotes', 'downvotes', 'created_at', 'updated_at')
    list_editable = ('is_edited',)
    
    def content_preview(self, obj):
        """Show a preview of the comment content"""
        return obj.content[:50] + "..." if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'type', 'severity', 'status', 'timestamp')
    list_filter = ('type', 'severity', 'status', 'timestamp')
    search_fields = ('title', 'details', 'user__email', 'company__name')
    readonly_fields = ('created_at', 'updated_at', 'timestamp')
    list_editable = ('status', 'severity')