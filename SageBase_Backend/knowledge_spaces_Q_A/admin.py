from django.contrib import admin
from django.utils.html import format_html
from django.contrib.admin import Simple<PERSON>istFilter
from .models import Knowledge_Space, QA, UserVote, Notification, QAComment


class ApprovalStatusFilter(SimpleListFilter):
    title = 'Approval Status'
    parameter_name = 'approval_status_filter'
    
    def lookups(self, request, model_admin):
        return (
            ('pending', 'Pending Review'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('needs_attention', 'Needs Attention'),
        )
    
    def queryset(self, request, queryset):
        if self.value() == 'needs_attention':
            return queryset.filter(approval_status='pending').exclude(answer_content='')
        elif self.value():
            return queryset.filter(approval_status=self.value())
        return queryset


@admin.register(Knowledge_Space)
class KnowledgeSpaceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'color', 'initial', 'company', 'doc_responsible', 'secondary_responsible', 'created_at')
    list_filter = ('created_at', 'company')
    search_fields = ('name', 'id', 'doc_responsible__email', 'secondary_responsible__email')
    autocomplete_fields = ['doc_responsible', 'secondary_responsible']


@admin.register(QA)
class QAAdmin(admin.ModelAdmin):
    list_display = ('id', 'knowledge_space', 'question_title_preview', 'colored_approval_status', 'answer_is_verified', 'total_tokens', 'upvotes', 'downvotes', 'views', 'created_by', 'created_at')
    list_filter = (ApprovalStatusFilter, 'knowledge_space', 'answer_is_verified', 'created_at', 'question_author_id', 'answer_author_id')
    search_fields = ('question_title', 'question_content', 'answer_content', 'question_author_id', 'answer_author_id', 'id')
    readonly_fields = ('views', 'upvotes', 'downvotes', 'created_at', 'updated_at', 'id')
    list_editable = ('answer_is_verified',)
    actions = ['approve_selected_qas', 'reject_selected_qas', 'mark_as_verified']
    list_per_page = 50
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'knowledge_space', 'approval_status', 'answer_is_verified', 'created_by')
        }),
        ('Question Details', {
            'fields': ('question_title', 'question_content', 'question_tags', 'question_author_id', 'question_author_avatar', 'question_author_name', 'question_tokens_count')
        }),
        ('Answer Details', {
            'fields': ('answer_content', 'answer_code', 'answer_explanation', 'answer_author_id', 'answer_author_name', 'answer_author_avatar', 'answer_tokens_count')
        }),
        ('Approval Workflow', {
            'fields': ('approval_reason', 'approved_by', 'approved_at')
        }),
        ('Engagement Metrics', {
            'fields': ('upvotes', 'downvotes', 'views', 'helpful_count')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        })
    )
    
    def question_title_preview(self, obj):
        """Show a preview of the question title"""
        return obj.question_title[:60] + "..." if len(obj.question_title) > 60 else obj.question_title
    question_title_preview.short_description = 'Question Title'
    
    def colored_approval_status(self, obj):
        """Display approval status with colors"""
        status_colors = {
            'pending': '#FFA500',    # Orange
            'approved': '#28A745',   # Green
            'rejected': '#DC3545',   # Red
        }
        color = status_colors.get(obj.approval_status, '#6C757D')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_approval_status_display()
        )
    colored_approval_status.short_description = 'Status'
    colored_approval_status.admin_order_field = 'approval_status'
    
    def total_tokens(self, obj):
        """Display total tokens for question and answer"""
        total = obj.get_total_tokens()
        return format_html('<span style="font-weight: bold;">{}</span>', total)
    total_tokens.short_description = 'Total Tokens'
    total_tokens.admin_order_field = 'question_tokens_count'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related for better performance"""
        return super().get_queryset(request).select_related('knowledge_space', 'approved_by')
    
    @admin.action(description='Approve selected QAs')
    def approve_selected_qas(self, request, queryset):
        updated = queryset.update(approval_status='approved')
        self.message_user(request, f'{updated} QAs were successfully approved.')
    
    @admin.action(description='Reject selected QAs')
    def reject_selected_qas(self, request, queryset):
        updated = queryset.update(approval_status='rejected')
        self.message_user(request, f'{updated} QAs were successfully rejected.')
    
    @admin.action(description='Mark selected QAs as verified')
    def mark_as_verified(self, request, queryset):
        updated = queryset.update(answer_is_verified=True)
        self.message_user(request, f'{updated} QAs were successfully marked as verified.')


@admin.register(UserVote)
class UserVoteAdmin(admin.ModelAdmin):
    list_display = ('user_id', 'qa', 'vote_type', 'created_at')
    list_filter = ('vote_type', 'created_at')
    search_fields = ('user_id', 'qa__question_title')


@admin.register(QAComment)
class QACommentAdmin(admin.ModelAdmin):
    list_display = ('id', 'qa', 'author_id', 'content_preview', 'upvotes', 'downvotes', 'is_edited', 'created_at')
    list_filter = ('is_edited', 'created_at', 'qa__knowledge_space')
    search_fields = ('content', 'author_id', 'qa__question_title')
    readonly_fields = ('upvotes', 'downvotes', 'created_at', 'updated_at')
    list_editable = ('is_edited',)
    
    def content_preview(self, obj):
        """Show a preview of the comment content"""
        return obj.content[:50] + "..." if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'type', 'severity', 'status', 'timestamp')
    list_filter = ('type', 'severity', 'status', 'timestamp')
    search_fields = ('title', 'details', 'user__email', 'company__name')
    readonly_fields = ('created_at', 'updated_at', 'timestamp')
    list_editable = ('status', 'severity')