"""
Chat Response Formatting Agent

This agent takes responses from the chat orchestrator and reformats them 
to different output formats (HTML, Markdown) without changing the content.
"""

import logging
import asyncio
from enum import Enum
from agents import Agent, Runner, ModelSettings
from agents import OpenAIChatCompletionsModel

# Set up logger
logger = logging.getLogger(__name__)
from utils.common_functions import clean_html

# Import Azure OpenAI client (required)
from konowledge_detection_agents.azure_config import async_azure_client,gpt4o_model


class ChatOutputFormat(Enum):
    """Supported output formats for response formatting"""
    HTML = "html"
    MARKDOWN = "markdown"
    MRKDWN = "mrkdwn"


class ChatResponseFormattingAgent:
    """Agent responsible for formatting chat responses to different output formats"""
    
    def __init__(self):
        """Initialize the formatting agent with Azure OpenAI client"""
        
        # Use Async Azure OpenAI client
        model = OpenAIChatCompletionsModel(
            model=gpt4o_model,
            openai_client=async_azure_client,
        )
        
        self.formatting_agent = Agent(
            name="Response Formatter",
            instructions="""You are a response formatting specialist with a single, critical task.

STRICT RULES - NEVER VIOLATE THESE:
1. NEVER change, modify, or alter the content, meaning, or information in any way
2. NEVER add new information, explanations, or content
3. NEVER remove any information, links, or references
4. ONLY reformat the presentation/structure of the existing content
5. Preserve ALL links, sources, references, and citations exactly as provided
6. Maintain the same tone and style of the original content
7. Never mention the style of the output format in the response, and never add "html" or "markdown" in the response, the output must be a good formatted text.
8. When you receive uri or urls, you must keep them as clickable links in the output.


YOUR TASK:
- Take the provided text from the user and reformat it to the requested output format
- The requested output format is added to the user's input.
- Ensure the formatted output is well-structured, visually appealing, and professional.
- Apply consistent styling and visual hierarchy throughout

CRITICAL: Your response must contain ONLY the reformatted content, no additional text, explanations, or meta-commentary.

FINALIZATION RULE: Your reply must contain only the reformatted content in the requested format. Do not output explanations. Do not leave the message empty under any circumstance.

""",
            model=model,
        )
        
        logger.info("✅ Chat Response Formatting Agent initialized successfully")
    
    async def format_response(self, response_text: str, output_format: ChatOutputFormat) -> str:
        """
        Format the response text to the specified output format
        
        Args:
            response_text: The original response text to format
            output_format: The desired output format (HTML, Markdown, or Slack mrkdwn)
            
        Returns:
            Formatted response text in the specified format
        """
        
        if not response_text or not response_text.strip():
            logger.warning("Empty response text provided to formatting agent")
            return response_text
        
        try:
            logger.info(f"🎨 Formatting response to {output_format.value} format...")
            
            # Prepare format-specific instructions
            format_instructions = {
                ChatOutputFormat.HTML: """The desired output format is HTML format with minimalistic Material Design styling:

CRITICAL FORMATTING RULES:
1. HEADER HIERARCHY: Use ONLY h3-h6 tags (NO h1 or h2 tags allowed)
   - Start with h3 for main sections
   - Use h4 for subsections
   - Use h5-h6 for deeper nesting if needed

2. SYNTAX COMPATIBILITY: NEVER mix incompatible elements
   - NO hyperlinks inside code blocks (<code> or <pre><code>)
   - NO HTML tags inside code blocks - keep code blocks pure text only
   - NO formatting inside code blocks (no bold, italic, etc.)

3. SOURCES AND LINKS: Place ALL sources and references at the end
   - Create a dedicated "Sources" or "References" section at the bottom
   - Use consistent formatting: "🔗 [Source Name](URL)" or "📚 [Reference Title](URL)"
   - Remove inline links from the main content and move them to the sources section
   - Use h4 tag for the sources section header

STYLING REQUIREMENTS:
- Use minimalistic Material Design color palette (grays, blues, subtle accents)
- Apply different text sizes and typography hierarchy (h3-h6, body text, captions)
- Include inline CSS styles for proper visual formatting
- Use appropriate spacing and margins for readability

MANDATORY LINK FORMATTING (in sources section only):
- ALL links must include a link emoji (🔗) before the link text
- ALL links must have hover effects with CSS (color change, underline, or subtle animation)
- Links must be visually obvious as clickable elements

SPECIAL ELEMENTS:
- Use proper HTML tables with styling for tabular data
- Use styled bullet points (ul/ol/li) with custom markers if needed
- Use proper code formatting with <code> or <pre><code> blocks with syntax highlighting styles
- Apply consistent spacing and visual hierarchy

HTML STRUCTURE:
- Use appropriate semantic HTML tags (h3-h6, p, ul/ol/li, table/tr/td/th, strong/em, code/pre)
- Include inline CSS styles for Material Design aesthetics
- Ensure proper HTML structure and readability
- Preserve all information exactly as provided
- Do not add HTML document structure (no <html>, <head>, <body> tags) - just the styled content markup

CONTENT ORGANIZATION:
- Main content first (with h3-h6 headers as appropriate)
- Sources/References section at the end with h4 header
- No inline links in main content - reference them in the sources section""",
                
                ChatOutputFormat.MARKDOWN: """Convert the following text to proper Markdown format:
- Use # for headers (# H1, ## H2, ### H3, etc.)
- Use **text** for bold, *text* for italics
- Use [text](url) for links
- Use - or * for unordered lists, 1. for ordered lists
- Ensure appropriate text sizes and design elements like tables, code snippets, and bullet points""",

                ChatOutputFormat.MRKDWN: """Convert the provided text to Slack mrkdwn format (not regular markdown)
BASIC mrkdwn FORMATTING:
- *bold text* (single asterisks for bold)
- _italic text_ (single underscores for italic)
- ~strikethrough text~ (tildes for strikethrough)
- `inline code` (backticks for monospace/inline code)

CODE BLOCKS:
- ```
code block
``` (triple backticks for multi-line code blocks)

BLOCKQUOTES:
- > blockquote (start line with > for blockquotes)

LISTS:
- - Item (hyphen and space for unordered lists)
- 1. Item (number, period, space for ordered lists)
- 2. Item (continue numbering manually)

LINKS:
- Links will be automatically formatted by Slack

MENTIONS:
- NOT ALLOWED TO USE MENTIONS

EMPHASIS:
- You are strictly limited to the mrkdwn syntax provided above.
- Keep formatting clean and professional"""
            }
            
            # Create input for the formatting agent
            format_instructions = format_instructions[output_format]
            #add specifi formatting rules to the instructions
            user_input = f"""Use these formatting rules: {format_instructions} \n\n 
            And here is the text to format:

            {response_text} """
            try:

                result = await asyncio.wait_for(
                    # Run the formatting agent
                    Runner.run(
                        self.formatting_agent,
                        user_input,
                        max_turns=3
                    ),timeout=10.0)

                # Prefer output_text; fall back gracefully
                formatted_response = (
                    getattr(result, "output_text", None)
                    or getattr(result, "final_output", None)
                )

                if not formatted_response:
                    logger.warning("Formatter produced no text; returning original.")
                    return response_text

                else:
                    logger.info(f"✅ Successfully formatted response to {output_format.value}")
                    logger.debug(f"Original length: {len(response_text)}, Formatted length: {len(formatted_response)}")
                
                return formatted_response
            except asyncio.TimeoutError:
                logger.error("⏰ Formatting timed out; returning original text.")
                return response_text
            
        except Exception as e:
            logger.exception("Full formatting error details:{e}")
            # Return original response if formatting fails
            return response_text

