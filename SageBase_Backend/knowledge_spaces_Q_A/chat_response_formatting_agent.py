"""
Chat Response Formatting Agent

This agent takes responses from the chat orchestrator and reformats them 
to different output formats (HTML, Markdown) without changing the content.
"""

import logging
from typing import Optional
from enum import Enum
from agents import Agent, Runner, ModelSettings
from agents import OpenAIChatCompletionsModel

# Set up logger
logger = logging.getLogger(__name__)

# Import Azure OpenAI client (required)
from konowledge_detection_agents.azure_config import async_azure_client, used_model


class ChatOutputFormat(Enum):
    """Supported output formats for response formatting"""
    HTML = "html"
    MARKDOWN = "markdown"
    MRKDWN = "mrkdwn"


class ChatResponseFormattingAgent:
    """Agent responsible for formatting chat responses to different output formats"""
    
    def __init__(self):
        """Initialize the formatting agent with Azure OpenAI client"""
        
        # Use Async Azure OpenAI client
        model = OpenAIChatCompletionsModel(
            model=used_model,
            openai_client=async_azure_client,
        )
        
        self.formatting_agent = Agent(
            name="Response Formatter",
            instructions="""You are a response formatting specialist with a single, critical task.

STRICT RULES - NEVER VIOLATE THESE:
1. NEVER change, modify, or alter the content, meaning, or information in any way
2. NEVER add new information, explanations, or content
3. NEVER remove any information, links, or references
4. ONLY reformat the presentation/structure of the existing content
5. Preserve ALL links, sources, references, and citations exactly as provided
6. Maintain the same tone and style of the original content

YOUR TASK:
- Take the provided text from the user and reformat it to the requested output format
- Ensure the formatted output is well-structured, visually appealing, and professional.
- Apply consistent styling and visual hierarchy throughout

CRITICAL: Your response must contain ONLY the reformatted content, no additional text, explanations, or meta-commentary.""",
            model=model,
            model_settings=ModelSettings(temperature=0.0),
        )
        
        logger.info("✅ Chat Response Formatting Agent initialized successfully")
    
    async def format_response(self, response_text: str, output_format: ChatOutputFormat) -> str:
        """
        Format the response text to the specified output format
        
        Args:
            response_text: The original response text to format
            output_format: The desired output format (HTML, Markdown, or Slack mrkdwn)
            
        Returns:
            Formatted response text in the specified format
        """
        
        if not response_text or not response_text.strip():
            logger.warning("Empty response text provided to formatting agent")
            return response_text
        
        try:
            logger.info(f"🎨 Formatting response to {output_format.value} format...")
            
            # Prepare format-specific instructions
            format_instructions = {
                ChatOutputFormat.HTML: """Convert the following text to clean, semantic HTML format with minimalistic Material Design styling:

STYLING REQUIREMENTS:
- Use minimalistic Material Design color palette (grays, blues, subtle accents)
- Apply different text sizes and typography hierarchy (h1-h6, body text, captions)
- Include inline CSS styles for proper visual formatting
- Use appropriate spacing and margins for readability

MANDATORY LINK FORMATTING:
- ALL links must include a link emoji (🔗) either before or after the link text
- ALL links must have hover effects with CSS (color change, underline, or subtle animation)
- Links must be visually obvious as clickable elements

SPECIAL ELEMENTS:
- Use proper HTML tables with styling for tabular data
- Use styled bullet points (ul/ol/li) with custom markers if needed
- Use proper code formatting with <code> or <pre><code> blocks with syntax highlighting styles
- Apply consistent spacing and visual hierarchy

HTML STRUCTURE:
- Use appropriate semantic HTML tags (h1-h6, p, ul/ol/li, table/tr/td/th, strong/em, a, code/pre)
- Include inline CSS styles for Material Design aesthetics
- Ensure proper HTML structure and readability
- Preserve all links and references exactly as provided
- Do not add HTML document structure (no <html>, <head>, <body> tags) - just the styled content markup""",
                
                ChatOutputFormat.MARKDOWN: """Convert the following text to proper Markdown format:
- Use # for headers (# H1, ## H2, ### H3, etc.)
- Use **text** for bold, *text* for italics
- Use [text](url) for links
- Use - or * for unordered lists, 1. for ordered lists
- Ensure appropriate text sizes and design elements like tables, code snippets, and bullet points""",

                ChatOutputFormat.MRKDWN: """Convert the provided text to Slack mrkdwn format:

BASIC FORMATTING:
- *bold text* (single asterisks for bold)
- _italic text_ (single underscores for italic)
- ~strikethrough text~ (tildes for strikethrough)
- `inline code` (backticks for monospace/inline code)

CODE BLOCKS:
- ```
code block
``` (triple backticks for multi-line code blocks)

BLOCKQUOTES:
- > blockquote (start line with > for blockquotes)

LISTS:
- * Item (asterisk and space for unordered lists)
- - Item (hyphen and space for unordered lists)
- 1. Item (number, period, space for ordered lists)
- 2. Item (continue numbering manually)
- Nested lists: indent sub-items with two spaces before bullet/number

LINKS:
- Use Slack's link formatting toolbar or keyboard shortcuts (Cmd/Ctrl + Shift + U)
- Links will be automatically formatted by Slack

MENTIONS:
- @username (@ symbol followed by username)
- <!subteam^ID> (user groups using subteam ID)
- #channel-name (# followed by channel name)

EMPHASIS:
- You are strictly limited to the mrkdwn syntax provided above.
- Use *bold text* for headings and emphasis instead of standard Markdown headers
- Keep formatting clean and professional"""
            }
            
            # Create input for the formatting agent
            format_instruction = format_instructions[output_format]
            conversation_input = [
                {"role": "user", "content": f"""{format_instruction}

TEXT TO FORMAT:
{response_text}"""}
            ]
            
            # Run the formatting agent
            result = await Runner.run(
                self.formatting_agent,
                conversation_input
            )
            
            formatted_response = result.final_output.strip()
            
            logger.info(f"✅ Successfully formatted response to {output_format.value}")
            logger.debug(f"Original length: {len(response_text)}, Formatted length: {len(formatted_response)}")
            
            return formatted_response
            
        except Exception as e:
            logger.error(f"❌ Error formatting response to {output_format.value}: {e}")
            logger.exception("Full formatting error details:")
            # Return original response if formatting fails
            return response_text

