from django.db import models
from django.db.models import Q
from django.utils import timezone
import uuid


def generate_qa_id():
    return f"qa-{str(uuid.uuid4())[:8]}"


def generate_project_id():
    return f"proj-{str(uuid.uuid4())[:8]}"


def generate_knowledge_space_id():
    return f"know-{str(uuid.uuid4())[:8]}"


class Knowledge_Space(models.Model):
    id = models.CharField(max_length=50, primary_key=True, default=generate_knowledge_space_id)
    name = models.Char<PERSON>ield(max_length=255)
    color = models.CharField(max_length=7)  # Hex color code
    initial = models.CharField(max_length=1)
    company = models.ForeignKey(
        'integrations.Company',
        on_delete=models.CASCADE,
        related_name='knowledge_spaces',
        help_text="The company this knowledge space belongs to (optional for user-owned spaces)",
        null=True,
        blank=True
    )
    user = models.ForeignKey(
        'integrations.User',
        on_delete=models.CASCADE,
        related_name='user_knowledge_spaces',
        help_text="The user this knowledge space belongs to (optional for company-owned spaces)",
        null=True,
        blank=True
    )
    # Responsible users for this knowledge space
    doc_responsible = models.ForeignKey(
        'integrations.User',
        on_delete=models.SET_NULL,
        related_name='doc_responsible_spaces',
        help_text="Primary responsible user for this knowledge space",
        null=True,
        blank=True
    )
    secondary_responsible = models.ForeignKey(
        'integrations.User',
        on_delete=models.SET_NULL,
        related_name='secondary_responsible_spaces',
        help_text="Secondary responsible user for this knowledge space",
        null=True,
        blank=True
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'knowledge_spaces'
        ordering = ['-created_at']
        # Ensure unique names per company
        unique_together = [
            ('name', 'company'),
        ]
        # Add constraint name for better error messages
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'company'],
                name='unique_knowledge_space_name_per_company',
                condition=models.Q(company__isnull=False),
            ),
        ]
    
    def __str__(self):
        return self.name
    
    @property
    def qa_count(self):
        return self.qas.count()
    
    @property
    def is_user_owned(self):
        """Check if this knowledge space is owned by a user"""
        return self.user is not None and self.company is None
    
    @property
    def is_company_owned(self):
        """Check if this knowledge space is owned by a company"""
        return self.company is not None and self.user is None
    
    @property
    def owner_name(self):
        """Get the name of the owner (user or company)"""
        if self.is_user_owned:
            return self.user.email if self.user else "Unknown User"
        elif self.is_company_owned:
            return self.company.name if self.company else "Unknown Company"
        return "Unknown Owner"
    
    def clean(self):
        """Ensure either user or company is set, but not both"""
        from django.core.exceptions import ValidationError
        if self.user and self.company:
            raise ValidationError("Knowledge space cannot be owned by both user and company")
        if not self.user and not self.company:
            raise ValidationError("Knowledge space must be owned by either user or company")
        
        # Check for duplicate names within the same company
        if self.company and self.name:
            existing = Knowledge_Space.objects.filter(
                name=self.name,
                company=self.company
            ).exclude(pk=self.pk)  # Exclude current instance if updating
            if existing.exists():
                raise ValidationError(f"A knowledge space with the name '{self.name}' already exists for this company.")


class QA(models.Model):
    id = models.CharField(max_length=50, primary_key=True, default=generate_qa_id)
    knowledge_space = models.ForeignKey(Knowledge_Space, on_delete=models.CASCADE, related_name='qas')
    
    # Question fields
    question_title = models.TextField()
    question_content = models.TextField()
    question_tags = models.JSONField(default=list)
    question_author_id = models.CharField(max_length=50)
    question_author_avatar = models.URLField(blank=True, null=True)
    question_author_name = models.CharField(max_length=255, blank=True, null=True)
    
    # Answer fields
    answer_content = models.TextField(blank=True, null=True)
    answer_code = models.TextField(blank=True, null=True)
    answer_explanation = models.TextField(blank=True, null=True)
    answer_author_id = models.CharField(max_length=50, blank=True, null=True)
    answer_author_name = models.CharField(max_length=255, blank=True, null=True)
    answer_author_avatar = models.URLField(blank=True, null=True)
    answer_is_verified = models.BooleanField(default=False)
    answer_tokens_count = models.IntegerField(default=0)
    question_tokens_count = models.IntegerField(default=0)
    # Approval workflow
    approval_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected')
        ],
        default='pending'
    )
    approval_reason = models.TextField(blank=True, null=True)  # Reason for rejection
    approved_by = models.ForeignKey(
        'integrations.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_qas'
    )
    approved_at = models.DateTimeField(blank=True, null=True)
    
    # Engagement metrics
    upvotes = models.IntegerField(default=0)
    downvotes = models.IntegerField(default=0)
    views = models.IntegerField(default=0)
    helpful_count = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'knowledge_spaces_Q_A_qa'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['question_author_id']),
            models.Index(fields=['approval_status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"QA: {self.question_title[:50]}..."
    
    def get_total_tokens(self):
        """Get total tokens for this Q&A pair"""
        return self.question_tokens_count + self.answer_tokens_count
    
    def increment_views(self):
        """Increment the view count for this QA"""
        self.views += 1
        self.save(update_fields=['views'])
    
    def track_page_view(self, user_id=None):
        """Track a page view and increment the total count"""
        # Create a page view record with just user_id
        QAPageView.objects.create(
            qa=self,
            user_id=user_id
        )
        
        # Increment the total view count
        self.increment_views()
        
        return self.views


class ChatSession(models.Model):
    """Model for managing persistent chat sessions between users and the LLM assistant"""
    
    class SessionType(models.TextChoices):
        SINGLE = 'single', 'Single Chat'
        GROUP = 'group', 'Group Chat'
    
    class Status(models.TextChoices):
        ACTIVE = 'active', 'Active'
        PAUSED = 'paused', 'Paused'
        ARCHIVED = 'archived', 'Archived'
        DELETED = 'deleted', 'Deleted'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session_type = models.CharField(max_length=20, choices=SessionType.choices, default=SessionType.SINGLE)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.ACTIVE)
    
    # Session metadata
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    workspace = models.CharField(max_length=100, default='sagebase')
    
    # Participants (for 1-to-1: user + assistant, for group: multiple users + assistant)
    participants = models.JSONField(default=list)  # List of user IDs/emails
    primary_user = models.ForeignKey('integrations.User', on_delete=models.CASCADE, related_name='chat_sessions')
    
    # Session context and preferences
    context_preferences = models.JSONField(default=dict)  # User preferences, platform settings, etc.
    metadata = models.JSONField(default=dict)  # Additional session metadata
    
    # Token tracking
    total_tokens_used = models.IntegerField(default=0)
    total_messages = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-last_activity']
        indexes = [
            models.Index(fields=['session_type']),
            models.Index(fields=['status']),
            models.Index(fields=['workspace']),
            models.Index(fields=['primary_user']),
            models.Index(fields=['last_activity']),
        ]
    
    def __str__(self):
        session_type_display = self.get_session_type_display()
        return f"{session_type_display}: {self.title or f'Session {self.id[:8]}'}"
    
    def get_participant_count(self):
        """Get number of participants in this session"""
        return len(self.participants)
    
    def add_participant(self, user_id: str):
        """Add a participant to the session"""
        if user_id not in self.participants:
            self.participants.append(user_id)
            self.save(update_fields=['participants'])
    
    def remove_participant(self, user_id: str):
        """Remove a participant from the session"""
        if user_id in self.participants:
            self.participants.remove(user_id)
            self.save(update_fields=['participants'])
    
    def is_participant(self, user_id: str) -> bool:
        """Check if a user is a participant in this session"""
        return user_id in self.participants
    
    def get_recent_messages(self, limit: int = 10):
        """Get recent messages for this session"""
        return self.messages.order_by('-created_at')[:limit]
    
    def update_activity(self):
        """Update the last activity timestamp"""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])


class ConversationMessage(models.Model):
    """Model for storing individual messages in chat sessions"""
    
    class MessageType(models.TextChoices):
        USER = 'user', 'User Message'
        ASSISTANT = 'assistant', 'Assistant Response'
        SYSTEM = 'system', 'System Message'
        ERROR = 'error', 'Error Message'
    
    class ContentType(models.TextChoices):
        TEXT = 'text', 'Text'
        CODE = 'code', 'Code'
        MARKDOWN = 'markdown', 'Markdown'
        JSON = 'json', 'JSON'
        MULTIMODAL = 'multimodal', 'Multimodal'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='messages')
    
    # Message content
    message_type = models.CharField(max_length=20, choices=MessageType.choices)
    content_type = models.CharField(max_length=20, choices=ContentType.choices, default=ContentType.TEXT)
    content = models.TextField()
    
    # Sender information
    sender_id = models.CharField(max_length=100)  # User ID or 'assistant'
    sender_name = models.CharField(max_length=255)
    sender_avatar = models.URLField(blank=True, null=True)
    
    # Message metadata
    tokens_count = models.IntegerField(default=0)
    metadata = models.JSONField(default=dict)  # Additional message metadata (references, tools used, etc.)
    
    # Parent message for threading (optional)
    parent_message = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='replies')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['session']),
            models.Index(fields=['message_type']),
            models.Index(fields=['sender_id']),
            models.Index(fields=['created_at']),
            models.Index(fields=['parent_message']),
        ]
    
    def __str__(self):
        return f"{self.get_message_type_display()}: {self.content[:50]}..."
    
    def get_reply_count(self):
        """Get number of replies to this message"""
        return self.replies.count()
    
    def add_reply(self, message):
        """Add a reply to this message"""
        message.parent_message = self
        message.save()
    
    def get_thread_messages(self):
        """Get all messages in this thread (including replies)"""
        return ConversationMessage.objects.filter(
            models.Q(id=self.id) | models.Q(parent_message=self)
        ).order_by('created_at')


class GroupChatSession(ChatSession):
    """Extended model for group chat sessions with additional group-specific features"""
    
    # Group-specific fields
    group_name = models.CharField(max_length=255)
    group_description = models.TextField(blank=True, null=True)
    
    # Group settings
    is_public = models.BooleanField(default=False)
    allow_join = models.BooleanField(default=True)
    max_participants = models.IntegerField(default=50)
    
    # Group roles and permissions
    moderators = models.JSONField(default=list)  # List of moderator user IDs
    admins = models.JSONField(default=list)  # List of admin user IDs
    
    # Group activity tracking
    total_messages_today = models.IntegerField(default=0)
    last_message_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['group_name']),
            models.Index(fields=['is_public']),
            models.Index(fields=['last_message_at']),
        ]
    
    def __str__(self):
        return f"Group: {self.group_name} ({self.get_participant_count()} participants)"
    
    def is_moderator(self, user_id: str) -> bool:
        """Check if a user is a moderator"""
        return user_id in self.moderators
    
    def is_admin(self, user_id: str) -> bool:
        """Check if a user is an admin"""
        return user_id in self.admins
    
    def add_moderator(self, user_id: str):
        """Add a moderator to the group"""
        if user_id not in self.moderators:
            self.moderators.append(user_id)
            self.save(update_fields=['moderators'])
    
    def remove_moderator(self, user_id: str):
        """Remove a moderator from the group"""
        if user_id in self.moderators:
            self.moderators.remove(user_id)
            self.save(update_fields=['moderators'])
    
    def can_join(self, user_id: str) -> bool:
        """Check if a user can join this group"""
        if not self.allow_join:
            return False
        if self.get_participant_count() >= self.max_participants:
            return False
        return True
    
    def update_message_stats(self):
        """Update message statistics"""
        today = timezone.now().date()
        today_messages = self.messages.filter(created_at__date=today).count()
        self.total_messages_today = today_messages
        self.last_message_at = timezone.now()
        self.save(update_fields=['total_messages_today', 'last_message_at'])


class UserVote(models.Model):
    VOTE_CHOICES = [
        ('up', 'Up'),
        ('down', 'Down'),
    ]
    
    id = models.BigAutoField(primary_key=True)
    user_id = models.CharField(max_length=50)
    qa = models.ForeignKey(QA, on_delete=models.CASCADE, related_name='user_votes')
    vote_type = models.CharField(max_length=4, choices=VOTE_CHOICES)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'user_votes'
        unique_together = ('user_id', 'qa')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user_id} voted {self.vote_type} on {self.qa.question_title[:30]}"


def generate_notification_id():
    return f"notif-{str(uuid.uuid4())[:8]}"


def generate_comment_id():
    return f"comment-{str(uuid.uuid4())[:8]}"


def generate_page_view_id():
    return f"view-{str(uuid.uuid4())[:8]}"


class QAPageView(models.Model):
    """Model for tracking individual page views of QAs"""
    
    id = models.CharField(max_length=50, primary_key=True, default=generate_page_view_id)
    qa = models.ForeignKey(QA, on_delete=models.CASCADE, related_name='page_views')
    
    # User information
    user_id = models.CharField(max_length=50, blank=True, null=True)  # Can be anonymous
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'qa_page_views'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['qa']),
            models.Index(fields=['user_id']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Page view by {self.user_id or 'anonymous'} on {self.qa.id}"


class QAComment(models.Model):
    """Model for comments on QA entries (Stack Overflow style)"""
    
    id = models.CharField(max_length=50, primary_key=True, default=generate_comment_id)
    qa = models.ForeignKey(QA, on_delete=models.CASCADE, related_name='comments')
    
    # Comment content
    content = models.TextField()
    
    # Author information (just the ID, names can be fetched from User model)
    author_id = models.CharField(max_length=50)
    
    # Comment metadata
    is_edited = models.BooleanField(default=False)
    edited_at = models.DateTimeField(blank=True, null=True)
    
    # Engagement metrics
    upvotes = models.IntegerField(default=0)
    downvotes = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'qa_comments'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['qa']),
            models.Index(fields=['author_id']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Comment by {self.author_id}: {self.content[:50]}..."
    
    def mark_as_edited(self):
        """Mark comment as edited"""
        self.is_edited = True
        self.edited_at = timezone.now()
        self.save()


class CommentVote(models.Model):
    """Model for votes on comments (thumbs up/down)"""
    
    VOTE_CHOICES = [
        ('up', 'Up'),
        ('down', 'Down'),
    ]
    
    id = models.BigAutoField(primary_key=True)
    user_id = models.CharField(max_length=50)
    comment = models.ForeignKey(QAComment, on_delete=models.CASCADE, related_name='user_votes')
    vote_type = models.CharField(max_length=4, choices=VOTE_CHOICES)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'comment_votes'
        unique_together = ('user_id', 'comment')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['comment']),
            models.Index(fields=['user_id']),
            models.Index(fields=['vote_type']),
        ]
    
    def __str__(self):
        return f"{self.user_id} voted {self.vote_type} on comment {self.comment.id}"


class Notification(models.Model):
    """Simple notification model"""
    
    id = models.CharField(max_length=50, primary_key=True, default=generate_notification_id)
    title = models.CharField(max_length=255)
    details = models.TextField()
    type = models.CharField(max_length=50)
    severity = models.CharField(max_length=20, default='medium')
    source = models.CharField(max_length=100, default='System')
    
    # Target
    user = models.ForeignKey('integrations.User', on_delete=models.CASCADE, null=True, blank=True)
    company = models.ForeignKey('integrations.Company', on_delete=models.CASCADE, null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=20, default='pending')
    accepted_at = models.DateTimeField(null=True, blank=True)
    ignored_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    timestamp = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'notifications'
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.title[:50]}..."
    
    def mark_as_accepted(self, user=None):
        self.status = 'accepted'
        self.accepted_at = timezone.now()
        self.save()
    
    def mark_as_ignored(self, user=None):
        self.status = 'ignored'
        self.ignored_at = timezone.now()
        self.save()
    
    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "details": self.details,
            "type": self.type,
            "severity": self.severity,
            "source": self.source,
            "timestamp": self.timestamp.isoformat(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


