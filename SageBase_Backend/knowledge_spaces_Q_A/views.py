import logging
import time
import uuid
from rest_framework import status
from rest_framework.status import HTTP_403_FORBIDDEN
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import F, Q
from django.utils import timezone

from .models import Knowledge_Space, QA, UserVote, Notification
from .serializers import (
    KnowledgeSpaceSerializer, QASerializer, QACreateSerializer, QAUpdateSerializer,
    VoteSerializer, VoteResponseSerializer, KnowledgeSpaceResponsibleUsersSerializer
)
# Import common authentication utilities
from integrations.auth_utils import require_authentication, require_admin_access
# Import WebSocket notification services
from Notifications.services import notification_service
from Notifications.types import NotificationPayload, NotificationType, NotificationLevel, NotificationTarget

# Import Q&A embedding utilities
from .utils import embed_q_a, delete_embedding_q_a, update_embedding_q_a

def can_user_approve_qa(user, qa_knowledge_space):
    """
    Check if user can approve/reject Q&As based on their role in the knowledge space.
    User can approve if they are:
    1. Admin of the company
    2. Primary responsible (doc_responsible) of the knowledge space
    3. Secondary responsible (secondary_responsible) of the knowledge space
    """
    try:
        # Check if user is admin
        if hasattr(user, 'role') and user.role == 'ADMIN':
            logger.debug(f"✅ User {user.email} is admin - can approve Q&A")
            return True
        
        # Check if user is primary responsible (doc_responsible)
        if qa_knowledge_space.doc_responsible == user:
            logger.debug(f"✅ User {user.email} is primary responsible - can approve Q&A")
            return True
        
        # Check if user is secondary responsible (secondary_responsible)
        if qa_knowledge_space.secondary_responsible == user:
            logger.debug(f"✅ User {user.email} is secondary responsible - can approve Q&A")
            return True
        
        logger.debug(f"❌ User {user.email} is not authorized to approve Q&A")
        return False
        
    except Exception as e:
        logger.error(f"❌ Error checking user approval permissions: {e}")
        return False

# Setup logger for this module
logger = logging.getLogger(__name__)



def get_user_context(request, user=None):
    """Extract user information from request. Modify based on your auth system."""
    if user:
        # Use the authenticated user object passed to the view
        user_id = str(user.id)
        user_name = user.email  # Use email as the user name
        user_avatar = getattr(user, 'avatar', None)
    else:
        # Fallback to request.user if no user object provided
        user_id = getattr(request.user, 'id', 'anonymous') if hasattr(request, 'user') else 'anonymous'
        user_name = getattr(request.user, 'email', 'Anonymous User') if hasattr(request, 'user') else 'Anonymous User'
        user_avatar = getattr(request.user, 'avatar', None) if hasattr(request, 'user') else None
    
    return {
        'user_id': user_id,
        'user_name': user_name,
        'user_avatar': user_avatar
    }


@api_view(['GET', 'POST', 'DELETE'])
@require_authentication
def knowledge_spaces(request, user):
    """
    GET /api/knowledge-spaces - List all knowledge spaces for user (company-owned and user-owned)
    POST /api/knowledge-spaces - Create new knowledge space (company-owned or user-owned)
    DELETE /api/knowledge-spaces/{knowledge_space_id} - Delete a knowledge space and all its Q&As
    """
    if request.method == 'GET':
        logger.info(f"📋 Listing knowledge spaces for user: {user.email}")
        
        # Get both company-owned and user-owned knowledge spaces
        company_spaces = Knowledge_Space.objects.filter(company=user.company)
        user_spaces = Knowledge_Space.objects.filter(user=user)

        for space in user_spaces:
            if space.is_user_owned:
                space.name = user.first_name + "(Private!)"
                space.save()
        
        # Combine and sort by creation date
        all_spaces = list(company_spaces) + list(user_spaces)
        all_spaces.sort(key=lambda x: x.created_at, reverse=True)
        
        serializer = KnowledgeSpaceSerializer(all_spaces, many=True)
        logger.debug(f"Found {len(company_spaces)} company spaces and {len(user_spaces)} user spaces")
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    elif request.method == 'POST':
        logger.info(f"🚀 Creating new knowledge space for user: {user.email}")
        
        # Add request context and user to serializer for validation
        serializer = KnowledgeSpaceSerializer(data=request.data, context={'request': request, 'user': user})
        if serializer.is_valid():
            # Always create company-owned knowledge space (shared)
            knowledge_space = serializer.save(company=user.company, user=None)
            logger.info(f"✅ Company knowledge space created: {knowledge_space.name} (ID: {knowledge_space.id})")
            
            # Log responsible users if assigned
            if knowledge_space.doc_responsible:
                logger.info(f"👤 Primary responsible user assigned: {knowledge_space.doc_responsible.email}")
            if knowledge_space.secondary_responsible:
                logger.info(f"👤 Secondary responsible user assigned: {knowledge_space.secondary_responsible.email}")
            
            
                
        logger.error(f"❌ Invalid project data: {serializer.errors}")
        return Response({
            'success': False,
            'error': 'Invalid data provided',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST', 'DELETE'])
@require_authentication
def knowledge_space_qas(request, user, knowledge_space_id):
    """
    GET /api/knowledge-spaces/{knowledge_space} - List all Q&As for a knowledge space (company or user owned)
    POST /api/knowledge-spaces/{knowledge_space} - Create new Q&A in knowledge space
    DELETE /api/knowledge-spaces/{knowledge_space} - Delete the knowledge space and all its Q&As
    """
    # Check access to knowledge space (either company-owned or user-owned)
    knowledge_space = get_object_or_404(
        Knowledge_Space, 
        Q(company=user.company) | Q(user=user),
        id=knowledge_space_id
    )
    
    # Check if user has access to this knowledge space
    has_access = (
        (knowledge_space.is_company_owned and knowledge_space.company == user.company) or
        (knowledge_space.is_user_owned and knowledge_space.user == user)
    )
    
    if not has_access:
        return Response({
            'success': False,
            'error': 'Access denied to this knowledge space'
        }, status=status.HTTP_403_FORBIDDEN)
    
    if request.method == 'GET':
        qas = QA.objects.filter(knowledge_space=knowledge_space, approval_status='approved')
        serializer = QASerializer(qas, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    elif request.method == 'POST':
        
        user_context = get_user_context(request, user)
        serializer = QACreateSerializer(
            data=request.data,
            context={**user_context, 'knowledge_space': knowledge_space}
        )
        if serializer.is_valid():
            qa = serializer.save()
            
            # Auto-approve QAs created in user-owned (private) knowledge spaces or if the user is an admin
            if knowledge_space.is_user_owned or user.role == 'ADMIN':
                qa.approval_status = 'approved'
                qa.approved_by = user
                qa.approved_at = timezone.now()
                qa.save(update_fields=['approval_status', 'approved_by', 'approved_at'])
                logger.info(f"✅ Auto-approved QA {qa.id} in user-owned knowledge space {knowledge_space.name}")
                
                # Embed the auto-approved QA for search functionality
                try:
                    embed_q_a(qa)
                    logger.info(f"✅ Embedded auto-approved QA {qa.id} for search")
                except Exception as embed_error:
                    logger.error(f"❌ Failed to embed auto-approved QA {qa.id}: {embed_error}")
                
                
                    
            # Send notification to the admin of the company
            if knowledge_space.is_company_owned:
                try:
                    from messaging.notification_service import NotificationService
                    import os
                    
                    # Get admin users for the company
                    from integrations.models import User
                    from integrations.models import SlackUserProfile
                    admin_users = User.objects.filter(company=user.company, role='ADMIN')
                    
                    if admin_users.exists():
                        notification_service = NotificationService()
                        
                        # Get credentials from environment variables
                        credentials = {}
                        slack_token = os.getenv('SLACK_BOT_TOKEN')
                        if slack_token:
                            credentials["slack"] = {"bot_token": slack_token}
                        
                        if credentials: 
                            # Use synchronous notification service
                            from knowledge_spaces_Q_A.sync_notification_service import send_authenticated_sync_notification
                            
                            # Send notifications to all admin users
                            for admin_user in admin_users:
                                try:
                                    # Get Slack profile for this admin
                                    slack_profile = SlackUserProfile.objects.filter(
                                        user=admin_user,
                                        is_active=True
                                    ).first()
                                    
                                    if slack_profile and slack_profile.slack_user_id:
                                        # Log the user details for debugging
                                        logger.info(f"🔄 Sending notification to admin {admin_user.email} with Slack user_id: '{slack_profile.slack_user_id}'")
                                        
                                        # Get frontend URL from environment
                                        from django.conf import settings
                                        frontend_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
                                        
                                        # Send the notification synchronously
                                        result = send_authenticated_sync_notification(
                                            credentials=credentials,
                                            user_id=slack_profile.slack_user_id,
                                            message_content=f"New QA '{qa.question_title}' has been created in knowledge space '{knowledge_space.name}' and requires approval.\n\nTo see and approve it, please visit {frontend_url}/qa-history"
                                        )
                                        
                                        if result.get('ok'):
                                            logger.info(f"✅ Notification sent to admin {admin_user.email} via Slack")
                                        else:
                                            logger.warning(f"⚠️ Failed to send notification to {admin_user.email}: {result.get('error')}")
                                    else:
                                        logger.warning(f"⚠️ No active Slack profile found for admin {admin_user.email}")
                                        
                                except Exception as e:
                                    logger.error(f"❌ Error sending notification to admin {admin_user.email}: {e}")
                                    # Continue with other admins even if one fails
                        else:
                            logger.warning("⚠️ No messaging credentials configured")
                    
                except Exception as e:
                    logger.error(f"❌ Error sending admin notification: {e}")
                    # Don't fail the QA creation if notification fails
            
            qa_serializer = QASerializer(qa)
            
            # Get updated list of approved Q&As for this knowledge space
            updated_qas = QA.objects.filter(knowledge_space=knowledge_space, approval_status='approved')
            updated_qas_serializer = QASerializer(updated_qas, many=True)
            
            # Add ownership info to response
            response_data = {
                'created_qa': qa_serializer.data,
                'updated_list': updated_qas_serializer.data,
                'knowledge_space_owner': 'company' if knowledge_space.is_company_owned else 'user'
            }
            
            return Response({
                'success': True,
                'data': response_data
            }, status=status.HTTP_201_CREATED)
        return Response({
            'success': False,
            'error': 'Invalid data provided',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        # Check admin access for deleting knowledge spaces (for company-owned spaces)
        # For user-owned spaces, the owner can always delete
        if knowledge_space.is_company_owned and user.role != 'ADMIN':
            return Response({
                'success': False,
                'error': 'Admin access required to delete company knowledge spaces'
            }, status=status.HTTP_403_FORBIDDEN)
        
        logger.warning(f"🗑️ Deleting knowledge space: {knowledge_space.name} (ID: {knowledge_space_id})")
        try:
            # Get Q&A count before deletion for response
            qa_count = knowledge_space.qas.count()
            logger.info(f"📊 Knowledge space has {qa_count} Q&As that will be deleted")
            
            # Delete the knowledge space (this will cascade delete all Q&As due to foreign key)
            knowledge_space.delete()
            
            logger.info(f"✅ Knowledge space deleted successfully: {knowledge_space_id}")
            return Response({
                'success': True,
                'message': f'Knowledge space "{knowledge_space.name}" deleted successfully',
                'data': {
                    'deleted_knowledge_space_id': knowledge_space_id,
                    'deleted_qa_count': qa_count
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"❌ Failed to delete knowledge space {knowledge_space_id}: {e}")
            return Response({
                'success': False,
                'error': 'Failed to delete knowledge space',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'PUT', 'DELETE'])
@require_authentication
def qa_detail(request, user, knowledge_space_id, qa_id):
    """
    GET /api/qa/{knowledge_space_id}/{id} - Get specific Q&A (increments views)
    PUT /api/qa/{knowledge_space_id}/{id} - Update Q&A
    DELETE /api/qa/{knowledge_space_id}/{id} - Delete Q&A
    """
    # First, check access to the knowledge space (either company-owned or user-owned)
    knowledge_space = get_object_or_404(
        Knowledge_Space, 
        Q(company=user.company) | Q(user=user),
        id=knowledge_space_id
    )
    
    # Check if user has access to this knowledge space
    has_access = (
        (knowledge_space.is_company_owned and knowledge_space.company == user.company) or
        (knowledge_space.is_user_owned and knowledge_space.user == user)
    )
    
    if not has_access:
        logger.warning(f"🚫 Access denied: User {user.email} tried to access knowledge space {knowledge_space_id}")
        return Response({
            'success': False,
            'error': 'Access denied to this knowledge space'
        }, status=status.HTTP_403_FORBIDDEN)
    
    logger.info(f"✅ Access granted: User {user.email} accessing {'private' if knowledge_space.is_user_owned else 'company'} knowledge space {knowledge_space_id}")
    
    # Now get the Q&A from this knowledge space
    qa = get_object_or_404(QA, id=qa_id, knowledge_space=knowledge_space)
    
    if request.method == 'GET':
        # Increment view count
        qa.increment_views()
        
        # Get user's current vote
        user_id = str(user.id)
        try:
            user_vote = UserVote.objects.get(user_id=user_id, qa=qa)
            current_user_vote = user_vote.vote_type
        except UserVote.DoesNotExist:
            current_user_vote = None
        
        serializer = QASerializer(qa)
        response_data = serializer.data
        response_data['userVote'] = current_user_vote  # Add user's vote to response
        
        return Response({
            'success': True,
            'data': response_data
        })
    
    elif request.method == 'PUT':
        # Check if user can update Q&As
        can_update = False
        
        if knowledge_space.is_company_owned:
            # For company-owned spaces, check admin permissions
            can_update = user.role == 'ADMIN'
        elif knowledge_space.is_user_owned:
            # For user-owned spaces, the owner can always update
            can_update = knowledge_space.user == user
        
        if not can_update:
            return Response({
                'success': False,
                'error': 'You are not authorized to update this Q&A.'
            }, status=status.HTTP_403_FORBIDDEN)
        
        user_context = get_user_context(request, user)
        serializer = QAUpdateSerializer(
            qa,
            data=request.data,
            context=user_context
        )
        if serializer.is_valid():
            updated_qa = serializer.save()
            
            # Update embedding if Q&A is approved
            if updated_qa.approval_status == 'approved':
                try:
                    update_embedding_q_a(updated_qa)
                    logger.info(f"🔄 Updated Q&A {qa_id} embedding after modification")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to update Q&A {qa_id} embedding: {e}")
            
            qa_serializer = QASerializer(updated_qa)
            
            # Get updated list of approved Q&As for this knowledge space
            updated_qas = QA.objects.filter(knowledge_space=updated_qa.knowledge_space, approval_status='approved')
            updated_qas_serializer = QASerializer(updated_qas, many=True)
            
            return Response({
                'success': True,
                'data': {
                    'updated_qa': qa_serializer.data,
                    'updated_list': updated_qas_serializer.data
                }
            })
        return Response({
            'success': False,
            'error': 'Invalid data provided',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        # Check if user can delete Q&As
        can_delete = False
        
        if knowledge_space.is_company_owned:
            # For company-owned spaces, check admin permissions
            can_delete = user.role == 'ADMIN'
        elif knowledge_space.is_user_owned:
            # For user-owned spaces, the owner can always delete
            can_delete = knowledge_space.user == user
        
        if not can_delete:
            return Response({
                'success': False,
                'error': 'You are not authorized to delete this Q&A.'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Remove Q&A from vector database embeddings before deletion
        try:
            delete_embedding_q_a(qa)
            logger.info(f"🗑️ Removed Q&A {qa_id} from vector database embeddings before deletion")
        except Exception as e:
            logger.warning(f"⚠️ Failed to remove Q&A {qa_id} from embeddings before deletion: {e}")
        
        qa.delete()
        
        # Get updated list of approved Q&As for this knowledge space
        updated_qas = QA.objects.filter(knowledge_space=qa.knowledge_space, approval_status='approved')
        updated_qas_serializer = QASerializer(updated_qas, many=True)
        
        return Response({
            'success': True,
            'message': 'Q&A deleted successfully',
            'data': {
                'updated_list': updated_qas_serializer.data
            }
        })


@api_view(['POST'])
@require_authentication
def qa_vote(request, user, knowledge_space_id, qa_id):
    """
    POST /api/qa/{knowledge_space_id}/{id}/vote - Vote on Q&A answer
    
    Vote types: 'up', 'down'
    - If user hasn't voted: creates new vote
    - If user votes same type again: removes vote (toggle)
    - If user votes different type: changes vote
    - Returns updated vote counts and user's current vote
    """
    # First, check access to the knowledge space (either company-owned or user-owned)
    knowledge_space = get_object_or_404(
        Knowledge_Space, 
        Q(company=user.company) | Q(user=user),
        id=knowledge_space_id
    )
    
    # Check if user has access to this knowledge space
    has_access = (
        (knowledge_space.is_company_owned and knowledge_space.company == user.company) or
        (knowledge_space.is_user_owned and knowledge_space.user == user)
    )
    
    if not has_access:
        return Response({
            'success': False,
            'error': 'Access denied to this knowledge space'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # Now get the Q&A from this knowledge space
    qa = get_object_or_404(QA, id=qa_id, knowledge_space=knowledge_space)
    user_id = str(user.id)
    
    serializer = VoteSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'error': 'Invalid vote type',
            'details': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    vote_type = serializer.validated_data['type']
    
    try:
        with transaction.atomic():
            # Check if user already voted
            try:
                user_vote = UserVote.objects.get(user_id=user_id, qa=qa)
                old_vote_type = user_vote.vote_type
                
                if old_vote_type == vote_type:
                    # User is voting the same type again - remove vote (toggle)
                    user_vote.delete()
                    
                    # Update vote counts
                    if vote_type == 'up':
                        QA.objects.filter(id=qa_id).update(upvotes=F('upvotes') - 1)
                    else:
                        QA.objects.filter(id=qa_id).update(downvotes=F('downvotes') - 1)
                    
                    current_user_vote = None
                    
                else:
                    # User is changing their vote
                    user_vote.vote_type = vote_type
                    user_vote.save()
                    
                    # Update vote counts
                    if old_vote_type == 'up' and vote_type == 'down':
                        QA.objects.filter(id=qa_id).update(
                            upvotes=F('upvotes') - 1,
                            downvotes=F('downvotes') + 1
                        )
                    elif old_vote_type == 'down' and vote_type == 'up':
                        QA.objects.filter(id=qa_id).update(
                            upvotes=F('upvotes') + 1,
                            downvotes=F('downvotes') - 1
                        )
                    
                    current_user_vote = vote_type
                    
            except UserVote.DoesNotExist:
                # User hasn't voted before - create new vote
                UserVote.objects.create(
                    user_id=user_id,
                    qa=qa,
                    vote_type=vote_type
                )
                
                # Update vote counts
                if vote_type == 'up':
                    QA.objects.filter(id=qa_id).update(upvotes=F('upvotes') + 1)
                else:
                    QA.objects.filter(id=qa_id).update(downvotes=F('downvotes') + 1)
                
                current_user_vote = vote_type
            
            # Refresh QA instance to get updated vote counts
            qa.refresh_from_db()
            
            # Return updated vote counts and user's current vote
            return Response({
                'success': True,
                'data': {
                    'votes': {
                        'upvotes': qa.upvotes,
                        'downvotes': qa.downvotes
                    },
                    'userVote': current_user_vote  # None if vote was removed, otherwise the vote type
                }
            })
    
    except Exception as e:
        return Response({
            'success': False,
            'error': 'Failed to process vote'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@require_authentication
def knowledge_space_detail(request, user, knowledge_space_id):
    """
    GET /api/knowledge-spaces/{knowledge_space}/detail - Get knowledge space details with QA count for user's company
    """
    # Check access to knowledge space (either company-owned or user-owned)
    knowledge_space = get_object_or_404(
        Knowledge_Space, 
        Q(company=user.company) | Q(user=user),
        id=knowledge_space_id
    )
    
    # Check if user has access to this knowledge space
    has_access = (
        (knowledge_space.is_company_owned and knowledge_space.company == user.company) or
        (knowledge_space.is_user_owned and knowledge_space.user == user)
    )
    
    if not has_access:
        return Response({
            'success': False,
            'error': 'Access denied to this knowledge space'
        }, status=status.HTTP_403_FORBIDDEN)
    
    serializer = KnowledgeSpaceSerializer(knowledge_space)
    return Response({
        'success': True,
        'data': serializer.data
    })





@api_view(['GET'])
@require_authentication
def qa_search(request, user):
    """
    GET /api/qa/search?q=query&project=project_id&tags=tag1,tag2
    Search Q&As with optional filters
    """
    query = request.GET.get('q', '')
    knowledge_space_id = request.GET.get('knowledge_space')
    tags = request.GET.get('tags', '').split(',') if request.GET.get('tags') else []
    
    # Search Q&As from both company-owned and user-owned knowledge spaces
    qas = QA.objects.filter(
        Q(knowledge_space__company=user.company) | Q(knowledge_space__user=user)
    )
    
    if query:
        qas = qas.filter(
            question_title__icontains=query
        ) | qas.filter(
            question_content__icontains=query
        ) | qas.filter(
            answer_content__icontains=query
        )
    
    if knowledge_space_id:
        # Ensure the knowledge space belongs to user's company or is user's private space
        qas = qas.filter(
            knowledge_space_id=knowledge_space_id
        ).filter(
            Q(knowledge_space__company=user.company) | Q(knowledge_space__user=user)
        )
    
    if tags:
        for tag in tags:
            if tag.strip():
                qas = qas.filter(question_tags__contains=[tag.strip()])
    
    serializer = QASerializer(qas, many=True)
    return Response({
        'success': True,
        'data': serializer.data
    })


@api_view(['GET'])
def health_check(request):
    """
    GET /api/knowledge-spaces/health - Health check endpoint
    """
    from django.db import connection
    
    try:
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        stats = {
            'total_knowledge_spaces': Knowledge_Space.objects.count(),
            'total_qas': QA.objects.count(),
            'total_votes': UserVote.objects.count()
        }
        
        return Response({
            'success': True,
            'status': 'healthy',
            'data': stats
        })
    except Exception as e:
        return Response({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([AllowAny])
def pending_approvals(request):
    """GET /api/knowledge-spaces/pending-approvals - List all Q&As pending approval for user's company"""
    
    # Extract user from token
    from integrations.auth_utils import get_user_from_token
    user = get_user_from_token(request)
    
    if not user:
        logger.debug("No valid token provided - returning empty list")
        return Response({
            'success': True,
            'data': [],
            'count': 0,
            'message': 'No authenticated user'
        })
    
    logger.info(f"📋 Listing pending approvals for user: {user.email}")
    
    # Check if user is admin
    if not hasattr(user, 'role') or user.role != 'ADMIN':
        logger.debug(f"Non-admin user {user.email} accessed pending approvals - returning empty list")
        return Response({
            'success': True,
            'data': [],
            'count': 0
        })
    
    # Get all Q&As from user's company that are pending approval
    pending_qas = QA.objects.filter(
        knowledge_space__company=user.company,
        approval_status='pending'
    ).select_related('knowledge_space').order_by('-created_at')
    
    serializer = QASerializer(pending_qas, many=True)
    logger.debug(f"Found {len(pending_qas)} pending Q&As for admin {user.email}")
    
    return Response({
        'success': True,
        'data': serializer.data,
        'count': len(pending_qas)
    })


@api_view(['GET'])
def pending_approvals_test(request):
    """Temporary test endpoint without authentication"""
    logger.info("📋 Testing pending approvals endpoint without authentication")
    
    # Get all Q&As that are pending approval (for testing)
    pending_qas = QA.objects.filter(
        approval_status='pending'
    ).select_related('knowledge_space').order_by('-created_at')
    
    serializer = QASerializer(pending_qas, many=True)
    logger.debug(f"Found {len(pending_qas)} pending Q&As")
    
    return Response({
        'success': True,
        'data': serializer.data,
        'count': len(pending_qas),
        'message': 'Test endpoint - no authentication required'
    })


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def approve_qa(request, qa_id):
    """POST /api/knowledge-spaces/qa/{qa_id}/approve - Approve a Q&A"""
    
    # Extract user from token
    from integrations.auth_utils import get_user_from_token
    user = get_user_from_token(request)
    
    if not user:
        logger.debug("No valid token provided for approve_qa")
        return Response({
            'success': False,
            'error': 'Authentication required'
        }, status=401)
    
    logger.info(f"✅ Approving Q&A {qa_id} by user: {user.email}")
    
    try:
        qa = QA.objects.get(id=qa_id, knowledge_space__company=user.company)
        
        # Check if user can approve this Q&A
        if not can_user_approve_qa(user, qa.knowledge_space):
            logger.warning(f"❌ User {user.email} is not authorized to approve Q&A {qa_id}")
            return Response({
                'success': False,
                'error': 'You are not authorized to approve this Q&A. Only admins, first assignees, or second assignees can approve Q&As.'
            }, status=status.HTTP_403_FORBIDDEN)
        
        if qa.approval_status != 'pending':
            return Response({
                'success': False,
                'error': f'Q&A is already {qa.approval_status}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if qa.approval_status != 'pending':
            return Response({
                'success': False,
                'error': f'Q&A is already {qa.approval_status}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Update approval status
        qa.approval_status = 'approved'
        qa.approved_by = user
        qa.approved_at = timezone.now()
        qa.save()
        
        logger.info(f"✅ Q&A {qa_id} approved by {user.email}")
        
        # Send WebSocket notification about approval
        try:
            notification = NotificationPayload(
                id=str(uuid.uuid4()),
                type=NotificationType.SUCCESS,
                level=NotificationLevel.MEDIUM,
                target=NotificationTarget.USER,
                title="Q&A Approved",
                message=f"Your Q&A '{qa.question_title}' has been approved!",
                target_id=qa.question_author_id,  # Send to the original author
                data={
                    "type": "QA_APPROVED",
                    "qaId": qa.id,
                    "title": qa.question_title,
                    "content": qa.question_content,
                    "author": "", # !TODO: Add logic to get name from User model
                    "approvedBy": user.first_name or user.email,
                    "approvedAt": timezone.now().isoformat(),
                    "knowledgeSpaceId": qa.knowledge_space.id,
                    "knowledgeSpaceName": qa.knowledge_space.name
                }
            )
            
            notification_service.send_to_user_email(qa.question_author_id, notification)
            logger.info(f"📨 Approval notification sent to {qa.question_author_id}")
            
        except Exception as e:
            logger.exception(f"Failed to send approval notification: {e}")
            
        # lets also embed the Q&A in the vector database
        embed_q_a(qa)
        
        return Response({
            'success': True,
            'message': 'Q&A approved successfully',
            'data': {
                'qa_id': qa.id,
                'status': 'approved',
                'approved_by': user.first_name or user.email,
                'approved_at': qa.approved_at.isoformat()
            }
        })
        
    except QA.DoesNotExist:
        logger.warning(f"⚠️ Q&A {qa_id} not found for user {user.email}")
        return Response({
            'success': False,
            'error': 'Q&A not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"❌ Error approving Q&A {qa_id}: {e}")
        return Response({
            'success': False,
            'error': 'Failed to approve Q&A'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def reject_qa(request, qa_id):
    """POST /api/knowledge-spaces/qa/{qa_id}/reject - Reject a Q&A"""
    
    # Extract user from token
    from integrations.auth_utils import get_user_from_token
    user = get_user_from_token(request)
    
    if not user:
        logger.debug("No valid token provided for reject_qa")
        return Response({
            'success': False,
            'error': 'Authentication required'
        }, status=401)
    
    
    try:
        qa = QA.objects.get(id=qa_id, knowledge_space__company=user.company)
        
        # Check if user can reject this Q&A
        if not can_user_approve_qa(user, qa.knowledge_space):
            logger.warning(f"❌ User {user.email} is not authorized to reject Q&A {qa_id}")
            return Response({
                'success': False,
                'error': 'You are not authorized to reject this Q&A. Only admins, first assignees, or second assignees can reject Q&As.'
            }, status=status.HTTP_403_FORBIDDEN)
        
        if qa.approval_status != 'pending':
            return Response({
                'success': False,
                'error': f'Q&A is already {qa.approval_status}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get rejection reason from request
        rejection_reason = request.data.get('reason', 'No reason provided')
        
        # Update approval status
        qa.approval_status = 'rejected'
        qa.approval_reason = rejection_reason
        qa.approved_by = user
        qa.approved_at = timezone.now()
        qa.save()
        
        logger.info(f"❌ Q&A {qa_id} rejected by {user.email} with reason: {rejection_reason}")
        
        # Send WebSocket notification about rejection
        try:
            notification = NotificationPayload(
                id=str(uuid.uuid4()),
                type=NotificationType.WARNING,
                level=NotificationLevel.MEDIUM,
                target=NotificationTarget.USER,
                title="Q&A Rejected",
                message=f"Your Q&A '{qa.question_title}' has been rejected.",
                target_id=qa.question_author_id,  # Send to the original author
                data={
                    "type": "QA_REJECTED",
                    "qaId": qa.id,
                    "title": qa.question_title,
                    "content": qa.question_content,
                    "author": "", # !TODO: Add logic to get name from User model
                    "rejectedBy": user.first_name or user.email,
                    "rejectedAt": timezone.now().isoformat(),
                    "reason": rejection_reason,
                    "knowledgeSpaceId": qa.knowledge_space.id,
                    "knowledgeSpaceName": qa.knowledge_space.name
                }
            )
            
            notification_service.send_to_user_email(qa.question_author_id, notification)
            logger.info(f"📨 Rejection notification sent to {qa.question_author_id}")
            
        except Exception as e:
            logger.exception(f"Failed to send rejection notification: {e}")
        
        # Remove Q&A from vector database embeddings if it was previously approved
        try:
            delete_embedding_q_a(qa)
            logger.info(f"🗑️ Removed Q&A {qa_id} from vector database embeddings")
        except Exception as e:
            logger.warning(f"⚠️ Failed to remove Q&A {qa_id} from embeddings: {e}")
        
        return Response({
            'success': True,
            'message': 'Q&A rejected successfully',
            'data': {
                'qa_id': qa.id,
                'status': 'rejected',
                'rejected_by': user.first_name or user.email,
                'rejected_at': qa.approved_at.isoformat(),
                'reason': rejection_reason
            }
        })
        
    except QA.DoesNotExist:
        logger.warning(f"⚠️ Q&A {qa_id} not found for user {user.email}")
        return Response({
            'success': False,
            'error': 'Q&A not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"❌ Error rejecting Q&A {qa_id}: {e}")
        return Response({
            'success': False,
            'error': 'Failed to reject Q&A'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@require_authentication
def notifications_list(request, user):
    """
    Get all notifications for the authenticated user or their company
    """
    try:
        # Get notifications for the user or their company
        notifications = Notification.objects.filter(
            Q(user=user) | Q(company=user.company)
        ).order_by('-timestamp')
        
        # Convert to API response format
        notifications_data = [notification.to_dict() for notification in notifications]
        
        return Response({
            'data': notifications_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"❌ Error getting notifications: {e}")
        return Response({
            'error': 'Failed to get notifications'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@require_authentication
def accept_notification(request, user, notification_id):
    """
    Accept a notification
    """
    try:
        # Get the notification
        notification = get_object_or_404(
            Notification,
            Q(user=user) | Q(company=user.company),
            id=notification_id
        )
        
        # Mark as accepted
        notification.mark_as_accepted(user)
        
        return Response({
            'success': True,
            'message': 'Notification accepted successfully'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"❌ Error accepting notification: {e}")
        return Response({
            'error': 'Failed to accept notification'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@require_authentication
def ignore_notification(request, user, notification_id):
    """
    Ignore a notification by deleting it
    """
    try:
        # Get the notification
        notification = get_object_or_404(
            Notification,
            Q(user=user) | Q(company=user.company),
            id=notification_id
        )
        
        # Delete the notification
        notification.delete()
        
        return Response({
            'success': True,
            'message': 'Notification deleted successfully'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"❌ Error deleting notification: {e}")
        return Response({
            'error': 'Failed to delete notification'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'PUT'])
@require_authentication
def knowledge_space_responsible_users(request, user, knowledge_space_id):
    """
    GET /api/knowledge-spaces/knowledge-space/{knowledge_space_id}/responsible-users
    - Get the responsible users for a knowledge space
    
    PUT /api/knowledge-spaces/knowledge-space/{knowledge_space_id}/responsible-users
    - Set the responsible users for a knowledge space
    """
    try:
        # Get the knowledge space
        knowledge_space = get_object_or_404(Knowledge_Space, id=knowledge_space_id)
        
        # Check if user has permission to manage this knowledge space
        # User can manage if:
        # 1. They are an admin
        # 2. They own the knowledge space (user-owned)
        # 3. They belong to the company that owns the knowledge space (company-owned)
        has_permission = (
            user.role == 'ADMIN' or 
            knowledge_space.user == user or 
            (knowledge_space.company and knowledge_space.company == user.company)
        )
        
        if not has_permission:
            logger.warning(f"⚠️ User {user.email} not authorized to manage knowledge space {knowledge_space_id}")
            return Response({
                'error': 'Not authorized to manage this knowledge space'
            }, status=status.HTTP_403_FORBIDDEN)
        
        if request.method == 'GET':
            # Return current responsible users
            serializer = KnowledgeSpaceResponsibleUsersSerializer(knowledge_space)
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        
        elif request.method == 'PUT':
            # Update responsible users
            serializer = KnowledgeSpaceResponsibleUsersSerializer(
                knowledge_space, 
                data=request.data, 
                partial=True
            )
            
            if serializer.is_valid():
                serializer.save()
                logger.info(f"✅ Updated responsible users for knowledge space {knowledge_space_id} by user {user.email}")
                
                return Response({
                    'success': True,
                    'message': 'Responsible users updated successfully',
                    'data': serializer.data
                }, status=status.HTTP_200_OK)
            else:
                logger.warning(f"⚠️ Invalid data for updating responsible users: {serializer.errors}")
                return Response({
                    'success': False,
                    'error': 'Invalid data',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
    
    except Knowledge_Space.DoesNotExist:
        logger.warning(f"⚠️ Knowledge space {knowledge_space_id} not found")
        return Response({
            'success': False,
            'error': 'Knowledge space not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"❌ Error managing responsible users for knowledge space {knowledge_space_id}: {e}")
        return Response({
            'success': False,
            'error': 'Failed to manage responsible users'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)