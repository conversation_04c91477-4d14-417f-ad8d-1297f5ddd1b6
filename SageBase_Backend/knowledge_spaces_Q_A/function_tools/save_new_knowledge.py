from agents import function_tool
from agents.run_context import RunContextWrapper
from asgiref.sync import sync_to_async
from knowledge_spaces_Q_A.chat_models import ChatUserContext
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


@function_tool
async def save_new_knowledge(context: RunContextWrapper[ChatUserContext], question_content: str, answer_content: str, question_title: str) -> str:
    """
    Save a new knowledge entry into the local knowledge base.
    
    This function creates a new Q&A instance in the knowledge base, embeds it for semantic search,
    and triggers notifications to relevant team members. The knowledge becomes immediately searchable
    and available for future question answering.
    
    Args:
        question_content (str): The question or topic being documented
        answer_content (str): The detailed answer, explanation, or knowledge content
        question_title (str): A concise title for the knowledge entry
    
    Returns:
        str: Success message with the created knowledge title, or error message if failed
        url: The url of the knowledge space qa
        Note: Any new QA needs to gets approved by an admin to be available for the users.
        
    """
    from knowledge_spaces_Q_A.utils import create_qa_instance
    from knowledge_spaces_Q_A.models import Knowledge_Space
    try:
        user = context.context.user if context.context and context.context.user else None
        company = context.context.company 
        
        # Use sync_to_async to get the knowledge space name
        def get_knowledge_space():
            return Knowledge_Space.objects.filter(company=company).first()
        
        knowledge_space_instance = await sync_to_async(get_knowledge_space, thread_sensitive=False)()

        # Use sync_to_async since create_qa_instance is a sync function
        # Determine created_by source
        created_by_value = None
        try:
            if context and context.context and getattr(context.context, 'platform_source', None):
                source = context.context.platform_source.lower()
                if source == 'slack':
                    created_by_value = 'Sagebase - Slack Agent'
                elif source == 'discord':
                    created_by_value = 'Sagebase - Discord Agent'
        except Exception:
            created_by_value = None

        qa_instance = await sync_to_async(create_qa_instance, thread_sensitive=False)(
            company=company,
            question_content=question_content,
            answer_content=answer_content,
            question_title=question_title,
            knowledge_space_instance=knowledge_space_instance,
            user_id=user.id if user else None,
            created_by=created_by_value,
        )      
        
        if qa_instance is None:
            return "❌ Failed to create QA instance"

        if qa_instance:
            logger.info(f"📚 Successfully created new knowledge: {qa_instance.question_title}")
            qa_url = f"{settings.FRONTEND_BASE_URL}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}"
            # Send admin notification for company-owned knowledge spaces
            try:
                from knowledge_spaces_Q_A.utils import send_admin_notification_for_new_qa
                # Wrap with sync_to_async since we're in an async context
                await sync_to_async(send_admin_notification_for_new_qa, thread_sensitive=False)(
                    qa_instance, knowledge_space_instance, user
                )
                logger.info(f"📨 Admin notification sent for new QA {qa_instance.id}")
            except Exception as notification_error:
                logger.warning(f"⚠️ Failed to send admin notification: {notification_error}")
            return f"✅ New knowledge embedded successfully: {qa_instance.question_title}.One admin shall review it and approve it to become available for the users.\n\nTo see and approve it, please visit {qa_url}"
        else:
            logger.error(f"❌ Failed to embed new knowledge: {qa_instance.question_title}")
    except Exception as e:
        logger.exception(f"❌ Error saving new knowledge: {e}")
        return f"❌ Error saving new knowledge: {str(e)}"
