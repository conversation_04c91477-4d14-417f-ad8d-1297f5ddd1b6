from agents import function_tool
from agents.run_context import RunContextWrapper
from asgiref.sync import sync_to_async
from knowledge_spaces_Q_A.chat_models import ChatUserContext
import logging

logger = logging.getLogger(__name__)


@function_tool
async def save_new_knowledge(context: RunContextWrapper[ChatUserContext], question_content: str, answer_content: str, question_title: str) -> str:
    """
    Save a new knowledge entry into the local knowledge base.
    
    This function creates a new Q&A instance in the knowledge base, embeds it for semantic search,
    and triggers notifications to relevant team members. The knowledge becomes immediately searchable
    and available for future question answering.
    
    Args:
        question_content (str): The question or topic being documented
        answer_content (str): The detailed answer, explanation, or knowledge content
        question_title (str): A concise title for the knowledge entry
    
    Returns:
        str: Success message with the created knowledge title, or error message if failed
        
    """
    from knowledge_spaces_Q_A.utils import create_qa_instance
    from knowledge_spaces_Q_A.models import Knowledge_Space
    try:
        user = context.context.user if context.context and context.context.user else None
        company = context.context.company 
        
        # Use sync_to_async to get the knowledge space name
        def get_knowledge_space_name():
            return Knowledge_Space.objects.filter(company=company).first().name
        
        knowledge_space_name = await sync_to_async(get_knowledge_space_name, thread_sensitive=False)()

        # Use sync_to_async since create_qa_instance is a sync function
        qa_instance = await sync_to_async(create_qa_instance, thread_sensitive=False)(
            company=company,
            question_content=question_content,
            answer_content=answer_content,
            question_title=question_title,
            knowledge_space_name=knowledge_space_name,
            user_id=user.id if user else None,
        )      
        
        if qa_instance is None:
            return "❌ Failed to create QA instance"

        if qa_instance:
            logger.info(f"📚 Successfully created new knowledge: {qa_instance.question_title}")
            return f"✅ New knowledge embedded successfully: {qa_instance.question_title}.One admin shall review it and approve it to become available for the users."
        else:
            logger.error(f"❌ Failed to embed new knowledge: {qa_instance.question_title}")
    except Exception as e:
        logger.exception(f"❌ Error saving new knowledge: {e}")
        return f"❌ Error saving new knowledge: {str(e)}"
