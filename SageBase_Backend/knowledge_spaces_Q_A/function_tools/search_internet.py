from agents import function_tool
import json
import requests
import logging

logger = logging.getLogger(__name__)

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class InternetSearchKnowledgeGraph(BaseModel):
    title: Optional[str]
    imageUrl: Optional[str]
    description: Optional[str]
    descriptionSource: Optional[str]
    descriptionLink: Optional[str]
    attributes: Optional[Dict[str, Any]]

class InternetSearchOrganicResult(BaseModel):
    title: str
    link: str
    snippet: str
    position: int
    date: Optional[str] = None

class InternetSearchRelatedSearch(BaseModel):
    query: str

class InternetSearchParameters(BaseModel):
    q: str
    type: str
    engine: str

class InternetSearchResult(BaseModel):
    searchParameters: InternetSearchParameters
    knowledgeGraph: Optional[InternetSearchKnowledgeGraph]
    organic: List[InternetSearchOrganicResult]
    relatedSearches: Optional[List[InternetSearchRelatedSearch]]
    credits: Optional[int]


@function_tool  
def search_internet(query: str, max_results: int = 3) -> InternetSearchResult:
    """Search the internet for information about the query using Serper API and return structured results."""
    try:
        logger.info(f"@function_tool -> 🔍 Calling tool search_internet for query: {query}")
        url = "https://google.serper.dev/search"

        payload = json.dumps({
            "q": query,
            "type": "search",
            "engine": "google"
        })
        headers = {
            'X-API-KEY': '8188fb5f9b43a5e6784a4f4dce6fb0b74d880f27',
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        data = response.json()

        # Parse searchParameters
        search_parameters = InternetSearchParameters(
            q=data.get("searchParameters", {}).get("q", query),
            type=data.get("searchParameters", {}).get("type", "search"),
            engine=data.get("searchParameters", {}).get("engine", "google")
        ) if "searchParameters" in data else InternetSearchParameters(q=query, type="search", engine="google")

        # Parse knowledgeGraph
        kg = data.get("knowledgeGraph")
        knowledge_graph = None
        if kg:
            knowledge_graph = InternetSearchKnowledgeGraph(
                title=kg.get("title"),
                imageUrl=kg.get("imageUrl"),
                description=kg.get("description"),
                descriptionSource=kg.get("descriptionSource"),
                descriptionLink=kg.get("descriptionLink"),
                attributes=kg.get("attributes")
            )

        # Parse organic results
        organic_results = []
        for item in data.get("organic", [])[:max_results]:
            organic_results.append(
                InternetSearchOrganicResult(
                    title=item.get("title", ""),
                    link=item.get("link", ""),
                    snippet=item.get("snippet", ""),
                    position=item.get("position", 0),
                    date=item.get("date")
                )
            )

        # Parse related searches
        related_searches = []
        for rel in data.get("relatedSearches", []):
            if "query" in rel:
                related_searches.append(InternetSearchRelatedSearch(query=rel["query"]))

        credits = data.get("credits")

        return InternetSearchResult(
            searchParameters=search_parameters,
            knowledgeGraph=knowledge_graph,
            organic=organic_results,
            relatedSearches=related_searches if related_searches else None,
            credits=credits
        )

    except Exception as e:
        logger.error(f"Internet search error: {str(e)}")
        # Return a minimal error result
        return InternetSearchResult(
            searchParameters=InternetSearchParameters(q=query, type="search", engine="google"),
            knowledgeGraph=None,
            organic=[],
            relatedSearches=None,
            credits=None
        )
