from agents import function_tool
import json
import os
import requests
import logging
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class InternetSearchKnowledgeGraph(BaseModel):
    title: Optional[str]
    imageUrl: Optional[str]
    description: Optional[str]
    descriptionSource: Optional[str]
    descriptionLink: Optional[str]
    attributes: Optional[Dict[str, Any]]

class InternetSearchOrganicResult(BaseModel):
    title: str
    link: str
    snippet: str
    position: int
    date: Optional[str] = None

class InternetSearchRelatedSearch(BaseModel):
    query: str

class InternetSearchParameters(BaseModel):
    q: str
    type: str
    engine: str

class InternetSearchResult(BaseModel):
    searchParameters: InternetSearchParameters
    knowledgeGraph: Optional[InternetSearchKnowledgeGraph]
    organic: List[InternetSearchOrganicResult]
    relatedSearches: Optional[List[InternetSearchRelatedSearch]]
    credits: Optional[int]
    # Small summary the model can read quickly
    summary: Optional[str] = None





@function_tool
def search_internet(query: str, max_results: int = 3) -> str:
    """
    Serper search. Always returns a JSON string so traces show output.
    On any failure, returns a compact JSON error payload (never None).
    """
    import os, json, requests
    from typing import List

    logger.info(f"@function_tool -> 🔍 search_internet for: {query!r}")

    try:
        api_key = os.getenv("SERPER_API_KEY")
        if not api_key:
            raise RuntimeError("SERPER_API_KEY is not set")

        url = "https://google.serper.dev/search"
        payload = {"q": query, "type": "search", "engine": "google"}
        headers = {"X-API-KEY": api_key, "Content-Type": "application/json"}

        # ---- HTTP (with timeout) ----
        resp = requests.post(url, headers=headers, json=payload, timeout=10)
        resp.raise_for_status()
        data = resp.json()

        # ---- Parse safely (truncate to keep traces readable) ----
        sp = data.get("searchParameters") or {}
        search_parameters = {
            "q": sp.get("q", query),
            "type": sp.get("type", "search"),
            "engine": sp.get("engine", "google"),
        }

        kg = data.get("knowledgeGraph") or None  # already JSON-serializable

        organic = []
        for item in (data.get("organic") or [])[: max(0, min(max_results, 5))]:
            organic.append({
                "title": (item.get("title") or "")[:300],
                "link": item.get("link") or "",
                "snippet": (item.get("snippet") or "")[:500],
                "position": int(item.get("position") or 0),
                "date": item.get("date"),
            })

        related = [{"query": r["query"]}
                   for r in (data.get("relatedSearches") or [])[:5]
                   if isinstance(r, dict) and "query" in r] or None

        summary = f"Top result: {organic[0]['title']} — {organic[0]['link']}" if organic else None

        result = {
            "searchParameters": search_parameters,
            "knowledgeGraph": kg,
            "organic": organic,
            "relatedSearches": related,
            "credits": data.get("credits"),
            "summary": summary,
        }

        # ✅ Return JSON string (most compatible with tracers/tool wrappers)
        return json.dumps(result, ensure_ascii=False)

    except requests.Timeout as e:
        err = f"timeout: {e}"
    except requests.HTTPError as e:
        err = f"http_{getattr(resp, 'status_code', '?')}: {getattr(resp, 'text', '')[:300]}"
    except json.JSONDecodeError as e:
        err = f"invalid_json: {e}"
    except Exception as e:
        err = f"{type(e).__name__}: {e}"

    # 🛡️ Never return None — return an error payload the model can still use
    logger.error(f"search_internet error -> {err}")
    fallback = {
        "searchParameters": {"q": query, "type": "search", "engine": "google"},
        "knowledgeGraph": None,
        "organic": [],
        "relatedSearches": None,
        "credits": None,
        "summary": None,
        "error": err,
    }
    return json.dumps(fallback, ensure_ascii=False)
