from agents import function_tool
from agents.run_context import RunContextWrapper
import logging

from knowledge_spaces_Q_A.chat_models import Chat<PERSON>ser<PERSON>ontext
from vectordb.interfaces import get_chroma_collection

logger = logging.getLogger(__name__)


@function_tool
async def search_local_knowledge(context: RunContextWrapper[ChatUserContext], query: str, max_results: int = 5) -> str:
    """Search for answers to the user question in the local knowledge base of the company
    This search shall have precedence over the internet search.
    query is the text that can be used to search in the local knowledge base.
    max_results is the maximum number of results to return.
    
    This function automatically searches in both:
    1. Company workspace (shared knowledge)
    2. User's private workspace (personal knowledge)
    
    Private Q&As are filtered to ensure users only see their own private content.
    """
    try:
        from integrations.models import User
        from asgiref.sync import sync_to_async
        
        # Initialize default company name
        user_email = context.context.user.email if context.context and context.context.user else None
        collections_to_search = context.context.collections_to_search #company id/user id/private collection id
        sources_to_search = context.context.sources_to_search #google drive/confluence....

        #now lets add the private collection to the collections_to_search
        if user_email:
            # Wrap synchronous ORM operation in sync_to_async
            get_user = sync_to_async(User.objects.get, thread_sensitive=False)
            user_instance = await get_user(email=user_email)
            if str(user_instance.id) not in collections_to_search:
                collections_to_search.append(str(user_instance.id))

        # Convert string sources to DataSource enum values
        from vectordb.interfaces import convert_sources_to_enum
        converted_sources = convert_sources_to_enum(sources_to_search)

        logger.info(f"🔍 Searching in collections_ids: {collections_to_search}")
        logger.info(f"🔍 Searching with sources: {sources_to_search} -> {[s.value for s in converted_sources]}")

        
        # Search in all workspaces in parallel and combine results
        all_search_results = []
        
        async def search_collections(collection):
            """Search a single workspace"""
            try:
                # Get workspace-specific collection manager
                collection_manager = get_chroma_collection(collection)
                
                # Wrap the search function in sync_to_async with thread_sensitive=False
                search_async = sync_to_async(collection_manager.search_across_sources, thread_sensitive=False)
                search_results = await search_async(
                    query=query,
                    sources=converted_sources,  # Use converted DataSource enum values
                    limit=max_results,
                    search_mode="hybrid",  # Use hybrid search for best results
                    textual_boost=1.2,  # Boost textual matches slightly
                    # Note: workspace parameter removed as it's handled by collection_manager
                )
                
                if search_results:
                    logger.info(f"✅ Found {len(search_results)} results in workspace '{collection}'")
                    return search_results
                else:
                    logger.debug(f"📭 No results found in workspace '{collection}'")
                    return []
                    
            except Exception as e:
                logger.warning(f"⚠️ Error searching workspace '{collection}': {e}")
                return []
        
        # Run all workspace searches in parallel
        import asyncio
        search_tasks = [search_collections(collection) for collection in collections_to_search]
        workspace_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # Combine all results
        for results in workspace_results:
            if isinstance(results, list):  # Successful search
                all_search_results.extend(results)
            else:  # Exception occurred
                logger.warning(f"⚠️ Workspace search failed with exception: {results}")
        
        if not all_search_results:
            return "No relevant information found in the local knowledge base."
        
        # Sort combined results by score (best first)
        all_search_results.sort(key=lambda x: x.score, reverse=True)
        
        # Take top results
        top_results = all_search_results[:max_results]
        
        # Pass the SearchResult objects directly
        formatted_results = []
        for i, result in enumerate(top_results, 1):
            # Check if this is a private Q&A and if the current user has access
            is_private = 'private' in result.metadata.tags if result.metadata.tags else False
            
            if is_private:
                # Wrap synchronous ORM operation in sync_to_async
                get_user = sync_to_async(User.objects.get, thread_sensitive=False)
                user_instance = await get_user(email=user_email)
                if str(user_instance.id) not in result.metadata.tags:
                   continue # user has no access to this private Q&A --> skip it
      
            # Convert SearchResult to dict with all its properties
            # Build metadata dict with only non-empty values
            metadata = {}
            metadata_fields = {
                "source": result.metadata.source.value if result.metadata.source else None,
                "source_id": result.metadata.source_id,
                "title": result.metadata.title,
                "author": result.metadata.author,
                "content_type": result.metadata.content_type,
                "tags": result.metadata.tags,
                "parent_id": result.metadata.parent_id,
                "permissions": result.metadata.permissions,
                "custom_fields": result.metadata.custom_fields,
                "created_at": result.metadata.created_at.isoformat() if result.metadata.created_at else None,
                "updated_at": result.metadata.updated_at.isoformat() if result.metadata.updated_at else None
            }
            
            # Only include non-empty values
            for key, value in metadata_fields.items():
                if value is not None and value != "" and value != [] and value != {}:
                    metadata[key] = value
            
            result_dict = {
                "content": result.content,
                "metadata": metadata,
                "url": result.metadata.url if result.metadata.url else ""
            }
            
            formatted_results.append(result_dict)
        
        # Convert to JSON string for the response
        import json
        response = f"Local knowledge base search results ({len(formatted_results)} found):\n\n{json.dumps(formatted_results, indent=2)}"
        logger.info(f"✅ Found {len(formatted_results)} total results from vectordb across {len(collections_to_search)} workspaces")
        return response
        
    except ImportError as e:
        logger.warning(f"⚠️ Vectordb not available: {e}")
        # Fallback to basic response
        return """
        Vector database not available. Basic knowledge:
        - For '@babel/preset-env' errors: npm install @babel/preset-env --save-dev
        - For project verizon installation issues: contact wissem
        """
    except Exception as e:
        logger.exception(f"❌ Error searching local knowledgeBAse{e}")
        return f"Error searching local knowledge base: {str(e)}"

