# Generated by Django 5.2.4 on 2025-08-09 03:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('knowledge_spaces_Q_A', '0010_qapageview_qapageview_qa_page_vie_qa_id_5dd8e9_idx_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='qapageview',
            name='referrer',
        ),
        migrations.RemoveField(
            model_name='qapageview',
            name='session_id',
        ),
        migrations.RemoveField(
            model_name='qapageview',
            name='user_agent',
        ),
        migrations.RemoveField(
            model_name='qapageview',
            name='user_ip',
        ),
        migrations.AddField(
            model_name='knowledge_space',
            name='doc_responsible',
            field=models.ForeignKey(blank=True, help_text='Primary responsible user for this knowledge space', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='doc_responsible_spaces', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='knowledge_space',
            name='secondary_responsible',
            field=models.ForeignKey(blank=True, help_text='Secondary responsible user for this knowledge space', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='secondary_responsible_spaces', to=settings.AUTH_USER_MODEL),
        ),
    ]
