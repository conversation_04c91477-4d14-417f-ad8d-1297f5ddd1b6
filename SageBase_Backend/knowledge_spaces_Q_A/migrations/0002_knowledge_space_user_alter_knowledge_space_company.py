# Generated by Django 5.2.4 on 2025-07-29 18:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0003_teaminvitation'),
        ('knowledge_spaces_Q_A', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='knowledge_space',
            name='user',
            field=models.ForeignKey(blank=True, help_text='The user this knowledge space belongs to (optional for company-owned spaces)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_knowledge_spaces', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='knowledge_space',
            name='company',
            field=models.ForeignKey(blank=True, help_text='The company this knowledge space belongs to (optional for user-owned spaces)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_spaces', to='integrations.company'),
        ),
    ]
