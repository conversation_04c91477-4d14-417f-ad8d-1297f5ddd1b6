# Generated by Django 4.2 on 2025-08-01 12:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import knowledge_spaces_Q_A.models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0005_repochange_cross_repo_monitor'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('knowledge_spaces_Q_A', '0006_merge_20250730_1910'),
    ]

    operations = [
        migrations.AlterModelTable(
            name='qa',
            table='knowledge_spaces_Q_A_qa',
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.CharField(default=knowledge_spaces_Q_A.models.generate_notification_id, max_length=50, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('details', models.TextField()),
                ('type', models.Char<PERSON>ield(max_length=50)),
                ('severity', models.Char<PERSON>ield(default='medium', max_length=20)),
                ('source', models.CharField(default='System', max_length=100)),
                ('status', models.CharField(default='pending', max_length=20)),
                ('accepted_at', models.DateTimeField(blank=True, null=True)),
                ('ignored_at', models.DateTimeField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='integrations.company')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'notifications',
                'ordering': ['-timestamp'],
            },
        ),
    ]
