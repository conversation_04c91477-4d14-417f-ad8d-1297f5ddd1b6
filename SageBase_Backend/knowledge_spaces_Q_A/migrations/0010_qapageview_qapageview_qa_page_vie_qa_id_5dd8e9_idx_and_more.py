# Generated by Django 4.2 on 2025-08-08 17:09

from django.db import migrations, models
import django.db.models.deletion
import knowledge_spaces_Q_A.models


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_spaces_Q_A", "0009_alter_chatsession_session_type_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="QAPageView",
            fields=[
                (
                    "id",
                    models.CharField(
                        default=knowledge_spaces_Q_A.models.generate_page_view_id,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("user_id", models.CharField(blank=True, max_length=50, null=True)),
                ("user_ip", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("session_id", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ("referrer", models.URL<PERSON>ield(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "qa",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="page_views",
                        to="knowledge_spaces_Q_A.qa",
                    ),
                ),
            ],
            options={
                "db_table": "qa_page_views",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="qapageview",
            index=models.Index(fields=["qa"], name="qa_page_vie_qa_id_5dd8e9_idx"),
        ),
        migrations.AddIndex(
            model_name="qapageview",
            index=models.Index(
                fields=["user_id"], name="qa_page_vie_user_id_269071_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="qapageview",
            index=models.Index(
                fields=["created_at"], name="qa_page_vie_created_95a0bc_idx"
            ),
        ),
    ]
