# Generated by Django 4.2 on 2025-08-08 15:55

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import knowledge_spaces_Q_A.models


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_spaces_Q_A", "0008_alter_knowledge_space_unique_together_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="chatsession",
            name="session_type",
            field=models.CharField(
                choices=[("single", "Single Chat"), ("group", "Group Chat")],
                default="single",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="qa",
            name="question_author_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name="QAComment",
            fields=[
                (
                    "id",
                    models.Char<PERSON>ield(
                        default=knowledge_spaces_Q_A.models.generate_comment_id,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("content", models.TextField()),
                ("author_id", models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ("is_edited", models.<PERSON><PERSON><PERSON><PERSON>ield(default=False)),
                ("edited_at", models.DateTimeField(blank=True, null=True)),
                ("upvotes", models.IntegerField(default=0)),
                ("downvotes", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "qa",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="knowledge_spaces_Q_A.qa",
                    ),
                ),
            ],
            options={
                "db_table": "qa_comments",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="CommentVote",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("user_id", models.CharField(max_length=50)),
                (
                    "vote_type",
                    models.CharField(
                        choices=[("up", "Up"), ("down", "Down")], max_length=4
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "comment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_votes",
                        to="knowledge_spaces_Q_A.qacomment",
                    ),
                ),
            ],
            options={
                "db_table": "comment_votes",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="qacomment",
            index=models.Index(fields=["qa"], name="qa_comments_qa_id_ebd877_idx"),
        ),
        migrations.AddIndex(
            model_name="qacomment",
            index=models.Index(
                fields=["author_id"], name="qa_comments_author__c82563_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="qacomment",
            index=models.Index(
                fields=["created_at"], name="qa_comments_created_596518_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="commentvote",
            index=models.Index(
                fields=["comment"], name="comment_vot_comment_d56df6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="commentvote",
            index=models.Index(
                fields=["user_id"], name="comment_vot_user_id_998163_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="commentvote",
            index=models.Index(
                fields=["vote_type"], name="comment_vot_vote_ty_d02f9a_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="commentvote",
            unique_together={("user_id", "comment")},
        ),
    ]
