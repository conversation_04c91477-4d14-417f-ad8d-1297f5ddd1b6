# Generated by Django 4.2 on 2025-07-26 20:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('knowledge_spaces_Q_A', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_type', models.CharField(choices=[('one_to_one', '1-to-1 Session'), ('group', 'Group Chat')], default='one_to_one', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('paused', 'Paused'), ('archived', 'Archived'), ('deleted', 'Deleted')], default='active', max_length=20)),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('workspace', models.CharField(default='sagebase', max_length=100)),
                ('participants', models.JSONField(default=list)),
                ('context_preferences', models.JSONField(default=dict)),
                ('metadata', models.JSONField(default=dict)),
                ('total_tokens_used', models.IntegerField(default=0)),
                ('total_messages', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='ConversationMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message_type', models.CharField(choices=[('user', 'User Message'), ('assistant', 'Assistant Response'), ('system', 'System Message'), ('error', 'Error Message')], max_length=20)),
                ('content_type', models.CharField(choices=[('text', 'Text'), ('code', 'Code'), ('markdown', 'Markdown'), ('json', 'JSON'), ('multimodal', 'Multimodal')], default='text', max_length=20)),
                ('content', models.TextField()),
                ('sender_id', models.CharField(max_length=100)),
                ('sender_name', models.CharField(max_length=255)),
                ('sender_avatar', models.URLField(blank=True, null=True)),
                ('tokens_count', models.IntegerField(default=0)),
                ('metadata', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='qa',
            name='edited_at',
        ),
        migrations.RemoveField(
            model_name='qa',
            name='edited_by_avatar',
        ),
        migrations.RemoveField(
            model_name='qa',
            name='edited_by_name',
        ),
        migrations.AlterField(
            model_name='qa',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AddIndex(
            model_name='qa',
            index=models.Index(fields=['question_author_id'], name='knowledge_s_questio_47111b_idx'),
        ),
        migrations.AddIndex(
            model_name='qa',
            index=models.Index(fields=['approval_status'], name='knowledge_s_approva_22ab3b_idx'),
        ),
        migrations.AddIndex(
            model_name='qa',
            index=models.Index(fields=['created_at'], name='knowledge_s_created_838ce7_idx'),
        ),
        migrations.AlterModelTable(
            name='qa',
            table=None,
        ),
        migrations.CreateModel(
            name='GroupChatSession',
            fields=[
                ('chatsession_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='knowledge_spaces_Q_A.chatsession')),
                ('group_name', models.CharField(max_length=255)),
                ('group_description', models.TextField(blank=True, null=True)),
                ('is_public', models.BooleanField(default=False)),
                ('allow_join', models.BooleanField(default=True)),
                ('max_participants', models.IntegerField(default=50)),
                ('moderators', models.JSONField(default=list)),
                ('admins', models.JSONField(default=list)),
                ('total_messages_today', models.IntegerField(default=0)),
                ('last_message_at', models.DateTimeField(blank=True, null=True)),
            ],
            bases=('knowledge_spaces_Q_A.chatsession',),
        ),
        migrations.AddField(
            model_name='conversationmessage',
            name='parent_message',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replies', to='knowledge_spaces_Q_A.conversationmessage'),
        ),
        migrations.AddField(
            model_name='conversationmessage',
            name='session',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='knowledge_spaces_Q_A.chatsession'),
        ),
        migrations.AddField(
            model_name='chatsession',
            name='primary_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='groupchatsession',
            index=models.Index(fields=['group_name'], name='knowledge_s_group_n_6038aa_idx'),
        ),
        migrations.AddIndex(
            model_name='groupchatsession',
            index=models.Index(fields=['is_public'], name='knowledge_s_is_publ_0d92e1_idx'),
        ),
        migrations.AddIndex(
            model_name='groupchatsession',
            index=models.Index(fields=['last_message_at'], name='knowledge_s_last_me_6d191a_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationmessage',
            index=models.Index(fields=['session'], name='knowledge_s_session_3ddef8_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationmessage',
            index=models.Index(fields=['message_type'], name='knowledge_s_message_28e4ef_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationmessage',
            index=models.Index(fields=['sender_id'], name='knowledge_s_sender__569011_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationmessage',
            index=models.Index(fields=['created_at'], name='knowledge_s_created_91841f_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationmessage',
            index=models.Index(fields=['parent_message'], name='knowledge_s_parent__27cc1a_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['session_type'], name='knowledge_s_session_63561a_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['status'], name='knowledge_s_status_948925_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['workspace'], name='knowledge_s_workspa_8fcfb2_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['primary_user'], name='knowledge_s_primary_94112f_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['last_activity'], name='knowledge_s_last_ac_97865d_idx'),
        ),
    ]
