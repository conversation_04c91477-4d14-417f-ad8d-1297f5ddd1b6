# Generated by Django 5.2.4 on 2025-07-27 08:29

import django.db.models.deletion
import django.utils.timezone
import knowledge_spaces_Q_A.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('integrations', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Knowledge_Space',
            fields=[
                ('id', models.CharField(default=knowledge_spaces_Q_A.models.generate_project_id, max_length=50, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('color', models.Char<PERSON>ield(max_length=7)),
                ('initial', models.CharField(max_length=1)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(help_text='The company this knowledge space belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_spaces', to='integrations.company')),
            ],
            options={
                'db_table': 'knowledge_spaces',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QA',
            fields=[
                ('id', models.CharField(default=knowledge_spaces_Q_A.models.generate_qa_id, max_length=50, primary_key=True, serialize=False)),
                ('question_title', models.TextField()),
                ('question_content', models.TextField()),
                ('question_tags', models.JSONField(default=list)),
                ('question_author_id', models.CharField(max_length=50)),
                ('question_author_name', models.CharField(default='Unknown', max_length=255)),
                ('question_author_avatar', models.URLField(blank=True, null=True)),
                ('answer_content', models.TextField(blank=True, null=True)),
                ('answer_code', models.TextField(blank=True, null=True)),
                ('answer_explanation', models.TextField(blank=True, null=True)),
                ('answer_author_id', models.CharField(blank=True, max_length=50, null=True)),
                ('answer_author_name', models.CharField(blank=True, max_length=255, null=True)),
                ('answer_author_avatar', models.URLField(blank=True, null=True)),
                ('answer_is_verified', models.BooleanField(default=False)),
                ('answer_tokens_count', models.IntegerField(default=0)),
                ('question_tokens_count', models.IntegerField(default=0)),
                ('approval_status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('approval_reason', models.TextField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('upvotes', models.IntegerField(default=0)),
                ('downvotes', models.IntegerField(default=0)),
                ('views', models.IntegerField(default=0)),
                ('helpful_count', models.IntegerField(default=0)),
                ('edited_by_name', models.CharField(blank=True, max_length=255, null=True)),
                ('edited_by_avatar', models.URLField(blank=True, null=True)),
                ('edited_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_qas', to=settings.AUTH_USER_MODEL)),
                ('knowledge_space', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qas', to='knowledge_spaces_Q_A.knowledge_space')),
            ],
            options={
                'db_table': 'knowledge_spaces_Q_A_qa',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserVote',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('user_id', models.CharField(max_length=50)),
                ('vote_type', models.CharField(choices=[('up', 'Up'), ('down', 'Down')], max_length=4)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('qa', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_votes', to='knowledge_spaces_Q_A.qa')),
            ],
            options={
                'db_table': 'user_votes',
                'ordering': ['-created_at'],
                'unique_together': {('user_id', 'qa')},
            },
        ),
    ]
