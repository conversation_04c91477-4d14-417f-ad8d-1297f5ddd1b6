# Generated by Django 5.2.4 on 2025-08-03 20:18

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0007_alter_notificationsettings_frequency_hours'),
        ('knowledge_spaces_Q_A', '0007_alter_qa_table_notification'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='knowledge_space',
            unique_together={('name', 'company')},
        ),
        migrations.AddConstraint(
            model_name='knowledge_space',
            constraint=models.UniqueConstraint(condition=models.Q(('company__isnull', False)), fields=('name', 'company'), name='unique_knowledge_space_name_per_company'),
        ),
    ]
