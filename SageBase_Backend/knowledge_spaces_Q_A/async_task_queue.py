"""
Async Task Queue for handling background async operations from sync Django views.
This provides a global async context with a task stack for non-blocking operations.
"""

import asyncio
import threading
import queue
import logging
from typing import Callable, Any, Dict, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

@dataclass
class AsyncTask:
    """Represents an async task to be executed"""
    id: str
    func: Callable
    args: tuple
    kwargs: dict
    created_at: datetime
    priority: int = 0  # Higher number = higher priority
    retry_count: int = 0
    max_retries: int = 3

class AsyncTaskQueue:
    """Global async task queue for background processing"""
    
    def __init__(self):
        self.task_queue = queue.PriorityQueue()
        self.running = False
        self.worker_thread = None
        self.event_loop = None
        self._lock = threading.Lock()
        
    def start(self):
        """Start the async task queue worker"""
        with self._lock:
            if not self.running:
                self.running = True
                self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
                self.worker_thread.start()
                logger.info("🚀 Async task queue started")
    
    def stop(self):
        """Stop the async task queue worker"""
        with self._lock:
            if self.running:
                self.running = False
                if self.event_loop:
                    self.event_loop.call_soon_threadsafe(self.event_loop.stop)
                logger.info("🛑 Async task queue stopped")
    
    def _worker_loop(self):
        """Main worker loop that runs in a separate thread"""
        try:
            # Create new event loop for this thread
            self.event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.event_loop)
            
            logger.info("🔄 Async worker loop started")
            
            while self.running:
                try:
                    # Get task from queue (non-blocking)
                    try:
                        priority, task = self.task_queue.get_nowait()
                    except queue.Empty:
                        # No tasks, sleep briefly
                        asyncio.sleep(0.1)
                        continue
                    
                    # Execute the task
                    self.event_loop.run_until_complete(self._execute_task(task))
                    
                except Exception as e:
                    logger.error(f"❌ Error in worker loop: {e}")
                    asyncio.sleep(1)  # Brief pause on error
                    
        except Exception as e:
            logger.error(f"❌ Fatal error in worker loop: {e}")
        finally:
            if self.event_loop:
                self.event_loop.close()
    
    async def _execute_task(self, task: AsyncTask):
        """Execute a single async task"""
        try:
            logger.debug(f"🔄 Executing task {task.id}")
            await task.func(*task.args, **task.kwargs)
            logger.debug(f"✅ Task {task.id} completed successfully")
            
        except Exception as e:
            logger.error(f"❌ Task {task.id} failed: {e}")
            
            # Retry logic
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                logger.info(f"🔄 Retrying task {task.id} (attempt {task.retry_count}/{task.max_retries})")
                
                # Re-queue with lower priority and exponential backoff
                import time
                await asyncio.sleep(2 ** task.retry_count)  # Exponential backoff
                self._add_task(task)
            else:
                logger.error(f"❌ Task {task.id} failed after {task.max_retries} retries")
    
    def _add_task(self, task: AsyncTask):
        """Add a task to the queue"""
        # Priority is negative so higher priority tasks come first
        self.task_queue.put((-task.priority, task))
    
    def enqueue_task(self, func: Callable, *args, priority: int = 0, **kwargs) -> str:
        """Enqueue an async task for background execution"""
        task_id = str(uuid.uuid4())
        task = AsyncTask(
            id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            created_at=datetime.now(),
            priority=priority
        )
        
        self._add_task(task)
        logger.debug(f"📝 Task {task_id} enqueued with priority {priority}")
        return task_id
    
    def enqueue_notification(self, notification_func: Callable, *args, **kwargs) -> str:
        """Convenience method for enqueueing notification tasks"""
        return self.enqueue_task(notification_func, *args, priority=10, **kwargs)
    
    def enqueue_async_operation(self, async_func: Callable, *args, priority: int = 5, **kwargs) -> str:
        """Generic method to enqueue any async operation"""
        return self.enqueue_task(async_func, *args, priority=priority, **kwargs)

# Global instance
task_queue = AsyncTaskQueue()

def get_task_queue() -> AsyncTaskQueue:
    """Get the global task queue instance"""
    return task_queue

def start_task_queue():
    """Start the global task queue"""
    task_queue.start()

def stop_task_queue():
    """Stop the global task queue"""
    task_queue.stop()

def enqueue_async_task(func: Callable, *args, priority: int = 0, **kwargs) -> str:
    """Enqueue an async task for background execution"""
    return task_queue.enqueue_task(func, *args, priority=priority, **kwargs)

def enqueue_notification(func: Callable, *args, **kwargs) -> str:
    """Enqueue a notification task with high priority"""
    return task_queue.enqueue_notification(func, *args, **kwargs)

def enqueue_async_operation(async_func: Callable, *args, priority: int = 5, **kwargs) -> str:
    """Generic function to enqueue any async operation"""
    return task_queue.enqueue_async_operation(async_func, *args, priority=priority, **kwargs) 