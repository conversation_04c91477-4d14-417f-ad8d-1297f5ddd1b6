"""
API views for QA comments functionality (Stack Overflow style)
"""

import logging
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

from integrations.auth_utils import require_authentication
from .models import QA, QAComment, CommentVote
from .serializers import (
    CommentSerializer, CommentCreateSerializer, CommentUpdateSerializer,
    CommentVoteSerializer, CommentVoteResponseSerializer
)

logger = logging.getLogger(__name__)


@api_view(['GET', 'POST'])
@require_authentication
def get_qa_comments(request, user, qa_id):
    """
    Get all comments for a specific QA (GET)
    Create a new comment for a QA (POST)
    
    URL: GET/POST /api/qa/{qa_id}/comments
    """
    if request.method == 'GET':
        try:
            qa = get_object_or_404(QA, id=qa_id)
            
            # Get comments ordered by creation date
            comments = QAComment.objects.filter(qa=qa).order_by('created_at')
            
            # Serialize comments
            comment_serializer = CommentSerializer(comments, many=True)
            
            return Response({
                'success': True,
                'qa_id': qa_id,
                'comments': comment_serializer.data,
                'total_comments': len(comments)
            })
            
        except Exception as e:
            logger.error(f"Error getting comments for QA {qa_id}: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    elif request.method == 'POST':
        try:
            qa = get_object_or_404(QA, id=qa_id)
            
            # Prepare context for serializer
            context = {
                'user_id': str(user.id),
                'qa': qa
            }
            
            # Validate and create comment
            serializer = CommentCreateSerializer(data=request.data, context=context)
            if serializer.is_valid():
                comment = serializer.save()
                
                # Return the created comment
                comment_serializer = CommentSerializer(comment)
                
                return Response({
                    'success': True,
                    'message': 'Comment created successfully',
                    'comment': comment_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'error': 'Invalid comment data',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"Error creating comment for QA {qa_id}: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT', 'DELETE'])
@require_authentication
def update_comment(request, user, qa_id, comment_id):
    """
    Update an existing comment (PUT)
    Delete a comment (DELETE)
    
    URL: PUT/DELETE /api/qa/{qa_id}/comments/{comment_id}
    """
    try:
        qa = get_object_or_404(QA, id=qa_id)
        comment = get_object_or_404(QAComment, id=comment_id, qa=qa)
        
        # Check if user is the author of the comment
        if comment.author_id != str(user.id):
            return Response({
                'success': False,
                'error': 'You can only edit your own comments'
            }, status=status.HTTP_403_FORBIDDEN)
        
        if request.method == 'PUT':
            # Prepare context for serializer
            context = {
                'user_name': user.first_name or user.email
            }
            
            # Validate and update comment
            serializer = CommentUpdateSerializer(comment, data=request.data, context=context)
            if serializer.is_valid():
                updated_comment = serializer.save()
                
                # Return the updated comment
                comment_serializer = CommentSerializer(updated_comment)
                
                return Response({
                    'success': True,
                    'message': 'Comment updated successfully',
                    'comment': comment_serializer.data
                })
            else:
                return Response({
                    'success': False,
                    'error': 'Invalid comment data',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
        
        elif request.method == 'DELETE':
            # Delete the comment
            comment.delete()
            
            return Response({
                'success': True,
                'message': 'Comment deleted successfully'
            })
            
    except Exception as e:
        logger.error(f"Error updating/deleting comment {comment_id} for QA {qa_id}: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@require_authentication
def vote_comment(request, user, qa_id, comment_id):
    """
    Vote on a comment (thumbs up/down)
    
    URL: POST /api/qa/{qa_id}/comments/{comment_id}/vote
    Body: {
        "vote_type": "up" or "down"
    }
    """
    try:
        qa = get_object_or_404(QA, id=qa_id)
        comment = get_object_or_404(QAComment, id=comment_id, qa=qa)
        
        # Validate vote data
        serializer = CommentVoteSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': 'Invalid vote data',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        vote_type = serializer.validated_data['vote_type']
        user_id = str(user.id)
        
        # Check if user already voted on this comment
        existing_vote = CommentVote.objects.filter(
            user_id=user_id,
            comment=comment
        ).first()
        
        if existing_vote:
            # User already voted, update the vote
            if existing_vote.vote_type == vote_type:
                # Same vote type, remove the vote
                existing_vote.delete()
                
                # Update comment vote counts
                if vote_type == 'up':
                    comment.upvotes = max(0, comment.upvotes - 1)
                else:
                    comment.downvotes = max(0, comment.downvotes - 1)
                comment.save()
                
                user_vote = 'none'
            else:
                # Different vote type, change the vote
                existing_vote.vote_type = vote_type
                existing_vote.save()
                
                # Update comment vote counts
                if vote_type == 'up':
                    comment.upvotes += 1
                    comment.downvotes = max(0, comment.downvotes - 1)
                else:
                    comment.downvotes += 1
                    comment.upvotes = max(0, comment.upvotes - 1)
                comment.save()
                
                user_vote = vote_type
        else:
            # New vote
            CommentVote.objects.create(
                user_id=user_id,
                comment=comment,
                vote_type=vote_type
            )
            
            # Update comment vote counts
            if vote_type == 'up':
                comment.upvotes += 1
            else:
                comment.downvotes += 1
            comment.save()
            
            user_vote = vote_type
        
        # Return updated vote information
        response_data = {
            'votes': {
                'upvotes': comment.upvotes,
                'downvotes': comment.downvotes
            },
            'user_vote': user_vote
        }
        
        response_serializer = CommentVoteResponseSerializer(response_data)
        
        return Response({
            'success': True,
            'message': 'Vote updated successfully',
            'vote_data': response_serializer.data
        })
        
    except Exception as e:
        logger.error(f"Error voting on comment {comment_id} for QA {qa_id}: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@require_authentication
def qa_views(request, user, qa_id):
    """
    Get the view count for a QA (GET)
    Increment the view count for a QA (POST)
    
    URL: GET/POST /api/qa/{qa_id}/views
    """
    try:
        qa = get_object_or_404(QA, id=qa_id)
        
        if request.method == 'GET':
            return Response({
                'success': True,
                'qa_id': qa_id,
                'views': qa.views
            })
        
        elif request.method == 'POST':
            # Track the page view with just user ID
            total_views = qa.track_page_view(user_id=str(user.id))
            
            return Response({
                'success': True,
                'message': 'Page view tracked successfully',
                'views': total_views
            })
        
    except Exception as e:
        logger.error(f"Error with views for QA {qa_id}: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
