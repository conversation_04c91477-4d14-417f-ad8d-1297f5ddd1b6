from django.urls import path
from . import views
from . import api_views
from . import comment_views

app_name = 'knowledge_spaces_Q_A'

urlpatterns = [
    # Knowledge Spaces (Projects) endpoints - RESTful style (no trailing slashes)
    path('knowledge-spaces', views.knowledge_spaces, name='knowledge_spaces'),
    path('knowledge-spaces/health', views.health_check, name='health_check'),
    path('knowledge-spaces/pending-approvals', views.pending_approvals, name='pending_approvals'),
    
    # Standalone Q&A Management endpoints (no project context) - RESTful style
    path('knowledge-spaces/qa/<str:qa_id>/approve', views.approve_qa, name='approve_qa'),
    path('knowledge-spaces/qa/<str:qa_id>/reject', views.reject_qa, name='reject_qa'),
    path('knowledge-spaces/qa/search', views.qa_search, name='qa_search'),
    
    # File Upload and Processing endpoints
    path('knowledge-spaces/upload/process-files', api_views.upload_and_process_files, name='upload_and_process_files'),
    

    # Knowledge Space-specific Q&A endpoints - RESTful style (no trailing slashes)
    path('knowledge-spaces/knowledge-space/<str:knowledge_space_id>/qa/<str:qa_id>', views.qa_detail, name='qa_detail'),
    path('knowledge-spaces/knowledge-space/<str:knowledge_space_id>/qa/<str:qa_id>/vote', views.qa_vote, name='qa_vote'),
    path('knowledge-spaces/knowledge-space/<str:knowledge_space_id>', views.knowledge_space_qas, name='knowledge_space_qas'),
    path('knowledge-spaces/knowledge-space/<str:knowledge_space_id>/detail', views.knowledge_space_detail, name='knowledge_space_detail'),
    path('knowledge-spaces/knowledge-space/<str:knowledge_space_id>/responsible-users', views.knowledge_space_responsible_users, name='knowledge_space_responsible_users'),
    
    # Notification endpoints
    path('notifications', views.notifications_list, name='notifications_list'),
    path('notifications/<str:notification_id>/accept', views.accept_notification, name='accept_notification'),
    path('notifications/<str:notification_id>/ignore', views.ignore_notification, name='ignore_notification'),
    
    # Comment endpoints - RESTful style (no trailing slashes)
    path('qa/<str:qa_id>/comments', comment_views.get_qa_comments, name='get_qa_comments'),
    path('qa/<str:qa_id>/comments/<str:comment_id>', comment_views.update_comment, name='update_comment'),
    path('qa/<str:qa_id>/comments/<str:comment_id>/vote', comment_views.vote_comment, name='vote_comment'),
    
    # QA Views endpoints
    path('qa/<str:qa_id>/views', comment_views.qa_views, name='qa_views'),
]