
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, TypedDict

from knowledge_spaces_Q_A.chat_response_formatting_agent import ChatOutputFormat


class ConversationType(Enum):
    """Session types for chat conversations"""
    SINGLE = "single" # Direct conversation between user and AI assistant
    GROUP = "group" # Group chat with multiple participants and AI assistant listening


class ConversationMessage(TypedDict):
    """Structure for conversation history messages"""
    sender_id: str = None
    sender_name: str
    content: str
    timestamp: str = None


class ChatRequest(TypedDict):
    """Request structure for chat operations"""
    user: Any = None # internal user object
    external_id: Any = None # external id from slack, discord, etc.
    message: str
    session_type: ConversationType
    session_id: Optional[str]
    company_name: str
    platform_source: Optional[str]  # "slack", "teams", "web", etc.
    channel_id: Optional[str]
    history: Optional[List[ConversationMessage]]  # Optional history for context
    output_format: Optional[ChatOutputFormat] = ChatOutputFormat.HTML
    needs_reply: bool = False # True when an answer is needed, like for direct messages


class ChatResponse(TypedDict):
    """Response structure for chat operations"""
    session_id: str
    message_id: str
    response: str
    references: List[str]
    context_used: Dict[str, Any]
    tokens_used: int
    agent_decisions: List[str]
    no_action_needed: bool = False  # Flag to indicate if no action is needed (e.g., noise message)

@dataclass
class ChatUserContext:
    """User context for chat operations"""
    user_email: str = None
    company: Any = None  # Use Any to avoid Pydantic schema generation issues
    user: Any = None     # Use Any to avoid Pydantic schema generation issues
    platform_source: Optional[str] = None  # e.g., "slack", "discord", "web"
    history: List[ConversationMessage] = field(default_factory=list)
    collections_to_search: List[str] = field(default_factory=list) # company id/user id/private collection id
    sources_to_search: List[str] = field(default_factory=list) # sources of knowledge to use for search in the vector database
    knowledge_space_name: str = None # name of the knowledge space to use for search in the vector database
    session_type: ConversationType = ConversationType.SINGLE


