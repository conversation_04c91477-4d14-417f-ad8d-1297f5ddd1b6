"""
Synchronous wrapper for notification service.
This allows Django views to call notification functions synchronously while the service handles async operations internally.
"""

import logging
import asyncio
import threading
from typing import List, Dict, Any, Optional
from messaging.notification_service import NotificationService

logger = logging.getLogger(__name__)

class SyncNotificationService:
    """Synchronous wrapper for notification service"""
    
    def __init__(self):
        self.notification_service = NotificationService()
        self._lock = threading.Lock()
    
    def send_private_message_to_user(self, user_email: str, message_content: str, platforms: List[str] = None) -> Dict[str, Any]:
        """
        Synchronous wrapper for sending private messages.
        Handles async operations internally.
        """
        try:
            # Validate user_email
            if not user_email or not user_email.strip():
                logger.error(f"❌ Invalid user_email")
                return {"ok": False, "error": "Invalid user_email"}
            
            # Log the attempt
            logger.info(f"🔄 Attempting to send sync notification to user_email: '{user_email}'")
            
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Run the async operation
                result = loop.run_until_complete(
                    self.notification_service.send_private_message_to_user(
                        user_email=user_email,
                        message_content=message_content,
                        platforms=platforms or ["slack"]
                    )
                )
                logger.info(f"✅ Sync notification sent to {user_email}")
                return result
                
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"❌ Failed to send sync notification to {user_email}: {e}")
            return {"ok": False, "error": str(e)}
    
    def send_multiple_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Send multiple messages synchronously.
        """
        results = []
        
        for msg in messages:
            try:
                result = self.send_private_message_to_user(
                    user_email=msg['user_email'],
                    message_content=msg['content'],
                    platforms=msg.get('platforms', ['slack'])
                )
                results.append(result)
            except Exception as e:
                logger.error(f"❌ Failed to send message to {msg.get('user_email', 'unknown')}: {e}")
                results.append({"ok": False, "error": str(e)})
        
        return results
    
    def authenticate_and_send(self, credentials: Dict[str, Any], user_email: str, message_content: str) -> Dict[str, Any]:
        """
        Authenticate and send message synchronously.
        """
        try:
            # Create a new event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Authenticate first
                authenticated = loop.run_until_complete(
                    self.notification_service.authenticate_all_platforms(credentials)
                )
                
                if authenticated:
                    # Then send the message
                    result = loop.run_until_complete(
                        self.notification_service.send_private_message_to_user(
                            user_email=user_email,
                            message_content=message_content,
                            platforms=["slack"]
                        )
                    )
                    logger.info(f"✅ Authenticated notification sent to {user_email}")
                    return result
                else:
                    logger.warning("⚠️ Authentication failed, cannot send message")
                    return {"ok": False, "error": "Authentication failed"}
                    
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"❌ Authentication and send failed: {e}")
            return {"ok": False, "error": str(e)}

# Global instance
sync_notification_service = SyncNotificationService()

def send_sync_notification(user_email: str, message_content: str, platforms: List[str] = None) -> Dict[str, Any]:
    """Send a notification synchronously"""
    return sync_notification_service.send_private_message_to_user(user_email, message_content, platforms)

def send_multiple_sync_notifications(messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Send multiple notifications synchronously"""
    return sync_notification_service.send_multiple_messages(messages)

def send_authenticated_sync_notification(credentials: Dict[str, Any], user_email: str, message_content: str) -> Dict[str, Any]:
    """Send an authenticated notification synchronously"""
    return sync_notification_service.authenticate_and_send(credentials, user_email, message_content) 