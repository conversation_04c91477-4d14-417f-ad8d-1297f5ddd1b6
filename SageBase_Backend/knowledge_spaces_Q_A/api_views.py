"""
API views for file upload and processing from frontend.
This module handles file uploads from the frontend and processes them using the existing document processing logic.
"""

import logging
import hashlib
import tempfile
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings

from rest_framework.decorators import api_view, parser_classes
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.response import Response
from rest_framework import status

# Import authentication utilities
from integrations.auth_utils import require_authentication

from agents import Agent, Runner, function_tool, ModelSettings
from pydantic import BaseModel, Field

# Import the existing document processing logic
from konowledge_detection_agents.sagebase_q_a.q_a_detection import (
    DocumentParser, 
    SimpleTextProcessor,
    StandaloneSageBaseDocumentProcessor
)

logger = logging.getLogger(__name__)

class StructuredAnswerQuestion(BaseModel):
    """Structured Q&A output from the agent for SageBase QA model"""
    question_title: str = Field(description="The title/summary of the question")
    question_content: str = Field(description="The detailed question content")
    question_tags: List[str] = Field(description="Tags related to the question (e.g., project_name, device_name, etc.)")
    answer_content: str = Field(description="The main answer content")

class QADetectionResult(BaseModel):
    """Result containing a list of detected Q&As from the document"""
    qa_list: List[StructuredAnswerQuestion] = Field(description="List of Q&A pairs detected in the document")

def process_uploaded_file(uploaded_file: UploadedFile, filename: str, knowledge_space_id, user=None) -> Dict[str, Any]:
    """
    Process an uploaded file using the existing StandaloneSageBaseDocumentProcessor
    
    Args:
        uploaded_file: Django UploadedFile object
        filename: Original filename
        knowledge_space_name: Name of the knowledge space for categorization
        user: Authenticated user object (optional)
        
    Returns:
        Dict with processing results
    """
    try:
        logger.info(f"🔄 Processing uploaded file: {filename}")
        
        # Read file content
        file_content = uploaded_file.read()
        
        logger.info(f"📄 File '{filename}' - Size: {len(file_content)} bytes")
        
        if not file_content:
            logger.warning(f"❌ Empty file content for '{filename}'")
            return {
                "success": False,
                "error": "Empty file content"
            }
        
        # Try to use original filename, create temp file only if it exists
        original_file_path = Path(filename)
        temp_file_path = None
        file_created_by_us = False
        
        if original_file_path.exists():
            # Original file exists, create temp file
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = Path(temp_file.name)
        else:
            # Use original filename
            temp_file_path = original_file_path
            with open(temp_file_path, 'wb') as f:
                f.write(file_content)
            file_created_by_us = True
        
        try:
            from knowledge_spaces_Q_A.models import Knowledge_Space
            knowledge_space_instance = Knowledge_Space.objects.get(id=knowledge_space_id)
            processor = StandaloneSageBaseDocumentProcessor()
            
            # Use the existing process_document method (synchronous)
            try:
                result = processor.process_document(temp_file_path, knowledge_space_instance, user)
                
                return {
                    "success": True,
                    "filename": filename,
                    "file_size": len(file_content),
                    "processing_result": result
                }
                
            except Exception as e:
                logger.exception(f"❌ Error in document processing: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
            
        finally:
            # Clean up file if it was created by us (temp file or file we created)
            if temp_file_path:
                try:
                    if temp_file_path != original_file_path or file_created_by_us:
                        # It's a temp file or a file we created, clean it up
                        os.unlink(temp_file_path)
                        logger.info(f"🗑️ Cleaned up file: {temp_file_path}")
                except Exception as e:
                    logger.warning(f"Could not delete file {temp_file_path}: {e}")
                
    except Exception as e:
        logger.error(f"Error processing uploaded file {filename}: {e}")
        return {
            "success": False,
            "error": str(e)
        }



@api_view(['POST'])
@parser_classes([MultiPartParser, FormParser])
@csrf_exempt
@require_authentication
def upload_and_process_files(request, user):
    """
    Upload and process files from frontend (supports single or multiple files)
    
    Expected request:
    - POST with multipart/form-data
    - 'files[]' field(s) containing uploaded files (can be single or multiple)
    - Optional 'name_of_knowledge_space' field for categorization
    
    Returns:
    - JSON response with processing results for all files
    """
    try:
        # Debug: Log all available files in request
        logger.info(f"🔍 Available files in request.FILES: {list(request.FILES.keys())}")
        
        # Check if files were uploaded
        if 'files[]' not in request.FILES:
            return Response({
                'success': False,
                'error': 'No files uploaded'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        uploaded_files = request.FILES.getlist('files[]')
        
        for i, file in enumerate(uploaded_files):
            logger.info(f"   File {i+1}: {file.name} ({file.size} bytes)")
        
        if not uploaded_files:
            return Response({
                'success': False,
                'error': 'No files uploaded'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get knowledge space name from request
        knowledge_space_name = request.POST.get('name_of_knowledge_space')
        knowledge_space_id = request.POST.get('knowledge_space_id')
        
        # Log user information for debugging
        logger.info(f"🔄 File upload request from user: {user.email} (ID: {user.id})")
        logger.info(f"🏢 User company: {user.company.name if user.company else 'No company'}")
        logger.info(f"📁 Knowledge space: {knowledge_space_name}")
        
        # Process files in parallel using ThreadPoolExecutor
        results = []
        total_files = len(uploaded_files)
        successful_files = 0
        failed_files = 0
        total_detected_qas = 0
        
        # Determine optimal number of workers (max 4 to avoid overwhelming the system)
        max_workers = min(4, len(uploaded_files))
        logger.info(f"🚀 Processing {total_files} files in parallel with {max_workers} workers")
        
        def process_file_with_context(uploaded_file):
            """Helper function to process a single file with proper error handling"""
            filename = uploaded_file.name
            
            # Validate file
            if not filename:
                return {
                    'filename': 'unknown',
                    'success': False,
                    'error': 'Invalid filename'
                }
            
            try:
                # Process the file using the existing processor
                result = process_uploaded_file(uploaded_file, filename, knowledge_space_id, user)
                result['filename'] = filename
                return result
                    
            except Exception as e:
                logger.error(f"❌ Error processing file {filename}: {e}")
                return {
                    'filename': filename,
                    'success': False,
                    'error': str(e)
                }
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all files for processing
            future_to_file = {}
            for i, uploaded_file in enumerate(uploaded_files):
                # Create a unique identifier for each file to handle duplicates
                file_id = f"{uploaded_file.name}_{i}"
                future = executor.submit(process_file_with_context, uploaded_file)
                future_to_file[future] = file_id
            
            # Collect results as they complete
            completed_count = 0
            for future in as_completed(future_to_file):
                file_id = future_to_file[future]
                completed_count += 1
                
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['success']:
                        successful_files += 1
                        # Add detected QAs count to total
                        results_this_file = result.get('processing_result', {})
                        total_detected_qas += results_this_file.get('detected_qas_count', 0)
                        logger.info(f"✅ Successfully processed: {result['filename']} ({completed_count}/{total_files})")
                    else:
                        failed_files += 1
                        logger.exception(f"❌ Failed to process: {result['filename']} - {result.get('error', 'Unknown error')} ({completed_count}/{total_files})")
                        
                except Exception as e:
                    logger.exception(f"❌ Exception processing {file_id}: {e}")
                    failed_files += 1
                    results.append({
                        'filename': file_id,
                        'success': False,
                        'error': str(e)
                    })
        
        # Return summary with user information
        return Response({
            'success': True,
            'message': f'Processed {total_files} files',
            'user_info': {
                'user_id': str(user.id),
                'user_email': user.email,
                'company_name': user.company.name if user.company else None
            },
            'knowledge_space': knowledge_space_name,
            'summary': {
                'total_files': total_files,
                'successful_files': successful_files,
                'failed_files': failed_files,
                'new_qas_detected': total_detected_qas
            },
            'results': results
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.exception(f"Error in upload_and_process_multiple_files: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

 