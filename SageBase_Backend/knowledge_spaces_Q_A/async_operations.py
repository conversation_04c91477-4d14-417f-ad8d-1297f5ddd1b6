"""
Generic async operations wrapper for the task queue.
This allows any async function to be easily enqueued from sync contexts.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from asgiref.sync import sync_to_async
from messaging.notification_service import NotificationService

logger = logging.getLogger(__name__)

class AsyncOperations:
    """Generic async operations that can be enqueued"""
    
    def __init__(self):
        self.notification_service = NotificationService()
    
    # 🔑 THE KEY: Generic async operation wrapper
    async def send_slack_message_async(self, user_email: str, message_content: str, platforms: List[str] = None):
        """Generic async function to send Slack messages"""
        try:
            # This is the actual async operation that needs the async context
            result = await self.notification_service.send_private_message_to_user(
                user_email=user_email,
                message_content=message_content,
                platforms=platforms or ["slack"]
            )
            logger.info(f"✅ Slack message sent to {user_email}: {result}")
            return result
        except Exception as e:
            logger.error(f"❌ Failed to send Slack message to {user_email}: {e}")
            raise
    
    async def send_multiple_slack_messages_async(self, messages: List[Dict[str, Any]]):
        """Send multiple Slack messages in parallel"""
        try:
            tasks = []
            for msg in messages:
                task = self.send_slack_message_async(
                    user_id=msg['user_id'],
                    message_content=msg['content'],
                    platforms=msg.get('platforms', ['slack'])
                )
                tasks.append(task)
            
            # Execute all messages in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)
            logger.info(f"✅ Sent {len(messages)} messages, {len([r for r in results if not isinstance(r, Exception)])} successful")
            return results
        except Exception as e:
            logger.error(f"❌ Failed to send multiple messages: {e}")
            raise
    
    async def authenticate_and_send_async(self, credentials: Dict[str, Any], user_id: str, message_content: str):
        """Authenticate and send message in one async operation"""
        try:
            # Authenticate first
            authenticated = await self.notification_service.authenticate_all_platforms(credentials)
            
            if authenticated:
                # Then send the message
                result = await self.send_slack_message_async(user_id, message_content)
                return result
            else:
                logger.warning("⚠️ Authentication failed, cannot send message")
                return None
        except Exception as e:
            logger.error(f"❌ Authentication and send failed: {e}")
            raise

# Global instance
async_operations = AsyncOperations()

def enqueue_slack_message(user_id: str, message_content: str, platforms: List[str] = None, priority: int = 10) -> str:
    """Enqueue a Slack message for background sending"""
    from knowledge_spaces_Q_A.async_task_queue import enqueue_async_operation
    
    task_id = enqueue_async_operation(
        async_operations.send_slack_message_async,
        user_id,
        message_content,
        platforms or ["slack"],
        priority=priority
    )
    
    logger.info(f"📝 Slack message enqueued for user {user_id}: {task_id}")
    return task_id

def enqueue_multiple_slack_messages(messages: List[Dict[str, Any]], priority: int = 10) -> str:
    """Enqueue multiple Slack messages for background sending"""
    from knowledge_spaces_Q_A.async_task_queue import enqueue_async_operation
    
    task_id = enqueue_async_operation(
        async_operations.send_multiple_slack_messages_async,
        messages,
        priority=priority
    )
    
    logger.info(f"📝 Multiple Slack messages enqueued: {task_id}")
    return task_id

def enqueue_authenticated_slack_message(credentials: Dict[str, Any], user_id: str, message_content: str, priority: int = 10) -> str:
    """Enqueue an authenticated Slack message for background sending"""
    from knowledge_spaces_Q_A.async_task_queue import enqueue_async_operation
    
    task_id = enqueue_async_operation(
        async_operations.authenticate_and_send_async,
        credentials,
        user_id,
        message_content,
        priority=priority
    )
    
    logger.info(f"📝 Authenticated Slack message enqueued for user {user_id}: {task_id}")
    return task_id 