import logging
from django.conf import settings
from django.db import transaction
from vectordb.interfaces import add_document, delete_by_metadata
from vectordb.models.document import DataSource
from .models import QA, Knowledge_Space
from integrations.models import Company
from django.contrib.auth import get_user_model
from integrations.models import User
from integrations.models import NotificationSettings
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)




def embed_q_a(qa_instance: QA):
    """
    Embed a Q&A document into the vector database.
    Supports both company-owned and user-owned knowledge spaces.
    """
    try:
        if qa_instance.knowledge_space is None:
           return False
        
        frontend_url = getattr(settings, 'FRONTEND_BASE_URL')
        content = f"Question: {qa_instance.question_content}\nAnswer: {qa_instance.answer_content}"
        collection_name = None
        
        if qa_instance.knowledge_space.is_user_owned:
            #for user owned knowledge spaces, we need to use the user id as the collection name
            collection_name = str(qa_instance.knowledge_space.user.id)
        else:
            #for company owned knowledge spaces, we need to use the company id as the collection name
            collection_name = str(qa_instance.knowledge_space.company.id)
            
        if not collection_name:
            logger.error(f"❌ Failed to embed Q&A for qa_instance with id {qa_instance.id}: collection name is None")
            return False
        
        # Determine collection name
        
        tags = qa_instance.question_tags if hasattr(qa_instance, 'question_tags') else []
        
        # Add "private" tag for user-owned knowledge spaces
        if qa_instance.knowledge_space.is_user_owned:
            if 'private' not in tags:
                tags.append('private')
                tags.append(str(qa_instance.knowledge_space.user.id))
            logger.info(f"🔒 Adding 'private' tag to Q&A in user-owned knowledge space")
            #for user owned knowledge spaces, we need to use the user id as the collection name
            collection_name = str(qa_instance.knowledge_space.user.id)
        
        add_document(
            collection_name=collection_name,
            content=content,
            source=DataSource.LOCAL,
            source_id=str(qa_instance.id),
            title=qa_instance.question_title,
            path=f"{frontend_url}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}",
            author=qa_instance.question_author_name,
            content_type="qa",
            url=f"{frontend_url}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}",
            custom_fields={
                "question_tokens_count": getattr(qa_instance, "question_tokens_count", 0),
                "answer_tokens_count": getattr(qa_instance, "answer_tokens_count", 0),
            },
            tags=tags
        )
        
        logger.info(f"✅ Successfully embedded Q&A: {qa_instance.question_title} (ID: {qa_instance.id})")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to embed Q&A for qa_instance with id {qa_instance.id}: {e}", exc_info=True)
        return False


def delete_embedding_q_a(qa_instance):
    """
    Delete a Q&A document from the vector database.
    """
    try:
        frontend_url = getattr(settings, 'FRONTEND_BASE_URL')
        qa_url = f"{frontend_url}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}"
        collection_name = str(qa_instance.knowledge_space.company.id) if qa_instance.knowledge_space.is_company_owned else str(qa_instance.knowledge_space.user.id)
        result = delete_by_metadata(collection_name, {"url": qa_url})
        
        if result.get("success"):
            deleted_count = result.get("deleted_count", 0)
            if deleted_count > 0:
                logger.info(f"✅ Successfully deleted {deleted_count} Q&A embedding(s): {qa_instance.question_title} (ID: {qa_instance.id})")
                return True
            else:
                logger.info(f"⚠️  Q&A embedding not found or already deleted: {qa_instance.question_title} (ID: {qa_instance.id})")
                return False
        else:
            logger.error(f"❌ Failed to delete Q&A embedding {qa_instance.id}: {result.get('error', 'Unknown error')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Failed to delete Q&A embedding for qa_instance with id {qa_instance.id}: {e}", exc_info=True)
        return False


def update_embedding_q_a(qa_instance):
    """
    Update a Q&A document in the vector database.
    """
    try:
        delete_success = delete_embedding_q_a(qa_instance)
        embed_success = embed_q_a(qa_instance)
        
        if embed_success:
            logger.info(f"✅ Successfully updated Q&A embedding: {qa_instance.question_title} (ID: {qa_instance.id})")
            return True
        else:
            logger.error(f"❌ Failed to update Q&A embedding: {qa_instance.question_title} (ID: {qa_instance.id})")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to update Q&A embedding for qa_instance with id {qa_instance.id}: {e}", exc_info=True)
        return False


def create_qa_instance(company: Company, question_content: str, answer_content: str,
                       question_title: str, knowledge_space_instance, user_id,
                       auto_approve: bool = False, created_by: str | None = None):
    """
    Create a real QA instance for embedding into the vector database.
    Supports both company-owned and user-owned knowledge spaces.
    
    Args:
        company: Company object (for company-owned)
        question_content: The question content
        answer_content: The answer content
        question_title: Optional title (will use first 50 chars of question if not provided)
        project_id: Optional project ID (will create default project if not provided)
        
    Returns:
        QA: A real QA model instance with all required attributes
        
    Example:
        >>> qa_instance = create_qa_instance(
        ...     question_content="What is SageBase?",
        ...     answer_content="SageBase is a knowledge management platform.",
        ...     question_title="SageBase Overview"
        ... )
        >>> success = embed_q_a(qa_instance)
    """
    from datetime import datetime
    from django.db import transaction
    
    # Import the real models
    from knowledge_spaces_Q_A.models import QA, Knowledge_Space
    from utils.token_utils import get_token_count
    
    with transaction.atomic():
   
        try:
            name="sagebase_bot"
            id="sagebase_bot"
            if user_id:
                user_instance = User.objects.get(id=user_id)
                name = user_instance.username if user_instance.username else user_instance.email
                id = user_instance.id


            question_tokens_count = get_token_count(question_content)
            answer_tokens_count = get_token_count(answer_content) if answer_content else 0
            qa_instance = QA.objects.create(
                question_content=question_content,
                answer_content=answer_content,
                question_title=question_title or (question_content[:50] + "..." if len(question_content) > 50 else question_content),
                question_tokens_count=question_tokens_count,
                answer_tokens_count=answer_tokens_count,
                knowledge_space=knowledge_space_instance,
                question_author_name=name,
                question_author_id=id,
                created_by= created_by if created_by else name,
            )
            
            # we still want the user to approve the QA if it is inserted by the AI agents.
            # sor for now auto_approve is False
            if knowledge_space_instance.is_user_owned and auto_approve:
                from django.utils import timezone
                qa_instance.approval_status = 'approved'
                qa_instance.approved_at = timezone.now()
                qa_instance.save(update_fields=['approval_status', 'approved_at'])
                logger.info(f"✅ Auto-approved QA {qa_instance.id} in user-owned knowledge space {knowledge_space_instance.name}")
                
            else:
                #for company-owned knowledge spaces, send a notification to the admin
                # Since this is a sync function, we'll call the async function in a thread
                import asyncio
                import threading
                
                def run_async_notification():
                    try:
                        asyncio.run(send_qa_notification_to_admins_and_assignees(qa_instance))
                    except Exception as e:
                        logger.error(f"❌ Error in async notification: {e}")
                
                # Run the async notification in a separate thread
                notification_thread = threading.Thread(target=run_async_notification)
                notification_thread.daemon = True
                notification_thread.start()
            
            return qa_instance
        except Exception as e:
            logger.error(f"❌ Failed to create QA instance: {e}", exc_info=True)
            return None


 
def get_emails_to_notify_for_project(user_company: Company, project_name: str):
    """Get emails to notify for a project
        The list contains admins and assignees of the project.
    """
    from integrations.models import User, SlackUserProfile
    
    emails_to_notify = set()
    admin_users = User.objects.filter(company=user_company, role='ADMIN')
    
    for admin_user in admin_users:
        emails_to_notify.add(admin_user.email)
    
    from knowledge_map.models import Project as knowledge_map_project
    # find if any knowledge map is linked to the knowledge space
    linked_knowledge_map = knowledge_map_project.objects.filter(name=project_name.lower()).first()
    if linked_knowledge_map:
        if linked_knowledge_map.doc_responsible:
            emails_to_notify.add(linked_knowledge_map.doc_responsible.email)
        if linked_knowledge_map.secondary_responsible:
            emails_to_notify.add(linked_knowledge_map.secondary_responsible.email)
            
    
    if not emails_to_notify:
        logger.warning("⚠️ No admin users found for the company {user_company.name} or no assignees found for the knowledge map--> skipping notification")
        return []
    
    return list(emails_to_notify)      
      
      
    
def check_notification_frequency(company, notification_type: str) -> bool:
    """
    Check if enough time has passed since the last notification for this company.
    
    Args:
        company: Company instance
        notification_type: Type of notification (e.g., 'qa_approval', 'repo_change')
    
    Returns:
        True if notification should be sent, False if it should be blocked due to frequency
    """
    try:
        
        
        # Get or create notification settings for the company
        settings,created = NotificationSettings.objects.get_or_create(company=company,defaults={
                'frequency_hours': 1,  # Default to no limit
                'enabled': True
            })

        
        # Check if notifications are enabled
        if not settings.enabled:
            logger.info(f"⚠️ Notifications disabled for company {company.name}")
            return False
        
        # If frequency is 0, no limit
        if settings.frequency_hours == 0:
            return True
        
        # Check if we have a last notification time
        if not settings.last_notification_time:
            # No previous notification, allow it
            return True
        
        # Calculate time since last notification
        time_since_last = timezone.now() - settings.last_notification_time
        required_interval = timedelta(hours=settings.frequency_hours)
        
        if time_since_last < required_interval:
            remaining_time = required_interval - time_since_last
            logger.info(f"⏰ Notification blocked for {company.name}. "
                        f"Last sent {time_since_last.total_seconds()/3600:.1f}h ago, "
                        f"need to wait {remaining_time.total_seconds()/3600:.1f}h more")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking notification frequency for {company.name}: {e}")
        # In case of error, allow the notification to be sent
        return True
        
          
async def send_qa_notification_to_admins_and_assignees(qa_instance: QA):
    """
    Send a notification to all admins and assignees of of that knowledge space.
    """
    from messaging.notification_service import NotificationService
    from asgiref.sync import sync_to_async
    import os
    import asyncio
   
    
    try:
        
        # Get the company first using sync_to_async
        company = await sync_to_async(lambda: qa_instance.knowledge_space.company)()
        
        # Lets check if the notification is blocked by frequency limit
        if company:
            should_send = await sync_to_async(check_notification_frequency)(company, "qa_approval")
            if not should_send:
                logger.info(f"⏰ Notification blocked by frequency limit for {company.name}")
                return
        
        # Get admin users and their profiles using sync_to_async
        # Since get_emails_to_notify_for_project has ORM calls, we need to wrap it properly
        
        def _get_emails_sync():
            #TODO : maybe allow more roles to accept theh new QAs.
            from integrations.models import User, SlackUserProfile
            
            emails_to_notify = set()
            admin_users = User.objects.filter(company=company, role='ADMIN')
            
            for admin_user in admin_users:
                emails_to_notify.add(admin_user.email)
                
            if not emails_to_notify:
                logger.warning("⚠️ No admin users found for the company or no assignees found for the knowledge map--> skipping notification")
                return []
            
            return list(emails_to_notify)
        
        emails_to_notify = await sync_to_async(_get_emails_sync)()
        
        if not emails_to_notify:
            logger.warning("⚠️ No admin users found for the company or no assignees found for the knowledge map--> skipping notification")
            return
        
        # Get credentials from company's Slack integration
        credentials = {}
        def _get_slack_token():
            from integrations.slack.services import get_company_slack_bot_token
            return get_company_slack_bot_token(company)
        
        slack_token = await sync_to_async(_get_slack_token)()
        if slack_token:
            credentials["slack"] = {"bot_token": slack_token}
        
        if not credentials:
            logger.warning("⚠️ No messaging credentials configured")
            return
        
        # Use async notification service directly since we're in async context
        from messaging.notification_service import NotificationService
        
        # Create notification service instance
        notification_service = NotificationService()
        
        # Send notifications to all admin users
        try:
            # Get frontend URL from environment
            from django.conf import settings
            frontend_url = getattr(settings, 'FRONTEND_BASE_URL')
            
            # Authenticate and send the notification asynchronously
            authenticated = await notification_service.authenticate_all_platforms(credentials)
            
            if authenticated:
                result = await notification_service.send_private_message_to_multiple_users(
                    users_emails=emails_to_notify,
                    message_content=f"New QA '{qa_instance.question_title}' has been created in knowledge space '{qa_instance.knowledge_space.name}' and requires approval.\n\nTo see and approve it, please visit {frontend_url}/qa-history",
                    platforms=["slack"],
                    company=company,
                    notification_type="qa_approval"
                )

                if result.get('status') == 'completed':
                    logger.info(f"✅ Notification sent to all these users {emails_to_notify} via Slack")
                    
                    #update the last notification time
                    settings = await sync_to_async(NotificationSettings.objects.get)(company=company)
                    settings.last_notification_time = timezone.now()
                    settings.enabled = True
                    await sync_to_async(settings.save)()
                else:
                    logger.warning(f"⚠️ Failed to send notification to {emails_to_notify}: {result.get('error')}")
            else:
                logger.warning(f"⚠️ No active Slack profile found for admin {emails_to_notify}")
                    
        except Exception as e:
            logger.error(f"❌ Error sending notification to admin {emails_to_notify}: {e}")
            # Continue with other admins even if one fails
                
    except Exception as e:
        logger.exception(f"❌ Error in send_qa_notification_to_admins_and_assignees: {e}")



def get_datasources_to_use_for_search(company_id: str):
    """find all connected platforms ffor a company to search for knowledge"""
    company = Company.objects.get(id=company_id)
    from integrations.models import CompanyIntegration
    datasources_to_use = [DataSource.LOCAL]
    integrations = CompanyIntegration.objects.filter(company=company, is_active=True)
    for integration in integrations:
        if integration.tool.slug == "confluence":
            datasources_to_use.append(DataSource.CONFLUENCE)
        elif integration.tool.slug == "google_drive":
            datasources_to_use.append(DataSource.GOOGLE_DRIVE)
        elif integration.tool.slug == "jira":
            datasources_to_use.append(DataSource.JIRA)
    return  datasources_to_use

 
def send_admin_notification_for_new_qa(qa_instance, knowledge_space, user):
    """
    Send notification to company admins when a new QA is created in a company-owned knowledge space.
    This function can be called from both frontend views and agent tools.
    
    Args:
        qa_instance: The created QA instance
        knowledge_space: The knowledge space where QA was created
        user: The user who created the QA (can be None for agent-created QAs)
    """
    try:
        from messaging.notification_service import NotificationService
        from integrations.models import User, SlackUserProfile
        from integrations.slack.services import get_company_slack_bot_token
        from knowledge_spaces_Q_A.sync_notification_service import send_authenticated_sync_notification
        from django.conf import settings
        
        # Only send notifications for company-owned knowledge spaces
        if not knowledge_space.is_company_owned:
            return
            
        # Get admin users for the company
        admin_users = User.objects.filter(company=knowledge_space.company, role='ADMIN')
        
        if not admin_users.exists():
            logger.warning(f"⚠️ No admin users found for company {knowledge_space.company.name}")
            return
            
        notification_service = NotificationService()
        
        # Get credentials from company's Slack integration
        credentials = {}
        slack_token = get_company_slack_bot_token(knowledge_space.company)
        if slack_token:
            credentials["slack"] = {"bot_token": slack_token}
        
        if not credentials:
            logger.warning(f"⚠️ No messaging credentials configured for company {knowledge_space.company.name}")
            return
            
        # Send notifications to all admin users
        for admin_user in admin_users:
            try:
                # Get Slack profile for this admin
                slack_profile = SlackUserProfile.objects.filter(
                    user=admin_user,
                    is_active=True
                ).first()
                
                if slack_profile and slack_profile.slack_user_id:
                    # Log the user details for debugging
                    logger.info(f"🔄 Sending notification to admin {admin_user.email} with Slack user_id: '{slack_profile.slack_user_id}'")
                    
                    # Create the notification message
                    creator_info = "an agent" if not user else f"user {user.first_name or user.email}"
                    message_content = f"New QA '{qa_instance.question_title}' has been created in knowledge space '{knowledge_space.name}' by {creator_info} and requires approval.\n\nTo see and approve it, please visit {settings.FRONTEND_BASE_URL}/qa-history"
                    
                    # Send the notification synchronously
                    result = send_authenticated_sync_notification(
                        credentials=credentials,
                        user_email=admin_user.email,
                        message_content=message_content
                    )
                    
                    if result.get('ok'):
                        logger.info(f"✅ Notification sent to admin {admin_user.email} via Slack")
                    else:
                        logger.warning(f"⚠️ Failed to send notification to {admin_user.email}: {result.get('error')}")
                else:
                    logger.warning(f"⚠️ No active Slack profile found for admin {admin_user.email}")
                    
            except Exception as e:
                logger.error(f"❌ Error sending notification to admin {admin_user.email}: {e}")
                # Continue with other admins even if one fails
                
        # Send Discord notification to the general channel
        try:
            logger.info(f"🔍 Attempting to send Discord notification for QA: {qa_instance.question_title}")
            from integrations.discord.services import get_company_discord_credentials
            from messaging.discord.discord_service import DiscordService
            import asyncio
            
            # Get Discord credentials
            discord_creds = get_company_discord_credentials(knowledge_space.company)
            logger.info(f"🔍 Discord credentials retrieved: {bool(discord_creds)}")
            
            if discord_creds and discord_creds.get("bot_token") and discord_creds.get("guild_id"):
                logger.info(f"🔍 Discord credentials valid - bot_token: {bool(discord_creds.get('bot_token'))}, guild_id: {discord_creds.get('guild_id')}")
                # Create Discord service and authenticate
                discord_service = DiscordService()
                
                # Use asyncio.run to handle async operations in sync context
                def send_discord_notification():
                    async def async_discord_ops():
                        logger.info(f"🔍 Authenticating Discord service...")
                        auth_result = await discord_service.authenticate(discord_creds)
                        logger.info(f"🔍 Discord authentication result: {auth_result}")
                        
                        if auth_result:
                            guild_id = discord_creds["guild_id"]
                            logger.info(f"🔍 Getting channels for guild: {guild_id}")
                            
                            # Try to find the general channel first
                            channels = await discord_service.get_channels(guild_id)
                            logger.info(f"🔍 Found {len(channels)} channels")
                            notification_channel = None
                            
                            # Look for common general channel names
                            general_channel_names = ["general", "chat", "main", "lobby", "announcements"]
                            
                            for channel in channels:
                                channel_name = channel.get("channel_name", "").lower()
                                logger.info(f"🔍 Checking channel: #{channel_name}")
                                if channel_name in general_channel_names:
                                    notification_channel = channel
                                    logger.info(f"✅ Found general channel: #{channel.get('channel_name')}")
                                    break
                            
                            # If no general channel found, use the first text channel
                            if not notification_channel:
                                for channel in channels:
                                    if channel.get("type") == "text":
                                        notification_channel = channel
                                        logger.info(f"✅ Using first available text channel: #{channel.get('channel_name')}")
                                        break
                            
                            if notification_channel:
                                # Send notification message to Discord
                                creator_info = "an agent" if not user else f"user {user.first_name or user.email}"
                                discord_message = f"🔔 **New QA Created - Requires Approval**\n\n" \
                                               f"**Question:** {qa_instance.question_title}\n" \
                                               f"**Knowledge Space:** {knowledge_space.name}\n" \
                                               f"**Created by:** {creator_info}\n\n" \
                                               f"**To approve/reject:** {settings.FRONTEND_BASE_URL}/qa-history"
                                
                                logger.info(f"🔍 Sending Discord message to channel: {notification_channel['id']}")
                                message_result = await discord_service.send_message(
                                    notification_channel["id"], 
                                    discord_message
                                )
                                logger.info(f"🔍 Discord message result: {message_result}")
                                
                                if message_result.get("status") == "success":
                                    logger.info(f"✅ Discord notification sent to channel #{notification_channel.get('channel_name')}")
                                else:
                                    logger.warning(f"⚠️ Failed to send Discord notification: {message_result.get('error')}")
                            else:
                                logger.warning(f"⚠️ No suitable Discord channel found in guild {guild_id}")
                                
                        else:
                            logger.warning(f"⚠️ Failed to authenticate Discord service for company {knowledge_space.company.name}")
                    
                    return asyncio.run(async_discord_ops())
                
                # Execute the Discord notification
                logger.info(f"🔍 Executing Discord notification...")
                send_discord_notification()
                logger.info(f"🔍 Discord notification execution completed")
            else:
                logger.warning(f"⚠️ Discord credentials missing or invalid for company {knowledge_space.company.name}")
                if discord_creds:
                    logger.info(f"🔍 Discord creds details - bot_token: {bool(discord_creds.get('bot_token'))}, guild_id: {discord_creds.get('guild_id')}")
                    
        except Exception as e:
            logger.error(f"❌ Error sending Discord notification: {e}", exc_info=True)
            # Don't fail the entire notification process if Discord fails
                
    except Exception as e:
        logger.error(f"❌ Error sending admin notification: {e}")
        # Don't fail the QA creation if notification fails

 