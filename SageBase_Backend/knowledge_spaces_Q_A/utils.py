import logging
from django.conf import settings
from django.db import transaction
from vectordb.interfaces import add_document, delete_by_metadata
from vectordb.models.document import DataSource
from .models import QA, Knowledge_Space
from integrations.models import Company
from django.contrib.auth import get_user_model
from integrations.models import User
from integrations.models import NotificationSettings
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)




def embed_q_a(qa_instance: QA):
    """
    Embed a Q&A document into the vector database.
    Supports both company-owned and user-owned knowledge spaces.
    """
    try:
        if qa_instance.knowledge_space is None:
           return False
        
        frontend_url = getattr(settings, 'FRONTEND_BASE_URL')
        content = f"Question: {qa_instance.question_content}\nAnswer: {qa_instance.answer_content}"
        
        # Determine collection name
        collection_name =qa_instance.knowledge_space.name
        
        tags = qa_instance.question_tags if hasattr(qa_instance, 'question_tags') else []
        
        # Add "private" tag for user-owned knowledge spaces
        if qa_instance.knowledge_space.is_user_owned:
            if 'private' not in tags:
                tags.append('private')
                tags.append(str(qa_instance.knowledge_space.user.id))
            logger.info(f"🔒 Adding 'private' tag to Q&A in user-owned knowledge space")
            #for user owned knowledge spaces, we need to use the user id as the collection name
            collection_name = str(qa_instance.knowledge_space.user.id)
        
        add_document(
            collection_name=collection_name,
            content=content,
            source=DataSource.LOCAL,
            source_id=str(qa_instance.id),
            title=qa_instance.question_title,
            path=f"{frontend_url}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}",
            author=qa_instance.question_author_name,
            content_type="qa",
            url=f"{frontend_url}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}",
            custom_fields={
                "question_tokens_count": getattr(qa_instance, "question_tokens_count", 0),
                "answer_tokens_count": getattr(qa_instance, "answer_tokens_count", 0),
            },
            tags=tags
        )
        
        logger.info(f"✅ Successfully embedded Q&A: {qa_instance.question_title} (ID: {qa_instance.id})")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to embed Q&A for qa_instance with id {qa_instance.id}: {e}", exc_info=True)
        return False


def delete_embedding_q_a(qa_instance):
    """
    Delete a Q&A document from the vector database.
    """
    try:
        frontend_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
        qa_url = f"{frontend_url}/qa/{qa_instance.knowledge_space.id}/{qa_instance.id}"
        
        result = delete_by_metadata(qa_instance.knowledge_space.company.id, {"url": qa_url})
        
        if result.get("success"):
            deleted_count = result.get("deleted_count", 0)
            if deleted_count > 0:
                logger.info(f"✅ Successfully deleted {deleted_count} Q&A embedding(s): {qa_instance.question_title} (ID: {qa_instance.id})")
                return True
            else:
                logger.warning(f"⚠️  Q&A embedding not found or already deleted: {qa_instance.question_title} (ID: {qa_instance.id})")
                return False
        else:
            logger.error(f"❌ Failed to delete Q&A embedding {qa_instance.id}: {result.get('error', 'Unknown error')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Failed to delete Q&A embedding for qa_instance with id {qa_instance.id}: {e}", exc_info=True)
        return False


def update_embedding_q_a(qa_instance):
    """
    Update a Q&A document in the vector database.
    """
    try:
        delete_success = delete_embedding_q_a(qa_instance)
        embed_success = embed_q_a(qa_instance)
        
        if embed_success:
            logger.info(f"✅ Successfully updated Q&A embedding: {qa_instance.question_title} (ID: {qa_instance.id})")
            return True
        else:
            logger.error(f"❌ Failed to update Q&A embedding: {qa_instance.question_title} (ID: {qa_instance.id})")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to update Q&A embedding for qa_instance with id {qa_instance.id}: {e}", exc_info=True)
        return False


def create_qa_instance(company: Company, question_content: str, answer_content: str,
                       question_title: str, knowledge_space_name: str, user_id):
    """
    Create a real QA instance for embedding into the vector database.
    Supports both company-owned and user-owned knowledge spaces.
    
    Args:
        company: Company object (for company-owned)
        question_content: The question content
        answer_content: The answer content
        question_title: Optional title (will use first 50 chars of question if not provided)
        project_id: Optional project ID (will create default project if not provided)
        
    Returns:
        QA: A real QA model instance with all required attributes
        
    Example:
        >>> qa_instance = create_qa_instance(
        ...     question_content="What is SageBase?",
        ...     answer_content="SageBase is a knowledge management platform.",
        ...     question_title="SageBase Overview"
        ... )
        >>> success = embed_q_a(qa_instance)
    """
    from datetime import datetime
    from django.db import transaction
    
    # Import the real models
    from knowledge_spaces_Q_A.models import QA, Knowledge_Space
    from utils.token_utils import get_token_count
    
    with transaction.atomic():
   
        try:
            user_instance = User.objects.get(id=user_id)
            knowledge_space = Knowledge_Space.objects.get(name=knowledge_space_name)
        except Knowledge_Space.DoesNotExist:
            logger.debug(f"❌ Knowledge space '{knowledge_space_name}' does not exist.-->using the first one")  
            knowledge_space = Knowledge_Space.objects.get_or_create(user=user_instance,name=knowledge_space_name,color="#3B82F6",initial=user_instance.email[0].upper() if user_instance.email else "U",company=company)
         
        try:
            question_tokens_count = get_token_count(question_content)
            answer_tokens_count = get_token_count(answer_content) if answer_content else 0
            qa_instance = QA.objects.create(
                question_content=question_content,
                answer_content=answer_content,
                question_title=question_title or (question_content[:50] + "..." if len(question_content) > 50 else question_content),
                question_tokens_count=question_tokens_count,
                answer_tokens_count=answer_tokens_count,
                question_author_id="sagebase_agent",
                knowledge_space=knowledge_space,
                question_author_name=user_instance.email if user_instance else "SageBase Agent",
            )
            
            # Auto-approve QAs created in user-owned (private) knowledge spaces
            if knowledge_space.is_user_owned:
                from django.utils import timezone
                qa_instance.approval_status = 'approved'
                qa_instance.approved_at = timezone.now()
                qa_instance.save(update_fields=['approval_status', 'approved_at'])
                logger.info(f"✅ Auto-approved QA {qa_instance.id} in user-owned knowledge space {knowledge_space.name}")
                
            else:
                #for company-owned knowledge spaces, send a notification to the admin
                # Since this is a sync function, we'll call the async function in a thread
                import asyncio
                import threading
                
                def run_async_notification():
                    try:
                        asyncio.run(send_qa_notification_to_admins_and_assignees(qa_instance))
                    except Exception as e:
                        logger.error(f"❌ Error in async notification: {e}")
                
                # Run the async notification in a separate thread
                notification_thread = threading.Thread(target=run_async_notification)
                notification_thread.daemon = True
                notification_thread.start()
            
            return qa_instance
        except Exception as e:
            logger.error(f"❌ Failed to create QA instance: {e}", exc_info=True)
            return None


 
def get_emails_to_notify_for_project(user_company: Company, project_name: str):
    """Get emails to notify for a project
        The list contains admins and assignees of the project.
    """
    from integrations.models import User, SlackUserProfile
    
    emails_to_notify = set()
    admin_users = User.objects.filter(company=user_company, role='ADMIN')
    
    for admin_user in admin_users:
        emails_to_notify.add(admin_user.email)
    
    from knowledge_map.models import Project as knowledge_map_project
    # find if any knowledge map is linked to the knowledge space
    linked_knowledge_map = knowledge_map_project.objects.filter(name=project_name.lower()).first()
    if linked_knowledge_map:
        if linked_knowledge_map.doc_responsible:
            emails_to_notify.add(linked_knowledge_map.doc_responsible.email)
        if linked_knowledge_map.secondary_responsible:
            emails_to_notify.add(linked_knowledge_map.secondary_responsible.email)
            
    
    if not emails_to_notify:
        logger.warning("⚠️ No admin users found for the company {user_company.name} or no assignees found for the knowledge map--> skipping notification")
        return []
    
    return list(emails_to_notify)      
      
      
    
def check_notification_frequency(company, notification_type: str) -> bool:
    """
    Check if enough time has passed since the last notification for this company.
    
    Args:
        company: Company instance
        notification_type: Type of notification (e.g., 'qa_approval', 'repo_change')
    
    Returns:
        True if notification should be sent, False if it should be blocked due to frequency
    """
    try:
        
        
        # Get or create notification settings for the company
        settings,created = NotificationSettings.objects.get_or_create(company=company,defaults={
                'frequency_hours': 1,  # Default to no limit
                'enabled': True
            })

        
        # Check if notifications are enabled
        if not settings.enabled:
            logger.info(f"⚠️ Notifications disabled for company {company.name}")
            return False
        
        # If frequency is 0, no limit
        if settings.frequency_hours == 0:
            return True
        
        # Check if we have a last notification time
        if not settings.last_notification_time:
            # No previous notification, allow it
            return True
        
        # Calculate time since last notification
        time_since_last = timezone.now() - settings.last_notification_time
        required_interval = timedelta(hours=settings.frequency_hours)
        
        if time_since_last < required_interval:
            remaining_time = required_interval - time_since_last
            logger.info(f"⏰ Notification blocked for {company.name}. "
                        f"Last sent {time_since_last.total_seconds()/3600:.1f}h ago, "
                        f"need to wait {remaining_time.total_seconds()/3600:.1f}h more")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking notification frequency for {company.name}: {e}")
        # In case of error, allow the notification to be sent
        return True
        
          
async def send_qa_notification_to_admins_and_assignees(qa_instance: QA):
    """
    Send a notification to all admins and assignees of of that knowledge space.
    """
    from messaging.notification_service import NotificationService
    from asgiref.sync import sync_to_async
    import os
    import asyncio
   
    
    try:
        
        # Get the company first using sync_to_async
        company = await sync_to_async(lambda: qa_instance.knowledge_space.company)()
        
        # Lets check if the notification is blocked by frequency limit
        if company:
            should_send = await sync_to_async(check_notification_frequency)(company, "qa_approval")
            if not should_send:
                logger.info(f"⏰ Notification blocked by frequency limit for {company.name}")
                return
        
        # Get admin users and their profiles using sync_to_async
        # Since get_emails_to_notify_for_project has ORM calls, we need to wrap it properly
        knowledge_space_name = qa_instance.knowledge_space.name
        
        def _get_emails_sync():
            from integrations.models import User, SlackUserProfile
            
            emails_to_notify = set()
            admin_users = User.objects.filter(company=company, role='ADMIN')
            
            for admin_user in admin_users:
                emails_to_notify.add(admin_user.email)
            
            from knowledge_map.models import Project as knowledge_map_project
            # find if any knowledge map is linked to the knowledge space
            linked_knowledge_map = knowledge_map_project.objects.filter(name=knowledge_space_name.lower()).first()
            if linked_knowledge_map:
                if linked_knowledge_map.doc_responsible:
                    emails_to_notify.add(linked_knowledge_map.doc_responsible.email)
                if linked_knowledge_map.secondary_responsible:
                    emails_to_notify.add(linked_knowledge_map.secondary_responsible.email)
                    
            if not emails_to_notify:
                logger.warning("⚠️ No admin users found for the company or no assignees found for the knowledge map--> skipping notification")
                return []
            
            return list(emails_to_notify)
        
        emails_to_notify = await sync_to_async(_get_emails_sync)()
        
        if not emails_to_notify:
            logger.warning("⚠️ No admin users found for the company or no assignees found for the knowledge map--> skipping notification")
            return
        
        # Get credentials from environment variables
        credentials = {}
        slack_token = os.getenv('SLACK_BOT_TOKEN')
        if slack_token:
            credentials["slack"] = {"bot_token": slack_token}
        
        if not credentials:
            logger.warning("⚠️ No messaging credentials configured")
            return
        
        # Use async notification service directly since we're in async context
        from messaging.notification_service import NotificationService
        
        # Create notification service instance
        notification_service = NotificationService()
        
        # Send notifications to all admin users
        try:
            # Get frontend URL from environment
            from django.conf import settings
            frontend_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
            
            # Authenticate and send the notification asynchronously
            authenticated = await notification_service.authenticate_all_platforms(credentials)
            
            if authenticated:
                result = await notification_service.send_private_message_to_multiple_users(
                    users_emails=emails_to_notify,
                    message_content=f"New QA '{qa_instance.question_title}' has been created in knowledge space '{qa_instance.knowledge_space.name}' and requires approval.\n\nTo see and approve it, please visit {frontend_url}/qa-history",
                    platforms=["slack"],
                    company=company,
                    notification_type="qa_approval"
                )

                if result.get('status') == 'completed':
                    logger.info(f"✅ Notification sent to all these users {emails_to_notify} via Slack")
                    
                    #update the last notification time
                    settings = await sync_to_async(NotificationSettings.objects.get)(company=company)
                    settings.last_notification_time = timezone.now()
                    settings.enabled = True
                    await sync_to_async(settings.save)()
                else:
                    logger.warning(f"⚠️ Failed to send notification to {emails_to_notify}: {result.get('error')}")
            else:
                logger.warning(f"⚠️ No active Slack profile found for admin {emails_to_notify}")
                    
        except Exception as e:
            logger.error(f"❌ Error sending notification to admin {emails_to_notify}: {e}")
            # Continue with other admins even if one fails
                
    except Exception as e:
        logger.exception(f"❌ Error in send_qa_notification_to_admins_and_assignees: {e}")



def get_datasources_to_use_for_search(company_id: str):
    """find all connected platforms ffor a company to search for knowledge"""
    company = Company.objects.get(id=company_id)
    from integrations.models import CompanyIntegration
    datasources_to_use = [DataSource.LOCAL]
    integrations = CompanyIntegration.objects.filter(company=company, is_active=True)
    for integration in integrations:
        if integration.tool.slug == "confluence":
            datasources_to_use.append(DataSource.CONFLUENCE)
        elif integration.tool.slug == "google_drive":
            datasources_to_use.append(DataSource.GOOGLE_DRIVE)
        elif integration.tool.slug == "jira":
            datasources_to_use.append(DataSource.JIRA)
    return  datasources_to_use

 