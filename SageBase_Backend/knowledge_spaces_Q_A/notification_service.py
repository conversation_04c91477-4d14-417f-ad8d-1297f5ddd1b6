"""
Notification service that uses the async task queue for background processing.
This allows synchronous Django views to enqueue async notification tasks.
"""

import logging
from typing import List, Dict, Any, Optional
from asgiref.sync import sync_to_async
from integrations.models import SlackUserProfile
from messaging.notification_service import NotificationService

logger = logging.getLogger(__name__)

class BackgroundNotificationService:
    """Service for enqueueing async notification tasks"""
    
    def __init__(self):
        self.notification_service = NotificationService()
    
    async def send_admin_notifications_async(self, qa_title: str, knowledge_space_name: str, emails_to_notify: List, credentials: Dict[str, Any]):
        """Async function to send admin notifications"""
        try:
            # Authenticate with available platforms
            authenticated = await self.notification_service.authenticate_all_platforms(credentials)
            
            if authenticated:
                for email in emails_to_notify:
                    
                    # Send the notification
                    result = await self.notification_service.send_private_message_to_user(
                        user_email=email,
                        message_content=f"New QA '{qa_title}' has been created in knowledge space '{knowledge_space_name}' and requires approval.",
                        platforms=["slack"]  # Send to Slack only for now
                    )
                    logger.info(f"✅ Notification sent to admin {email} via Slack")

            else:
                logger.warning("⚠️ No messaging platforms authenticated")
                
        except Exception as e:
            logger.error(f"❌ Error sending admin notifications: {e}")

# Global instance
background_notification_service = BackgroundNotificationService()

def enqueue_admin_notification(qa_title: str, knowledge_space_name: str, admin_users: List, credentials: Dict[str, Any]) -> str:
    """Enqueue an admin notification task"""
    from knowledge_spaces_Q_A.async_task_queue import enqueue_notification
    
    task_id = enqueue_notification(
        background_notification_service.send_admin_notifications_async,
        qa_title,
        knowledge_space_name,
        admin_users,
        credentials
    )
    
    logger.info(f"📝 Admin notification task enqueued: {task_id}")
    return task_id 