"""
Session Manager for Persistent Memory and Chat Sessions
======================================================

This module provides OOP-oriented session management for:
- Persistent memory storage and retrieval
- 1-to-1 user-LLM sessions
- Group chat sessions
- Smart context retrieval and management
"""

import logging
from typing import List, Dict, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid

from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist

from .models import ChatSession, ConversationMessage, GroupChatSession
from utils.token_utils import get_token_count

logger = logging.getLogger(__name__)


class SessionType(Enum):
    """Session types for chat management"""
    SINGLE = "single"
    GROUP = "group"


class MessageType(Enum):
    """Message types for conversation tracking"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    ERROR = "error"


@dataclass
class SessionContext:
    """Context information for a chat session"""
    session_id: str
    user_id: str
    user_email: str
    workspace: str = "sagebase"
    preferences: List[str] = field(default_factory=lambda: ["general"])
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "user_email": self.user_email,
            "workspace": self.workspace,
            "preferences": self.preferences,
            "metadata": self.metadata,
        }


@dataclass
class ConversationHistory:
    """Structured conversation history for context building"""
    messages: List[Dict[str, Any]] = field(default_factory=list)
    total_tokens: int = 0
    message_count: int = 0
    
    def add_message(self, role: str, content: str, tokens: int = 0, metadata: Dict[str, Any] = None):
        """Add a message to the conversation history"""
        message = {
            "role": role,
            "content": content,
            "tokens": tokens,
            "metadata": metadata or {},
            "timestamp": datetime.now().isoformat()
        }
        self.messages.append(message)
        self.total_tokens += tokens
        self.message_count += 1
    
    def get_recent_messages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent messages for context building"""
        return self.messages[-limit:] if self.messages else []
    
    def get_total_tokens(self) -> int:
        """Get total tokens used in conversation"""
        return self.total_tokens
    
    def clear(self):
        """Clear conversation history"""
        self.messages.clear()
        self.total_tokens = 0
        self.message_count = 0


class SessionManager:
    """
    Main session manager for handling persistent chat sessions and memory
    """
    
    def __init__(self, workspace: str = "sagebase"):
        self.workspace = workspace
        self.logger = logging.getLogger(f"{__name__}.SessionManager")
    
    async def create_session(
        self,
        user_id: str,
        user_email: str,
        session_type: SessionType = SessionType.SINGLE,
        title: str = None,
        description: str = None,
        participants: List[str] = None,
        preferences: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> ChatSession:
        """
        Create a new chat session
        
        Args:
            user_id: Primary user ID
            user_email: Primary user email
            session_type: Type of session (1-to-1 or group)
            title: Session title
            description: Session description
            participants: List of participant IDs
            preferences: User preferences
            metadata: Additional metadata
            
        Returns:
            Created ChatSession instance
        """
        try:
            from integrations.models import User
            from asgiref.sync import sync_to_async
            
            # Get or create user
            user, created = await sync_to_async(User.objects.get_or_create)(
                email=user_email,
                defaults={'first_name': user_id}
            )
            
            # Prepare participants list
            if participants is None:
                participants = [user_id]
            elif user_id not in participants:
                participants.append(user_id)
            
            # Create session context preferences
            context_preferences = {
                "preferences": preferences or ["general"],
                "workspace": self.workspace,
                "session_type": session_type.value,
                "created_at": timezone.now().isoformat()
            }
            
            # Create the session
            if session_type == SessionType.GROUP:
                session = await sync_to_async(GroupChatSession.objects.create)(
                    session_type=session_type.value,
                    title=title or f"Group Chat {uuid.uuid4().hex[:8]}",
                    description=description,
                    workspace=self.workspace,
                    participants=participants,
                    primary_user=user,
                    context_preferences=context_preferences,
                    metadata=metadata or {},
                    group_name=title or f"Group {uuid.uuid4().hex[:8]}",
                    group_description=description
                )
            else:
                session = await sync_to_async(ChatSession.objects.create)(
                    session_type=session_type.value,
                    title=title or f"Chat Session {uuid.uuid4().hex[:8]}",
                    description=description,
                    workspace=self.workspace,
                    participants=participants,
                    primary_user=user,
                    context_preferences=context_preferences,
                    metadata=metadata or {}
                )
            
            self.logger.info(f"Created {session_type.value} session: {session.id}")
            return session
            
        except Exception as e:
            self.logger.error(f"Failed to create session: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a session by ID"""
        try:
            from asgiref.sync import sync_to_async
            return await sync_to_async(ChatSession.objects.get)(id=session_id)
        except ObjectDoesNotExist:
            return None
    
    async def get_user_sessions(
        self,
        user_id: str,
        session_type: Optional[SessionType] = None,
        status: str = "active",
        limit: int = 20
    ) -> List[ChatSession]:
        """Get sessions for a user"""
        from asgiref.sync import sync_to_async
        
        queryset = ChatSession.objects.filter(
            participants__contains=[user_id],
            status=status
        ).order_by('-last_activity')
        
        if session_type:
            queryset = queryset.filter(session_type=session_type.value)
        
        return await sync_to_async(list)(queryset[:limit])
    
    async def add_message(
        self,
        session_id: str,
        sender_id: str,
        sender_name: str,
        content: str,
        message_type: MessageType = MessageType.USER,
        content_type: str = "text",
        parent_message_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> Optional[ConversationMessage]:
        """
        Add a message to a session
        
        Args:
            session_id: Session ID
            sender_id: Sender ID
            sender_name: Sender name
            content: Message content
            message_type: Type of message
            content_type: Type of content
            parent_message_id: Parent message ID for threading
            metadata: Additional metadata
            
        Returns:
            Created ConversationMessage instance
        """
        try:
            from asgiref.sync import sync_to_async
            
            session = await self.get_session(session_id)
            if not session:
                self.logger.error(f"Session not found: {session_id}")
                return None
            
            # Calculate tokens
            tokens = get_token_count(content)
            
            # Create message
            message = await sync_to_async(ConversationMessage.objects.create)(
                session=session,
                message_type=message_type.value,
                content_type=content_type,
                content=content,
                sender_id=sender_id,
                sender_name=sender_name,
                tokens_count=tokens,
                metadata=metadata or {}
            )
            
            # Set parent message if provided
            if parent_message_id:
                try:
                    parent_message = await sync_to_async(ConversationMessage.objects.get)(id=parent_message_id)
                    message.parent_message = parent_message
                    await sync_to_async(message.save)()
                except ObjectDoesNotExist:
                    self.logger.warning(f"Parent message not found: {parent_message_id}")
            
            # Update session statistics
            session.total_tokens_used += tokens
            session.total_messages += 1
            await sync_to_async(session.update_activity)()
            await sync_to_async(session.save)(update_fields=['total_tokens_used', 'total_messages'])
            
            # Update group stats if it's a group session
            if isinstance(session, GroupChatSession):
                await sync_to_async(session.update_message_stats)()
            
            self.logger.info(f"Added message to session {session_id}: {tokens} tokens")
            return message
            
        except Exception as e:
            self.logger.error(f"Failed to add message: {e}")
            return None
    
    async def get_conversation_history(
        self,
        session_id: str,
        limit: int = 50,
        include_metadata: bool = True
    ) -> ConversationHistory:
        """
        Get conversation history for a session
        
        Args:
            session_id: Session ID
            limit: Maximum number of messages to retrieve
            include_metadata: Whether to include message metadata
            
        Returns:
            ConversationHistory object
        """
        try:
            from asgiref.sync import sync_to_async
            
            session = await self.get_session(session_id)
            if not session:
                return ConversationHistory()
            
            messages = await sync_to_async(list)(session.messages.order_by('created_at')[:limit])
            
            history = ConversationHistory()
            for message in messages:
                metadata = message.metadata if include_metadata else {}
                history.add_message(
                    role=message.message_type,
                    content=message.content,
                    tokens=message.tokens_count,
                    metadata=metadata
                )
            
            return history
            
        except Exception as e:
            self.logger.error(f"Failed to get conversation history: {e}")
            return ConversationHistory()
    
    def get_smart_context(
        self,
        session_id: str,
        current_question: str,
        max_tokens: int = 2000,
        include_semantic_search: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get smart context for a session, combining recent history and semantic search
        
        Args:
            session_id: Session ID
            current_question: Current question being asked
            max_tokens: Maximum tokens for context
            include_semantic_search: Whether to include semantic search results
            
        Returns:
            List of context messages for the LLM
        """
        try:
            # Get recent conversation history
            history = self.get_conversation_history(session_id, limit=20)
            context_messages = []
            
            # Add recent conversation history
            recent_messages = history.get_recent_messages(limit=10)
            for msg in recent_messages:
                context_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Add semantic search results if enabled
            if include_semantic_search:
                try:
                    from vectordb.interfaces import search
                    
                    # Search for semantically similar Q&A
                    results = search(
                        query=current_question,
                        sources=None,
                        limit=3,
                        collection_name=self.workspace,
                        similarity_threshold=0.7,
                        search_mode="hybrid"
                    )
                    
                    # Add semantic search results as context
                    for result in results:
                        if result.content:
                            context_messages.append({
                                "role": "system",
                                "content": f"Relevant context: {result.content}"
                            })
                            
                except Exception as e:
                    self.logger.warning(f"Semantic search failed: {e}")
            
            # Limit context by tokens
            total_tokens = 0
            limited_context = []
            
            for msg in reversed(context_messages):  # Start from most recent
                msg_tokens = get_token_count(msg["content"])
                if total_tokens + msg_tokens <= max_tokens:
                    limited_context.insert(0, msg)  # Add to beginning to maintain order
                    total_tokens += msg_tokens
                else:
                    break
            
            self.logger.info(f"Generated smart context: {len(limited_context)} messages, {total_tokens} tokens")
            return limited_context
            
        except Exception as e:
            self.logger.error(f"Failed to get smart context: {e}")
            return []
    
    async def update_session_status(self, session_id: str, status: str) -> bool:
        """Update session status"""
        try:
            from asgiref.sync import sync_to_async
            
            session = await self.get_session(session_id)
            if session:
                session.status = status
                await sync_to_async(session.save)(update_fields=['status'])
                self.logger.info(f"Updated session {session_id} status to {status}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to update session status: {e}")
            return False
    
    async def archive_session(self, session_id: str) -> bool:
        """Archive a session"""
        return await self.update_session_status(session_id, "archived")
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session (soft delete)"""
        return await self.update_session_status(session_id, "deleted")
    
    async def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get session statistics"""
        try:
            session = await self.get_session(session_id)
            if not session:
                return {}
            
            return {
                "session_id": str(session.id),
                "session_type": session.session_type,
                "status": session.status,
                "participant_count": session.get_participant_count(),
                "total_messages": session.total_messages,
                "total_tokens": session.total_tokens_used,
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
                "workspace": session.workspace
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get session stats: {e}")
            return {}


class GroupChatManager(SessionManager):
    """
    Extended session manager for group chat functionality
    """
    
    async def create_group_chat(
        self,
        creator_id: str,
        creator_email: str,
        group_name: str,
        group_description: str = None,
        is_public: bool = False,
        allow_join: bool = True,
        max_participants: int = 50,
        initial_participants: List[str] = None
    ) -> Optional[GroupChatSession]:
        """
        Create a new group chat session
        
        Args:
            creator_id: Creator user ID
            creator_email: Creator email
            group_name: Name of the group
            group_description: Group description
            is_public: Whether the group is public
            allow_join: Whether users can join
            max_participants: Maximum number of participants
            initial_participants: Initial participants list
            
        Returns:
            Created GroupChatSession instance
        """
        try:
            from integrations.models import User
            from asgiref.sync import sync_to_async
            
            # Get or create creator user
            creator, created = await sync_to_async(User.objects.get_or_create)(
                email=creator_email,
                defaults={'first_name': creator_id}
            )
            
            # Prepare participants list
            participants = initial_participants or []
            if creator_id not in participants:
                participants.append(creator_id)
            
            # Create group chat session
            session = await sync_to_async(GroupChatSession.objects.create)(
                session_type=SessionType.GROUP.value,
                title=group_name,
                description=group_description,
                workspace=self.workspace,
                participants=participants,
                primary_user=creator,
                group_name=group_name,
                group_description=group_description,
                is_public=is_public,
                allow_join=allow_join,
                max_participants=max_participants,
                admins=[creator_id],  # Creator is admin
                moderators=[creator_id]  # Creator is moderator
            )
            
            self.logger.info(f"Created group chat: {group_name} ({session.id})")
            return session
            
        except Exception as e:
            self.logger.error(f"Failed to create group chat: {e}")
            return None
    
    async def join_group(self, session_id: str, user_id: str, user_email: str) -> bool:
        """Join a group chat"""
        try:
            from asgiref.sync import sync_to_async
            
            session = await self.get_session(session_id)
            if not session or not isinstance(session, GroupChatSession):
                return False
            
            if not session.can_join(user_id):
                self.logger.warning(f"User {user_id} cannot join group {session_id}")
                return False
            
            await sync_to_async(session.add_participant)(user_id)
            self.logger.info(f"User {user_id} joined group {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to join group: {e}")
            return False
    
    async def leave_group(self, session_id: str, user_id: str) -> bool:
        """Leave a group chat"""
        try:
            from asgiref.sync import sync_to_async
            
            session = await self.get_session(session_id)
            if not session or not isinstance(session, GroupChatSession):
                return False
            
            await sync_to_async(session.remove_participant)(user_id)
            self.logger.info(f"User {user_id} left group {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to leave group: {e}")
            return False
    
    async def get_public_groups(self, limit: int = 20) -> List[GroupChatSession]:
        """Get public group chats"""
        from asgiref.sync import sync_to_async
        return await sync_to_async(list)(GroupChatSession.objects.filter(
            is_public=True,
            status="active"
        ).order_by('-last_activity')[:limit])


# Global session manager instances
session_manager = SessionManager()
group_chat_manager = GroupChatManager() 