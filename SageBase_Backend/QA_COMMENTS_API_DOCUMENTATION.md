# QA Comments API Documentation

## Overview
This API provides Stack Overflow-style commenting functionality for QA entries. Users can add comments, edit their own comments, delete their comments, and vote on comments with thumbs up/down.

## ⚠️ Important Note
**The frontend should NOT use `/create` suffix in URLs.** The correct endpoints are:
- ✅ `POST /api/qa/{qa_id}/comments` (NOT `/comments/create`)
- ✅ `GET /api/qa/{qa_id}/comments`
- ✅ `PUT /api/qa/{qa_id}/comments/{comment_id}` (NOT `/comments/{comment_id}/edit`)
- ✅ `DELETE /api/qa/{qa_id}/comments/{comment_id}` (NOT `/comments/{comment_id}/delete`)
- ✅ `POST /api/qa/{qa_id}/comments/{comment_id}/vote`
- ✅ `GET /api/qa/{qa_id}/views`
- ✅ `POST /api/qa/{qa_id}/views`

## Authentication
All endpoints require authentication. Include the user's authentication token in the request headers.

## Base URL
```
https://your-domain.com/api/
```

---

## API Endpoints

### 1. Get Comments for a QA

**Endpoint:** `GET /api/qa/{qa_id}/comments`

**Description:** Retrieve all comments for a specific QA entry, ordered by creation date.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry

**Response:**
```json
{
  "success": true,
  "qa_id": "qa-abc12345",
  "comments": [
    {
      "id": "comment-xyz789",
      "content": "This is a helpful comment!",
      "author": {
        "id": "user123",
        "name": "John Doe",
        "avatar": "https://example.com/avatar.jpg"
      },
      "timestamp": "14:30",
      "date": "Today",
      "votes": {
        "upvotes": 5,
        "downvotes": 1
      },
      "is_edited": false,
      "created_at": "2024-08-08T14:30:00Z",
      "updated_at": "2024-08-08T14:30:00Z"
    }
  ],
  "total_comments": 1
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message"
}
```

---

### 2. Create a Comment

**Endpoint:** `POST /api/qa/{qa_id}/comments`

**Description:** Create a new comment for a QA entry.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry

**Request Body:**
```json
{
  "content": "This is my comment text"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Comment created successfully",
  "comment": {
    "id": "comment-xyz789",
    "content": "This is my comment text",
    "author": {
      "id": "user123",
      "name": "John Doe",
      "avatar": "https://example.com/avatar.jpg"
    },
    "timestamp": "14:30",
    "date": "Today",
    "votes": {
      "upvotes": 0,
      "downvotes": 0
    },
    "is_edited": false,
    "created_at": "2024-08-08T14:30:00Z",
    "updated_at": "2024-08-08T14:30:00Z"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Invalid comment data",
  "details": {
    "content": ["This field is required."]
  }
}
```

---

### 3. Update a Comment

**Endpoint:** `PUT /api/qa/{qa_id}/comments/{comment_id}`

**Description:** Update an existing comment. Users can only edit their own comments.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry
- `comment_id` (string, required): The ID of the comment to update

**Request Body:**
```json
{
  "content": "Updated comment text"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Comment updated successfully",
  "comment": {
    "id": "comment-xyz789",
    "content": "Updated comment text",
    "author": {
      "id": "user123",
      "name": "John Doe",
      "avatar": "https://example.com/avatar.jpg"
    },
    "timestamp": "14:30",
    "date": "Today",
    "votes": {
      "upvotes": 5,
      "downvotes": 1
    },
    "is_edited": true,
    "created_at": "2024-08-08T14:30:00Z",
    "updated_at": "2024-08-08T15:45:00Z"
  }
}
```

**Error Responses:**

**Unauthorized (403):**
```json
{
  "success": false,
  "error": "You can only edit your own comments"
}
```

**Invalid Data (400):**
```json
{
  "success": false,
  "error": "Invalid comment data",
  "details": {
    "content": ["This field is required."]
  }
}
```

---

### 4. Delete a Comment

**Endpoint:** `DELETE /api/qa/{qa_id}/comments/{comment_id}`

**Description:** Delete a comment. Users can only delete their own comments.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry
- `comment_id` (string, required): The ID of the comment to delete

**Response:**
```json
{
  "success": true,
  "message": "Comment deleted successfully"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "You can only delete your own comments"
}
```

---

### 5. Vote on a Comment

**Endpoint:** `POST /api/qa/{qa_id}/comments/{comment_id}/vote`

**Description:** Vote on a comment with thumbs up or down. Users can change their vote or remove it by voting the same way again.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry
- `comment_id` (string, required): The ID of the comment to vote on

**Request Body:**
```json
{
  "vote_type": "up"
}
```

**Vote Types:**
- `"up"`: Thumbs up
- `"down"`: Thumbs down

**Response:**
```json
{
  "success": true,
  "message": "Vote updated successfully",
  "vote_data": {
    "votes": {
      "upvotes": 6,
      "downvotes": 1
    },
    "user_vote": "up"
  }
}
```

**User Vote Values:**
- `"up"`: User voted thumbs up
- `"down"`: User voted thumbs down
- `"none"`: User has no vote (removed their vote)

**Error Response:**
```json
{
  "success": false,
  "error": "Invalid vote data",
  "details": {
    "vote_type": ["\"invalid\" is not a valid choice."]
  }
}
```

---

### 6. Get QA Views

**Endpoint:** `GET /api/qa/{qa_id}/views`

**Description:** Get the current view count for a QA entry.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry

**Response:**
```json
{
  "success": true,
  "qa_id": "qa-abc12345",
  "views": 42
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message"
}
```

---

### 7. Increment QA Views

**Endpoint:** `POST /api/qa/{qa_id}/views`

**Description:** Track a page view for a QA entry. This creates a detailed page view record and increments the total view count. Should be called when a user views the QA.

**Parameters:**
- `qa_id` (string, required): The ID of the QA entry

**What Gets Tracked:**
- User ID (if authenticated)
- IP address
- User agent (browser/device info)
- Session ID
- Referrer URL
- Timestamp

**Response:**
```json
{
  "success": true,
  "message": "Page view tracked successfully",
  "views": 43
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message"
}
```

---

## Frontend Implementation Examples

### JavaScript/TypeScript Examples

#### Get Comments
```javascript
async function getComments(qaId) {
  const response = await fetch(`/api/qa/${qaId}/comments`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  if (data.success) {
    return data.comments;
  } else {
    throw new Error(data.error);
  }
}
```

#### Create Comment
```javascript
async function createComment(qaId, content) {
  const response = await fetch(`/api/qa/${qaId}/comments`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ content })
  });
  
  const data = await response.json();
  if (data.success) {
    return data.comment;
  } else {
    throw new Error(data.error);
  }
}
```

#### Update Comment
```javascript
async function updateComment(qaId, commentId, content) {
  const response = await fetch(`/api/qa/${qaId}/comments/${commentId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ content })
  });
  
  const data = await response.json();
  if (data.success) {
    return data.comment;
  } else {
    throw new Error(data.error);
  }
}
```

#### Delete Comment
```javascript
async function deleteComment(qaId, commentId) {
  const response = await fetch(`/api/qa/${qaId}/comments/${commentId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  if (data.success) {
    return true;
  } else {
    throw new Error(data.error);
  }
}
```

#### Vote on Comment
```javascript
async function voteComment(qaId, commentId, voteType) {
  const response = await fetch(`/api/qa/${qaId}/comments/${commentId}/vote`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ vote_type: voteType })
  });
  
  const data = await response.json();
  if (data.success) {
    return data.vote_data;
  } else {
    throw new Error(data.error);
  }
}
```

#### Get QA Views
```javascript
async function getQAViews(qaId) {
  const response = await fetch(`/api/qa/${qaId}/views`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  if (data.success) {
    return data.views;
  } else {
    throw new Error(data.error);
  }
}
```

#### Increment QA Views
```javascript
async function incrementQAViews(qaId) {
  const response = await fetch(`/api/qa/${qaId}/views`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  if (data.success) {
    return data.views;
  } else {
    throw new Error(data.error);
  }
}
```

### React Example
```jsx
import React, { useState, useEffect } from 'react';

function CommentSection({ qaId }) {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [views, setViews] = useState(0);

  useEffect(() => {
    loadComments();
    loadViews();
  }, [qaId]);

  const loadComments = async () => {
    try {
      const commentsData = await getComments(qaId);
      setComments(commentsData);
    } catch (error) {
      console.error('Error loading comments:', error);
    }
  };

  const loadViews = async () => {
    try {
      const viewsCount = await getQAViews(qaId);
      setViews(viewsCount);
    } catch (error) {
      console.error('Error loading views:', error);
    }
  };

  const handleViewQA = async () => {
    try {
      const newViewsCount = await incrementQAViews(qaId);
      setViews(newViewsCount);
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  };

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    setLoading(true);
    try {
      const comment = await createComment(qaId, newComment);
      setComments([...comments, comment]);
      setNewComment('');
    } catch (error) {
      console.error('Error creating comment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVote = async (commentId, voteType) => {
    try {
      const voteData = await voteComment(qaId, commentId, voteType);
      // Update the comment's vote counts in the UI
      setComments(comments.map(comment => 
        comment.id === commentId 
          ? { ...comment, votes: voteData.votes }
          : comment
      ));
    } catch (error) {
      console.error('Error voting:', error);
    }
  };

  const handleEditComment = async (commentId, newContent) => {
    try {
      const updatedComment = await updateComment(qaId, commentId, newContent);
      setComments(comments.map(comment => 
        comment.id === commentId ? updatedComment : comment
      ));
    } catch (error) {
      console.error('Error updating comment:', error);
    }
  };

  const handleDeleteComment = async (commentId) => {
    try {
      await deleteComment(qaId, commentId);
      setComments(comments.filter(comment => comment.id !== commentId));
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };

  return (
    <div className="comment-section">
      <div className="qa-stats">
        <span>👁️ {views} views</span>
        <button onClick={handleViewQA}>Mark as Viewed</button>
      </div>
      
      <h3>Comments ({comments.length})</h3>
      
      {/* Comment Form */}
      <form onSubmit={handleSubmitComment}>
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          disabled={loading}
        />
        <button type="submit" disabled={loading}>
          {loading ? 'Posting...' : 'Post Comment'}
        </button>
      </form>

      {/* Comments List */}
      <div className="comments-list">
        {comments.map(comment => (
          <div key={comment.id} className="comment">
            <div className="comment-content">{comment.content}</div>
            <div className="comment-meta">
              <span>{comment.author.name}</span>
              <span>{comment.date} at {comment.timestamp}</span>
              {comment.is_edited && <span>(edited)</span>}
            </div>
            <div className="comment-votes">
              <button onClick={() => handleVote(comment.id, 'up')}>
                👍 {comment.votes.upvotes}
              </button>
              <button onClick={() => handleVote(comment.id, 'down')}>
                👎 {comment.votes.downvotes}
              </button>
            </div>
            {/* Show edit/delete buttons only for user's own comments */}
            {comment.author.id === currentUserId && (
              <div className="comment-actions">
                <button onClick={() => handleEditComment(comment.id, newContent)}>
                  Edit
                </button>
                <button onClick={() => handleDeleteComment(comment.id)}>
                  Delete
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
```

---