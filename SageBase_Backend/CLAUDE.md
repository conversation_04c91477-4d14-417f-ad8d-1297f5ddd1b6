# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup

#### Option 1: Docker (Recommended for Teams)
```bash
# One-command setup
./scripts/setup.sh

# Development commands
./scripts/dev.sh start    # Start environment
./scripts/dev.sh logs     # View logs
./scripts/dev.sh shell    # Access container shell
./scripts/dev.sh test     # Run tests
./scripts/dev.sh stop     # Stop environment
```

#### Option 2: Manual Setup
```bash
# Create and activate virtual environment with uv
uv venv
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# Install dependencies
uv pip install -r requirements.txt
```

### Running the Server
```bash
# Development server
uv run python manage.py runserver 0.0.0.0:8000

# VS Code debugging available via F5 (uses .vscode/launch.json)
```

### Database Operations
```bash
# Run migrations
python manage.py migrate

# Create new migrations after model changes
python manage.py makemigrations
```

### Testing
```bash
# Run tests
python manage.py test

# Run tests for specific app
python manage.py test integrations
```

## Architecture Overview

### Core Structure
- **Django 4.2 REST API** with minimal apps (no admin, auth, sessions by default)
- **Supabase PostgreSQL** database with SSL requirement
- **Django Channels** for WebSocket support and real-time notifications
- **uv package manager** for Python dependency management

### Key Applications
- `integrations/` - Main application containing GitHub integration functionality
- `SageBase_Backend/` - Django project configuration

### GitHub Integration System
The core feature is a comprehensive GitHub integration with three main components:

#### 1. LLM-Powered Repository Analysis (`/api/integrations/github/ask/`)
- **Intent Classification**: Automatically detects user intent (commits, PRs, code search, contributors)
- **Smart Code Search**: Uses both rule-based and LLM-based keyword extraction
- **Context Assembly**: Combines GitHub API data with code snippets
- **Answer Generation**: Uses Google Gemini (gemini-2.0-flash) for natural language responses
- **Caching Layer**: Redis-like caching for GitHub API responses

#### 2. Webhook Management (`/api/integrations/github/webhooks/`)
- **Dynamic Webhook Creation**: Programmatically create/delete GitHub webhooks
- **Event Processing**: Handle push events and other GitHub webhook events
- **Webhook Receiver**: Endpoint for GitHub to send webhook payloads

#### 3. Real-time Notification System
- **WebSocket Consumer**: `NotificationConsumer` for real-time updates
- **Channel Groups**: Subscribe/unsubscribe to specific repository notifications
- **Push Event Broadcasting**: Webhook events trigger WebSocket notifications to subscribed clients

### Key Services and Components

#### GitHub API Service (`integrations/github/api/services.py`)
- Handles all GitHub API interactions (commits, PRs, contributors, code search)
- Implements caching for performance
- Processes different intents (list_commits, list_prs, etc.)
- Manages file content retrieval and code snippet extraction

#### LLM Service (`integrations/github/llm/services.py`)
- Google Gemini integration for natural language processing
- Intent classification and keyword extraction
- Answer generation from combined context
- Both async and sync operation modes

#### WebSocket Routing
- Configured in `SageBase_Backend/asgi.py`
- Routes WebSocket connections through `integrations.github.api.routing`
- Supports real-time notifications for repository events

### Configuration Requirements

#### Environment Variables (.env)
- `DATABASE_URL` - Supabase PostgreSQL connection string
- `GITHUB_CLIENT_ID` - GitHub OAuth app client ID
- `GITHUB_CLIENT_SECRET` - GitHub OAuth app secret  
- `GITHUB_REDIRECT_URI` - OAuth callback URL
- `GEMINI_API_KEY` - Google Gemini API key for LLM functionality

#### Database Configuration
- Uses Supabase PostgreSQL with SSL requirement
- Supports both `DATABASE_URL` and individual connection parameters
- SSL mode is enforced for all connections

### API Endpoints Structure
```
/api/integrations/
├── github/
│   ├── ask/                    # LLM-powered repository analysis
│   ├── webhooks/              # Webhook management (list/create)
│   ├── webhooks/delete/       # Webhook deletion
│   └── webhook/receive/       # GitHub webhook receiver
```

### WebSocket Endpoints
- WebSocket connections for real-time notifications
- Subscribe/unsubscribe to specific repository events
- Automatic notification broadcasting on webhook events

### Vector Database (ChromaDB) Integration

#### Core Components
- **ChromaDB Service** (`vectordb/services/chroma_service.py`) - Base connection management and client operations
- **Document Service** (`vectordb/services/document_service.py`) - High-level CRUD operations for documents
- **Collection Manager** (`vectordb/collections/collection_manager.py`) - Multi-source data management
- **Document Models** (`vectordb/models/document.py`) - Data structures for different sources

#### Supported Data Sources
- Confluence, Google Drive, Notion, GitHub, Slack, JIRA, and generic sources
- Each source has dedicated collections with source-specific metadata
- Unified search and management across all sources

#### Key Features
- **Semantic Search**: Using SentenceTransformer embeddings (all-MiniLM-L6-v2)
- **Text Chunking**: Intelligent document splitting with overlap for large documents
- **Metadata Filtering**: Advanced search with author, workspace, tags, date range filters
- **Bulk Operations**: Efficient batch import and management
- **Health Monitoring**: Comprehensive system health checks and statistics

#### API Endpoints (`/api/vectordb/`)
- `GET /health/` - System health check
- `GET /stats/` - Collection statistics
- `POST /documents/add/` - Add single document
- `POST /documents/bulk-add/` - Bulk document import
- `POST /documents/search/` - Semantic search with filters
- `DELETE /documents/delete/` - Delete documents

#### Configuration
- `CHROMA_PERSIST_DIR` - Local storage directory (default: `./chroma_db/`)
- `CHROMA_HOST` / `CHROMA_PORT` - Remote ChromaDB server (optional)
- `CHROMA_EMBEDDING_MODEL` - SentenceTransformer model name

#### Usage Patterns
```python
from vectordb.collections.collection_manager import get_collection_manager
from vectordb.models.document import DataSource

# Add document
manager = get_collection_manager()
manager.add_document(
    content="Document content",
    source=DataSource.CONFLUENCE,
    source_id="unique_id",
    title="Document Title",
    workspace="team_name"
)

# Search across sources
results = manager.search_across_sources(
    query="search terms",
    sources=[DataSource.CONFLUENCE, DataSource.NOTION],
    workspace="team_name"
)
```

#### Testing
- Run examples: `python manage.py test_vectordb`
- Unit tests: `python manage.py test vectordb`
- Example usage: `vectordb/examples/usage_examples.py`

### Development Notes

#### LLM Integration Patterns
- Always check `github_llm_service` availability before LLM operations
- Use async/await pattern for LLM service calls
- Implement proper error handling for LLM service failures
- Cache LLM responses when appropriate

#### GitHub API Best Practices
- All GitHub API calls go through `GitHubAPIService`
- Use caching (`github_cache`) to minimize API rate limit usage
- Always validate GitHub tokens before API operations
- Handle rate limiting and API errors gracefully

#### WebSocket Implementation
- Use `NotificationConsumer` for real-time features
- Implement proper connection/disconnection handling
- Use channel groups for broadcasting to specific repository subscribers
- Always handle WebSocket message parsing errors

#### Testing Approach
- Basic test structure exists in `integrations/tests.py`
- Use Django's test framework for unit and integration tests
- Test both sync and async operations appropriately
- Mock external services (GitHub API, Gemini) in tests