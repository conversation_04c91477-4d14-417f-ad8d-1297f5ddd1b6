import logging
import threading
from datetime import datetime, timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Project, ProjectContributors
from integrations.models import CompanyIntegration, IntegrationTool
import requests

logger = logging.getLogger(__name__)


def _fetch_and_update_contributors(project_id: str):
    try:
        project = Project.objects.get(id=project_id)
    except Project.DoesNotExist:
        return

    # Only GitHub projects
    if not project.repository_url or project.repository_provider != Project.RepoProvider.GITHUB:
        return

    try:
        tool = IntegrationTool.objects.get(slug="github")
        integ = CompanyIntegration.objects.filter(company=project.company, tool=tool, is_active=True).first()
        if not integ:
            logger.debug(f"[Contrib] No GitHub integration for company {project.company_id}")
            return
        token = integ.get_valid_github_token()
    except Exception as e:
        logger.warning(f"[Contrib] Failed to get token for project {project_id}: {e}")
        return

    # Determine owner/repo
    full_name = project.repository_full_name
    if not full_name:
        try:
            parts = project.repository_url.rstrip('/').split('/')
            full_name = '/'.join(parts[-2:])
        except Exception:
            pass
    if not full_name or '/' not in full_name:
        logger.warning(f"[Contrib] Cannot infer owner/repo for project {project_id}")
        return

    owner, repo = full_name.split('/', 1)
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/vnd.github+json',
        'X-GitHub-Api-Version': '2022-11-28',
        'User-Agent': 'SageBase-Contrib-Updater'
    }

    try:
        # Get contributors list
        r = requests.get(f"https://api.github.com/repos/{owner}/{repo}/contributors", headers=headers, timeout=30)
        if r.status_code >= 300:
            logger.warning(f"[Contrib] GitHub contributors API {r.status_code} for {owner}/{repo}")
            return
        contributors = r.json() or []
    except Exception as e:
        logger.warning(f"[Contrib] Error calling GitHub contributors API: {e}")
        return

    # Fetch detailed contribution history for time-based filtering
    contributions_history = {
        'commits': [],
        'pull_requests': []
    }

    # Fetch commits for each contributor with timestamps
    for contributor in contributors:
        login = contributor.get('login')
        if not login:
            continue

        try:
            # Get commits by this contributor
            commits_response = requests.get(
                f"https://api.github.com/repos/{owner}/{repo}/commits?author={login}&per_page=100",
                headers=headers,
                timeout=30
            )
            
            if commits_response.status_code < 300:
                commits_data = commits_response.json() or []
                for commit in commits_data:
                    try:
                        commit_info = {
                            'author': login,
                            'sha': commit.get('sha', ''),
                            'date': commit.get('commit', {}).get('author', {}).get('date', ''),
                            'message': commit.get('commit', {}).get('message', ''),
                            'lines_added': 0,
                            'lines_deleted': 0
                        }
                        
                        # Get commit stats if available
                        if commit.get('stats'):
                            commit_info['lines_added'] = commit['stats'].get('additions', 0)
                            commit_info['lines_deleted'] = commit['stats'].get('deletions', 0)
                        
                        contributions_history['commits'].append(commit_info)
                    except Exception as e:
                        logger.debug(f"[Contrib] Error processing commit {commit.get('sha', 'unknown')}: {e}")
                        continue
        except Exception as e:
            logger.debug(f"[Contrib] Error fetching commits for {login}: {e}")
            continue

        # Fetch pull requests by this contributor
        try:
            prs_response = requests.get(
                f"https://api.github.com/search/issues?q=repo:{owner}/{repo}+type:pr+author:{login}&per_page=100",
                headers=headers,
                timeout=30
            )
            
            if prs_response.status_code < 300:
                prs_data = prs_response.json() or {}
                for pr in prs_data.get('items', []):
                    try:
                        pr_info = {
                            'author': login,
                            'number': pr.get('number', 0),
                            'date': pr.get('created_at', ''),
                            'state': pr.get('state', ''),
                            'title': pr.get('title', ''),
                            'lines_added': 0,
                            'lines_deleted': 0
                        }
                        
                        # Get PR stats if available (requires additional API call)
                        try:
                            pr_stats_response = requests.get(
                                f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr['number']}",
                                headers=headers,
                                timeout=20
                            )
                            if pr_stats_response.status_code < 300:
                                pr_stats = pr_stats_response.json()
                                pr_info['lines_added'] = pr_stats.get('additions', 0)
                                pr_info['lines_deleted'] = pr_stats.get('deletions', 0)
                        except Exception:
                            pass  # Skip stats if unavailable
                        
                        contributions_history['pull_requests'].append(pr_info)
                    except Exception as e:
                        logger.debug(f"[Contrib] Error processing PR {pr.get('number', 'unknown')}: {e}")
                        continue
        except Exception as e:
            logger.debug(f"[Contrib] Error fetching PRs for {login}: {e}")
            continue

    # Process contributors data with aggregated metrics
    contributors_data = []
    for contributor in contributors:
        login = contributor.get('login')
        if not login:
            continue

        # Calculate metrics from detailed history
        user_commits = [c for c in contributions_history['commits'] if c['author'] == login]
        user_prs = [p for p in contributions_history['pull_requests'] if p['author'] == login]
        
        # Calculate lines of code from commits
        total_lines = sum(c.get('lines_added', 0) + c.get('lines_deleted', 0) for c in user_commits)
        
        # Get last contribution date
        last_contribution = ''
        if user_commits:
            commit_dates = [c.get('date', '') for c in user_commits if c.get('date')]
            if commit_dates:
                last_contribution = max(commit_dates)
        if user_prs and not last_contribution:
            pr_dates = [p.get('date', '') for p in user_prs if p.get('date')]
            if pr_dates:
                last_contribution = max(pr_dates)

        contributors_data.append({
            'github_username': login,
            'name': login,
            'commits': len(user_commits),
            'linesOfCode': total_lines,
            'pullRequests': len(user_prs),
            'docContributions': 0,  # Could be enhanced with documentation detection
            'contributions': min(100, len(user_commits) * 2 + len(user_prs)),
            'last_contribution': last_contribution or datetime.utcnow().isoformat() + 'Z',
            'email': '',
            'role': ''
        })

    # Update the ProjectContributors record with both data and history
    record, _ = ProjectContributors.objects.get_or_create(project=project)
    record.update_contributors_data(contributors_data, contributions_history)
    logger.info(f"[Contrib] Updated {len(contributors_data)} contributors for project {project.id} with detailed history")


@receiver(post_save, sender=Project)
def create_contributors_and_kickoff_fetch(sender, instance: Project, created: bool, **kwargs):
    # Ensure related ProjectContributors exists
    ProjectContributors.objects.get_or_create(project=instance)

    # Kick off a one-shot background fetch on create or when repo fields change
    should_fetch = created
    update_fields = kwargs.get('update_fields')
    if update_fields:
        if any(f in update_fields for f in ['repository_url', 'repository_full_name', 'repository_provider']):
            should_fetch = True

    if should_fetch:
        t = threading.Thread(target=_fetch_and_update_contributors, args=(str(instance.id),), daemon=True)
        t.start()


