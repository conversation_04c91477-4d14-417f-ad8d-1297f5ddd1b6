from django.contrib import admin
from .models import Project, ProjectContributors, ProjectMetrics


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'company', 'status', 'repository_provider', 'repository_full_name',
        'last_activity', 'total_contributors',
    )
    list_filter = ('company', 'status', 'repository_provider')
    search_fields = ('name', 'slug', 'repository_full_name', 'repository_url')
    readonly_fields = ('created_at', 'updated_at', 'last_activity')
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'slug', 'description', 'status')
        }),
        ('Repository', {
            'fields': (
                'repository_provider', 'repository_full_name', 'repository_id',
                'repository_url', 'documentation_url',
            )
        }),
        ('Classification', {
            'fields': ('categories', 'tags')
        }),
        ('Tracking', {
            'fields': ('last_activity', 'total_contributors', 'created_at', 'updated_at')
        }),
    )


@admin.register(ProjectContributors)
class ProjectContributorsAdmin(admin.ModelAdmin):
    list_display = ('project', 'total_contributors', 'last_fetched', 'created_at')
    list_filter = ('project__company',)
    search_fields = ('project__name',)
    readonly_fields = ('last_fetched', 'created_at', 'updated_at')


@admin.register(ProjectMetrics)
class ProjectMetricsAdmin(admin.ModelAdmin):
    list_display = ('project', 'health_score', 'risk_level', 'documentation_coverage', 'active_contributors', 'last_updated')
    list_filter = ('risk_level', 'project__company')
    search_fields = ('project__name',)
    readonly_fields = ('last_updated', 'created_at')


