from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuth<PERSON>icated, AllowAny
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, F
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.paginator import <PERSON>ginator

from integrations.models import User, Company
from .models import Project, ProjectContributors, ProjectMetrics
from .serializers import (
    ProjectSerializer, ProjectCreateSerializer, ProjectUpdateSerializer,
    TeamMemberSerializer, ProjectContributorSerializer, ProjectMetricsSerializer,
    DocumentationAssignmentSerializer, ProjectSearchSerializer, ContributorDetailSerializer
)
import logging
import jwt
from django.conf import settings

logger = logging.getLogger(__name__)

def get_user_from_token(request):
    """Extract user from Authorization header token"""
    # Simple request-level caching to avoid repeated lookups
    if hasattr(request, '_cached_user'):
        return request._cached_user
    
    # Rate limiting: don't make too many database queries
    if hasattr(request, '_auth_attempts'):
        request._auth_attempts += 1
        if request._auth_attempts > 3:  # Max 3 attempts per request
            logger.warning("Too many authentication attempts, returning None")
            return None
    else:
        request._auth_attempts = 1
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    try:
        token = auth_header.split(' ')[1]
        
        # Decode the JWT token (without verification for now)
        decoded_token = jwt.decode(token, options={"verify_signature": False})
        
        # Extract user email from the token
        user_email = decoded_token.get('email')
        logger.info(f"Extracted email from token: {user_email}")
        if not user_email:
            logger.warning("No email found in JWT token")
            return None
        
        # Find the user in Django by email with connection error handling
        from integrations.models import User
        from django.db import connection
        
        try:
            # Use select_related to optimize the query
            user = User.objects.select_related('company').get(email=user_email)
            logger.info(f"Found user in database: {user.email}, company: {user.company}")
            # Cache the result for this request
            request._cached_user = user
            return user
        except User.DoesNotExist:
            logger.warning(f"User with email {user_email} not found in Django database")
            request._cached_user = None
            return None
        except Exception as e:
            # Handle database connection errors
            if "MaxClientsInSessionMode" in str(e) or "connection" in str(e).lower():
                logger.error(f"Database connection error: {e}")
                # Close the connection to free up the pool
                connection.close()
                request._cached_user = None
                return None
            else:
                logger.warning(f"Failed to get user from database: {e}")
                request._cached_user = None
                return None
        
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {e}")
        return None
    except Exception as e:
        logger.warning(f"Failed to extract user from token: {e}")
        return None


def get_company(company_id):
    """Helper function to get company by ID"""
    try:
        return Company.objects.get(id=company_id)
    except Company.DoesNotExist:
        return None


class ProjectsAPIView(APIView):
    """API for managing projects"""
    permission_classes = [AllowAny]  # Keep AllowAny for now, but we'll get company from user if authenticated
    
    def get(self, request):
        """Get all projects with optional time range filter"""
        try:
            # Get company from authenticated user if available, otherwise fallback to query param
            company = None
            
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated (handle None case)
            if user and user.is_authenticated:
                # User is authenticated, get their company
                company = user.company
                if not company:
                    return Response({"error": "User has no associated company"}, status=400)
            
            # Filter projects by user's company
            projects = Project.objects.filter(company=company)
            
            time_range = request.query_params.get('timeRange')
            
            # Apply time range filter if provided
            if time_range:
                cutoff_date = self._get_cutoff_date(time_range)
                if cutoff_date:
                    projects = projects.filter(last_activity__gte=cutoff_date)
            
            # Prefetch related data for performance
            projects = projects.select_related('metrics')
            
            serializer = ProjectSerializer(projects, many=True, context={
                "time_range": time_range
            })
            return Response({"data": serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error fetching projects")
            return Response(
                {"error": f"Failed to fetch projects: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        """Create a new project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Debug logging
            logger.info(f"POST /api/projects - User from token: {user}")
            logger.info(f"POST /api/projects - User authenticated: {user.is_authenticated if user else 'None'}")
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            serializer = ProjectCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            project = serializer.save(company=company)
            
            # Create default metrics and empty contributors record
            ProjectMetrics.objects.create(project=project)
            try:
                if not ProjectContributors.objects.filter(project=project).exists():
                    ProjectContributors.objects.create(project=project, contributors_data=[], contributions_history={})
            except Exception as ce:
                logger.warning(f"Could not initialize ProjectContributors for project {project.id}: {ce}")
            
            response_serializer = ProjectSerializer(project)
            return Response({"data": response_serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error creating project")
            return Response(
                {"error": f"Failed to create project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_cutoff_date(self, time_range):
        """Get cutoff date based on time range"""
        now = timezone.now()
        if time_range == '1month':
            return now - timedelta(days=30)
        elif time_range == '3months':
            return now - timedelta(days=90)
        elif time_range == '6months':
            return now - timedelta(days=180)
        elif time_range == '1year':
            return now - timedelta(days=365)
        return None


class ProjectDetailAPIView(APIView):
    """API for managing individual projects"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def get(self, request, project_id):
        """Get a specific project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            project = get_object_or_404(
                Project.objects.select_related('metrics'),
                id=project_id,
                company=company
            )
            
            try:
                serializer = ProjectSerializer(project, context={
                    "time_range": request.query_params.get('timeRange')
                })
                return Response({"data": serializer.data, "success": True})
            except Exception as e:
                logger.exception("Error serializing project detail")
                return Response({"error": f"Serialization error: {str(e)}"}, status=500)
            
        except Exception as e:
            logger.exception("Error fetching project")
            return Response(
                {"error": f"Failed to fetch project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request, project_id):
        """Update a project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=company
            )
            
            serializer = ProjectUpdateSerializer(project, data=request.data, partial=True)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            project = serializer.save()
            response_serializer = ProjectSerializer(project)
            return Response({"data": response_serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error updating project")
            return Response(
                {"error": f"Failed to update project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def delete(self, request, project_id):
        """Delete a project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=company
            )
            
            # Delete related objects first
            try:
                # Delete ProjectContributors
                ProjectContributors.objects.filter(project=project).delete()
                logger.info(f"Deleted ProjectContributors for project {project_id}")
            except Exception as e:
                logger.warning(f"Could not delete ProjectContributors for project {project_id}: {e}")
            
            try:
                # Delete ProjectMetrics
                ProjectMetrics.objects.filter(project=project).delete()
                logger.info(f"Deleted ProjectMetrics for project {project_id}")
            except Exception as e:
                logger.warning(f"Could not delete ProjectMetrics for project {project_id}: {e}")
            
            # Finally delete the project
            project.delete()
            return Response({"success": True, "message": "Project deleted successfully"})
            
        except Exception as e:
            logger.exception("Error deleting project")
            return Response(
                {"error": f"Failed to delete project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(["GET"])
@permission_classes([AllowAny])
def team_members(request):
    """Get all team members for a company"""
    # Get company from authenticated user if available, otherwise fallback to query param
    company = None
    
    # Try to get user from token first, then fallback to request.user
    user = get_user_from_token(request) or request.user
    
    if user and user.is_authenticated:
        company = user.company
        if not company:
            return Response({"error": "User has no associated company"}, status=400)
    else:
        # Fallback for unauthenticated requests (development/testing)
        company_id = request.GET.get("company_id")
        if not company_id:
            return Response({"error": "company_id required for unauthenticated requests"}, status=400)
        
        company = get_company(company_id)
        if not company:
            return Response({"error": "Company not found"}, status=404)
    
    try:
        members = User.objects.filter(company=company)
        serializer = TeamMemberSerializer(members, many=True)
        return Response({"data": serializer.data, "success": True})
    except Exception as e:
        logger.exception("Error fetching team members")
        return Response(
            {"error": f"Failed to fetch team members: {str(e)}"},
            status=500
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def team_member_detail(request, member_id):
    """Get a specific team member"""
    company_id = request.GET.get("company_id")
    if not company_id:
        return Response({"error": "company_id required"}, status=400)
    
    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)
    
    try:
        member = get_object_or_404(User, id=member_id, company=company)
        serializer = TeamMemberSerializer(member)
        return Response({"data": serializer.data, "success": True})
    except Exception as e:
        logger.exception("Error fetching team member")
        return Response(
            {"error": f"Failed to fetch team member: {str(e)}"},
            status=500
        )


class ProjectContributorsAPIView(APIView):
    """API for managing project contributors"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def get(self, request, project_id):
        """Get project contributors"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=user.company
            )
            
            time_range = request.query_params.get('timeRange')
            contributors = ProjectContributors.objects.filter(project=project)
            
            # Apply time range filter if provided
            if time_range:
                cutoff_date = ProjectsAPIView()._get_cutoff_date(time_range)
                if cutoff_date:
                    contributors = contributors.filter(last_contribution__gte=cutoff_date)
            
            contributors = contributors.select_related('user').order_by('-contributions')
            serializer = ProjectContributorSerializer(contributors, many=True)
            return Response({"data": serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error fetching contributors")
            return Response(
                {"error": f"Failed to fetch contributors: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProjectContributorDetailAPIView(APIView):
    """API for getting specific contributor activity"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def get(self, request, project_id, contributor_id):
        """Get specific contributor activity"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=user.company
            )
            
            contributor = get_object_or_404(
                ProjectContributors,
                project=project,
                user_id=contributor_id
            )
            
            time_range = request.query_params.get('timeRange')
            if time_range:
                cutoff_date = ProjectsAPIView()._get_cutoff_date(time_range)
                if cutoff_date and contributor.last_contribution < cutoff_date:
                    return Response(
                        {"error": "No activity in specified time range"},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            serializer = ContributorDetailSerializer(contributor, context={'project': project})
            return Response({"data": serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error fetching contributor details")
            return Response(
                {"error": f"Failed to fetch contributor details: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProjectMetricsAPIView(APIView):
    """API for managing project metrics"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def get(self, request, project_id):
        """Get project metrics"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=user.company
            )
            
            metrics, created = ProjectMetrics.objects.get_or_create(project=project)
            serializer = ProjectMetricsSerializer(metrics)
            return Response({"data": serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error fetching project metrics")
            return Response(
                {"error": f"Failed to fetch project metrics: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AllProjectMetricsAPIView(APIView):
    """API for getting all project metrics"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def get(self, request):
        """Get all project metrics"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            if not hasattr(user, 'company') or not user.company:
                return Response(
                    {"error": "User must belong to a company"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            metrics = ProjectMetrics.objects.filter(
                project__company=user.company
            ).select_related('project')
            
            serializer = ProjectMetricsSerializer(metrics, many=True)
            return Response({"data": serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error fetching all project metrics")
            return Response(
                {"error": f"Failed to fetch project metrics: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProjectSearchAPIView(APIView):
    """API for searching projects"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def get(self, request):
        """Search projects with filters"""
        try:
            # Validate search parameters
            search_serializer = ProjectSearchSerializer(data=request.query_params)
            if not search_serializer.is_valid():
                return Response(search_serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = search_serializer.validated_data
            
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            if not hasattr(user, 'company') or not user.company:
                return Response(
                    {"error": "User must belong to a company"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Start with company projects
            projects = Project.objects.filter(company=user.company)
            
            # Apply search query
            if validated_data.get('q'):
                query = validated_data['q']
                projects = projects.filter(
                    Q(name__icontains=query) |
                    Q(description__icontains=query) |
                    Q(categories__icontains=query) |
                    Q(tags__icontains=query)
                )
            
            # Apply category filter
            if validated_data.get('categories'):
                categories = [cat.strip() for cat in validated_data['categories'].split(',')]
                category_filter = Q()
                for category in categories:
                    category_filter |= Q(categories__icontains=category)
                projects = projects.filter(category_filter)
            
            # Apply tags filter
            if validated_data.get('tags'):
                tags = [tag.strip() for tag in validated_data['tags'].split(',')]
                tag_filter = Q()
                for tag in tags:
                    tag_filter |= Q(tags__icontains=tag)
                projects = projects.filter(tag_filter)
            
            # Filter by contributors if specified
            if validated_data.get('contributors'):
                contributor_ids = [cid.strip() for cid in validated_data['contributors'].split(',')]
                # Since we now store contributors as JSON, we need to check the JSON field
                from django.db.models import Q
                contributor_filters = Q()
                for contributor_id in contributor_ids:
                    contributor_filters |= Q(contributors_data__contains=[{"user_id": str(contributor_id)}])
                projects = projects.filter(contributor_filters).distinct()
            
            # Apply time range filter
            if validated_data.get('timeRange'):
                cutoff_date = ProjectsAPIView()._get_cutoff_date(validated_data['timeRange'])
                if cutoff_date:
                    projects = projects.filter(last_activity__gte=cutoff_date)
            
            # Apply pagination
            paginator = Paginator(projects, validated_data['limit'])
            page = paginator.get_page(validated_data['page'])
            
            # Serialize results
            projects = projects.select_related('doc_responsible', 'secondary_responsible')
            
            serializer = ProjectSerializer(page.object_list, many=True)
            
            return Response({
                "data": serializer.data,
                "pagination": {
                    "page": page.number,
                    "limit": validated_data['limit'],
                    "total": paginator.count,
                    "pages": paginator.num_pages
                },
                "success": True
            })
            
        except Exception as e:
            logger.exception("Error searching projects")
            return Response(
                {"error": f"Failed to search projects: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DocumentationAssignmentAPIView(APIView):
    """API for assigning documentation responsibility"""
    permission_classes = [AllowAny]  # Allow any for now, but we'll handle auth manually
    
    def post(self, request, project_id):
        """Assign documentation responsibility"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=user.company
            )
            
            serializer = DocumentationAssignmentSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            member_email = serializer.validated_data['memberEmail']
            assignment_type = serializer.validated_data['type']
            
            # Handle member assignment or removal
            member = None
            if member_email:
                # Verify member belongs to same company
                member = get_object_or_404(
                    User,
                    email=member_email
                )
            
            # Assign or remove responsibility
            if assignment_type == 'main':
                project.doc_responsible = member
            else:  # secondary
                project.secondary_responsible = member
            
            project.save()
            
            response_serializer = ProjectSerializer(project)
            return Response({"data": response_serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error assigning documentation responsibility")
            return Response(
                {"error": f"Failed to assign documentation responsibility: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProjectCRUDView(APIView):
    """
    Unified API for all project CRUD operations
    
    This single endpoint replaces multiple separate endpoints for project management:
    
    GET /api/projects/                    - Get all projects (with optional timeRange filter)
    GET /api/projects/{project_id}/       - Get specific project by ID
    POST /api/projects/                   - Create new project
    PUT /api/projects/{project_id}/       - Update existing project
    DELETE /api/projects/{project_id}/    - Delete project
    
    Usage Examples:
    - GET /api/projects/?timeRange=1month     - Get projects active in last month
    - GET /api/projects/123e4567-e89b-12d3-a456-426614174000/  - Get specific project
    - POST /api/projects/                     - Create new project (requires auth)
    - PUT /api/projects/123e4567-e89b-12d3-a456-426614174000/  - Update project (requires auth)
    - DELETE /api/projects/123e4567-e89b-12d3-a456-426614174000/ - Delete project (requires auth)
    
    Authentication: Required for POST, PUT, DELETE operations
    Company Scope: All operations are scoped to the authenticated user's company
    """
    permission_classes = [AllowAny]  # Keep AllowAny for now, but we'll get company from user if authenticated
    
    def get(self, request, project_id=None):
        """Get all projects or a specific project by ID"""
        try:
            # Get company from authenticated user if available, otherwise fallback to query param
            company = None
            
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated (handle None case)
            if user and user.is_authenticated:
                # User is authenticated, get their company
                company = user.company
                if not company:
                    return Response({"error": "User has no associated company"}, status=400)
            
            # Filter projects by user's company
            projects = Project.objects.filter(company=company)
            
            # If project_id is provided, return specific project
            if project_id:
                project = get_object_or_404(
                    projects.select_related('metrics'),
                    id=project_id
                )
                serializer = ProjectSerializer(project, context={
                    "time_range": request.query_params.get('timeRange')
                })
                return Response({"data": serializer.data, "success": True})
            
            # Otherwise return all projects with optional time range filter
            time_range = request.query_params.get('timeRange')
            
            # Apply time range filter if provided
            if time_range:
                cutoff_date = self._get_cutoff_date(time_range)
                if cutoff_date:
                    projects = projects.filter(last_activity__gte=cutoff_date)
            
            # Prefetch related data for performance
            projects = projects.select_related('metrics')
            
            serializer = ProjectSerializer(projects, many=True, context={
                "time_range": time_range
            })
            return Response({"data": serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error fetching projects")
            return Response(
                {"error": f"Failed to fetch projects: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        """Create a new project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Debug logging
            logger.info(f"POST /api/projects - User from token: {user}")
            logger.info(f"POST /api/projects - User authenticated: {user.is_authenticated if user else 'None'}")
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            serializer = ProjectCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            project = serializer.save(company=company)
            
            # Create default metrics
            ProjectMetrics.objects.create(project=project)
            
            response_serializer = ProjectSerializer(project)
            return Response({"data": response_serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error creating project")
            return Response(
                {"error": f"Failed to create project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request, project_id):
        """Update a project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=company
            )
            
            serializer = ProjectUpdateSerializer(project, data=request.data, partial=True)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            project = serializer.save()
            response_serializer = ProjectSerializer(project)
            return Response({"data": response_serializer.data, "success": True})
            
        except Exception as e:
            logger.exception("Error updating project")
            return Response(
                {"error": f"Failed to update project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def delete(self, request, project_id):
        """Delete a project"""
        try:
            # Try to get user from token first, then fallback to request.user
            user = get_user_from_token(request) or request.user
            
            # Check if user is authenticated
            if not user or not user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            
            company = user.company
            if not company:
                return Response({"error": "User has no associated company"}, status=400)
            
            project = get_object_or_404(
                Project,
                id=project_id,
                company=company
            )
            
            # Delete related objects first
            try:
                # Delete ProjectContributors
                ProjectContributors.objects.filter(project=project).delete()
                logger.info(f"Deleted ProjectContributors for project {project_id}")
            except Exception as e:
                logger.warning(f"Could not delete ProjectContributors for project {project_id}: {e}")
            
            try:
                # Delete ProjectMetrics
                ProjectMetrics.objects.filter(project=project).delete()
                logger.info(f"Deleted ProjectMetrics for project {project_id}")
            except Exception as e:
                logger.warning(f"Could not delete ProjectMetrics for project {project_id}: {e}")
            
            # Finally delete the project
            project.delete()
            return Response({"success": True, "message": "Project deleted successfully"})
            
        except Exception as e:
            logger.exception("Error deleting project")
            return Response(
                {"error": f"Failed to delete project: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_cutoff_date(self, time_range):
        """Get cutoff date based on time range"""
        now = timezone.now()
        if time_range == '1month':
            return now - timedelta(days=30)
        elif time_range == '3months':
            return now - timedelta(days=90)
        elif time_range == '6months':
            return now - timedelta(days=180)
        elif time_range == '1year':
            return now - timedelta(days=365)
        return None