from django.db import models
import uuid


class Project(models.Model):
    """Project model for team knowledge map"""
    
    class RepoType(models.TextChoices):
        GITHUB = 'github', 'GitHub'
        GITLAB = 'gitlab', 'GitLab'
        BITBUCKET = 'bitbucket', 'Bitbucket'
        OTHER = 'other', 'Other'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey('integrations.Company', on_delete=models.CASCADE, related_name='projects')
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255)
    description = models.TextField(blank=True, null=True)
    
    # Documentation responsibility
    doc_responsible = models.ForeignKey('integrations.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='primary_projects')
    secondary_responsible = models.ForeignKey('integrations.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='secondary_projects')
    
    # Repository information
    repo_path = models.URLField(blank=True, null=True)
    docs_path = models.URLField(blank=True, null=True)
    repo_type = models.CharField(max_length=20, choices=RepoType.choices, default=RepoType.GITHUB)
    
    # Categories for project classification
    categories = models.JSONField(default=list, blank=True)
    
    # Tags for flexible labeling
    tags = models.JSONField(default=list, blank=True)
    
    # Activity tracking
    last_activity = models.DateTimeField(auto_now=True)
    total_contributors = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['company', 'slug']
        ordering = ['-last_activity']
    
    def __str__(self):
        return f"{self.name} ({self.company.name})"
    
