from django.db import models
import uuid


class Project(models.Model):
    """Project model for team dashboard, independent from knowledge spaces."""

    class RepoProvider(models.TextChoices):
        GITHUB = 'github', 'GitHub'
        GITLAB = 'gitlab', 'GitLab'
        BITBUCKET = 'bitbucket', 'Bitbucket'
        OTHER = 'other', 'Other'

    class Status(models.TextChoices):
        ACTIVE = 'active', 'Active'
        PAUSED = 'paused', 'Paused'
        COMPLETED = 'completed', 'Completed'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey('integrations.Company', on_delete=models.CASCADE, related_name='projects')
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255)
    description = models.TextField(blank=True, null=True)

    # Repository information
    repository_url = models.URLField(blank=True, null=True, help_text="Full repository URL")
    documentation_url = models.URLField(blank=True, null=True, help_text="Full documentation URL")
    repository_full_name = models.CharField(max_length=255, blank=True, null=True, help_text="owner/repo for GitHub or similar")
    repository_id = models.CharField(max_length=255, blank=True, null=True)
    repository_provider = models.CharField(max_length=20, choices=RepoProvider.choices, default=RepoProvider.GITHUB)

    # Project status
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.ACTIVE)

    # Optional categorization
    categories = models.JSONField(default=list, blank=True)
    tags = models.JSONField(default=list, blank=True)

    # Activity tracking
    last_activity = models.DateTimeField(auto_now=True)
    total_contributors = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['company', 'slug']
        ordering = ['-last_activity']

    def __str__(self):
        return f"{self.name} ({self.company.name})"


class ProjectContributors(models.Model):
    """
    Project contributors model for storing aggregated contributor data as JSON
    
    contributors_data structure example:
    [
        {
            "github_username": "john_doe",
            "name": "John Doe",
            "commits": 45,
            "linesOfCode": 0,
            "pullRequests": 12,
            "docContributions": 3,
            "contributions": 60,
            "last_contribution": "2024-01-15T10:30:00Z",
            "email": "",
            "role": ""
        }
    ]
    
    contributions_history structure for time-based filtering:
    {
        "commits": [
            {
                "author": "john_doe",
                "sha": "abc123",
                "date": "2024-01-15T10:30:00Z",
                "message": "Update feature",
                "lines_added": 50,
                "lines_deleted": 10
            }
        ],
        "pull_requests": [
            {
                "author": "john_doe",
                "number": 123,
                "date": "2024-01-14T15:45:00Z",
                "state": "merged",
                "title": "Add new feature",
                "lines_added": 100,
                "lines_deleted": 20
            }
        ]
    }
    
    This approach avoids creating database objects for external GitHub users
    while still providing fast access to contributor data and detailed history for filtering.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='contributors_data')
    
    # Store all contributor data as JSON to avoid database bloat
    contributors_data = models.JSONField(
        default=list,
        help_text="List of contributor data from GitHub/GitLab API"
    )
    
    # Store detailed contribution history with timestamps for time-based filtering
    contributions_history = models.JSONField(
        default=dict,
        help_text="Detailed commit and PR history with timestamps for filtering"
    )
    
    # Last time data was fetched from remote API
    last_fetched = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['project']  # One record per project
        ordering = ['-last_fetched']
    
    def __str__(self):
        return f"Contributors for {self.project.name} ({len(self.contributors_data)} contributors)"
    
    @property
    def total_contributors(self):
        """Get total number of contributors"""
        return len(self.contributors_data)
    
    @property
    def total_contributions(self):
        """Get total contributions across all contributors"""
        return sum(contributor.get('contributions', 0) for contributor in self.contributors_data)
    
    def get_top_contributors(self, limit=5):
        """Get top contributors sorted by contributions"""
        sorted_contributors = sorted(
            self.contributors_data, 
            key=lambda x: x.get('contributions', 0), 
            reverse=True
        )
        return sorted_contributors[:limit]
    
    def update_contributors_data(self, new_data, new_history=None):
        """Update contributors data and history from API fetch"""
        self.contributors_data = new_data
        if new_history:
            self.contributions_history = new_history
        # Force update of last_fetched timestamp
        from django.utils import timezone
        self.last_fetched = timezone.now()
        self.save(update_fields=['contributors_data', 'contributions_history', 'last_fetched'])


class ProjectMetrics(models.Model):
    """Project metrics model for tracking project health"""
    
    class RiskLevel(models.TextChoices):
        LOW = 'Low', 'Low'
        MEDIUM = 'Medium', 'Medium'
        HIGH = 'High', 'High'
        CRITICAL = 'Critical', 'Critical'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.OneToOneField(Project, on_delete=models.CASCADE, related_name='metrics')
    
    # Health metrics
    health_score = models.IntegerField(default=0)  # 0-100
    risk_level = models.CharField(max_length=20, choices=RiskLevel.choices, default=RiskLevel.LOW)
    documentation_coverage = models.IntegerField(default=0)  # 0-100
    active_contributors = models.IntegerField(default=0)
    
    # Timestamps
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-last_updated']
    
    def __str__(self):
        return f"{self.project.name} metrics (Health: {self.health_score}%, Risk: {self.risk_level})"
