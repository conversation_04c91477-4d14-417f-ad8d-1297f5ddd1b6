from django.urls import path
from . import views
from . import project_members_views


urlpatterns = [
    # Unified Projects API - Single endpoint for all CRUD operations
    path('projects/', views.ProjectCRUDView.as_view(), name='projects-crud'), # get details  about contributors, metrics, etc.
    path('projects/<uuid:project_id>/', views.ProjectCRUDView.as_view(), name='projects-crud'),
    
    # Specialized project operations
    path('projects/<uuid:project_id>/assign-documentation/', views.DocumentationAssignmentAPIView.as_view(), name='assign-documentation'),
    
    # Team Members API
    path('team-members/', views.team_members, name='team-members-list'),
    path('team-members/<uuid:member_id>/', views.team_member_detail, name='team-member-detail'),
    
    # Contributors API
    path('projects/<uuid:project_id>/contributors/', views.ProjectContributorsAPIView.as_view(), name='project-contributors'),
    path('projects/<uuid:project_id>/contributors/<uuid:contributor_id>/', views.ProjectContributorDetailAPIView.as_view(), name='project-contributor-detail'),
    
    # Metrics API
    path('projects/<uuid:project_id>/metrics/', views.ProjectMetricsAPIView.as_view(), name='project-metrics'),
    path('projects/metrics/', views.AllProjectMetricsAPIView.as_view(), name='all-project-metrics'),
    
    # Search API
    path('projects/search/', views.ProjectSearchAPIView.as_view(), name='project-search'),
    
    # Specialized project operations (only the ones that are actually different)
    path('projects/<uuid:project_id>/recalculate/', project_members_views.recalculate_project_metrics, name='recalculate-project-metrics'),
    path('projects/recalculate-all/', project_members_views.recalculate_project_metrics, name='recalculate-all-projects'),
]