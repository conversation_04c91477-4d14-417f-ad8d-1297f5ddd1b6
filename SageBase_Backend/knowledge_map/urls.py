from django.urls import path
from . import views

urlpatterns = [
    # Projects API
    path('projects/', views.ProjectsAPIView.as_view(), name='projects-list'),
    path('projects/<uuid:project_id>/', views.ProjectDetailAPIView.as_view(), name='project-detail'),
    path('projects/<uuid:project_id>/assign-documentation/', views.DocumentationAssignmentAPIView.as_view(), name='assign-documentation'),
    
    # Team Members API
    path('team-members/', views.team_members, name='team-members-list'),
    path('team-members/<uuid:member_id>/', views.team_member_detail, name='team-member-detail'),
    
    # Contributors API
    path('projects/<uuid:project_id>/contributors/', views.ProjectContributorsAPIView.as_view(), name='project-contributors'),
    path('projects/<uuid:project_id>/contributors/<uuid:contributor_id>/', views.ProjectContributorDetailAPIView.as_view(), name='project-contributor-detail'),
    
    # Metrics API
    path('projects/<uuid:project_id>/metrics/', views.ProjectMetricsAPIView.as_view(), name='project-metrics'),
    path('projects/metrics/', views.AllProjectMetricsAPIView.as_view(), name='all-project-metrics'),
    
    # Search API
    path('projects/search/', views.ProjectSearchAPIView.as_view(), name='project-search'),
]