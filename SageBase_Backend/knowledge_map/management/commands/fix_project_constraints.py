from django.core.management.base import BaseCommand
from django.db import connection, transaction
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix foreign key constraints by cleaning up old integrations_projectmetrics data'

    def handle(self, *args, **options):
        with connection.cursor() as cursor:
            try:
                # Check if integrations_projectmetrics table exists
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'integrations_projectmetrics'
                    )
                """)
                
                table_exists = cursor.fetchone()[0]
                
                if table_exists:
                    # Check how many records are in the old table
                    cursor.execute("SELECT COUNT(*) FROM integrations_projectmetrics")
                    count = cursor.fetchone()[0]
                    
                    self.stdout.write(f"Found {count} records in integrations_projectmetrics")
                    
                    if count > 0:
                        # Delete all records from the old table
                        with transaction.atomic():
                            cursor.execute("DELETE FROM integrations_projectmetrics")
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"Deleted {count} records from integrations_projectmetrics"
                                )
                            )
                    
                    # Drop the old table completely
                    cursor.execute("DROP TABLE IF EXISTS integrations_projectmetrics CASCADE")
                    self.stdout.write(
                        self.style.SUCCESS("Dropped integrations_projectmetrics table")
                    )
                    
                    # Check if integrations_projectcontributors exists and drop it too
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' 
                            AND table_name = 'integrations_projectcontributors'
                        )
                    """)
                    
                    contrib_table_exists = cursor.fetchone()[0]
                    if contrib_table_exists:
                        cursor.execute("DROP TABLE IF EXISTS integrations_projectcontributors CASCADE")
                        self.stdout.write(
                            self.style.SUCCESS("Dropped integrations_projectcontributors table")
                        )
                
                else:
                    self.stdout.write("integrations_projectmetrics table does not exist")
                
                # Check remaining foreign key constraints
                cursor.execute("""
                    SELECT 
                        tc.constraint_name,
                        tc.table_name,
                        kcu.column_name,
                        ccu.table_name AS referenced_table,
                        ccu.column_name AS referenced_column
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage ccu 
                        ON ccu.constraint_name = tc.constraint_name
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND ccu.table_name = 'knowledge_map_project'
                    AND tc.table_name LIKE 'integrations_%'
                """)
                
                remaining_constraints = cursor.fetchall()
                if remaining_constraints:
                    self.stdout.write("Remaining foreign key constraints from integrations:")
                    for constraint in remaining_constraints:
                        self.stdout.write(f"  - {constraint[1]}.{constraint[2]} -> {constraint[3]}.{constraint[4]}")
                else:
                    self.stdout.write(
                        self.style.SUCCESS("No remaining foreign key constraints from integrations tables")
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error fixing constraints: {e}")
                )
                raise
