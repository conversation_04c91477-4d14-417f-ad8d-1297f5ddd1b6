from django.core.management.base import BaseCommand
from django.db import connection, transaction
from knowledge_map.models import Project, ProjectContributors, ProjectMetrics

class Command(BaseCommand):
    help = 'Clear all projects and fix foreign key constraints by dropping old integrations tables'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force deletion without confirmation',
        )

    def handle(self, *args, **options):
        if not options['force']:
            confirm = input("This will delete ALL projects and related data. Are you sure? (yes/no): ")
            if confirm.lower() != 'yes':
                self.stdout.write("Operation cancelled.")
                return

        with connection.cursor() as cursor:
            try:
                self.stdout.write("Checking for old integrations tables...")
                
                # Check if integrations_projectmetrics exists and drop it
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'integrations_projectmetrics'
                    )
                """)
                
                if cursor.fetchone()[0]:
                    self.stdout.write("Found integrations_projectmetrics table - dropping it...")
                    cursor.execute("DROP TABLE IF EXISTS integrations_projectmetrics CASCADE")
                    self.stdout.write(
                        self.style.SUCCESS("✓ Dropped integrations_projectmetrics")
                    )
                
                # Check if integrations_projectcontributors exists and drop it
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'integrations_projectcontributors'
                    )
                """)
                
                if cursor.fetchone()[0]:
                    self.stdout.write("Found integrations_projectcontributors table - dropping it...")
                    cursor.execute("DROP TABLE IF EXISTS integrations_projectcontributors CASCADE")
                    self.stdout.write(
                        self.style.SUCCESS("✓ Dropped integrations_projectcontributors")
                    )
                
                # Now clear the projects using Django ORM
                self.stdout.write("Clearing projects using Django ORM...")
                
                with transaction.atomic():
                    # Delete in correct order to respect foreign keys
                    ProjectContributors.objects.all().delete()
                    ProjectMetrics.objects.all().delete()
                    Project.objects.all().delete()
                
                self.stdout.write(
                    self.style.SUCCESS("✅ All projects and related data cleared successfully!")
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Error: {e}")
                )
                raise
