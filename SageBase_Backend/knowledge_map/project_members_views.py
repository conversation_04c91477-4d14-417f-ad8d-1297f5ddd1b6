"""
Project Members Data API Views
Provides endpoints for team dashboard using Django ORM models

TODO: Add JWT authentication middleware for production use.
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import get_object_or_404
from django.db.models import Count, Sum, Q
from django.utils import timezone
import json
from datetime import datetime, timedelta
import time

# Import existing models
from knowledge_map.models import Project, ProjectContributors
import logging
import requests

logger = logging.getLogger(__name__)


def serialize_team_member(contributor_data, team_totals=None):
    """Serialize a contributor from JSON data to team member dict"""
    if not contributor_data:
        return None
    
    # Extract data from JSON structure
    github_username = contributor_data.get('github_username', 'Unknown')
    name = contributor_data.get('name', github_username)
    commits = contributor_data.get('commits', 0)
    reviews = contributor_data.get('reviews', 0)
    docs = contributor_data.get('docs', 0)
    contributions = contributor_data.get('contributions', 0)
    last_contribution = contributor_data.get('last_contribution')
    
    # Extract line change data
    additions = contributor_data.get('additions', 0)
    deletions = contributor_data.get('deletions', 0)
    edited_lines = contributor_data.get('edited_lines', 0)
    net_change = contributor_data.get('net_change', 0)
    
    # Calculate intelligent contribution score based on team comparison
    if team_totals and any(team_totals.values()):
        # Calculate relative contribution percentages
        commit_percentage = (commits / max(team_totals['total_commits'], 1)) * 100
        lines_percentage = (edited_lines / max(team_totals['total_lines'], 1)) * 100
        pr_percentage = (reviews / max(team_totals['total_prs'], 1)) * 100
        docs_percentage = (docs / max(team_totals['total_docs'], 1)) * 100
        
        # Weighted score: lines of code (40%), commits (30%), PRs (20%), docs (10%)
        weighted_score = (
            (lines_percentage * 0.4) +
            (commit_percentage * 0.3) +
            (pr_percentage * 0.3) 
            #(docs_percentage * 0.1) # TODO : add docs back in later when we have a way to track it
        )
        
        # Normalize to 0-100 scale
        contribution_score = min(100, max(0, int(weighted_score)))
    else:
        contribution_score = 0

    
    # Generate avatar from name initials
    name_parts = name.split()
    avatar = ''.join([part[0].upper() for part in name_parts[:2]]) if name_parts else 'UN'
    
    return {
        "id": github_username,  # Use GitHub username as ID
        "name": name,
        "avatar": avatar,
        "commits": commits,
        "linesOfCode": edited_lines,  # Total lines edited (additions + deletions)
        "additions": additions,       # New lines added
        "deletions": deletions,      # Lines deleted
        "netChange": net_change,     # Net change (additions - deletions)
        "pullRequests": reviews,
        "docContributions": docs,
        "role": "Contributor",  # Default role for external contributors
        "contributionScore": contribution_score,
        "email": f"{github_username}@github.com",  # Placeholder email
        "lastActive": last_contribution if last_contribution else datetime.now().isoformat()
    }


def serialize_project(project, time_range=None):
    """Serialize a Project instance to project dict"""
    # Get project contributors from the new JSON structure
    try:
        contributors_record = ProjectContributors.objects.filter(project=project).first()
        if contributors_record:
            contributors_data = contributors_record.contributors_data
        else:
            contributors_data = []
    except:
        contributors_data = []
    
    # Apply time range filtering if specified
    if time_range and contributors_data:
        time_filter = get_time_filter(time_range)
        if time_filter:
            # Filter contributors by last_contribution date
            filtered_contributors = []
            for contributor in contributors_data:
                last_contribution = contributor.get('last_contribution')
                if last_contribution:
                    try:
                        contributor_date = datetime.fromisoformat(last_contribution.replace('Z', '+00:00'))
                        if contributor_date >= time_filter:
                            filtered_contributors.append(contributor)
                    except:
                        filtered_contributors.append(contributor)
                else:
                    filtered_contributors.append(contributor)
            contributors_data = filtered_contributors
    
    # Calculate team totals first (before applying time multipliers)
    raw_team_totals = {
        'total_commits': sum(contributor.get('commits', 0) for contributor in contributors_data),
        'total_lines': sum(contributor.get('edited_lines', 0) for contributor in contributors_data),
        'total_prs': sum(contributor.get('reviews', 0) for contributor in contributors_data),
        'total_docs': sum(contributor.get('docs', 0) for contributor in contributors_data)
    }
    
    # Serialize team data with team totals for proper scoring
    team_data = []
    for contributor in contributors_data:
        member_data = serialize_team_member(contributor, raw_team_totals)
        if member_data:
            # Apply time range multipliers for simulation
            if time_range:
                member_data = apply_time_multipliers(member_data, time_range)
            team_data.append(member_data)
    
    # Calculate final totals (after time multipliers)
    total_commits = sum(member['commits'] for member in team_data)
    total_lines_of_code = sum(member['linesOfCode'] for member in team_data)
    total_pull_requests = sum(member['pullRequests'] for member in team_data)
    total_doc_contributions = sum(member['docContributions'] for member in team_data)
    
    # Determine project status based on activity
    if project.last_activity:
        days_since_activity = (datetime.now(project.last_activity.tzinfo) - project.last_activity).days
        if days_since_activity > 60:
            status = "On Hold"
        elif days_since_activity > 30:
            status = "Planning"
        elif total_commits > 100:
            status = "Completed"
        else:
            status = "Active"
    else:
        status = "Planning"
    
    return {
        "id": str(project.id),
        "name": project.name,
        "description": project.description or f"Project {project.name}",
        "status": status,
        "repositoryUrl": project.repository_url or f"https://github.com/company/{project.slug}",
        "documentationUrl": project.documentation_url or f"https://docs.company.com/{project.slug}",
        "lastUpdated": project.last_activity.isoformat() if project.last_activity else datetime.now().isoformat(),
        "totalCommits": total_commits,
        "totalLinesOfCode": total_lines_of_code,
        "totalPullRequests": total_pull_requests,
        "totalDocContributions": total_doc_contributions,
        "teamData": team_data
    }


def get_time_filter(time_range):
    """Get datetime filter for time range"""
    now = datetime.now()
    if time_range == "last-7-days":
        return now - timedelta(days=7)
    elif time_range == "last-30-days":
        return now - timedelta(days=30)
    elif time_range == "last-3-months":
        return now - timedelta(days=90)
    elif time_range == "last-6-months":
        return now - timedelta(days=180)
    return None


def apply_time_multipliers(member_data, time_range):
    """Apply time range multipliers to simulate different time periods"""
    multipliers = {
        "last-7-days": 0.2,
        "last-30-days": 1.0,
        "last-3-months": 2.5,
        "last-6-months": 4.0
    }
    
    multiplier = multipliers.get(time_range, 1.0)
    if multiplier != 1.0:
        member_data = member_data.copy()
        member_data["commits"] = int(member_data["commits"] * multiplier)
        member_data["linesOfCode"] = int(member_data["linesOfCode"] * multiplier)
        member_data["pullRequests"] = int(member_data["pullRequests"] * multiplier)
        member_data["docContributions"] = int(member_data["docContributions"] * multiplier)
        member_data["contributionScore"] = min(100, int(member_data["contributionScore"] * (0.8 if multiplier < 1 else 1.1)))
    
    return member_data


@require_http_methods(["GET"])
def get_all_projects(request):
    """Get all projects with optional time range filtering"""
    try:
        time_range = request.GET.get('timeRange', 'last-30-days')
        
        # Query all projects with their contributors
        projects = Project.objects.all()
        
        # Serialize projects
        project_data = []
        for project in projects:
            #get stats like total commits, total lines of code, total pull requests, total doc contributions
            serialized = serialize_project(project, time_range)
            project_data.append(serialized)
        
        return JsonResponse({
            "success": True,
            "data": project_data
        })
    
    except Exception as e:
        return JsonResponse({
            "success": False,
            "message": "Failed to fetch projects",
            "errorCode": "FETCH_ERROR",
            "timestamp": datetime.now().isoformat()
        }, status=500)


@require_http_methods(["GET"])
def get_project_details(request, project_id):
    """Get specific project details"""
    try:
        time_range = request.GET.get('timeRange', 'last-30-days')
        
        # Get project by ID (UUID or string)
        try:
            project = get_object_or_404(
                Project.objects.prefetch_related('contributors__user'),
                id=project_id
            )
        except ValueError:
            # Handle case where project_id is not a valid UUID
            project = get_object_or_404(
                Project.objects.prefetch_related('contributors__user'),
                slug=project_id
            )
        
        # Serialize project
        project_data = serialize_project(project, time_range)
        
        return JsonResponse({
            "success": True,
            "data": project_data
        })
    
    except Project.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Project not found",
            "errorCode": "PROJECT_NOT_FOUND",
            "timestamp": datetime.now().isoformat()
        }, status=404)
    except Exception as e:
        return JsonResponse({
            "success": False,
            "message": "Failed to fetch project details",
            "errorCode": "FETCH_ERROR",
            "timestamp": datetime.now().isoformat()
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def recalculate_project_metrics(request, project_id=None):
    """Recalculate project metrics and fetch fresh GitHub contributor data for all projects or a specific project"""
    try:
        # Parse request body
        try:
            body = json.loads(request.body)
            time_range = body.get('timeRange', 'last-30-days')
        except json.JSONDecodeError:
            time_range = 'last-30-days'
        
        # Convert time range to days
        since_days = 30  # Default to 30 days
        if time_range == 'last-7-days':
            since_days = 7
        elif time_range == 'last-30-days':
            since_days = 30
        elif time_range == 'last-90-days':
            since_days = 90
        elif time_range == 'last-180-days':
            since_days = 180
        
        # Import the GitHub stats API
        try:
            from integrations.management.commands.fetch_project_contributors import update_project_contributors
        except ImportError:
            logger.error("Could not import update_project_contributors function")
            return JsonResponse({
                "success": False,
                "message": "GitHub integration not available",
                "errorCode": "INTEGRATION_UNAVAILABLE",
                "timestamp": datetime.now().isoformat()
            }, status=500)
        
        # If specific project ID provided, update only that project
        if project_id:
            try:
                project = get_object_or_404(Project, id=project_id)
            except ValueError:
                project = get_object_or_404(Project, slug=project_id)
            
            projects_to_update = [project]
            message = f"Project '{project.name}' metrics recalculated successfully for {time_range}"
        else:
            # Update all projects with GitHub repositories
            projects_to_update = Project.objects.filter(
                repository_url__isnull=False
            ).exclude(repository_url='')
            message = f"All project metrics recalculated successfully for {time_range}"
        
        # Update each project
        updated_projects = []
        failed_projects = []
        
        for project in projects_to_update:
            try:
                # Fetch fresh contributor data from GitHub
                contributors_data = update_project_contributors(project, since_days)
                
                if contributors_data:
                    # Update project activity timestamp
                    project.last_activity = timezone.now()
                    project.save(update_fields=['last_activity'])
                    
                    updated_projects.append({
                        'id': str(project.id),
                        'name': project.name,
                        'contributors_count': len(contributors_data),
                        'status': 'success'
                    })
                    logger.info(f"Successfully updated contributors for project {project.id} with {len(contributors_data)} contributors")
                else:
                    failed_projects.append({
                        'id': str(project.id),
                        'name': project.name,
                        'status': 'no_data',
                        'message': 'No contributor data fetched'
                    })
                    logger.warning(f"No contributor data fetched for project {project.id}")
                    
            except Exception as e:
                failed_projects.append({
                    'id': str(project.id),
                    'name': project.name,
                    'status': 'error',
                    'message': str(e)
                })
                logger.error(f"Error updating project {project.id}: {e}")
        
        # Simulate processing time
        time.sleep(1)
        
        return JsonResponse({
            "success": True,
            "message": message,
            "data": {
                "time_range": time_range,
                "since_days": since_days,
                "total_projects": len(projects_to_update),
                "updated_projects": updated_projects,
                "failed_projects": failed_projects,
                "last_updated": timezone.now().isoformat()
            }
        })
        
    except Project.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Project not found",
            "errorCode": "PROJECT_NOT_FOUND",
            "timestamp": datetime.now().isoformat()
        }, status=404)
    except Exception as e:
        logger.exception("Error recalculating project metrics")
        return JsonResponse({
            "success": False,
            "message": "Failed to recalculate project metrics",
            "errorCode": "RECALCULATION_ERROR",
            "timestamp": datetime.now().isoformat()
        }, status=500)