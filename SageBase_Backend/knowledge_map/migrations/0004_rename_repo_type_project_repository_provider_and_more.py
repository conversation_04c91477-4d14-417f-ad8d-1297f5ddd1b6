# Generated by Django 4.2 on 2025-08-22 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_map", "0003_update_project_user_foreign_keys"),
    ]

    operations = [
        migrations.RenameField(
            model_name="project",
            old_name="repo_type",
            new_name="repository_provider",
        ),
        migrations.RemoveField(
            model_name="project",
            name="doc_responsible",
        ),
        migrations.RemoveField(
            model_name="project",
            name="docs_path",
        ),
        migrations.RemoveField(
            model_name="project",
            name="repo_path",
        ),
        migrations.RemoveField(
            model_name="project",
            name="secondary_responsible",
        ),
        migrations.AddField(
            model_name="project",
            name="documentation_url",
            field=models.URLField(
                blank=True, help_text="Full documentation URL", null=True
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="repository_full_name",
            field=models.CharField(
                blank=True,
                help_text="owner/repo for GitHub or similar",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="repository_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="project",
            name="repository_url",
            field=models.URLField(
                blank=True, help_text="Full repository URL", null=True
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("paused", "Paused"),
                    ("completed", "Completed"),
                ],
                default="active",
                max_length=20,
            ),
        ),
    ]
