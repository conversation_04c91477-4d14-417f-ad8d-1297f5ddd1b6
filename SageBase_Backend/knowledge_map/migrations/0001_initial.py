# Generated by Django 5.2.4 on 2025-07-27 08:28

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('integrations', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('repo_path', models.URLField(blank=True, null=True)),
                ('docs_path', models.URLField(blank=True, null=True)),
                ('repo_type', models.CharField(choices=[('github', 'GitHub'), ('gitlab', 'GitLab'), ('bitbucket', 'Bitbucket'), ('other', 'Other')], default='github', max_length=20)),
                ('categories', models.J<PERSON><PERSON>ield(blank=True, default=list)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('total_contributors', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='integrations.company')),
                ('doc_responsible', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_projects', to=settings.AUTH_USER_MODEL)),
                ('secondary_responsible', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='secondary_projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_activity'],
                'unique_together': {('company', 'slug')},
            },
        ),
    ]
