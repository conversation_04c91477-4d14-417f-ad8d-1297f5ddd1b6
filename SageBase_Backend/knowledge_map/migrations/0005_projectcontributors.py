# Generated by Django 4.2 on 2025-08-22 15:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_map", "0004_rename_repo_type_project_repository_provider_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectContributors",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("contributors_data", models.JSONField(blank=True, default=list)),
                ("contributions_history", models.JSONField(blank=True, default=dict)),
                (
                    "last_fetched_repo",
                    models.URLField(blank=True, max_length=500, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contributors",
                        to="knowledge_map.project",
                    ),
                ),
            ],
            options={
                "verbose_name": "Project Contributors",
                "verbose_name_plural": "Project Contributors",
            },
        ),
    ]
