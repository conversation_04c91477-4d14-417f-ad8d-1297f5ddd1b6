# Generated by Django 4.2 on 2025-08-22 17:07

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("knowledge_map", "0006_delete_projectcontributors"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectMetrics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("health_score", models.IntegerField(default=0)),
                (
                    "risk_level",
                    models.CharField(
                        choices=[
                            ("Low", "Low"),
                            ("Medium", "Medium"),
                            ("High", "High"),
                            ("Critical", "Critical"),
                        ],
                        default="Low",
                        max_length=20,
                    ),
                ),
                ("documentation_coverage", models.IntegerField(default=0)),
                ("active_contributors", models.IntegerField(default=0)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "project",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="metrics",
                        to="knowledge_map.project",
                    ),
                ),
            ],
            options={
                "ordering": ["-last_updated"],
            },
        ),
        migrations.CreateModel(
            name="ProjectContributors",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "contributors_data",
                    models.JSONField(
                        default=list,
                        help_text="List of contributor data from GitHub/GitLab API",
                    ),
                ),
                (
                    "contributions_history",
                    models.JSONField(
                        default=dict,
                        help_text="Detailed commit and PR history with timestamps for filtering",
                    ),
                ),
                ("last_fetched", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contributors_data",
                        to="knowledge_map.project",
                    ),
                ),
            ],
            options={
                "ordering": ["-last_fetched"],
                "unique_together": {("project",)},
            },
        ),
    ]
