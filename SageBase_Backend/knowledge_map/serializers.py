from rest_framework import serializers
from integrations.models import User, ProjectContributor, ProjectMetrics
from knowledge_map.models import Project
from django.utils import timezone
from datetime import datetime, timedelta


class TeamMemberSerializer(serializers.ModelSerializer):
    """Serializer for team members"""
    
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'role']
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Format name for frontend
        data['name'] = f"{instance.first_name} {instance.last_name}".strip()
        # Add avatar placeholder
        data['avatar'] = f"https://ui-avatars.com/api/?name={data['name']}&background=random"
        return data


class ProjectContributorSerializer(serializers.ModelSerializer):
    """Serializer for project contributors"""
    name = serializers.SerializerMethodField()
    role = serializers.CharField(source='user.role', read_only=True)
    
    class Meta:
        model = ProjectContributor
        fields = ['name', 'role', 'contributions', 'commits', 'reviews', 'docs']
    
    def get_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip()


class ProjectMetricsSerializer(serializers.ModelSerializer):
    """Serializer for project metrics"""
    
    class Meta:
        model = ProjectMetrics
        fields = ['id', 'health_score', 'risk_level', 'last_updated', 'documentation_coverage', 'active_contributors']
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Format the project ID for consistency
        data['projectId'] = str(instance.project.id)
        return data


class ProjectSerializer(serializers.ModelSerializer):
    """Serializer for projects"""
    topContributors = ProjectContributorSerializer(source='contributors', many=True, read_only=True)
    docResponsible = serializers.CharField(source='doc_responsible_id', read_only=True)
    secondaryResponsible = serializers.CharField(source='secondary_responsible_id', read_only=True)
    lastActivity = serializers.SerializerMethodField()
    repoPath = serializers.URLField(source='repo_path', required=False, allow_blank=True)
    docsPath = serializers.URLField(source='docs_path', required=False, allow_blank=True)
    repoType = serializers.CharField(source='repo_type', required=False)
    totalContributors = serializers.IntegerField(source='total_contributors', read_only=True)
    
    class Meta:
        model = Project
        fields = [
            'id', 'name', 'description', 'lastActivity', 'totalContributors',
            'categories', 'tags', 'docResponsible', 'secondaryResponsible', 'repoPath',
            'docsPath', 'repoType', 'topContributors'
        ]
    
    def get_lastActivity(self, obj):
        """Format last activity as human-readable string"""
        if not obj.last_activity:
            return "Never"
        
        now = timezone.now()
        diff = now - obj.last_activity
        
        if diff.days == 0:
            if diff.seconds < 3600:
                return "Just now"
            elif diff.seconds < 86400:
                hours = diff.seconds // 3600
                return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.days == 1:
            return "1 day ago"
        elif diff.days < 7:
            return f"{diff.days} days ago"
        elif diff.days < 30:
            weeks = diff.days // 7
            return f"{weeks} week{'s' if weeks > 1 else ''} ago"
        elif diff.days < 365:
            months = diff.days // 30
            return f"{months} month{'s' if months > 1 else ''} ago"
        else:
            years = diff.days // 365
            return f"{years} year{'s' if years > 1 else ''} ago"
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Limit top contributors to 5
        if 'topContributors' in data and data['topContributors']:
            data['topContributors'] = data['topContributors'][:5]
        return data


class ProjectCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating projects"""
    repoPath = serializers.URLField(source='repo_path', required=False, allow_blank=True)
    docsPath = serializers.URLField(source='docs_path', required=False, allow_blank=True)
    repoType = serializers.CharField(source='repo_type', required=False)
    
    class Meta:
        model = Project
        fields = ['name', 'description', 'categories', 'tags', 'repoPath', 'docsPath', 'repoType']
    
    def create(self, validated_data):
        # Auto-generate slug from name
        from django.utils.text import slugify
        validated_data['slug'] = slugify(validated_data['name'])
        return super().create(validated_data)


class ProjectUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating projects"""
    repoPath = serializers.URLField(source='repo_path', required=False, allow_blank=True)
    docsPath = serializers.URLField(source='docs_path', required=False, allow_blank=True)
    repoType = serializers.CharField(source='repo_type', required=False)
    
    class Meta:
        model = Project
        fields = ['name', 'description', 'categories', 'tags', 'repoPath', 'docsPath', 'repoType']
    
    def update(self, instance, validated_data):
        # Update slug if name changes
        if 'name' in validated_data and validated_data['name'] != instance.name:
            from django.utils.text import slugify
            validated_data['slug'] = slugify(validated_data['name'])
        return super().update(instance, validated_data)


class DocumentationAssignmentSerializer(serializers.Serializer):
    """Serializer for assigning documentation responsibility"""
    memberEmail = serializers.EmailField(required=False, allow_null=True)
    type = serializers.ChoiceField(choices=['main', 'secondary'])
    
    def validate_memberEmail(self, value):
        """Validate that the member exists or is null for removal"""
        if value is None or value == "":
            return None
        try:
            User.objects.get(email=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")


class ProjectSearchSerializer(serializers.Serializer):
    """Serializer for project search parameters"""
    q = serializers.CharField(required=False, allow_blank=True)
    categories = serializers.CharField(required=False, allow_blank=True)
    tags = serializers.CharField(required=False, allow_blank=True)
    contributors = serializers.CharField(required=False, allow_blank=True)
    timeRange = serializers.CharField(required=False, allow_blank=True)
    page = serializers.IntegerField(default=1, min_value=1)
    limit = serializers.IntegerField(default=20, min_value=1, max_value=100)
    
    def validate_timeRange(self, value):
        """Validate time range"""
        if value and value not in ['1month', '3months', '6months', '1year']:
            raise serializers.ValidationError("Invalid time range")
        return value


class ContributorDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed contributor information"""
    name = serializers.SerializerMethodField()
    role = serializers.CharField(source='user.role', read_only=True)
    
    class Meta:
        model = ProjectContributor
        fields = ['name', 'role', 'contributions', 'commits', 'reviews', 'docs']
    
    def get_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip()