from rest_framework import serializers
from integrations.models import User
from .models import Project, ProjectContributors, ProjectMetrics
from django.utils import timezone
from datetime import datetime, timedelta
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class TeamMemberSerializer(serializers.ModelSerializer):
    """Serializer for team members"""
    
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'role']
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Format name for frontend
        data['name'] = f"{instance.first_name} {instance.last_name}".strip()
        # Add avatar placeholder
        data['avatar'] = f"https://ui-avatars.com/api/?name={data['name']}&background=random"
        return data


class ProjectContributorSerializer(serializers.ModelSerializer):
    """Serializer for project contributors from JSON data"""
    contributors = serializers.SerializerMethodField()
    totalContributors = serializers.IntegerField(source='total_contributors', read_only=True)
    totalContributions = serializers.IntegerField(source='total_contributions', read_only=True)
    lastFetched = serializers.DateTimeField(source='last_fetched', read_only=True)
    
    class Meta:
        model = ProjectContributors
        fields = ['id', 'contributors', 'totalContributors', 'totalContributions', 'lastFetched']
    
    def get_contributors(self, obj):
        """Get top contributors from JSON data"""
        return obj.get_top_contributors(limit=10)  # Return top 10 contributors


class ProjectMetricsSerializer(serializers.ModelSerializer):
    """Serializer for project metrics"""
    
    class Meta:
        model = ProjectMetrics
        fields = ['id', 'health_score', 'risk_level', 'last_updated', 'documentation_coverage', 'active_contributors']
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Format the project ID for consistency
        data['projectId'] = str(instance.project.id)
        return data


class ProjectSerializer(serializers.ModelSerializer):
    """Serializer for projects (list/detail) matching Team Dashboard API).
    Adds computed fields and maps to the documented contract.
    """
    repositoryUrl = serializers.SerializerMethodField()
    documentationUrl = serializers.SerializerMethodField()
    lastUpdated = serializers.DateTimeField(source='last_activity', read_only=True)
    teamData = serializers.SerializerMethodField()
    totalCommits = serializers.SerializerMethodField()
    totalLinesOfCode = serializers.SerializerMethodField()
    totalPullRequests = serializers.SerializerMethodField()
    totalDocContributions = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    contributorsReady = serializers.SerializerMethodField()
    lastFetched = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            'id', 'name', 'description', 'status',
            'repositoryUrl', 'documentationUrl', 'teamData', 'lastUpdated', 'contributorsReady', 'lastFetched',
            'totalCommits', 'totalLinesOfCode', 'totalPullRequests', 'totalDocContributions',
        ]
    
    def get_repositoryUrl(self, obj: Project):
        return obj.repository_url or ''
    
    def get_documentationUrl(self, obj: Project):
        return obj.documentation_url or ''

    def get_status(self, obj: Project):
        return obj.status or 'active'

    def _get_contributors_record(self, obj: Project):
        try:
            # Get the most recent contributor record for this project
            # This handles any potential duplicates gracefully
            record = ProjectContributors.objects.filter(
                project=obj
            ).order_by('-last_fetched').first()
            
            if not record:
                logger.warning(f"No ProjectContributors record found for project {obj.id} ({obj.name})")
                return None
                
            # Log if we found multiple records (for debugging)
            total_records = ProjectContributors.objects.filter(project=obj).count()
            if total_records > 1:
                logger.warning(f"Found {total_records} ProjectContributors records for project {obj.id}, using most recent")
            
            # Debug logging to see what data we're getting
            logger.info(f"Found ProjectContributors record for project {obj.id}: {len(record.contributors_data)} contributors")
            if record.contributors_data:
                logger.info(f"Sample contributor data: {record.contributors_data[0] if record.contributors_data else 'None'}")
                
            return record
        except Exception as e:
            logger.error(f"Error getting contributors record for project {obj.id}: {e}")
            return None

    def _normalize_member(self, raw: dict, team_totals=None) -> dict:
        # Map raw JSON contributor into the frontend TeamMember shape
        name = raw.get('name') or raw.get('github_username') or 'Unknown'
        initials = ''.join([p[:1] for p in str(name).split() if p]) or 'U'
        
        # Calculate intelligent contribution score with team totals
        contribution_score = self.get_contribution_score(raw, team_totals)
        
        # Map the actual field names from your data structure
        # Your data has: linesOfCode, pullRequests, additions, deletions, netChange
        return {
            'id': str(raw.get('github_username') or name),
            'name': name,
            'avatar': initials.upper(),
            'commits': int(raw.get('commits', 0)),
            'linesOfCode': int(raw.get('linesOfCode', raw.get('lines_of_code', 0))),  # Your data has linesOfCode
            'pullRequests': int(raw.get('pullRequests', raw.get('pull_requests', 0))),  # Your data has pullRequests
            'docContributions': int(raw.get('docs', raw.get('docContributions', 0))),
            'role': raw.get('role', ''),
            'contributionScore': contribution_score,
            'email': raw.get('email', ''),
            'lastActive': raw.get('last_contribution', raw.get('lastActive', '')),
            # Additional fields from your data structure
            'additions': int(raw.get('additions', 0)),
            'deletions': int(raw.get('deletions', 0)),
            'netChange': int(raw.get('netChange', 0)),
        }

    def _within_range(self, iso_str: str, start, end) -> bool:
        try:
            if not iso_str:
                return False
            dt = datetime.fromisoformat(iso_str.replace('Z', '+00:00'))
            return start <= dt <= end
        except Exception:
            return False

    def _filter_contributions_by_time(self, contributions_history: dict, start, end) -> dict:
        """Filter contributions by time range and recalculate metrics"""
        if not start or not end:
            return contributions_history

        filtered_history = {
            'commits': [],
            'pull_requests': []
        }

        # Filter commits by date
        for commit in contributions_history.get('commits', []):
            if self._within_range(commit.get('date', ''), start, end):
                filtered_history['commits'].append(commit)

        # Filter PRs by date
        for pr in contributions_history.get('pull_requests', []):
            if self._within_range(pr.get('date', ''), start, end):
                filtered_history['pull_requests'].append(pr)

        return filtered_history

    def _recalculate_contributor_metrics(self, contributor_data: dict, filtered_history: dict) -> dict:
        """Recalculate contributor metrics based on filtered history"""
        username = contributor_data.get('github_username', '')
        
        # Filter commits and PRs for this contributor
        user_commits = [c for c in filtered_history.get('commits', []) if c.get('author') == username]
        user_prs = [p for p in filtered_history.get('pull_requests', []) if p.get('author') == username]
        
        # Recalculate metrics
        filtered_commits = len(user_commits)
        filtered_lines_of_code = sum(c.get('lines_added', 0) + c.get('lines_deleted', 0) for c in user_commits)
        filtered_prs = len(user_prs)
        
        # Calculate new contribution score
        filtered_contributions = min(100, filtered_commits * 2 + filtered_prs)
        
        # Get last active date from filtered data
        last_active = ''
        if user_commits:
            commit_dates = [c.get('date', '') for c in user_commits if c.get('date')]
            if commit_dates:
                last_active = max(commit_dates)
        if user_prs and not last_active:
            pr_dates = [p.get('date', '') for p in user_prs if p.get('date')]
            if pr_dates:
                last_active = max(pr_dates)

        return {
            **contributor_data,
            'commits': filtered_commits,
            'linesOfCode': filtered_lines_of_code,
            'pullRequests': filtered_prs,
            'contributions': filtered_contributions,
            'lastActive': last_active or contributor_data.get('last_contribution', '')
        }

    def get_teamData(self, obj: Project):
        try:
            record = self._get_contributors_record(obj)
            if not record or not record.contributors_data:
                logger.warning(f"No contributors data for project {obj.id}: record={record}, data_length={len(record.contributors_data) if record else 0}")
                return []

            logger.info(f"Processing {len(record.contributors_data)} contributors for project {obj.id}")

            # Get time filter from context
            time_range = self.context.get('time_range') if hasattr(self, 'context') else None
            start = end = None
            
            if time_range in ['last-7-days', 'last-30-days', 'last-3-months', 'last-6-months', '1month', '3months', '6months']:
                end = timezone.now()
                if time_range in ['last-7-days']:
                    start = end - timedelta(days=7)
                elif time_range in ['last-30-days', '1month']:
                    start = end - timedelta(days=30)
                elif time_range in ['last-3-months', '3months']:
                    start = end - timedelta(days=90)
                elif time_range in ['last-6-months', '6months']:
                    start = end - timedelta(days=180)

            # Get contributors data (filtered or not)
            contributors_data = record.contributors_data
            if start and end:
                # Apply time filtering and recalculate metrics
                filtered_history = self._filter_contributions_by_time(
                    record.contributions_history or {}, start, end
                )
                # Recalculate metrics for each contributor based on filtered history
                contributors_data = []
                for raw in record.contributors_data:
                    try:
                        recalculated_data = self._recalculate_contributor_metrics(raw, filtered_history)
                        contributors_data.append(recalculated_data)
                    except Exception as e:
                        logger.error(f"Error recalculating metrics for contributor {raw.get('github_username', 'unknown')}: {e}")
                        continue

            # Calculate team totals for relative scoring
            team_totals = {
                'lines': sum(int(c.get('linesOfCode', 0)) for c in contributors_data),
                'commits': sum(int(c.get('commits', 0)) for c in contributors_data),
                'prs': sum(int(c.get('pullRequests', 0)) for c in contributors_data),
                'reviews': sum(int(c.get('reviews', 0)) for c in contributors_data)
            }

            # Normalize members with team totals for relative scoring
            normalized = []
            for raw in contributors_data:
                try:
                    normalized.append(self._normalize_member(raw, team_totals))
                except Exception as e:
                    logger.error(f"Error normalizing contributor {raw.get('github_username', 'unknown')}: {e}")
                    continue

            logger.info(f"Successfully normalized {len(normalized)} contributors with team totals: {team_totals}")
            return normalized
        except Exception as e:
            logger.error(f"Error in get_teamData for project {obj.id}: {e}")
            return []

    def _aggregate(self, members: list, key: str) -> int:
        safe = [int(m.get(key, 0) or 0) for m in members]
        return int(sum(safe))

    def get_contributorsReady(self, obj: Project) -> bool:
        try:
            record = self._get_contributors_record(obj)
            return bool(record and record.contributors_data and len(record.contributors_data) > 0)
        except Exception:
            return False

    def get_lastFetched(self, obj: Project):
        try:
            record = self._get_contributors_record(obj)
            if record and record.last_fetched:
                return record.last_fetched.isoformat()
            return None
        except Exception:
            return None

    def get_totalCommits(self, obj: Project) -> int:
        members = self.get_teamData(obj)
        return self._aggregate(members, 'commits')

    def get_totalLinesOfCode(self, obj: Project) -> int:
        members = self.get_teamData(obj)
        return self._aggregate(members, 'linesOfCode')

    def get_totalPullRequests(self, obj: Project) -> int:
        members = self.get_teamData(obj)
        return self._aggregate(members, 'pullRequests')

    def get_totalDocContributions(self, obj: Project) -> int:
        members = self.get_teamData(obj)
        return self._aggregate(members, 'docContributions')
    
    
    def get_contribution_score(self, raw, team_totals=None):
        """
        Simple performance score: 0 - 100
        Factors: PRs (40%), Commits (30%), Lines of Code (20%), Reviews (10%)
        """

        commits = int(raw.get('commits', 0))
        prs = int(raw.get('pullRequests', 0))
        loc = int(raw.get('linesOfCode', 0))
        reviews = int(raw.get('reviews', 0))

        # fallback if no team totals -> basic weighted activity
        if not team_totals:
            base_score = (
                prs * 4 +
                commits * 3 +
                (loc // 100) * 2 +
                reviews
            )
            return min(100, base_score)

        score = 0.0

        # Normalize each metric relative to team totals
        if team_totals.get('prs', 0) > 0:
            score += (prs / team_totals['prs']) * 100 * 0.4  # 40%

        if team_totals.get('commits', 0) > 0:
            score += (commits / team_totals['commits']) * 100 * 0.3  # 30%

        if team_totals.get('lines', 0) > 0:
            score += ((loc / team_totals['lines']) * 100) * 0.2  # 30%


        return round(min(100, score), 1)



class ProjectCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating projects"""
    # Back-compat aliases
    repoPath = serializers.URLField(write_only=True, required=False, allow_blank=True)
    docsPath = serializers.URLField(write_only=True, required=False, allow_blank=True)
    repoType = serializers.CharField(write_only=True, required=False, allow_blank=True)
    status = serializers.CharField(required=False, allow_blank=True)
    repository_url = serializers.CharField(write_only=True, required=False, allow_blank=True)
    repository_id = serializers.CharField(write_only=True, required=False, allow_blank=True)
    timeRange = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = Project
        fields = ['name', 'description', 'repoPath', 'docsPath', 'repoType', 'status', 'repository_url', 'repository_id', 'timeRange']
    
    def create(self, validated_data):
        # Auto-generate slug from name
        from django.utils.text import slugify
        validated_data['slug'] = slugify(validated_data['name'])
        # Extract write-only aliases
        repo_alias = validated_data.pop('repository_url', None)
        repoPath = validated_data.pop('repoPath', None)
        docsPath = validated_data.pop('docsPath', None)
        repoType = validated_data.pop('repoType', None)
        validated_data.pop('repository_id', None)
        validated_data.pop('timeRange', None)

        # Map to new fields
        validated_data['repository_url'] = repo_alias or repoPath or None
        if docsPath:
            validated_data['documentation_url'] = docsPath
        if repoType:
            validated_data['repository_provider'] = repoType

        return super().create(validated_data)


class ProjectUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating projects"""
    # Back-compat aliases
    repoPath = serializers.URLField(write_only=True, required=False, allow_blank=True)
    docsPath = serializers.URLField(write_only=True, required=False, allow_blank=True)
    repoType = serializers.CharField(write_only=True, required=False, allow_blank=True)
    status = serializers.CharField(required=False, allow_blank=True)
    
    class Meta:
        model = Project
        fields = ['name', 'description', 'repoPath', 'docsPath', 'repoType', 'status']
    
    def update(self, instance, validated_data):
        # Update slug if name changes
        if 'name' in validated_data and validated_data['name'] != instance.name:
            from django.utils.text import slugify
            validated_data['slug'] = slugify(validated_data['name'])
        # Map aliases
        repoPath = validated_data.pop('repoPath', None)
        docsPath = validated_data.pop('docsPath', None)
        repoType = validated_data.pop('repoType', None)
        if repoPath is not None:
            validated_data['repository_url'] = repoPath
        if docsPath is not None:
            validated_data['documentation_url'] = docsPath
        if repoType is not None:
            validated_data['repository_provider'] = repoType
        return super().update(instance, validated_data)


class DocumentationAssignmentSerializer(serializers.Serializer):
    """Serializer for assigning documentation responsibility"""
    memberEmail = serializers.EmailField(required=False, allow_null=True)
    type = serializers.ChoiceField(choices=['main', 'secondary'])
    
    def validate_memberEmail(self, value):
        """Validate that the member exists or is null for removal"""
        if value is None or value == "":
            return None
        try:
            User.objects.get(email=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")


class ProjectSearchSerializer(serializers.Serializer):
    """Serializer for project search parameters"""
    q = serializers.CharField(required=False, allow_blank=True)
    categories = serializers.CharField(required=False, allow_blank=True)
    tags = serializers.CharField(required=False, allow_blank=True)
    contributors = serializers.CharField(required=False, allow_blank=True)
    timeRange = serializers.CharField(required=False, allow_blank=True)
    page = serializers.IntegerField(default=1, min_value=1)
    limit = serializers.IntegerField(default=20, min_value=1, max_value=100)
    
    def validate_timeRange(self, value):
        """Validate time range"""
        if value and value not in ['1month', '3months', '6months', '1year']:
            raise serializers.ValidationError("Invalid time range")
        return value


class ContributorDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed contributor information from JSON data"""
    contributors = serializers.SerializerMethodField()
    totalContributors = serializers.IntegerField(source='total_contributors', read_only=True)
    totalContributions = serializers.IntegerField(source='total_contributions', read_only=True)
    lastFetched = serializers.DateTimeField(source='last_fetched', read_only=True)
    
    class Meta:
        model = ProjectContributors
        fields = ['id', 'contributors', 'totalContributors', 'totalContributions', 'lastFetched']
    
    def get_contributors(self, obj):
        """Get all contributors from JSON data"""
        return obj.contributors_data  # Return all contributors