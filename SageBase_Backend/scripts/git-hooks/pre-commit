#!/bin/sh
# Ensure deploy script is executable and basic sanity
if [ -f scripts/deploy.sh ] && [ ! -x scripts/deploy.sh ]; then
  chmod +x scripts/deploy.sh
fi
# Optionally check that .env.deployment isn't committed accidentally
if git diff --cached --name-only | grep -q "^\.env\.deployment$"; then
  echo "[pre-commit] Warning: .env.deployment is staged. Consider removing secrets before committing." >&2
fi
exit 0