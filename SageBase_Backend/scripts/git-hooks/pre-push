#!/bin/sh
# Local pre-push hook: if pushing deployment branch, prepare deploy metadata
# Install via: scripts/install_git_hook.sh

branch="$(git rev-parse --abbrev-ref HEAD)"
if [ "$branch" = "deployment" ]; then
  echo "[pre-push] deployment branch detected -> preparing deploy tag/env"

  bump="${BUMP:-}" # allow manual override: BUMP=minor git push
  if [ -z "$bump" ]; then
    # Infer bump from last commit message (merge commit included)
    msg=$(git log -1 --pretty=%B)
    echo "[pre-push] last commit message: $(echo "$msg" | head -n1)"
    if   echo "$msg" | grep -Eqi '\[bump:major\]|breaking change|feat!'; then
      bump="major"
    elif echo "$msg" | grep -Eqi '\[bump:minor\]|^feat(\(|:)|feat\:'; then
      bump="minor"
    elif echo "$msg" | grep -Eqi '\[bump:patch\]|^fix(\(|:)|fix\:'; then
      bump="patch"
    else
      bump="patch"
    fi
  fi

  if [ -x "scripts/deploy.sh" ]; then
    echo "[pre-push] using bump=$bump"
    ./scripts/deploy.sh --bump "$bump" --no-push
  else
    echo "[pre-push] scripts/deploy.sh not executable or missing"
  fi
fi
exit 0