#!/usr/bin/env bash
set -euo pipefail

# Install local git hooks by symlinking from scripts/git-hooks to .git/hooks

repo_root_dir=$(git rev-parse --show-toplevel)
hooks_src_dir="$repo_root_dir/scripts/git-hooks"
hooks_dst_dir="$repo_root_dir/.git/hooks"

if [ ! -d "$hooks_src_dir" ]; then
  echo "Hooks source directory not found: $hooks_src_dir" >&2
  exit 1
fi

mkdir -p "$hooks_dst_dir"

for hook in pre-commit pre-push; do
  src="$hooks_src_dir/$hook"
  dst="$hooks_dst_dir/$hook"
  if [ -f "$src" ]; then
    chmod +x "$src"
    ln -sf "$src" "$dst"
    echo "Installed hook: $hook"
  fi
done

echo "Local git hooks installed."