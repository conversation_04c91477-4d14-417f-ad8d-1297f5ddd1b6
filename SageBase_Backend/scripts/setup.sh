#!/bin/bash

# SageBase Backend Setup Script
set -e

echo "🚀 Setting up SageBase Backend environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker &> /dev/null || ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose V2 is not available. Please install Docker with Compose V2."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cat > .env << EOF
# Database Configuration
DATABASE_URL=**************************************/sagebase

# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key

# ChromaDB Configuration
CHROMA_PERSIST_DIR=./chroma_db/

# Debug Mode
DEBUG=1
EOF
    echo "⚠️  Please update the .env file with your actual credentials"
fi

# Build and start containers
echo "🔨 Building Docker containers..."
docker compose build

echo "🚀 Starting services..."
docker compose up -d

echo "⏳ Waiting for services to be ready..."
sleep 10

echo "🎉 Setup complete!"
echo "📍 API available at: http://localhost:8000"
echo "📍 Redis (Docker) available at: localhost:6380"
echo "📍 PostgreSQL available at: localhost:5434"
echo "📍 Your existing Redis service remains on: localhost:6379"
echo ""
echo "🔧 Useful commands:"
echo "  - View logs: docker compose logs -f"
echo "  - Stop services: docker compose down"
echo "  - Restart: docker compose restart"
echo "  - Shell access: docker compose exec web bash"