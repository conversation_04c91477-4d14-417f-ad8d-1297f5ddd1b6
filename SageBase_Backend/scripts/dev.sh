#!/bin/bash

# Development helper script
set -e

case "$1" in
    "start")
        echo "🚀 Starting development environment..."
        docker compose up -d
        ;;
    "stop")
        echo "🛑 Stopping development environment..."
        docker compose down
        ;;
    "restart")
        echo "🔄 Restarting development environment..."
        docker compose restart
        ;;
    "logs")
        if [ "$2" = "django" ] || [ "$2" = "web" ]; then
            echo "📋 Showing Django logs..."
            docker compose logs -f web
        else
            echo "📋 Showing all logs..."
            docker compose logs -f
        fi
        ;;
    "shell")
        echo "🐚 Opening shell in web container..."
        docker compose exec web bash
        ;;
    "test")
        echo "🧪 Running tests..."
        docker compose exec web python manage.py test
        ;;
    "migrate")
        echo "📊 Running migrations..."
        docker compose exec web python manage.py migrate
        ;;
    "makemigrations")
        echo "📝 Creating new migrations..."
        docker compose exec web python manage.py makemigrations
        ;;
    "debug")
        echo "🐛 Starting Django server with debugger support..."
        echo "Server will be available at: http://localhost:8000"
        echo "Debugger will listen on port 5678 for VS Code attachment"
        echo "Press Ctrl+C to stop"
        docker compose exec web python -Xfrozen_modules=off -m debugpy --listen 0.0.0.0:5678 --wait-for-client manage.py runserver 0.0.0.0:8000
        ;;
    "run")
        echo "🚀 Starting Django server (no debugger)..."
        echo "Server will be available at: http://localhost:8000"
        echo "Perfect for print-based debugging"
        echo "Press Ctrl+C to stop"
        docker compose exec web python -Xfrozen_modules=off manage.py runserver 0.0.0.0:8000
        ;;
    "clean")
        echo "🧹 Cleaning up containers and volumes..."
        docker compose down -v
        docker system prune -f
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|logs|shell|test|migrate|makemigrations|run|debug|clean}"
        echo ""
        echo "Commands:"
        echo "  start          - Start development environment"
        echo "  stop           - Stop development environment"
        echo "  restart        - Restart development environment"
        echo "  logs           - Show all container logs"
        echo "  logs django    - Show Django server logs only"
        echo "  shell          - Open shell in web container"
        echo "  test           - Run tests"
        echo "  migrate        - Run database migrations"
        echo "  makemigrations - Create new migrations"
        echo "  run            - Start Django server (no debugger, print-friendly)"
        echo "  debug          - Start Django server with debugger (VS Code ready)"
        echo "  clean          - Clean up containers and volumes"
        exit 1
        ;;
esac