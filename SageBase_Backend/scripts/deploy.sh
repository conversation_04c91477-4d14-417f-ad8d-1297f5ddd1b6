#!/usr/bin/env bash
set -euo pipefail

# SageBase simple deploy helper (no CI)
# - Computes/bumps a tag (default: patch)
# - Writes .env.version with APP_VERSION, GIT_COMMIT, BUILD_TIME
# - Optionally pushes the tag (skip with --no-push)
#
# Usage:
#   scripts/deploy.sh [--bump patch|minor|major] [--tag vX.Y.Z] [--no-push] [--no-tag] [--env-file .env.version]
#
# Notes:
# - If --tag is provided, --bump is ignored
# - If --no-tag is provided, APP_VERSION will be derived from commit/date
# - Run from repo root

bump="patch"
explicit_tag=""
push_tag=1
create_tag=1
env_file=".env.version"

while [[ $# -gt 0 ]]; do
  case "$1" in
    --bump)
      bump="${2:-patch}"; shift 2;;
    --tag)
      explicit_tag="${2:-}"; shift 2;;
    --no-push)
      push_tag=0; shift;;
    --no-tag)
      create_tag=0; shift;;
    --env-file)
      env_file="${2:-.env.version}"; shift 2;;
    *)
      echo "Unknown arg: $1"; exit 1;;
  esac
done

# Ensure git available and in a repo
if ! git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
  echo "This script must run inside a git repository." >&2
  exit 1
fi

# Compute tag
next_tag=""
if [[ $create_tag -eq 1 ]]; then
  if [[ -n "$explicit_tag" ]]; then
    next_tag="$explicit_tag"
  else
    latest_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
    # Split latest tag
    ver=${latest_tag#v}
    IFS='.' read -r major minor patch <<<"$ver"
    major=${major:-0}; minor=${minor:-0}; patch=${patch:-0}
    case "$bump" in
      major) major=$((major+1)); minor=0; patch=0;;
      minor) minor=$((minor+1)); patch=0;;
      patch) patch=$((patch+1));;
      *) echo "Invalid bump: $bump"; exit 1;;
    esac
    next_tag="v${major}.${minor}.${patch}"
  fi
fi

# Compute metadata
GIT_COMMIT=$(git rev-parse --short HEAD)
BUILD_TIME=$(date -u +%FT%TZ)

if [[ $create_tag -eq 1 ]]; then
  # Create annotated tag if it doesn't already exist
  if git rev-parse "$next_tag" >/dev/null 2>&1; then
    echo "Tag $next_tag already exists. Using it."
  else
    git tag -a "$next_tag" -m "release: $next_tag"
    echo "Created tag $next_tag"
  fi
  APP_VERSION="$next_tag"
else
  # No tag: derive a version string
  APP_VERSION="dev-${GIT_COMMIT}-${BUILD_TIME}"
fi

# Optionally push tag
if [[ $create_tag -eq 1 && $push_tag -eq 1 ]]; then
  git push origin "$next_tag"
  echo "Pushed tag $next_tag to origin"
else
  echo "Skipping tag push (hook or --no-push)"
fi

# Write env file (version-only overrides)
cat > "$env_file" <<EOF
APP_VERSION=$APP_VERSION
GIT_COMMIT=$GIT_COMMIT
BUILD_TIME=$BUILD_TIME
EOF

echo "Wrote $env_file with:"
echo "  APP_VERSION=$APP_VERSION"
echo "  GIT_COMMIT=$GIT_COMMIT"
echo "  BUILD_TIME=$BUILD_TIME"

echo "Next steps:"
echo "- Ensure your runtime loads $env_file in addition to your base env (.env.dev/.env.deployment)."
echo "- Frontend will read version via /api/version."