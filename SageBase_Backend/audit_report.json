{"dependencies": [{"name": "annotated-types", "version": "0.7.0", "vulns": []}, {"name": "anyio", "version": "4.10.0", "vulns": []}, {"name": "authlib", "version": "1.6.3", "vulns": []}, {"name": "bandit", "version": "1.8.6", "vulns": []}, {"name": "boolean-py", "version": "5.0", "vulns": []}, {"name": "cachecontrol", "version": "0.14.3", "vulns": []}, {"name": "certifi", "version": "2025.8.3", "vulns": []}, {"name": "cffi", "version": "1.17.1", "vulns": []}, {"name": "charset-normalizer", "version": "3.4.3", "vulns": []}, {"name": "click", "version": "8.2.1", "vulns": []}, {"name": "cryptography", "version": "45.0.7", "vulns": []}, {"name": "cyclonedx-python-lib", "version": "9.1.0", "vulns": []}, {"name": "defusedxml", "version": "0.7.1", "vulns": []}, {"name": "dparse", "version": "0.6.4", "vulns": []}, {"name": "filelock", "version": "3.16.1", "vulns": []}, {"name": "git-filter-repo", "version": "2.47.0", "vulns": []}, {"name": "gitdb", "version": "4.0.12", "vulns": []}, {"name": "git<PERSON><PERSON>on", "version": "3.1.44", "vulns": []}, {"name": "h11", "version": "0.16.0", "vulns": []}, {"name": "httpcore", "version": "1.0.9", "vulns": []}, {"name": "httpx", "version": "0.28.1", "vulns": []}, {"name": "idna", "version": "3.10", "vulns": []}, {"name": "jinja2", "version": "3.1.6", "vulns": []}, {"name": "joblib", "version": "1.5.2", "vulns": []}, {"name": "license-expression", "version": "30.4.4", "vulns": []}, {"name": "markdown-it-py", "version": "4.0.0", "vulns": []}, {"name": "markupsafe", "version": "3.0.2", "vulns": []}, {"name": "marshmallow", "version": "4.0.1", "vulns": []}, {"name": "mdurl", "version": "0.1.2", "vulns": []}, {"name": "msgpack", "version": "1.1.1", "vulns": []}, {"name": "nltk", "version": "3.9.1", "vulns": []}, {"name": "packageurl-python", "version": "0.17.5", "vulns": []}, {"name": "packaging", "version": "25.0", "vulns": []}, {"name": "pip", "version": "24.3.1", "vulns": []}, {"name": "pip-api", "version": "0.0.34", "vulns": []}, {"name": "pip-audit", "version": "2.9.0", "vulns": []}, {"name": "pip-requirements-parser", "version": "32.0.1", "vulns": []}, {"name": "platformdirs", "version": "4.4.0", "vulns": []}, {"name": "psutil", "version": "6.1.1", "vulns": []}, {"name": "py-serializable", "version": "2.1.0", "vulns": []}, {"name": "pyc<PERSON><PERSON>", "version": "2.22", "vulns": []}, {"name": "pydantic", "version": "2.9.2", "vulns": []}, {"name": "pydantic-core", "version": "2.23.4", "vulns": []}, {"name": "pygments", "version": "2.19.2", "vulns": []}, {"name": "pyparsing", "version": "3.2.3", "vulns": []}, {"name": "pytz", "version": "2025.1", "vulns": []}, {"name": "pyyaml", "version": "6.0.2", "vulns": []}, {"name": "regex", "version": "2025.8.29", "vulns": []}, {"name": "requests", "version": "2.32.5", "vulns": []}, {"name": "rich", "version": "14.1.0", "vulns": []}, {"name": "ruamel-yaml", "version": "0.18.15", "vulns": []}, {"name": "ruamel-yaml-clib", "version": "0.2.12", "vulns": []}, {"name": "safety", "version": "3.6.0", "vulns": []}, {"name": "safety-schemas", "version": "0.0.14", "vulns": []}, {"name": "setuptools", "version": "80.9.0", "vulns": []}, {"name": "shellingham", "version": "1.5.4", "vulns": []}, {"name": "smmap", "version": "5.0.2", "vulns": []}, {"name": "sniffio", "version": "1.3.1", "vulns": []}, {"name": "sortedcontainers", "version": "2.4.0", "vulns": []}, {"name": "s<PERSON><PERSON><PERSON>", "version": "5.5.0", "vulns": []}, {"name": "tenacity", "version": "9.1.2", "vulns": []}, {"name": "toml", "version": "0.10.2", "vulns": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.13.3", "vulns": []}, {"name": "tqdm", "version": "4.67.1", "vulns": []}, {"name": "typer", "version": "0.17.3", "vulns": []}, {"name": "typing-extensions", "version": "4.15.0", "vulns": []}, {"name": "urllib3", "version": "2.5.0", "vulns": []}], "fixes": []}