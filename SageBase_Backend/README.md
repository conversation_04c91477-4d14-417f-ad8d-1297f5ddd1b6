### Versioning, Deploy, and Frontend Display

This project exposes a simple backend endpoint to report the running version, and includes lightweight deploy automation (no CI/CD required).

- Backend endpoint: `GET /api/version`
  - Returns `{ version, env, commit, build_time }`
  - Values are read from environment variables with safe defaults.

### Environment variables

Base dev env (`.env.dev` or `.env.development`):
```
APP_VERSION=dev
DJANGO_ENV=development
# Optional (for display in /api/version during dev)
GIT_COMMIT=local
BUILD_TIME=local
# SageBase Backend

A Django REST API with GitHub integration, LLM-powered repository analysis, and real-time notifications.

## 🚀 New Engineer Setup Guide

### Step 1: Install Docker (5 minutes)

**On Windows:**
1. Download [Docker Desktop for Windows](https://docs.docker.com/desktop/windows/install/)
2. Run the installer
3. Restart your computer
4. Open Command Prompt and verify: `docker --version`

**On Mac:**
1. Download [Docker Desktop for Mac](https://docs.docker.com/desktop/mac/install/)
2. Drag Docker to Applications folder
3. Launch Docker from Applications
4. Open Terminal and verify: `docker --version`

**On Linux:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo usermod -aG docker $USER
# Log out and log back in
```

Deployment overrides are written to a separate file by the deploy script: `.env.version`.
Load both files in your runtime (e.g., Docker Compose supports multiple `env_file` entries).

### Frontend usage

- Fetch once on app init:
```
GET /api/version
```
- Display rule suggestion:
  - If `env == 'deployment'`, show `version` (e.g., `v0.0.7`)
  - Else show `dev`

### Deploy script (no CI)
git commit -m "Add new API endpoint #minor"   --> create minor tag update
git commit -m "Breaking DB schema change #major"






 Python environment (uv)

Use a local virtual environment in `.venv` and install dependencies with `uv`.

Prerequisites:
- Python 3.11+
- uv installed (choose one):
  - Homebrew (macOS): `brew install uv`
  - pipx: `pipx install uv`
  - curl: `curl -LsSf https://astral.sh/uv/install.sh | sh`

Setup:
1) Create venv
```
uv venv .venv
```
2) Activate venv (macOS/Linux)
```
source .venv/bin/activate
```
3) Install dependencies
```
uv pip install -r requirements.txt
```
(Optional) Run Django commands via uv
```
uv run python manage.py migrate
uv run python manage.py runserver 0.0.0.0:8000
```
Note: The project convention is to always use the `.venv` environment when running commands.

**Manual Debug Options:**
- **Manual Docker Debug**: Use `./scripts/dev.sh debug` then attach with "Python: Attach to Django Docker"
- **Local Debug**: Use "Python: Django" configuration for local development

### Viewing Django Logs

**Option 1: Real-time logs (Recommended)**
```bash
# View all logs in real-time
./scripts/dev.sh logs

# View only Django server logs
./scripts/dev.sh logs django

# Alternative: View Django logs directly
docker-compose logs -f web
```

**Option 2: Inside container**
```bash
# Access the container shell
./scripts/dev.sh shell

# View Django logs directly
python manage.py runserver --verbosity=2
```

**Option 3: Manual setup logs**
```bash
# If running without Docker
python manage.py runserver 0.0.0.0:8000 --verbosity=2

# With debug output
python manage.py runserver 0.0.0.0:8000 --settings=SageBase_Backend.settings --verbosity=3
```

**Log levels:**
- `--verbosity=0`: Minimal output
- `--verbosity=1`: Normal output (default)
- `--verbosity=2`: Verbose output
- `--verbosity=3`: Very verbose output

## Troubleshooting

**Container won't start:**
```bash
./scripts/dev.sh logs
```

**Database issues:**
```bash
./scripts/dev.sh migrate
```

**Clean slate:**
```bash
./scripts/dev.sh clean
./scripts/setup.sh
```

**Access container:**
```bash
./scripts/dev.sh shell
```

**View all available commands:**
```bash
./scripts/dev.sh
```

### Useful Commands

**Start development environment:**
```bash
F="docker-compose.test.yml" && docker compose -f "$F" down --volumes && docker compose -f "$F" build --no-cache && docker compose -f "$F" up -dpru
```

**Manually force load environment variables:**
```bash
set -a; source .env.dev; set +a
```


## Contributing

1. Create feature branch
2. Make changes
3. Run tests: `./scripts/dev.sh test`
4. Submit PR

## Support

For issues or questions:
1. Check logs: `./scripts/dev.sh logs`
2. Access container: `./scripts/dev.sh shell`
3. Clean and restart: `./scripts/dev.sh clean && ./scripts/setup.sh`
