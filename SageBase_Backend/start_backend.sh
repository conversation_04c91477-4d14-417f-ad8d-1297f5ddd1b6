#!/bin/sh

echo "🚀 Starting SageBase Backend..."
echo "📁 Working directory: $(pwd)"

# Use container environment variables (already set via docker run -e flags)
echo "🔧 Using container environment variables..."
echo "✅ Environment variables loaded: DJANGO_ENV=$DJANGO_ENV"
echo "✅ Database URL: $DATABASE_URL"

# comment out the lines when needed

echo "📦 Installing Python dependencies..."
pip install -r requirements.txt
echo "✅ Dependencies installed successfully"

#echo "🗄️ Running database migrations..."
#python manage.py migrate
#echo "✅ Database migrations completed"

echo "🌐 Starting Uvicorn server..."
uvicorn SageBase_Backend.asgi:application --host 0.0.0.0 --port 8000 --workers 4 