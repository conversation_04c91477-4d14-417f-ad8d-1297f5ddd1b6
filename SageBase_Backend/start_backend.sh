#!/bin/sh

echo "🚀 Starting SageBase Backend..."
echo "📁 Updating repository..."
cd /data/SageBase_BackEnd

#git pull deployment
echo "✅ Repository updated successfully"

# comment out the lines when needed

echo "📦 Installing Python dependencies..."
pip install -r requirements.txt
echo "✅ Dependencies installed successfully"

#echo "🗄️ Running database migrations..."
#python manage.py migrate
#echo "✅ Database migrations completed"

echo "🌐 Starting Uvicorn server..."
uvicorn SageBase_Backend.asgi:application --host 0.0.0.0 --port 8000 --workers 4 