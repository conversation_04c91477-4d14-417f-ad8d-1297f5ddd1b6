# 👥 SageBase User Management Guide

**For Django Application Administrators**

This guide explains how to set up companies and manage users in SageBase using the Django Admin Panel with Supabase-centric workflow.

---

## 🎯 **Overview: How User Management Works**

SageBase uses a **hybrid approach** combining Django Admin Panel with Supabase authentication:

- **Django Admin Panel** → Where you create companies and manage users (now with one-click option!)
- **Django Database** → Stores company data, user profiles, and business logic
- **Supabase Auth** → Handles all authentication, login, and password management
- **Automatic Emails** → Professional welcome emails sent automatically from `<EMAIL>`

### 🚀 **One-Click Company + Admin Creation**
The complete workflow to set up a new company with admin user:
1. Click "🚀 Create Company + Admin" button in Django Admin
2. Fill single form with company and admin details  
3. Everything created automatically with welcome email sent!

### 🎉 **Key Feature: AUTOMATIC Email Sending**
The system **automatically**:
1. Creates the user in Supabase Auth with temporary password
2. Sends professional welcome email from `<EMAIL>`  
3. Shows success message: *"Supabase connection email automatically sent"*

**No manual email sending required!**

---

## 📋 **Prerequisites**

### **Required Environment Variables**
Ensure these are configured in your `.env.dev` file:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key

# Frontend URL
FRONTEND_BASE_URL=http://localhost:3000

# Email Configuration (already set)
EMAIL_HOST=send.one.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=SageBase2025*
DEFAULT_FROM_EMAIL=SageBase <<EMAIL>>
```

### **Required Services**
- ✅ Django server running on `http://localhost:8000`
- ✅ Frontend application running on `http://localhost:3000`
- ✅ Supabase project configured with Auth enabled
- ✅ Email service (one.com) configured

### **Django Admin Access**
- ✅ Django superuser account created
- ✅ Access to `http://localhost:8000/admin/`

---

## 🚀 **Company + Admin Creation Process**

### **🎯 One-Click Company + Admin Creation**

**The Complete Workflow in One Step:**

1. **Open your browser** and go to: `http://localhost:8000/admin/`
2. **Login** with your Django superuser credentials
3. **Click on "Companies"** in the Django admin panel
4. **Click the green "🚀 Create Company + Admin" button** (top right)
5. **Fill in the one-click form:**
   - **Company Name:** `Acme Corporation`
   - **Company Email:** `<EMAIL>` (optional)
   - **Admin First Name:** `John`
   - **Admin Last Name:** `Doe`
   - **Admin Email:** `<EMAIL>`
6. **Click "🚀 Create Company + Admin"** button

**✅ What happens AUTOMATICALLY in one click:**
- ✅ Company created in Django database with default knowledge space
- ✅ Admin user created in Django database with role=ADMIN
- ✅ Admin user created in Supabase Auth with temporary password
- ✅ **Professional welcome email sent automatically** to admin from `<EMAIL>`
- ✅ Success message displayed: *"Successfully created company and admin user! Supabase connection email automatically sent"*
- ✅ Redirects to company list view

**🎉 That's it! Everything done in one step with one form!**

---

### **STEP 4: Admin Receives Welcome Email (Automatic!)**

The admin will receive a professional email from `<EMAIL>`:

**📧 Predefined Email Content (Sent Automatically):**
```
Subject: Welcome to SageBase - Setup Your Acme Corporation Admin Account

Hello John Doe,

Welcome to SageBase! Your Acme Corporation admin account has been created.
You can now set up your account and start managing your team.

📧 Your Login Details:
Email: <EMAIL>
Temporary Password: [secure-temporary-password]

🚀 Next Steps:
1. [Login to SageBase] → http://localhost:3000/login
2. [Reset Your Password] → http://localhost:3000/reset-password  
3. Invite Your Team → Once logged in, start inviting team members

💡 Pro Tip: Please login and change your password immediately for security.

Best regards,
The SageBase Team
```

**✨ This professional email is sent automatically when you create the admin user - no manual action required!**

---

### **STEP 5: Admin Sets Up Account**

**The admin will:**
1. **Login** to your frontend at `http://localhost:3000/login`
2. **Use credentials** from the email (email + temporary password)
3. **Reset password** immediately for security via Supabase
4. **Access admin dashboard** to start inviting team members

---

### **STEP 6: Admin Invites Team Members**

Once the admin is logged into your frontend application, they can use the built-in team invitation feature. The frontend will automatically call the team invitation API to:

1. Create team member users in Django database (role=USER)
2. Create team member users in Supabase Auth with temporary passwords  
3. Send professional invitation emails from `<EMAIL>`

**📝 Note:** Team member invitations are handled through your frontend application's admin interface, not through the Django Admin Panel.

---

### **STEP 8: Team Members Receive Invitations**

Team members receive professional emails from `<EMAIL>`:

**📧 Email Content:**
```
Subject: You're invited to join Acme Corporation on SageBase

Hi there,

John Doe has invited you to join Acme Corporation on SageBase.

📧 Your Login Details:
Email: <EMAIL>
Temporary Password: [secure-temporary-password]

🚀 Get Started:
1. [Login to SageBase] → http://localhost:3000/login
2. [Set Your Password] → http://localhost:3000/reset-password

💡 Important: This invitation expires in 7 days. Please set up your account soon!
```

---

## 🛠️ **Management & Troubleshooting**

### **View All Companies and Users**

**In Django Admin Panel:**
1. **Click on "Companies"** to see all companies created
2. **Click on "Users"** to see all users and their companies
3. **Use filters** to find specific companies or user roles
4. **Search by email** or company name

### **Resend Admin Connection Email (If Needed)**

If an admin didn't receive their email or lost their credentials:

1. **Go to "Users"** in Django Admin Panel
2. **Find the admin user** you want to resend email to
3. **Select the checkbox** next to their name
4. **In the "Action" dropdown** select **"📧 Resend Supabase connection email"**
5. **Click "Go"** to resend the welcome email

**✅ What happens:**
- New temporary password generated in Supabase
- Fresh welcome email sent from `<EMAIL>`
- Success message displayed in Django admin
- Admin can login with new credentials

**📝 Note:** This is only needed if the automatic email failed or the admin lost their credentials.

---

## 🔍 **Verification & Testing**

### **Test the Complete Flow**

1. **Create test company + admin in one click:**
   - Go to `http://localhost:8000/admin/`
   - Click on "Companies"
   - Click "🚀 Create Company + Admin" button (green button, top right)
   - Fill form: Company=`Test Company`, Admin=`<EMAIL>`
   - Click "🚀 Create Company + Admin"

2. **Verify success:**
   - Check for success message: "Successfully created company 'Test Company' with admin user 'Test Admin'! Supabase connection email automatically <NAME_EMAIL>"
   - Should redirect to company list view

3. **Check email delivery:**
   - Look for email in `<EMAIL>` inbox
   - Email should come from `<EMAIL>`
   - Should contain login credentials and next steps

4. **Verify Supabase Auth:**
   - Go to your Supabase dashboard → Authentication → Users  
   - Should see `<EMAIL>` user created
   - User should have company metadata attached

5. **Test admin login:**
   - Go to `http://localhost:3000/login`
   - Use credentials from email
   - Should successfully login via Supabase

---

## ❌ **Common Issues & Solutions**

### **Issue: Email Not Received**
**Causes:**
- Email service (one.com) not configured properly
- SMTP credentials incorrect
- Recipient email in spam folder

**Solutions:**
```bash
# Test email configuration
python manage.py shell
>>> from django.core.mail import send_mail
>>> send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])
```

### **Issue: Supabase User Not Created**
**Causes:**
- `SUPABASE_URL` or `SUPABASE_SERVICE_ROLE_KEY` missing/incorrect
- Supabase project not configured properly

**Solutions:**
1. Check environment variables in `.env.dev`
2. Verify Supabase project settings
3. Check Django logs for Supabase API errors

### **Issue: Admin Can't Login**
**Solutions:**
1. **Resend connection email** using Django admin action
2. **Check Supabase dashboard** → Authentication → Users
3. **Manual password reset** via Supabase dashboard if needed
4. **Verify user is active** in Django Admin → Users

---

## 📊 **Django Admin Panel Features**

| Feature | Location | Description |
|---------|----------|-------------|
| **Companies Management** | Django Admin → Companies | Create, edit, and view all companies |
| **User Management** | Django Admin → Users | Create, edit, and manage all users |
| **Send Welcome Emails** | Users → Actions → "📧 Resend Supabase connection email" | Resend Supabase connection emails (automatic on creation) |
| **Team Invitations** | Frontend Application | Admins invite team members via app interface |
| **Invitation Tracking** | Django Admin → Team Invitations | View all invitation statuses |

---

## 🎯 **Best Practices**

### **For One-Click Company + Admin Creation:**
- ✅ Use professional company names (they appear in emails)
- ✅ Verify admin email addresses before submitting the form
- ✅ Use real email addresses you can access for testing
- ✅ Always test with your own email first
- ✅ Monitor success/error messages in Django admin after submission

### **For Email Management:**
- ✅ Welcome emails sent automatically when creating admin users
- ✅ Monitor success messages in Django admin for confirmation
- ✅ Resend emails if users report not receiving them (using admin action)
- ✅ Check spam folders if emails don't arrive
- ✅ Monitor email delivery through Django admin messages

### **For Security:**
- ✅ Temporary passwords expire after first login
- ✅ Force users to reset passwords immediately
- ✅ Monitor failed login attempts via Supabase dashboard
- ✅ Deactivate unused accounts in Django admin

---

## 🆘 **Support & Troubleshooting**

**If you encounter issues:**

1. **Check Django admin messages** for success/error notifications
2. **Check Django logs** for detailed error messages  
3. **Verify environment variables** are correctly set in `.env.dev`
4. **Test email delivery** using the Django admin action
5. **Check Supabase dashboard** → Authentication → Users for user creation

**For urgent issues:**
- **Use the Django admin action** to resend emails to stuck users
- **Check Supabase Auth logs** for authentication problems
- **Verify your one.com email service** is working properly
- **Check user status** in Django Admin (Active, Company assigned, Role set)

---

## 🎉 **Success Metrics**

**You'll know the system is working when:**
- ✅ Companies created successfully in Django Admin Panel
- ✅ Admin users created successfully in Django Admin Panel  
- ✅ **Automatic success message appears**: *"Supabase connection email automatically sent"*
- ✅ Admins receive professional welcome emails from `<EMAIL>` **immediately**
- ✅ Admins can login to frontend via Supabase with temp credentials from email
- ✅ Admins can reset passwords and access the application
- ✅ Admins can invite team members through the frontend
- ✅ Team members receive invitation emails and can join

**Happy Django Admin Panel user management!** 🚀

---

## 📋 **Quick Reference Checklist**

**For each new company:**
- [ ] Go to Django Admin → Companies
- [ ] Click "🚀 Create Company + Admin" button
- [ ] Fill single form (Company + Admin details)
- [ ] Click "🚀 Create Company + Admin"
- [ ] ✅ Everything Created Automatically (company, admin, knowledge space, Supabase user, email sent!)
- [ ] Verify Success Message: "Successfully created company and admin user! Supabase connection email automatically sent"
- [ ] Confirm Email Delivery in Admin's Inbox
- [ ] Test Admin Login at Frontend

**Perfect one-click Supabase-centric workflow via Django Admin Panel!** ✨

---

## 🚀 **Summary: Fully Automated Workflow**

### **🎯 What You Do (Simple One-Click Process):**
1. Open Django Admin Panel → Companies
2. Click "🚀 Create Company + Admin" button
3. Fill single form (Company + Admin details)
4. Click "🚀 Create Company + Admin"

### **What Happens Automatically in One Click:**
1. ✅ Company created in Django database with default knowledge space
2. ✅ Admin user created in Django database with role=ADMIN
3. ✅ Admin user created in Supabase Auth with temp password  
4. ✅ Professional email sent from `<EMAIL>`
5. ✅ Success message displayed in Django Admin
6. ✅ Admin receives "Welcome to SageBase, please login using this link" email
7. ✅ Admin can immediately login to frontend with temp credentials

### **Zero Manual Email Steps Required!**

The system provides a **streamlined one-click workflow** that handles everything automatically - company creation, admin setup, Supabase user creation, and professional welcome email sending all in a single form submission! 🎉