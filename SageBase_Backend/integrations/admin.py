from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from knowledge_map.models import Project
from django.utils.html import format_html
from django.contrib import messages
from django.http import HttpResponseRedirect
import json
from .models import (
    Company, User, IntegrationTool, CompanyIntegration, MonitoredItem,
    SlackUserProfile, CrossRepoMonitor, ProjectContributor, ProjectMetrics, RepoChange, TeamInvitation,
    NotificationSettings
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'email', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'email']
    prepopulated_fields = {'slug': ('name',)}
    
    def get_urls(self):
        """Add custom URLs for admin actions"""
        from django.urls import path
        from .admin_views import create_company_admin_view
        
        urls = super().get_urls()
        custom_urls = [
            path('create-company-admin/', 
                 self.admin_site.admin_view(create_company_admin_view), 
                 name='integrations_company_create_company_admin'),
        ]
        return custom_urls + urls
    
    def changelist_view(self, request, extra_context=None):
        """Add custom context to company list view"""
        extra_context = extra_context or {}
        extra_context['show_create_company_admin_link'] = True
        return super().changelist_view(request, extra_context=extra_context)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ['id', 'email', 'first_name', 'last_name', 'company', 'role', 'is_active']
    list_filter = ['role', 'is_active', 'company']
    search_fields = ['email', 'first_name', 'last_name', 'company__name']
    ordering = ('email',)
    actions = ['send_supabase_connection_email']
    
    fieldsets = (
        ('Authentication', {
            'fields': ('email', 'password')
        }),
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'phone')
        }),
        ('Company & Role', {
            'fields': ('company', 'role')
        }),
        ('Status', {
            'fields': ('is_active', 'is_staff', 'is_superuser')
        }),
    )
    
    add_fieldsets = (
        ('Required Information', {
            'classes': ('wide',),
            'fields': ('email', 'first_name', 'last_name', 'password1', 'password2', 'company', 'role')
        }),
    )
    
    def send_supabase_connection_email(self, request, queryset):
        """Send Supabase connection email to selected users"""
        from .services.supabase_centric_service import SupabaseCentricService
        
        supabase_service = SupabaseCentricService()
        success_count = 0
        error_count = 0
        
        for user in queryset:
            try:
                # Only send to active users with companies
                if not user.is_active or not user.company:
                    continue
                
                # Create Supabase user with temp password
                user_metadata = {
                    'company_id': str(user.company.id),
                    'company_name': user.company.name,
                    'role': user.role,
                    'first_name': user.first_name,
                    'last_name': user.last_name
                }
                
                supabase_user, temp_password = supabase_service.create_supabase_user_with_temp_password(
                    email=user.email,
                    user_metadata=user_metadata
                )
                
                # Send connection email
                if temp_password:
                    supabase_service.send_admin_supabase_connection_email(user, temp_password)
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                messages.error(request, f"Failed to send email to {user.email}: {str(e)}")
        
        if success_count > 0:
            messages.success(request, f"Successfully sent Supabase connection emails to {success_count} user(s)")
        if error_count > 0:
            messages.warning(request, f"Failed to send emails to {error_count} user(s)")
    
    send_supabase_connection_email.short_description = "📧 Resend Supabase connection email"
    
    def save_model(self, request, obj, form, change):
        """Override save_model to show success message for automatic email sending"""
        
        # Check if this is a new admin user before saving
        is_new_admin = (
            obj.pk is None and 
            obj.role == User.Role.ADMIN and 
            obj.company and 
            obj.is_active
        )
        
        # Save the user (this will trigger automatic email sending via User.save())
        super().save_model(request, obj, form, change)
        
        # Show success message for automatic email sending
        if is_new_admin:
            messages.success(
                request, 
                f'✅ Admin user "{obj.first_name} {obj.last_name}" created successfully! '
                f'Supabase connection email automatically sent to {obj.email}'
            )
        elif change:
            messages.success(request, f'User "{obj.first_name} {obj.last_name}" updated successfully.')


class ProjectContributorInline(admin.TabularInline):
    """Inline admin for project contributors"""
    model = ProjectContributor
    extra = 0
    fields = ['user', 'contributions', 'commits', 'reviews', 'docs', 'last_contribution']
    readonly_fields = ['last_contribution']
    ordering = ['-contributions']


class ProjectMetricsInline(admin.StackedInline):
    """Inline admin for project metrics"""
    model = ProjectMetrics
    extra = 0
    fields = ['health_score', 'risk_level', 'documentation_coverage', 'active_contributors', 'last_updated']
    readonly_fields = ['last_updated']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'company', 'doc_responsible', 'secondary_responsible', 'total_contributors', 'repo_type', 'last_activity', 'created_at']
    list_filter = ['company', 'repo_type', 'created_at', 'last_activity']
    search_fields = ['name', 'description', 'company__name']
    prepopulated_fields = {'slug': ('name',)}
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'company')
        }),
        ('Documentation Responsibility', {
            'fields': ('doc_responsible', 'secondary_responsible')
        }),
        ('Repository Information', {
            'fields': ('repo_path', 'docs_path', 'repo_type')
        }),
        ('Classification', {
            'fields': ('categories', 'tags'),
            'description': 'Enter categories and tags as JSON arrays, e.g., ["tag1", "tag2"]'
        }),
        ('Statistics', {
            'fields': ('total_contributors', 'last_activity'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_activity', 'created_at', 'updated_at']
    
    inlines = [ProjectContributorInline, ProjectMetricsInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'doc_responsible', 'secondary_responsible')
    
    def display_categories(self, obj):
        """Display categories as formatted list"""
        if obj.categories:
            return format_html('<br>'.join(obj.categories))
        return 'None'
    display_categories.short_description = 'Categories'
    
    def display_tags(self, obj):
        """Display tags as formatted list"""
        if obj.tags:
            return format_html('<br>'.join(obj.tags))
        return 'None'
    display_tags.short_description = 'Tags'


@admin.register(IntegrationTool)
class IntegrationToolAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'is_active']
    list_filter = ['category', 'is_active']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}


@admin.register(CompanyIntegration)
class CompanyIntegrationAdmin(admin.ModelAdmin):
    list_display = ['id','company', 'tool', 'status', 'is_active', 'connected_at']
    list_filter = ['status', 'is_active', 'tool', 'company']
    search_fields = ['company__name', 'tool__name']


@admin.register(MonitoredItem)
class MonitoredItemAdmin(admin.ModelAdmin):
    list_display = ("id", "company", "type", "name", "url", "repo", "created_at")
    list_filter = ("company", "type")
    search_fields = ("name", "url", "repo")


@admin.register(SlackUserProfile)
class SlackUserProfileAdmin(admin.ModelAdmin):
    list_display = ["id", "user", "slack_user_id", "bot_token", "user_token", "is_active", "created_at"]
    search_fields = ["user__email", "slack_user_id"]
    list_filter = ["is_active", "created_at"]
    fields = ("user", "slack_user_id", "bot_token", "user_token", "is_active", "created_at", "updated_at")
    readonly_fields = ("created_at", "updated_at")


@admin.register(ProjectContributor)
class ProjectContributorAdmin(admin.ModelAdmin):
    list_display = ['user', 'project', 'get_project_company', 'contributions', 'commits', 'reviews', 'docs', 'last_contribution']
    list_filter = ['project__company', 'project', 'last_contribution']
    search_fields = ['user__first_name', 'user__last_name', 'user__email', 'project__name']
    ordering = ['-contributions', '-last_contribution']
    
    fieldsets = (
        ('Assignment', {
            'fields': ('project', 'user')
        }),
        ('Contribution Metrics', {
            'fields': ('contributions', 'commits', 'reviews', 'docs')
        }),
        ('Activity', {
            'fields': ('last_contribution', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_contribution', 'created_at', 'updated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('project', 'user', 'project__company')
    
    def get_project_company(self, obj):
        return obj.project.company.name
    get_project_company.short_description = 'Company'
    get_project_company.admin_order_field = 'project__company__name'


@admin.register(ProjectMetrics)
class ProjectMetricsAdmin(admin.ModelAdmin):
    list_display = ['project', 'get_project_company', 'colored_health_score', 'colored_risk_level', 'documentation_coverage', 'active_contributors', 'last_updated']
    list_filter = ['risk_level', 'project__company', 'last_updated']
    search_fields = ['project__name', 'project__company__name']
    ordering = ['-last_updated']
    
    fieldsets = (
        ('Project', {
            'fields': ('project',)
        }),
        ('Health Metrics', {
            'fields': ('health_score', 'risk_level', 'documentation_coverage', 'active_contributors')
        }),
        ('Timestamps', {
            'fields': ('last_updated', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_updated', 'created_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('project', 'project__company')
    
    def get_project_company(self, obj):
        return obj.project.company.name
    get_project_company.short_description = 'Company'
    get_project_company.admin_order_field = 'project__company__name'
    
    def colored_health_score(self, obj):
        """Display health score with color coding"""
        if obj.health_score >= 80:
            color = 'green'
        elif obj.health_score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.health_score
        )
    colored_health_score.short_description = 'Health Score'
    colored_health_score.admin_order_field = 'health_score'
    
    def colored_risk_level(self, obj):
        """Display risk level with color coding"""
        colors = {
            'Low': 'green',
            'Medium': 'orange', 
            'High': 'red',
            'Critical': 'darkred'
        }
        color = colors.get(obj.risk_level, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.risk_level
        )
    colored_risk_level.short_description = 'Risk Level'
    colored_risk_level.admin_order_field = 'risk_level'


@admin.register(TeamInvitation)
class TeamInvitationAdmin(admin.ModelAdmin):
    list_display = ['email', 'company', 'role', 'status', 'invited_by', 'created_at', 'expires_at', 'is_expired']
    list_filter = ['status', 'role', 'company', 'created_at']
    search_fields = ['email', 'company__name', 'invited_by__email']
    ordering = ['-created_at']
    readonly_fields = ['token', 'created_at', 'accepted_at']
    
    fieldsets = (
        ('Invitation Details', {
            'fields': ('company', 'email', 'role', 'status')
        }),
        ('Invitation Meta', {
            'fields': ('invited_by', 'token', 'created_at', 'expires_at', 'accepted_at')
        }),
    )
    
    def is_expired(self, obj):
        return obj.is_expired()
    is_expired.boolean = True
    is_expired.short_description = 'Expired'
    def external_repos_count(self, obj):
        return len(obj.external_repos)
    external_repos_count.short_description = 'External Repos Count'


@admin.register(RepoChange)
class RepoChangeAdmin(admin.ModelAdmin):
    list_display = ("id", "company", "internal_repo", "external_repo", "commit_id", "status", "created_at", "approved_by", "approved_at")
    list_filter = ("company", "status", "internal_repo", "external_repo", "created_at")
    search_fields = ("internal_repo", "external_repo", "commit_id", "summary")
    readonly_fields = ("created_at", "updated_at")
    actions = ["delete_selected", "delete_all_repo_changes"]

    def delete_all_repo_changes(self, request, queryset):
        total = RepoChange.objects.count()
        if 'apply' in request.POST:
            RepoChange.objects.all().delete()
            self.message_user(request, f"All {total} RepoChange records have been deleted.", messages.SUCCESS)
            return
        return admin.helpers.render_to_response(
            request,
            "admin/delete_all_confirmation.html",
            context={
                'title': 'Are you sure?',
                'objects_name': 'all RepoChange records',
                'action_checkbox_name': admin.helpers.ACTION_CHECKBOX_NAME,
                'opts': self.model._meta,
                'app_label': self.model._meta.app_label,
                'action_name': 'delete_all_repo_changes',
                'total': total,
            }
        )
    delete_all_repo_changes.short_description = "Delete ALL RepoChange records (dangerous)"


@admin.register(CrossRepoMonitor)
class CrossRepoMonitorAdmin(admin.ModelAdmin):
    list_display = ('company', 'internal_repo', 'external_repos_count', 'created_at')
    list_filter = ('company', 'created_at')
    search_fields = ('internal_repo', 'company__name')
    readonly_fields = ('created_at',)
    
    def external_repos_count(self, obj):
        return len(obj.external_repos)
    external_repos_count.short_description = 'External Repos Count'


@admin.register(NotificationSettings)
class NotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ['company', 'frequency_hours', 'enabled', 'last_notification_time', 'updated_at']
    list_filter = ['enabled', 'frequency_hours', 'updated_at']
    search_fields = ['company__name']
    ordering = ['company__name']
    
    fieldsets = (
        ('Company', {
            'fields': ('company',)
        }),
        ('Notification Control', {
            'fields': ('frequency_hours', 'enabled', 'last_notification_time'),
            'description': 'Set frequency_hours to 0 for no limit'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_notification_time', 'created_at', 'updated_at']



