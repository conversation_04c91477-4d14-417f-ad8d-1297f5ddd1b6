from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from knowledge_map.models import Project
from django.utils.html import format_html
from django.contrib import messages
from django.http import HttpResponseRedirect
import json
from .models import (
    Company, User, CompanyIntegration, IntegrationTool, MonitoredItem,
    SlackUserProfile, AtlassianUserProfile, NotificationSettings,
    CrossRepoMonitor, RepoChange, TeamInvitation, ActiveSearchPlatforms,
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'email', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'email']
    prepopulated_fields = {'slug': ('name',)}
    
    def get_urls(self):
        """Add custom URLs for admin actions"""
        from django.urls import path
        from .admin_views import create_company_admin_view
        
        urls = super().get_urls()
        custom_urls = [
            path('create-company-admin/', 
                 self.admin_site.admin_view(create_company_admin_view), 
                 name='integrations_company_create_company_admin'),
        ]
        return custom_urls + urls
    
    def changelist_view(self, request, extra_context=None):
        """Add custom context to company list view"""
        extra_context = extra_context or {}
        extra_context['show_create_company_admin_link'] = True
        return super().changelist_view(request, extra_context=extra_context)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ['id', 'email', 'first_name', 'last_name', 'company', 'role', 'is_active']
    list_filter = ['role', 'is_active', 'company']
    search_fields = ['email', 'first_name', 'last_name', 'company__name']
    ordering = ('email',)
    actions = ['send_welcome_email']
    
    fieldsets = (
        ('Authentication', {
            'fields': ('email', 'password')
        }),
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'phone')
        }),
        ('Company & Role', {
            'fields': ('company', 'role')
        }),
        ('Status', {
            'fields': ('is_active', 'is_staff', 'is_superuser')
        }),
    )
    
    add_fieldsets = (
        ('Required Information', {
            'classes': ('wide',),
            'fields': ('email', 'first_name', 'last_name', 'password1', 'password2', 'company', 'role')
        }),
    )
    
    def send_welcome_email(self, request, queryset):
        """Send welcome email to selected users with password reset instructions"""
        from .services.django_auth_service import DjangoAuthService
        
        auth_service = DjangoAuthService()
        success_count = 0
        error_count = 0
        
        for user in queryset:
            try:
                # Only send to active users with companies
                if not user.is_active or not user.company:
                    messages.warning(request, f"Skipped {user.email} - user must be active and have a company")
                    continue
                
                # Send password reset email instead of connection email
                success = auth_service.send_password_reset_email(user)
                
                if success:
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                messages.error(request, f"Failed to send email to {user.email}: {str(e)}")
        
        if success_count > 0:
            messages.success(request, f"Successfully sent password reset emails to {success_count} user(s)")
        if error_count > 0:
            messages.warning(request, f"Failed to send emails to {error_count} user(s)")
    
    send_welcome_email.short_description = "📧 Send password reset email"
    
    def save_model(self, request, obj, form, change):
        """Override save_model to show success message"""
        
        # Check if this is a new user before saving
        is_new_user = obj.pk is None
        
        # Save the user
        super().save_model(request, obj, form, change)
        
        # Show success message
        if is_new_user:
            messages.success(
                request, 
                f'✅ User "{obj.first_name} {obj.last_name}" created successfully! '
                f'Use "Send password reset email" action to send login instructions.'
            )
        elif change:
            messages.success(request, f'User "{obj.first_name} {obj.last_name}" updated successfully.')


@admin.register(IntegrationTool)
class IntegrationToolAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'is_active']
    list_filter = ['category', 'is_active']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}


@admin.register(CompanyIntegration)
class CompanyIntegrationAdmin(admin.ModelAdmin):
    list_display = ['id','company', 'tool', 'status', 'is_active', 'connected_at', 'config']
    list_filter = ['status', 'is_active', 'tool', 'company']
    search_fields = ['company__name', 'tool__name']
    readonly_fields = ['config']


@admin.register(MonitoredItem)
class MonitoredItemAdmin(admin.ModelAdmin):
    list_display = ("id", "company", "type", "name", "url", "repo", "created_at")
    list_filter = ("company", "type")
    search_fields = ("name", "url", "repo")


@admin.register(SlackUserProfile)
class SlackUserProfileAdmin(admin.ModelAdmin):
    def get_fieldsets(self, request, obj=None):
        # Dynamically show all model fields
        fields = [field.name for field in self.model._meta.fields]
        return [(None, {'fields': fields})]

    list_display = [field.name for field in SlackUserProfile._meta.fields]
    search_fields = ["user__email", "slack_user_id"]
    list_filter = ["is_active", "created_at"]
    readonly_fields = ("created_at", "updated_at")


@admin.register(TeamInvitation)
class TeamInvitationAdmin(admin.ModelAdmin):
    list_display = ['email', 'company', 'role', 'status', 'invited_by', 'created_at', 'expires_at', 'is_expired']
    list_filter = ['status', 'role', 'company', 'created_at']
    search_fields = ['email', 'company__name', 'invited_by__email']
    ordering = ['-created_at']
    readonly_fields = ['token', 'created_at', 'accepted_at']
    
    fieldsets = (
        ('Invitation Details', {
            'fields': ('company', 'email', 'role', 'status')
        }),
        ('Invitation Meta', {
            'fields': ('invited_by', 'token', 'created_at', 'expires_at', 'accepted_at')
        }),
    )
    
    def is_expired(self, obj):
        return obj.is_expired()
    is_expired.boolean = True
    is_expired.short_description = 'Expired'
    def external_repos_count(self, obj):
        return len(obj.external_repos)
    external_repos_count.short_description = 'External Repos Count'


@admin.register(RepoChange)
class RepoChangeAdmin(admin.ModelAdmin):
    list_display = ("id", "company", "internal_repo", "external_repo", "commit_id", "status", "created_at", "approved_by", "approved_at")
    list_filter = ("company", "status", "internal_repo", "external_repo", "created_at")
    search_fields = ("internal_repo", "external_repo", "commit_id", "summary")
    readonly_fields = ("created_at", "updated_at")
    actions = ["delete_selected", "delete_all_repo_changes"]

    def delete_all_repo_changes(self, request, queryset):
        total = RepoChange.objects.count()
        if 'apply' in request.POST:
            RepoChange.objects.all().delete()
            self.message_user(request, f"All {total} RepoChange records have been deleted.", messages.SUCCESS)
            return
        return admin.helpers.render_to_response(
            request,
            "admin/delete_all_confirmation.html",
            context={
                'title': 'Are you sure?',
                'objects_name': 'all RepoChange records',
                'action_checkbox_name': admin.helpers.ACTION_CHECKBOX_NAME,
                'opts': self.model._meta,
                'app_label': self.model._meta.app_label,
                'action_name': 'delete_all_repo_changes',
                'total': total,
            }
        )
    delete_all_repo_changes.short_description = "Delete ALL RepoChange records (dangerous)"


@admin.register(CrossRepoMonitor)
class CrossRepoMonitorAdmin(admin.ModelAdmin):
    list_display = ('company', 'internal_repo', 'external_repos_count', 'created_at')
    list_filter = ('company', 'created_at')
    search_fields = ('internal_repo', 'company__name')
    readonly_fields = ('created_at',)
    
    def external_repos_count(self, obj):
        return len(obj.external_repos)
    external_repos_count.short_description = 'External Repos Count'


@admin.register(NotificationSettings)
class NotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ['company', 'frequency_hours', 'enabled', 'last_notification_time', 'updated_at']
    list_filter = ['enabled', 'frequency_hours', 'updated_at']
    search_fields = ['company__name']
    ordering = ['company__name']
    
    fieldsets = (
        ('Company', {
            'fields': ('company',)
        }),
        ('Notification Control', {
            'fields': ('frequency_hours', 'enabled', 'last_notification_time'),
            'description': 'Set frequency_hours to 0 for no limit'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_notification_time', 'created_at', 'updated_at']

# Register AtlassianUserProfile in admin
@admin.register(AtlassianUserProfile)
class AtlassianUserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'account_id', 'email', 'refresh_token', 'access_token')
    search_fields = ('user__email', 'account_id', 'email', 'refresh_token', 'access_token')


@admin.register(ActiveSearchPlatforms)
class ActiveSearchPlatformsAdmin(admin.ModelAdmin):
    list_display = ['user', 'platforms_count']
    search_fields = ['user__email', 'user__first_name', 'user__last_name']
    ordering = ['user__email']
    
    def platforms_count(self, obj):
        return len(obj.platforms)
    platforms_count.short_description = 'Platforms Count'
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Platforms', {
            'fields': ('platforms',),
            'description': 'List of active search platform names for this user'
        }),
    )
