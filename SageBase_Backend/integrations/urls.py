from django.urls import path, include
from . import views
from . import invitation_views
from . import django_auth_views
from . import django_native_auth_views
from .github.api import app_webhook_receiver

urlpatterns = [
    # Company and user management (specific patterns first)
    path("company-users/", views.company_users, name="company-users"),
    path("get-user-by-email/", views.get_user_by_email, name="get-user-by-email"),
    path("company-users/<str:user_id>/", views.company_user_detail, name="company-user-detail"),
    path("company-integrations/", views.company_integrations, name="company-integrations"),
    path("github-company-repos/", views.github_company_repos, name="github-company-repos"),
    path("company-monitored-items/", views.company_monitored_items, name="company-monitored-items"),
    path("company-monitored-items/<str:item_id>/", views.monitored_item_detail, name="monitored-item-detail"),
    
    # Integration endpoints (specific patterns first)
    path("github/", include("integrations.github.urls")),
    path("discord/", include("integrations.discord.urls")),
    path("google-drive/", include("integrations.google_drive.urls")),
    path("confluence/", include("integrations.confluence.urls")),
    
    # Catch-all pattern must come LAST
    path("<provider_name>/", include("integrations.slack.urls")),
    
    # Django authentication endpoints
    path("django-login/", django_auth_views.django_login, name="django-login"),
    path("django-verify-token/", django_auth_views.django_verify_token, name="django-verify-token"),
    
    # Django native authentication endpoints
    path("auth/login/", django_native_auth_views.login, name="auth-login"),
    path("auth/register/", django_native_auth_views.register, name="auth-register"),
    path("auth/verify-email/", django_native_auth_views.verify_email, name="auth-verify-email"),
    path("auth/request-password-reset/", django_native_auth_views.request_password_reset, name="auth-request-password-reset"),
    path("auth/validate-reset-token/", django_native_auth_views.validate_reset_token, name="auth-validate-reset-token"),
    path("auth/reset-password/", django_native_auth_views.reset_password, name="auth-reset-password"),
    path("auth/resend-verification/", django_native_auth_views.resend_verification_email, name="auth-resend-verification"),
    path("auth/create-company-admin/", django_native_auth_views.create_company_admin, name="auth-create-company-admin"),
    path("auth/accept-invitation/", django_native_auth_views.accept_invitation, name="auth-accept-invitation"),
    
    # Team invitation endpoints
    path("invite-team-member/", invitation_views.invite_team_member, name="invite-team-member"),
    path("invitation/<str:token>/", invitation_views.get_invitation_details, name="get-invitation-details"),
    path("invitation/<str:token>/accept/", invitation_views.accept_invitation, name="accept-invitation"),
    path("invitations/", invitation_views.invitations_management, name="invitations-management"),
    
    # Monitoring and notifications
    path("company-cross-repo-monitors/", app_webhook_receiver.company_cross_repo_monitor_create),
    path("process_external_commit/", views.process_external_commit, name="process_external_commit"),
    path("notification-settings/", views.notification_settings, name="notification-settings"),
    path("user-profile/", views.user_profile, name="user-profile"),
    
    # Active search platforms
    path("active-search-platforms/", views.active_search_platforms, name="active-search-platforms"),

    #add endpoint for firing slack notifications
    path("notifications/slack/user-connection/", views.slack_user_connection_notification, name="slack-user-connection-notification"),
    
]


