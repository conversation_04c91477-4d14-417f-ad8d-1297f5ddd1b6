from django.urls import path, include
from . import views, views_test
from . import invitation_views
from . import django_auth_views
from . import supabase_centric_views
from .github.api import app_webhook_receiver

urlpatterns = [    
    path("github/", include("integrations.github.urls")),
    path("discord/", include("integrations.discord.urls")),
    path("google-drive/", include("integrations.google_drive.urls")),
    path("company-users/", views.company_users, name="company-users"),
    path("get-user-by-email/", views.get_user_by_email, name="get-user-by-email"),
    path("company-users/<str:user_id>/", views.company_user_detail, name="company-user-detail"),
    path("company-integrations/", views.company_integrations, name="company-integrations"),
    path("company-monitored-items/", views.company_monitored_items, name="company-monitored-items"),
    path("company-monitored-items/<str:item_id>/", views.monitored_item_detail, name="monitored-item-detail"),
    
    # Team invitation endpoints
    path("create-company-admin-supabase/", invitation_views.create_company_admin_supabase, name="create-company-admin-supabase"),
    path("invite-team-member/", invitation_views.invite_team_member, name="invite-team-member"),
    path("invitation/<str:token>/", invitation_views.get_invitation_details, name="get-invitation-details"),
    path("invitation/<str:token>/accept/", invitation_views.accept_invitation, name="accept-invitation"),
    path("invitation/<str:token>/accept-supabase/", invitation_views.accept_invitation_supabase, name="accept-invitation-supabase"),
    path("invitations/", invitation_views.list_company_invitations, name="list-company-invitations"),
    
    # Django authentication endpoints (alternative to Supabase)
    path("django-login/", django_auth_views.django_login, name="django-login"),
    path("django-verify-token/", django_auth_views.django_verify_token, name="django-verify-token"),
    
    # Supabase-centric workflow endpoints (YOUR PREFERRED WORKFLOW)
    # Note: Company + Admin creation now handled via Django Admin Panel UI (integrations/admin_views.py)
    path("django-admin/companies/", supabase_centric_views.list_companies_and_admins, name="list-companies-and-admins"),
    path("django-admin/resend-admin-email/", supabase_centric_views.resend_admin_connection_email, name="resend-admin-connection-email"),
    path("admin/invite-team-member-supabase/", supabase_centric_views.admin_invite_team_member_supabase, name="admin-invite-team-member-supabase"),
    path("admin/delete-user-supabase/", supabase_centric_views.delete_user_with_supabase_cleanup, name="delete-user-supabase"),
    
    path("<provider_name>/", include("integrations.slack.urls")),
    path('company-cross-repo-monitors/', app_webhook_receiver.company_cross_repo_monitor_create),

    path('process_external_commit/', views.process_external_commit, name='process_external_commit'),
    path('notification-settings/', views.notification_settings, name='notification-settings'),
    
    
    path("test-trigger-event/", views_test.test_trigger_event, name="test-trigger-event"),#just for testing
    #example curl -X GET "http://localhost:8001/api/integrations/test-trigger-event/"
    

]
