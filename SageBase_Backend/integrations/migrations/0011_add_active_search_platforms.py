# Generated by Django 4.2 on 2025-08-25 19:16

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("integrations", "0010_atlassianuserprofile"),
    ]

    operations = [
        migrations.CreateModel(
            name="ActiveSearchPlatforms",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "platforms",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100),
                        blank=True,
                        default=list,
                        help_text="List of active search platform names for this user",
                        size=None,
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="active_search_platforms",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Active Search Platforms",
                "ordering": ["user__email"],
            },
        ),
        migrations.RemoveField(
            model_name="projectmetrics",
            name="project",
        ),
        migrations.DeleteModel(
            name="ProjectContributor",
        ),
        migrations.DeleteModel(
            name="ProjectMetrics",
        ),
    ]
