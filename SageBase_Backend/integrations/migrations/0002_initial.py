# Generated by Django 5.2.4 on 2025-07-27 08:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('integrations', '0001_initial'),
        ('knowledge_map', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='projectcontributor',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contributors', to='knowledge_map.project'),
        ),
        migrations.AddField(
            model_name='projectcontributor',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_contributions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projectmetrics',
            name='project',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='knowledge_map.project'),
        ),
        migrations.AddField(
            model_name='slackuserprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='slack_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='teamsuserprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='teams_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='companyintegration',
            unique_together={('company', 'tool')},
        ),
        migrations.AlterUniqueTogether(
            name='projectcontributor',
            unique_together={('project', 'user')},
        ),
    ]
