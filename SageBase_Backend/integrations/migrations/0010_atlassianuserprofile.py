# Generated by Django 4.2 on 2025-08-18 07:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("integrations", "0009_update_team_invitation_invited_by_foreign_key"),
    ]

    operations = [
        migrations.CreateModel(
            name="AtlassianUserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "account_id",
                    models.<PERSON>r<PERSON><PERSON>(help_text="Atlassian account ID", max_length=100),
                ),
                (
                    "email",
                    models.EmailField(
                        help_text="Atlassian account email", max_length=254
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        blank=True,
                        help_text="User's display name",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "picture",
                    models.URLField(
                        blank=True, help_text="Profile picture URL", null=True
                    ),
                ),
                (
                    "nickname",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="User's nickname",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "domain",
                    models.CharField(
                        blank=True,
                        help_text="Atlassian domain/site",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "cloud_id",
                    models.CharField(
                        blank=True,
                        help_text="Confluence cloud ID",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "confluence_url",
                    models.URLField(
                        blank=True, help_text="Confluence instance URL", null=True
                    ),
                ),
                (
                    "access_token",
                    models.TextField(help_text="OAuth access token (encrypted)"),
                ),
                (
                    "refresh_token",
                    models.TextField(
                        blank=True,
                        help_text="OAuth refresh token (encrypted)",
                        null=True,
                    ),
                ),
                (
                    "token_expires_at",
                    models.DateTimeField(
                        blank=True, help_text="Access token expiration time", null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="atlassian_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
