# Generated by Django 5.2.4 on 2025-08-03 07:33

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0005_repochange_cross_repo_monitor'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('frequency_hours', models.IntegerField(default=0, help_text='Minimum hours between notifications (0 = no limit)')),
                ('enabled', models.BooleanField(default=True, help_text='Whether notifications are enabled for this company')),
                ('last_notification_time', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to='integrations.company')),
            ],
            options={
                'verbose_name_plural': 'Notification Settings',
                'ordering': ['company__name'],
            },
        ),
    ]
