"""
Django-integrated Confluence service that uses Django ChromaDB for processing and emb                 logger.info(f"🔐 Using OAuth2 Bearer token for spaces discovery")
            else:             logger.info(f"🔐 Using OAuth2 Bearer token for spaces discovery")
            else:ding.
This replaces the MCP server's embedding functionality while keeping its document fetching capabilities.
"""
import os
import sys
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

# Django imports
from django.conf import settings
from vectordb.models.document import DataSource

logger = logging.getLogger(__name__)


class DjangoConfluenceService:
    """
    Confluence service that uses MCP server for document fetching 
    and Django ChromaDB for preprocessing, embedding, and storage.
    """
    
    def __init__(self):
        self.mcp_server_path = Path(__file__).parent.parent / "mcp_server" / "documents-vector-search"
    
    def _get_v2_api_url(self, confluence_url: str, cloud_id: str = None) -> str:
        """
        Convert any Confluence URL to v2 API format.
        v2 API format: https://your-domain.atlassian.net/wiki/api/v2/spaces
        """
        # If it's already in instance URL format, use it directly
        if '.atlassian.net' in confluence_url and not confluence_url.startswith('https://api.atlassian.com'):
            instance_url = confluence_url.rstrip('/')
            return f"{instance_url}/wiki/api/v2/spaces"
        
        # If it's OAuth2 gateway URL, extract cloud_id and use fallback
        if 'api.atlassian.com/ex/confluence/' in confluence_url:
            if not cloud_id:
                cloud_id = confluence_url.split('/ex/confluence/')[-1].split('/')[0]
            # Use fallback instance URL (this should be dynamic in production)
            instance_url = "https://sagebase-tech.atlassian.net"
            return f"{instance_url}/wiki/api/v2/spaces"
        
        # Default fallback
        return f"{confluence_url.rstrip('/')}/wiki/api/v2/spaces"
        
    def get_user_accessible_spaces(self, confluence_email: str, confluence_api_token: str, cloud_id: str, confluence_url: str = None) -> List[Dict[str, Any]]:
        """
        Fetch all Confluence spaces that the user has access to using the v2 API endpoint.

        Args:
            confluence_email: User email for Confluence
            confluence_api_token: OAuth2 Bearer token for Confluence
            cloud_id: Cloud ID for the Confluence instance
            confluence_url: Optional Confluence instance URL (for compatibility)

        Returns:
            List of space dictionaries with key, name, and type (never None)
        """
        try:
            return self._fetch_accessible_spaces_internal(confluence_email, confluence_api_token, cloud_id, confluence_url)
        except Exception as e:
            logger.error(f"❌ Fatal error in get_user_accessible_spaces: {e}")
            logger.exception("Full traceback:")
            return []  # Always return empty list instead of None
    
    def _fetch_accessible_spaces_internal(self, confluence_email: str, confluence_api_token: str, cloud_id: str, confluence_url: str = None) -> List[Dict[str, Any]]:
        """
        Internal method to fetch accessible spaces with detailed error handling.
        """
        import requests

        logger.info(f"🔍 Fetching accessible spaces for user: {confluence_email}")
        logger.info(f"🔧 Space discovery parameters:")
        logger.info(f"   • Confluence URL: {confluence_url}")
        logger.info(f"   • Cloud ID: {cloud_id}")

        # For v2 spaces API, we need to use the OAuth2 API format
        # But for v1 document fetching API, we use the original instance URL
        if cloud_id:
            # Use OAuth2 API format for v2 spaces API (this works as confirmed by tests)
            spaces_url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/api/v2/spaces"
            logger.info(f"📋 Using OAuth2 API format for spaces API: {spaces_url}")
        else:
            logger.error(f"❌ Cannot construct spaces URL - no cloud_id provided")
            return []

        headers = {"Authorization": f"Bearer {confluence_api_token}"}
        logger.info(f"🔑 Using OAuth2 Bearer token for authentication")

        all_spaces = []
        start = 0
        limit = 50

        while True:
            params = {
                'start': start,
                'limit': limit,
                'expand': 'description,permissions'
            }

            try:
                logger.debug(f"🔄 Making API request to: {spaces_url}")
                logger.debug(f"   Parameters: {params}")
                
                response = requests.get(spaces_url, headers=headers, params=params, timeout=30)
                
                logger.debug(f"📊 API Response:")
                logger.debug(f"   Status Code: {response.status_code}")
                logger.debug(f"   Headers: {dict(response.headers)}")
                logger.debug(f"   Content Length: {len(response.content)}")
                logger.debug(f"   Content Type: {response.headers.get('content-type', 'unknown')}")
                
                response.raise_for_status()

                # Debug the raw response before parsing
                raw_content = response.text
                logger.debug(f"📄 Raw response content (first 500 chars): {raw_content[:500]}")
                
                if not raw_content.strip():
                    logger.error(f"❌ Empty response body from API")
                    break
                
                try:
                    data = response.json()
                    logger.debug(f"📋 Parsed JSON data type: {type(data)}")
                    if data is None:
                        logger.error(f"❌ JSON parsing returned None")
                        break
                    logger.debug(f"📋 JSON data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                except ValueError as json_error:
                    logger.error(f"❌ JSON parsing failed: {json_error}")
                    logger.error(f"   Raw content: {raw_content}")
                    break
                
                spaces = data.get('results', []) if data else []
                logger.info(f"📊 Found {len(spaces)} spaces in API response")
                
                # Log all spaces before filtering
                if spaces:
                    logger.info(f"📋 All spaces found in this batch:")
                    for i, space in enumerate(spaces):
                        space_key = space.get('key', 'NO_KEY')
                        space_name = space.get('name', 'NO_NAME')
                        space_status = space.get('status', 'NO_STATUS')
                        space_type = space.get('type', 'NO_TYPE')
                        logger.info(f"   Space {i+1}: {space_key} - {space_name} (status: {space_status}, type: {space_type})")

                if not spaces:
                    logger.info(f"🔚 No more spaces found, ending pagination")
                    break

                for i, space in enumerate(spaces):
                    logger.debug(f"🔍 Processing space {i+1}/{len(spaces)}:")
                    logger.debug(f"   Raw space data: {space}")
                    
                    space_info = {
                        'key': space.get('key'),
                        'name': space.get('name'),
                        'type': space.get('type'),
                        'description': space.get('description', {}).get('plain', '') if space.get('description') else '',
                        'status': space.get('status', 'current')  # Default to current
                    }
                    
                    logger.debug(f"   Parsed space info: {space_info}")
                    
                    # Add all spaces without status filtering (like the successful views.py test)
                    if space_info['key']:  # Only require that the space has a key
                        all_spaces.append(space_info)
                        logger.info(f"✅ Added space to results: {space_info['key']} ({space_info['name']})")
                    else:
                        logger.warning(f"⚠️ Skipped space without key: {space_info['name']}")

                if len(spaces) < limit:
                    break

                start += limit

            except requests.RequestException as e:
                logger.error(f"❌ Error fetching spaces: {e}")
                logger.error(f"   Request URL: {spaces_url}")
                logger.error(f"   Request params: start={start}, limit={limit}")
                break
            except Exception as e:
                logger.error(f"❌ Unexpected error fetching spaces: {e}")
                logger.error(f"   Request URL: {spaces_url}")
                break

        logger.info(f"✅ Found {len(all_spaces)} accessible spaces: {[s['key'] for s in all_spaces]}")
        
        # Ensure we always return a list, never None
        if all_spaces is None:
            logger.warning(f"⚠️ Spaces list is None, returning empty list")
            return []
            
        return all_spaces
    
    def sync_confluence_documents(self, 
                                confluence_email: str,
                                confluence_api_token: str,
                                confluence_url: str = None,
                                space_key: str = "DEV",
                                workspace: str = None,
                                user_id: str = None,
                                cloud_id: str = None,
                                company_id: str = None) -> Dict[str, Any]:
        """
        Enhanced sync method with user context and better error handling
        
        Args:
            confluence_email: User email for Confluence
            confluence_api_token: API token for Confluence  
            confluence_url: Confluence instance URL
            space_key: Space to sync
            workspace: Workspace/tenant identifier
            user_id: User identifier for tracking
            cloud_id: Atlassian Cloud ID for OAuth2 URLs
            
        Returns:
            Dict with sync results
        """
        try:
            import time
            sync_start_time = time.time()
            
            logger.info(f"🚀 Starting Confluence sync for user {user_id}, workspace {workspace}")
            logger.info(f"📋 Sync parameters:")
            logger.info(f"   • Space: {space_key}")
            logger.info(f"   • User: {confluence_email}")
            logger.info(f"   • Workspace: {workspace}")
            logger.info(f"   • Cloud ID: {cloud_id}")
            
            # Set default URL if not provided
            if not confluence_url:
                confluence_url = "https://sagebase-tech.atlassian.net"
            
            # Track sync progress
            sync_details = {
                'documents_fetched': 0,
                'documents_converted': 0,
                'chunks_created': 0,
                'embeddings_generated': 0,
                'documents_indexed': 0,
                'processing_time': 0
            }
            
            # Step 1: Use MCP server to fetch and process documents
            logger.info(f"📥 Step 1: Fetching documents from Confluence space '{space_key}'...")
            fetch_start_time = time.time()
            
            raw_documents = self._fetch_documents_via_mcp(
                confluence_email=confluence_email,
                confluence_api_token=confluence_api_token,
                confluence_url=confluence_url,
                space_key=space_key,
                cloud_id=cloud_id
            )
            
            fetch_time = time.time() - fetch_start_time
            sync_details['documents_fetched'] = len(raw_documents) if raw_documents else 0
            
            logger.info(f"✅ Step 1 Complete: Fetched {sync_details['documents_fetched']} documents in {fetch_time:.2f}s")
            
            if not raw_documents:
                logger.warning(f"⚠️ No documents found in space {space_key}")
                return {
                    'status': 'warning',
                    'message': f'No documents found in space {space_key}',
                    'documents_processed': 0,
                    'workspace': workspace,
                    'sync_details': sync_details
                }
            
            # Step 2: Process documents through Django ChromaDB
            logger.info(f"🔄 Step 2: Processing {len(raw_documents)} documents through vector database...")
            processing_start_time = time.time()
            
            # Company ID MUST be provided (no fallback allowed)
            if not company_id:
                logger.error(f"❌ Company ID is required for Confluence embedding")
                logger.error(f"   user_id: {user_id}, confluence_email: {confluence_email}")
                return {
                    'status': 'error',
                    'message': 'Company ID is required for Confluence embedding',
                    'documents_processed': 0,
                    'workspace': workspace,
                    'error': 'Company ID not provided'
                }
            
            # User ID MUST be provided (no fallback allowed)
            if not user_id:
                logger.error(f"❌ User ID is required for Confluence embedding")
                logger.error(f"   company_id: {company_id}, confluence_email: {confluence_email}")
                return {
                    'status': 'error',
                    'message': 'User ID is required for Confluence embedding',
                    'documents_processed': 0,
                    'workspace': workspace,
                    'error': 'User ID not provided'
                }
            
            processed_count = self._process_documents_with_django(
                raw_documents=raw_documents,
                workspace=workspace or f"user_{user_id}",
                confluence_url=confluence_url,
                sync_details=sync_details,  # Pass sync_details to track progress
                company_id=company_id
            )
            
            processing_time = time.time() - processing_start_time
            total_time = time.time() - sync_start_time
            sync_details['processing_time'] = total_time
            
            logger.info(f"✅ Step 2 Complete: Processed {processed_count} documents in {processing_time:.2f}s")
            logger.info(f"🎉 Sync Complete: Total time {total_time:.2f}s")
            logger.info(f"📊 Final Results:")
            logger.info(f"   • Documents fetched: {sync_details['documents_fetched']}")
            logger.info(f"   • Documents converted: {sync_details['documents_converted']}")
            logger.info(f"   • Text chunks created: {sync_details['chunks_created']}")
            logger.info(f"   • Embeddings generated: {sync_details['embeddings_generated']}")
            logger.info(f"   • Documents indexed: {sync_details['documents_indexed']}")
            
            return {
                'status': 'success',
                'message': f'Successfully synced {processed_count} documents from {space_key}',
                'documents_processed': processed_count,
                'workspace': workspace,
                'space_key': space_key,
                'sync_details': sync_details
            }
            
        except Exception as e:
            total_time = time.time() - sync_start_time if 'sync_start_time' in locals() else 0
            logger.exception(f"❌ Confluence sync failed for user {user_id}: {e}")
            logger.error(f"💥 Sync failed after {total_time:.2f}s")
            
            # Try to include partial progress if available
            error_details = {
                'failed_step': 'unknown',
                'error_type': type(e).__name__,
                'partial_results': sync_details if 'sync_details' in locals() else {}
            }
            
            # Try to determine which step failed
            if 'raw_documents' not in locals():
                error_details['failed_step'] = 'document_fetching'
            elif 'processed_count' not in locals():
                error_details['failed_step'] = 'document_processing'
            else:
                error_details['failed_step'] = 'unknown'
            
            logger.error(f"📋 Error occurred during: {error_details['failed_step']}")
            if error_details['partial_results']:
                partial = error_details['partial_results']
                logger.error(f"📊 Partial progress before failure:")
                logger.error(f"   • Documents fetched: {partial.get('documents_fetched', 0)}")
                logger.error(f"   • Documents converted: {partial.get('documents_converted', 0)}")
                logger.error(f"   • Text chunks created: {partial.get('chunks_created', 0)}")
                logger.error(f"   • Embeddings generated: {partial.get('embeddings_generated', 0)}")
            
            return {
                'status': 'error',
                'message': f'Sync failed: {str(e)}',
                'error': str(e),
                'documents_processed': 0,
                'workspace': workspace,
                'error_details': error_details
            }
    
    def _fetch_documents_via_mcp(self, 
                               confluence_email: str,
                               confluence_api_token: str,
                               confluence_url: str,
                               space_key: str,
                               cloud_id: str = None) -> List[Dict[str, Any]]:
        """
        Use MCP server to fetch raw documents from Confluence.
        Enhanced to handle OAuth2 tokens and different URL formats.
        Returns processed document data without embeddings.
        
        Args:
            confluence_email: User email
            confluence_api_token: OAuth2 Bearer token
            confluence_url: Confluence URL (will be converted to OAuth2 format if needed)
            space_key: Space key to sync
            cloud_id: Atlassian Cloud ID for OAuth2 URL conversion
        
        Based on MCP server documentation:
        - For Confluence Cloud with API tokens: Use ATLASSIAN_EMAIL + ATLASSIAN_TOKEN
        - For Confluence Server/Data Center or OAuth2 Bearer: Use CONF_TOKEN
        """
        # Add MCP server path to sys.path temporarily
        mcp_path = str(self.mcp_server_path)
        if mcp_path not in sys.path:
            sys.path.insert(0, mcp_path)
        
        # Set up environment variables for MCP server based on authentication type
        original_env = {}
        
        try:
            # Dynamic imports from MCP server (these will only resolve at runtime)
            # pylint: disable=import-outside-toplevel,import-error
            from main.sources.confluence.confluence_cloud_document_reader import ConfluenceCloudDocumentReader
            from main.sources.confluence.confluence_oauth2_document_reader import ConfluenceOAuth2DocumentReader
            from main.sources.confluence.confluence_document_converter import ConfluenceDocumentConverter
            
            # Determine authentication method based on URL and token format
            is_oauth2_token = len(confluence_api_token) > 50  # OAuth2 tokens are typically much longer
            is_oauth2_url = 'api.atlassian.com' in confluence_url
            
            logger.info(f"🔍 Authentication detection:")
            logger.info(f"   URL: {confluence_url}")
            logger.info(f"   Token length: {len(confluence_api_token)}")
            logger.info(f"   Is OAuth2 URL: {is_oauth2_url}")
            logger.info(f"   Is OAuth2 Token: {is_oauth2_token}")
            
            # ALWAYS use OAuth2 for long tokens (OAuth2 Bearer tokens)
            # Convert URL format if necessary
            if is_oauth2_token:
                logger.info("🔐 Using OAuth2 Bearer token configuration (forced for long tokens)")
                
                # Convert to OAuth2 API format if we have cloud_id
                if cloud_id and not is_oauth2_url:
                    original_confluence_url = confluence_url
                    confluence_url = f"https://api.atlassian.com/ex/confluence/{cloud_id}"
                    logger.info(f"🔄 Converted URL for OAuth2 token:")
                    logger.info(f"   From: {original_confluence_url}")
                    logger.info(f"   To: {confluence_url}")
                elif not cloud_id:
                    logger.error(f"❌ Cannot convert URL - no cloud_id provided")
                    logger.error(f"   Current URL: {confluence_url}")
                    logger.error(f"   OAuth2 tokens require cloud_id for proper URL format")
                    
                # Configure environment for OAuth2 Bearer token (Server/Data Center mode)
                original_env['CONF_TOKEN'] = os.environ.get('CONF_TOKEN', '')
                os.environ['CONF_TOKEN'] = confluence_api_token
                
                # Debug: Log what token is being set
                logger.info(f"🔧 Setting CONF_TOKEN environment variable")
                logger.info(f"🔑 Token length: {len(confluence_api_token)}")
                logger.info(f"🔗 Confluence URL for MCP: {confluence_url}")
                
                # Clear API token env vars if they exist
                if 'ATLASSIAN_EMAIL' in os.environ:
                    original_env['ATLASSIAN_EMAIL'] = os.environ['ATLASSIAN_EMAIL']
                    del os.environ['ATLASSIAN_EMAIL']
                if 'ATLASSIAN_TOKEN' in os.environ:
                    original_env['ATLASSIAN_TOKEN'] = os.environ['ATLASSIAN_TOKEN']
                    del os.environ['ATLASSIAN_TOKEN']
                
                # Use the OAuth2 API format URL that we know works (from views.py tests)
                oauth2_api_url = confluence_url  # This is already in OAuth2 format from our conversion above
                
                logger.info(f"🔧 OAuth2 Configuration:")
                logger.info(f"   CONF_TOKEN: Set (Bearer token)")
                logger.info(f"   OAuth2 API URL: {oauth2_api_url}")
                logger.info(f"   Cloud ID: {cloud_id or 'Not available'}")
                logger.info(f"   Using OAuth2 Document Reader with OAuth2 API format")
                
                # Pass the OAuth2 API URL - this is confirmed to work from views.py tests
                document_reader = ConfluenceOAuth2DocumentReader(
                    base_url=oauth2_api_url,  # Use OAuth2 API format for all API calls
                    query=f"space = '{space_key}' AND type = 'page'",
                    access_token=confluence_api_token,
                    cloud_id=cloud_id,
                    read_all_comments=False  # Disable comments for OAuth2 to avoid 410 Gone errors
                )
            else:
                # This branch should not be used anymore since we only support OAuth2
                logger.error("❌ Non-OAuth2 tokens are not supported in this configuration")
                raise ValueError("Only OAuth2 Bearer tokens are supported. Please use OAuth2 authentication.")
            
            document_converter = ConfluenceDocumentConverter()
            
            # Fetch documents using MCP server
            raw_documents = []
            logger.info(f"Fetching documents from Confluence space: {space_key}")
            
            for document in document_reader.read_all_documents():
                # Convert document using MCP converter
                converted_docs = document_converter.convert(document)
                raw_documents.extend(converted_docs)
            
            logger.info(f"Fetched {len(raw_documents)} documents from Confluence")
            return raw_documents
            
        except ImportError as ie:
            logger.error(f"MCP server components not available: {ie}")
            raise Exception(f"MCP server components not found. Ensure MCP server is properly installed: {ie}")
        except Exception as e:
            logger.exception(f"Error fetching documents via MCP server: {e}")
            raise
        finally:
            # Restore original environment variables
            for key, value in original_env.items():
                if value:  # Only restore if original value existed
                    os.environ[key] = value
                elif key in os.environ:  # Remove if we added it and original didn't exist
                    del os.environ[key]
                    
            # Clean up sys.path
            if mcp_path in sys.path:
                sys.path.remove(mcp_path)
    
    def _process_documents_with_django(self, 
                                     raw_documents: List[Dict[str, Any]],
                                     workspace: str,
                                     confluence_url: str,
                                     sync_details: Dict[str, Any] = None,
                                     company_id: str = None) -> int:
        """
        Process raw documents using Django ChromaDB interfaces.
        Handles text processing, chunking, embedding, and storage.
        
        Args:
            raw_documents: List of raw document dictionaries from MCP server
            workspace: Workspace identifier
            confluence_url: Confluence instance URL
            sync_details: Dictionary to track sync progress
            company_id: Company ID to use as collection name (for search_local_knowledge compatibility)
        """
        import time
        
        processed_count = 0
        total_docs = len(raw_documents)
        total_chunks = 0
        total_embeddings = 0
        
        if sync_details is None:
            sync_details = {}
        
        logger.info(f"🔄 Processing {total_docs} documents through vector database pipeline...")
        
        # Company ID MUST be provided (no fallback allowed)
        if not company_id:
            logger.error(f"❌ Company ID is required for Confluence embedding")
            return 0
        
        collection_name = str(company_id)
        
        # Get collection manager using company ID as collection name (same as Google Drive)
        try:
            from vectordb.collections.collection_manager import get_collection_manager
            collection_manager = get_collection_manager(collection_name)
            logger.info(f"📦 Using collection manager for company collection: '{collection_name}'")
        except Exception as e:
            logger.error(f"❌ Failed to get collection manager for company '{collection_name}': {e}")
            return 0
        
        for i, doc in enumerate(raw_documents, 1):
            try:
                doc_start_time = time.time()
                doc_id = doc.get('id', f'unknown_{i}')
                
                logger.info(f"📄 Processing document {i}/{total_docs}: {doc_id}")
                
                # Extract document metadata
                logger.debug(f"   • Extracting metadata...")
                doc_metadata = self._extract_document_metadata(doc, workspace, confluence_url)
                
                # Process each text chunk through Django ChromaDB
                full_text = doc.get('text', '')
                if not full_text.strip():
                    logger.warning(f"   ⚠️ Skipping empty document: {doc_id}")
                    continue
                
                logger.debug(f"   • Text length: {len(full_text)} characters")
                logger.debug(f"   • Title: {doc_metadata.get('title', 'Untitled')}")
                
                # Use collection manager's add_document method (similar to Google Drive pattern)
                # This handles: text cleaning, chunking, embedding, and storage
                logger.debug(f"   • Creating text chunks and embeddings...")
                chunk_start_time = time.time()
                
                document_ids = collection_manager.add_document(
                    content=full_text,
                    source=DataSource.CONFLUENCE,
                    source_id=doc['id'],
                    title=doc_metadata['title'],
                    author=doc_metadata.get('author'),
                    workspace=collection_name,  # Use company ID as workspace (same as Google Drive)
                    tags=doc_metadata.get('tags', []),
                    url=doc_metadata['url'],
                    content_type='confluence_page',
                    # Additional Confluence-specific metadata
                    space=doc_metadata.get('space'),
                    page_id=doc['id'],
                    modified_time=doc_metadata.get('modified_time')
                )
                
                chunk_time = time.time() - chunk_start_time
                doc_time = time.time() - doc_start_time
                
                chunks_created = len(document_ids) if document_ids else 0
                total_chunks += chunks_created
                total_embeddings += chunks_created  # Assuming 1 embedding per chunk
                
                processed_count += 1
                
                logger.info(f"   ✅ Processed in {doc_time:.2f}s:")
                logger.info(f"      • Text chunks: {chunks_created}")
                logger.info(f"      • Embeddings: {chunks_created}")
                logger.info(f"      • Chunk processing time: {chunk_time:.2f}s")
                
                # Update sync details
                sync_details['documents_converted'] = processed_count
                sync_details['chunks_created'] = total_chunks
                sync_details['embeddings_generated'] = total_embeddings
                sync_details['documents_indexed'] = processed_count
                
            except Exception as e:
                logger.exception(f"❌ Error processing document {doc.get('id', 'unknown')}: {e}")
                logger.error(f"   • Document {i}/{total_docs} failed")
                continue
        
        logger.info(f"✅ Document processing complete:")
        logger.info(f"   • Successfully processed: {processed_count}/{total_docs} documents")
        logger.info(f"   • Total text chunks created: {total_chunks}")
        logger.info(f"   • Total embeddings generated: {total_embeddings}")
        
        return processed_count
    
    def sync_multiple_spaces(self, confluence_profile, spaces: list = None):
        """
        Sync multiple Confluence spaces for a user
        
        Args:
            confluence_profile: ConfluenceUserProfile instance
            spaces: List of space keys to sync
            
        Returns:
            List of sync results for each space
        """
        spaces = spaces or ['DEV']  # Default spaces
        results = []
        
        for space_key in spaces:
            logger.info(f"Syncing space {space_key} for user {confluence_profile.user.id}")
            
            # Get company ID from user
            company_id = str(confluence_profile.user.company.id) if confluence_profile.user.company else None
            
            result = self.sync_confluence_documents(
                confluence_email=confluence_profile.user_email,
                confluence_api_token=confluence_profile.access_token,
                confluence_url=confluence_profile.confluence_url,
                space_key=space_key,
                workspace=f"user_{confluence_profile.user.id}",
                user_id=str(confluence_profile.user.id),
                company_id=company_id
            )
            results.append({
                'space_key': space_key,
                'result': result
            })
        
        return results
    
    def get_sync_progress(self, user_id: str, workspace: str = None) -> Dict[str, Any]:
        """
        Get sync progress information for debugging/monitoring
        
        Args:
            user_id: User identifier
            workspace: Workspace identifier
            
        Returns:
            Dict with progress information
        """
        try:
            from vectordb.interfaces import search
            from vectordb.models.document import DataSource
            
            workspace = workspace or f"user_{user_id}"
            
            # Search for any Confluence documents to check if sync has started
            results = search(
                query="*",  # Match any document
                sources=[DataSource.CONFLUENCE],
                workspace=workspace,
                limit=1
            )
            
            has_documents = len(results) > 0
            
            return {
                'workspace': workspace,
                'has_confluence_documents': has_documents,
                'sample_document_count': len(results)
            }
            
        except Exception as e:
            logger.exception(f"Error checking sync progress for user {user_id}: {e}")
            return {
                'workspace': workspace,
                'error': str(e),
                'has_confluence_documents': False
            }
    
    def _extract_document_metadata(self, doc: Dict[str, Any], workspace: str, confluence_url: str) -> Dict[str, Any]:
        """
        Extract and standardize metadata from MCP document format.
        Enhanced to handle OAuth2 URLs and better metadata extraction.
        """
        # Parse the hierarchical title (Parent -> Child -> Page)
        title_parts = doc.get('text', '').split('\n')[0] if doc.get('text') else ''
        if ' -> ' in title_parts:
            title = title_parts.split(' -> ')[-1]  # Get the actual page title
        else:
            title = title_parts or f"Confluence Page {doc.get('id', 'Unknown')}"
        
        # Extract space from URL or title hierarchy
        space = None
        if ' -> ' in title_parts:
            space_parts = title_parts.split(' -> ')
            if len(space_parts) > 1:
                space = space_parts[0]  # First part is usually the space
        
        # Handle different URL formats for OAuth2 vs API token
        doc_url = doc.get('url', '')
        if not doc_url:
            if 'api.atlassian.com' in confluence_url:
                # OAuth2 format - construct web URL from API URL
                # https://api.atlassian.com/ex/confluence/{cloud_id} -> https://your-domain.atlassian.net
                doc_url = f"{confluence_url}/wiki/pages/{doc.get('id', '')}"
            else:
                # Traditional format
                doc_url = f"{confluence_url}/pages/{doc.get('id', '')}"
        
        # Extract more metadata if available
        metadata = {
            'title': title.strip(),
            'url': doc_url,
            'space': space,
            'modified_time': doc.get('modifiedTime'),
            'created_time': doc.get('createdTime'),
            'tags': ['confluence', space] if space else ['confluence'],
            'author': doc.get('author', {}).get('displayName') if doc.get('author') else None,
            'page_id': doc.get('id', ''),
            'workspace': workspace,
        }
        
        # Add OAuth2-specific metadata if available
        if doc.get('version'):
            metadata['version'] = doc['version']
        if doc.get('status'):
            metadata['status'] = doc['status']
        
        return metadata
    
    def update_confluence_documents(self, 
                                  confluence_email: str,
                                  confluence_api_token: str,
                                  confluence_url: str = "https://sagebase-tech.atlassian.net",
                                  space_key: str = "DEV",
                                  workspace: str = None) -> Dict[str, Any]:
        """
        Update existing Confluence documents (incremental sync).
        For now, this performs a full sync, but could be optimized with change tracking.
        """
        logger.info(f"Updating Confluence documents for workspace: {workspace}")
        
        # For now, perform full sync
        # TODO: Implement incremental updates based on modification times
        return self.sync_confluence_documents(
            confluence_email=confluence_email,
            confluence_api_token=confluence_api_token,
            confluence_url=confluence_url,
            space_key=space_key,
            workspace=workspace,
            company_id=None  # This method doesn't have access to company_id, will be derived from user_id
        )

    def get_indexed_document_count(self, workspace: str) -> int:
        """
        Get the count of indexed documents for a specific workspace.
        
        Args:
            workspace: The workspace identifier
            
        Returns:
            Number of indexed documents
        """
        try:
            from interfaces_data.vector_db_interface import VectorDBInterface
            
            # Initialize vector DB interface
            vector_db = VectorDBInterface()
            
            # Query the collection to get document count
            # This assumes the collection name follows the workspace naming pattern
            collection_name = f"confluence_{workspace}"
            
            # Get collection info or count
            try:
                # Try to get collection metadata
                collection_info = vector_db.get_collection_info(collection_name)
                if collection_info:
                    return collection_info.get('document_count', 0)
            except:
                # Fallback: try to query documents directly
                try:
                    results = vector_db.search(
                        collection_name=collection_name,
                        query_text="*",  # Match all documents
                        top_k=1,  # We just want the count
                        get_count_only=True
                    )
                    return results.get('total_count', 0)
                except:
                    return 0
                    
            return 0
            
        except Exception as e:
            logger.error(f"❌ Error getting document count for workspace {workspace}: {e}")
            return 0
    
    def get_recent_indexed_documents(self, workspace: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get recently indexed documents for a workspace.
        
        Args:
            workspace: The workspace identifier
            limit: Maximum number of documents to return
            
        Returns:
            List of recent document metadata
        """
        try:
            from interfaces_data.vector_db_interface import VectorDBInterface
            
            # Initialize vector DB interface
            vector_db = VectorDBInterface()
            
            collection_name = f"confluence_{workspace}"
            
            # Query recent documents
            results = vector_db.search(
                collection_name=collection_name,
                query_text="*",  # Match all documents
                top_k=limit,
                include_metadata=True,
                sort_by="timestamp",  # Assuming we store timestamps
                sort_order="desc"
            )
            
            documents = []
            for result in results.get('results', []):
                metadata = result.get('metadata', {})
                documents.append({
                    'id': result.get('id', 'unknown'),
                    'title': metadata.get('title', 'Unknown Title'),
                    'url': metadata.get('url', ''),
                    'space': metadata.get('space', ''),
                    'modified_time': metadata.get('modifiedTime', ''),
                    'score': result.get('score', 0.0)
                })
            
            return documents
            
        except Exception as e:
            logger.error(f"❌ Error getting recent documents for workspace {workspace}: {e}")
            return []
    
    def list_confluence_workspaces(self) -> List[str]:
        """
        List all available Confluence workspaces in the vector database.
        
        Returns:
            List of workspace names
        """
        try:
            from interfaces_data.vector_db_interface import VectorDBInterface
            
            # Initialize vector DB interface
            vector_db = VectorDBInterface()
            
            # Get all collections and filter for Confluence ones
            all_collections = vector_db.list_collections()
            
            confluence_workspaces = []
            for collection in all_collections:
                if collection.startswith('confluence_'):
                    # Extract workspace name from collection name
                    workspace = collection.replace('confluence_', '')
                    confluence_workspaces.append(workspace)
            
            return confluence_workspaces
            
        except Exception as e:
            logger.error(f"❌ Error listing Confluence workspaces: {e}")
            return []

    def cleanup_all_confluence_documents(self, company_id: str) -> Dict[str, Any]:
        """
        Remove all Confluence sourced documents from a company's collection.
        
        Args:
            company_id: Company ID to use as collection name
            
        Returns:
            Dictionary with cleanup results including number removed.
        """
        try:
            logger.info(f"🧹 Starting cleanup of all Confluence documents for company {company_id}")
            
            # Get collection manager using company ID as collection name
            from vectordb.collections.collection_manager import get_collection_manager
            collection_manager = get_collection_manager(str(company_id))
            
            if not collection_manager:
                logger.error(f"❌ Collection manager not found for company {company_id}")
                return {
                    'success': False,
                    'error': 'Collection manager not found'
                }

            # Delete all Confluence documents from the collection
            deleted_count = collection_manager.delete_documents_by_metadata(
                source=DataSource.CONFLUENCE,
                metadata_filters={}
            )

            logger.info(
                f"✅ Removed {deleted_count} Confluence documents from collection for company {company_id}"
            )

            return {
                'success': True,
                'removed': deleted_count,
                'company_id': company_id
            }
            
        except Exception as e:
            logger.error(f"❌ Error cleaning all Confluence documents for company {company_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'company_id': company_id
            }


def cleanup_confluence_documents_for_company(company_id: str) -> Dict[str, Any]:
    """
    Helper function to cleanup all Confluence documents for a company.
    
    Args:
        company_id: Company ID to use as collection name
        
    Returns:
        Dictionary with cleanup results
    """
    try:
        service = DjangoConfluenceService()
        return service.cleanup_all_confluence_documents(company_id)
    except Exception as e:
        logger.error(f"❌ Error creating Confluence service for cleanup: {e}")
        return {
            'success': False,
            'error': str(e),
            'company_id': company_id
        }
