import requests
from typing import Dict, Any
from urllib.parse import urlencode
import os
from dotenv import load_dotenv
from pathlib import Path
import logging
import base64
import hashlib
import secrets

BASE_DIR = Path(__file__).resolve().parent.parent
load_dotenv(os.path.join(BASE_DIR, '.env'))

logger = logging.getLogger(__name__)

def generate_code_verifier() -> str:
    # Generate a high-entropy cryptographic random string (43-128 chars)
    return base64.urlsafe_b64encode(secrets.token_bytes(32)).rstrip(b'=').decode('utf-8')

def generate_code_challenge(verifier: str) -> str:
    # Create a code challenge from the code verifier
    digest = hashlib.sha256(verifier.encode('utf-8')).digest()
    return base64.urlsafe_b64encode(digest).rstrip(b'=').decode('utf-8')

class ConfluenceOAuthProvider:
    """Confluence (Atlassian) OAuth provider implementation with PKCE support"""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri

    @property
    def name(self) -> str:
        return "confluence"
    
    @property
    def auth_url(self) -> str:
        return "https://auth.atlassian.com/authorize"
    
    @property
    def token_url(self) -> str:
        return "https://auth.atlassian.com/oauth/token"
    
    @property
    def scopes(self) -> list:
        # Updated scopes for Confluence v2 API compatibility and search
        return [
            "read:space:confluence",  # Required for v2 spaces API
            "search:confluence",  # Required for v1 search API (Classic - RECOMMENDED)
            "read:content-details:confluence",  # Required for v1 search API (Granular)
            "read:confluence-space.summary",
            "read:confluence-content.summary", 
            "read:confluence-content.all",
            "read:confluence-content.permission",  # Required for search API
            "read:me",  # Required for accessing https://api.atlassian.com/me
            "offline_access",
            # Additional scopes that might be needed for v2 API
            "read:confluence-props",
            "read:confluence-user",
            "read:confluence-groups"
        ]
    
    def get_authorization_url(self, state: str, request=None) -> str:
        # PKCE: generate code_verifier and code_challenge
        code_verifier = generate_code_verifier()
        code_challenge = generate_code_challenge(code_verifier)
        if request is not None:
            request.session['confluence_code_verifier'] = code_verifier
            request.session.modified = True
        params = {
            'audience': 'api.atlassian.com',
            'client_id': self.client_id,
            'scope': ' '.join(self.scopes),
            'redirect_uri': self.redirect_uri,
            'state': state,
            'response_type': 'code',
            'prompt': 'consent',
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
        }
        return f"{self.auth_url}?{urlencode(params)}"
    
    def exchange_code_for_token(self, code: str, request=None) -> Dict[str, Any]:
        print("=== TOKEN EXCHANGE STARTED ===")
        print(f"Code: {code[:50]}...")
        print(f"Redirect URI: {self.redirect_uri}")
        print(f"Client ID: {self.client_id}")
        
        try:
            # PKCE: retrieve code_verifier from session
            code_verifier = None
            if request is not None:
                code_verifier = request.session.get('confluence_code_verifier')
            logger.info(f"Token exchange started:")
            logger.info(f"   Code: {code[:20]}...{code[-10:] if len(code) > 30 else code}")
            logger.info(f"   Redirect URI: {self.redirect_uri}")
            logger.info(f"   Code Verifier: {'Present' if code_verifier else 'Missing'}")
            logger.info(f"   Client ID: {self.client_id}")
            
            data = {
                'grant_type': 'authorization_code',
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'code': code,
                'redirect_uri': self.redirect_uri
            }
            if code_verifier:
                data['code_verifier'] = code_verifier
                logger.info(f"   PKCE Code Verifier length: {len(code_verifier)}")
            
            print(f"Making request to: {self.token_url}")
            logger.info(f"Making token request to: {self.token_url}")
            response = requests.post(self.token_url, json=data, timeout=10)
            print(f"Response status: {response.status_code}")
            print(f"Response text: {response.text}")
            
            logger.info(f"Token response:")
            logger.info(f"   Status Code: {response.status_code}")
            logger.info(f"   Response: {response.text}")
            
            response.raise_for_status()
            data = response.json()
            
            if 'access_token' not in data:
                logger.error(f"No access_token in response: {data}")
                raise Exception(f"Token exchange failed: {data}")
                
            print("=== TOKEN EXCHANGE SUCCESS ===")
            logger.info(f"Token exchange successful!")
            logger.info(f"   Access token length: {len(data['access_token'])}")
            if 'refresh_token' in data:
                logger.info(f"   Refresh token length: {len(data['refresh_token'])}")
            if 'expires_in' in data:
                logger.info(f"   Expires in: {data['expires_in']} seconds")
                
            return data
        except requests.RequestException as e:
            print(f"=== REQUEST EXCEPTION: {str(e)} ===")
            logger.error(f"Request exception during token exchange: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response text: {e.response.text}")
                logger.error(f"   Response status: {e.response.status_code}")
                logger.error(f"   Response text: {e.response.text}")
            raise Exception(f"Token exchange failed: {str(e)}")
        except Exception as e:
            print(f"=== UNEXPECTED EXCEPTION: {str(e)} ===")
            logger.error(f"Unexpected exception during token exchange: {str(e)}")
            raise Exception(f"Token exchange failed: {str(e)}")

    def test_connection(self, access_token: str) -> Dict[str, Any]:
        headers = {'Authorization': f'Bearer {access_token}'}
        try:
            # Example: Get current user info from Atlassian
            response = requests.get('https://api.atlassian.com/me', headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            return {
                'success': True,
                'message': 'Connection successful!',
                'user': data
            }
        except requests.RequestException as e:
            return {
                'success': False,
                'message': f"Connection test failed: {str(e)}"
            } 