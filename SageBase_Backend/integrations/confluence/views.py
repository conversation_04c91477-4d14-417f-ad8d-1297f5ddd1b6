from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponseRedirect, HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone

import os
import secrets
import logging
import json
import requests
import subprocess
import threading
import time
from typing import Dict, Optional

from integrations.models import AtlassianUserProfile
# from integrations.models import ConfluenceUserProfile  # Commented out until migration
from .oauth_providers import ConfluenceOAuthProvider
from .services.django_confluence_service import DjangoConfluenceService

logger = logging.getLogger(__name__)

REQUEST_TIMEOUT = 15  # seconds


# =========================
# Background sync launcher
# =========================
def _trigger_direct_background_sync(
    confluence_email: str,
    access_token: str,
    refresh_token: str,
    confluence_url: str,
    cloud_id: str,
    original_instance_url: Optional[str] = None,
    company_id: Optional[str] = None,
    user_id: Optional[str] = None,
):
    """
    Launch sync in a separate thread. Uses OAuth2 endpoints (api.atlassian.com).
    """
    def sync_worker():
        try:
            logger.info(f"🚀 Starting direct background sync for Confluence user {confluence_email}")
            service = DjangoConfluenceService()
            workspace = f"confluence_{confluence_email.replace('@', '_at_').replace('.', '_')}"

            # 1) Discover spaces
            logger.info(f"🔍 Discovering accessible spaces for {confluence_email}")
            try:
                accessible_spaces = service.get_user_accessible_spaces(
                    confluence_email=confluence_email,
                    confluence_api_token=access_token,
                    confluence_url=confluence_url,
                    cloud_id=cloud_id,
                )
                if not accessible_spaces:
                    logger.warning(f"⚠️ No accessible spaces found for {confluence_email}")
                    return
                spaces_to_sync = [space['key'] for space in accessible_spaces]
                logger.info(f"📋 Found {len(spaces_to_sync)} spaces to sync: {spaces_to_sync}")
                for space in accessible_spaces:
                    logger.info(f"   • {space['key']}: {space['name']} ({space['type']})")
            except Exception as discovery_error:
                logger.error(f"❌ Failed to discover spaces: {discovery_error}", exc_info=True)
                spaces_to_sync = ['DEV', 'TECH', 'DOCS']
                logger.warning(f"⚠️ Using fallback spaces: {spaces_to_sync}")

            # 2) Sync each space
            total_documents = 0
            successful_spaces = 0
            for space_key in spaces_to_sync:
                try:
                    logger.info(f"🔄 Syncing space {space_key} for user {confluence_email}")
                    if not user_id:
                        logger.warning(f"⚠️ No user_id provided for {confluence_email}")

                    result = service.sync_confluence_documents(
                        confluence_email=confluence_email,
                        confluence_api_token=access_token,
                        confluence_url=confluence_url,
                        space_key=space_key,
                        workspace=workspace,
                        user_id=user_id,
                        cloud_id=cloud_id,
                        company_id=company_id,
                    )

                    logger.info(f"📊 Sync result for {space_key}: status={result.get('status')} msg={result.get('message')}")
                    if result['status'] == 'success':
                        docs_count = result.get('documents_processed', 0)
                        total_documents += docs_count
                        successful_spaces += 1
                        logger.info(f"✅ Successfully synced {docs_count} documents from {space_key} (workspace={result.get('workspace')})")
                        if 'sync_details' in result:
                            details = result['sync_details']
                            logger.info(
                                "📋 Details: fetched=%s converted=%s chunks=%s embeddings=%s indexed=%s",
                                details.get('documents_fetched', 'N/A'),
                                details.get('documents_converted', 'N/A'),
                                details.get('chunks_created', 'N/A'),
                                details.get('embeddings_generated', 'N/A'),
                                details.get('documents_indexed', 'N/A'),
                            )
                    else:
                        logger.error(f"❌ Failed to sync space {space_key} — {result.get('error')}")
                        if 'error_details' in result:
                            logger.error(f"📋 Error details: {result['error_details']}")
                except Exception as space_error:
                    logger.error(f"❌ Exception syncing space {space_key}: {space_error}", exc_info=True)

            # 3) Notify
            try:
                from integrations.models import User
                from knowledge_spaces_Q_A.models import Notification as QA_Notification
                user_obj = User.objects.filter(id=user_id, is_active=True).first() if user_id else None

                if successful_spaces > 0:
                    QA_Notification.objects.create(
                        title="Confluence Documents indexing complete",
                        details=f"Successful spaces: {successful_spaces}/{len(spaces_to_sync)}\nTotal documents: {total_documents}",
                        type="embedding",
                        severity="medium",
                        source="Confluence",
                        user=user_obj,
                        company_id=company_id,
                        status="pending",
                        accepted_at=None,
                        ignored_at=None,
                    )
                    logger.info("✅ Created notification for successful Confluence sync")
                else:
                    QA_Notification.objects.create(
                        title="Confluence Documents indexing failed",
                        details=f"Successful spaces: 0/{len(spaces_to_sync)}\nTotal documents: 0",
                        type="embedding",
                        severity="high",
                        source="Confluence",
                        user=user_obj,
                        company_id=company_id,
                        status="pending",
                        accepted_at=None,
                        ignored_at=None,
                    )
                    logger.info("✅ Created notification for failed Confluence sync")
            except Exception as notify_err:
                logger.debug(f"Notification create failed: {notify_err}")

        except Exception as e:
            logger.error(f"❌ Fatal error in direct background sync for {confluence_email}: {e}", exc_info=True)

    threading.Thread(target=sync_worker, daemon=True, name="confluence-embedding").start()
    logger.info(f"🔄 Direct background sync thread started for user {confluence_email}")


# =========================
# Helpers
# =========================
def _extract_confluence_url_from_cloud_id(cloud_id: str, access_token: str) -> str:
    """Best-effort URL from accessible resources."""
    try:
        resp = requests.get(
            "https://api.atlassian.com/oauth/token/accessible-resources",
            headers={"Authorization": f"Bearer {access_token}"},
            timeout=REQUEST_TIMEOUT,
        )
        resp.raise_for_status()
        for resource in resp.json():
            if resource.get('id') == cloud_id:
                return resource.get('url', f'https://api.atlassian.com/ex/confluence/{cloud_id}')
        return f'https://api.atlassian.com/ex/confluence/{cloud_id}'
    except Exception as e:
        logger.error(f"❌ Error extracting Confluence URL for cloud_id {cloud_id}: {e}")
        return f'https://api.atlassian.com/ex/confluence/{cloud_id}'


def _exchange_code_directly_with_atlassian(code: str, code_verifier: Optional[str], redirect_uri: str,
                                           client_id: str, client_secret: Optional[str]) -> dict:
    """
    Fallback exchange that talks straight to Atlassian (fixes 'Failed to receive token' issues).
    Uses PKCE if verifier present; otherwise includes client_secret (if available).
    """
    url = "https://auth.atlassian.com/oauth/token"
    payload = {
        "grant_type": "authorization_code",
        "client_id": client_id,
        "code": code,
        "redirect_uri": redirect_uri,
    }
    if code_verifier:
        payload["code_verifier"] = code_verifier
    elif client_secret:
        payload["client_secret"] = client_secret

    resp = requests.post(url, headers={"Content-Type": "application/json"}, json=payload, timeout=REQUEST_TIMEOUT)
    try:
        data = resp.json()
    except Exception:
        data = {"raw_text": resp.text}

    if resp.status_code != 200 or "access_token" not in data:
        logger.error(f"Atlassian token exchange failed [{resp.status_code}]: {data}")
        raise ValueError(data.get("error_description") or data.get("error") or "Failed to receive Confluence token.")
    return data


# =========================
# OAuth connect
# =========================
def confluence_oauth_connect(request):
    """Initiate OAuth connection for Confluence (stores email + state + PKCE in session)."""
    logger.debug("[DEBUG] Entered confluence_oauth_connect view")

    user_email = request.GET.get('user_email')
    if not user_email:
        return HttpResponse("User email is required", status=400)

    request.session['confluence_user_email'] = user_email
    client_id = os.getenv('CONFLUENCE_CLIENT_ID', '')
    client_secret = os.getenv('CONFLUENCE_CLIENT_SECRET', '')
    redirect_uri = os.getenv('CONFLUENCE_REDIRECT_URI', '')

    try:
        provider = ConfluenceOAuthProvider(
            client_id=client_id,
            client_secret=client_secret,
            redirect_uri=redirect_uri
        )
    except Exception as e:
        logger.error(f"Error creating ConfluenceOAuthProvider: {e}")
        return HttpResponse(f"Error creating provider: {e}", status=500)

    try:
        state = secrets.token_urlsafe(32)
        request.session['oauth_state_confluence'] = state
    except Exception as e:
        logger.error(f"Error generating state: {e}")
        return HttpResponse(f"Error generating state: {e}", status=500)

    try:
        auth_url = provider.get_authorization_url(state, request=request)  # ensures PKCE
    except Exception as e:
        logger.error(f"Error generating auth_url: {e}")
        return HttpResponse(f"Error generating auth_url: {e}", status=500)

    if not auth_url or not auth_url.startswith("http"):
        return HttpResponse(f"Invalid or missing auth_url: {auth_url}", status=500)
    return redirect(auth_url)


# =========================
# Ephemeral token cache
# =========================
_tokens_lock = threading.Lock()
EPHEMERAL_TOKENS: Dict[str, dict] = {}
TOKENS_TTL_SECONDS_DEFAULT = 3600  # 1h

def _cache_tokens(user_key: str, data: dict, ttl: int):
    now = int(time.time())
    with _tokens_lock:
        EPHEMERAL_TOKENS[user_key] = {"data": data, "exp": now + max(60, ttl)}  # enforce min TTL 60s

def _get_tokens(user_key: str) -> Optional[dict]:
    now = int(time.time())
    with _tokens_lock:
        rec = EPHEMERAL_TOKENS.get(user_key)
        if not rec:
            return None
        if rec["exp"] < now:
            EPHEMERAL_TOKENS.pop(user_key, None)
            return None
        return rec["data"]


# =========================
# OAuth callback
# =========================
def confluence_oauth_callback(request):
    """
    Completes OAuth, stores tokens in DB + in-memory cache, triggers background sync,
    and redirects FE with ?success=true and NO secrets.
    """
    logger.info("🔄 Entering enhanced OAuth callback for Confluence")

    client_id = os.getenv('CONFLUENCE_CLIENT_ID', '')
    client_secret = os.getenv('CONFLUENCE_CLIENT_SECRET', '')
    redirect_uri = os.getenv('CONFLUENCE_REDIRECT_URI', '')

    provider = ConfluenceOAuthProvider(
        client_id=client_id,
        client_secret=client_secret,
        redirect_uri=redirect_uri
    )

    code = request.GET.get('code')
    state = request.GET.get('state')
    error = request.GET.get('error')
    stored_state = request.session.get('oauth_state_confluence')

    if error:
        logger.error(f"❌ Confluence authorization failed: {error}")
        messages.error(request, f"Confluence authorization failed: {error}")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=false&error={error}")

    if not code:
        messages.error(request, "Authorization code not received")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=false&error=missing_code")

    if not stored_state or stored_state != state:
        messages.error(request, "Invalid authorization state")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=false&error=invalid_state")

    try:
        # 1) Token exchange with fallback
        code_verifier = request.session.get('confluence_code_verifier')
        try:
            token_data = provider.exchange_code_for_token(code, request=request)
            if not token_data or 'access_token' not in token_data:
                raise ValueError("provider returned no access_token")
        except Exception as e:
            logger.warning(f"⚠️ Provider exchange failed: {e}. Falling back to direct Atlassian exchange.")
            token_data = _exchange_code_directly_with_atlassian(
                code=code,
                code_verifier=code_verifier,
                redirect_uri=redirect_uri,
                client_id=client_id,
                client_secret=client_secret or None,
            )

        access_token = token_data['access_token']
        refresh_token = token_data.get('refresh_token', '')
        expires_in = int(token_data.get('expires_in', TOKENS_TTL_SECONDS_DEFAULT))
        token_expires_at = timezone.now() + timezone.timedelta(seconds=expires_in)

        # 2) Resources
        resources_resp = requests.get(
            "https://api.atlassian.com/oauth/token/accessible-resources",
            headers={"Authorization": f"Bearer {access_token}"},
            timeout=REQUEST_TIMEOUT,
        )
        if resources_resp.status_code != 200:
            logger.error(f"❌ Failed to get accessible resources: {resources_resp.text}")
        resources_resp.raise_for_status()
        resources = resources_resp.json()

        cloud_id = None
        confluence_url = None
        original_instance_url = None
        for resource in resources:
            if resource.get("scopes") and "read:confluence-content.all" in resource["scopes"]:
                cloud_id = resource["id"]
                original_instance_url = resource.get("url")
                confluence_url = f'https://api.atlassian.com/ex/confluence/{cloud_id}'
                break
        if not cloud_id and resources:
            cloud_id = resources[0]["id"]
            original_instance_url = resources[0].get("url")
            confluence_url = f'https://api.atlassian.com/ex/confluence/{cloud_id}'
        if not cloud_id:
            raise ValueError("No accessible Confluence resources found")

        # 3) Profile (email)
        try:
            profile_resp = requests.get(
                "https://api.atlassian.com/me",
                headers={"Authorization": f"Bearer {access_token}"},
                timeout=REQUEST_TIMEOUT,
            )
            profile_resp.raise_for_status()
            confluence_user_email = profile_resp.json().get('email')
            if not confluence_user_email:
                raise ValueError("Missing email in Atlassian profile")
        except Exception as profile_error:
            raise ValueError(f"Failed to get Confluence user email from profile API: {profile_error}")

        # 4) Map to our platform user via session (or request.user as fallback)
        platform_user_email = request.session.get('confluence_user_email')
        if not platform_user_email and getattr(request.user, "is_authenticated", False):
            platform_user_email = getattr(request.user, "email", None)

        company_id = None
        user_id = None
        if platform_user_email:
            try:
                from integrations.models import User, CompanyIntegration, IntegrationTool
                user = User.objects.filter(email__iexact=platform_user_email).first()
                if not user:
                    logger.error(f"❌ User not found with email: {platform_user_email}")
                else:
                    atlassian_profile, created = AtlassianUserProfile.objects.update_or_create(
                        user=user,
                        defaults={
                            'account_id': '',
                            'email': confluence_user_email,
                            'display_name': '',
                            'domain': '',
                            'cloud_id': cloud_id,
                            'confluence_url': confluence_url,
                            'access_token': access_token,
                            'refresh_token': refresh_token,
                            'token_expires_at': token_expires_at,
                            'is_active': True,
                        },
                    )
                    logger.info(f"✅ AtlassianUserProfile {'created' if created else 'updated'} for user {user.email}")
                    if not user.company:
                        raise ValueError(f"User {user.email} has no company assigned")
                    
                    # Create or update CompanyIntegration record
                    confluence_tool = IntegrationTool.objects.filter(slug="confluence").first()
                    if not confluence_tool:
                        # Create the Confluence tool if it doesn't exist
                        confluence_tool = IntegrationTool.objects.create(
                            name="Confluence",
                            slug="confluence",
                            category=IntegrationTool.Category.PROJECT_MANAGEMENT,
                            description="Atlassian Confluence integration for document management",
                            is_active=True
                        )
                        logger.info(f"✅ Created IntegrationTool for Confluence")
                    
                    company_integration, integration_created = CompanyIntegration.objects.update_or_create(
                        company=user.company,
                        tool=confluence_tool,
                        defaults={
                            'status': CompanyIntegration.Status.CONNECTED,
                            'is_active': True,
                            'connected_at': timezone.now(),
                            'config': {
                                'confluence_user_email': confluence_user_email,
                                'cloud_id': cloud_id,
                                'confluence_url': confluence_url,
                                'connected_by_user': user.email,
                                'connected_at': timezone.now().isoformat(),
                            }
                        }
                    )
                    logger.info(f"✅ CompanyIntegration {'created' if integration_created else 'updated'} for company {user.company.name}")
                    
                    company_id = str(user.company.id)
                    user_id = str(user.id)
            except Exception as profile_error:
                logger.error(f"❌ Error saving AtlassianUserProfile/CompanyIntegration: {profile_error}", exc_info=True)
        else:
            logger.warning("⚠️ No platform user email in session/request — profile not saved")

        # 5) Cache tokens in-memory (no URL leakage)
        cache_ttl = expires_in
        user_key = (platform_user_email or confluence_user_email or f"user-{cloud_id or 'unknown'}").lower()
        _cache_tokens(
            user_key,
            {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "cloud_id": cloud_id,
                "confluence_url": confluence_url,
                "confluence_user_email": confluence_user_email,
            },
            ttl=cache_ttl,
        )
        logger.info(f"🔐 Cached Confluence tokens in-memory for key={user_key} (TTL={cache_ttl}s)")

        # 6) Optional quick API smoke test (non-fatal)
        try:
            test_url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/api/v2/spaces?limit=1"
            test_resp = requests.get(test_url, headers={"Authorization": f"Bearer {access_token}"}, timeout=REQUEST_TIMEOUT)
            logger.info(f"🧪 v2 Spaces API test status: {test_resp.status_code}")
        except Exception as test_error:
            logger.debug(f"Spaces API test skipped/failed: {test_error}")

        # 7) Start background sync if we know the platform user + company
        if company_id and user_id:
            _trigger_direct_background_sync(
                confluence_email=confluence_user_email,
                access_token=access_token,
                refresh_token=refresh_token,
                confluence_url=confluence_url,
                cloud_id=cloud_id,
                original_instance_url=original_instance_url,
                company_id=company_id,
                user_id=user_id,
            )
        else:
            logger.info("⚠️ Skipping background sync (missing company_id or user_id)")

        # 8) Clear session keys related to OAuth
        for key in ['oauth_state_confluence', 'confluence_code_verifier', 'confluence_user_email']:
            if key in request.session:
                del request.session[key]
        request.session.modified = True

        messages.success(request, "✅ Successfully connected to Confluence!")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=true")

    except requests.RequestException as req_error:
        logger.error(f"❌ HTTP request error in OAuth callback: {req_error}")
        messages.error(request, f"Error communicating with Confluence: {str(req_error)}")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=false&error=http_error")

    except ValueError as val_error:
        logger.error(f"❌ Validation error in OAuth callback: {val_error}")
        messages.error(request, f"Invalid response from Confluence: {str(val_error)}")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=false&error=validation_error")

    except Exception as e:
        logger.error(f"❌ Unexpected error in OAuth callback: {str(e)}", exc_info=True)
        messages.error(request, f"Error connecting to Confluence: {str(e)}")
        fb = os.getenv('FRONTEND_BASE_URL', 'http://localhost:3000')
        return HttpResponseRedirect(f"{fb}/confluence/callback?success=false&error=unexpected_error")


# =========================
# Read-only status (no secrets)
# =========================
@csrf_exempt
def confluence_status(request):
    """
    Read-only connection status. No tokens leaked.
    GET /api/integrations/confluence/status/?email=<platform_user_email>
    """
    email = (request.user.email if getattr(request.user, "is_authenticated", False)
             else request.GET.get("email", "")).strip()
    if not email:
        return JsonResponse({"connected": False, "reason": "no_email"}, status=400)

    from integrations.models import User
    user = User.objects.filter(email__iexact=email).first()
    if not user:
        return JsonResponse({"connected": False, "reason": "no_user"}, status=404)

    prof = AtlassianUserProfile.objects.filter(user=user, is_active=True).first()
    if not prof:
        return JsonResponse({"connected": False})

    key = email.lower()
    has_cached = _get_tokens(key) is not None

    return JsonResponse({
        "connected": True,
        "email": prof.email,
        "cloud_id": prof.cloud_id,
        "confluence_url": prof.confluence_url,
        "cached": has_cached,
        "token_expires_at": prof.token_expires_at.isoformat() if prof.token_expires_at else None,
    })


# =========================
# Search endpoint (unchanged)
# =========================
@csrf_exempt
def confluence_search_documents(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        query = data.get('query', '').strip()
        workspace = data.get('workspace', '').strip()

        if not query:
            return JsonResponse({'error': 'Query parameter is required'}, status=400)
        if not workspace:
            return JsonResponse({'error': 'Workspace parameter is required'}, status=400)

        from vectordb.interfaces import search
        from vectordb.models.document import DataSource

        logger.info(f"🔍 Simple Confluence search: '{query}' in workspace '{workspace}'")
        search_results = search(
            query=query,
            sources=[DataSource.CONFLUENCE],
            workspace=workspace,
            limit=10,
            search_mode="semantic",
            similarity_threshold=0.1
        )

        results = []
        for result in search_results:
            results.append({
                "title": result.metadata.title,
                "content": result.content[:300] + "..." if len(result.content) > 300 else result.content,
                "score": round(result.score, 3),
                "url": result.metadata.url
            })

        return JsonResponse({
            "success": True,
            "query": query,
            "workspace": workspace,
            "count": len(results),
            "results": results
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"❌ Search error: {e}", exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)


# =========================
# (Optional) Delete profile
# =========================
@csrf_exempt
def delete_atlassian_profile(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        user_email = data.get('email', '').strip()
        if not user_email:
            return JsonResponse({'error': 'User email is required'}, status=400)

        from integrations.models import User
        user = User.objects.filter(email__iexact=user_email).first()
        if not user:
            return JsonResponse({'error': f'User not found with email: {user_email}'}, status=404)

        try:
            atlassian_profile = AtlassianUserProfile.objects.get(user=user)
            profile_data = {
                'id': atlassian_profile.id,
                'account_id': atlassian_profile.account_id,
                'email': atlassian_profile.email,
                'display_name': atlassian_profile.display_name,
                'domain': atlassian_profile.domain,
                'cloud_id': atlassian_profile.cloud_id
            }

            company_id = str(user.company.id) if user.company else None
            cleanup_result = None
            if company_id:
                try:
                    from .services.django_confluence_service import cleanup_confluence_documents_for_company
                    cleanup_result = cleanup_confluence_documents_for_company(company_id)
                except Exception as cleanup_error:
                    logger.error(f"❌ Error during Confluence cleanup: {cleanup_error}", exc_info=True)

            atlassian_profile.delete()
            return JsonResponse({
                'success': True,
                'message': 'Atlassian profile disconnected successfully',
                'deleted_profile': profile_data,
                'cleanup_result': cleanup_result
            })

        except AtlassianUserProfile.DoesNotExist:
            return JsonResponse({'error': 'No Atlassian profile found for this user'}, status=404)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON format'}, status=400)
    except Exception as e:
        logger.error(f"❌ Error deleting Atlassian profile: {str(e)}", exc_info=True)
        return JsonResponse({'error': f'Failed to delete profile: {str(e)}'}, status=500)
