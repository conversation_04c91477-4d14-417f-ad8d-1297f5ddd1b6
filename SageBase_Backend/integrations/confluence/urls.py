from django.urls import path
from .views import (
    confluence_oauth_connect, 
    confluence_oauth_callback, 
    confluence_search_documents,
    delete_atlassian_profile
)

urlpatterns = [
    path("connect/", confluence_oauth_connect, name="confluence-start"),
    path("callback/", confluence_oauth_callback, name="confluence-callback"),
    # New vectordb-based endpoints
    path("search/", confluence_search_documents, name="confluence-search-documents"),
    # Atlassian profile management
    path("delete-profile/", delete_atlassian_profile, name="delete-atlassian-profile"),
]
