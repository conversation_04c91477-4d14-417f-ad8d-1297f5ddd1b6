"""
Retry utility for handling API request failures.
"""

import time
import logging
from typing import Callable, Any

logger = logging.getLogger(__name__)


def execute_with_retry(func: Callable[[], Any], 
                      operation_description: str, 
                      max_retries: int = 3, 
                      retry_delay: float = 1.0) -> Any:
    """
    Execute a function with retry logic.
    
    Args:
        func: Function to execute
        operation_description: Description of the operation for logging
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        
    Returns:
        Result of the function execution
        
    Raises:
        Exception: If all retries fail
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as e:
            last_exception = e
            if attempt < max_retries:
                logger.warning(f"Attempt {attempt + 1} failed for {operation_description}: {e}")
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                # Exponential backoff
                retry_delay *= 2
            else:
                logger.error(f"All {max_retries + 1} attempts failed for {operation_description}")
                break
    
    raise last_exception
