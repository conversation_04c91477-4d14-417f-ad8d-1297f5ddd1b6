import os

from bs4 import BeautifulSoup
from langchain.text_splitter import RecursiveCharacterTextSplitter

class ConfluenceDocumentConverter:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=100,
        )

    def convert(self, document):
        # Handle both Cloud and OAuth2 document formats
        page = document["page"]
        
        # Check if this is Cloud format (has 'content' wrapper) or OAuth2 format (direct page)
        if "content" in page:
            # Cloud format: page has a 'content' wrapper
            page_content = page["content"]
            page_id = page_content['id']
            modified_time = page_content.get('version', {}).get('when', 'Unknown')
            created_time = page_content.get('createdAt', 'Unknown')
            url = self.__build_url(page_content)
            author = page_content.get('author', {})
        else:
            # OAuth2 format: page is direct
            page_content = page
            page_id = page['id']
            modified_time = page.get('version', {}).get('when', 'Unknown')
            created_time = page.get('createdAt', 'Unknown')
            url = self.__build_url(page)
            author = page.get('author', {})
        
        return [{
            "id": page_id,
            "url": url,
            "modifiedTime": modified_time,
            "createdTime": created_time,
            "text": self.__build_document_text(document, page_content),
            "author": author,
            "chunks": self.__split_to_chunks(document, page_content)
        }]
    
    def __build_document_text(self, document, page_content):
        title = self.__build_path_of_titles(page_content)
        body_and_comments = self.__fetch_body_and_comments(document, page_content)

        return self.__convert_to_text([title, body_and_comments])

    def __split_to_chunks(self, document, page_content):
        chunks = [{
                "indexedData": self.__build_path_of_titles(page_content),
            }]
        
        body_and_comments = self.__fetch_body_and_comments(document, page_content)
        
        if body_and_comments:
            for chunk in self.text_splitter.split_text(body_and_comments):
                chunks.append({
                    "indexedData": chunk
                })
            
        return chunks
    
    def __fetch_body_and_comments(self, document, page_content):
        body = self.__get_cleaned_body(page_content)
        comments = [self.__get_cleaned_body(comment) for comment in document["comments"]]

        return self.__convert_to_text([body] + comments)

    def __convert_to_text(self, elements, delimiter="\n\n"):
        return delimiter.join([element for element in elements if element])

    def __get_cleaned_body(self, document):
        # Safely get body content with fallbacks
        body = document.get("body", {})
        storage = body.get("storage", {})
        document_text_html = storage.get("value", "")
        
        if not document_text_html:
            return ""
        
        soup = BeautifulSoup(document_text_html, "html.parser")
        return soup.get_text(separator=os.linesep, strip=True) 

    def __build_path_of_titles(self, document):
        page_title = [document.get('title', 'Untitled')] if document.get('title') else []
        ancestors = document.get('ancestors', [])
        ancestor_titles = [ancestor.get("title", "") for ancestor in ancestors if ancestor.get("title")]
        return " -> ".join(ancestor_titles + page_title)
    
    def __build_url(self, page):
        # Handle both v1 and v2 API URL formats
        if '_links' in page and 'self' in page['_links']:
            # v1 API format
            base_url = page['_links']['self'].split("/rest/api/")[0]
            if 'webui' in page['_links']:
                return f"{base_url}{page['_links']['webui']}"
            else:
                # Fallback for v2 API
                return f"{base_url}/wiki/pages/{page.get('id', '')}"
        else:
            # v2 API format - try to extract domain from self link if available
            if '_links' in page and 'self' in page['_links']:
                self_link = page['_links']['self']
                if '/wiki/api/v2/' in self_link:
                    # Extract domain from OAuth2 API URL
                    # https://api.atlassian.com/ex/confluence/{cloud_id}/wiki/api/v2/content/{id}
                    # -> https://your-domain.atlassian.net/wiki/pages/{id}
                    parts = self_link.split('/wiki/api/v2/')
                    if len(parts) > 0:
                        # For OAuth2, we need to convert the API URL to the web URL
                        # This is a simplified approach - in production you'd want to map cloud_id to domain
                        return f"https://sagebase-tech.atlassian.net/wiki/pages/{page.get('id', '')}"
            
            # Final fallback
            return f"https://sagebase-tech.atlassian.net/wiki/pages/{page.get('id', '')}"
