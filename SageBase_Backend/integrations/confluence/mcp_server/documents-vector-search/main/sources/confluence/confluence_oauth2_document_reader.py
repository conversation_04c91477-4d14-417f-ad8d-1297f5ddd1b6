import requests
import os

from ...utils.retry import execute_with_retry
from ...utils.batch import read_items_in_batches

class ConfluenceOAuth2DocumentReader:
    def __init__(self, 
                 base_url, 
                 query,
                 access_token=None,
                 cloud_id=None,
                 batch_size=50, 
                 number_of_retries=3, 
                 retry_delay=1, 
                 max_skipped_items_in_row=5,
                 read_all_comments=False):
        # Get access token from environment variable as Django service expects
        self.access_token = access_token or os.getenv('CONF_TOKEN')
        if not self.access_token:
            raise ValueError("'access_token' or 'CONF_TOKEN' environment variable must be provided for OAuth2 authentication.")

        # Debug logging
        print(f"🔧 OAuth2 Reader Initialization:")
        print(f"   Base URL: {base_url}")
        print(f"   Access token provided: {access_token is not None}")
        print(f"   CONF_TOKEN from env: {os.getenv('CONF_TOKEN') is not None}")
        print(f"   Final token length: {len(self.access_token) if self.access_token else 0}")
        print(f"   Token preview: {self.access_token[:20] if self.access_token else 'None'}...")

        self.base_url = base_url
        self.query = ConfluenceOAuth2DocumentReader.build_page_query(query)
        self.cloud_id = cloud_id
        self.batch_size = batch_size
        # Confluence has hierarchical comments, we can read first level by adding "children.comment.body.storage" to "expand" parameter
        # but to read all comments we need to make additional request with "depth=all" parameter
        self.expand = "body.storage,ancestors,version,children.comment" if read_all_comments else "body.storage,ancestors,version,children.comment.body.storage"
        self.number_of_retries = number_of_retries
        self.retry_delay = retry_delay
        self.max_skipped_items_in_row = max_skipped_items_in_row
        self.read_all_comments = read_all_comments
    
    def read_all_documents(self):
        for page in self.__read_items():
            yield {
                "page": page,
                "comments": self.__read_comments(page)
            }

    def get_number_of_documents(self):
        search_result = self.__request(
            self.__add_url_prefix('/wiki/rest/api/search'),
            {
                "cql": self.query,
                "limit": 1,
                "start": 0
            })

        return search_result['totalSize']
    
    def get_reader_details(self) -> dict:
        return {
            "type": "confluenceOAuth2",
            "baseUrl": self.base_url,
            "query": self.query,
            "expand": self.expand,
            "batchSize": self.batch_size,
            "readAllComments": self.read_all_comments,
        }
    
    @staticmethod
    def build_page_query(user_query):
        if not user_query:
            return "type=page"

        return f'type=page AND ({user_query})'

    def __add_url_prefix(self, relative_path):
        return self.base_url + relative_path
    
    def __read_comments(self, page):
        # For v2 API, we need to check if comments exist in the page structure
        # The structure might be different from v1 API
        if 'children' not in page or 'comment' not in page['children']:
            return []

        if page['children']['comment']['size'] == 0:
            return []

        if not self.read_all_comments:
            return page['children']['comment']['results']

        read_batch_func = lambda start_at, batch_size: self.__request(
            self.__add_url_prefix(f"/wiki/rest/api/content/{page['id']}/child/comment"),
            {
                "limit": batch_size,
                "start": start_at,
                "expand": "body.storage",
                "depth": "all"
            })

        comments_generator = read_items_in_batches(read_batch_func,
                              fetch_items_from_result_func=lambda result: result['results'],
                              fetch_total_from_result_func=lambda result: result['size'],
                              batch_size=self.batch_size,
                              max_skipped_items_in_row=self.max_skipped_items_in_row,
                              itemsName="comments")

        return [comment for comment in comments_generator]

    def __read_items(self):
        read_batch_func = lambda start_at, batch_size: self.__request(
            self.__add_url_prefix('/wiki/rest/api/search'),
            {
                "cql": self.query,
                "limit": batch_size,
                "start": start_at,
                "expand": self.expand
            })

        return read_items_in_batches(read_batch_func,
                              fetch_items_from_result_func=lambda result: result['results'],
                              fetch_total_from_result_func=lambda result: result['totalSize'],
                              batch_size=self.batch_size,
                              max_skipped_items_in_row=self.max_skipped_items_in_row,
                              itemsName="pages")

    def __request(self, url, params):
        def do_request():
            # Debug logging
            print(f"🔍 OAuth2 Request Debug:")
            print(f"   URL: {url}")
            print(f"   Token length: {len(self.access_token) if self.access_token else 0}")
            print(f"   Token preview: {self.access_token[:20] if self.access_token else 'None'}...")
            
            response = requests.get(url=url, 
                                    headers={
                                        "Accept": "application/json",
                                        "Authorization": f"Bearer {self.access_token}"
                                    }, 
                                    params=params)
            response.raise_for_status()
            return response.json()

        return execute_with_retry(do_request, f"Requesting items with params: {params}", self.number_of_retries, self.retry_delay)
  