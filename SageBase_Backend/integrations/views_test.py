from rest_framework.decorators import api_view
from rest_framework.response import Response
from integrations.models import NotificationSettings

@api_view(["GET"])
def test_trigger_event(request):
    from knowledge_spaces_Q_A.utils import create_qa_instance
    
    
    created_qa = create_qa_instance(
       workspace ="sagebase",
       question_content="this is a test",
       answer_content="test answer",
       question_title="test_title", 
       user_id="b29a98cc-3773-4fdd-9153-385e39a2e9e4",
       knowledge_space_name="sagebase"
    )
    return Response({"message": "Event triggered successfully"})