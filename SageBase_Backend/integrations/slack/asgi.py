import os
import django
from channels.routing import Protocol<PERSON><PERSON><PERSON><PERSON><PERSON>, URLRouter
from django.core.asgi import get_asgi_application
import asyncio
import threading

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SageBase_BackEnd.settings')
django.setup()

application = get_asgi_application()

async def main():
    # Run the Django ASGI application
    asgi_app = get_asgi_application()
    
    # Run the Slack listener in a separate thread
    slack_thread = threading.Thread(target=run_listener_in_thread, daemon=True)
    slack_thread.start()
    
    # Keep the main thread alive
    while True:
        await asyncio.sleep(1)

# Start the main async function
if __name__ == "__main__":
    asyncio.run(main())
