from django.urls import path
from .views import slack_oauth_connect, slack_oauth_callback, save_slack_user_profile, delete_slack_user_profile, get_thread_replies, get_channel_history_with_threads
from integrations.slack.api.slack_webhook_receiver import SlackWebhookReceiverView

urlpatterns = [
    # OAuth endpoints
    path("connect/", slack_oauth_connect, name="slack-start"),
    path("callback/", slack_oauth_callback, name="slack-callback"),
    path('save-slack-user-profile/', save_slack_user_profile, name='save_slack_user_profile'),
    path('delete-slack-user-profile/', delete_slack_user_profile, name='delete_slack_user_profile'),
    # Thread-related API endpoints
    path('get-thread-replies/', get_thread_replies, name='get_thread_replies'),
    path('get-channel-history-with-threads/', get_channel_history_with_threads, name='get_channel_history_with_threads'),
    # Webhook endpoints
    path("webhook/receive/", SlackWebhookReceiverView.as_view(), name="slack-webhook-receive"),
]
