import os
from slack_sdk.web import WebClient
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


def send_message_to_user(user_id: str, message: str, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Send a direct message to a user by their user ID.
    
    Args:
        user_id: The Slack user ID to send the message to
        message: The message content to send
        bot_token: Optional bot token. If not provided, will use SLACK_BOT_TOKEN from environment
    
    Returns:
        dict: Response from Slack API with 'ok' status and other details
        
    Example:
        result = send_message_to_user("U093656FK5G", "Hello! This is a test message.")
        if result.get("ok"):
            logger.debug("Message sent successfully")
        else:
            logger.debug(f"Failed to send message: {result}")
    """
    try:
        # Get bot token from parameter or environment
        if not bot_token:
            bot_token = os.getenv("SLACK_BOT_TOKEN")
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token provided or found in environment variables"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # Open a DM with the user
        dm_response = client.conversations_open(users=[user_id])
        
        if not dm_response.get("ok"):
            return {
                "ok": False,
                "error": f"Failed to open DM with user {user_id}: {dm_response.get('error', 'Unknown error')}"
            }
        
        # Get the DM channel ID
        dm_channel_id = dm_response["channel"]["id"]
        
        # Send the message
        post_response = client.chat_postMessage(
            channel=dm_channel_id,
            text=message,
            unfurl_links=False,
            unfurl_media=False
        )
        
        if post_response.get("ok"):
            return {
                "ok": True,
                "channel_id": dm_channel_id,
                "ts": post_response.get("ts"),
                "message": "Message sent successfully"
            }
        else:
            return {
                "ok": False,
                "error": f"Failed to send message: {post_response.get('error', 'Unknown error')}"
            }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }


def send_message_to_channel(channel_id: str, message: str, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Send a message to a channel by its channel ID.
    The bot will automatically join the channel if it's not already a member.
    
    Args:
        channel_id: The Slack channel ID to send the message to
        message: The message content to send
        bot_token: Optional bot token. If not provided, will use SLACK_BOT_TOKEN from environment
    
    Returns:
        dict: Response from Slack API with 'ok' status and other details
        
    Example:
        result = send_message_to_channel("C1234567890", "Hello channel!")
        if result.get("ok"):
            logger.debug("Message sent successfully")
        else:
            logger.debug(f"Failed to send message: {result}")
    """
    try:
        # Get bot token from parameter or environment
        if not bot_token:
            bot_token = os.getenv("SLACK_BOT_TOKEN")
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token provided or found in environment variables"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # First, try to join the channel if the bot is not already a member
        try:
            join_response = client.conversations_join(channel=channel_id)
            if join_response.get("ok"):
                logger.debug(f"Bot successfully joined channel {channel_id}")
            else:
                # If join fails, it might already be a member or there might be permission issues
                error = join_response.get("error", "Unknown error")
                if error != "already_in_channel":
                    logger.debug(f"Warning: Could not join channel {channel_id}: {error}")
        except Exception as join_error:
            logger.debug(f"Warning: Error joining channel {channel_id}: {join_error}")
        
        # Send the message
        post_response = client.chat_postMessage(
            channel=channel_id,
            text=message,
            unfurl_links=False,
            unfurl_media=False
        )
        
        if post_response.get("ok"):
            return {
                "ok": True,
                "ts": post_response.get("ts"),
                "message": "Message sent successfully"
            }
        else:
            error = post_response.get("error", "Unknown error")
            if error == "channel_not_found":
                return {
                    "ok": False,
                    "error": f"Channel {channel_id} not found or bot doesn't have access"
                }
            elif error == "not_in_channel":
                return {
                    "ok": False,
                    "error": f"Bot is not in channel {channel_id} and cannot join automatically"
                }
            else:
                return {
                    "ok": False,
                    "error": f"Failed to send message: {error}"
                }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }


def get_user_info(user_id: str, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Get information about a user by their user ID.
    
    Args:
        user_id: The Slack user ID to get information for
        bot_token: Optional bot token. If not provided, will use SLACK_BOT_TOKEN from environment
    
    Returns:
        dict: User information or error details
        
    Example:
        user_info = get_user_info("U093656FK5G")
        if user_info.get("ok"):
            logger.debug(f"User name: {user_info['user']['name']}")
            logger.debug(f"Real name: {user_info['user']['real_name']}")
        else:
            logger.debug(f"Failed to get user info: {user_info}")
    """
    try:
        # Get bot token from parameter or environment
        if not bot_token:
            bot_token = os.getenv("SLACK_BOT_TOKEN")
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token provided or found in environment variables"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # Get user info
        user_response = client.users_info(user=user_id)
        
        if user_response.get("ok"):
            return {
                "ok": True,
                "user": user_response["user"]
            }
        else:
            return {
                "ok": False,
                "error": f"Failed to get user info: {user_response.get('error', 'Unknown error')}"
            }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }
