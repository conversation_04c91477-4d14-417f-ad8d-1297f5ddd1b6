import os
from slack_sdk.web import WebClient
from typing import Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)


def get_company_slack_bot_token(company):
    """
    Get the Slack bot token for a specific company/workspace.
    
    Args:
        company: Company instance
        
    Returns:
        str: Bot token if found, None otherwise
    """
    if not company:
        return None
        
    from integrations.models import CompanyIntegration
    
    try:
        integration = CompanyIntegration.objects.filter(
            company=company,
            tool__slug='slack',
            is_active=True,
            status='CONNECTED'
        ).first()
        
        if integration and integration.config:
            return integration.config.get('bot_token')
    except Exception as e:
        logger.error(f"Error retrieving Slack bot token for company {company.id}: {e}")
    
    return None


def get_thread_replies(channel_id: str, parent_ts: str, company=None, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Retrieve all replies to a specific message thread using conversations.replies.
    
    Args:
        channel_id: The ID of the channel where the thread originated
        parent_ts: The timestamp of the parent message that started the thread
        company: Company instance to get bot token from
        bot_token: Optional bot token. If not provided, will get from company integration
    
    Returns:
        dict: Response from Slack API with thread messages or error details
        
    Example:
        result = get_thread_replies("C1234567890", "1482960137.003543", company=user.company)
        if result.get("ok"):
            thread_messages = result["messages"]
            for message in thread_messages:
                print(f"Message: {message['text']} (Timestamp: {message['ts']})")
        else:
            print(f"Failed to retrieve thread replies: {result}")
    """
    try:
        # Get bot token from parameter or company integration
        if not bot_token:
            bot_token = get_company_slack_bot_token(company)
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token found for company's Slack integration"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # Call conversations.replies to get thread messages
        response = client.conversations_replies(
            channel=channel_id,
            ts=parent_ts
        )
        
        if response.get("ok"):
            return {
                "ok": True,
                "messages": response.get("messages", []),
                "has_more": response.get("has_more", False),
                "response_metadata": response.get("response_metadata", {})
            }
        else:
            return {
                "ok": False,
                "error": f"Failed to retrieve thread replies: {response.get('error', 'Unknown error')}"
            }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }


def get_channel_history_with_threads(channel_id: str, limit: int = 100, company=None, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Retrieve channel history including all threaded messages.
    
    Args:
        channel_id: The ID of the channel to retrieve messages from
        limit: Maximum number of messages to retrieve (default 100)
        company: Company instance to get bot token from
        bot_token: Optional bot token. If not provided, will get from company integration
    
    Returns:
        dict: Channel history with threaded messages included
        
    Example:
        result = get_channel_history_with_threads("C1234567890", limit=50, company=user.company)
        if result.get("ok"):
            all_messages = result["messages"]
            print(f"Retrieved {len(all_messages)} messages including threads")
        else:
            print(f"Failed to retrieve channel history: {result}")
    """
    try:
        # Get bot token from parameter or company integration
        if not bot_token:
            bot_token = get_company_slack_bot_token(company)
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token found for company's Slack integration"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # First, get the main channel history
        history_response = client.conversations_history(
            channel=channel_id,
            limit=limit
        )
        
        if not history_response.get("ok"):
            return {
                "ok": False,
                "error": f"Failed to retrieve channel history: {history_response.get('error', 'Unknown error')}"
            }
        
        messages = history_response.get("messages", [])
        all_messages = []
        
        # Process each message and check for threads
        for message in messages:
            # Add the main message
            all_messages.append(message)
            
            # Check if this message has a thread
            thread_ts = message.get("thread_ts")
            if thread_ts:
                # This is a threaded message, get all replies
                thread_response = get_thread_replies(channel_id, thread_ts, bot_token=bot_token)
                
                if thread_response.get("ok"):
                    thread_messages = thread_response.get("messages", [])
                    # Add thread messages (skip the first one as it's the parent)
                    for thread_msg in thread_messages[1:]:
                        all_messages.append(thread_msg)
                else:
                    logger.warning(f"Failed to retrieve thread replies for message {message.get('ts')}: {thread_response.get('error')}")
        
        # Sort all messages by timestamp
        all_messages.sort(key=lambda x: float(x.get("ts", 0)))
        
        return {
            "ok": True,
            "messages": all_messages,
            "has_more": history_response.get("has_more", False),
            "response_metadata": history_response.get("response_metadata", {})
        }
        
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }


def send_message_to_user(user_id: str, message: str, company=None, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Send a direct message to a user by their user ID.
    
    Args:
        user_id: The Slack user ID to send the message to
        message: The message content to send
        company: Company instance to get bot token from
        bot_token: Optional bot token. If not provided, will get from company integration
    
    Returns:
        dict: Response from Slack API with 'ok' status and other details
        
    Example:
        result = send_message_to_user("U093656FK5G", "Hello! This is a test message.", company=user.company)
        if result.get("ok"):
            logger.debug("Message sent successfully")
        else:
            logger.debug(f"Failed to send message: {result}")
    """
    try:
        # Get bot token from parameter or company integration
        if not bot_token:
            bot_token = get_company_slack_bot_token(company)
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token found for company's Slack integration"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # Open a DM with the user
        dm_response = client.conversations_open(users=[user_id])
        
        if not dm_response.get("ok"):
            return {
                "ok": False,
                "error": f"Failed to open DM with user {user_id}: {dm_response.get('error', 'Unknown error')}"
            }
        
        # Get the DM channel ID
        dm_channel_id = dm_response["channel"]["id"]
        
        # Send the message
        post_response = client.chat_postMessage(
            channel=dm_channel_id,
            text=message,
            unfurl_links=False,
            unfurl_media=False
        )
        
        if post_response.get("ok"):
            return {
                "ok": True,
                "channel_id": dm_channel_id,
                "ts": post_response.get("ts"),
                "message": "Message sent successfully"
            }
        else:
            return {
                "ok": False,
                "error": f"Failed to send message: {post_response.get('error', 'Unknown error')}"
            }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }


def send_message_to_channel(channel_id: str, message: str, company=None, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Send a message to a channel by its channel ID.
    The bot will automatically join the channel if it's not already a member.
    
    Args:
        channel_id: The Slack channel ID to send the message to
        message: The message content to send
        company: Company instance to get bot token from
        bot_token: Optional bot token. If not provided, will get from company integration
    
    Returns:
        dict: Response from Slack API with 'ok' status and other details
        
    Example:
        result = send_message_to_channel("C1234567890", "Hello channel!", company=user.company)
        if result.get("ok"):
            logger.debug("Message sent successfully")
        else:
            logger.debug(f"Failed to send message: {result}")
    """
    try:
        # Get bot token from parameter or company integration
        if not bot_token:
            bot_token = get_company_slack_bot_token(company)
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token found for company's Slack integration"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # First, try to join the channel if the bot is not already a member
        try:
            join_response = client.conversations_join(channel=channel_id)
            if join_response.get("ok"):
                logger.debug(f"Bot successfully joined channel {channel_id}")
            else:
                # If join fails, it might already be a member or there might be permission issues
                error = join_response.get("error", "Unknown error")
                if error != "already_in_channel":
                    logger.debug(f"Warning: Could not join channel {channel_id}: {error}")
        except Exception as join_error:
            logger.debug(f"Warning: Error joining channel {channel_id}: {join_error}")
        
        # Send the message
        post_response = client.chat_postMessage(
            channel=channel_id,
            text=message,
            unfurl_links=False,
            unfurl_media=False
        )
        
        if post_response.get("ok"):
            return {
                "ok": True,
                "ts": post_response.get("ts"),
                "message": "Message sent successfully"
            }
        else:
            error = post_response.get("error", "Unknown error")
            if error == "channel_not_found":
                return {
                    "ok": False,
                    "error": f"Channel {channel_id} not found or bot doesn't have access"
                }
            elif error == "not_in_channel":
                return {
                    "ok": False,
                    "error": f"Bot is not in channel {channel_id} and cannot join automatically"
                }
            else:
                return {
                    "ok": False,
                    "error": f"Failed to send message: {error}"
                }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }


def get_user_info(user_id: str, company=None, bot_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Get information about a user by their user ID.
    
    Args:
        user_id: The Slack user ID to get information for
        company: Company instance to get bot token from
        bot_token: Optional bot token. If not provided, will get from company integration
    
    Returns:
        dict: User information or error details
        
    Example:
        user_info = get_user_info("U093656FK5G", company=user.company)
        if user_info.get("ok"):
            logger.debug(f"User name: {user_info['user']['name']}")
            logger.debug(f"Real name: {user_info['user']['real_name']}")
        else:
            logger.debug(f"Failed to get user info: {user_info}")
    """
    try:
        # Get bot token from parameter or company integration
        if not bot_token:
            bot_token = get_company_slack_bot_token(company)
        
        if not bot_token:
            return {
                "ok": False,
                "error": "No bot token found for company's Slack integration"
            }
        
        # Initialize Slack client
        client = WebClient(token=bot_token)
        
        # Get user info
        user_response = client.users_info(user=user_id)
        
        if user_response.get("ok"):
            return {
                "ok": True,
                "user": user_response["user"]
            }
        else:
            return {
                "ok": False,
                "error": f"Failed to get user info: {user_response.get('error', 'Unknown error')}"
            }
            
    except Exception as e:
        return {
            "ok": False,
            "error": f"Exception occurred: {str(e)}"
        }
