# Slack Integration Setup Guide

## Overview
This document outlines the required configuration steps for Slack integration to work properly.

## Prerequisites
- Slack App created at https://api.slack.com/apps
- App ID: `A090UT57GP8`

## Required Configuration Steps

### 1. Event Subscriptions Configuration
**URL:** https://api.slack.com/apps/A090UT57GP8/event-subscriptions

**Steps:**
1. Navigate to the Event Subscriptions page
2. Enable Events
3. Set Request URL to your webhook endpoint
4. Subscribe to relevant bot events:
   - `message.channels` - For channel messages
   - `message.groups` - For private group messages
   - `message.im` - For direct messages
   - `message.mpim` - For multi-person direct messages

### 2. OAuth & Permissions Configuration
**URL:** https://api.slack.com/apps/A090UT57GP8/oauth

**Steps:**
1. Navigate to the OAuth & Permissions page
2. Add required scopes:
   - `channels:read` - Read public channels
   - `groups:read` - Read private channels
   - `im:read` - Read direct messages
   - `mpim:read` - Read group direct messages
   - `chat:write` - Send messages
   - `users:read` - Read user information
3. Install the app to your workspace
4. Copy the Bot User OAuth Token

## Important Notes

⚠️ **Both configurations are required** for Slack messages to be received and processed properly.

- **Event Subscriptions** handle incoming messages from Slack
- **OAuth & Permissions** provide the necessary access tokens and scopes

## Environment Variables

Make sure to set these environment variables:
```bash
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_SIGNING_SECRET=your-signing-secret
SLACK_APP_TOKEN=xapp-your-app-token
```

## Testing

After configuration:
1. Send a message in any channel where the bot is present
2. Check the Django logs for incoming webhook events
3. Verify the bot can respond to messages

## Troubleshooting

- **No messages received:** Check Event Subscriptions configuration
- **Permission errors:** Verify OAuth scopes are correctly set
- **Webhook errors:** Ensure the Request URL is accessible and returns 200 OK