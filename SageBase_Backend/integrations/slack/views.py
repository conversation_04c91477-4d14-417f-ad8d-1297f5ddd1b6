from django.shortcuts import redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
import os
import secrets
import logging
from django.http import HttpResponseRedirect, JsonResponse
import requests
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST, require_http_methods
from django.utils.decorators import method_decorator
from django.http import JsonResponse
import json
from integrations.models import User, SlackUserProfile

from slack_sdk.web import WebClient
from integrations.models import CompanyIntegration
from rest_framework.decorators import api_view
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny

from .oauth_providers import SlackOAuthProvider
from django.conf import settings
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType
from datetime import datetime
from integrations.slack.services import get_user_info

logger = logging.getLogger(__name__)

def slack_oauth_connect(request, provider_name):
    """Initiate OAuth connection for Slack"""
    if provider_name != 'slack':
        messages.error(request, f"Provider '{provider_name}' not supported")
        return redirect('/')
    
    # Get redirect URI from env and replace {BASE_URL} if needed
    redirect_uri = os.getenv('SLACK_REDIRECT_URI', '')
    if redirect_uri and "{BASE_URL}" in redirect_uri:
        base_url = os.getenv("BACKEND_BASE_URL")
        redirect_uri = redirect_uri.replace("{BASE_URL}", base_url)
    
    provider = SlackOAuthProvider(
        client_id=os.getenv('SLACK_CLIENT_ID', ''),
        client_secret=os.getenv('SLACK_CLIENT_SECRET', ''),
        redirect_uri=redirect_uri
    )
    
    state = secrets.token_urlsafe(32)
    request.session['oauth_state_slack'] = state
    logger.info(f"Generated state: {state} for slack")
    auth_url = provider.get_authorization_url(state)
    logger.info(f"Redirecting to: {auth_url}")
    return redirect(auth_url)


@csrf_exempt
def slack_oauth_callback(request, provider_name):
    """Handle OAuth callback for Slack"""
    logger.info(f"Entering oauth_callback for provider: {provider_name}")
    
    # Get redirect URI from env and replace {BASE_URL} if needed
    redirect_uri = os.getenv('SLACK_REDIRECT_URI', '')
    if redirect_uri and "{BASE_URL}" in redirect_uri:
        base_url = os.getenv("BACKEND_BASE_URL")
        redirect_uri = redirect_uri.replace("{BASE_URL}", base_url)

    if provider_name != 'slack':
        logger.error(f"Provider '{provider_name}' not supported")
        messages.error(request, f"Provider '{provider_name}' not supported")
        return redirect('/')
    
    provider = SlackOAuthProvider(
        client_id=os.getenv('SLACK_CLIENT_ID', ''),
        client_secret=os.getenv('SLACK_CLIENT_SECRET', ''),
        redirect_uri=redirect_uri
    )
    
    code = request.GET.get('code')
    state = request.GET.get('state')
    error = request.GET.get('error')
    logger.info(f"Callback params - Code: {code}, State: {state}, Error: {error}")
    
    stored_state = request.session.get('oauth_state_slack')
    logger.info(f"Stored state from session: {stored_state}")
    logger.info(f"Received state from callback: {state}")
    logger.info(f"Session key: {request.session.session_key}")
    logger.info(f"Session data: {dict(request.session.items())}")
    logger.info(f"States match: {stored_state == state}")
    
    if error:
        logger.error(f"Slack authorization failed: {error}")
        messages.error(request, f"Slack authorization failed: {error}")
        return redirect('/')
    
    if not code:
        logger.error("No authorization code received")
        messages.error(request, "Authorization code not received")
        return redirect('/')
    
    if not stored_state or stored_state != state:
        logger.error("Invalid state parameter")
        messages.error(request, "Invalid authorization state")
        return redirect('/')
    
    try:
        logger.info("Attempting token exchange")
        token_data = provider.exchange_code_for_token(code)
        bot_token = token_data.get("access_token")
        user_token = token_data.get("authed_user", {}).get("access_token")
        slack_user_id = token_data.get("authed_user", {}).get("id")
        team_id = token_data.get("team_id") or (token_data.get("team") or {}).get("id")
        team_name = token_data.get("team_name") or (token_data.get("team") or {}).get("name")
        logger.info(f"Token data: {token_data}")
        logger.info(f"Team ID: {team_id}, Team Name: {team_name}") #example Team ID: T0908KH06TV, Team Name: SageBase   
        
        
        #get user instance based on the email
        user_info = get_user_info(slack_user_id, bot_token=bot_token)
        company_instance=None
            
        if user_info and user_info.get('user'):
            user_profile = user_info['user'].get('profile', {})
            email = user_profile.get('email')
            if email:
                user = User.objects.filter(email__iexact=email.strip()).first()
                if not user:
                    logger.error(f"[DEBUG] No user found for email: {email}--> slack_user_id: {slack_user_id} can not integrate slack")
                    return JsonResponse({'error': 'User needs to be admin in sagebase.tech to integrate slack'}, status=404)
                else:
                    company_instance = user.company
            if not email:
                from integrations.models import Company
                # try to locate the company by its teamid:
                try:
                    company_instance = Company.objects.filter(name=team_name).first()
                except Exception as e:
                    logger.error(f"[DEBUG] Error locating company by name: {e}")
                    return JsonResponse({'error': 'Error locating company by name'}, status=404)
                if not company_instance:
                    logger.error(f"[DEBUG] No company found for team_id: {team_id}")
                    return JsonResponse({'error': 'Company not found'}, status=404)

         
        
        #now that we authenticated, lets create the company integration
        # Store team information in the user's company integration config
            #TODO: for nour: the team_id is empty! 
            if team_id and company_instance:
                from integrations.models import CompanyIntegration, IntegrationTool
                
                # Get or create Slack integration for this company
                slack_tool, _ = IntegrationTool.objects.get_or_create(
                    slug='slack',
                    defaults={'name': 'Slack', 'description': 'Slack messaging platform'}
                )
                
                integration, integration_created = CompanyIntegration.objects.get_or_create(
                    company=company_instance,
                    tool=slack_tool,
                    defaults={
                        'status': 'CONNECTED',
                        'is_active': True,
                        'config': {
                            'team_id': team_id,
                            'team_name': team_name,
                            'bot_token': bot_token,
                            'connected_at': datetime.now().isoformat()
                        }
                    }
                )
                
                if not integration_created:
                    # Update existing integration with team info
                    config = integration.config or {}
                    config.update({
                        'team_id': team_id,
                        'team_name': team_name,
                        'bot_token': bot_token,
                        'connected_at': datetime.now().isoformat()
                    })
                    integration.config = config
                    integration.status = 'CONNECTED'
                    integration.is_active = True
                    integration.save()
                
                logger.debug(f"[DEBUG] {'Created' if integration_created else 'Updated'} Slack integration for company '{company_instance.name}' with team '{team_name}' ({team_id})")
        
        # Authenticate with SlackService using the new bot_token
        slack_service = SlackService()
        import asyncio
        def get_or_create_event_loop():
            try:
                return asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return loop
        loop = get_or_create_event_loop()
        loop.run_until_complete(slack_service.authenticate({"bot_token": bot_token}))
        # Optionally, send a welcome message to the user (if you want)
        # welcome_message = Message(content="Welcome to SageBase! Your Slack integration is now active.", message_type=MessageType.TEXT)
        # loop.run_until_complete(slack_service.send_message(slack_user_id, welcome_message))
        # Clear state from session
        del request.session['oauth_state_slack']
        request.session.modified = True
        import json
        combined_token = json.dumps({
            "slack_bot_token": bot_token,
            "slack_user_token": user_token,
            "slack_user_id": slack_user_id,
            "slack_team_id": team_id,
            "slack_team_name": team_name
        })
        messages.success(request, "Successfully connected to Slack!")
        frontend_redirect_url = f"{settings.FRONTEND_BASE_URL}/slack/callback?token={combined_token}"
    except Exception as e:
        logger.error(f"Error in callback: {str(e)}")
        messages.error(request, f"Error connecting to Slack: {str(e)}")
        frontend_redirect_url = "/"

    return HttpResponseRedirect(frontend_redirect_url)

# New API endpoint to create/update SlackUserProfile
@csrf_exempt
@require_http_methods(["POST", "DELETE"])
def save_slack_user_profile(request, provider_name=None):
    try:
        if request.method == "POST":
            data = json.loads(request.body.decode('utf-8'))
            email = data.get('email')
            slack_user_id = data.get('slack_user_id')
            bot_token = data.get('bot_token')
            user_token = data.get('user_token')
            team_id = data.get('team_id')  # Extract team ID
            team_name = data.get('team_name')  # Extract team name
            logger.debug(f"[DEBUG] Received email: {email}")
            logger.debug(f"[DEBUG] Team ID: {team_id}")
            logger.debug(f"[DEBUG] Team Name: {team_name}")
            logger.debug(f"[DEBUG] All user emails in DB: {list(User.objects.values_list('email', flat=True))}")
            if not all([email, slack_user_id]):
                return JsonResponse({'error': 'Missing required fields'}, status=400)
            user = User.objects.filter(email__iexact=email.strip()).first()
            if not user:
                logger.debug(f"[DEBUG] No user found for email: {email}")
                return JsonResponse({'error': 'User not found'}, status=404)
            
            # Create or update the SlackUserProfile with team information
            # Note: bot_token is stored per company in CompanyIntegration, not per user
            profile, created = SlackUserProfile.objects.update_or_create(
                user=user,
                defaults={
                    'slack_user_id': slack_user_id,
                    'user_token': user_token,
                    'is_active': True,
                    "bot_token": bot_token# this is not needed as it is stored at the company integration
                }
            )
            
            
            
            return JsonResponse({'status': 'success'})
        elif request.method == "DELETE":
            data = json.loads(request.body.decode('utf-8'))
            email = data.get('email')
            if not email:
                return JsonResponse({'error': 'Email is required'}, status=400)
            user = User.objects.filter(email__iexact=email.strip()).first()
            if not user:
                return JsonResponse({'error': 'User not found'}, status=404)
            profile = SlackUserProfile.objects.filter(user=user).first()
            if not profile:
                return JsonResponse({'error': 'Slack user profile not found'}, status=404)
            profile.delete()
            return JsonResponse({'status': 'Slack user profile deleted'})
    except Exception as e:
        logger.debug(f"[DEBUG] Exception in save_slack_user_profile: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def delete_slack_user_profile(request, provider_name=None):
    try:
        data = json.loads(request.body.decode('utf-8'))
        email = data.get('email')
        if not email:
            return JsonResponse({'error': 'Email is required'}, status=400)
        user = User.objects.filter(email__iexact=email.strip()).first()
        if not user:
            return JsonResponse({'error': 'User not found'}, status=404)
        profile = SlackUserProfile.objects.filter(user=user).first()
        if not profile:
            return JsonResponse({'error': 'Slack user profile not found'}, status=404)
        profile.delete()
        return JsonResponse({'status': 'Slack user profile deleted'})
    except Exception as e:
        logger.debug(f"[DEBUG] Exception in delete_slack_user_profile: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def get_thread_replies(request, provider_name=None):
    """API endpoint to retrieve thread replies for a specific message."""
    try:
        data = json.loads(request.body.decode('utf-8'))
        channel_id = data.get('channel_id')
        parent_ts = data.get('parent_ts')
        company_id = data.get('company_id')
        
        if not all([channel_id, parent_ts, company_id]):
            return JsonResponse({'error': 'Missing required fields: channel_id, parent_ts, company_id'}, status=400)
        
        # Get company instance
        from integrations.models import Company
        company = Company.objects.filter(id=company_id).first()
        if not company:
            return JsonResponse({'error': 'Company not found'}, status=404)
        
        # Get thread replies using the service
        from .services import get_thread_replies
        result = get_thread_replies(
            channel_id=channel_id,
            parent_ts=parent_ts,
            company=company
        )
        
        if result.get("ok"):
            return JsonResponse({
                'status': 'success',
                'messages': result.get("messages", []),
                'has_more': result.get("has_more", False),
                'response_metadata': result.get("response_metadata", {})
            })
        else:
            return JsonResponse({
                'status': 'error',
                'error': result.get("error", "Unknown error")
            }, status=400)
            
    except Exception as e:
        logger.error(f"Error in get_thread_replies: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def get_channel_history_with_threads(request, provider_name=None):
    """API endpoint to retrieve channel history including all threaded messages."""
    try:
        data = json.loads(request.body.decode('utf-8'))
        channel_id = data.get('channel_id')
        limit = data.get('limit', 100)
        company_id = data.get('company_id')
        
        if not all([channel_id, company_id]):
            return JsonResponse({'error': 'Missing required fields: channel_id, company_id'}, status=400)
        
        # Get company instance
        from integrations.models import Company
        company = Company.objects.filter(id=company_id).first()
        if not company:
            return JsonResponse({'error': 'Company not found'}, status=404)
        
        # Get channel history with threads using the service
        from .services import get_channel_history_with_threads
        result = get_channel_history_with_threads(
            channel_id=channel_id,
            limit=limit,
            company=company
        )
        
        if result.get("ok"):
            return JsonResponse({
                'status': 'success',
                'messages': result.get("messages", []),
                'has_more': result.get("has_more", False),
                'response_metadata': result.get("response_metadata", {})
            })
        else:
            return JsonResponse({
                'status': 'error',
                'error': result.get("error", "Unknown error")
            }, status=400)
            
    except Exception as e:
        logger.error(f"Error in get_channel_history_with_threads: {e}")
        return JsonResponse({'error': str(e)}, status=500)


def revoke_slack_token(token: str) -> bool:
    try:
        response = requests.post(
            "https://slack.com/api/auth.revoke",
            data={"token": token},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        data = response.json()
        return data.get("ok", False)
    except Exception as e:
        logger.debug(f"[ERROR] Slack token revoke request failed: {e}")
        return False


# @login_required
# @require_http_methods(["POST"])
# def slack_oauth_disconnect(request, provider_name):
#     """Disconnect OAuth integration"""
#     oauth_manager.remove_integration(str(request.user.id), provider_name)
#     messages.success(request, f"Disconnected from {provider_name.title()}")
#     return redirect('/')
