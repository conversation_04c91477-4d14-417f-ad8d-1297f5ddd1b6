import httpx
from typing import List, Dict, Any, Optional
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType
import os

class SlackWebhookService:
    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.headers = {
            "Authorization": f"Bearer {bot_token}",
            "Content-Type": "application/json",
        }

    async def list_webhooks(self, channel_id: str) -> List[Dict[str, Any]]:
        """List webhooks for a specific channel (Note: Slack doesn't have channel-specific webhooks)"""
        # Slack webhooks are workspace-level, not channel-specific
        # This is a placeholder for consistency with GitHub webhook service
        return []

    async def create_webhook(self, channel_id: str, webhook_url: str = None, events: Optional[list] = None) -> Dict[str, Any]:
        """
        Create a webhook for Slack events.
        Note: Slack webhooks are configured at the app level, not channel level.
        This method provides guidance on how to set up the webhook.
        """
        # If webhook_url is not provided, try to get from env and replace {BASE_URL}
        if webhook_url is None:
            webhook_url = os.getenv("SLACK_WEBHOOK_URL")
        if webhook_url and "{BASE_URL}" in webhook_url:
            base_url = os.getenv("BACKEND_BASE_URL")
            webhook_url = webhook_url.replace("{BASE_URL}", base_url)
        # Slack webhooks are configured in the Slack app settings, not via API
        # This method provides instructions for manual setup
        return {
            "message": "Slack webhooks must be configured manually in your Slack app settings",
            "instructions": [
                "1. Go to your Slack app settings at api.slack.com/apps",
                "2. Select your app",
                "3. Go to 'Event Subscriptions'",
                "4. Enable events and add your webhook URL",
                "5. Subscribe to the following bot events:",
                "   - message.channels",
                "   - message.groups",
                "   - message.im",
                "   - message.mpim",
                "   - reaction_added",
                "   - file_shared"
            ],
            "webhook_url": webhook_url,
            "events": events or ["message", "reaction_added", "file_shared"]
        }

    async def delete_webhook(self, channel_id: str, hook_id: str) -> bool:
        """Delete a webhook (Note: Slack webhooks are managed in app settings)"""
        # Slack webhooks are configured in the Slack app settings, not via API
        return True

    async def test_webhook(self, webhook_url: str, channel_id: str) -> Dict[str, Any]:
        """Test if a webhook URL is accessible by sending a test message using the messaging interface."""
        try:
            # Instead of sending a test payload directly, use SlackService to send a test message
            slack_service = SlackService()
            await slack_service.authenticate({"bot_token": self.bot_token})
            test_message = Message(content="Test webhook message", message_type=MessageType.TEXT)
            result = await slack_service.send_message(channel_id, test_message)
            return {
                "status": "success",
                "result": result
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            } 