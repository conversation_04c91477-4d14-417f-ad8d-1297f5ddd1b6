import json
from channels.generic.websocket import AsyncWebsocketConsumer

class SlackNotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        print(f"[WS][CONNECT] {self.channel_name} connected.")
        await self.accept()
        self.joined_channels = set()

    async def disconnect(self, close_code):
        print(f"[WS][DISCONNECT] {self.channel_name} disconnected. Leaving channels: {self.joined_channels}")
        for channel in self.joined_channels:
            await self.channel_layer.group_discard(channel, self.channel_name)

    async def receive(self, text_data):
        data = json.loads(text_data)
        action = data.get("action")
        channel_id = data.get("channel_id")
        company_id = data.get("company_id")
        
        if action == "subscribe" and channel_id and company_id:
            group_name = f"slack_{company_id}_{channel_id}"
            await self.channel_layer.group_add(group_name, self.channel_name)
            self.joined_channels.add(group_name)
            print(f"[WS][SUBSCRIBE] {self.channel_name} subscribed to {group_name}")
        elif action == "unsubscribe" and channel_id and company_id:
            group_name = f"slack_{company_id}_{channel_id}"
            await self.channel_layer.group_discard(group_name, self.channel_name)
            self.joined_channels.discard(group_name)
            print(f"[WS][UNSUBSCRIBE] {self.channel_name} unsubscribed from {group_name}")

    async def notify(self, event):
        print(f"[WS][NOTIFY] Sending notification to {self.channel_name}: {event['notification']}")
        await self.send(text_data=json.dumps(event["notification"])) 