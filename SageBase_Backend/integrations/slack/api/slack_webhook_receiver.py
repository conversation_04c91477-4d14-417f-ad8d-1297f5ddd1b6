from typing import List
from rest_framework.views import APIView
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import HttpRequest
import hmac
import hashlib
import json
import os
import logging
from datetime import datetime
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync, sync_to_async
from integrations.models import MonitoredItem, CompanyIntegration, SlackUserProfile, User
from integrations.slack.services import get_user_info
from knowledge_spaces_Q_A.chat_models import ChatUserContext
from knowledge_spaces_Q_A.chat_response_formatting_agent import ChatOutputFormat
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType
from messaging.base_interface import ReceivedMessage

logger = logging.getLogger(__name__)

# Simple cache for admin user ID to avoid rate limiting
_admin_user_id_cache = None
_cache_timestamp = 0
CACHE_DURATION = 300  # 5 minutes

# Redis-based cache for processed events to prevent duplicates across workers
EVENT_CACHE_DURATION = 3600  # 1 hour

def get_redis_client():
    """Get Redis client for event deduplication"""
    try:
        import redis
        from django.conf import settings
        
        # Try to get Redis URL from environment or Django settings
        redis_url = getattr(settings, 'REDIS_URL', None) or \
                   os.getenv('REDIS_URL', None)
        
        if redis_url and isinstance(redis_url, str):
            return redis.from_url(redis_url)
        
        # Fallback to default Redis connection
        redis_host = getattr(settings, 'REDIS_HOST', 'localhost')
        redis_port = getattr(settings, 'REDIS_PORT', 6379)
        redis_db = getattr(settings, 'REDIS_DB', 0)
        
        # If we're running in a container and the host is 'redis', try both container and host
        if redis_host == 'redis':
            # Try container first (redis:6379)
            try:
                container_client = redis.Redis(
                    host='redis',
                    port=6379,
                    db=redis_db,
                    decode_responses=True,
                    socket_connect_timeout=2
                )
                container_client.ping()
                return container_client
            except:
                # Fallback to host port (localhost:6380)
                try:
                    host_client = redis.Redis(
                        host='localhost',
                        port=6380,
                        db=redis_db,
                        decode_responses=True,
                        socket_connect_timeout=2
                    )
                    host_client.ping()
                    return host_client
                except:
                    pass
        
        # Default connection
        return redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True
        )
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        return None

def is_event_processed(event_id: str) -> bool:
    """Check if an event has already been processed using Redis"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            logger.warning("Redis not available, falling back to in-memory cache")
            return False
        
        # Check if event exists in Redis
        key = f"slack_processed_event:{event_id}"
        return redis_client.exists(key) > 0
        
    except Exception as e:
        logger.error(f"Error checking if event is processed: {e}")
        return False

def mark_event_processed(event_id: str):
    """Mark an event as processed using Redis"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            logger.warning("Redis not available, cannot mark event as processed")
            return
        
        # Store event with TTL
        key = f"slack_processed_event:{event_id}"
        redis_client.setex(key, EVENT_CACHE_DURATION, "processed")
        
    except Exception as e:
        logger.error(f"Error marking event as processed: {e}")

def cleanup_old_events():
    """Cleanup old events from Redis (Redis handles TTL automatically)"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            return
        
        # Redis automatically handles TTL, so we don't need manual cleanup
        # But we can log some stats for monitoring
        pattern = "slack_processed_event:*"
        keys = redis_client.keys(pattern)
        if keys:
            logger.debug(f"Redis contains {len(keys)} processed event keys")
            
    except Exception as e:
        logger.error(f"Error during Redis cleanup: {e}")

def notify_frontend(event_type, data):
    """Notify frontend via WebSocket about Slack events"""
    data_json = json.dumps(data)
    logger.info(f"[Notify Frontend] {event_type}: {data_json}")
    
    channel_id = data.get("channel_id")
    company_id = data.get("company_id")
    
    if channel_id and company_id:
        # Create group name for the channel
        group_name = f"slack_{company_id}_{channel_id}"
        logger.info(f"[WS][GROUP_SEND] Preparing to send to group '{group_name}' for event '{event_type}' with data: {data_json}")
        
        channel_layer = get_channel_layer()
        notification_data = dict(data)
        
        async_to_sync(channel_layer.group_send)(
            group_name,
            {
                "type": "notify",
                "notification": notification_data
            }
        )
        logger.info(f"[WS][GROUP_SEND] Sent notification to group '{group_name}' for event '{event_type}'.")

async def get_bot_token_for_user(slack_user_id):
    import asyncio
    
    def _get_bot_token():
        try:
            profile = SlackUserProfile.objects.get(slack_user_id=slack_user_id, is_active=True)
            return profile.bot_token
        except SlackUserProfile.DoesNotExist:
            logger.warning(f"[Slack Webhook] No SlackUserProfile found for slack_user_id={slack_user_id}, ignoring event.")
            return None
    
    return await asyncio.to_thread(_get_bot_token)

async def get_company_name_for_slack_user(slack_user_id):
    """Get the company name for a Slack user ID"""
    import asyncio
    
    def _get_company_name():
        try:
            profile = SlackUserProfile.objects.get(slack_user_id=slack_user_id, is_active=True)
            user = profile.user
            company = user.company
            return company  # Normalize company name for workspace
        except SlackUserProfile.DoesNotExist:
            logger.warning(f"[Slack Webhook] No SlackUserProfile found for slack_user_id={slack_user_id}")
            return None
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error getting company name for slack_user_id={slack_user_id}: {e}")
            return None
    
    return await asyncio.to_thread(_get_company_name)

async def get_company_name_for_slack_team(team_id):
    """Get the company name for a Slack team ID - async with asyncio.to_thread"""
    try:
        # Find CompanyIntegration with matching team_id in config
        from integrations.models import CompanyIntegration, IntegrationTool
        import asyncio
        
        def _get_company():
            slack_tool = IntegrationTool.objects.filter(slug='slack').first()
            if not slack_tool:
                return None, None
            
            integration = CompanyIntegration.objects.filter(
                tool=slack_tool,
                is_active=True,
                config__team_id=team_id
            ).first()
            
            if integration:
                company_name = integration.company.name.lower().replace(' ', '_')
                return integration, company_name
            
            return None, None
        
        integration, company_name = await asyncio.to_thread(_get_company)
        
        if integration:
            logger.info(f"[Slack Webhook] Found company '{integration.company.name}' for team '{team_id}'")
            return company_name
        elif integration is None and company_name is None:
            logger.warning(f"[Slack Webhook] No Slack integration tool found")
            return None
        else:
            logger.warning(f"[Slack Webhook] No company found for team '{team_id}'")
            return None
        
    except Exception as e:
        logger.exception(f"[Slack Webhook] Error getting company name for team_id={team_id}: {e}")
        return None



async def get_company_and_team_info_for_slack_team(team_id):
    """Get both company  instance and team name for a Slack team ID - async with asyncio.to_thread
    None if no match between team_id and company_integration
    """
    try:
        # Find CompanyIntegration with matching team_id in config
        from integrations.models import CompanyIntegration, IntegrationTool
        import asyncio
        
        def _get_integration():
            slack_tool = IntegrationTool.objects.filter(slug='slack').first()
            if not slack_tool:
                return None, None, None
            
            integration = CompanyIntegration.objects.filter(
                tool=slack_tool,
                is_active=True,
                config__team_id=team_id
            ).first()
            
            if integration:
                company_instance = integration.company
                team_name = integration.config.get('team_name', 'Unknown Team')
                return integration, company_instance, team_name
            
            return None, None, None
        
        integration, company_instance, team_name = await asyncio.to_thread(_get_integration)
        
        if integration:
            logger.info(f"[Slack Webhook] Found company '{integration.company.name}' for team '{team_name}' ({team_id})")
            return company_instance, team_name
        elif integration is None and company_instance is None and team_name is None:
            logger.warning(f"[Slack Webhook] No Slack integration tool found")
            return None, None
        else:
            logger.warning(f"[Slack Webhook] No company found for team '{team_id}'")
            return None, None
        
    except Exception as e:
        logger.exception(f"[Slack Webhook] Error getting company and team info for team_id={team_id}: {e}")
        return None, None

async def create_group_channel_and_invite_users(slack_user_id, support_user_ids, original_user, original_message, original_channel):
    """
    Create a new private channel and invite the support team and the user who asked the question.
    Uses the messaging interface abstraction and retrieves the bot token from the DB.
    """
    try:
        bot_token = await get_bot_token_for_user(slack_user_id)
        if not bot_token:
            logger.warning(f"[Slack Webhook] No bot token found for slack_user_id={slack_user_id}")
            return {"ok": False, "error": "No bot token found"}
        
        slack_service = SlackService()
        await slack_service.authenticate({"bot_token": bot_token})

        # Create a new group chat (private channel)
        group_name = original_message.strip().split()[0].lower() if original_message.strip().split() else "support"
        import re, time
        group_name = re.sub(r'[^a-z0-9-]', '', group_name)
        if len(group_name) < 2:
            group_name = "support"
        timestamp = int(time.time())
        channel_name = f"{group_name}-{timestamp}"
        
        # Create group chat
        group_chat = await slack_service.create_group_chat(channel_name, support_user_ids + [original_user])
        new_channel_id = group_chat.id
        new_channel_name = group_chat.name
        logger.info(f"[Slack Webhook] ✅ Created new channel: {new_channel_name} ({new_channel_id})")

        # Post the original message in the new channel
        formatted_message = (
            f"📨 **Question from <@{original_user}> in <#{original_channel}>**\n\n"
            f"**Message:** {original_message}\n\n"
            f"*This channel was created to handle your question. Feel free to discuss here!*"
        )
        message = Message(content=formatted_message, message_type=MessageType.TEXT)
        await slack_service.send_message(new_channel_id, message)
        logger.info(f"[Slack Webhook] ✅ Posted message in new channel {new_channel_name}")

        # Send a notification to the original channel
        notification_message = f"<@{original_user}> Your question has been moved to <#{new_channel_id}> for better assistance."
        notification = Message(content=notification_message, message_type=MessageType.TEXT)
        await slack_service.send_message(original_channel, notification)
        logger.info(f"[Slack Webhook] ✅ Sent notification to original channel")

        return {"ok": True, "channel_id": new_channel_id, "channel_name": new_channel_name}
    except Exception as e:
        logger.exception(f"[Slack Webhook] ❌ Error creating channel and inviting users: {e}")
        return {"ok": False, "error": str(e)}
    finally:
        # Ensure SlackService session is properly closed
        try:
            await slack_service.disconnect()
            logger.debug("[Slack Webhook] ✅ SlackService session closed in create_group_channel")
        except Exception as cleanup_error:
            logger.warning(f"[Slack Webhook] ⚠️ Failed to close SlackService session: {cleanup_error}")

def get_support_user_ids(bot_token):
    """
    Get the hardcoded support user IDs (users who should be invited to support channels).
    
    Args:
        bot_token: Slack bot token (not used in this simplified version)
    
    Returns:
        list: List of hardcoded support user IDs
    """
    # Hardcoded support user IDs - replace with your desired user IDs
    # You can find user IDs by looking at the logs when the bot runs
    # or by using the Slack API to list users
    HARDCODED_SUPPORT_USER_IDS = [
        "U093656FK5G",
        "U0908KH070B"
    ]
    
    logger.info(f"[Slack Webhook] ✅ Using hardcoded support user IDs: {HARDCODED_SUPPORT_USER_IDS}")
    return HARDCODED_SUPPORT_USER_IDS

def list_all_users_for_debugging(bot_token):
    """
    Debug function to list all users and their IDs.
    Use this to find user IDs for hardcoding.
    
    Args:
        bot_token: Slack bot token
    
    Returns:
        None (prints to console)
    """
    try:
        from slack_sdk import WebClient
        client = WebClient(token=bot_token)
        response = client.users_list()
        
        if response.get("ok"):
            users = response["users"]
            print("\n" + "="*60)
            print("SLACK USERS LIST (for finding user IDs)")
            print("="*60)
            
            for user in users:
                if not user.get("is_bot") and not user.get("deleted"):
                    user_id = user.get("id", "N/A")
                    username = user.get("name", "N/A")
                    real_name = user.get("real_name", "N/A")
                    is_admin = user.get("is_admin", False)
                    is_owner = user.get("is_owner", False)
                    
                    print(f"ID: {user_id}")
                    print(f"  Username: {username}")
                    print(f"  Real Name: {real_name}")
                    print(f"  Admin: {is_admin}, Owner: {is_owner}")
                    print("-" * 40)
        else:
            logger.error(f"[Slack Webhook] ❌ Failed to get users list: {response}")
            
    except Exception as e:
        logger.exception(f"[Slack Webhook] ❌ Error listing users: {e}")

@method_decorator(csrf_exempt, name='dispatch')
class SlackWebhookReceiverView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request: HttpRequest, *args, **kwargs):
        logger.info("[Slack Webhook] Received POST request")
        
        # Verify Slack signature
        if not self._verify_slack_signature(request):
            logger.warning("[Slack Webhook] Invalid signature!")
            return Response({"error": "Invalid signature"}, status=400)
        
        # Parse the payload
        payload = request.data if isinstance(request.data, dict) else json.loads(request.body)
        
        # Handle URL verification challenge
        if payload.get("type") == "url_verification":
            challenge = payload.get("challenge")
            logger.info(f"[Slack Webhook] URL verification challenge: {challenge}")
            return Response({"challenge": challenge})
        
        # Handle events
        if payload.get("type") == "event_callback":
            event = payload.get("event", {})
            event_type = event.get("type")
            
            if event_type == "message" and "subtype" not in event:
                return self._handle_message_event_sync(event, payload)
            elif event_type == "reaction_added":
                return self._handle_reaction_event_sync(event, payload)
            elif event_type == "file_shared":
                return self._handle_file_event_sync(event, payload)
        
        return Response({"status": "received"})

    def _handle_message_event_sync(self, event, payload):
        """Handle Slack message events using sync wrapper for async operations"""
        import asyncio
        
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # Run the async handler in the new event loop
                result = loop.run_until_complete(self._handle_message_event(event, payload))
                return result
            finally:
                loop.close()
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error in sync wrapper: {e}")
            return Response({"status": "error_handled"}, status=200)

    def _handle_reaction_event_sync(self, event, payload):
        """Handle Slack reaction events using sync wrapper"""
        import asyncio
        
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._handle_reaction_event(event, payload))
            finally:
                loop.close()
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error in reaction sync wrapper: {e}")
            return Response({"status": "error_handled"}, status=200)

    def _handle_file_event_sync(self, event, payload):
        """Handle Slack file events using sync wrapper"""
        import asyncio
        
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._handle_file_event(event, payload))
            finally:
                loop.close()
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error in file sync wrapper: {e}")
            return Response({"status": "error_handled"}, status=200)

    async def _get_slack_user_info(self, slack_user_id, team_id):
        """Get user information from Slack API"""
        try:
            # Get bot token for the team/workspace
            bot_token = os.getenv('SLACK_BOT_TOKEN')
            if not bot_token:
                logger.warning("⚠️ SLACK_BOT_TOKEN not found in environment")
                return None
            
            # Initialize and authenticate SlackService
            slack_service = SlackService()
            authenticated = await slack_service.authenticate({
                'bot_token': bot_token
            })
            
            if not authenticated:
                logger.warning("⚠️ Failed to authenticate with Slack API")
                return None
            
            # Try to get user info using Slack Web API
            # Note: This requires the bot token to have users:read scope
            user_info = get_user_info(slack_user_id)
            
            if user_info and user_info.get('user'):
                user_profile = user_info['user'].get('profile', {})
                return {
                    'id': user_info['user'].get('id'),
                    'name': user_info['user'].get('name'),
                    'real_name': user_info['user'].get('real_name'),
                    'email': user_profile.get('email'),
                    'display_name': user_profile.get('display_name'),
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting Slack user info for {slack_user_id}: {e}")
            return None

    async def _handle_message_event(self, event, payload):
        """Handle Slack message events using base interface abstraction"""
        channel_id = event.get("channel")
        slack_user_id = event.get("user", "Unknown")
        bot_id = event.get("bot_id")
        text = event.get("text", "")
        timestamp = event.get("ts", "")
        team_id = event.get("team")  # Extract team ID from the event
        event_id = payload.get("event_id", "no_event_id")
        
        # Prevent bot loop: ignore messages from bots (including itself)
        if bot_id is not None:
            logger.info(f"[Slack Webhook] Ignoring message from bot (bot_id: {bot_id})")
            return Response({"status": "ignored bot message"})
        
        # Get User instance and ID from SlackUserProfile
        user_id = None
        user_email = None
        user = None
        if os.getenv("DJANGO_ENV") == "development":
            from integrations.models import Company
            from asgiref.sync import sync_to_async
            
            def get_company():
                return Company.objects.get(name="sagebase")
            
            company_instance = await sync_to_async(get_company, thread_sensitive=False)()
        elif team_id :
            company_instance, team_name = await get_company_and_team_info_for_slack_team(team_id)
            if company_instance:
                logger.info(f"[Slack Webhook] ✅ Using company uid '{company_instance.id}' from team '{team_name}' ({team_id})")
                
            else:
                logger.error("message received but not processed")
                return Response({"status": "ignored - no workspace"}, status=200)
        
        try:
            import asyncio
            
            def _get_user_info():
                try:
                    # First, try to find existing SlackUserProfile
                    slack_profile = SlackUserProfile.objects.filter(slack_user_id=slack_user_id, is_active=True).first()
                    
                    if slack_profile:
                        try:
                            user = slack_profile.user
                            user_id = str(user.id)
                            if user :
                                user_email = user.email
                                logger.info(f"📨 Slack message from {user_email} (User ID: {user_id}) in channel {channel_id}")
                                return user_id, user_email, True  # Found existing profile
                            else:
                                logger.warning(f"⚠️ User company doesn't match workspace for slack_user_id: {slack_user_id}")
                                return user_id, user_email, True  # Need to try email lookup
                        except Exception as e:
                            logger.exception(f"❌ Error getting Slack user info: {e}")
                            return None, None, False  # Need to try email lookup
                    else:
                        logger.warning(f"⚠️ No active SlackUserProfile found for slack_user_id: {slack_user_id}-->contintuing as a guest user")
                        return None, None, False  # Need to try email lookup
                    
                    
                except Exception as e:
                    logger.exception(f"❌ Error getting user from SlackUserProfile: {e}")
                    return None, None, False
            
            sagebase_user_id, user_email, found_profile = await sync_to_async(_get_user_info, thread_sensitive=False)()
            
            if not found_profile and not sagebase_user_id:
                logger.debug(f"No active SlackUserProfile found for slack_user_id: {slack_user_id}")
                
                # Try to get user info from Slack API and match by email
                # We don't need to idnetify each user, as we serve anyone in the channels
                try:
                    slack_user_info = await self._get_slack_user_info(slack_user_id, team_id)
                    if slack_user_info and slack_user_info.get('email'):
                        slack_email = slack_user_info['email']
                        logger.info(f"🔍 Incoming slack message in team {team_id} from email: {slack_email}")
                        
                    
                except Exception as e:
                    logger.debug(f"Could not find Slack user info: {e}")
                
        except Exception as e:
            logger.exception(f"❌ Error in user lookup process: {e}")
        
        # Format timestamp first
        try:
            dt = datetime.fromtimestamp(float(timestamp))
            formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            formatted_time = timestamp
            
        # Log ALL incoming messages (before duplicate check)
        logger.debug(f"\n📨 [SLACK WEBHOOK] Incoming message in channel {channel_id}")
        logger.debug(f"   Slack User ID: {slack_user_id}")
        logger.debug(f"   Team: {team_id}")
        logger.debug(f"   Time: {formatted_time}")
        logger.debug(f"   Message: {text}")
        logger.debug(f"   Event ID: {event_id}")
        logger.debug(f"   {'─' * 50}")
        
        # Prevent duplicate processing using Redis
        cleanup_old_events()  # Optional: log Redis stats
        
        # Check if we've already processed this event
        if is_event_processed(event_id):
            logger.warning(f"[Slack Webhook] ⚠️ Event {event_id} already processed, skipping")
            return Response({"status": "already_processed"})
        
        logger.warning("consumin recieved message")
        # Mark event as processed
        mark_event_processed(event_id)
        
        # Respond to Slack immediately
        response = Response({"status": "message_processed"})

        # Use base interface to handle the message
         # Use base interface to handle the message
        try:
            # Get bot token for the user with connection management
            bot_token = os.getenv("SLACK_BOT_TOKEN")
            if not bot_token:
                logger.warning(f"[Slack Webhook] No bot token found for slack_user_id={slack_user_id}, ignoring event.")
                return Response({"status": "ignored - no user profile"}, status=200)
            
            # Initialize SlackService using base interface
            slack_service = SlackService()
            
            try:
                await slack_service.authenticate({"bot_token": bot_token})
            except Exception as auth_error:
                logger.error(f"[Slack Webhook] Authentication error: {auth_error}")
                return Response({"status": "authentication_failed"}, status=200)

            # Use base interface to receive messages from the channel
            logger.info(f"[Slack Webhook] Using base interface to receive messages from channel {channel_id}")
            try:
                # Fetch the last 10 messages for context (instead of just 1)
                received_messages = await slack_service.receive_messages(channel_id=channel_id, limit=10)
            except Exception as receive_error:
                logger.error(f"[Slack Webhook] Error receiving messages: {receive_error}")
                return Response({"status": "receive_failed"}, status=200)
                
            if len(received_messages) > 0:
                latest_message = received_messages[0]  # Most recent message
                logger.info(f"[Slack Webhook] ✅ Received {len(received_messages)} messages via base interface:")
                logger.info(f"   Latest Content: {latest_message.content}")
                logger.info(f"   Latest Sender: {latest_message.sender_name} ({latest_message.sender_id})")
                logger.info(f"   Latest Type: {latest_message.message_type}")
                logger.info(f"   Conversation History: {len(received_messages)} messages")
                
                # Process the message using base interface with team context
                await self._process_message_with_base_interface(slack_service, latest_message, received_messages, channel_id, team_id, user_id)
            else:
                logger.warning(f"[Slack Webhook] ⚠️ No messages received via base interface")
                
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error processing message with base interface: {e}")
            # Return success to prevent Slack from retrying
            return Response({"status": "error_handled"}, status=200)

        return response

    async def get_channel_history(user_id: str, channel_id: str, limit: int = 20) -> str:
        """
        Get the history of a channel from Slack API.
        Args:
            channel_id: The ID of the channel to get the history of.
            limit: The maximum number of messages to return.
        Returns:
            A JSON string containing the history of the channel.
        """
        try:
            from messaging.slack.slack_service import SlackService
            from integrations.models import SlackUserProfile, CompanyIntegration
            from asgiref.sync import sync_to_async
            
            # Get bot token from database
            def get_slack_profile():
                return SlackUserProfile.objects.filter(user_id=user_id, is_active=True).first() 
            
            def get_company_integration():
                return CompanyIntegration.objects.filter(
                    tool__slug='slack',
                    is_active=True
                ).first()
            
            # Use thread_sensitive=False to avoid CurrentThreadExecutor issues
            slack_profile = await sync_to_async(get_slack_profile, thread_sensitive=False)()
            company_integration = await sync_to_async(get_company_integration, thread_sensitive=False)()
            
            if slack_profile or company_integration:
                # Initialize SlackService
                slack_service = SlackService()
                
                # Get bot token
                bot_token = None
                if slack_profile and slack_profile.bot_token:
                    bot_token = slack_profile.bot_token
                elif company_integration and company_integration.config:
                    bot_token = company_integration.config.get('bot_token')
                
                if bot_token:
                    # Authenticate and get real messages
                    await slack_service.authenticate({"bot_token": bot_token})
                    messages = await slack_service.receive_messages(channel_id, limit=limit)
                    
                    if messages:
                        # Convert to expected format
                        channel_messages = []
                        for msg in messages:
                            channel_messages.append({
                                "content": msg.content,
                                "user_id": msg.sender_id,
                                "user_name": msg.sender_name,
                                "channel_id": channel_id,
                                "platform": "slack",
                                "timestamp": msg.timestamp,
                            })
                        
                        return json.dumps({
                            "platform": "slack",
                            "channel_id": channel_id,
                            "messages": channel_messages,
                            "total_messages": len(channel_messages),
                            "source": "real_api"
                        })
                    
        except Exception as api_error:
            logger.warning(f"Failed to get real Slack messages: {api_error}")
    


        except Exception as e:
            logger.error(f"Error in external channel history: {e}")
            return json.dumps({"error": str(e), "messages": []})


    async def _process_message_with_base_interface(self, slack_service, received_message, received_messages: List[ReceivedMessage], channel_id, team_id=None, user_id=None):
        """Process received message using base interface methods and question answering system"""
        try:
            # Import the chat orchestrator
            from knowledge_spaces_Q_A.chat_orchestrator import ChatOrchestrator
            from knowledge_spaces_Q_A.chat_models import ConversationType, ChatRequest, ConversationMessage
            from datetime import datetime
            import asyncio
            
            logger.info(f"[Slack Webhook] 🤖 Processing message with Chat Orchestrator")
            logger.info(f"   Message: '{received_message.content}'")
            logger.info(f"   Sender: {received_message.sender_name} ({received_message.sender_id})")
            logger.info(f"   Team: {team_id}")
            
            # Determine company_instance based on team_id first, then fall back to user-based lookup
            company_instance = None
            team_name = None
            if team_id:
                pass
                company_instance, team_name = await get_company_and_team_info_for_slack_team(team_id)
                if company_instance:
                    logger.info(f"[Slack Webhook] ✅ Using company_id '{company_instance.id}' from team '{team_name}' ({team_id})")
            
            # Fall back to user-based lookup if team lookup failed
            if not company_instance and received_message.sender_id:
                logger.error("Comapny and user mismatch for user_id: {received_message.sender_id} and team_id: {team_id}")
                return Response( status=200)
            
            

            # Build collections to search - always include company collection
            collections_to_search = [str(company_instance.id)] if company_instance else []
            #also include private collection if user_id is not None
            if user_id:
                collections_to_search.append(str(user_id))
            
            context = ChatUserContext(
                user_id=user_id,
                company=company_instance,
                collections_to_search=collections_to_search
            )
                
            chat_orchestrator = ChatOrchestrator(context=context)
            slack_channel_info = await slack_service.get_channel_info(channel_id=channel_id)

            history = []
            # Remove the first message from history
            if received_messages:
                history = received_messages[1:]

            # map the history to ConversationMessage objects
            history = [
                ConversationMessage(
                    content=msg.content,
                    sender_id=msg.sender_id,
                    sender_name=msg.sender_name,
                    timestamp=msg.timestamp
                ) for msg in history
            ]

            # Create chat request from the received message 
            chat_request = ChatRequest(
                message=received_message.content,
                user_id=user_id or received_message.sender_id,
                session_type=ConversationType.SINGLE if slack_channel_info.get("is_im") else ConversationType.GROUP,
                company_name=company_instance.name,
                history=history,
                output_format=ChatOutputFormat.MRKDWN  # Slack's custom mrkdwn format
            )
            
            # Process the message through the chat orchestrator
            logger.debug(f"[Slack Webhook] 🔍 Processing with Chat Orchestrator...")
            result = await chat_orchestrator.process_chat_request(chat_request)
            
            # Let the Chat Orchestrator handle all decision-making and response logic
            # The orchestrator will return whether a response is needed and what it should be
            response_text = result.get("response", "")
            agent_decisions = result.get("agent_decisions", [])
            context_used = result.get("context_used", {})
            no_action_needed = result.get("no_action_needed", False)

            logger.info(f"[Slack Webhook] 🤖 Chat Orchestrator Decision:")
            logger.info(f"   Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
            logger.info(f"   No Action Needed: {no_action_needed}")
            logger.info(f"   Agent Decisions: {len(agent_decisions)}")
            logger.info(f"   Tools Used: {list(context_used.keys()) if context_used else 'None'}")
            
            # Send response if needed (Chat Orchestrator has already decided)
            if not no_action_needed and response_text and response_text.strip():
                # Format response with references if available
                references = result.get("references", [])

                response_content = response_text.strip()

                if references:
                    response_content += f"\n\n📚 **Sources:** {', '.join(references)}"
                
                response_message = Message(
                    content=response_content,
                    message_type=MessageType.TEXT
                )
                
                result_send = await slack_service.send_message(channel_id, response_message)
                if result_send.get("status") == "success":
                    logger.info(f"[Slack Webhook] ✅ Sent response via Chat Orchestrator")
                    logger.info(f"   Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                    logger.info(f"   Agent Decisions: {len(agent_decisions)}")
                    logger.info(f"   Tools Used: {list(context_used.keys()) if context_used else 'None'}")
                else:
                    logger.error(f"[Slack Webhook] ❌ Failed to send response: {result_send.get('error')}")
            else:
                logger.info(f"[Slack Webhook] ℹ️ No response sent - Chat Orchestrator determined no action needed")
                
        except ImportError as e:
            logger.error(f"[Slack Webhook] ❌ Could not import Chat Orchestrator: {e}")
            
        except Exception as e:
            logger.exception(f"[Slack Webhook] ❌ Error in Chat Orchestrator processing: {e}")
        finally:
            # Ensure SlackService session is properly closed
            if hasattr(slack_service, 'session') and slack_service.session:
                await slack_service.session.close()

    async def _handle_reaction_event(self, event, payload):
        """Handle Slack reaction events"""
        channel_id = event.get("item", {}).get("channel")
        user_id = event.get("user", "Unknown")
        reaction = event.get("reaction", "")
        timestamp = event.get("event_ts", "")
        
        # Find the MonitoredItem for this channel
        monitored = await sync_to_async(MonitoredItem.objects.filter)(type="slack_channel", name=channel_id)
        monitored = await sync_to_async(lambda: monitored.first())()
        
        if not monitored:
            return Response({"error": f"No MonitoredItem found for channel_id {channel_id}"}, status=404)
        
        # Find the active Slack integration for this company
        integration = await sync_to_async(CompanyIntegration.objects.filter)(
            company=monitored.company, 
            tool__slug="slack", 
            is_active=True
        )
        integration = await sync_to_async(lambda: integration.first())()
        
        if not integration:
            return Response({"error": "No active Slack integration found for this company"}, status=404)
        
        # Get channel name
        channel_name = await self._get_channel_name(integration, channel_id)
        
        logger.info(f"\n😀 [SLACK WEBHOOK] New reaction in #{channel_name}")
        logger.info(f"   Company: {monitored.company.name}")
        logger.info(f"   Channel: #{channel_name} ({channel_id})")
        logger.info(f"   User: {user_id}")
        logger.info(f"   Reaction: :{reaction}:")
        logger.info(f"   {'─' * 50}")
        
        # Notify frontend
        notify_frontend("slack_reaction", {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "company_id": str(monitored.company.id),
            "company_name": monitored.company.name,
            "user_id": user_id,
            "reaction": reaction,
            "timestamp": timestamp,
            "event_type": "reaction",
            "raw": event,
        })
        
        return Response({"status": "reaction_processed"})

    async def _handle_file_event(self, event, payload):
        """Handle Slack file events"""
        channel_id = event.get("channel_id")
        user_id = event.get("user_id", "Unknown")
        file_info = event.get("file", {})
        file_name = file_info.get("name", "Unknown file")
        file_type = file_info.get("filetype", "unknown")
        timestamp = event.get("event_ts", "")
        
        # Find the MonitoredItem for this channel
        monitored = await sync_to_async(MonitoredItem.objects.filter)(type="slack_channel", name=channel_id)
        monitored = await sync_to_async(lambda: monitored.first())()
        
        if not monitored:
            return Response({"error": f"No MonitoredItem found for channel_id {channel_id}"}, status=404)
        
        # Find the active Slack integration for this company
        integration = await sync_to_async(CompanyIntegration.objects.filter)(
            company=monitored.company, 
            tool__slug="slack", 
            is_active=True
        )
        integration = await sync_to_async(lambda: integration.first())()
        
        if not integration:
            return Response({"error": "No active Slack integration found for this company"}, status=404)
        
        # Get channel name
        channel_name = await self._get_channel_name(integration, channel_id)
        
        logger.info(f"\n📎 [SLACK WEBHOOK] New file shared in #{channel_name}")
        logger.info(f"   Company: {monitored.company.name}")
        logger.info(f"   Channel: #{channel_name} ({channel_id})")
        logger.info(f"   User: {user_id}")
        logger.info(f"   File: {file_name} ({file_type})")
        logger.info(f"   {'─' * 50}")
        
        # Notify frontend
        notify_frontend("slack_file", {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "company_id": str(monitored.company.id),
            "company_name": monitored.company.name,
            "user_id": user_id,
            "file_name": file_name,
            "file_type": file_type,
            "file_info": file_info,
            "timestamp": timestamp,
            "event_type": "file",
            "raw": event,
        })
        
        return Response({"status": "file_processed"})

    async def _get_channel_name(self, integration, channel_id):
        """Get channel name from Slack API"""
        try:
            bot_token = integration.config.get("slack_bot_token") or integration.config.get("bot_token")
            if not bot_token:
                return channel_id
            
            from slack_sdk import WebClient
            client = WebClient(token=bot_token)
            response = client.conversations_info(channel=channel_id)
            return response["channel"]["name"]
        except Exception as e:
            logger.error(f"[Slack Webhook] Error getting channel name: {e}")
            return channel_id

    def _verify_slack_signature(self, request):
        """Verify Slack webhook signature"""
        secret = os.getenv("SLACK_SIGNING_SECRET")
        if not secret:
            logger.warning("[Slack Webhook] No SLACK_SIGNING_SECRET configured, skipping signature verification")
            return True
        
        signature = request.headers.get("X-Slack-Signature")
        timestamp = request.headers.get("X-Slack-Request-Timestamp")
        
        if not signature or not timestamp:
            logger.warning("[Slack Webhook] Missing signature or timestamp headers")
            return False
        
        # Create the signature base string
        sig_basestring = f"v0:{timestamp}:{request.body.decode('utf-8')}"
        
        # Create the expected signature
        expected_signature = f"v0={hmac.new(secret.encode(), sig_basestring.encode(), hashlib.sha256).hexdigest()}"
        
        return hmac.compare_digest(expected_signature, signature)
