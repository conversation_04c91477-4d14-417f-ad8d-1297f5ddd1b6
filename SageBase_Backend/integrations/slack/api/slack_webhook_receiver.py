from typing import List
from rest_framework.views import APIView
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import HttpRequest
import hmac
import hashlib
import json
import os
import logging
from datetime import datetime
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync, sync_to_async
from integrations.models import MonitoredItem, CompanyIntegration, SlackUserProfile, User
from integrations.slack.services import get_user_info
from knowledge_spaces_Q_A.chat_models import ChatUserContext, ConversationType
from knowledge_spaces_Q_A.chat_response_formatting_agent import ChatOutputFormat
from messaging.slack.slack_service import SlackService
from messaging.base_interface import Message, MessageType
from messaging.base_interface import ReceivedMessage
from vectordb.models.document import DataSource

logger = logging.getLogger(__name__)

# Simple cache for admin user ID to avoid rate limiting
_admin_user_id_cache = None
_cache_timestamp = 0
CACHE_DURATION = 300  # 5 minutes

# Redis-based cache for processed events to prevent duplicates across workers
EVENT_CACHE_DURATION = 3600  # 1 hour

# Message aggregation settings
MESSAGE_AGGREGATION_DELAY = 2.0  # seconds to wait before processing message
AGGREGATION_CACHE_DURATION = 10  # seconds to keep message aggregation data

def get_redis_client():
    """Get Redis client for event deduplication"""
    try:
        import redis
        from django.conf import settings
        
        # Try to get Redis URL from environment or Django settings
        redis_url = getattr(settings, 'REDIS_URL', None) or \
                   os.getenv('REDIS_URL', None)
        
        if redis_url and isinstance(redis_url, str):
            return redis.from_url(redis_url)
        
        # Fallback to default Redis connection
        redis_host = getattr(settings, 'REDIS_HOST', 'localhost')
        redis_port = getattr(settings, 'REDIS_PORT', 6379)
        redis_db = getattr(settings, 'REDIS_DB', 0)
        
        # If we're running in a container and the host is 'redis', use internal container port
        if redis_host == 'redis':
            # Use internal container port (6379) when connecting from another container
            try:
                container_client = redis.Redis(
                    host='redis',
                    port=6379,
                    db=redis_db,
                    decode_responses=True,
                    socket_connect_timeout=2
                )
                container_client.ping()
                logger.info("✅ Connected to Redis container (redis:6379)")
                return container_client
            except Exception as e:
                logger.warning(f"Failed to connect to Redis container (redis:6379): {e}")
                # Fallback to host port (localhost:6380) if running outside container
                try:
                    host_client = redis.Redis(
                        host='localhost',
                        port=6380,
                        db=redis_db,
                        decode_responses=True,
                        socket_connect_timeout=2
                    )
                    host_client.ping()
                    logger.info("✅ Connected to Redis host (localhost:6380)")
                    return host_client
                except Exception as e2:
                    logger.warning(f"Failed to connect to Redis host (localhost:6380): {e2}")
                    pass
        
        # Default connection
        return redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True
        )
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        return None

def is_event_processed(event_id: str) -> bool:
    """Check if an event has already been processed using Redis"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            logger.warning("Redis not available, falling back to in-memory cache")
            return False
        
        # Check if event exists in Redis
        key = event_id
        exists = redis_client.exists(key) > 0
        logger.info(f"Event {event_id} processed: {exists}")
        return exists
        
    except Exception as e:
        logger.error(f"Error checking if event is processed: {e}")
        return False

def mark_event_processed(event_id: str):
    """Mark an event as processed using Redis"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            logger.warning("Redis not available, cannot mark event as processed")
            return
        
        # Store event with TTL
        key = event_id
        redis_client.setex(key, EVENT_CACHE_DURATION, "processed")
        logger.info(f"Marked event {event_id} as processed")
        
    except Exception as e:
        logger.error(f"Error marking event as processed: {e}")

def cleanup_old_events():
    """Cleanup old events from Redis (Redis handles TTL automatically)"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            return
        
        # Redis automatically handles TTL, so we don't need manual cleanup
        # But we can log some stats for monitoring
        pattern = "slack_processed_event:*"
        keys = redis_client.keys(pattern)
        if keys:
            logger.debug(f"Redis contains {len(keys)} processed event keys")
            
    except Exception as e:
        logger.error(f"Error during Redis cleanup: {e}")

def get_message_aggregation_key(user_id: str, channel_id: str) -> str:
    """Generate a unique key for message aggregation"""
    return f"slack_msg_agg:{user_id}:{channel_id}"

def store_message_for_aggregation(user_id: str, channel_id: str, message_data: dict):
    """Store a message in Redis for aggregation"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            logger.warning("Redis not available, cannot store message for aggregation")
            return False
        
        key = get_message_aggregation_key(user_id, channel_id)
        
        # Get existing messages or create new list
        existing_messages = redis_client.get(key)
        if existing_messages:
            messages = json.loads(existing_messages)
        else:
            messages = []
        
        # Add new message
        messages.append(message_data)
        
        # Store updated list with TTL
        redis_client.setex(key, AGGREGATION_CACHE_DURATION, json.dumps(messages))
        logger.info(f"Stored message for aggregation: {user_id}:{channel_id} (total: {len(messages)})")
        return True
        
    except Exception as e:
        logger.error(f"Error storing message for aggregation: {e}")
        return False

def get_aggregated_messages(user_id: str, channel_id: str) -> list:
    """Get and clear aggregated messages for a user/channel"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            logger.warning("Redis not available, returning empty aggregated messages")
            return []
        
        key = get_message_aggregation_key(user_id, channel_id)
        
        # Get messages
        existing_messages = redis_client.get(key)
        if existing_messages:
            messages = json.loads(existing_messages)
            # Clear the messages after retrieving
            redis_client.delete(key)
            logger.info(f"Retrieved {len(messages)} aggregated messages for {user_id}:{channel_id}")
            return messages
        
        return []
        
    except Exception as e:
        logger.error(f"Error getting aggregated messages: {e}")
        return []

def is_aggregation_pending(user_id: str, channel_id: str) -> bool:
    """Check if there are pending messages for aggregation"""
    try:
        redis_client = get_redis_client()
        if not redis_client:
            return False
        
        key = get_message_aggregation_key(user_id, channel_id)
        return redis_client.exists(key) > 0
        
    except Exception as e:
        logger.error(f"Error checking aggregation status: {e}")
        return False

def notify_frontend(event_type, data):
    """Notify frontend via WebSocket about Slack events"""
    data_json = json.dumps(data)
    logger.info(f"[Notify Frontend] {event_type}: {data_json}")
    
    channel_id = data.get("channel_id")
    company_id = data.get("company_id")
    
    if channel_id and company_id:
        # Create group name for the channel
        group_name = f"slack_{company_id}_{channel_id}"
        logger.info(f"[WS][GROUP_SEND] Preparing to send to group '{group_name}' for event '{event_type}' with data: {data_json}")
        
        channel_layer = get_channel_layer()
        notification_data = dict(data)
        
        async_to_sync(channel_layer.group_send)(
            group_name,
            {
                "type": "notify",
                "notification": notification_data
            }
        )
        logger.info(f"[WS][GROUP_SEND] Sent notification to group '{group_name}' for event '{event_type}'.")

async def get_bot_token_for_user(slack_user_id):
    import asyncio
    
    def _get_bot_token():
        try:
            profile = SlackUserProfile.objects.get(slack_user_id=slack_user_id, is_active=True)
            return profile.bot_token
        except SlackUserProfile.DoesNotExist:
            logger.warning(f"[Slack Webhook] No SlackUserProfile found for slack_user_id={slack_user_id}, ignoring event.")
            return None
    
    return await asyncio.to_thread(_get_bot_token)



async def get_company_name_for_slack_team(team_id):
    """Get the company name for a Slack team ID - async with asyncio.to_thread"""
    try:
        # Find CompanyIntegration with matching team_id in config
        from integrations.models import CompanyIntegration, IntegrationTool
        import asyncio
        
        def _get_company():
            slack_tool = IntegrationTool.objects.filter(slug='slack').first()
            if not slack_tool:
                return None, None
            
            integration = CompanyIntegration.objects.filter(
                tool=slack_tool,
                is_active=True,
                config__team_id=team_id
            ).first()
            
            if integration:
                company_name = integration.company.name.lower().replace(' ', '_')
                return integration, company_name
            
            return None, None
        
        integration, company_name = await asyncio.to_thread(_get_company)
        
        if integration:
            logger.info(f"[Slack Webhook] Found company '{integration.company.name}' for team '{team_id}'")
            return company_name
        elif integration is None and company_name is None:
            logger.warning(f"[Slack Webhook] No Slack integration tool found")
            return None
        else:
            logger.warning(f"[Slack Webhook] No company found for team '{team_id}'")
            return None
        
    except Exception as e:
        logger.exception(f"[Slack Webhook] Error getting company name for team_id={team_id}: {e}")
        return None



async def get_company_and_team_info_for_slack_team(team_id):
    """Get both company  instance and team name for a Slack team ID - async with asyncio.to_thread
    None if no match between team_id and company_integration
    """
    try:
        # Find CompanyIntegration with matching team_id in config
        from integrations.models import CompanyIntegration, IntegrationTool
        import asyncio
        
        def _get_integration():
            slack_tool = IntegrationTool.objects.filter(slug='slack').first()
            if not slack_tool:
                return None, None, None
            
            integration = CompanyIntegration.objects.filter(
                tool=slack_tool,
                is_active=True,
                config__team_id=team_id
            ).first()
            
            if integration:
                company_instance = integration.company
                team_name = integration.config.get('team_name', 'Unknown Team')
                return integration, company_instance, team_name
            
            return None, None, None
        
        integration, company_instance, team_name = await asyncio.to_thread(_get_integration)
        
        if integration:
            logger.info(f"[Slack Webhook] Found company '{integration.company.name}' for team '{team_name}' ({team_id})")
            return company_instance, team_name
        elif integration is None and company_instance is None and team_name is None:
            logger.warning(f"[Slack Webhook] No Slack integration tool found")
            return None, None
        else:
            logger.error(f"[Slack Webhook] No company found for team '{team_id}'")
            return None, None
        
    except Exception as e:
        logger.exception(f"[Slack Webhook] Error getting company and team info for team_id={team_id}: {e}")
        return None, None

async def create_group_channel_and_invite_users(slack_user_id, support_user_ids, original_user, original_message, original_channel):
    """
    Create a new private channel and invite the support team and the user who asked the question.
    Uses the messaging interface abstraction and retrieves the bot token from the DB.
    """
    try:
        bot_token = await get_bot_token_for_user(slack_user_id)
        if not bot_token:
            logger.warning(f"[Slack Webhook] No bot token found for slack_user_id={slack_user_id}")
            return {"ok": False, "error": "No bot token found"}
        
        slack_service = SlackService()
        await slack_service.authenticate({"bot_token": bot_token})

        # Create a new group chat (private channel)
        group_name = original_message.strip().split()[0].lower() if original_message.strip().split() else "support"
        import re, time
        group_name = re.sub(r'[^a-z0-9-]', '', group_name)
        if len(group_name) < 2:
            group_name = "support"
        timestamp = int(time.time())
        channel_name = f"{group_name}-{timestamp}"
        
        # Create group chat
        group_chat = await slack_service.create_group_chat(channel_name, support_user_ids + [original_user])
        new_channel_id = group_chat.id
        new_channel_name = group_chat.name
        logger.info(f"[Slack Webhook] ✅ Created new channel: {new_channel_name} ({new_channel_id})")

        # Post the original message in the new channel
        formatted_message = (
            f"📨 **Question from <@{original_user}> in <#{original_channel}>**\n\n"
            f"**Message:** {original_message}\n\n"
            f"*This channel was created to handle your question. Feel free to discuss here!*"
        )
        message = Message(content=formatted_message, message_type=MessageType.TEXT)
        await slack_service.send_message(new_channel_id, message)
        logger.info(f"[Slack Webhook] ✅ Posted message in new channel {new_channel_name}")

        # Send a notification to the original channel
        notification_message = f"<@{original_user}> Your question has been moved to <#{new_channel_id}> for better assistance."
        notification = Message(content=notification_message, message_type=MessageType.TEXT)
        await slack_service.send_message(original_channel, notification)
        logger.info(f"[Slack Webhook] ✅ Sent notification to original channel")

        return {"ok": True, "channel_id": new_channel_id, "channel_name": new_channel_name}
    except Exception as e:
        logger.exception(f"[Slack Webhook] ❌ Error creating channel and inviting users: {e}")
        return {"ok": False, "error": str(e)}
    finally:
        # Ensure SlackService session is properly closed
        try:
            await slack_service.disconnect()
            logger.debug("[Slack Webhook] ✅ SlackService session closed in create_group_channel")
        except Exception as cleanup_error:
            logger.warning(f"[Slack Webhook] ⚠️ Failed to close SlackService session: {cleanup_error}")

def get_support_user_ids(bot_token):
    """
    Get the hardcoded support user IDs (users who should be invited to support channels).
    
    Args:
        bot_token: Slack bot token (not used in this simplified version)
    
    Returns:
        list: List of hardcoded support user IDs
    """
    # Hardcoded support user IDs - replace with your desired user IDs
    # You can find user IDs by looking at the logs when the bot runs
    # or by using the Slack API to list users
    HARDCODED_SUPPORT_USER_IDS = [
        "U093656FK5G",
        "U0908KH070B"
    ]
    
    logger.info(f"[Slack Webhook] ✅ Using hardcoded support user IDs: {HARDCODED_SUPPORT_USER_IDS}")
    return HARDCODED_SUPPORT_USER_IDS

def list_all_users_for_debugging(bot_token):
    """
    Debug function to list all users and their IDs.
    Use this to find user IDs for hardcoding.
    
    Args:
        bot_token: Slack bot token
    
    Returns:
        None (prints to console)
    """
    try:
        from slack_sdk import WebClient
        client = WebClient(token=bot_token)
        response = client.users_list()
        
        if response.get("ok"):
            users = response["users"]
            print("\n" + "="*60)
            print("SLACK USERS LIST (for finding user IDs)")
            print("="*60)
            
            for user in users:
                if not user.get("is_bot") and not user.get("deleted"):
                    user_id = user.get("id", "N/A")
                    username = user.get("name", "N/A")
                    real_name = user.get("real_name", "N/A")
                    is_admin = user.get("is_admin", False)
                    is_owner = user.get("is_owner", False)
                    
                    print(f"ID: {user_id}")
                    print(f"  Username: {username}")
                    print(f"  Real Name: {real_name}")
                    print(f"  Admin: {is_admin}, Owner: {is_owner}")
                    print("-" * 40)
        else:
            logger.error(f"[Slack Webhook] ❌ Failed to get users list: {response}")
            
    except Exception as e:
        logger.exception(f"[Slack Webhook] ❌ Error listing users: {e}")

@method_decorator(csrf_exempt, name='dispatch')
class SlackWebhookReceiverView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request: HttpRequest, *args, **kwargs):
        from utils.common_functions import spawn_function
        logger.info("[Slack Webhook] Received POST request")

        # Verify Slack signature
        if not self._verify_slack_signature(request):
            logger.warning("[Slack Webhook] Invalid signature!")
            return Response({"error": "Invalid signature"}, status=400)

        # Parse payload
        try:
            payload = request.data if isinstance(request.data, dict) else json.loads(request.body)
        except Exception:
            logger.exception("[Slack Webhook] Failed to parse body")
            return Response(status=200)  # ACK anyway to avoid Slack retries

        # URL verification challenge
        if payload.get("type") == "url_verification":
            return Response({"challenge": payload.get("challenge")})

        # Route events → schedule async work, but ACK immediately
        if payload.get("type") == "event_callback":
            event = payload.get("event", {})
            etype = event.get("type")

            if etype == "message" and "subtype" not in event:
                spawn_function(self._handle_message_event, event, payload)
            elif etype == "reaction_added":
                spawn_function(self._handle_reaction_event, event, payload)
            elif etype == "file_shared":
                spawn_function(self._handle_file_event, event, payload)

        # Always ACK within ~3 seconds
        return Response({"status": "ack"})

    def _handle_message_event_sync(self, event, payload):
        """Handle Slack message events using asgiref.async_to_sync for proper loop management"""
        from asgiref.sync import async_to_sync
        try:
            return async_to_sync(self._handle_message_event)(event, payload)
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error in sync wrapper: {e}")
            return Response({"status": "error_handled"}, status=200)

    def _handle_reaction_event_sync(self, event, payload):
        """Handle Slack reaction events using asgiref.async_to_sync for proper loop management"""
        from asgiref.sync import async_to_sync
        try:
            return async_to_sync(self._handle_reaction_event)(event, payload)
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error in reaction sync wrapper: {e}")
            return Response({"status": "error_handled"}, status=200)

    def _handle_file_event_sync(self, event, payload):
        """Handle Slack file events using asgiref.async_to_sync for proper loop management"""
        from asgiref.sync import async_to_sync
        try:
            return async_to_sync(self._handle_file_event)(event, payload)
        except Exception as e:
            logger.exception(f"[Slack Webhook] Error in file sync wrapper: {e}")
            return Response({"status": "error_handled"}, status=200)

    async def _get_slack_user_info(self, slack_user_id, team_id):
        """Get user information from Slack API"""
        try:
            # Get company and bot token for the team/workspace
            company_instance, team_name = await get_company_and_team_info_for_slack_team(team_id)
            if not company_instance:
                logger.warning(f"⚠️ No company found for team_id {team_id}")
                return None
                
            from integrations.slack.services import get_company_slack_bot_token
            from asgiref.sync import sync_to_async
            bot_token = await sync_to_async(get_company_slack_bot_token)(company_instance)
            if not bot_token:
                logger.warning(f"⚠️ No bot token found for company {company_instance.name}")
                return None
            
            # Initialize and authenticate SlackService
            slack_service = SlackService()
            authenticated = await slack_service.authenticate({
                'bot_token': bot_token
            })
            
            if not authenticated:
                logger.warning("⚠️ Failed to authenticate with Slack API")
                return None
            
            # Try to get user info using Slack Web API
            # Note: This requires the bot token to have users:read scope
            user_info = await sync_to_async(get_user_info)(slack_user_id, bot_token=bot_token)
            
            if user_info and user_info.get('user'):
                user_profile = user_info['user'].get('profile', {})
                return {
                    'id': user_info['user'].get('id'),
                    'name': user_info['user'].get('name'),
                    'real_name': user_info['user'].get('real_name'),
                    'email': user_profile.get('email'),
                    'display_name': user_profile.get('display_name'),
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting Slack user info for {slack_user_id}: {e}")
            return None


    

    def _is_app_mentioned(self, event, payload, text):
        """
        Check if the Slack app was mentioned/tagged in the message.
        Returns True if the app should respond, False otherwise.
        
        Detection methods:
        1. app_mention event type (most reliable)
        2. Text contains @sagebase_test_app or similar
        3. bot_id in event (indicates bot was mentioned)
        4. Direct message (always respond to DMs)
        5. Slack mention format <@U1234567890>
        """
        # Method 1: Check for app_mention event type (most reliable)
        if payload.get("event", {}).get("type") == "app_mention":
            logger.info(f"[Slack Webhook] App mentioned via app_mention event")
            return True
        
        # Method 2: Check if message contains @sagebase_test_app or similar
        if text and any(mention in text.lower() for mention in ["@sagebase", "@sagebase_test_app", "<@"]):
            logger.info(f"[Slack Webhook] App mentioned via text: {text}")
            return True
        
        # Method 3: Check for bot_id in the event (indicates bot was mentioned)
        if event.get("bot_id") or "bot_id" in event:
            logger.info(f"[Slack Webhook] App mentioned via bot_id")
            return True
        
        # Method 4: Check if this is a direct message (always respond to DMs)
        if event.get("channel_type") == "im":
            logger.info(f"[Slack Webhook] Direct message received - always respond")
            return True
        
        # Method 5: Check for mentions in the text using Slack's mention format
        if text and "<@" in text and ">" in text:
            # Extract user IDs mentioned in the format <@U1234567890>
            import re
            mention_pattern = r'<@([A-Z0-9]+)>'
            mentions = re.findall(mention_pattern, text)
            
            # Check if any of the mentions match your bot's user ID
            # Set SLACK_BOT_USER_ID in your environment (e.g., "U1234567890")
            bot_user_id = os.getenv("SLACK_BOT_USER_ID")
            if bot_user_id and bot_user_id in mentions:
                logger.info(f"[Slack Webhook] Bot user ID mentioned: {bot_user_id}")
                return True
        
        return False

    async def _handle_message_event(self, event, payload):
        """Background worker: process Slack message and post a reply when ready.
        No hard time constraints, since we already acknowledged the event.
        """
        slack_service = None
        try:
            import asyncio
            channel_id = event.get("channel")
            slack_user_id = event.get("user", "Unknown")
            bot_id = event.get("bot_id")
            text = event.get("text", "")
            timestamp = event.get("ts", "")
            team_id = event.get("team")
            event_id = payload.get("event_id", "no_event_id")
            
            # Check if this is a threaded message
            thread_ts = event.get("thread_ts")
            is_thread_reply = thread_ts and thread_ts != timestamp
            parent_thread_ts = thread_ts if not is_thread_reply else None

            # Dedup
            if is_event_processed(event_id):
                logger.debug(f"[Slack Webhook] ⚠️ Event {event_id} already processed, skipping")
                return
            mark_event_processed(event_id)

            # Ignore bot messages
            if bot_id is not None:
                logger.info(f"[Slack Webhook] Ignoring message from bot (bot_id: {bot_id})")
                return

            # Check if the app was mentioned/tagged
            # This prevents the bot from responding to every message in channels
            app_mentioned = self._is_app_mentioned(event, payload, text)
            

            if not team_id:
                logger.error("Message received from an unknown team → ignoring")
                return

            company_instance, team_name = await get_company_and_team_info_for_slack_team(team_id)
            if not company_instance:
                logger.error(f"No Slack integration for team {team_id} → ignoring")
                return

            # Message Aggregation Logic
            message_data = {
                "text": text,
                "timestamp": timestamp,
                "event_id": event_id,
                "app_mentioned": app_mentioned,
                "thread_ts": thread_ts,
                "is_thread_reply": is_thread_reply,
                "parent_thread_ts": parent_thread_ts
            }
            
            # Store this message for potential aggregation
            store_message_for_aggregation(slack_user_id, channel_id, message_data)
            
            # Wait for the aggregation delay to collect potentially related messages
            logger.info(f"[Slack Webhook] Waiting {MESSAGE_AGGREGATION_DELAY}s for message aggregation...")
            await asyncio.sleep(MESSAGE_AGGREGATION_DELAY)
            
            # Get all aggregated messages for this user/channel
            aggregated_messages = get_aggregated_messages(slack_user_id, channel_id)
            
            if not aggregated_messages:
                logger.info(f"[Slack Webhook] No messages found in aggregation for {slack_user_id}:{channel_id}")
                return
            
            # Combine all messages into a single text
            combined_text_parts = []
            combined_app_mentioned = False
            latest_timestamp = timestamp
            thread_context = None
            
            for msg in aggregated_messages:
                if msg.get("text", "").strip():
                    combined_text_parts.append(msg["text"].strip())
                if msg.get("app_mentioned", False):
                    combined_app_mentioned = True
                # Use the latest timestamp
                if msg.get("timestamp", "") > latest_timestamp:
                    latest_timestamp = msg["timestamp"]
                
                # Check for thread context
                if msg.get("thread_ts") and not thread_context:
                    thread_context = {
                        "thread_ts": msg.get("thread_ts"),
                        "is_thread_reply": msg.get("is_thread_reply", False),
                        "parent_thread_ts": msg.get("parent_thread_ts")
                    }
            
            # If no meaningful text content, skip processing
            if not combined_text_parts:
                logger.info(f"[Slack Webhook] No meaningful text content in aggregated messages")
                return
            
            # Join all text parts with newlines to preserve multi-line structure
            combined_text = "\n".join(combined_text_parts)
            
            logger.info(f"[Slack Webhook] Aggregated {len(aggregated_messages)} messages into single request: '{combined_text[:120]}...'")
            if thread_context:
                logger.info(f"[Slack Webhook] Thread context detected: {thread_context}")

            # Auth - get bot token for this company
            from integrations.slack.services import get_company_slack_bot_token
            from asgiref.sync import sync_to_async
            bot_token = await sync_to_async(get_company_slack_bot_token)(company_instance)
            if not bot_token:
                logger.warning(f"[Slack Webhook] No bot token for company {company_instance.name}; skipping event {event_id}")
                return

            slack_service = SlackService()
            try:
                await slack_service.authenticate({"bot_token": bot_token})
            except Exception as e:
                logger.error(f"[Slack Webhook] bot Authentication error: {e}")
                return

            # Fetch recent messages (context) – keep this bounded
            try:
                received_messages = await asyncio.wait_for(
                    slack_service.receive_messages(channel_id=channel_id, limit=15),  # Get more to account for filtering
                    timeout=4.0
                )
            except asyncio.TimeoutError:
                logger.warning(f"[Slack Webhook] receive_messages timed out for channel {channel_id}")
                received_messages = []

            # Filter out the messages we just aggregated from the history to avoid duplication
            aggregated_timestamps = {msg.get("timestamp") for msg in aggregated_messages}
            filtered_messages = []
            
            for msg in received_messages:
                # Skip messages that were part of our aggregation
                if msg.timestamp not in aggregated_timestamps:
                    filtered_messages.append(msg)
                # Limit to 10 messages for history context
                if len(filtered_messages) >= 10:
                    break
            
            logger.info(f"[Slack Webhook] Filtered history: {len(received_messages)} -> {len(filtered_messages)} messages (removed {len(received_messages) - len(filtered_messages)} aggregated messages)")

            if not combined_text and not filtered_messages:
                # Nothing to do
                return

            # Create a single aggregated message for processing
            latest_message = ReceivedMessage(
                id=None, 
                channel_id=channel_id, 
                content=combined_text, 
                sender_id=slack_user_id, 
                sender_name="unknown", 
                timestamp=latest_timestamp,
                metadata={
                    "thread_context": thread_context,
                    "aggregated_count": len(aggregated_messages)
                } if thread_context else None
            )

            logger.info(
                f"Processing aggregated message for company {company_instance.id} in channel {channel_id}: "
                f"{latest_message.content[:120]}"
            )
            if thread_context:
                logger.info(f"Thread context: {thread_context}")

            # Build context & chat request (same as your code)
            slack_channel_info = await slack_service.get_channel_info(channel_id=channel_id)
            
            # Additional check: If this is a direct message, always respond
            if slack_channel_info and slack_channel_info.get("is_im") and not combined_app_mentioned:
                combined_app_mentioned = True
                logger.info(f"[Slack Webhook] Direct message confirmed via channel info - always respond")

            collections_to_search = [str(company_instance.id)]
            user = None
            user_id = None  # you can keep your existing user lookup logic if needed

            if user_id:
                collections_to_search.append(str(user_id))
                user = User.objects.get(id=user_id)

            sources_to_search = []
            from integrations.models import CompanyIntegration
            
            # Use asyncio.to_thread for async-safe database queries
            all_integrations = await asyncio.to_thread(
                lambda: list(CompanyIntegration.objects.filter(company=company_instance, is_active=True).select_related('tool'))
            )
            
            for integration in all_integrations:
                if integration.tool.slug in ["google-drive", "github", "confluence", "local"]:
                    #TODO : cleanup later this dirty fix
                    if integration.tool.slug == "google-drive":
                        sources_to_search.append(DataSource.GOOGLE_DRIVE)
                        continue
                    sources_to_search.append(integration.tool.slug)

            # Handle case when no sources are available
            if not sources_to_search:
                logger.info(f"[Slack Webhook] No active integrations found for company {company_instance.name}")
                # You can either set a default source or handle this case specifically
                sources_to_search = ["local"]  # Default to local knowledge only

            context = ChatUserContext(
                user=user,
                company=company_instance,
                collections_to_search=collections_to_search,
                sources_to_search=sources_to_search,  # Use dynamic sources instead of hardcoded
                platform_source='slack',
                session_type=(
                    ConversationType.SINGLE if slack_channel_info.get("is_im")
                    else ConversationType.GROUP
                ),
            )

            # Prepare history from filtered messages (exclude our aggregated message)
            # The latest_message (our aggregated message) should not be in history since it's the current message being processed
            from knowledge_spaces_Q_A.chat_models import ConversationMessage, ChatRequest
            history = [
                ConversationMessage(
                    content=m.content, sender_id=m.sender_id,
                    sender_name=m.sender_name, timestamp=m.timestamp
                ) for m in filtered_messages
            ]

            # Check if we have any sources to search
            if not sources_to_search:
                # No integrations available - send informative message
                no_sources_message = (
                    "⚠️ *No Knowledge Sources Available*\n\n"
                    "I don't have access to any knowledge sources (Google Drive, GitHub, Confluence, etc.) "
                    "for your company at the moment.\n\n"
                    "Please contact your administrator to set up integrations."
                )
                
                await slack_service.send_message(
                    channel_id,
                    Message(content=no_sources_message, message_type=MessageType.TEXT),
                )
                logger.info("[Slack Webhook] ℹ️ No sources available, sent info message.")
                return

            chat_request = ChatRequest(
                message=combined_text,
                user=user,
                external_id=latest_message.sender_id,
                session_type=ConversationType.SINGLE if slack_channel_info.get("is_im") else ConversationType.GROUP,
                company_name=company_instance.name,
                history=history,
                output_format=ChatOutputFormat.MRKDWN,
                needs_reply=False
            )

            # Run orchestrator with a wall-clock timeout
            from knowledge_spaces_Q_A.chat_orchestrator import ChatOrchestrator
            chat_orchestrator = ChatOrchestrator(context=context)

            try:
                orchestration_result = await asyncio.wait_for(
                    chat_orchestrator.process_chat_request(chat_request),
                    timeout=180.0
                )
            except asyncio.TimeoutError:
                logger.warning("[Slack Webhook] Orchestrator timed out; posting a holding message.")
                #TODO: for private messages, we shall send a message to the user that we are working on it
                #await slack_service.send_message(
                #    channel_id,
                #    Message(content="⏳ I'm still working on it—I'll post the answer shortly.", message_type=MessageType.TEXT),
                #)
                return

            # Shape result
            if hasattr(orchestration_result, 'to_dict'):
                result_dict = orchestration_result.to_dict()
            elif isinstance(orchestration_result, dict):
                result_dict = orchestration_result
            else:
                result_dict = {
                    "response": str(orchestration_result),
                    "no_action_needed": False,
                    "agent_decisions": [],
                    "context_used": {},
                    "references": []
                }

            response_text = (result_dict.get("response") or "").strip()
            no_action_needed = bool(result_dict.get("no_action_needed", False))
            references = result_dict.get("references") or []

            if not no_action_needed and response_text:
                # Append sources if present
                if references:
                    response_text += f"\n\n📚 **Sources:** {', '.join(references)}"

                # If this is a threaded conversation, reply in thread
                if thread_context and thread_context.get("thread_ts"):
                    # Reply in the thread
                    await slack_service.send_message(
                        channel_id,
                        Message(content=response_text, message_type=MessageType.TEXT),
                        thread_ts=thread_context["thread_ts"]
                    )
                    logger.info(f"[Slack Webhook] Replied in thread {thread_context['thread_ts']}")
                else:
                    # Send as regular message
                    await slack_service.send_message(
                        channel_id,
                        Message(content=response_text, message_type=MessageType.TEXT),
                    )
                    logger.info("[Slack Webhook] Sent regular message response")

        except Exception as e:
            logger.exception(f"[Slack Webhook] ❌ Unexpected error in message worker: {e}")
        finally:
            if slack_service and getattr(slack_service, "session", None):
                try:
                    await slack_service.session.close()
                except Exception as e:
                    logger.warning(f"[Slack Webhook] Warning: Could not close SlackService session: {e}")


    async def get_channel_history(user_id: str, channel_id: str, limit: int = 20) -> str:
        """
        Get the history of a channel from Slack API.
        Args:
            channel_id: The ID of the channel to get the history of.
            limit: The maximum number of messages to return.
        Returns:
            A JSON string containing the history of the channel.
        """
        try:
            from messaging.slack.slack_service import SlackService
            from integrations.models import SlackUserProfile, CompanyIntegration
            
            # Get bot token from database
            def get_slack_profile():
                return SlackUserProfile.objects.filter(user_id=user_id, is_active=True).first() 
            
            def get_company_integration():
                return CompanyIntegration.objects.filter(
                    tool__slug='slack',
                    is_active=True
                ).first()
            
            # Use thread_sensitive=False to avoid CurrentThreadExecutor issues
            slack_profile = await sync_to_async(get_slack_profile, thread_sensitive=False)()
            company_integration = await sync_to_async(get_company_integration, thread_sensitive=False)()
            
            if slack_profile or company_integration:
                # Initialize SlackService
                slack_service = SlackService()
                
                # Get bot token
                bot_token = None
                if slack_profile and slack_profile.bot_token:
                    bot_token = slack_profile.bot_token
                elif company_integration and company_integration.config:
                    bot_token = company_integration.config.get('bot_token')
                
                if bot_token:
                    # Authenticate and get real messages
                    await slack_service.authenticate({"bot_token": bot_token})
                    messages = await slack_service.receive_messages(channel_id, limit=limit)
                    
                    if messages:
                        # Convert to expected format
                        channel_messages = []
                        for msg in messages:
                            channel_messages.append({
                                "content": msg.content,
                                "user_id": msg.sender_id,
                                "user_name": msg.sender_name,
                                "channel_id": channel_id,
                                "platform": "slack",
                                "timestamp": msg.timestamp,
                            })
                        
                        return json.dumps({
                            "platform": "slack",
                            "channel_id": channel_id,
                            "messages": channel_messages,
                            "total_messages": len(channel_messages),
                            "source": "real_api"
                        })
                    
        except Exception as api_error:
            logger.warning(f"Failed to get real Slack messages: {api_error}")
    


        except Exception as e:
            logger.error(f"Error in external channel history: {e}")
            return json.dumps({"error": str(e), "messages": []})


    async def _process_message_with_base_interface(self, slack_service, received_message, received_messages: List[ReceivedMessage], channel_id, company_instance, user_id=None):
        """Process received message using base interface methods and question answering system"""
        try:
            # Import the chat orchestrator
            from knowledge_spaces_Q_A.chat_orchestrator import ChatOrchestrator
            from knowledge_spaces_Q_A.chat_models import ConversationType, ChatRequest, ConversationMessage
            from datetime import datetime
            from asgiref.sync import sync_to_async
            
            logger.debug(f"[Slack Webhook] 🤖 Processing message with Chat Orchestrator")
            logger.debug(f"   Message: '{received_message.content}'")
            logger.debug(f"   Sender: {received_message.sender_name} ({received_message.sender_id})")

            slack_channel_info = await slack_service.get_channel_info(channel_id=channel_id)
            
            logger.info(f"processing message with base interface for company_instance: {company_instance.id} and user_id: {user_id}, message: {received_message.content}")

            # Build collections to search - always include company collection
            collections_to_search = [str(company_instance.id)] if company_instance else []
            #also include private collection if user_id is not None
            user = None
            if user_id:
                collections_to_search.append(str(user_id))
                user = User.objects.get(id=user_id)
            
            context = ChatUserContext(
                user=user,
                company=company_instance,
                collections_to_search=collections_to_search,
                sources_to_search=["google_drive", "github", "confluence", "internet", "local"],
                session_type=ConversationType.SINGLE if slack_channel_info.get("is_im") else ConversationType.GROUP,
            )
                
            history = []
            # Remove the first message from history
            if received_messages:
                history = received_messages[1:]

            # map the history to ConversationMessage objects
            history = [
                ConversationMessage(
                    content=msg.content,
                    sender_id=msg.sender_id,
                    sender_name=msg.sender_name,
                    timestamp=msg.timestamp
                ) for msg in history
            ]

            # Create chat request from the received message 
            chat_request = ChatRequest(
                message=received_message.content,
                user=user,
                external_id=received_message.sender_id,
                session_type=ConversationType.SINGLE if slack_channel_info.get("is_im") else ConversationType.GROUP,
                company_name=company_instance.name,
                history=history,
                output_format=ChatOutputFormat.MRKDWN  # Slack's custom mrkdwn format
            )
            
            # Process the message through the chat orchestrator
            logger.debug(f"[Slack Webhook] 🔍 Processing with Chat Orchestrator...")
            
            # Create ChatOrchestrator and process request directly (it's already async)
            chat_orchestrator = ChatOrchestrator(context=context)
            
            # Process the request directly since ChatOrchestrator.process_chat_request is async
            result = await chat_orchestrator.process_chat_request(chat_request)
            
            logger.warning(f"passed chat request to chat orchestrator")
            
            # Convert ChatResponse to dict format for compatibility
            if hasattr(result, 'to_dict'):
                result_dict = result.to_dict()
            else:
                # Fallback if it's already a dict
                result_dict = result if isinstance(result, dict) else {
                    "response": str(result),
                    "no_action_needed": False,
                    "agent_decisions": [],
                    "context_used": {},
                    "references": []
                }
            
            # Let the Chat Orchestrator handle all decision-making and response logic
            # The orchestrator will return whether a response is needed and what it should be
            response_text = result_dict.get("response", "")
            if response_text:
                agent_decisions = result_dict.get("agent_decisions", [])
                context_used = result_dict.get("context_used", {})
                no_action_needed = result_dict.get("no_action_needed", False)

                logger.debug(f"[Slack Webhook] 🤖 Chat Orchestrator Decision:")
                logger.debug(f"   Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                logger.debug(f"   No Action Needed: {no_action_needed}")
                logger.debug(f"   Agent Decisions: {len(agent_decisions)}")
                logger.debug(f"   Tools Used: {list(context_used.keys()) if context_used else 'None'}")
                
                # Send response if needed (Chat Orchestrator has already decided)
                if not no_action_needed and response_text and response_text.strip():
                    # Format response with references if available
                    references = result_dict.get("references", [])

                    response_content = response_text.strip()

                    if references:
                        response_content += f"\n\n📚 **Sources:** {', '.join(references)}"
                    
                    response_message = Message(
                        content=response_content,
                        message_type=MessageType.TEXT
                    )
                    
                    logger.info(f"slack response: {response_content} to message: {received_message.content} for company {company_instance.name}")
                    
                    result_send = await slack_service.send_message(channel_id, response_message)
                    if result_send.get("status") == "success":
                        logger.debug(f"[Slack Webhook] ✅ Sent response via Chat Orchestrator")
                        logger.debug(f"   Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                        logger.debug(f"   Agent Decisions: {len(agent_decisions)}")
                        logger.debug(f"   Tools Used: {list(context_used.keys()) if context_used else 'None'}")
                    else:
                        logger.error(f"[Slack Webhook] ❌ Failed to send response: {result_send.get('error')}")
                else:
                    logger.info(f"[Slack Webhook] ℹ️ No response sent - Chat Orchestrator determined no action needed to message: {received_message.content} for company {company_instance.name}")
                
        except ImportError as e:
            logger.error(f"[Slack Webhook] ❌ Could not import Chat Orchestrator: {e}")
            
        except Exception as e:
            logger.exception(f"[Slack Webhook] ❌ Error in Chat Orchestrator processing: {e}")
        finally:
            # Ensure SlackService session is properly closed
            if hasattr(slack_service, 'session') and slack_service.session:
                await slack_service.session.close()

    async def _handle_reaction_event(self, event, payload):
        """Handle Slack reaction events"""
        channel_id = event.get("item", {}).get("channel")
        user_id = event.get("user", "Unknown")
        reaction = event.get("reaction", "")
        timestamp = event.get("event_ts", "")
        
        # Find the MonitoredItem for this channel
        monitored = await sync_to_async(MonitoredItem.objects.filter)(type="slack_channel", name=channel_id)
        monitored = await sync_to_async(lambda: monitored.first())()
        
        if not monitored:
            return Response({"error": f"No MonitoredItem found for channel_id {channel_id}"}, status=404)
        
        # Find the active Slack integration for this company
        integration = await sync_to_async(CompanyIntegration.objects.filter)(
            company=monitored.company, 
            tool__slug="slack", 
            is_active=True
        )
        integration = await sync_to_async(lambda: integration.first())()
        
        if not integration:
            return Response({"error": "No active Slack integration found for this company"}, status=404)
        
        # Get channel name
        channel_name = await self._get_channel_name(integration, channel_id)
        
        logger.info(f"\n😀 [SLACK WEBHOOK] New reaction in #{channel_name}")
        logger.info(f"   Company: {monitored.company.name}")
        logger.info(f"   Channel: #{channel_name} ({channel_id})")
        logger.info(f"   User: {user_id}")
        logger.info(f"   Reaction: :{reaction}:")
        logger.info(f"   {'─' * 50}")
        
        # Notify frontend
        notify_frontend("slack_reaction", {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "company_id": str(monitored.company.id),
            "company_name": monitored.company.name,
            "user_id": user_id,
            "reaction": reaction,
            "timestamp": timestamp,
            "event_type": "reaction",
            "raw": event,
        })
        
        return Response({"status": "reaction_processed"})

    async def _handle_file_event(self, event, payload):
        """Handle Slack file events"""
        channel_id = event.get("channel_id")
        user_id = event.get("user_id", "Unknown")
        file_info = event.get("file", {})
        file_name = file_info.get("name", "Unknown file")
        file_type = file_info.get("filetype", "unknown")
        timestamp = event.get("event_ts", "")
        
        # Find the MonitoredItem for this channel
        monitored = await sync_to_async(MonitoredItem.objects.filter)(type="slack_channel", name=channel_id)
        monitored = await sync_to_async(lambda: monitored.first())()
        
        if not monitored:
            return Response({"error": f"No MonitoredItem found for channel_id {channel_id}"}, status=404)
        
        # Find the active Slack integration for this company
        integration = await sync_to_async(CompanyIntegration.objects.filter)(
            company=monitored.company, 
            tool__slug="slack", 
            is_active=True
        )
        integration = await sync_to_async(lambda: integration.first())()
        
        if not integration:
            return Response({"error": "No active Slack integration found for this company"}, status=404)
        
        # Get channel name
        channel_name = await self._get_channel_name(integration, channel_id)
        
        logger.info(f"\n📎 [SLACK WEBHOOK] New file shared in #{channel_name}")
        logger.info(f"   Company: {monitored.company.name}")
        logger.info(f"   Channel: #{channel_name} ({channel_id})")
        logger.info(f"   User: {user_id}")
        logger.info(f"   File: {file_name} ({file_type})")
        logger.info(f"   {'─' * 50}")
        
        # Notify frontend
        notify_frontend("slack_file", {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "company_id": str(monitored.company.id),
            "company_name": monitored.company.name,
            "user_id": user_id,
            "file_name": file_name,
            "file_type": file_type,
            "file_info": file_info,
            "timestamp": timestamp,
            "event_type": "file",
            "raw": event,
        })
        
        return Response({"status": "file_processed"})

    async def _get_channel_name(self, integration, channel_id):
        """Get channel name from Slack API"""
        try:
            bot_token = integration.config.get("slack_bot_token") or integration.config.get("bot_token")
            if not bot_token:
                return channel_id
            
            from slack_sdk import WebClient
            client = WebClient(token=bot_token)
            response = client.conversations_info(channel=channel_id)
            return response["channel"]["name"]
        except Exception as e:
            logger.error(f"[Slack Webhook] Error getting channel name: {e}")
            return channel_id

    def _verify_slack_signature(self, request):
        """Verify Slack webhook signature"""
        # Check if test mode is enabled via environment
        if os.getenv('SLACK_TEST_MODE') == 'true':
            logger.info("[Slack Webhook] 🧪 Test mode enabled via environment - bypassing signature verification")
            return True
        
        # Production signature verification
        secret = os.getenv("SLACK_SIGNING_SECRET")
        if not secret:
            logger.warning("[Slack Webhook] No SLACK_SIGNING_SECRET configured, skipping signature verification")
            return True
        
        signature = request.headers.get("X-Slack-Signature")
        timestamp = request.headers.get("X-Slack-Request-Timestamp")
        
        if not signature or not timestamp:
            logger.warning("[Slack Webhook] Missing signature or timestamp headers")
            return False
        
        # Create the signature base string
        sig_basestring = f"v0:{timestamp}:{request.body.decode('utf-8')}"
        
        # Create the expected signature
        expected_signature = f"v0={hmac.new(secret.encode(), sig_basestring.encode(), hashlib.sha256).hexdigest()}"
        
        return hmac.compare_digest(expected_signature, signature)
