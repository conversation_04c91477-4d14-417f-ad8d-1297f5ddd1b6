import requests
from typing import Dict, Any
from urllib.parse import urlencode
import os
from dotenv import load_dotenv
from pathlib import Path
import logging

BASE_DIR = Path(__file__).resolve().parent.parent
load_dotenv(os.path.join(BASE_DIR, '.env'))

logger = logging.getLogger(__name__)

class SlackOAuthProvider:
    """Slack OAuth provider implementation"""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri

    @property
    def name(self) -> str:
        return "slack"
    
    @property
    def auth_url(self) -> str:
        return "https://slack.com/oauth/v2/authorize"
    
    @property
    def token_url(self) -> str:
        return "https://slack.com/api/oauth.v2.access"
    
    @property
    def scopes(self) -> list:
        return ["channels:read", "users:read", "team:read", "channels:history","channels:write"]


    
    def get_authorization_url(self, state: str) -> str:
        #TODO make this dynamic
        scopes="app_mentions:read,channels:history,groups:history,im:history,incoming-webhook,links:read,metadata.message:read,mpim:history,channels:read,channels:write.invites,chat:write,chat:write.customize,chat:write.public,conversations.connect:write,groups:read,groups:write,im:read,im:write,mpim:read,reactions:read,usergroups:read,users.profile:read,users:read,users:read.email"
        user_scope="channels:history,channels:read,chat:write,groups:history,im:history,im:write,mpim:history,mpim:read,mpim:write,reactions:read,users:read,users:read.email"
        params = {
            'client_id': self.client_id,
            'scope': scopes,
            'user_scope': user_scope,
            'redirect_uri': self.redirect_uri,
            'state': state,
        }
        return f"{self.auth_url}?{urlencode(params)}"
    
    def exchange_code_for_token(self, code: str) -> Dict[str, Any]:
        try:
            logger.info(f"Exchanging code: {code}, redirect_uri: {self.redirect_uri}")
            response = requests.post(self.token_url, {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'code': code,
                'redirect_uri': self.redirect_uri
            }, timeout=10)
            logger.info(f"Token exchange response: {response.status_code}, {response.text}")
            response.raise_for_status()
            data = response.json()
            
            if not data.get('ok'):
                raise Exception(f"Token exchange failed: {data.get('error', 'Unknown error')}")

            return {
                'access_token': data['access_token'],
                'scope': data['scope'],
                'team_id': data['team']['id'],
                'team_name': data['team']['name'],
                'user_id': data['authed_user']['id'],
                'authed_user': data['authed_user'],
                'raw_response': data
            }

        except requests.RequestException as e:
            raise Exception(f"Token exchange failed: {str(e)}")

    def test_connection(self, access_token: str) -> Dict[str, Any]:
        headers = {'Authorization': f'Bearer {access_token}'}
        try:
            response = requests.get('https://slack.com/api/auth.test', headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            return {
                'success': data.get('ok', False),
                'message': 'Connection successful!' if data.get('ok') else f"Error: {data.get('error', 'Unknown')}"
            }
        except requests.RequestException as e:
            return {
                'success': False,
                'message': f"Connection test failed: {str(e)}"
            }
