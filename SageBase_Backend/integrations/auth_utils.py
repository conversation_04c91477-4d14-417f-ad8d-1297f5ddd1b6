"""
Authentication utilities for handling Supabase JWT tokens across all Django apps.
This module provides a centralized way to extract and authenticate users from JWT tokens.
"""
import jwt
import logging
from django.db import connection

logger = logging.getLogger(__name__)



def get_user_from_token(request):
    """
    Extract user from Authorization header token.
    
    This function:
    1. Extracts JWT token from Authorization header
    2. Decodes the token to get user email
    3. Looks up the user in Django database
    4. Caches the result per request to avoid repeated queries
    5. Handles database connection errors gracefully
    
    Args:
        request: Django request object
        
    Returns:
        User object if found and authenticated, None otherwise
    """
    # Simple request-level caching to avoid repeated lookups
    if hasattr(request, '_cached_user'):
        return request._cached_user
    
    # Rate limiting: don't make too many database queries
    if hasattr(request, '_auth_attempts'):
        request._auth_attempts += 1
        if request._auth_attempts > 3:  # Max 3 attempts per request
            logger.warning("Too many authentication attempts, returning None")
            return None
    else:
        request._auth_attempts = 1
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    try:
        token = auth_header.split(' ')[1]
        
        # Decode the JWT token (without verification for now)
        # In production, you should verify the token with Supabase
        decoded_token = jwt.decode(token, options={"verify_signature": False})
        
        # Extract user email from the token
        user_email = decoded_token.get('email')
        logger.debug(f"Extracted email from token: {user_email}")
        if not user_email:
            logger.warning("No email found in JWT token")
            return None
        
        # Find the user in Django by email with connection error handling
        from integrations.models import User
        
        try:
            # Use select_related to optimize the query
            user = User.objects.select_related('company').get(email=user_email)
            logger.debug(f"Found user in database: {user.email}, company: {user.company}")
            # Cache the result for this request
            request._cached_user = user
            return user
        except User.DoesNotExist:
            logger.warning(f"User with email {user_email} not found in Django database")
            request._cached_user = None
            return None
        except Exception as e:
            # Handle database connection errors
            if "MaxClientsInSessionMode" in str(e) or "connection" in str(e).lower():
                logger.error(f"Database connection error: {e}")
                # Close the connection to free up the pool
                connection.close()
                request._cached_user = None
                return None
            else:
                logger.warning(f"Failed to get user from database: {e}")
                request._cached_user = None
                return None
        
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {e}")
        return None
    except Exception as e:
        logger.warning(f"Failed to extract user from token: {e}")
        return None


def require_authenticated_user(request):
    """
    Helper function to get authenticated user or return error response data.
    
    Args:
        request: Django request object
        
    Returns:
        tuple: (user_object, error_response_data)
        - If successful: (user, None)
        - If failed: (None, {'error': 'message', 'status': http_status_code})
    """
    user = get_user_from_token(request) or request.user
    
    if not user or not user.is_authenticated:
        return None, {
            'error': 'Authentication required',
            'status': 401
        }
    
    if not hasattr(user, 'company') or not user.company:
        return None, {
            'error': 'User must belong to a company',
            'status': 400
        }
    
    return user, None


def require_authentication(view_func):
    """
    Decorator to require authentication for API views.
    
    Usage:
        @api_view(['GET', 'POST'])
        @require_authentication
        def my_view(request, user):
            # user is guaranteed to be authenticated and have a company
            return Response({'data': 'success'})
    """
    from functools import wraps
    from rest_framework.response import Response
    
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        user, error = require_authenticated_user(request)
        if error:
            return Response({
                'success': False,
                'error': error['error']
            }, status=error['status'])
        
        # Pass the authenticated user as a parameter to the view
        return view_func(request, user, *args, **kwargs)
    
    return wrapper


def require_admin_access(view_func):
    """
    Decorator to require admin access for API views.
    
    Usage:
        @api_view(['POST', 'PUT', 'DELETE'])
        @require_admin_access
        def my_view(request, user):
            # user is guaranteed to be authenticated, have a company, and be an admin
            return Response({'data': 'success'})
    """
    from functools import wraps
    from rest_framework.response import Response
    
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        user, error = require_authenticated_user(request)
        if error:
            return Response({
                'success': False,
                'error': error['error']
            }, status=error['status'])
        
        # Check if user is admin
        if user.role != 'ADMIN':
            return Response({
                'success': False,
                'error': 'Admin access required'
            }, status=403)
        
        # Pass the authenticated admin user as a parameter to the view
        return view_func(request, user, *args, **kwargs)
    
    return wrapper 