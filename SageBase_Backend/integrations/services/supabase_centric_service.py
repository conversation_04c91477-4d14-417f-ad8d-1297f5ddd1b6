"""
Supabase-centric workflow service
Django Admin creates users → Users get Supabase connection emails
"""
import os
import requests
import logging
import secrets
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from integrations.models import User, Company, TeamInvitation

logger = logging.getLogger(__name__)


class SupabaseCentricService:
    """Service for Supabase-centric user management workflow"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL', '')
        self.supabase_service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY', '')
        self.frontend_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
        
        if not all([self.supabase_url, self.supabase_service_key]):
            logger.warning("Supabase credentials not configured. Emails will contain generic Supabase links.")
    
    def create_supabase_user_with_temp_password(self, email, user_metadata=None):
        """
        Create user in Supabase with temporary password
        """
        if not self.supabase_url or not self.supabase_service_key:
            logger.warning("Supabase not configured, skipping Auth user creation")
            return None, None
        
        try:
            # Generate temporary password
            temp_password = secrets.token_urlsafe(16)
            
            url = f"{self.supabase_url}/auth/v1/admin/users"
            headers = {
                'apikey': self.supabase_service_key,
                'Authorization': f'Bearer {self.supabase_service_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'email': email,
                'password': temp_password,
                'email_confirm': True,  # Auto-confirm email
                'user_metadata': user_metadata or {}
            }
            
            response = requests.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                supabase_user = response.json()
                logger.info(f"✅ Created Supabase Auth user with temp password: {email}")
                return supabase_user, temp_password
            else:
                logger.error(f"❌ Failed to create Supabase user: {response.status_code} - {response.text}")
                return None, None
                
        except Exception as e:
            logger.exception(f"❌ Error creating Supabase user: {str(e)}")
            return None, None
    
    def find_supabase_user_by_email(self, email):
        """
        Find Supabase user by email
        Returns the user object if found, None otherwise
        """
        if not self.supabase_url or not self.supabase_service_key:
            logger.warning("Supabase not configured, skipping user lookup")
            return None
        
        try:
            url = f"{self.supabase_url}/auth/v1/admin/users"
            headers = {
                'apikey': self.supabase_service_key,
                'Authorization': f'Bearer {self.supabase_service_key}',
            }
            
            # Add query parameter to filter by email
            params = {'email': email}
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                users_data = response.json()
                users = users_data.get('users', [])
                
                # Find exact email match
                for user in users:
                    if user.get('email', '').lower() == email.lower():
                        logger.info(f"✅ Found Supabase user: {email}")
                        return user
                
                logger.info(f"ℹ️ No Supabase user found with email: {email}")
                return None
            else:
                logger.error(f"❌ Failed to search Supabase users: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.exception(f"❌ Error searching Supabase user: {str(e)}")
            return None
    
    def delete_supabase_user_by_email(self, email):
        """
        Delete Supabase user by email
        Returns True if successful, False otherwise
        """
        # First find the user
        supabase_user = self.find_supabase_user_by_email(email)
        if not supabase_user:
            logger.info(f"ℹ️ No Supabase user found to delete for email: {email}")
            return True  # Consider it successful if user doesn't exist
        
        # Delete using the user ID
        return self.delete_supabase_user_by_id(supabase_user.get('id'))
    
    def delete_supabase_user_by_id(self, supabase_user_id):
        """
        Delete Supabase user by ID
        Returns True if successful, False otherwise
        """
        if not self.supabase_url or not self.supabase_service_key:
            logger.warning("Supabase not configured, skipping user deletion")
            return False
        
        try:
            url = f"{self.supabase_url}/auth/v1/admin/users/{supabase_user_id}"
            headers = {
                'apikey': self.supabase_service_key,
                'Authorization': f'Bearer {self.supabase_service_key}'
            }
            
            response = requests.delete(url, headers=headers)
            
            if response.status_code == 200:
                logger.info(f"✅ Deleted Supabase Auth user: {supabase_user_id}")
                return True
            else:
                logger.error(f"❌ Failed to delete Supabase user: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error deleting Supabase user: {str(e)}")
            return False
    
    def send_admin_supabase_connection_email(self, admin_user, temp_password):
        """
        Send email to new admin with Supabase connection instructions
        """
        try:
            # Create email content
            subject = f"Welcome to SageBase - Setup Your {admin_user.company.name.title()} Admin Account"
            
            # Supabase reset password link (will work with any Supabase project)
            supabase_login_url = f"{self.frontend_url}/login"
            supabase_reset_url = f"{self.frontend_url}/reset-password"
            
            # HTML email template
            html_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50;">Welcome to SageBase! 🎉</h2>
                    
                    <p>Hello <strong>{admin_user.first_name} {admin_user.last_name}</strong>,</p>
                    
                    <p>Your <strong>{admin_user.company.name.title()}</strong> admin account has been created on SageBase. You can now set up your account and start managing your team.</p>
                    
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #495057; margin-top: 0;">📧 Your Login Details:</h3>
                        <p><strong>Email:</strong> {admin_user.email}</p>
                        <p><strong>Temporary Password:</strong> <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{temp_password}</code></p>
                    </div>
                    
                    <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #1976d2; margin-top: 0;">🚀 Next Steps:</h3>
                        <ol>
                            <li><strong>Login to SageBase:</strong><br>
                                <a href="{supabase_login_url}" 
                                   style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px 0;">
                                    Login to SageBase
                                </a>
                            </li>
                            <li><strong>Reset Your Password:</strong><br>
                                After logging in, we recommend changing your password:<br>
                                <a href="{supabase_reset_url}" 
                                   style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px 0;">
                                    Reset Password
                                </a>
                            </li>
                            <li><strong>Invite Your Team:</strong><br>
                                Once logged in, you can start inviting team members to join your workspace.
                            </li>
                        </ol>
                    </div>
                    
                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>💡 Pro Tip:</strong> Save this email until you've successfully logged in and changed your password.</p>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="font-size: 12px; color: #666;">
                        This email was sent by SageBase for {admin_user.company.name.title()}. If you didn't expect this email, please contact support.
                    </p>
                </div>
            </body>
            </html>
            """
            
            # Plain text version
            plain_message = f"""
Welcome to SageBase!

Hello {admin_user.first_name} {admin_user.last_name},

Your {admin_user.company.name.title()} admin account has been created on SageBase.

Login Details:
Email: {admin_user.email}
Temporary Password: {temp_password}

Next Steps:
1. Login to SageBase: {supabase_login_url}
2. Reset your password: {supabase_reset_url}
3. Start inviting your team members

Save this email until you've successfully logged in and changed your password.

Best regards,
The SageBase Team
            """
            
            # Send email
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[admin_user.email],
                html_message=html_message,
                fail_silently=False,
            )
            
            logger.info(f"✅ Admin connection email sent to {admin_user.email}")
            return True
            
        except Exception as e:
            logger.exception(f"❌ Failed to send admin connection email to {admin_user.email}: {str(e)}")
            raise
    
    def send_team_member_supabase_invitation(self, team_member_email, company, invited_by_admin):
        """
        Send team member invitation with Supabase signup/login instructions
        """
        try:
            # Create temporary Supabase user for team member
            user_metadata = {
                'company_id': str(company.id),
                'company_name': company.name,
                'role': 'USER',
                'invited_by': invited_by_admin.email
            }
            
            # Check if invitation already exists for this email and company
            existing_invitation = TeamInvitation.objects.filter(
                email=team_member_email,
                company=company,
                status__in=['PENDING', 'SENT']  # Only check active invitations
            ).first()
            
            if existing_invitation:
                raise ValueError(f"An invitation already exists for {team_member_email}")
            
            supabase_user, temp_password = self.create_supabase_user_with_temp_password(
                email=team_member_email,
                user_metadata=user_metadata
            )
            
      
            
            # Create invitation record in Django
            invitation = TeamInvitation.objects.create(
                company=company,
                email=team_member_email,
                role=User.Role.USER,
                invited_by=invited_by_admin
            )
            
            # Email content
            subject = f"You're invited to join {company.name.title()} on SageBase"
            
            supabase_login_url = f"{self.frontend_url}/login"
            supabase_set_password_url = f"{self.frontend_url}/set-password?email={team_member_email}"
            
            # HTML email template
            html_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50;">You're Invited to Join {company.name.title()}! 🎉</h2>
                    
                    <p>Hi there,</p>
                    
                    <p><strong>{invited_by_admin.first_name} {invited_by_admin.last_name}</strong> has invited you to join <strong>{company.name.title()}</strong> on SageBase.</p>
                    
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #495057; margin-top: 0;">📧 Your Login Details:</h3>
                        <p><strong>Email:</strong> {team_member_email}</p>
                        <p><strong>Temporary Password:</strong> <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{temp_password if temp_password else 'Use reset password link below'}</code></p>
                    </div>
                    
                    <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #2e7d32; margin-top: 0;">🚀 Get Started:</h3>
                        <ol>
                            <li><strong>Login to SageBase:</strong><br>
                                <a href="{supabase_login_url}" 
                                   style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 8px 0; font-weight: bold;">
                                    Login to SageBase
                                </a>
                            </li>
                            <li><strong>Set Your Password:</strong><br>
                                We recommend setting a new password immediately:<br>
                                <a href="{supabase_set_password_url}" 
                                   style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 8px 0; font-weight: bold;">
                                    Set New Password
                                </a>
                            </li>
                        </ol>
                    </div>
                    
                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <p style="margin: 0;"><strong>💡 Important:</strong> This invitation will expire in 7 days. Please set up your account soon!</p>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="font-size: 12px; color: #666;">
                        This invitation was sent by SageBase on behalf of {company.name.title()}. If you didn't expect this invitation, you can safely ignore this email.
                    </p>
                </div>
            </body>
            </html>
            """
            
            # Plain text version
            plain_message = f"""
You're Invited to Join {company.name.title()}!

Hi there,

{invited_by_admin.first_name} {invited_by_admin.last_name} has invited you to join {company.name.title()} on SageBase.

Your Login Details:
Email: {team_member_email}
Temporary Password: {temp_password if temp_password else 'Use reset password link'}

Get Started:
1. Login to SageBase: {supabase_login_url}
2. Set your new password: {supabase_set_password_url}

This invitation expires in 7 days.

Best regards,
The SageBase Team
            """
            
            # Send email
            logger.info(f"📧 Sending email to {team_member_email}")
            logger.info(f"📧 From: {settings.DEFAULT_FROM_EMAIL}")
            logger.info(f"📧 Subject: {subject}")
            logger.info(f"📧 Email settings - HOST: {settings.EMAIL_HOST}, PORT: {settings.EMAIL_PORT}, USER: {settings.EMAIL_HOST_USER}")
            
            try:
                from django.core.mail import EmailMessage
                from email.mime.multipart import MIMEMultipart
                from email.mime.text import MIMEText
                
                # Create email with proper display name
                email = EmailMessage(
                    subject=subject,
                    body=html_message,
                    from_email="<EMAIL>",
                    to=[team_member_email],
                )
                email.content_subtype = "html"
                email.send(fail_silently=False)
                
                logger.info(f"✅ Team member Supabase invitation sent to {team_member_email}")
                return invitation, supabase_user
            except Exception as email_error:
                logger.error(f"❌ Email sending failed: {email_error}")
                logger.error(f"❌ Email details - To: {team_member_email}, From: {settings.DEFAULT_FROM_EMAIL}")
                raise
            
        except Exception as e:
            logger.exception(f"❌ Failed to send team member invitation to {team_member_email}: {str(e)}")
            raise


class DjangoAdminWorkflowService:
    """Service for Django Admin to create companies and users"""
    
    def __init__(self):
        self.supabase_service = SupabaseCentricService()
    
    def create_company_and_admin_for_django_admin(self, company_name, admin_email, admin_first_name, admin_last_name):
        """
        Django Admin creates company + admin user, sends Supabase connection email
        """
        company = None
        admin_user = None
        knowledge_space = None
        supabase_user = None
        existing_knowledge_space = None
        
        try:
            # Step 1: Create the company
            from django.utils.text import slugify
            company = Company.objects.create(
                name=company_name.lower(),
                slug=slugify(company_name),
                email=admin_email
            )
            logger.info(f"✅ Created company: {company.name}")
            
            
                        # Lets create a project in the knowledge map

            
            # Step 2: Create default knowledge space
            try:
                from knowledge_spaces_Q_A.models import Knowledge_Space
                import random
                
                # Check if a knowledge space with this name already exists for this company
                knowledge_space_name = f"{company.name.title()}"
                existing_knowledge_space = Knowledge_Space.objects.filter(
                    name=knowledge_space_name,
                    company=company
                ).first()
                
                if not existing_knowledge_space:
                    # Generate a unique color for the knowledge space
                    colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16']
                    color = random.choice(colors)
                    
                    # Create the default knowledge space
                    knowledge_space = Knowledge_Space.objects.create(
                        name=knowledge_space_name,
                        color=color,
                        initial=company.name[0].upper() if company.name else 'K',
                        company=company
                    )
                    
                    logger.info(f"✅ Created default knowledge space: {knowledge_space.name} (ID: {knowledge_space.id}) for company: {company.name}")
                else:
                    knowledge_space = existing_knowledge_space
                    logger.info(f"ℹ️ Knowledge space '{knowledge_space_name}' already exists for company: {company.name}")
                    
            except Exception as e:
                logger.exception(f"❌ Failed to create default knowledge space for company {company.name}: {e}")
                # Rollback company creation
                company.delete()
                raise Exception(f"Failed to create knowledge space: {str(e)}")
            
            # Step 3: Create the admin user in Django
            try:
                from django.utils.crypto import get_random_string
                admin_user = User.objects.create_user(
                    email=admin_email,
                    password=get_random_string(12),  # Random password, not used
                    first_name=admin_first_name,
                    last_name=admin_last_name,
                    company=company,
                    role=User.Role.ADMIN,
                    is_active=True
                )
                logger.info(f"✅ Created admin user: {admin_user.email}")
                
            except Exception as e:
                logger.exception(f"❌ Failed to create admin user: {str(e)}")
                # Rollback: delete knowledge space and company
                if knowledge_space and not existing_knowledge_space:
                    knowledge_space.delete()
                company.delete()
                raise Exception(f"Failed to create admin user: {str(e)}")
            
            if company and admin_user:
                from knowledge_map.models import Project
                project = Project.objects.create(
                    name=company.name.lower(),
                    company=company,
                    doc_responsible=admin_user,
                    secondary_responsible=admin_user
                )
            
            # Step 4: Create Supabase user with temp password
            try:
                user_metadata = {
                    'company_id': str(company.id),
                    'company_name': company.name,
                    'role': admin_user.role,
                    'first_name': admin_first_name,
                    'last_name': admin_last_name
                }
                
                supabase_user, temp_password = self.supabase_service.create_supabase_user_with_temp_password(
                    email=admin_email,
                    user_metadata=user_metadata
                )
                
                if not supabase_user:
                    logger.warning(f"⚠️ Supabase user creation failed for {admin_email}, but continuing with Django user")
                
            except Exception as e:
                logger.exception(f"❌ Failed to create Supabase user: {str(e)}")
                # Rollback: delete admin user, knowledge space, and company
                admin_user.delete()
                if knowledge_space and not existing_knowledge_space:
                    knowledge_space.delete()
                company.delete()
                raise Exception(f"Failed to create Supabase user: {str(e)}")
            
            # Step 5: Send connection email to admin
            try:
                if supabase_user and temp_password:
                    self.supabase_service.send_admin_supabase_connection_email(admin_user, temp_password)
                    logger.info(f"✅ Connection email sent to {admin_email}")
                else:
                    logger.warning(f"⚠️ Skipping email sending - no Supabase user or temp password available")
                    
            except Exception as e:
                logger.exception(f"❌ Failed to send connection email: {str(e)}")
                # Don't rollback everything for email failure, but log it
                logger.warning(f"⚠️ Company and user created successfully, but email failed: {str(e)}")
            
            logger.info(f"✅ Company '{company.name}' and admin user created successfully")
            return company, admin_user, supabase_user
            
        except Exception as e:
            logger.exception(f"❌ Failed to create company and admin: {str(e)}")
            # Final cleanup if something went wrong
            if admin_user:
                try:
                    admin_user.delete()
                except:
                    pass
            if knowledge_space and not existing_knowledge_space:
                try:
                    knowledge_space.delete()
                except:
                    pass
            if company:
                try:
                    company.delete()
                except:
                    pass
            raise