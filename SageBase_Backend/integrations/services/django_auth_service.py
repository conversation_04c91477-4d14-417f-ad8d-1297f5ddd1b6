"""
Django-native authentication service to replace Supabase Auth integration.
Handles user creation, JWT token generation, email verification, and password management.
"""
import os
import jwt
import uuid
import secrets
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.crypto import get_random_string
from django.utils.html import strip_tags
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils import timezone
from integrations.models import User, TeamInvitation

logger = logging.getLogger(__name__)


class DjangoAuthService:
    """Django-native authentication service"""
    
    def __init__(self):
        self.jwt_secret = getattr(settings, 'SECRET_KEY')
        self.jwt_algorithm = 'HS256'
        self.token_expiry_hours = 24
    
    def generate_jwt_token(self, user):
        """
        Generate JWT token for user authentication
        """
        try:
            payload = {
                'user_id': str(user.id),
                'email': user.email,
                'company_id': str(user.company.id) if user.company else None,
                'company_name': user.company.name if user.company else None,
                'role': user.role,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'exp': datetime.utcnow() + timedelta(hours=self.token_expiry_hours),
                'iat': datetime.utcnow(),
                'iss': 'sagebase-backend'
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
            logger.info(f"✅ Generated JWT token for user: {user.email}")
            return token
            
        except Exception as e:
            logger.error(f"❌ Error generating JWT token for user {user.email}: {str(e)}")
            return None
    
    def verify_jwt_token(self, token):
        """
        Verify and decode JWT token
        
        Returns:
            dict: Decoded token payload if valid, None if invalid
        """
        try:
            payload = jwt.decode(
                token, 
                self.jwt_secret, 
                algorithms=[self.jwt_algorithm],
                options={"verify_exp": True}
            )
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error verifying JWT token: {str(e)}")
            return None
    
    def authenticate_user(self, email, password):
        """
        Authenticate user with email and password
        
        Returns:
            tuple: (user_object, jwt_token) if successful, (None, None) if failed
        """
        try:
            user = authenticate(username=email, password=password)
            if user and user.is_active:
                token = self.generate_jwt_token(user)
                logger.info(f"✅ User authenticated successfully: {email}")
                return user, token
            else:
                logger.warning(f"❌ Authentication failed for user: {email}")
                return None, None
                
        except Exception as e:
            logger.error(f"❌ Error authenticating user {email}: {str(e)}")
            return None, None
    
    def create_django_user(self, email, password, user_metadata=None):
        """
        Create user in Django with email verification
        
        Args:
            email: User email
            password: User password
            user_metadata: Dict containing user info (first_name, last_name, company_id, role)
        
        Returns:
            User object if created successfully, None if failed
        """
        try:
            # Check if user already exists
            if User.objects.filter(email=email).exists():
                logger.warning(f"User with email {email} already exists")
                return None
            
            # Extract metadata
            metadata = user_metadata or {}
            first_name = metadata.get('first_name', '')
            last_name = metadata.get('last_name', '')
            company_id = metadata.get('company_id')
            role = metadata.get('role', User.Role.USER)
            
            # Get company if provided
            company = None
            if company_id:
                from integrations.models import Company
                try:
                    company = Company.objects.get(id=company_id)
                except Company.DoesNotExist:
                    logger.warning(f"Company with ID {company_id} not found")
            
            # Create user (inactive until email verification)
            user = User.objects.create_user(
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                company=company,
                role=role,
                is_active=False  # Require email verification
            )
            
            logger.info(f"✅ Created Django user: {email}")
            
            # Send email verification
            self.send_email_verification(user)
            
            return user
            
        except Exception as e:
            logger.error(f"❌ Error creating Django user {email}: {str(e)}")
            return None
    
    def send_email_verification(self, user, custom_message=None):
        """
        Send email verification to user
        """
        try:
            # Generate verification token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            
            # Build verification URL
            frontend_base_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
            verification_url = f"{frontend_base_url}/verify-email?uid={uid}&token={token}"
            
            subject = 'SageBase - Verify your email address'
            
            if custom_message:
                message = custom_message
            else:
                message = f"""
Hello {user.first_name},

Welcome to SageBase! Please verify your email address by clicking the link below:

{verification_url}

This link will expire in 24 hours.

If you didn't create an account with SageBase, please ignore this email.

Best regards,
The SageBase Team
"""
            
            send_mail(
                subject=subject,
                message=message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[user.email],
                fail_silently=False
            )
            
            logger.info(f"✅ Sent email verification to: {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending email verification to {user.email}: {str(e)}")
            return False
    
    def verify_email(self, uid, token):
        """
        Verify email address using token
        
        Returns:
            User object if verification successful, None if failed
        """
        try:
            from django.utils.http import urlsafe_base64_decode
            
            # Decode user ID
            user_id = urlsafe_base64_decode(uid).decode()
            user = User.objects.get(pk=user_id)
            
            # Verify token
            if default_token_generator.check_token(user, token):
                user.is_active = True
                user.save()
                logger.info(f"✅ Email verified for user: {user.email}")
                return user
            else:
                logger.warning(f"❌ Invalid verification token for user: {user.email}")
                return None
                
        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            logger.warning(f"❌ Invalid verification link: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ Error verifying email: {str(e)}")
            return None
    
    def send_password_reset_email(self, user):
        """
        Send password reset email to user using HTML template
        """
        try:
            # Generate reset token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            
            # Build reset URL
            frontend_base_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
            reset_url = f"{frontend_base_url}/reset-password?uid={uid}&token={token}"
            login_url = f"{frontend_base_url}/login"
            
            # Context for email templates
            context = {
                'user': user,
                'reset_url': reset_url,
                'login_url': login_url
            }
            
            # Render HTML and text versions
            html_message = render_to_string('emails/password_reset.html', context)
            text_message = render_to_string('emails/password_reset.txt', context)
            
            subject = '🔐 SageBase - Password Reset Request'
            
            # Send email with both HTML and text versions
            from django.core.mail import EmailMultiAlternatives
            
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', 'SageBase <<EMAIL>>'),
                to=[user.email]
            )
            email.attach_alternative(html_message, "text/html")
            email.send()
            
            logger.info(f"✅ Sent password reset email to: {user.email}")
            return True
            
        except Exception as e:
            logger.exception(f"❌ Error sending password reset email to {user.email}: {str(e)}")
            return False
    
    def reset_password(self, uid, token, new_password):
        """
        Reset user password using token
        
        Returns:
            User object if reset successful, None if failed
        """
        try:
            from django.utils.http import urlsafe_base64_decode
            
            # Decode user ID
            user_id = urlsafe_base64_decode(uid).decode()
            user = User.objects.get(pk=user_id)
            
            # Verify token
            if default_token_generator.check_token(user, token):
                user.set_password(new_password)
                user.save()
                logger.info(f"✅ Password reset successful for user: {user.email}")
                return user
            else:
                logger.warning(f"❌ Invalid password reset token for user: {user.email}")
                return None
                
        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            logger.warning(f"❌ Invalid password reset link: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ Error resetting password: {str(e)}")
            return None
    
    def delete_user(self, user_id):
        """
        Delete user from Django
        
        Returns:
            bool: True if deletion successful, False otherwise
        """
        try:
            user = User.objects.get(id=user_id)
            email = user.email
            user.delete()
            logger.info(f"✅ Deleted Django user: {email}")
            return True
            
        except User.DoesNotExist:
            logger.warning(f"❌ User with ID {user_id} not found")
            return False
        except Exception as e:
            logger.error(f"❌ Error deleting user {user_id}: {str(e)}")
            return False


class EnhancedInvitationService:
    """Enhanced invitation service with Django Auth integration"""
    
    def __init__(self):
        self.django_auth = DjangoAuthService()
    
    def create_company_admin_with_django_auth(self, company_name, admin_email, admin_first_name, admin_last_name, password=None):
        """
        Create company and admin with Django authentication
        """
        from integrations.services.invitation_service import InvitationService
        
        try:
            # Step 1: Generate temporary password if not provided
            temp_password = password or get_random_string(12)
            
            # Step 2: Create company and Django user with Django's native auth
            company, admin_user = InvitationService.create_company_admin(
                company_name=company_name,
                admin_email=admin_email,
                admin_first_name=admin_first_name,
                admin_last_name=admin_last_name,
                password=temp_password
            )
            
            # Create project in the knowledge map
            if company:

                #also create a knowledge space for the admin user
                from knowledge_spaces_Q_A.models import Knowledge_Space
                # Generate a unique color for the user's knowledge space
                import random
                colors = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4", "#84CC16", "#F97316"]
                color = random.choice(colors)
                knowledge_space = Knowledge_Space.objects.create(
                    name=company.name.lower(),
                    company=company,
                    user=admin_user,
                    color=color,
                    initial=company.name[0].upper()
                )
                logger.info(f"✅ Created knowledge space for company: {company.name}")
            
            # Step 3: Send welcome email with login instructions
            self.send_admin_welcome_email(admin_user, temp_password)
            
            logger.info(f"✅ Created company and admin with Django auth: {admin_email}")
            
            return company, admin_user
            
        except Exception as e:
            logger.error(f"❌ Failed to create company admin with Django auth: {str(e)}")
            raise
    
    def accept_invitation_with_django_auth(self, token, first_name, last_name, password):
        """
        Accept invitation and create Django user
        """
        try:
            # Step 1: Get invitation
            invitation = TeamInvitation.objects.get(
                token=token,
                status=TeamInvitation.Status.PENDING
            )
            
            if invitation.is_expired():
                invitation.status = TeamInvitation.Status.EXPIRED
                invitation.save()
                raise ValueError("Invitation has expired")
            
            # Step 2: Check if user already exists
            if User.objects.filter(email=invitation.email).exists():
                raise ValueError("User with this email already exists")
            
            # Step 3: Create Django user (active by default since they accepted invitation)
            django_user = User.objects.create_user(
                email=invitation.email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                company=invitation.company,
                role=invitation.role,
                is_active=True
            )
            
            # Step 4: Mark invitation as accepted
            invitation.status = TeamInvitation.Status.ACCEPTED
            invitation.accepted_at = timezone.now()
            invitation.save()
            
            # Step 5: Send welcome email
            self.send_team_member_welcome_email(django_user)
            
            logger.info(f"✅ Created Django user from invitation: {invitation.email}")
            
            return django_user
            
        except TeamInvitation.DoesNotExist:
            logger.error(f"❌ Invalid invitation token: {token}")
            raise ValueError("Invalid invitation token")
        except Exception as e:
            logger.error(f"❌ Failed to accept invitation with Django auth: {str(e)}")
            raise
    
    def send_admin_welcome_email(self, user, temp_password):
        """
        Send welcome email to company admin using HTML template
        """
        try:
            frontend_base_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
            login_url = f"{frontend_base_url}/login"
            
            # Context for email templates
            context = {
                'user': user,
                'temp_password': temp_password,
                'login_url': login_url,
                'company_name': user.company.name.title() if user.company else 'Your Company'
            }
            
            # Render HTML and text versions
            html_message = render_to_string('emails/admin_welcome.html', context)
            text_message = render_to_string('emails/admin_welcome.txt', context)
            
            subject = f'🎉 Welcome to SageBase - Your {user.company.name.title()} account is ready!'
            
            # Send email with both HTML and text versions
            from django.core.mail import EmailMultiAlternatives
            
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', 'SageBase <<EMAIL>>'),
                to=[user.email]
            )
            email.attach_alternative(html_message, "text/html")
            email.send()
            
            logger.info(f"✅ Sent admin welcome email to: {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending admin welcome email to {user.email}: {str(e)}")
            return False
    
    def send_team_member_welcome_email(self, user):
        """
        Send welcome email to team member using HTML template
        """
        try:
            frontend_base_url = getattr(settings, 'FRONTEND_BASE_URL', 'http://localhost:3000')
            login_url = f"{frontend_base_url}/login"
            
            # Context for email templates
            context = {
                'user': user,
                'login_url': login_url,
                'company_name': user.company.name.title() if user.company else 'Your Company'
            }
            
            # Render HTML and text versions
            html_message = render_to_string('emails/team_member_welcome.html', context)
            text_message = render_to_string('emails/team_member_welcome.txt', context)
            
            subject = f'🎉 Welcome to {user.company.name.title()} on SageBase!'
            
            # Send email with both HTML and text versions
            from django.core.mail import EmailMultiAlternatives
            
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', 'SageBase <<EMAIL>>'),
                to=[user.email]
            )
            email.attach_alternative(html_message, "text/html")
            email.send()
            
            logger.info(f"✅ Sent team member welcome email to: {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending team member welcome email to {user.email}: {str(e)}")
            return False