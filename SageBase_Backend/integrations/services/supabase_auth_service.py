"""
Supabase Auth integration service for SageBase invitation system
"""
import os
import requests
import logging
from django.conf import settings
from django.utils.crypto import get_random_string
from integrations.models import User, TeamInvitation
from django.utils import timezone

logger = logging.getLogger(__name__)


class SupabaseAuthService:
    """Service for integrating with Supabase Auth"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL', '')
        self.supabase_service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY', '')
        self.supabase_anon_key = os.getenv('SUPABASE_ANON_KEY', '')
        
        if not all([self.supabase_url, self.supabase_service_key]):
            logger.warning("Supabase credentials not configured. Some features may not work.")
    
    def create_supabase_user(self, email, password, user_metadata=None):
        """
        Create user in Supabase Auth
        """
        if not self.supabase_url or not self.supabase_service_key:
            logger.error("Supabase not configured, skipping Auth user creation")
            return None
        
        try:
            url = f"{self.supabase_url}/auth/v1/admin/users"
            headers = {
                'apikey': self.supabase_service_key,
                'Authorization': f'Bearer {self.supabase_service_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'email': email,
                'password': password,
                'email_confirm': True,  # Auto-confirm email
                'user_metadata': user_metadata or {}
            }
            
            response = requests.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                supabase_user = response.json()
                logger.info(f"✅ Created Supabase Auth user: {email}")
                return supabase_user
            else:
                logger.error(f"❌ Failed to create Supabase user: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error creating Supabase user: {str(e)}")
            return None
    
    def update_user_metadata(self, supabase_user_id, metadata):
        """
        Update user metadata in Supabase Auth
        """
        if not self.supabase_url or not self.supabase_service_key:
            return None
        
        try:
            url = f"{self.supabase_url}/auth/v1/admin/users/{supabase_user_id}"
            headers = {
                'apikey': self.supabase_service_key,
                'Authorization': f'Bearer {self.supabase_service_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'user_metadata': metadata
            }
            
            response = requests.put(url, json=data, headers=headers)
            
            if response.status_code == 200:
                logger.info(f"✅ Updated Supabase user metadata: {supabase_user_id}")
                return response.json()
            else:
                logger.error(f"❌ Failed to update user metadata: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error updating user metadata: {str(e)}")
            return None
    
    def delete_supabase_user(self, supabase_user_id):
        """
        Delete user from Supabase Auth
        """
        if not self.supabase_url or not self.supabase_service_key:
            return False
        
        try:
            url = f"{self.supabase_url}/auth/v1/admin/users/{supabase_user_id}"
            headers = {
                'apikey': self.supabase_service_key,
                'Authorization': f'Bearer {self.supabase_service_key}'
            }
            
            response = requests.delete(url, headers=headers)
            
            if response.status_code == 200:
                logger.info(f"✅ Deleted Supabase Auth user: {supabase_user_id}")
                return True
            else:
                logger.error(f"❌ Failed to delete Supabase user: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error deleting Supabase user: {str(e)}")
            return False


class EnhancedInvitationService:
    """Enhanced invitation service with Supabase Auth integration"""
    
    def __init__(self):
        self.supabase_auth = SupabaseAuthService()
    
    def create_company_admin_with_supabase(self, company_name, admin_email, admin_first_name, admin_last_name, password=None):
        """
        Create company and admin with both Django and Supabase Auth
        """
        from integrations.services.invitation_service import InvitationService
        
        try:
            # Step 1: Create company and Django user
            company, admin_user = InvitationService.create_company_admin(
                company_name=company_name,
                admin_email=admin_email,
                admin_first_name=admin_first_name,
                admin_last_name=admin_last_name,
                password=password
            )
            
            # Lets create a project in the knowledge map
            if company:
                from knowledge_map.models import Project
                project = Project.objects.create(
                    name=company.name.lower(),
                    company=company,
                    doc_responsible=admin_user,
                    secondary_responsible=admin_user
                )
            
            # Step 2: Create user in Supabase Auth
            user_metadata = {
                'company_id': str(company.id),
                'company_name': company.name,
                'role': admin_user.role,
                'first_name': admin_first_name,
                'last_name': admin_last_name
            }
            
            supabase_user = self.supabase_auth.create_supabase_user(
                email=admin_email,
                password=password or get_random_string(12),
                user_metadata=user_metadata
            )
            
            if supabase_user:
                # Store Supabase user ID in Django user (you might want to add this field)
                logger.info(f"✅ Created both Django and Supabase users for admin: {admin_email}")
            else:
                logger.warning(f"⚠️ Django user created but Supabase Auth creation failed for: {admin_email}")
            
            return company, admin_user, supabase_user
            
        except Exception as e:
            logger.error(f"❌ Failed to create company admin with Supabase: {str(e)}")
            raise
    
    def accept_invitation_with_supabase(self, token, first_name, last_name, password):
        """
        Accept invitation and create both Django and Supabase Auth users
        """
        try:
            # Step 1: Get invitation
            invitation = TeamInvitation.objects.get(
                token=token,
                status=TeamInvitation.Status.PENDING
            )
            
            if invitation.is_expired():
                invitation.status = TeamInvitation.Status.EXPIRED
                invitation.save()
                raise ValueError("Invitation has expired")
            
            # Step 2: Check if user already exists
            if User.objects.filter(email=invitation.email).exists():
                raise ValueError("User with this email already exists")
            
            # Step 3: Create Django user
            django_user = User.objects.create_user(
                email=invitation.email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                company=invitation.company,
                role=invitation.role,
                is_active=True
            )
            
            # Step 4: Create Supabase Auth user
            user_metadata = {
                'company_id': str(invitation.company.id),
                'company_name': invitation.company.name,
                'role': invitation.role,
                'first_name': first_name,
                'last_name': last_name
            }
            
            supabase_user = self.supabase_auth.create_supabase_user(
                email=invitation.email,
                password=password,
                user_metadata=user_metadata
            )
            
            # Step 5: Mark invitation as accepted
            invitation.status = TeamInvitation.Status.ACCEPTED
            invitation.accepted_at = timezone.now()
            invitation.save()
            
            if supabase_user:
                logger.info(f"✅ Created both Django and Supabase users for: {invitation.email}")
            else:
                logger.warning(f"⚠️ Django user created but Supabase Auth creation failed for: {invitation.email}")
            
            return django_user, supabase_user
            
        except TeamInvitation.DoesNotExist:
            logger.error(f"❌ Invalid invitation token: {token}")
            raise ValueError("Invalid invitation token")
        except Exception as e:
            logger.error(f"❌ Failed to accept invitation with Supabase: {str(e)}")
            raise