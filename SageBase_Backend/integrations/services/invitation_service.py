"""
Team invitation service for SageBase
"""
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils import timezone
from django.utils.crypto import get_random_string
from integrations.models import User, Company, TeamInvitation
import logging

logger = logging.getLogger(__name__)


class InvitationService:
    """Service for handling team member invitations"""
    
    @staticmethod
    def create_company_admin(company_name, admin_email, admin_first_name, admin_last_name, password=None):
        """
        Create a company and its admin user
        """
        try:
            # Create the company
            from django.utils.text import slugify
            company = Company.objects.create(
                name=company_name.lower(),
                slug=slugify(company_name),
                email=admin_email
            )
            
            # Create the admin user
            admin_user = User.objects.create_user(
                email=admin_email,
                password=password or get_random_string(12),
                first_name=admin_first_name,
                last_name=admin_last_name,
                company=company,
                role=User.Role.ADMIN,
                is_active=True
            )
            
            logger.info(f"✅ Created company '{company.name}' with admin user '{admin_user.email}'")
            return company, admin_user
            
        except Exception as e:
            logger.error(f"❌ Failed to create company admin: {str(e)}")
            raise
    
    @staticmethod
    def send_invitation_email(invitation):
        """
        Send invitation email to team member
        """
        try:
            # Build invitation URL
            frontend_url = getattr(settings, 'FRONTEND_BASE_URL')
            invitation_url = f"{frontend_url}/accept-invitation?token={invitation.token}"
            
            
            # Email context
            context = {
                'company_name': invitation.company.name,
                'inviter_name': f"{invitation.invited_by.first_name} {invitation.invited_by.last_name}",
                'invitation_url': invitation_url,
                'role': invitation.get_role_display(),
                'expires_at': invitation.expires_at,
            }
            
            # Create email content
            subject = f"You're invited to join {invitation.company.name.title()} on SageBase"
            
            # HTML email template (inline for simplicity)
            html_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50;">You're Invited to Join {context['company_name'].title()}!</h2>
                    
                    <p>Hi there,</p>
                    
                    <p><strong>{context['inviter_name']}</strong> has invited you to join <strong>{context['company_name'].title()}</strong> on SageBase as a <strong>{context['role']}</strong>.</p>
                    
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <p style="margin: 0; text-align: center;">
                            <a href="{context['invitation_url']}" 
                               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">
                                Accept Invitation
                            </a>
                        </p>
                    </div>
                    
                    <p><small>This invitation will expire on {context['expires_at'].strftime('%B %d, %Y at %I:%M %p UTC')}.</small></p>
                    
                    <p>If you can't click the button above, copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #666;">{context['invitation_url']}</p>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="font-size: 12px; color: #666;">
                        This invitation was sent by SageBase. If you didn't expect this invitation, you can safely ignore this email.
                    </p>
                </div>
            </body>
            </html>
            """
            
            # Plain text version
            plain_message = f"""
You're Invited to Join {context['company_name'].title()}!

Hi there,

{context['inviter_name']} has invited you to join {context['company_name'].title()} on SageBase as a {context['role']}.

To accept this invitation, click the following link:
{context['invitation_url']}

This invitation will expire on {context['expires_at'].strftime('%B %d, %Y at %I:%M %p UTC')}.

If you didn't expect this invitation, you can safely ignore this email.

Best regards,
The SageBase Team
            """
            
            # Send email
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[invitation.email],
                html_message=html_message,
                fail_silently=False,
            )
            
            logger.info(f"✅ Invitation email sent to {invitation.email} for company {invitation.company.name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send invitation email to {invitation.email}: {str(e)}")
            raise
    
    @staticmethod
    def invite_team_member(company, email, role, invited_by):
        """
        Create and send team member invitation
        """
        try:
            # Check if user already exists in the company
            if User.objects.filter(email=email, company=company).exists():
                raise ValueError(f"User with email {email} already exists in company {company.name}")
            
            # Check if there's already a pending invitation
            existing_invitation = TeamInvitation.objects.filter(
                company=company,
                email=email,
                status=TeamInvitation.Status.PENDING
            ).first()
            
            if existing_invitation and not existing_invitation.is_expired():
                raise ValueError(f"Pending invitation already exists for {email}")
            
            # Create new invitation
            invitation = TeamInvitation.objects.create(
                company=company,
                email=email,
                role=role,
                invited_by=invited_by
            )
            
            # Send invitation email
            InvitationService.send_invitation_email(invitation)
            
            logger.info(f"✅ Team member invitation created and sent to {email}")
            return invitation
            
        except Exception as e:
            logger.error(f"❌ Failed to invite team member {email}: {str(e)}")
            raise
    
    @staticmethod
    def accept_invitation(token, first_name, last_name, password):
        """
        Accept team invitation and create user account
        """
        try:
            # Find the invitation
            invitation = TeamInvitation.objects.get(
                token=token,
                status=TeamInvitation.Status.PENDING
            )
            
            # Check if invitation is expired
            if invitation.is_expired():
                invitation.status = TeamInvitation.Status.EXPIRED
                invitation.save()
                raise ValueError("Invitation has expired")
            
            # Check if user already exists
            if User.objects.filter(email=invitation.email).exists():
                raise ValueError("User with this email already exists")
            
            # Create the user
            user = User.objects.create_user(
                email=invitation.email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                company=invitation.company,
                role=invitation.role,
                is_active=True
            )
            
            # Mark invitation as accepted
            invitation.status = TeamInvitation.Status.ACCEPTED
            invitation.accepted_at = timezone.now()
            invitation.save()
            
            logger.info(f"✅ Invitation accepted by {user.email} for company {invitation.company.name}")
            return user
            
        except TeamInvitation.DoesNotExist:
            logger.error(f"❌ Invalid invitation token: {token}")
            raise ValueError("Invalid invitation token")
        except Exception as e:
            logger.error(f"❌ Failed to accept invitation: {str(e)}")
            raise