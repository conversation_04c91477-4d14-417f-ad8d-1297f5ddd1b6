from django.db import connection
import logging

logger = logging.getLogger(__name__)

class DatabaseConnectionMiddleware:
    """Middleware to manage database connections"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Process request
        response = self.get_response(request)
        
        # Close database connections at the end of each request
        # This helps prevent connection pool exhaustion
        try:
            connection.close()
        except Exception as e:
            logger.warning(f"Failed to close database connection: {e}")
        
        return response 