"""
Team invitation API views
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from integrations.models import User, Company, TeamInvitation
from integrations.services.invitation_service import InvitationService
from integrations.auth_utils import get_user_from_token
import logging

logger = logging.getLogger(__name__)



@api_view(['POST'])
@permission_classes([AllowAny])
def invite_team_member(request):
    """
    Invite a new team member
    POST /api/integrations/invite-team-member/
    
    Body:
    {
        "email": "<EMAIL>",
        "role": "USER",  # or "ADMIN"
        "companyId": "uuid"  # optional if user is authenticated
    }
    """
    try:
        data = request.data
        
        # Validate required fields
        if not data.get('email'):
            return Response(
                {"error": "email is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the inviting user and company
        user = get_user_from_token(request) or request.user
        company = None
        
        if user and user.is_authenticated:
            # Check if user is admin
            if user.role != User.Role.ADMIN:
                return Response(
                    {"error": "Only admin users can invite team members"},
                    status=status.HTTP_403_FORBIDDEN
                )
            company = user.company
        else:
            # Fallback to company_id parameter
            company_id = data.get('companyId')
            if not company_id:
                return Response(
                    {"error": "companyId is required for unauthenticated requests"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            company = get_object_or_404(Company, id=company_id)
        
        if not company:
            return Response(
                {"error": "Company not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Default role and invited_by
        role = data.get('role', User.Role.USER)
        invited_by = user if user.is_authenticated else company.users.filter(role=User.Role.ADMIN).first()
        
        if not invited_by:
            return Response(
                {"error": "No admin user found to send invitation"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if invitation already exists for this email and company
        existing_invitation = TeamInvitation.objects.filter(
            email=data['email'],
            company=company,
            status__in=['PENDING', 'SENT']  # Only check active invitations
        ).first()
        
        if existing_invitation:
            return Response(
                {
                    "error": f"An invitation already exists for {data['email']}",
                    "details": {
                        "invitationId": str(existing_invitation.id),
                        "status": existing_invitation.status,
                        "expiresAt": existing_invitation.expires_at.isoformat() if existing_invitation.expires_at else None
                    }
                },
                status=status.HTTP_409_CONFLICT
            )
        
        # Create and send invitation
        invitation = InvitationService.invite_team_member(
            company=company,
            email=data['email'],
            role=role,
            invited_by=invited_by
        )
        
        return Response({
            "success": True,
            "message": f"Invitation sent to {data['email']}",
            "data": {
                "invitationId": str(invitation.id),
                "email": invitation.email,
                "role": invitation.role,
                "status": invitation.status,
                "expiresAt": invitation.expires_at.isoformat()
            }
        }, status=status.HTTP_201_CREATED)
        
    except ValueError as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.exception("Error inviting team member")
        return Response(
            {"error": f"Failed to invite team member: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def get_invitation_details(request, token):
    """
    Get invitation details by token
    GET /api/integrations/invitation/{token}/
    """
    try:
        invitation = get_object_or_404(
            TeamInvitation,
            token=token,
            status=TeamInvitation.Status.PENDING
        )
        
        # Check if invitation is expired
        if invitation.is_expired():
            invitation.status = TeamInvitation.Status.EXPIRED
            invitation.save()
            return Response(
                {"error": "Invitation has expired"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response({
            "success": True,
            "data": {
                "email": invitation.email,
                "companyName": invitation.company.name.title(),
                "role": invitation.get_role_display(),
                "inviterName": f"{invitation.invited_by.first_name} {invitation.invited_by.last_name}",
                "expiresAt": invitation.expires_at.isoformat()
            }
        })
        
    except Exception as e:
        logger.exception("Error getting invitation details")
        return Response(
            {"error": "Invalid or expired invitation"},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def accept_invitation(request, token):
    """
    Accept team invitation and create user account
    POST /api/integrations/invitation/{token}/accept/
    
    Body:
    {
        "firstName": "John",
        "lastName": "Doe", 
        "password": "secure_password"
    }
    """
    try:
        data = request.data
        
        # Validate required fields
        required_fields = ['firstName', 'lastName', 'password']
        for field in required_fields:
            if not data.get(field):
                return Response(
                    {"error": f"{field} is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Accept invitation
        user = InvitationService.accept_invitation(
            token=token,
            first_name=data['firstName'],
            last_name=data['lastName'],
            password=data['password']
        )
        
        return Response({
            "success": True,
            "message": "Invitation accepted successfully",
            "data": {
                "userId": str(user.id),
                "email": user.email,
                "name": f"{user.first_name} {user.last_name}",
                "role": user.role,
                "company": {
                    "id": str(user.company.id),
                    "name": user.company.name.title()
                }
            }
        }, status=status.HTTP_201_CREATED)
        
    except ValueError as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.exception("Error accepting invitation")
        return Response(
            {"error": f"Failed to accept invitation: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET', 'DELETE'])
@permission_classes([AllowAny])
def invitations_management(request):
    """
    Manage company invitations (admin only)
    GET /api/integrations/invitations/ - List all invitations
    DELETE /api/integrations/invitations/ - Delete a specific invitation
    
    For DELETE, include invitation_id in request body:
    {
        "invitation_id": "uuid",
        "companyId": "uuid"  # optional if authenticated
    }
    """
    try:
        # Get user and company
        user = get_user_from_token(request) or request.user
        company = None
        
        if user and user.is_authenticated:
            if user.role != User.Role.ADMIN:
                return Response(
                    {"error": "Only admin users can view invitations"},
                    status=status.HTTP_403_FORBIDDEN
                )
            company = user.company
        else:
            company_id = request.GET.get('companyId')
            if not company_id:
                return Response(
                    {"error": "companyId is required for unauthenticated requests"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            company = get_object_or_404(Company, id=company_id)
        
        if not company:
            return Response(
                {"error": "Company not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Handle DELETE request
        if request.method == 'DELETE':
            invitation_id = request.data.get('invitation_id')
            if not invitation_id:
                return Response(
                    {"error": "invitation_id is required for DELETE requests"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get the invitation
            invitation = get_object_or_404(TeamInvitation, id=invitation_id, company=company)
            
            # Check if invitation can be deleted
            if invitation.status == TeamInvitation.Status.ACCEPTED:
                return Response(
                    {"error": "Cannot delete an accepted invitation"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Store invitation info for response
            invitation_info = {
                "id": str(invitation.id),
                "email": invitation.email,
                "role": invitation.get_role_display(),
                "status": invitation.get_status_display()
            }
            
            # Delete the invitation
            invitation.delete()
            
            logger.info(f"Invitation deleted: {invitation_info['email']} for company {company.name}")
            
            return Response({
                "success": True,
                "message": f"Invitation for {invitation_info['email']} deleted successfully",
                "data": {
                    "deleted_invitation": invitation_info
                }
            }, status=status.HTTP_200_OK)
        
        # Handle GET request - List invitations
        invitations = TeamInvitation.objects.filter(company=company).order_by('-created_at')
        
        invitation_data = []
        for invitation in invitations:
            invitation_data.append({
                "id": str(invitation.id),
                "email": invitation.email,
                "role": invitation.get_role_display(),
                "status": invitation.get_status_display(),
                "inviterName": f"{invitation.invited_by.first_name} {invitation.invited_by.last_name}",
                "createdAt": invitation.created_at.isoformat(),
                "expiresAt": invitation.expires_at.isoformat(),
                "acceptedAt": invitation.accepted_at.isoformat() if invitation.accepted_at else None,
                "isExpired": invitation.is_expired()
            })
        
        return Response({
            "success": True,
            "data": invitation_data
        })
        
    except Exception as e:
        logger.exception("Error listing company invitations")
        return Response(
            {"error": f"Failed to list invitations: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Supabase-specific functions removed - use Django native auth endpoints instead:
# - POST /api/integrations/auth/create-company-admin/
# - POST /api/integrations/auth/accept-invitation/