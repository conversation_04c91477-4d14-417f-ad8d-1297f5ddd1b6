from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
import uuid
import logging
from django.utils import timezone
from datetime import timedelta
import secrets
from django.db.models.signals import post_delete
from django.dispatch import receiver

logger = logging.getLogger(__name__)


from django.contrib.postgres.fields import ArrayField


class UserManager(BaseUserManager):
    """Custom user manager for email-based authentication"""
    
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        
        
        try:
            from knowledge_spaces_Q_A.models import Knowledge_Space
            import random
            
            # Generate a unique color for the user's knowledge space
            colors = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4", "#84CC16", "#F97316"]
            color = random.choice(colors)
            
            # Create private knowledge space
            knowledge_space = Knowledge_Space.objects.create(
                name= str(user.id),
                user=user,
                company=None,  # User-owned, not company-owned
                color=color,
                initial=user.first_name[0].upper() if user.first_name else "U"
            )
            
            logger.info(f"✅ Created user-owned knowledge space for {user.email}: {knowledge_space.name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to create user knowledge space for {user.email}: {str(e)}")
            # Don't raise the exception - we don't want to prevent user creation if knowledge space creation fails
        return user
    
    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
        
        return self.create_user(email, password, **extra_fields)


class Company(models.Model):
    """Simple company model"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True) #shall be lowercase
    slug = models.SlugField(max_length=255, unique=True)
    email = models.EmailField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name_plural = 'Companies'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def delete(self, *args, **kwargs):
        """
        Override delete to ensure all related data is properly cleaned up
        """
        logger.info(f"🗑️ Deleting company '{self.name}' and all related data...")
        
        # Delete related data in the correct order to avoid foreign key constraint issues
        
        # 1. Delete team invitations (they reference users)
        invitations_deleted = self.invitations.all().delete()
        logger.info(f"   Deleted {invitations_deleted[0]} team invitations")
        
        # 2. Delete monitored items
        monitored_items_deleted = self.monitored_items.all().delete()
        logger.info(f"   Deleted {monitored_items_deleted[0]} monitored items")
        
        # 3. Delete company integrations
        integrations_deleted = self.integrations.all().delete()
        logger.info(f"   Deleted {integrations_deleted[0]} company integrations")
        
        # 4. Delete project contributors (they reference users and projects)
        # This will be handled by the Project model's cascade delete
        
        # 5. Delete project metrics (they reference projects)
        # This will be handled by the Project model's cascade delete
        
        # 6. Delete knowledge spaces (they reference company)
        try:
            from knowledge_spaces_Q_A.models import Knowledge_Space
            knowledge_spaces_deleted = Knowledge_Space.objects.filter(company=self).delete()
            logger.info(f"   Deleted {knowledge_spaces_deleted[0]} knowledge spaces")
        except Exception as e:
            logger.warning(f"   Warning: Could not delete knowledge spaces: {e}")
        
        # 7. Delete projects (they reference company)
        try:
            from knowledge_map.models import Project
            projects_deleted = Project.objects.filter(company=self).delete()
            logger.info(f"   Deleted {projects_deleted[0]} projects")
        except Exception as e:
            logger.warning(f"   Warning: Could not delete projects: {e}")
        
        # 8. Delete users (they reference company)
        users_deleted = self.users.all().delete()
        logger.info(f"   Deleted {users_deleted[0]} users")
        
        # 9. Finally delete the company itself
        logger.info(f"✅ Successfully deleted company '{self.name}' and all related data")
        super().delete(*args, **kwargs)
    


class User(AbstractUser):
    """Simple user model"""
    class Role(models.TextChoices):
        ADMIN = 'ADMIN', 'Admin'
        USER = 'USER', 'User'
    
    username = None
    email = models.EmailField(unique=True)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='users', null=True, blank=True)
    role = models.CharField(max_length=10, choices=Role.choices, default=Role.USER)
    phone = models.CharField(max_length=20, blank=True, null=True)
    
    # Use custom manager
    objects = UserManager()
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']
    
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"
    
    def save(self, *args, **kwargs):
        """
        Override save to create user-owned knowledge space for new users
        """
        # Check if this is a new user being created
        is_new_user = self.pk is None
        
        # Save the user first
        super().save(*args, **kwargs)
        
        # Create knowledge space for new users
        if is_new_user:
            logger.info(f"🔄 New user created: {self.email} (role: {self.role})")
            
            try:
                from knowledge_spaces_Q_A.models import Knowledge_Space
                import random
                
                # Check if user already has a knowledge space
                existing_knowledge_space = Knowledge_Space.objects.filter(user=self).first()
                
                if not existing_knowledge_space:
                    # Generate a unique color for the knowledge space
                    colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16']
                    color = random.choice(colors)
                    
                    # Create the default knowledge space
                    knowledge_space = Knowledge_Space.objects.create(
                        name=str(self.id),
                        company=None,  # User-owned, not company-owned
                        color=color,
                        initial=self.first_name[0].upper() if self.first_name else "U"
                    )
                    
                    logger.info(f"✅ Created user-owned knowledge space for {self.email}: {knowledge_space.name}")
                else:
                    logger.info(f"📋 User {self.email} already has a knowledge space: {existing_knowledge_space.name}")
                    
            except Exception as e:
                logger.error(f"❌ Failed to create user knowledge space for {self.email}: {str(e)}")
                # Don't raise the exception - we don't want to prevent user creation if knowledge space creation fails
        
       





class IntegrationTool(models.Model):
    """Simple integration tool model"""
    class Category(models.TextChoices):
        COMMUNICATION = 'COMMUNICATION', 'Communication'
        PROJECT_MANAGEMENT = 'PROJECT_MANAGEMENT', 'Project Management'
        VERSION_CONTROL = 'VERSION_CONTROL', 'Version Control'
        OTHER = 'OTHER', 'Other'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    category = models.CharField(max_length=30, choices=Category.choices)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['category', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"


class CompanyIntegration(models.Model):
    """Simple company integration model"""
    class Status(models.TextChoices):
        DISCONNECTED = 'DISCONNECTED', 'Disconnected'
        CONNECTED = 'CONNECTED', 'Connected'
        ERROR = 'ERROR', 'Error'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='integrations')
    tool = models.ForeignKey(IntegrationTool, on_delete=models.CASCADE, related_name='company_integrations')
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.DISCONNECTED)
    is_active = models.BooleanField(default=True)
    connected_at = models.DateTimeField(blank=True, null=True)
    config = models.JSONField(blank=True, null=True, default=dict)  # Generic config for integration-specific data
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['company', 'tool']
        ordering = ['-connected_at', 'tool__name']
    
    def __str__(self):
        return f"{self.company.name} - {self.tool.name} ({self.get_status_display()})"

    def get_valid_github_token(self) -> str:
        """
        Get a valid GitHub access token for this integration, refreshing if necessary.
        
        Returns:
            Valid access token
            
        Raises:
            Exception: If token cannot be refreshed or integration is not GitHub
        """
        if self.tool.slug != "github":
            raise Exception("This integration is not a GitHub integration")
        
        if not self.config:
            raise Exception("No configuration found for GitHub integration")
        
        try:
            from integrations.github.services import get_valid_github_token
            valid_token = get_valid_github_token(self.config)
            
            # Save the updated config if tokens were refreshed
            if self.config.get("last_token_refresh"):
                self.save()
                logger.debug(f"Updated GitHub integration config for company {self.company.name}")
            
            return valid_token
        except Exception as e:
            logger.error(f"Failed to get valid GitHub token for company {self.company.name}: {e}")
            raise


class MonitoredItem(models.Model):
    """Monitored URL or GitHub repo, scoped to company"""
    class Type(models.TextChoices):
        URL = 'url', 'URL'
        GITHUB_REPO = 'github_repo', 'GitHub Repo'
        SLACK_CHANNEL = 'slack_channel', 'Slack Channel'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='monitored_items')
    type = models.CharField(max_length=20, choices=Type.choices)
    name = models.CharField(max_length=255)
    url = models.URLField(blank=True, null=True)
    repo = models.CharField(max_length=255, blank=True, null=True)  # For github_repo type
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.type}) for {self.company.name}"


class SlackUserProfile(models.Model):
    """Slack messaging profile for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='slack_profile')
    slack_user_id = models.CharField(max_length=100)
    preferred_channel_id = models.CharField(max_length=100, blank=True, null=True)
    bot_token = models.TextField()  # Store encrypted
    user_token = models.TextField(blank=True, null=True)  # Store encrypted
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Slack profile for {self.user.email}"


class TeamsUserProfile(models.Model):
    """Microsoft Teams messaging profile for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='teams_profile')
    teams_user_id = models.CharField(max_length=100)
    tenant_id = models.CharField(max_length=100)
    chat_id = models.CharField(max_length=100, blank=True, null=True)
    access_token = models.TextField()  # Store encrypted
    refresh_token = models.TextField(blank=True, null=True)  # Store encrypted
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Teams profile for {self.user.email}"


class DiscordUserProfile(models.Model):
    """Discord messaging profile for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='discord_profile')
    discord_user_id = models.CharField(max_length=100)
    guild_id = models.CharField(max_length=100, blank=True, null=True)
    channel_id = models.CharField(max_length=100, blank=True, null=True)
    bot_token = models.TextField()  # Store encrypted
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Discord profile for {self.user.email}"


class AtlassianUserProfile(models.Model):
    """Atlassian (Confluence/Jira) profile for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='atlassian_profile')
    account_id = models.CharField(max_length=100, help_text="Atlassian account ID")
    email = models.EmailField(help_text="Atlassian account email")
    display_name = models.CharField(max_length=255, blank=True, null=True, help_text="User's display name")
    picture = models.URLField(blank=True, null=True, help_text="Profile picture URL")
    nickname = models.CharField(max_length=100, blank=True, null=True, help_text="User's nickname")
    domain = models.CharField(max_length=255, blank=True, null=True, help_text="Atlassian domain/site")
    cloud_id = models.CharField(max_length=100, blank=True, null=True, help_text="Confluence cloud ID")
    confluence_url = models.URLField(blank=True, null=True, help_text="Confluence instance URL")
    access_token = models.TextField(help_text="OAuth access token (encrypted)")
    refresh_token = models.TextField(blank=True, null=True, help_text="OAuth refresh token (encrypted)")
    token_expires_at = models.DateTimeField(blank=True, null=True, help_text="Access token expiration time")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Atlassian profile for {self.user.email} ({self.display_name or 'No display name'})"


class TeamInvitation(models.Model):
    """Model for team member invitations"""
    class Status(models.TextChoices):
        PENDING = 'PENDING', 'Pending'
        ACCEPTED = 'ACCEPTED', 'Accepted'
        EXPIRED = 'EXPIRED', 'Expired'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='invitations')
    email = models.EmailField()
    role = models.CharField(max_length=10, choices=User.Role.choices, default=User.Role.USER)
    token = models.CharField(max_length=64, unique=True)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.PENDING)
    invited_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='sent_invitations')
    
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    accepted_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['company', 'email']
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.token:
            self.token = secrets.token_urlsafe(32)
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(days=7)  # 7 days expiry
        super().save(*args, **kwargs)
    
    def is_expired(self):
        return timezone.now() > self.expires_at or self.status == self.Status.EXPIRED
    
    def __str__(self):
        return f"Invitation for {self.email} to join {self.company.name} ({self.status})"


class CrossRepoMonitor(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='cross_repo_monitors')
    internal_repo = models.CharField(max_length=255)
    external_repos = ArrayField(models.CharField(max_length=255))
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.company} | Internal: {self.internal_repo} | Externals: {', '.join(self.external_repos)}"


class RepoChange(models.Model):
    """Tracks changes detected in monitored repositories, for approval and review."""
    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("reviewed", "Reviewed"),
    ]
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name="repo_changes")
    internal_repo = models.CharField(max_length=255)
    external_repo = models.CharField(max_length=255)
    cross_repo_monitor = models.ForeignKey('CrossRepoMonitor', on_delete=models.CASCADE, null=True, blank=True, related_name="repo_changes")
    commit_id = models.CharField(max_length=100, blank=True, null=True)
    summary = models.TextField()
    details = models.JSONField(default=dict, blank=True)  # structured change info
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="approved_repo_changes")
    approved_at = models.DateTimeField(null=True, blank=True)
    likes = models.ManyToManyField(User, related_name="liked_repo_changes", blank=True)
    dislikes = models.ManyToManyField(User, related_name="disliked_repo_changes", blank=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.internal_repo} <- {self.external_repo} | {self.status} | {self.summary[:40]}..."


class NotificationSettings(models.Model):
    """Company-specific notification settings and frequency controls
    For slack,discord...notifications
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='notification_settings')
    
    # Frequency control
    frequency_hours = models.IntegerField(
        default=2, 
        help_text="Minimum hours between notifications (0 = no limit)"
    )
    enabled = models.BooleanField(
        default=True,
        help_text="Whether notifications are enabled for this company"
    )
    
    # Simple tracking of last notification time
    last_notification_time = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = 'Notification Settings'
        ordering = ['company__name']
    
    def __str__(self):
        return f"{self.company.name} - {self.frequency_hours}h frequency"


@receiver(post_delete, sender=CompanyIntegration)
def delete_cross_repo_monitors_on_github_integration_delete(sender, instance, **kwargs):
    """
    Signal handler to delete cross repo monitors when a GitHub integration is deleted.
    This ensures cross repo monitors are cleaned up regardless of how the integration is deleted.
    """
    if instance.tool.slug == "github":
        cross_repo_monitors_deleted = CrossRepoMonitor.objects.filter(company=instance.company).delete()
        logger.info(f"[Signal Handler] Deleted {cross_repo_monitors_deleted[0]} cross repo monitors for company {instance.company.id} after GitHub integration deletion")


class ActiveSearchPlatforms(models.Model):
    """Model to store active search platforms for each user"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='active_search_platforms')
    platforms = ArrayField(
        models.CharField(max_length=100),
        default=list,
        blank=True,
        help_text="List of active search platform names for this user"
    )
    
    class Meta:
        verbose_name_plural = 'Active Search Platforms'
        ordering = ['user__email']
    
    def __str__(self):
        return f"{self.user.email} - {len(self.platforms)} platforms: {', '.join(self.platforms)}"
