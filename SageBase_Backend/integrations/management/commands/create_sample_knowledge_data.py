from django.core.management.base import BaseCommand
from django.utils.text import slugify
from integrations.models import Company, User, Project, ProjectContributor, ProjectMetrics
from django.utils import timezone
import uuid
import random


class Command(BaseCommand):
    help = 'Create sample data for knowledge map testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company',
            type=str,
            help='Company name to create sample data for',
            default='Sample Tech Company'
        )

    def handle(self, *args, **options):
        company_name = options['company']
        
        # Create or get company
        company, created = Company.objects.get_or_create(
            name=company_name,
            defaults={
                'slug': slugify(company_name),
                'email': f'admin@{slugify(company_name)}.com'
            }
        )
        
        if created:
            self.stdout.write(f'Created company: {company.name}')
        else:
            self.stdout.write(f'Using existing company: {company.name}')
        
        # Create sample users
        users_data = [
            {'first_name': '<PERSON>', 'last_name': '<PERSON>', 'email': '<EMAIL>', 'role': 'ADMIN'},
            {'first_name': '<PERSON>', 'last_name': '<PERSON>', 'email': '<EMAIL>', 'role': 'USER'},
            {'first_name': 'Emma', 'last_name': 'Davis', 'email': '<EMAIL>', 'role': 'USER'},
            {'first_name': 'Alex', 'last_name': 'Rodriguez', 'email': '<EMAIL>', 'role': 'USER'},
            {'first_name': 'Lisa', 'last_name': 'Wang', 'email': '<EMAIL>', 'role': 'USER'},
        ]
        
        users = []
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                email=user_data['email'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'company': company,
                    'role': user_data['role']
                }
            )
            users.append(user)
            if created:
                self.stdout.write(f'Created user: {user.get_full_name()}')
        
        # Create sample projects
        projects_data = [
            {
                'name': 'Atlas Documentation',
                'description': 'Comprehensive documentation for the Atlas platform',
                'categories': ['Documentation', 'API', 'Architecture'],
                'tags': ['important', 'public-facing', 'well-documented'],
                'repo_path': 'https://github.com/company/atlas-platform',
                'docs_path': 'https://docs.company.com/atlas',
                'repo_type': 'github'
            },
            {
                'name': 'Phoenix Migration',
                'description': 'Migration from legacy system to new Phoenix architecture',
                'categories': ['Migration', 'Backend', 'Database'],
                'tags': ['high-priority', 'complex', 'legacy-replacement'],
                'repo_path': 'https://github.com/company/phoenix-migration',
                'docs_path': 'https://phoenix.docs.company.com',
                'repo_type': 'github'
            },
            {
                'name': 'Quantum Analytics',
                'description': 'Advanced analytics platform with real-time data processing',
                'categories': ['Analytics', 'Real-time', 'Visualization'],
                'tags': ['machine-learning', 'real-time', 'data-intensive'],
                'repo_path': 'https://github.com/company/quantum-analytics',
                'docs_path': 'https://quantum.docs.company.com',
                'repo_type': 'github'
            },
            {
                'name': 'Mobile App SDK',
                'description': 'SDK for mobile applications with cross-platform support',
                'categories': ['Mobile', 'SDK', 'Cross-platform'],
                'tags': ['client-facing', 'stable', 'cross-platform'],
                'repo_path': 'https://github.com/company/mobile-sdk',
                'docs_path': 'https://mobile.docs.company.com',
                'repo_type': 'github'
            },
            {
                'name': 'Security Framework',
                'description': 'Enterprise security framework with OAuth2 and JWT support',
                'categories': ['Security', 'Authentication', 'Framework'],
                'tags': ['critical', 'security', 'enterprise'],
                'repo_path': 'https://github.com/company/security-framework',
                'docs_path': 'https://security.docs.company.com',
                'repo_type': 'github'
            }
        ]
        
        projects = []
        for project_data in projects_data:
            project, created = Project.objects.get_or_create(
                name=project_data['name'],
                company=company,
                defaults={
                    'slug': slugify(project_data['name']),
                    'description': project_data['description'],
                    'categories': project_data['categories'],
                    'tags': project_data['tags'],
                    'repo_path': project_data['repo_path'],
                    'docs_path': project_data['docs_path'],
                    'repo_type': project_data['repo_type'],
                    'doc_responsible': random.choice(users),
                    'secondary_responsible': random.choice(users),
                    'total_contributors': random.randint(3, 12)
                }
            )
            projects.append(project)
            if created:
                self.stdout.write(f'Created project: {project.name}')
                
                # Create project metrics
                ProjectMetrics.objects.create(
                    project=project,
                    health_score=random.randint(60, 95),
                    risk_level=random.choice(['Low', 'Medium', 'High']),
                    documentation_coverage=random.randint(40, 98),
                    active_contributors=random.randint(3, 10)
                )
                
                # Create contributors for each project
                project_users = random.sample(users, k=random.randint(3, len(users)))
                for user in project_users:
                    ProjectContributor.objects.create(
                        project=project,
                        user=user,
                        commits=random.randint(5, 150),
                        reviews=random.randint(1, 50),
                        docs=random.randint(0, 25),
                        contributions=random.randint(10, 200)
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample data for {company.name}:\n'
                f'- {len(users)} users\n'
                f'- {len(projects)} projects\n'
                f'- Project metrics and contributors'
            )
        )
        
        # Display some sample API endpoints
        self.stdout.write('\n' + '='*50)
        self.stdout.write('Sample API endpoints to test:')
        self.stdout.write('='*50)
        self.stdout.write('GET /api/integrations/projects/')
        self.stdout.write('GET /api/integrations/team-members/')
        self.stdout.write('GET /api/integrations/projects/search?q=atlas')
        self.stdout.write('GET /api/integrations/projects/search?tags=important,critical')
        self.stdout.write('GET /api/integrations/projects/search?categories=Security&tags=enterprise')
        self.stdout.write('GET /api/integrations/projects/metrics/')
        for project in projects[:2]:
            self.stdout.write(f'GET /api/integrations/projects/{project.id}/')
            self.stdout.write(f'GET /api/integrations/projects/{project.id}/contributors/')
            self.stdout.write(f'GET /api/integrations/projects/{project.id}/metrics/')
        self.stdout.write('='*50)
        
        # Display assignment examples
        self.stdout.write('\nDocumentation Assignment Examples:')
        self.stdout.write('='*50)
        sample_project = projects[0]
        sample_user = users[0]
        self.stdout.write(f'POST /api/integrations/projects/{sample_project.id}/assign-documentation/')
        self.stdout.write('Body: {"memberId": "' + str(sample_user.id) + '", "type": "main"}')
        self.stdout.write('Body: {"memberId": "' + str(sample_user.id) + '", "type": "secondary"}')
        self.stdout.write('Body: {"memberId": null, "type": "main"}  # Remove assignment')
        self.stdout.write('Body: {"memberId": "", "type": "secondary"}  # Remove assignment')
        self.stdout.write('='*50)