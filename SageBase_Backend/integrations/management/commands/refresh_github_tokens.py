from django.core.management.base import BaseCommand
from django.conf import settings
from integrations.models import CompanyIntegration, IntegrationTool
from integrations.github.services import get_valid_github_token, validate_github_token
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Refresh expired GitHub tokens for all companies'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=str,
            help='Refresh tokens for a specific company ID',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be refreshed without actually refreshing',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force refresh even if token appears valid',
        )

    def handle(self, *args, **options):
        company_id = options.get('company_id')
        dry_run = options.get('dry_run')
        force = options.get('force')

        # Get GitHub tool
        try:
            github_tool = IntegrationTool.objects.get(slug="github")
        except IntegrationTool.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('GitHub integration tool not found. Please ensure GitHub integration is set up.')
            )
            return

        # Get GitHub integrations
        integrations = CompanyIntegration.objects.filter(
            tool=github_tool,
            is_active=True
        )

        if company_id:
            integrations = integrations.filter(company_id=company_id)

        if not integrations.exists():
            self.stdout.write(
                self.style.WARNING('No active GitHub integrations found.')
            )
            return

        self.stdout.write(f"Found {integrations.count()} GitHub integration(s)")

        refreshed_count = 0
        error_count = 0

        for integration in integrations:
            company_name = integration.company.name
            self.stdout.write(f"\nProcessing company: {company_name}")

            if not integration.config:
                self.stdout.write(
                    self.style.WARNING(f"  No configuration found for {company_name}")
                )
                continue

            ghu_token = integration.config.get("ghu_token")
            ghr_token = integration.config.get("ghr_token")

            if not ghu_token:
                self.stdout.write(
                    self.style.WARNING(f"  No access token found for {company_name}")
                )
                continue

            if not ghr_token:
                self.stdout.write(
                    self.style.WARNING(f"  No refresh token found for {company_name}")
                )
                continue

            # Check if token is valid
            is_valid = validate_github_token(ghu_token)
            
            if is_valid and not force:
                self.stdout.write(
                    self.style.SUCCESS(f"  Token is valid for {company_name}")
                )
                continue

            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f"  Would refresh token for {company_name}")
                )
                continue

            try:
                # Attempt to refresh the token
                valid_token = get_valid_github_token(integration.config)
                
                # Save the updated integration
                integration.save()
                
                self.stdout.write(
                    self.style.SUCCESS(f"  Successfully refreshed token for {company_name}")
                )
                refreshed_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"  Failed to refresh token for {company_name}: {e}")
                )
                error_count += 1

        # Summary
        self.stdout.write(f"\n{'='*50}")
        self.stdout.write(f"Summary:")
        self.stdout.write(f"  Total integrations: {integrations.count()}")
        self.stdout.write(f"  Refreshed: {refreshed_count}")
        self.stdout.write(f"  Errors: {error_count}")
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING("  This was a dry run - no tokens were actually refreshed")
            ) 