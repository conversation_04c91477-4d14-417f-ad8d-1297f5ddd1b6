"""
Django management command to test the auto-async ORM functionality.
This command demonstrates that the same ORM code works in both sync and async contexts.
"""

import asyncio
from django.core.management.base import BaseCommand
from integrations.models import User, Company, SlackUserProfile


class Command(BaseCommand):
    help = 'Test the auto-async ORM functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-async',
            action='store_true',
            help='Test async context as well',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Testing Auto-Async ORM Functionality')
        )
        
        # Test sync context (management commands run in sync context)
        self.test_sync_context()
        
        if options['test_async']:
            # Test async context
            asyncio.run(self.test_async_context())
        
        self.stdout.write(
            self.style.SUCCESS('✅ Auto-Async ORM tests completed!')
        )

    def test_sync_context(self):
        """Test ORM operations in sync context (no await needed)"""
        self.stdout.write("📝 Testing sync context...")
        
        try:
            # These operations work without await in sync context
            company_count = Company.objects.count()
            user_count = User.objects.count()
            profile_count = SlackUserProfile.objects.count()
            
            self.stdout.write(f"   Companies: {company_count}")
            self.stdout.write(f"   Users: {user_count}")  
            self.stdout.write(f"   Slack Profiles: {profile_count}")
            
            # Test filtering
            active_users = User.objects.filter(is_active=True)
            self.stdout.write(f"   Active Users: {active_users.count()}")
            
            # Test getting first company
            first_company = Company.objects.first()
            if first_company:
                self.stdout.write(f"   First Company: {first_company.name}")
                
                # Test related queries
                company_users = User.objects.filter(company=first_company)
                self.stdout.write(f"   Users in {first_company.name}: {company_users.count()}")
            
            self.stdout.write(self.style.SUCCESS("   ✅ Sync context tests passed!"))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"   ❌ Sync context test failed: {e}")
            )

    async def test_async_context(self):
        """Test ORM operations in async context (await required)"""
        self.stdout.write("📝 Testing async context...")
        
        try:
            # Same operations, but with await in async context
            company_count = await Company.objects.count()
            user_count = await User.objects.count()
            profile_count = await SlackUserProfile.objects.count()
            
            self.stdout.write(f"   Companies: {company_count}")
            self.stdout.write(f"   Users: {user_count}")
            self.stdout.write(f"   Slack Profiles: {profile_count}")
            
            # Test filtering
            active_users = await User.objects.filter(is_active=True)
            active_count = await active_users.count()
            self.stdout.write(f"   Active Users: {active_count}")
            
            # Test getting first company
            first_company = await Company.objects.first()
            if first_company:
                self.stdout.write(f"   First Company: {first_company.name}")
                
                # Test related queries
                company_users = await User.objects.filter(company=first_company)
                company_user_count = await company_users.count()
                self.stdout.write(f"   Users in {first_company.name}: {company_user_count}")
            
            self.stdout.write(self.style.SUCCESS("   ✅ Async context tests passed!"))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"   ❌ Async context test failed: {e}")
            )

    def test_mixed_function(self):
        """Test a function that works in both contexts"""
        def get_company_info(company_id):
            """This function works in both sync and async contexts!"""
            try:
                company = Company.objects.get(id=company_id)
                user_count = User.objects.filter(company=company).count()
                return {
                    'name': company.name,
                    'user_count': user_count
                }
            except Company.DoesNotExist:
                return None
        
        # Test in sync context
        self.stdout.write("📝 Testing mixed function in sync context...")
        first_company = Company.objects.first()
        if first_company:
            info = get_company_info(first_company.id)
            if info:
                self.stdout.write(f"   Company: {info['name']} ({info['user_count']} users)")
        
        # Test in async context would require await:
        # info = await get_company_info(first_company.id) 