import time
import requests
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

def get_contributor_stats_since(owner: str, repo: str, since_date: datetime, headers: dict) -> dict:
    """Get detailed contributor stats including lines of code changes since a specific date"""
    try:
        # Use GitHub's stats/contributors API
        stats_url = f"https://api.github.com/repos/{owner}/{repo}/stats/contributors"
        
        # Poll until stats are ready (GitHub generates these asynchronously)
        for attempt in range(10):
            r = requests.get(stats_url, headers=headers, timeout=30)
            if r.status_code == 202:
                logger.info(f"Stats not ready for {owner}/{repo}, retrying in 2 seconds... (attempt {attempt + 1}/10)")
                time.sleep(2)
                continue
            elif r.status_code >= 300:
                logger.warning(f"GitHub API error {r.status_code} for stats: {r.text[:200]}")
                return {}
            else:
                break
        else:
            logger.warning(f"Stats not ready for {owner}/{repo} after 10 attempts")
            return {}
        
        data = r.json() or []
        since_timestamp = since_date.timestamp()
        
        per_contributor = {}
        
        for author in data:
            if not author.get("author"):
                continue
                
            login = author["author"]["login"]
            additions = 0
            deletions = 0
            commits = 0
            
            # Filter weeks by the since_date
            for week in author["weeks"]:
                week_timestamp = week["w"]  # Week timestamp
                if week_timestamp >= since_timestamp:
                    additions += week["a"]  # Additions
                    deletions += week["d"]  # Deletions
                    commits += week["c"]    # Commits
            
            # Only include contributors with activity in the time period
            if additions > 0 or deletions > 0 or commits > 0:
                per_contributor[login] = {
                    "additions": additions,
                    "deletions": deletions,
                    "edited_lines": additions + deletions,
                    "net_change": additions - deletions,
                    "commits": commits,
                }
        
        logger.info(f"Fetched stats for {len(per_contributor)} active contributors in {owner}/{repo}")
        return per_contributor
        
    except Exception as e:
        logger.warning(f"Error fetching contributor stats for {owner}/{repo}: {e}")
        return {}

def get_contributor_commits_since(owner: str, repo: str, username: str, since_date: datetime, headers: dict) -> int:
    """Get commit count for a contributor since a specific date"""
    try:
        # Use GitHub's commits API with since parameter
        commits_api = f"https://api.github.com/repos/{owner}/{repo}/commits"
        params = {
            'author': username,
            'since': since_date.isoformat() + 'Z'
        }
        r = requests.get(commits_api, headers=headers, params=params, timeout=30)
        if r.status_code < 300:
            commits = r.json() or []
            return len(commits)
    except Exception as e:
        logger.warning(f"Error fetching commits for {username}: {e}")
    return 0

def get_contributor_prs_since(owner: str, repo: str, username: str, since_date: datetime, headers: dict) -> int:
    """Get PR count for a contributor since a specific date"""
    try:
        # Use GitHub's search API with time filtering
        since_str = since_date.strftime('%Y-%m-%d')
        search_query = f"repo:{owner}/{repo} type:pr author:{username} created:>={since_str}"
        search_api = f"https://api.github.com/search/issues?q={search_query}"
        r = requests.get(search_api, headers=headers, timeout=30)
        if r.status_code < 300:
            result = r.json() or {}
            return result.get('total_count', 0)
    except Exception as e:
        logger.warning(f"Error fetching PRs for {username}: {e}")
    return 0

def get_contributor_reviews_since(owner: str, repo: str, username: str, since_date: datetime, headers: dict) -> int:
    """Get review count for a contributor since a specific date"""
    try:
        # Use GitHub's search API for reviews
        since_str = since_date.strftime('%Y-%m-%d')
        search_query = f"repo:{owner}/{repo} type:pr reviewed-by:{username} updated:>={since_str}"
        search_api = f"https://api.github.com/search/issues?q={search_query}"
        r = requests.get(search_api, headers=headers, timeout=30)
        if r.status_code < 300:
            result = r.json() or {}
            return result.get('total_count', 0)
    except Exception as e:
        logger.warning(f"Error fetching reviews for {username}: {e}")
    return 0

def cleanup_duplicate_contributors():
    """Clean up any duplicate ProjectContributors records"""
    try:
        from knowledge_map.models import ProjectContributors
        from django.db.models import Count
        
        # Find projects with multiple contributor records
        duplicates = ProjectContributors.objects.values('project').annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        cleaned_count = 0
        for dup in duplicates:
            project_id = dup['project']
            records = ProjectContributors.objects.filter(project_id=project_id).order_by('-last_fetched')
            
            # Keep the most recently updated record, delete others
            if records.count() > 1:
                latest_record = records.first()
                records.exclude(id=latest_record.id).delete()
                cleaned_count += records.count() - 1
                logger.info(f"Cleaned up {records.count() - 1} duplicate records for project {project_id}")
        
        if cleaned_count > 0:
            logger.info(f"Total duplicate records cleaned: {cleaned_count}")
        else:
            logger.info("No duplicate records found")
            
        return cleaned_count
        
    except Exception as e:
        logger.error(f"Error cleaning up duplicate contributors: {e}")
        return 0


def update_project_contributors(project, since_days: int = 30):
    """Update project contributors with fresh GitHub data"""
    try:
        # Only handle GitHub projects for now
        if not hasattr(project, 'repository_url') or not project.repository_url:
            logger.info(f"Skipping project {project.id} - no repository URL")
            return

        # Extract owner/repo from repository URL
        repo_parts = project.repository_url.rstrip('/').split('/')
        if len(repo_parts) < 2:
            logger.warning(f"Cannot infer owner/repo for project {project.id}")
            return
            
        owner = repo_parts[-2]
        repo = repo_parts[-1]

        # Get GitHub token from company integration
        company = project.company
        if not company:
            logger.warning(f"No company for project {project.id}")
            return
            
        from integrations.models import CompanyIntegration, IntegrationTool
        
        github_tool = IntegrationTool.objects.filter(slug="github").first()
        if not github_tool:
            logger.warning("GitHub integration tool not found")
            return
            
        github_integration = CompanyIntegration.objects.filter(
            company=company,
            tool=github_tool,
            is_active=True
        ).first()
        
        if not github_integration or not github_integration.config:
            logger.warning(f"No active GitHub integration for company {company.id}")
            return

        # Get GitHub token
        token = None
        try:
            token = github_integration.get_valid_github_token()
        except:
            token = github_integration.config.get('ghu_token')
            
        if not token:
            logger.warning(f"No GitHub token for company {company.id}")
            return

        headers = {
            'Authorization': f'Bearer {token}',
            'Accept': 'application/vnd.github+json',
            'X-GitHub-Api-Version': '2022-11-28',
            'User-Agent': 'SageBase-Fetcher'
        }

        # Calculate time window
        since_date = datetime.utcnow() - timedelta(days=since_days)
        since_iso = since_date.isoformat() + 'Z'
        logger.info(f"Fetching contributors for {owner}/{repo} since {since_iso} ({since_days} days ago)")

        # Get detailed contributor stats including lines of code changes
        contributor_stats = get_contributor_stats_since(owner, repo, since_date, headers)
        
        # Fetch contributors list (this gives us base contributor info)
        contributors_api = f"https://api.github.com/repos/{owner}/{repo}/contributors"
        r = requests.get(contributors_api, headers=headers, timeout=30)
        if r.status_code >= 300:
            logger.warning(f"GitHub API error {r.status_code} for {contributors_api}: {r.text[:200]}")
            return
        base_contributors = r.json() or []

        contributors_data = []

        for c in base_contributors:
            github_login = c.get('login')
            if not github_login:
                continue

            # Get stats for this contributor from our detailed stats
            stats = contributor_stats.get(github_login, {})
            
            # Get time-filtered commits for this contributor
            commits = get_contributor_commits_since(
                owner, repo, github_login, since_date, headers
            )

            # Get time-filtered PRs for this contributor
            prs_count = get_contributor_prs_since(
                owner, repo, github_login, since_date, headers
            )

            # Get time-filtered reviews for this contributor
            reviews_count = get_contributor_reviews_since(
                owner, repo, github_login, since_date, headers
            )

            # Only include contributors with activity in the time window
            if commits > 0 or prs_count > 0 or reviews_count > 0:
                # Use detailed stats if available, fallback to commit-based estimates
                additions = stats.get('additions', commits * 25)  # Fallback: ~25 lines per commit
                deletions = stats.get('deletions', commits * 15)  # Fallback: ~15 lines per commit
                edited_lines = stats.get('edited_lines', additions + deletions)
                net_change = stats.get('net_change', additions - deletions)
                
                # Calculate contribution score based on time-filtered data
                contribution_score = min(100, commits * 2 + prs_count * 3 + reviews_count * 2 + (edited_lines // 100))

                contributors_data.append({
                    'github_username': github_login,
                    'name': github_login,
                    'commits': commits,
                    'reviews': reviews_count,
                    'docs': 0,  # Documentation contributions would need separate logic
                    'contributions': contribution_score,
                    'last_contribution': since_date.isoformat() + 'Z',
                    'pullRequests': prs_count,
                    'linesOfCode': edited_lines,  # Total lines edited (additions + deletions)
                    'additions': additions,       # New lines added
                    'deletions': deletions,      # Lines deleted
                    'netChange': net_change,     # Net change (additions - deletions)
                    'email': f"{github_login}@github.com",
                    'role': 'Contributor'
                })

        # Update the project contributors record
        from knowledge_map.models import ProjectContributors
        
        # Always use get_or_create to ensure only one record per project
        record, created = ProjectContributors.objects.get_or_create(
            project=project,
            defaults={
                'contributors_data': [],
                'contributions_history': {}
            }
        )
        
        if created:
            logger.info(f"Created new ProjectContributors record for project {project.id}")
        else:
            logger.info(f"Using existing ProjectContributors record for project {project.id}")
        
        # Update the record with fresh data
        record.update_contributors_data(contributors_data)
        logger.info(f"Updated {len(contributors_data)} active contributors for project {project.id} in last {since_days} days")
        
        return contributors_data
        
    except Exception as e:
        logger.exception(f"Error updating project contributors for project {project.id}: {e}")
        return []


