"""
Smart ORM wrapper that automatically detects async context and handles sync/async transparently.
This allows you to write code once and have it work in both sync and async contexts.
"""

import asyncio
import inspect
import logging
from typing import Any, Callable, Union
from asgiref.sync import sync_to_async
from django.db import models

logger = logging.getLogger(__name__)


def is_async_context() -> bool:
    """
    Detect if we're currently in an async context.
    Returns True if called from within an async function or event loop.
    """
    try:
        # Check if there's a running event loop
        loop = asyncio.get_running_loop()
        return loop is not None
    except RuntimeError:
        # No event loop running, we're in sync context
        return False


class SmartQuerySet:
    """
    A wrapper around Django QuerySet that automatically handles sync/async based on context.
    """
    
    def __init__(self, queryset):
        self._queryset = queryset
        self._model = queryset.model
    
    def __getattr__(self, name):
        """Intercept all method calls and make them context-aware."""
        attr = getattr(self._queryset, name)
        
        if callable(attr):
            def smart_method(*args, **kwargs):
                # Check if we're in async context
                if is_async_context():
                    # Convert to async and return awaitable
                    async_func = sync_to_async(attr, thread_sensitive=False)
                    return async_func(*args, **kwargs)
                else:
                    # Call synchronously
                    return attr(*args, **kwargs)
            
            return smart_method
        else:
            return attr
    
    def __iter__(self):
        """Handle iteration - check context first."""
        if is_async_context():
            # In async context, this should be awaited
            raise RuntimeError("Cannot iterate over SmartQuerySet in async context. Use 'async for' or await list()")
        return iter(self._queryset)
    
    async def __aiter__(self):
        """Async iteration support."""
        items = await sync_to_async(list, thread_sensitive=False)(self._queryset)
        for item in items:
            yield item


class SmartManager(models.Manager):
    """
    A smart manager that returns SmartQuerySet instances.
    """
    
    def get_queryset(self):
        return SmartQuerySet(super().get_queryset())
    
    def __getattr__(self, name):
        """Intercept manager method calls."""
        attr = getattr(super(), name)
        
        if callable(attr):
            def smart_method(*args, **kwargs):
                if is_async_context():
                    async_func = sync_to_async(attr, thread_sensitive=False)
                    return async_func(*args, **kwargs)
                else:
                    return attr(*args, **kwargs)
            return smart_method
        else:
            return attr


class SmartModel(models.Model):
    """
    Base model class that provides context-aware database operations.
    """
    
    objects = SmartManager()
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """Context-aware save method."""
        if is_async_context():
            async_save = sync_to_async(super().save, thread_sensitive=False)
            return async_save(*args, **kwargs)
        else:
            return super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        """Context-aware delete method."""
        if is_async_context():
            async_delete = sync_to_async(super().delete, thread_sensitive=False)
            return async_delete(*args, **kwargs)
        else:
            return super().delete(*args, **kwargs)
    
    def refresh_from_db(self, *args, **kwargs):
        """Context-aware refresh method."""
        if is_async_context():
            async_refresh = sync_to_async(super().refresh_from_db, thread_sensitive=False)
            return async_refresh(*args, **kwargs)
        else:
            return super().refresh_from_db(*args, **kwargs)


# Utility function to make any existing model "smart"
def make_smart(model_class):
    """
    Convert an existing Django model to use smart context-aware operations.
    
    Usage:
        SmartUser = make_smart(User)
        
        # Now works in both contexts:
        user = SmartUser.objects.get(id=1)  # sync
        user = await SmartUser.objects.get(id=1)  # async
    """
    
    class SmartWrapper:
        def __init__(self):
            self._model = model_class
        
        def __getattr__(self, name):
            attr = getattr(self._model, name)
            
            if name == 'objects':
                # Return a smart manager
                original_manager = attr
                
                class ContextAwareManager:
                    def __getattr__(self, method_name):
                        method = getattr(original_manager, method_name)
                        
                        if callable(method):
                            def smart_method(*args, **kwargs):
                                if is_async_context():
                                    async_func = sync_to_async(method, thread_sensitive=False)
                                    return async_func(*args, **kwargs)
                                else:
                                    return method(*args, **kwargs)
                            return smart_method
                        else:
                            return method
                
                return ContextAwareManager()
            
            elif callable(attr):
                def smart_method(*args, **kwargs):
                    if is_async_context():
                        async_func = sync_to_async(attr, thread_sensitive=False)
                        return async_func(*args, **kwargs)
                    else:
                        return attr(*args, **kwargs)
                return smart_method
            else:
                return attr
    
    return SmartWrapper()


# Pre-made smart versions of common models
def create_smart_models():
    """Create smart versions of your existing models."""
    try:
        from .models import User, Company, SlackUserProfile, CompanyIntegration, IntegrationTool
        
        return {
            'User': make_smart(User),
            'Company': make_smart(Company),
            'SlackUserProfile': make_smart(SlackUserProfile),
            'CompanyIntegration': make_smart(CompanyIntegration),
            'IntegrationTool': make_smart(IntegrationTool),
        }
    except ImportError as e:
        logger.warning(f"Could not create smart models: {e}")
        return {}


# Global smart models - import these instead of the regular models
SMART_MODELS = create_smart_models()

# Easy access
SmartUser = SMART_MODELS.get('User')
SmartCompany = SMART_MODELS.get('Company')
SmartSlackUserProfile = SMART_MODELS.get('SlackUserProfile')
SmartCompanyIntegration = SMART_MODELS.get('CompanyIntegration')
SmartIntegrationTool = SMART_MODELS.get('IntegrationTool') 