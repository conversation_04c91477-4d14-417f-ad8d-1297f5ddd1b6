"""
Generic async utilities for converting synchronous functions to async.
This module provides decorators and utilities to avoid repetitive sync_to_async calls.
"""

import logging
import functools
from typing import Callable, Any
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


def make_async(thread_sensitive: bool = False):
    """
    Decorator to convert any sync function to async.
    
    Args:
        thread_sensitive: Whether the function needs thread-sensitive execution
        
    Usage:
        @make_async()
        def my_sync_function(arg1, arg2):
            return some_orm_operation()
        
        # Now you can use: await my_sync_function(arg1, arg2)
    """
    def decorator(func: Callable) -> Callable:
        async_func = sync_to_async(func, thread_sensitive=thread_sensitive)
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await async_func(*args, **kwargs)
        
        return wrapper
    return decorator


def to_async(func: Callable, thread_sensitive: bool = False) -> Callable:
    """
    Convert a sync function to async on-the-fly.
    
    Args:
        func: The sync function to convert
        thread_sensitive: Whether the function needs thread-sensitive execution
        
    Usage:
        sync_function = lambda x: SomeModel.objects.get(id=x)
        async_function = to_async(sync_function)
        result = await async_function(123)
    """
    return sync_to_async(func, thread_sensitive=thread_sensitive)


class AsyncDB:
    """
    Utility class to run database operations asynchronously.
    
    Usage:
        # Instead of sync_to_async everywhere:
        result = await AsyncDB.run(lambda: MyModel.objects.filter(name="test"))
        
        # Or with thread sensitivity:
        result = await AsyncDB.run_sensitive(lambda: some_thread_local_operation())
    """
    
    @staticmethod
    async def run(func: Callable, *args, **kwargs) -> Any:
        """Run a sync function asynchronously (thread_sensitive=False)"""
        async_func = sync_to_async(func, thread_sensitive=False)
        return await async_func(*args, **kwargs)
    
    @staticmethod
    async def run_sensitive(func: Callable, *args, **kwargs) -> Any:
        """Run a sync function asynchronously (thread_sensitive=True)"""
        async_func = sync_to_async(func, thread_sensitive=True)
        return await async_func(*args, **kwargs)


# Pre-configured common async functions for Slack operations
@make_async()
def get_company_name_for_slack_user(slack_user_id: str):
    """Get the company name for a Slack user ID"""
    from .models import SlackUserProfile
    try:
        profile = SlackUserProfile.objects.get(slack_user_id=slack_user_id, is_active=True)
        user = profile.user
        company = user.company
        return company.name.lower().replace(' ', '_')
    except SlackUserProfile.DoesNotExist:
        logger.warning(f"[Async Utils] No SlackUserProfile found for slack_user_id={slack_user_id}")
        return None
    except Exception as e:
        logger.exception(f"[Async Utils] Error getting company name for slack_user_id={slack_user_id}: {e}")
        return None


@make_async()
def get_company_name_for_slack_team(team_id: str):
    """Get the company name for a Slack team ID"""
    from .models import CompanyIntegration, IntegrationTool
    try:
        slack_tool = IntegrationTool.objects.filter(slug='slack').first()
        if not slack_tool:
            logger.warning(f"[Async Utils] No Slack integration tool found")
            return None
        
        integration = CompanyIntegration.objects.filter(
            tool=slack_tool,
            is_active=True,
            config__team_id=team_id
        ).first()
        
        if integration:
            company_name = integration.company.name.lower().replace(' ', '_')
            logger.info(f"[Async Utils] Found company '{integration.company.name}' for team '{team_id}'")
            return company_name
        
        logger.warning(f"[Async Utils] No company found for team '{team_id}'")
        return None
        
    except Exception as e:
        logger.exception(f"[Async Utils] Error getting company name for team_id={team_id}: {e}")
        return None


@make_async()
def get_bot_token_for_user(slack_user_id: str):
    """Get bot token for a Slack user"""
    from .models import SlackUserProfile
    try:
        profile = SlackUserProfile.objects.get(slack_user_id=slack_user_id, is_active=True)
        return profile.bot_token
    except SlackUserProfile.DoesNotExist:
        logger.warning(f"[Async Utils] No SlackUserProfile found for slack_user_id={slack_user_id}")
        return None
    except Exception as e:
        logger.exception(f"[Async Utils] Error getting bot token for slack_user_id={slack_user_id}: {e}")
        return None 