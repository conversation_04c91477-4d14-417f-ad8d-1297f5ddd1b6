# integrations/google_drive/views/webhooks.py
import logging
from typing import Iterable, Dict, Optional
import json

from django.http import HttpRequest, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.conf import settings

from integrations.google_drive.models import DriveChannel
from integrations.google_drive.services import get_drive_client
from integrations.google_drive.watcher.bootstrap import (
    renew_channel,
    RENEW_THRESHOLD,
)

log = logging.getLogger("drive.watcher")


def _latest_by_file(changes: Iterable[Dict]) -> Iterable[Dict]:
    """Deduplicate changes by fileId, keeping the latest change per file."""
    seen = {}
    for ch in changes:
        seen[ch["fileId"]] = ch
    return seen.values()


def _handle_sync_notification(chan: DriveChannel) -> None:
    """Handle the initial sync notification from Google Drive."""
    log.info("🔄 Processing sync notification for channel %s", chan.id)
    
    # Get the latest start page token
    try:
        drive = get_drive_client(chan.workspace).service
        start_token_resp = drive.changes().getStartPageToken(
            supportsAllDrives=True
        ).execute()
        
        new_start_token = start_token_resp["startPageToken"]
        
        # Update the channel with the new start token
        chan.page_token = new_start_token
        chan.last_msg_no = 0  # Reset message number for new channel
        chan.save(update_fields=("page_token", "last_msg_no", "updated_at"))
        
        log.info("✅ Sync completed for channel %s, new start token: %s", chan.id, new_start_token)
        
    except Exception as e:
        log.error("❌ Failed to handle sync notification for channel %s: %s", chan.id, e)
        raise

# TODO: Add logic to process changes
def _process_changes(chan: DriveChannel) -> int:
    """Process changes from Google Drive and return the number of changes processed."""
    try:
        drive = get_drive_client(chan.workspace).service
        
        # Get changes using the current page token
        resp = drive.changes().list(
            pageToken=chan.page_token,
            includeRemoved=True,
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            pageSize=1000,
        ).execute()
        
        changes = resp.get("changes", [])
        processed = 0
        
        # Get included file preferences for this workspace
        from integrations.google_drive.models import GoogleDriveFilePreference
        included_files = set(
            GoogleDriveFilePreference.objects.filter(
                workspace=chan.workspace,
                is_included=True
            ).values_list('file_id', flat=True)
        )
        
        log.info("📋 Processing changes for workspace %s - %s included files", 
                chan.workspace_id, len(included_files))
        
        # Process each change, but only for included files
        for ch in _latest_by_file(changes):
            fid = ch["fileId"]
            meta = ch.get("file", {})
            
            # Check if this file is included in preferences
            if fid not in included_files:
                log.debug("⏭️  Skipping file %s - not in included preferences", fid)
                continue
            
            # Process only included files
            if ch.get("removed"):
                log.info("🗑️  removed   %s (included file)", fid)
                # Remove preference when file is deleted
                try:
                    GoogleDriveFilePreference.objects.filter(
                        workspace=chan.workspace,
                        file_id=fid
                    ).delete()
                    log.info("🗑️  Removed preference for deleted file %s", fid)
                except Exception as e:
                    log.warning("⚠️  Failed to remove preference for deleted file %s: %s", fid, e)
                
                # Process the removal
                _process_included_file_change(chan.workspace, fid, meta, 'removed')
                    
            elif meta.get("trashed"):
                log.info("🚮 trashed   %s (included file)", fid)
                # Process the trashing
                _process_included_file_change(chan.workspace, fid, meta, 'trashed')
                
            else:
                log.info("✏️  modified  %s (included file)", fid)
                # Process the modification
                _process_included_file_change(chan.workspace, fid, meta, 'modified')
                
            processed += 1
        
        # Update the page token to the new start page token
        new_start_token = resp.get("newStartPageToken")
        if new_start_token:
            chan.page_token = new_start_token
        
        if processed > 0:
            log.info("✅ Processed %s changes for included files in workspace %s", 
                    processed, chan.workspace_id)
        else:
            log.info("ℹ️  No changes for included files in workspace %s", chan.workspace_id)
        
        return processed
        
    except Exception as e:
        log.error("❌ Failed to process changes for channel %s: %s", chan.id, e)
        raise


def _should_renew_channel(chan: DriveChannel) -> bool:
    """Check if the channel should be renewed based on expiration time."""
    if not chan.expires_at:
        return False
    
    # Ensure timezone awareness
    if timezone.is_naive(chan.expires_at):
        chan.expires_at = chan.expires_at.replace(tzinfo=timezone.utc)
    
    time_until_expiry = chan.expires_at - timezone.now()
    return time_until_expiry < RENEW_THRESHOLD


def _process_included_file_change(workspace, file_id: str, file_meta: dict, change_type: str):
    """
    Process changes for files that are included in user preferences.
    
    Args:
        workspace: GoogleDriveWorkspace instance
        file_id: Google Drive file ID
        file_meta: File metadata from Google Drive
        change_type: Type of change ('modified', 'removed', 'trashed')
    """
    try:
        mime_type = file_meta.get('mimeType', 'Unknown')
        file_name = file_meta.get('name', 'Unknown')
        
        log.info("🔄 Processing %s for included file %s (%s) [%s]", 
                change_type, file_id, file_name, mime_type)
        
        if change_type == 'modified':
            # TODO: Add business logic for file modification
            log.info("📝 File %s was modified - processing content", file_id)
            
        elif change_type == 'removed':
            # TODO: Add cleanup logic for file removal
            log.info("🗑️  File %s was removed - cleaning up", file_id)
            
        elif change_type == 'trashed':
            # TODO: Add logic for trashed files
            log.info("🚮 File %s was trashed - updating status", file_id)
            
    except Exception as e:
        log.error("❌ Failed to process %s for file %s: %s", change_type, file_id, e)
        raise


@csrf_exempt
def drive_webhook(request: HttpRequest) -> HttpResponse:
    """
    Handle Google Drive webhook notifications.
    
    This endpoint receives notifications from Google Drive when files change.
    It handles both sync notifications (initial setup) and change notifications.
    """
    # Extract headers
    chan_id = request.headers.get("X-Goog-Channel-Id")
    msg_no = int(request.headers.get("X-Goog-Message-Number", 0))
    token = request.headers.get("X-Goog-Channel-Token")
    resource_state = request.headers.get("X-Goog-Resource-State", "")
    resource_uri = request.headers.get("X-Goog-Resource-URI", "")
    
    log.info("📡 Webhook hit – id=%s msg=%s state=%s", chan_id, msg_no, resource_state)
    
    # Validate required headers
    if not chan_id or not token:
        log.warning("❌ Missing required headers: chan_id=%s, token=%s", chan_id, token)
        return HttpResponse(status=400)
    
    # ── verify channel & secret ───────────────────────────────────────────
    try:
        chan = (
            DriveChannel.objects
            .select_related("workspace")
            .only(
                "id", "secret", "page_token", "last_msg_no",
                "workspace", "expires_at", "resource_id"
            )
            .get(id=chan_id)
        )
    except DriveChannel.DoesNotExist:
        log.warning("🧟 Webhook from unknown channel %s – returning 204 to kill retry", chan_id)
        return HttpResponse(status=204)
    
    # Verify token
    if token != chan.secret:
        log.warning("🔒 Invalid token for channel %s", chan_id)
        return HttpResponse(status=403)
    
    # Handle duplicate/old notifications
    if msg_no and msg_no <= chan.last_msg_no:
        log.info("🔄 Duplicate/old notification for channel %s (msg_no: %s, last: %s)", 
                chan_id, msg_no, chan.last_msg_no)
        return HttpResponse(status=200)
    
    # ── handle sync notification ───────────────────────────────────────────
    if resource_state == "sync":
        log.info("🔄 Processing sync notification for channel %s", chan_id)
        try:
            _handle_sync_notification(chan)
            return HttpResponse(status=200)
        except Exception as e:
            log.error("❌ Failed to handle sync notification: %s", e)
            return HttpResponse(status=500)
    
    # ── handle change notification ─────────────────────────────────────────
    if resource_state in ["update", "change"]:
        log.info("📝 Processing change notification for channel %s (state: %s)", chan_id, resource_state)
        
        # Check if channel needs renewal
        renewed = False
        if _should_renew_channel(chan):
            log.info("⏳ Channel %s about to expire, renewing …", chan.id)
            try:
                renew_channel(chan)
                renewed = True
                log.info("✅ Channel %s renewed successfully", chan.id)
            except Exception as e:
                log.error("❌ Failed to renew channel %s: %s", chan.id, e)
                return HttpResponse(status=500)
        
        # Update message number only if we didn't renew
        if not renewed:
            chan.last_msg_no = msg_no
        
        # Process changes
        try:
            processed = _process_changes(chan)
            
            # Save channel state
            chan.save(update_fields=("page_token", "last_msg_no", "updated_at"))
            
            if processed > 0:
                log.info(
                    "✅ Workspace %s – %s change(s) processed, new token %s",
                    chan.workspace_id, processed, chan.page_token
                )
            else:
                log.info("ℹ️  No changes to process for channel %s", chan_id)
                
        except Exception as e:
            log.error("❌ Failed to process changes for channel %s: %s", chan.id, e)
            return HttpResponse(status=500)
    
    # Handle unknown resource state
    else:
        log.warning("⚠️  Unknown resource state '%s' for channel %s - treating as change notification", resource_state, chan_id)
        # Treat unknown states as change notifications to be safe
        try:
            processed = _process_changes(chan)
            chan.last_msg_no = msg_no
            chan.save(update_fields=("page_token", "last_msg_no", "updated_at"))
            
            if processed > 0:
                log.info(
                    "✅ Workspace %s – %s change(s) processed (unknown state: %s), new token %s",
                    chan.workspace_id, processed, resource_state, chan.page_token
                )
            else:
                log.info("ℹ️  No changes to process for channel %s (unknown state: %s)", chan_id, resource_state)
                
        except Exception as e:
            log.error("❌ Failed to process changes for channel %s (unknown state: %s): %s", chan.id, resource_state, e)
            return HttpResponse(status=500)
    
    return HttpResponse(status=200)
