"""
Google Drive integration views.
"""
import json
import base64
import logging
from django.conf import settings
from django.http import HttpResponseRedirect, JsonResponse
from django.utils import timezone
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request

from integrations.models import Company, User, IntegrationTool, CompanyIntegration
from .models import GoogleDriveWorkspace, GoogleDriveFilePreference
from .services import get_drive_client, revoke_google_drive_authorization

logger = logging.getLogger(__name__)

# MIME types that can be embedded/processed
EMBEDDABLE_MIME_TYPES = [
    # Google Workspace files (require export)
    'application/vnd.google-apps.document',       # Google Docs → export as text/plain
    'application/vnd.google-apps.spreadsheet',    # Google Sheets → export as text/csv
    'application/vnd.google-apps.presentation',   # Google Slides → export as text/plain

    # Microsoft Office files
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',        # .xlsx
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',# .pptx

    # PDFs
    'application/pdf',

    # Plain text
    'text/plain',
    'text/csv',
    'text/tab-separated-values',

    # Markdown
    'text/markdown',

    # JSON / XML
    'application/json',
    'application/xml',
    'text/xml',

    # HTML
    'text/html'
]


def is_embeddable_file(file_info):
    """
    Check if a file is of an embeddable/processable type.
    
    Args:
        file_info: Dictionary containing file metadata from Google Drive
        
    Returns:
        bool: True if file is embeddable, False otherwise
    """
    mime_type = file_info.get('mimeType', '')
    
    # Skip folders
    if mime_type == 'application/vnd.google-apps.folder':
        return False
    
    # Check if MIME type is in our embeddable list
    return mime_type in EMBEDDABLE_MIME_TYPES


@api_view(['GET'])
@permission_classes([AllowAny])
def google_drive_oauth_start(request):
    """
    Start Google Drive OAuth2 flow.
    Returns authorization URL with encoded state parameter.
    """
    logger.info("🚀 Starting Google Drive OAuth flow")
    logger.info(f"📋 Request params: {request.GET}")
    
    company_id = request.GET.get('company_id')
    workspace = request.GET.get('workspace', 'default')
    
    if not company_id:
        return Response({'error': 'company_id parameter required'}, status=400)
    
    # Verify company exists
    try:
        Company.objects.get(id=company_id)
    except Company.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)
    
    try:
        # Log credentials file path
        logger.info(f"📁 Using credentials file: {settings.GDRIVE_OAUTH_CREDENTIALS_FILE}")
        logger.info(f"🔄 Using redirect URI: {settings.GDRIVE_REDIRECT_URI}")
        
        # Create OAuth2 flow with all possible scopes that Google might return
        flow = Flow.from_client_secrets_file(
            settings.GDRIVE_OAUTH_CREDENTIALS_FILE,
            scopes=[
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/drive.metadata',
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
                'openid'
            ],
            redirect_uri=settings.GDRIVE_REDIRECT_URI
        )
        
        # Encode state with company_id and workspace
        state_data = {
            'company_id': company_id,
            'workspace': workspace
        }
        state = base64.urlsafe_b64encode(json.dumps(state_data).encode()).decode()
        
        # Generate authorization URL with prompt=consent to force refresh token
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent',  # Force consent screen to get refresh token
            state=state
        )
        
        return HttpResponseRedirect(authorization_url)
        
    except Exception as e:
        logger.error(f"Error starting Google Drive OAuth: {e}")
        return Response({'error': 'Failed to start OAuth flow'}, status=500)


@api_view(['GET'])
@permission_classes([AllowAny])
def google_drive_oauth_callback(request):
    """
    Handle Google Drive OAuth2 callback.
    Exchange code for credentials and store in database.
    """
    logger.info("🚀 GOOGLE DRIVE CALLBACK CALLED!")
    logger.info(f"📋 Request params: {request.GET}")
    
    code = request.GET.get('code')
    state = request.GET.get('state')
    error = request.GET.get('error')
    
    if error:
        logger.error(f"OAuth error: {error}")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error={error}"
        return HttpResponseRedirect(redirect_url)
    
    if not code or not state:
        error_msg = "Missing authorization code or state"
        logger.error(error_msg)
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error={error_msg}"
        return HttpResponseRedirect(redirect_url)
    
    try:
        # Decode state parameter
        state_data = json.loads(base64.urlsafe_b64decode(state.encode()).decode())
        company_id = state_data['company_id']
        workspace = state_data['workspace']
        
        # Get company
        company = Company.objects.get(id=company_id)
        
        # Get the first admin user for this company (following Discord pattern)
        user = User.objects.filter(company=company, role=User.Role.ADMIN).first()
        if not user:
            error_msg = "No admin user found for company"
            logger.error(error_msg)
            redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error={error_msg}"
            return HttpResponseRedirect(redirect_url)
        
        # Exchange code for credentials with all possible scopes that Google might return
        flow = Flow.from_client_secrets_file(
            settings.GDRIVE_OAUTH_CREDENTIALS_FILE,
            scopes=[
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/drive.metadata',
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
                'openid'
            ],
            redirect_uri=settings.GDRIVE_REDIRECT_URI,
            state=state
        )
        
        # Log the redirect URI being used for debugging
        logger.info(f"Using redirect URI: {settings.GDRIVE_REDIRECT_URI}")
        
        try:
            flow.fetch_token(code=code)
        except Exception as token_error:
            logger.error(f"Failed to exchange code for token: {token_error}")
            redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error=token_exchange_failed"
            return HttpResponseRedirect(redirect_url)
        creds = flow.credentials
        
        # Get user info from Google
        from googleapiclient.discovery import build
        service = build(
            "drive",
            "v3",
            credentials=creds,
            cache_discovery=False,   # ← silences the warning
        )

        about = service.about().get(fields='user').execute()
        google_user_email = about['user']['emailAddress']
        
        # Create or update GoogleDriveWorkspace
        gdrive_workspace, created = GoogleDriveWorkspace.objects.update_or_create(
            user=user,
            company=company,
            workspace=workspace,
            defaults={
                'credentials_json': creds.to_json(),
                'google_user_email': google_user_email,
                'status': GoogleDriveWorkspace.Status.CONNECTED,
                'is_active': True,
                'connected_at': timezone.now(),
                'folder_id': '',  # Will be set later by user
                'folder_name': '',
            }
        )
        
        # Get or create Google Drive integration tool
        gdrive_tool, tool_created = IntegrationTool.objects.get_or_create(
            slug='google-drive',
            defaults={
                'name': 'Google Drive',
                'category': 'OTHER',
                'description': 'Google Drive integration for file management',
                'is_active': True
            }
        )
        
        # Create company integration
        integration, created = CompanyIntegration.objects.update_or_create(
            company=company,
            tool=gdrive_tool,
            defaults={
                'status': 'CONNECTED',
                'is_active': True,
                'connected_at': timezone.now(),
                'config': {
                    'workspace_id': str(gdrive_workspace.id),
                    'google_user_email': google_user_email,
                    'workspace': workspace,
                    'connected_by': user.email
                }
            }
        )
        
        logger.info(f"Google Drive connected for company {company.name}, workspace {workspace}")
        
        # Create webhook channel for the workspace
        try:
            from integrations.google_drive.watcher.bootstrap import ensure_channel
            ensure_channel(gdrive_workspace)
            logger.info(f"Created Drive channel for workspace {gdrive_workspace.id}")
        except Exception as e:
            logger.warning(f"Failed to create Drive channel: {e}")
        
        # Automatically include all files in the workspace
        try:
            auto_include_all_files(gdrive_workspace)
            logger.info(f"Automatically included all files for workspace {gdrive_workspace.id}")
        except Exception as e:
            logger.warning(f"Failed to auto-include files: {e}")
        
        # Redirect to frontend success page
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?success=true&workspace_id={gdrive_workspace.id}"
        return HttpResponseRedirect(redirect_url)
        
    except Exception as e:
        logger.error(f"Error in Google Drive OAuth callback: {e}")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error=oauth_failed"
        return HttpResponseRedirect(redirect_url)


def auto_include_all_files(workspace):
    """
    Automatically include embeddable files in the workspace by creating preferences for them.
    This is called after Google Drive authorization to set up initial file preferences.
    Only files with processable MIME types are included.
    """
    try:
        # Get drive client
        drive_client = get_drive_client(workspace)
        if not drive_client:
            logger.error(f"Failed to get drive client for workspace {workspace.id}")
            return
        
        # Recursively get all files from all folders
        all_files = get_all_files_recursively(drive_client, 'root')
        
        # Create preferences only for embeddable files
        created_count = 0
        skipped_count = 0
        
        for file in all_files:
            # Check if file is embeddable/processable
            if is_embeddable_file(file):
                try:
                    # Create preference with is_included=True
                    preference, created = GoogleDriveFilePreference.objects.get_or_create(
                        workspace=workspace,
                        file_id=file['id'],
                        defaults={
                            'file_path': file.get('name', 'Unknown File'),
                            'is_included': True,
                        }
                    )
                    if created:
                        created_count += 1
                        logger.debug(f"✅ Included embeddable file: {file.get('name', 'Unknown')} ({file.get('mimeType', 'Unknown')})")
                except Exception as e:
                    logger.warning(f"Failed to create preference for file {file['id']}: {e}")
            else:
                skipped_count += 1
                logger.debug(f"⏭️  Skipped non-embeddable file: {file.get('name', 'Unknown')} ({file.get('mimeType', 'Unknown')})")
        
        logger.info(f"Auto-included {created_count} embeddable files for workspace {workspace.id} (skipped {skipped_count} non-embeddable files)")
        
    except Exception as e:
        logger.error(f"Error in auto_include_all_files: {e}")
        raise


def get_all_files_recursively(drive_client, folder_id, processed_folders=None):
    """
    Recursively get all files from a folder and its subfolders.
    """
    if processed_folders is None:
        processed_folders = set()
    
    # Avoid infinite recursion
    if folder_id in processed_folders:
        return []
    
    processed_folders.add(folder_id)
    all_files = []
    
    try:
        # Get files and folders from current folder
        items = drive_client.list_files(folder_id=folder_id)
        
        for item in items:
            all_files.append(item)
            
            # If it's a folder, recursively process it
            if 'folder' in item['mimeType']:
                subfolder_files = get_all_files_recursively(drive_client, item['id'], processed_folders)
                all_files.extend(subfolder_files)
        
        return all_files
        
    except Exception as e:
        logger.warning(f"Failed to process folder {folder_id}: {e}")
        return []


@api_view(['GET'])
@permission_classes([AllowAny])
def google_drive_status(request):
    """
    Get Google Drive connection status for a company and workspace.
    """
    company_id = request.GET.get('company_id')
    workspace = request.GET.get('workspace', 'default')
    
    if not company_id:
        return Response({'error': 'company_id parameter required'}, status=400)
    
    try:
        company = Company.objects.get(id=company_id)
        
        # Get workspace for this company
        gdrive_workspace = GoogleDriveWorkspace.objects.filter(
            company=company,
            workspace=workspace,
            is_active=True
        ).first()
        
        if not gdrive_workspace:
            return Response({
                'connected': False,
                'status': 'DISCONNECTED',
                'workspace': workspace,
                'company_id': company_id
            })
        
        return Response({
            'connected': True,
            'status': gdrive_workspace.status,
            'workspace': workspace,
            'workspace_id': str(gdrive_workspace.id),
            'google_user_email': gdrive_workspace.google_user_email,
            'folder_id': gdrive_workspace.folder_id,
            'folder_name': gdrive_workspace.folder_name,
            'last_sync': gdrive_workspace.last_sync.isoformat() if gdrive_workspace.last_sync else None,
            'connected_at': gdrive_workspace.connected_at.isoformat(),
            'company_id': company_id
        })
        
    except Company.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)
    except Exception as e:
        logger.error(f"Error getting Google Drive status: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def update_file_preference(request):
    """
    Update file inclusion/exclusion preference.
    Only embeddable files can be included. Excluded files are deleted from database.
    """
    workspace_id = request.data.get('workspace_id')
    file_id = request.data.get('file_id')
    file_path = request.data.get('file_path')
    mime_type = request.data.get('mime_type', '')
    is_included = request.data.get('is_included', True)  # Default to True (included)
    
    if not workspace_id or not file_id or not file_path:
        return Response({'error': 'workspace_id, file_id, and file_path required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        if is_included:
            # Check if file is embeddable before including
            file_info = {
                'mimeType': mime_type,
                'id': file_id,
                'name': file_path
            }
            
            if not is_embeddable_file(file_info):
                return Response({
                    'success': False,
                    'error': 'File type not supported for embedding/processing',
                    'mime_type': mime_type,
                    'supported_types': EMBEDDABLE_MIME_TYPES
                }, status=400)
            
            # Include file - create or update in database
            preference, created = GoogleDriveFilePreference.objects.update_or_create(
                workspace=workspace,
                file_id=file_id,
                defaults={
                    'file_path': file_path,
                    'is_included': True,
                }
            )
            
            return Response({
                'success': True,
                'created': created,
                'preference_id': str(preference.id),
                'file_id': preference.file_id,
                'file_path': preference.file_path,
                'is_included': True
            })
        else:
            # Exclude file - delete from database
            try:
                preference = GoogleDriveFilePreference.objects.get(
                    workspace=workspace,
                    file_id=file_id
                )
                preference.delete()
                return Response({
                    'success': True,
                    'deleted': True,
                    'file_id': file_id,
                    'is_included': False
                })
            except GoogleDriveFilePreference.DoesNotExist:
                # File was already not in database (excluded)
                return Response({
                    'success': True,
                    'deleted': False,
                    'file_id': file_id,
                    'is_included': False
                })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error updating file preference: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def batch_update_preferences(request):
    """
    Batch update file preferences - used for APPLY button functionality.
    Included files exist in database, excluded files are deleted from database.
    """
    workspace_id = request.data.get('workspace_id')
    preferences = request.data.get('preferences', [])  # Array of {file_id, file_path, is_included}
    
    if not workspace_id or not preferences:
        return Response({'error': 'workspace_id and preferences array required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        results = {
            'included': [],
            'excluded': [],
            'errors': []
        }
        
        for pref in preferences:
            file_id = pref.get('file_id')
            file_path = pref.get('file_path')
            mime_type = pref.get('mime_type', '')
            is_included = pref.get('is_included', True)  # Default to True
            
            if not file_id or not file_path:
                results['errors'].append({
                    'file_id': file_id,
                    'error': 'Missing file_id or file_path'
                })
                continue
            
            try:
                if is_included:
                    # Check if file is embeddable before including
                    file_info = {
                        'mimeType': mime_type,
                        'id': file_id,
                        'name': file_path
                    }
                    
                    if not is_embeddable_file(file_info):
                        results['errors'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'error': f'File type not supported for embedding/processing: {mime_type}'
                        })
                        continue
                    
                    # Include file - create or update in database
                    preference, created = GoogleDriveFilePreference.objects.update_or_create(
                        workspace=workspace,
                        file_id=file_id,
                        defaults={
                            'file_path': file_path,
                            'is_included': True,
                        }
                    )
                    results['included'].append({
                        'file_id': file_id,
                        'file_path': file_path,
                        'created': created
                    })
                else:
                    # Exclude file - delete from database
                    try:
                        preference = GoogleDriveFilePreference.objects.get(
                            workspace=workspace,
                            file_id=file_id
                        )
                        preference.delete()
                        results['excluded'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'deleted': True
                        })
                    except GoogleDriveFilePreference.DoesNotExist:
                        # Already not in database
                        results['excluded'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'deleted': False
                        })
                        
            except Exception as e:
                results['errors'].append({
                    'file_id': file_id,
                    'error': str(e)
                })
        
        return Response({
            'success': True,
            'results': results,
            'summary': {
                'total_processed': len(preferences),
                'included_count': len(results['included']),
                'excluded_count': len(results['excluded']),
                'error_count': len(results['errors'])
            }
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error in batch update preferences: {e}")
        return Response({'error': 'Internal server error'}, status=500)


# Removed add_pattern_rule - no longer needed with simplified preferences


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def list_folder_files(request):
    """
    List files in the workspace folder with current inclusion state.
    Supports both GET and POST for compatibility with frontend.
    """
    if request.method == 'POST':
        workspace_id = request.data.get('workspace_id')
        folder_id = request.data.get('folder_id')
    else:
        workspace_id = request.GET.get('workspace_id')
        folder_id = request.GET.get('folder_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id parameter required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        drive_client = get_drive_client(workspace)
        
        if not drive_client:
            return Response({'error': 'Failed to authenticate with Google Drive'}, status=401)
        
        # Use folder_id from request or workspace default
        target_folder_id = folder_id or workspace.folder_id or 'root'
        
        # Get files from Google Drive
        files = drive_client.list_files(folder_id=target_folder_id)
        
        # Get existing preferences for files that have explicit preferences set
        preferences = {
            pref.file_id: pref for pref in 
            GoogleDriveFilePreference.objects.filter(workspace=workspace)
            if pref.file_id  # Only get preferences with actual file_ids
        }
        
        # Build response with inclusion state
        files_with_preferences = []
        for file in files:
            file_id = file['id']
            preference = preferences.get(file_id)
            is_embeddable = is_embeddable_file(file)
            
            files_with_preferences.append({
                'id': file_id,
                'name': file['name'],
                'mimeType': file['mimeType'],
                'modifiedTime': file.get('modifiedTime'),
                'size': file.get('size'),
                'is_included': preference is not None,  # If preference exists, file is included
                'has_preference': preference is not None,
                'is_embeddable': is_embeddable,  # Whether file type can be processed
                'can_be_included': is_embeddable  # Only embeddable files can be included
            })
        
        return Response({
            'files': files_with_preferences,
            'folder_id': target_folder_id,
            'total_files': len(files_with_preferences),
            'workspace_id': workspace_id
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error listing folder files: {e}")
        return Response({'error': 'Internal server error'}, status=500) 
