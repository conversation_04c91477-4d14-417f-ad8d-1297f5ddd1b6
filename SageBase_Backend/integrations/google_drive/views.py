"""
Google Drive integration views.
"""
import json
import base64
import logging
from django.conf import settings
from django.http import HttpResponseRedirect, JsonResponse
from django.utils import timezone
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request

from integrations.models import Company, User, IntegrationTool, CompanyIntegration
from .models import GoogleDriveWorkspace, GoogleDriveFilePreference
from .services import get_drive_client, revoke_google_drive_authorization
from .embedding.tasks import (
    enqueue_add_file_to_collection,
    enqueue_remove_file_from_collection,
    enqueue_update_file_in_collection,
    enqueue_cleanup_workspace_files_by_company,
    enqueue_sync_file_preferences,
    enqueue_embed_all_included_files,
)

logger = logging.getLogger(__name__)

# MIME types that can be embedded/processed
EMBEDDABLE_MIME_TYPES = [
    # Google Workspace files (require export)
    'application/vnd.google-apps.document',       # Google Docs → export as text/plain
    'application/vnd.google-apps.spreadsheet',    # Google Sheets → export as text/csv
    'application/vnd.google-apps.presentation',   # Google Slides → export as text/plain

    # Microsoft Office files
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',        # .xlsx
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',# .pptx

    # PDFs
    'application/pdf',

    # Plain text
    'text/plain',
    'text/csv',
    'text/tab-separated-values',

    # Markdown
    'text/markdown',

    # JSON / XML
    'application/json',
    'application/xml',
    'text/xml',

    # HTML
    'text/html'
]


def is_embeddable_file(file_info):
    """
    Check if a file is of an embeddable/processable type.
    
    Args:
        file_info: Dictionary containing file metadata from Google Drive
        
    Returns:
        bool: True if file is embeddable, False otherwise
    """
    mime_type = file_info.get('mimeType', '')
    
    # Skip folders
    if mime_type == 'application/vnd.google-apps.folder':
        return False
    
    # Check if MIME type is in our embeddable list
    return mime_type in EMBEDDABLE_MIME_TYPES


@api_view(['GET'])
@permission_classes([AllowAny])
def google_drive_oauth_start(request):
    """
    Start Google Drive OAuth2 flow.
    Returns authorization URL with encoded state parameter.
    """
    logger.info("🚀 Starting Google Drive OAuth flow")
    logger.info(f"📋 Request params: {request.GET}")
    
    company_id = request.GET.get('company_id')
    workspace = request.GET.get('workspace', 'default')
    
    if not company_id:
        return Response({'error': 'company_id parameter required'}, status=400)
    
    # Verify company exists
    try:
        Company.objects.get(id=company_id)
    except Company.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)
    
    try:
        # Log credentials file path
        logger.info(f"📁 Using credentials file: {settings.GDRIVE_OAUTH_CREDENTIALS_FILE}")
        logger.info(f"🔄 Using redirect URI: {settings.GDRIVE_REDIRECT_URI}")
        
        # Create OAuth2 flow with all possible scopes that Google might return
        flow = Flow.from_client_secrets_file(
            settings.GDRIVE_OAUTH_CREDENTIALS_FILE,
            scopes=[
                'https://www.googleapis.com/auth/drive.readonly',
                'https://www.googleapis.com/auth/drive.metadata.readonly',
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
                'openid'
            ],
            redirect_uri=settings.GDRIVE_REDIRECT_URI
        )
        
        # Encode state with company_id and workspace
        state_data = {
            'company_id': company_id,
            'workspace': workspace
        }
        state = base64.urlsafe_b64encode(json.dumps(state_data).encode()).decode()
        
        # Generate authorization URL with prompt=consent to force refresh token
        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent',  # Force consent screen to get refresh token
            state=state
        )
        
        return HttpResponseRedirect(authorization_url)
        
    except Exception as e:
        logger.error(f"Error starting Google Drive OAuth: {e}")
        return Response({'error': 'Failed to start OAuth flow'}, status=500)


@api_view(['GET'])
@permission_classes([AllowAny])
def google_drive_oauth_callback(request):
    """
    Handle Google Drive OAuth2 callback.
    Exchange code for credentials and store in database.
    """
    logger.info("🚀 GOOGLE DRIVE CALLBACK CALLED!")
    logger.info(f"📋 Request params: {request.GET}")
    
    code = request.GET.get('code')
    state = request.GET.get('state')
    error = request.GET.get('error')
    
    if error:
        logger.error(f"OAuth error: {error}")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error={error}"
        return HttpResponseRedirect(redirect_url)
    
    if not code or not state:
        error_msg = "Missing authorization code or state"
        logger.error(error_msg)
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error={error_msg}"
        return HttpResponseRedirect(redirect_url)
    
    try:
        # Decode state parameter
        state_data = json.loads(base64.urlsafe_b64decode(state.encode()).decode())
        company_id = state_data['company_id']
        workspace = state_data['workspace']
        
        # Get company
        company = Company.objects.get(id=company_id)
        
        # Get the first admin user for this company (following Discord pattern)
        user = User.objects.filter(company=company, role=User.Role.ADMIN).first()
        if not user:
            error_msg = "No admin user found for company"
            logger.error(error_msg)
            redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error={error_msg}"
            return HttpResponseRedirect(redirect_url)
        
        # Exchange code for credentials with all possible scopes that Google might return
        flow = Flow.from_client_secrets_file(
            settings.GDRIVE_OAUTH_CREDENTIALS_FILE,
            scopes=[
                'https://www.googleapis.com/auth/drive.readonly',
                'https://www.googleapis.com/auth/drive.metadata.readonly',
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
                'openid'
            ],
            redirect_uri=settings.GDRIVE_REDIRECT_URI,
            state=state
        )
        
        # Log the redirect URI being used for debugging
        logger.info(f"Using redirect URI: {settings.GDRIVE_REDIRECT_URI}")
        
        try:
            flow.fetch_token(code=code)
        except Exception as token_error:
            logger.error(f"Failed to exchange code for token: {token_error}")
            redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error=token_exchange_failed"
            return HttpResponseRedirect(redirect_url)
        creds = flow.credentials
        
        # Get user info from Google
        from googleapiclient.discovery import build
        service = build(
            "drive",
            "v3",
            credentials=creds,
            cache_discovery=False,   # ← silences the warning
        )

        about = service.about().get(fields='user').execute()
        google_user_email = about['user']['emailAddress']
        
        # Create or update GoogleDriveWorkspace
        gdrive_workspace, created = GoogleDriveWorkspace.objects.update_or_create(
            user=user,
            company=company,
            workspace=workspace,
            defaults={
                'credentials_json': creds.to_json(),
                'google_user_email': google_user_email,
                'status': GoogleDriveWorkspace.Status.CONNECTED,
                'is_active': True,
                'connected_at': timezone.now(),
                'folder_id': '',  # Will be set later by user
                'folder_name': '',
            }
        )
        
        # Get or create Google Drive integration tool
        gdrive_tool, tool_created = IntegrationTool.objects.get_or_create(
            slug='google-drive',
            defaults={
                'name': 'Google Drive',
                'category': 'OTHER',
                'description': 'Google Drive integration for file management',
                'is_active': True
            }
        )
        
        # Create company integration
        integration, created = CompanyIntegration.objects.update_or_create(
            company=company,
            tool=gdrive_tool,
            defaults={
                'status': 'CONNECTED',
                'is_active': True,
                'connected_at': timezone.now(),
                'config': {
                    'workspace_id': str(gdrive_workspace.id),
                    'google_user_email': google_user_email,
                    'workspace': workspace,
                    'connected_by': user.email
                }
            }
        )
        
        logger.info(f"Google Drive connected for company {company.name}, workspace {workspace}")
        
        
        # Redirect to frontend success page
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?success=true&workspace_id={gdrive_workspace.id}"
        return HttpResponseRedirect(redirect_url)
        
    except Exception as e:
        logger.error(f"Error in Google Drive OAuth callback: {e}")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/integrations/google-drive/callback?error=oauth_failed"
        return HttpResponseRedirect(redirect_url)




def get_all_files_recursively(drive_client, folder_id, processed_folders=None):
    """
    Recursively get all files from a folder and its subfolders.
    """
    if processed_folders is None:
        processed_folders = set()
    
    # Avoid infinite recursion
    if folder_id in processed_folders:
        return []
    
    processed_folders.add(folder_id)
    all_files = []
    
    try:
        # Get files and folders from current folder
        items = drive_client.list_files(folder_id=folder_id)
        
        for item in items:
            is_folder = item.get('mimeType') == 'application/vnd.google-apps.folder'
            # Include only folders or embeddable files in the returned list
            if is_folder or is_embeddable_file(item):
                all_files.append(item)
            
            # If it's a folder, recursively process it
            if is_folder:
                subfolder_files = get_all_files_recursively(drive_client, item['id'], processed_folders)
                all_files.extend(subfolder_files)
         
        return all_files
        
    except Exception as e:
        logger.warning(f"Failed to process folder {folder_id}: {e}")
        return []


@api_view(['GET'])
@permission_classes([AllowAny])
def google_drive_status(request):
    """
    Get Google Drive connection status for a company and workspace.
    """
    company_id = request.GET.get('company_id')
    workspace = request.GET.get('workspace', 'default')
    
    if not company_id:
        return Response({'error': 'company_id parameter required'}, status=400)
    
    try:
        company = Company.objects.get(id=company_id)
        
        # Get workspace for this company
        gdrive_workspace = GoogleDriveWorkspace.objects.filter(
            company=company,
            workspace=workspace,
            is_active=True
        ).first()
        
        if not gdrive_workspace:
            return Response({
                'connected': False,
                'status': 'DISCONNECTED',
                'workspace': workspace,
                'company_id': company_id
            })
        
        return Response({
            'connected': True,
            'status': gdrive_workspace.status,
            'workspace': workspace,
            'workspace_id': str(gdrive_workspace.id),
            'google_user_email': gdrive_workspace.google_user_email,
            'folder_id': gdrive_workspace.folder_id,
            'folder_name': gdrive_workspace.folder_name,
            'last_sync': gdrive_workspace.last_sync.isoformat() if gdrive_workspace.last_sync else None,
            'connected_at': gdrive_workspace.connected_at.isoformat(),
            'company_id': company_id
        })
        
    except Company.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)
    except Exception as e:
        logger.error(f"Error getting Google Drive status: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def update_file_preference(request):
    """
    Update file inclusion/exclusion preference.
    Only embeddable files can be included. Excluded files are deleted from database and ChromaDB.
    """
    workspace_id = request.data.get('workspace_id')
    file_id = request.data.get('file_id')
    file_path = request.data.get('file_path')
    mime_type = request.data.get('mime_type', '')
    is_included = request.data.get('is_included', True) 
    
    if not workspace_id or not file_id or not file_path:
        return Response({'error': 'workspace_id, file_id, and file_path required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        if is_included:
            # Check if file is embeddable before including
            file_info = {
                'mimeType': mime_type,
                'id': file_id,
                'name': file_path
            }
            
            if not is_embeddable_file(file_info):
                return Response({
                    'success': False,
                    'error': 'File type not supported for embedding/processing',
                    'mime_type': mime_type,
                    'supported_types': EMBEDDABLE_MIME_TYPES
                }, status=400)
            
            # Include file - create or update in database
            preference, created = GoogleDriveFilePreference.objects.update_or_create(
                workspace=workspace,
                file_id=file_id,
                defaults={
                    'file_path': file_path,
                    'is_included': True,
                }
            )
            
            return Response({
                'success': True,
                'created': created,
                'preference_id': str(preference.id),
                'file_id': preference.file_id,
                'file_path': preference.file_path,
                'is_included': True,
                # 'chroma_added': embedding_result.get('success', False) if 'embedding_result' in locals() else False
            })
        else:
            # Exclude file - delete from database and enqueue ChromaDB removal
            try:
                preference = GoogleDriveFilePreference.objects.get(
                    workspace=workspace,
                    file_id=file_id
                )
                
                # Enqueue removal from ChromaDB collection
                try:
                    enqueue_remove_file_from_collection(str(workspace.id), file_id)
                    logger.info(f"🧵 Enqueued removal of file {file_id} from ChromaDB collection")
                except Exception as e:
                    logger.error(f"Error enqueuing removal of file {file_id} from ChromaDB: {e}")
                
                # Then delete from database
                preference.delete()
                
                return Response({
                    'success': True,
                    'deleted': True,
                    'file_id': file_id,
                    'is_included': False,
                    'queued': True
                })
            except GoogleDriveFilePreference.DoesNotExist:
                # File was already not in database (excluded)
                return Response({
                    'success': True,
                    'deleted': False,
                    'file_id': file_id,
                    'is_included': False
                })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error updating file preference: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def batch_update_preferences(request):
    """
    Batch update file preferences - used for APPLY button functionality.
    Included files exist in database, excluded files are deleted from database.
    """
    workspace_id = request.data.get('workspace_id')
    preferences = request.data.get('preferences', [])  # Array of {file_id, file_path, is_included}
    
    logger.info(f"🔄 Processing batch preferences for workspace {workspace_id}")
    logger.info(f"📋 Received {len(preferences)} preferences: {preferences}")
    
    if not workspace_id or not preferences:
        return Response({'error': 'workspace_id and preferences array required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        logger.info(f"✅ Found active workspace: {workspace.id}")
        
        results = {
            'included': [],
            'excluded': [],
            'errors': []
        }
        
        for pref in preferences:
            file_id = pref.get('file_id')
            file_path = pref.get('file_path')
            mime_type = pref.get('mime_type', '')
            is_included = pref.get('is_included', True)  # Default to True
            
            logger.info(f"📄 Processing file {file_id} ({file_path}): include={is_included}, mime={mime_type}")
            
            if not file_id or not file_path:
                error_msg = 'Missing file_id or file_path'
                logger.warning(f"⚠️ {error_msg} for preference: {pref}")
                results['errors'].append({
                    'file_id': file_id,
                    'error': error_msg
                })
                continue
            
            try:
                if is_included:
                    # Check if file is embeddable before including
                    file_info = {
                        'mimeType': mime_type,
                        'id': file_id,
                        'name': file_path
                    }
                    
                    if not is_embeddable_file(file_info):
                        error_msg = f'File type not supported for embedding/processing: {mime_type}'
                        logger.warning(f"⚠️ {error_msg} for file {file_id}")
                        results['errors'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'error': error_msg
                        })
                        continue
                    
                    # Include file - create or update in database
                    preference, created = GoogleDriveFilePreference.objects.update_or_create(
                        workspace=workspace,
                        file_id=file_id,
                        defaults={
                            'file_path': file_path,
                            'is_included': True,
                        }
                    )
                    
                    logger.info(f"✅ File preference {'created' if created else 'updated'} for {file_id}: {file_path}")
                    
                    # Do not touch Chroma here; start_embedding_process handles embedding
                    
                    results['included'].append({
                        'file_id': file_id,
                        'file_path': file_path,
                        'created': created,
                        'queued': False
                    })
                else:
                    # Exclude file - delete from database only (no Chroma action in batch)
                    try:
                        preference = GoogleDriveFilePreference.objects.get(
                            workspace=workspace,
                            file_id=file_id
                        )
                        
                        # Then delete from database
                        preference.delete()
                        logger.info(f"✅ File preference deleted for {file_id}: {file_path}")
                        
                        results['excluded'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'deleted': True,
                            'queued': False
                        })
                    except GoogleDriveFilePreference.DoesNotExist:
                        # Already not in database
                        logger.info(f"ℹ️ File preference already not in database for {file_id}")
                        results['excluded'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'deleted': False,
                            'queued': False
                        })
                        
            except Exception as e:
                error_msg = str(e)
                logger.error(f"❌ Error processing file {file_id}: {error_msg}")
                results['errors'].append({
                    'file_id': file_id,
                    'error': error_msg
                })
        
        logger.info(f"🔄 Batch processing completed: {len(results['included'])} included, {len(results['excluded'])} excluded, {len(results['errors'])} errors")
        
        return Response({
            'success': True,
            'results': results,
            'summary': {
                'total_processed': len(preferences),
                'included_count': len(results['included']),
                'excluded_count': len(results['excluded']),
                'error_count': len(results['errors'])
            }
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        logger.error(f"❌ Workspace {workspace_id} not found or not active")
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"❌ Error in batch_update_preferences: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def process_folder_files(request):
    """
    Process all files in selected folders - works around file path issues.
    This endpoint accepts folder IDs and processes all files within them.
    """
    workspace_id = request.data.get('workspace_id')
    folder_ids = request.data.get('folder_ids', [])  # Array of folder IDs to process
    
    logger.info(f"🔄 Processing folders for workspace {workspace_id}")
    logger.info(f"📁 Received {len(folder_ids)} folder IDs: {folder_ids}")
    
    if not workspace_id or not folder_ids:
        return Response({'error': 'workspace_id and folder_ids array required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        logger.info(f"✅ Found active workspace: {workspace.id}")
        
        drive_client = get_drive_client(workspace)
        if not drive_client:
            return Response({'error': 'Failed to authenticate with Google Drive'}, status=401)
        
        results = {
            'included': [],
            'excluded': [],
            'errors': [],
            'folders_processed': []
        }
        
        total_files_processed = 0
        
        for folder_id in folder_ids:
            logger.info(f"🎯 Processing folder: {folder_id}")
            
            try:
                # Get all files recursively from this folder
                all_files = get_all_files_recursively(drive_client, folder_id)
                logger.info(f"📁 Folder {folder_id} contains {len(all_files)} files")
                
                folder_results = {
                    'folder_id': folder_id,
                    'files_found': len(all_files),
                    'files_processed': 0,
                    'errors': 0
                }
                
                for file_info in all_files:
                    file_id = file_info['id']
                    file_name = file_info['name']
                    mime_type = file_info.get('mimeType', '')
                    
                    # Skip folders
                    if mime_type == 'application/vnd.google-apps.folder':
                        continue
                    
                    # Check if file is embeddable
                    if not is_embeddable_file(file_info):
                        logger.warning(f"⚠️ Skipping non-embeddable file {file_id}: {mime_type}")
                        continue
                    
                    try:
                        # Create a proper file path using folder structure
                        # We'll use the file name as the path since the backend has path issues
                        file_path = file_name
                        
                        # Include file - create or update in database
                        preference, created = GoogleDriveFilePreference.objects.update_or_create(
                            workspace=workspace,
                            file_id=file_id,
                            defaults={
                                'file_path': file_path,
                                'is_included': True,
                            }
                        )
                        
                        logger.info(f"✅ File preference {'created' if created else 'updated'} for {file_id}: {file_name}")
                        
                        # # Add file to ChromaDB collection
                        # chroma_added = False
                        # try:
                        #     from .embedding.embedding_service import create_embedding_service
                        #     service = create_embedding_service(str(workspace.id))
                        #     if service:
                        #         embedding_result = service.add_file_to_collection(file_id)
                        #         chroma_added = embedding_result.get('success', False)
                        #         if chroma_added:
                        #             logger.info(f"✅ File {file_id} added to ChromaDB collection")
                        #         else:
                        #             logger.warning(f"⚠️ Failed to add file {file_id} to ChromaDB: {embedding_result.get('error')}")
                        #     else:
                        #         logger.error(f"❌ Failed to create embedding service for workspace {workspace.id}")
                        # except Exception as e:
                        #     logger.error(f"Error adding file {file_id} to ChromaDB: {e}")
                        
                        results['included'].append({
                            'file_id': file_id,
                            'file_path': file_path,
                            'file_name': file_name,
                            'created': created,
                            # 'chroma_added': chroma_added
                        })
                        
                        folder_results['files_processed'] += 1
                        total_files_processed += 1
                        
                    except Exception as e:
                        error_msg = str(e)
                        logger.error(f"❌ Error processing file {file_id}: {error_msg}")
                        results['errors'].append({
                            'file_id': file_id,
                            'file_name': file_name,
                            'error': error_msg
                        })
                        folder_results['errors'] += 1
                
                results['folders_processed'].append(folder_results)
                logger.info(f"✅ Folder {folder_id} processed: {folder_results['files_processed']} files, {folder_results['errors']} errors")
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"❌ Error processing folder {folder_id}: {error_msg}")
                results['errors'].append({
                    'folder_id': folder_id,
                    'error': error_msg
                })
        
        logger.info(f"🔄 Folder processing completed: {total_files_processed} total files processed")
        
        return Response({
            'success': True,
            'message': f'Processed {len(folder_ids)} folders with {total_files_processed} files',
            'results': results,
            'summary': {
                'folders_processed': len(folder_ids),
                'total_files_processed': total_files_processed,
                'included_count': len(results['included']),
                'error_count': len(results['errors'])
            }
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        logger.error(f"❌ Workspace {workspace_id} not found or not active")
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"❌ Error in process_folder_files: {e}")
        return Response({'error': 'Internal server error'}, status=500)


# Removed add_pattern_rule - no longer needed with simplified preferences


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def list_folder_files(request):
    """
    List files in the workspace folder with current inclusion state.
    Supports both GET and POST for compatibility with frontend.
    """
    if request.method == 'POST':
        workspace_id = request.data.get('workspace_id')
        folder_id = request.data.get('folder_id')
    else:
        workspace_id = request.GET.get('workspace_id')
        folder_id = request.GET.get('folder_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id parameter required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        drive_client = get_drive_client(workspace)
        
        if not drive_client:
            return Response({'error': 'Failed to authenticate with Google Drive'}, status=401)
        
        # Use folder_id from request or workspace default
        target_folder_id = folder_id or workspace.folder_id or 'root'
        
        # Get files from Google Drive
        files = drive_client.list_files(folder_id=target_folder_id)
        # Filter: keep folders and embeddable files only
        files = [
            f for f in files
            if f.get('mimeType') == 'application/vnd.google-apps.folder' or is_embeddable_file(f)
        ]
        
        # Get existing preferences for files that have explicit preferences set
        preferences = {
            pref.file_id: pref for pref in 
            GoogleDriveFilePreference.objects.filter(workspace=workspace)
            if pref.file_id  # Only get preferences with actual file_ids
        }
        
        # Build response with inclusion state
        files_with_preferences = []
        for file in files:
            file_id = file['id']
            preference = preferences.get(file_id)
            is_embeddable = is_embeddable_file(file)
            
            files_with_preferences.append({
                'id': file_id,
                'name': file['name'],
                'mimeType': file['mimeType'],
                'modifiedTime': file.get('modifiedTime'),
                'size': file.get('size'),
                'is_included': preference is not None,  # If preference exists, file is included
                'has_preference': preference is not None,
                'is_embeddable': is_embeddable,  # Whether file type can be processed
                'can_be_included': is_embeddable  # Only embeddable files can be included
            })
        
        return Response({
            'files': files_with_preferences,
            'folder_id': target_folder_id,
            'total_files': len(files_with_preferences),
            'workspace_id': workspace_id
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error listing folder files: {e}")
        return Response({'error': 'Internal server error'}, status=500) 


@api_view(['GET'])
@permission_classes([AllowAny])
def list_file_preferences(request):
    """
    List all file preferences for a workspace (flat list, no folders) with file state.
    """
    workspace_id = request.GET.get('workspace_id')
    if not workspace_id:
        return Response({'error': 'workspace_id parameter required'}, status=400)

    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)

        prefs_qs = GoogleDriveFilePreference.objects.filter(workspace=workspace)
        items = []
        for pref in prefs_qs:
            # Transform "Embedded" to "Indexed" for display purposes
            display_file_state = pref.file_state
            if pref.file_state == 'EMBEDDED':
                display_file_state = 'Indexed'
            elif pref.file_state == 'Not embedded':
                display_file_state = 'Not '

            
            items.append({
                'file_id': pref.file_id,
                'file_path': pref.file_path,
                'is_included': pref.is_included,
                'file_state': display_file_state,
                'updated_at': pref.updated_at.isoformat() if pref.updated_at else None,
            })

        return Response({
            'workspace_id': str(workspace.id),
            'total': len(items),
            'preferences': items
        })

    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error listing file preferences: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def start_embedding_process(request):
    """
    Start the embedding process for all included files in a workspace.
    This endpoint processes all files that have is_included=True in preferences.
    """
    workspace_id = request.data.get('workspace_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Get all included files from preferences
        included_files = GoogleDriveFilePreference.objects.filter(
            workspace=workspace,
            is_included=True
        )
        
        if not included_files.exists():
            return Response({
                'success': False,
                'error': 'No files are currently included for embedding'
            }, status=400)
        
        # Enqueue a single background task to process all included files and notify upon completion
        total_files = included_files.count()
        try:
            enqueue_embed_all_included_files(str(workspace.id), None)
        except Exception as e:
            logger.error(f"Error enqueuing embed-all task for workspace {workspace.id}: {e}")

        return Response({
            'success': True,
            'message': 'Your files are being processed in the background.',
            'summary': {
                'total_files': total_files,
                'mode': 'background'
            },
            'queued': True
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error starting embedding process: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_embedding_status(request):
    """
    Get the current embedding status for a workspace.
    Returns information about included files and their embedding status.
    """
    workspace_id = request.GET.get('workspace_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id parameter required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Get all file preferences for this workspace
        preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace)
        
        # Count files by status
        included_count = preferences.filter(is_included=True).count()
        excluded_count = preferences.filter(is_included=False).count()
        
        # Get some sample included files for display
        included_files = preferences.filter(is_included=True)[:10]
        file_list = []
        
        for pref in included_files:
            file_list.append({
                'file_id': pref.file_id,
                'file_path': pref.file_path,
                'created_at': pref.created_at.isoformat() if pref.created_at else None
            })
        
        return Response({
            'workspace_id': str(workspace.id),
            'company_name': workspace.company.name,
            'google_user_email': workspace.google_user_email,
            'status_summary': {
                'total_files': preferences.count(),
                'included_files': included_count,
                'excluded_files': excluded_count
            },
            'included_files_sample': file_list,
            'last_updated': workspace.updated_at.isoformat()
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error getting embedding status: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def set_root_folder(request):
    """
    Set the root folder for a Google Drive workspace.
    """
    workspace_id = request.data.get('workspace_id')
    folder_id = request.data.get('folder_id')
    folder_name = request.data.get('folder_name')
    
    if not workspace_id or not folder_id or not folder_name:
        return Response({'error': 'workspace_id, folder_id, and folder_name required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Update workspace with new folder settings
        workspace.folder_id = folder_id
        workspace.folder_name = folder_name
        workspace.save(update_fields=['folder_id', 'folder_name'])
        
        return Response({
            'success': True,
            'message': f'Root folder set to {folder_name}',
            'folder_id': folder_id,
            'folder_name': folder_name
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error setting root folder: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def delete_google_drive_workspace(request):
    """
    Delete a Google Drive workspace and clean up all associated data.
    This includes:
    - All file preferences
    - All embeddings from ChromaDB
    - The workspace itself
    """
    workspace_id = request.data.get('workspace_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        logger.info(f"🗑️ Starting cleanup for Google Drive workspace {workspace_id}")
        
        # Step 1: Get all file preferences to know what needs to be removed from ChromaDB
        file_preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace)
        total_files = file_preferences.count()
        
        # Always attempt a full cleanup of Google Drive docs to avoid ghosts
        try:
            from .embedding.embedding_service import create_embedding_service
            service = create_embedding_service(str(workspace.id))
            if service:
                full_cleanup = service.cleanup_all_google_drive_documents()
                if not full_cleanup.get('success', False):
                    logger.error(f"❌ Full Google Drive cleanup failed: {full_cleanup.get('error')}")
            else:
                logger.error(f"❌ Failed to create embedding service for full cleanup")
        except Exception as e:
            logger.error(f"❌ Error during full Google Drive cleanup: {e}")
        
        # Step 3: Delete all file preferences
        deleted_preferences = file_preferences.delete()
        logger.info(f"🗑️ Deleted {deleted_preferences[0]} file preferences")
        
        # Step 4: Delete the workspace
        workspace.delete()
        logger.info(f"🗑️ Deleted Google Drive workspace {workspace_id}")
        
        return Response({
            'success': True,
            'message': f'Google Drive workspace deleted successfully',
            'cleanup_summary': {
                'total_files_processed': total_files,
                'preferences_deleted': deleted_preferences[0],
                'workspace_deleted': True
            }
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error deleting Google Drive workspace: {e}")
        return Response({'error': 'Internal server error'}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def disconnect_google_drive(request):
    """
    Disconnect Google Drive integration for a company.
    This will:
    - Mark workspace as inactive
    - Remove all embeddings from ChromaDB
    - Keep file preferences for potential reconnection
    """
    workspace_id = request.data.get('workspace_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        logger.info(f"🔌 Disconnecting Google Drive workspace {workspace_id}")
        
        # Step 1: Get all included files to remove from ChromaDB
        included_files = GoogleDriveFilePreference.objects.filter(
            workspace=workspace,
            is_included=True
        )
        total_included = included_files.count()
        
        # Always attempt a full cleanup of Google Drive docs to avoid ghosts
        try:
            from .embedding.embedding_service import create_embedding_service
            service = create_embedding_service(str(workspace.id))
            if service:
                full_cleanup = service.cleanup_all_google_drive_documents()
                if not full_cleanup.get('success', False):
                    logger.error(f"❌ Full Google Drive cleanup failed: {full_cleanup.get('error')}")
            else:
                logger.error(f"❌ Failed to create embedding service for full cleanup")
        except Exception as e:
            logger.error(f"❌ Error during full Google Drive cleanup: {e}")
        
        # Step 3: Mark workspace as inactive
        workspace.is_active = False
        workspace.status = GoogleDriveWorkspace.Status.DISCONNECTED
        workspace.disconnected_at = timezone.now()
        workspace.save(update_fields=['is_active', 'status', 'disconnected_at'])
        
        logger.info(f"🔌 Google Drive workspace {workspace_id} marked as disconnected")
        
        return Response({
            'success': True,
            'message': f'Google Drive disconnected successfully',
            'cleanup_summary': {
                'total_files_processed': total_included,
                'workspace_disconnected': True
            }
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error disconnecting Google Drive: {e}")
        return Response({'error': 'Internal server error'}, status=500) 


@api_view(['GET'])
@permission_classes([AllowAny])
def get_collection_statistics(request):
    """
    Get ChromaDB collection statistics for a Google Drive workspace.
    """
    workspace_id = request.GET.get('workspace_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id parameter required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Get embedding service to access collection stats
        from .embedding.embedding_service import create_embedding_service
        service = create_embedding_service(str(workspace.id))
        
        if not service:
            return Response({
                'error': 'Failed to create embedding service'
            }, status=500)
        
        # Get collection statistics
        stats = service.get_collection_stats()
        
        if not stats.get('success', False):
            return Response({
                'error': f'Failed to get collection stats: {stats.get("error")}'
            }, status=500)
        
        # Get file preference counts
        total_preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace).count()
        included_preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace, is_included=True).count()
        excluded_preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace, is_included=False).count()
        
        return Response({
            'workspace_id': str(workspace.id),
            'company_name': workspace.company.name,
            'collection_stats': {
                'total_documents': stats['total_documents'],
                'collection_id': stats['collection_id']
            },
            'file_preferences': {
                'total_files': total_preferences,
                'included_files': included_preferences,
                'excluded_files': excluded_preferences
            },
            'last_updated': workspace.updated_at.isoformat()
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error getting collection statistics: {e}")
        return Response({'error': 'Internal server error'}, status=500) 


@api_view(['POST'])
@permission_classes([AllowAny])
def update_file_content(request):
    """
    Update file content in ChromaDB when a Google Drive file is modified.
    This is typically called by webhooks or sync processes.
    """
    workspace_id = request.data.get('workspace_id')
    file_id = request.data.get('file_id')
    action = request.data.get('action', 'update')  # 'update', 'delete', 'rename'
    
    if not workspace_id or not file_id:
        return Response({'error': 'workspace_id and file_id required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Check if file is included in preferences
        try:
            preference = GoogleDriveFilePreference.objects.get(
                workspace=workspace,
                file_id=file_id,
                is_included=True
            )
        except GoogleDriveFilePreference.DoesNotExist:
            return Response({
                'success': False,
                'error': 'File is not included in workspace preferences'
            }, status=400)
        
        # Get embedding service
        from .embedding.embedding_service import create_embedding_service
        service = create_embedding_service(str(workspace.id))
        
        if not service:
            return Response({
                'success': False,
                'error': 'Failed to create embedding service'
            }, status=500)
        
        if action == 'delete':
            # Remove file from ChromaDB
            result = service.remove_file_from_collection(file_id)
            if result['success']:
                logger.info(f"✅ File {file_id} removed from ChromaDB due to deletion")
            else:
                logger.warning(f"⚠️ Failed to remove deleted file {file_id} from ChromaDB: {result.get('error')}")
            
            # Delete preference since file no longer exists
            preference.delete()
            
            return Response({
                'success': True,
                'action': 'deleted',
                'file_id': file_id,
                'chroma_removed': result.get('success', False)
            })
            
        elif action == 'rename':
            # Update file path in preference
            new_path = request.data.get('new_path')
            if new_path:
                preference.file_path = new_path
                preference.save(update_fields=['file_path'])
                logger.info(f"✅ File {file_id} renamed to {new_path}")
            
            # Re-embed the file to update content
            result = service.update_file_in_collection(file_id)
            
            return Response({
                'success': True,
                'action': 'renamed',
                'file_id': file_id,
                'new_path': new_path,
                'chroma_updated': result.get('success', False)
            })
            
        else:  # update
            # Re-embed the file to update content
            result = service.update_file_in_collection(file_id)
            
            return Response({
                'success': True,
                'action': 'updated',
                'file_id': file_id,
                'chroma_updated': result.get('success', False)
            })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error updating file content: {e}")
        return Response({'error': 'Internal server error'}, status=500) 


@api_view(['POST'])
@permission_classes([AllowAny])
def sync_file_preferences(request):
    """
    Sync file preferences with ChromaDB collection.
    This ensures ChromaDB only contains files that are currently included.
    """
    workspace_id = request.data.get('workspace_id')
    preferences = request.data.get('preferences', [])
    
    if not workspace_id or not preferences:
        return Response({'error': 'workspace_id and preferences array required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Get embedding service
        from .embedding.embedding_service import create_embedding_service
        service = create_embedding_service(str(workspace.id))
        
        if not service:
            return Response({
                'error': 'Failed to create embedding service'
            }, status=500)
        
        # Sync preferences with ChromaDB
        sync_result = service.sync_file_preferences(preferences)
        
        if sync_result.get('success', False):
            return Response({
                'success': True,
                'message': 'File preferences synced with ChromaDB',
                'sync_summary': {
                    'total_files': sync_result['total_files'],
                    'added': sync_result['added'],
                    'removed': sync_result['removed'],
                    'errors': len(sync_result['errors'])
                },
                'errors': sync_result.get('errors', [])
            })
        else:
            return Response({
                'success': False,
                'error': f'Sync failed: {sync_result.get("error")}'
            }, status=500)
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error syncing file preferences: {e}")
        return Response({'error': 'Internal server error'}, status=500) 


@api_view(['POST'])
@permission_classes([AllowAny])
def remove_all_file_preferences(request):
    """
    Remove all file preferences for a workspace and clean up ChromaDB.
    This is useful for resetting the workspace to default state.
    """
    workspace_id = request.data.get('workspace_id')
    
    if not workspace_id:
        return Response({'error': 'workspace_id required'}, status=400)
    
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        
        # Get all file preferences
        preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace)
        total_preferences = preferences.count()
        
        if total_preferences == 0:
            return Response({
                'success': True,
                'message': 'No file preferences found',
                'total_removed': 0
            })
        
        # Remove from ChromaDB first
        chroma_removed = 0
        try:
            from .embedding.embedding_service import create_embedding_service
            service = create_embedding_service(str(workspace.id))
            if service:
                # Get all file IDs to remove (best-effort targeted cleanup)
                file_ids = list(preferences.values_list('file_id', flat=True))
                file_ids = [fid for fid in file_ids if fid]
                if file_ids:
                    cleanup_result = service.cleanup_workspace_files(file_ids)
                    chroma_removed = cleanup_result.get('removed', 0)
                    logger.info(f"✅ Removed {chroma_removed} files from ChromaDB during bulk cleanup")
                else:
                    logger.info("ℹ️ No valid file IDs found for targeted ChromaDB cleanup")

                # Always run a full cleanup to remove any ghost Google Drive documents
                try:
                    full_cleanup = service.cleanup_all_google_drive_documents()
                    if full_cleanup.get('success', False):
                        extra_removed = full_cleanup.get('removed', 0)
                        # Sum to reflect total documents removed across both passes
                        chroma_removed += extra_removed
                        logger.info(f"🧹 Full Google Drive cleanup removed {extra_removed} additional documents")
                    else:
                        logger.error(f"❌ Full Google Drive cleanup failed: {full_cleanup.get('error')}")
                except Exception as e:
                    logger.error(f"Error during full Google Drive cleanup: {e}")
            else:
                logger.error(f"❌ Failed to create embedding service for workspace {workspace.id}")
        except Exception as e:
            logger.error(f"Error during ChromaDB cleanup: {e}")
        
        # Delete all preferences from database
        preferences.delete()
        
        logger.info(f"✅ Removed all {total_preferences} file preferences for workspace {workspace.id}")
        
        return Response({
            'success': True,
            'message': f'Removed {total_preferences} file preferences',
            'total_removed': total_preferences,
            'chroma_removed': chroma_removed
        })
        
    except GoogleDriveWorkspace.DoesNotExist:
        return Response({'error': 'Workspace not found'}, status=404)
    except Exception as e:
        logger.error(f"Error removing all file preferences: {e}")
        return Response({'error': str(e)}, status=500) 
