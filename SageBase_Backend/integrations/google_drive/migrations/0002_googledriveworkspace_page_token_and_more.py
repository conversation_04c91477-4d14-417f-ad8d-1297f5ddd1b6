# Generated by Django 4.2 on 2025-07-30 03:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('google_drive', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='googledriveworkspace',
            name='page_token',
            field=models.CharField(blank=True, help_text='Last Drive startPageToken cursor', max_length=256, null=True),
        ),
        migrations.AddField(
            model_name='googledriveworkspace',
            name='sync_status',
            field=models.Char<PERSON>ield(choices=[('OK', 'OK'), ('ERROR', 'Error')], default='OK', help_text='Result of the last poll run', max_length=20),
        ),
    ]
