# Generated by Django 4.2 on 2025-08-18 11:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("google_drive", "0005_googledrivefilepreference_file_state"),
    ]

    operations = [
        migrations.AlterField(
            model_name="googledrivefilepreference",
            name="file_state",
            field=models.CharField(
                choices=[
                    ("NOT_EMBEDDED", "Not embedded"),
                    ("EMBEDDED", "Embedded"),
                    ("MODIFIED", "Modified"),
                    ("TRASHED", "Trashed"),
                    ("REMOVED", "Removed"),
                ],
                default="NOT_EMBEDDED",
                help_text="Embedding/polling state of the file",
                max_length=20,
            ),
        ),
    ]
