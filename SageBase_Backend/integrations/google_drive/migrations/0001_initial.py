# Generated by Django 4.2 on 2025-07-28 10:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('integrations', '0003_teaminvitation'),
    ]

    operations = [
        migrations.CreateModel(
            name='GoogleDriveWorkspace',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('workspace', models.CharField(max_length=255)),
                ('folder_id', models.CharField(help_text='Google Drive root folder ID', max_length=255)),
                ('folder_name', models.CharField(blank=True, help_text='Human-readable folder name', max_length=500)),
                ('credentials_json', models.TextField(help_text='OAuth2 credentials as JSON')),
                ('google_user_email', models.<PERSON><PERSON><PERSON>ield(help_text='Google account email', max_length=254)),
                ('status', models.CharField(choices=[('DISCONNECTED', 'Disconnected'), ('CONNECTED', 'Connected'), ('ERROR', 'Error'), ('TOKEN_EXPIRED', 'Token Expired')], default='DISCONNECTED', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('last_sync', models.DateTimeField(blank=True, null=True)),
                ('connected_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='google_drive_workspaces', to='integrations.company')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='google_drive_workspaces', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('user', 'company', 'workspace')},
            },
        ),
        migrations.CreateModel(
            name='GoogleDriveFilePreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file_id', models.CharField(blank=True, db_index=True, help_text='Google Drive file ID', max_length=255, null=True)),
                ('file_path', models.TextField(help_text='Human-readable file path for UI')),
                ('is_included', models.BooleanField(default=False, help_text='True=include, False=exclude')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='file_preferences', to='google_drive.googledriveworkspace')),
            ],
        ),
        migrations.AddIndex(
            model_name='googledrivefilepreference',
            index=models.Index(fields=['workspace', 'file_id'], name='google_driv_workspa_7a6c23_idx'),
        ),
        migrations.AddIndex(
            model_name='googledrivefilepreference',
            index=models.Index(fields=['workspace', 'is_included'], name='google_driv_workspa_f77c2a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='googledrivefilepreference',
            unique_together={('workspace', 'file_id')},
        ),
    ]
