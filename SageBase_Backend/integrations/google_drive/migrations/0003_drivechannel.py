# Generated by Django 4.2 on 2025-07-31 01:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('google_drive', '0002_googledriveworkspace_page_token_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DriveChannel',
            fields=[
                ('id', models.CharField(max_length=128, primary_key=True, serialize=False)),
                ('resource_id', models.Char<PERSON>ield(max_length=256)),
                ('page_token', models.Char<PERSON>ield(max_length=256)),
                ('expires_at', models.DateTimeField()),
                ('secret', models.CharField(max_length=128)),
                ('last_msg_no', models.BigIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('workspace', models.Foreign<PERSON>ey(on_delete=django.db.models.deletion.CASCADE, to='google_drive.googledriveworkspace')),
            ],
        ),
    ]
