# integrations/google_drive/management/commands/validate_drive_channels.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from integrations.google_drive.models import GoogleDriveWorkspace, DriveChannel
from integrations.google_drive.watcher.bootstrap import (
    ensure_channel, 
    renew_channel, 
    cleanup_expired_channels,
    validate_channel
)
import logging

logger = logging.getLogger("drive.watcher")

class Command(BaseCommand):
    help = "Validate and maintain Google Drive channels - check, renew, and cleanup as needed."

    def add_arguments(self, parser):
        parser.add_argument(
            '--force-renew',
            action='store_true',
            help='Force renewal of all channels regardless of expiration',
        )
        parser.add_argument(
            '--cleanup-only',
            action='store_true',
            help='Only cleanup expired channels, do not validate or renew',
        )
        parser.add_argument(
            '--workspace-id',
            type=str,
            help='Validate channels for specific workspace ID only',
        )

    def handle(self, *args, **options):
        force_renew = options['force_renew']
        cleanup_only = options['cleanup_only']
        workspace_id = options['workspace_id']
        
        self.stdout.write("=== Google Drive Channel Validation ===\n")
        
        # Cleanup expired channels first
        if cleanup_only:
            expired_count = cleanup_expired_channels()
            self.stdout.write(f"🧹 Cleaned up {expired_count} expired channels")
            return
        
        # Get workspaces to process
        if workspace_id:
            workspaces = GoogleDriveWorkspace.objects.filter(
                id=workspace_id,
                status="CONNECTED",
                is_active=True
            )
        else:
            workspaces = GoogleDriveWorkspace.objects.filter(
                status="CONNECTED",
                is_active=True
            )
        
        self.stdout.write(f"Found {workspaces.count()} CONNECTED workspaces")
        
        total_channels = 0
        valid_channels = 0
        renewed_channels = 0
        created_channels = 0
        
        for workspace in workspaces:
            self.stdout.write(f"\nProcessing workspace: {workspace.id} ({workspace.google_user_email})")
            
            # Get channels for this workspace
            channels = DriveChannel.objects.filter(workspace=workspace)
            total_channels += channels.count()
            
            if not channels.exists():
                self.stdout.write("  ⚠️  No channels found - creating new channel")
                try:
                    ensure_channel(workspace)
                    created_channels += 1
                    self.stdout.write("  ✅ Created new channel")
                except Exception as e:
                    self.stdout.write(f"  ❌ Failed to create channel: {e}")
                continue
            
            # Validate each channel
            for channel in channels:
                self.stdout.write(f"  Checking channel: {channel.id}")
                
                # Check if channel is valid
                is_valid = validate_channel(channel)
                
                if is_valid:
                    valid_channels += 1
                    self.stdout.write("    ✅ Channel is valid")
                    
                    # Check if renewal is needed
                    if force_renew or self._should_renew(channel):
                        self.stdout.write("    🔄 Renewing channel...")
                        try:
                            renew_channel(channel)
                            renewed_channels += 1
                            self.stdout.write("    ✅ Channel renewed successfully")
                        except Exception as e:
                            self.stdout.write(f"    ❌ Failed to renew channel: {e}")
                else:
                    self.stdout.write("    ⚠️  Channel is invalid - will be cleaned up")
        
        # Cleanup expired channels
        expired_count = cleanup_expired_channels()
        
        # Summary
        self.stdout.write("\n=== Summary ===")
        self.stdout.write(f"📊 Total channels processed: {total_channels}")
        self.stdout.write(f"✅ Valid channels: {valid_channels}")
        self.stdout.write(f"🔄 Renewed channels: {renewed_channels}")
        self.stdout.write(f"🎉 Created channels: {created_channels}")
        self.stdout.write(f"🧹 Cleaned up expired channels: {expired_count}")
        
        if valid_channels == total_channels and expired_count == 0:
            self.stdout.write(self.style.SUCCESS("🎉 All channels are healthy!"))
        else:
            self.stdout.write(self.style.WARNING("⚠️  Some channels need attention"))
    
    def _should_renew(self, channel):
        """Check if channel should be renewed based on expiration time."""
        if not channel.expires_at:
            return False
        
        # Ensure timezone awareness
        if timezone.is_naive(channel.expires_at):
            channel.expires_at = channel.expires_at.replace(tzinfo=timezone.utc)
        
        from datetime import timedelta
        time_until_expiry = channel.expires_at - timezone.now()
        return time_until_expiry < timedelta(hours=24)  # Renew if expiring within 24 hours 