import time
import pprint
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from integrations.google_drive.models import GoogleDriveWorkspace
from integrations.google_drive.services import get_drive_client


class Command(BaseCommand):
    """
    Poll Google Drive `changes.list` for a SINGLE workspace and pretty-print
    every modification as soon as it appears.

    Usage:
        python manage.py debug_drive_watch <workspace_id|google_email>
    """

    help = "Continuously print Drive changes for one workspace (debug only)"

    def add_arguments(self, parser):
        parser.add_argument("selector",
                            help="Workspace UUID or google_user_email")

    # ---------------------------------------------------------------------- #
    def handle(self, selector, *args, **opts):
        # ── locate workspace ------------------------------------------------
        try:
            ws = (GoogleDriveWorkspace.objects
                  .get(id=selector) if len(selector) == 36            # uuid4
                  else GoogleDriveWorkspace.objects.get(
                          google_user_email__iexact=selector))
        except GoogleDriveWorkspace.DoesNotExist:
            raise CommandError(f"No workspace found for “{selector}”")

        self.stdout.write(
            self.style.SUCCESS(f"🎧 Watching Drive changes for {ws.google_user_email}")
        )

        # ── auth client -----------------------------------------------------
        drive = get_drive_client(ws)
        svc   = drive.service

        # starting cursor
        page_token = (svc.changes()
                         .getStartPageToken(supportsAllDrives=True)
                         .execute()["startPageToken"])

        pp = pprint.PrettyPrinter(indent=2, compact=True, width=120)

        # ── endless loop ----------------------------------------------------
        while True:
            resp = (svc.changes()
                      .list(pageToken=page_token,
                            includeRemoved=True,
                            supportsAllDrives=True,
                            includeItemsFromAllDrives=True,
                            pageSize=1000)
                      .execute())

            for ch in resp.get("changes", []):
                fid   = ch["fileId"]
                state = (
                    "REMOVED"  if ch.get("removed") else
                    "TRASHED"  if ch.get("file", {}).get("trashed") else
                    "MODIFIED"
                )
                ts = datetime.utcnow().isoformat(timespec="seconds")
                self.stdout.write(f"[{ts}] {state} → {fid}")
                pp.pprint(ch)   # full JSON, comment out if too noisy

            # advance cursor (even if zero rows)
            page_token = resp.get("newStartPageToken", page_token)
            time.sleep(1)      # 1-second poll cadence  (ctrl-C to stop)
