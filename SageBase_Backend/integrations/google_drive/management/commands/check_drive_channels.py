# integrations/google_drive/management/commands/check_drive_channels.py
from django.core.management.base import BaseCommand
from integrations.google_drive.watcher.bootstrap import ensure_all_channels_synced, check_channel_sync_status
from integrations.google_drive.models import DriveChannel
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Check and fix idle Google Drive channels to ensure webhooks are active."

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Automatically fix idle channels',
        )
        parser.add_argument(
            '--channel-id',
            type=str,
            help='Check specific channel ID',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information for each channel',
        )

    def handle(self, *args, **options):
        fix_channels = options.get('fix')
        channel_id = options.get('channel_id')
        verbose = options.get('verbose')
        
        if channel_id:
            # Check specific channel
            try:
                channel = DriveChannel.objects.get(id=channel_id)
                self.stdout.write(f"🔍 Checking channel {channel_id}...")
                
                is_synced = check_channel_sync_status(channel)
                
                if is_synced:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Channel {channel_id} is active and synced")
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f"⚠️  Channel {channel_id} is idle")
                    )
                    
                    if fix_channels:
                        self.stdout.write(f"🔄 Attempting to fix channel {channel_id}...")
                        from integrations.google_drive.watcher.bootstrap import _trigger_initial_sync
                        try:
                            _trigger_initial_sync(channel)
                            self.stdout.write(
                                self.style.SUCCESS(f"✅ Successfully fixed channel {channel_id}")
                            )
                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(f"❌ Failed to fix channel {channel_id}: {e}")
                            )
                
            except DriveChannel.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"❌ Channel {channel_id} not found")
                )
        
        else:
            # Check all channels
            if fix_channels:
                self.stdout.write("🔍 Checking and fixing all channels...")
                synced_count, total_count = ensure_all_channels_synced()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"📊 Channel status: {synced_count}/{total_count} channels active"
                    )
                )
            else:
                # Just check status without fixing
                channels = DriveChannel.objects.all()
                total_count = channels.count()
                synced_count = 0
                
                self.stdout.write(f"🔍 Checking {total_count} channels...")
                
                for channel in channels:
                    is_synced = check_channel_sync_status(channel)
                    if is_synced:
                        synced_count += 1
                        status = "✅ ACTIVE"
                    else:
                        status = "⚠️  IDLE"
                    
                    if verbose:
                        self.stdout.write(f"  {channel.id}: {status}")
                        self.stdout.write(f"    Workspace: {channel.workspace_id}")
                        self.stdout.write(f"    Expires: {channel.expires_at}")
                        self.stdout.write(f"    Last Message: {channel.last_msg_no}")
                        self.stdout.write("")
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"📊 Channel status: {synced_count}/{total_count} channels active"
                    )
                )
                
                if synced_count < total_count:
                    self.stdout.write(
                        self.style.WARNING(
                            f"⚠️  {total_count - synced_count} channels are idle. Use --fix to attempt repair."
                        )
                    ) 