# integrations/google_drive/management/commands/trigger_drive_sync.py
from django.core.management.base import BaseCommand
from integrations.google_drive.models import DriveChannel
from integrations.google_drive.watcher.bootstrap import _trigger_initial_sync
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Trigger initial sync for existing Google Drive channels to activate webhooks."

    def add_arguments(self, parser):
        parser.add_argument(
            '--channel-id',
            type=str,
            help='Specific channel ID to sync (optional)',
        )
        parser.add_argument(
            '--workspace-id',
            type=str,
            help='Workspace ID to sync all channels for (optional)',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Sync all existing channels',
        )

    def handle(self, *args, **options):
        channel_id = options.get('channel_id')
        workspace_id = options.get('workspace_id')
        sync_all = options.get('all')
        
        if channel_id:
            # Sync specific channel
            try:
                channel = DriveChannel.objects.get(id=channel_id)
                self.stdout.write(f"🔄 Triggering sync for channel {channel_id}...")
                _trigger_initial_sync(channel)
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Sync triggered for channel {channel_id}")
                )
            except DriveChannel.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"❌ Channel {channel_id} not found")
                )
        
        elif workspace_id:
            # Sync all channels for specific workspace
            channels = DriveChannel.objects.filter(workspace_id=workspace_id)
            if not channels.exists():
                self.stdout.write(
                    self.style.WARNING(f"⚠️  No channels found for workspace {workspace_id}")
                )
                return
            
            self.stdout.write(f"🔄 Triggering sync for {channels.count()} channels in workspace {workspace_id}...")
            for channel in channels:
                try:
                    _trigger_initial_sync(channel)
                    self.stdout.write(f"✅ Synced channel {channel.id}")
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Failed to sync channel {channel.id}: {e}")
                    )
        
        elif sync_all:
            # Sync all channels
            channels = DriveChannel.objects.all()
            if not channels.exists():
                self.stdout.write(
                    self.style.WARNING("⚠️  No channels found")
                )
                return
            
            self.stdout.write(f"🔄 Triggering sync for {channels.count()} channels...")
            success_count = 0
            error_count = 0
            
            for channel in channels:
                try:
                    _trigger_initial_sync(channel)
                    success_count += 1
                    self.stdout.write(f"✅ Synced channel {channel.id}")
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f"❌ Failed to sync channel {channel.id}: {e}")
                    )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Sync completed: {success_count} successful, {error_count} failed"
                )
            )
        
        else:
            self.stdout.write(
                self.style.ERROR(
                    "❌ Please specify --channel-id, --workspace-id, or --all"
                )
            ) 