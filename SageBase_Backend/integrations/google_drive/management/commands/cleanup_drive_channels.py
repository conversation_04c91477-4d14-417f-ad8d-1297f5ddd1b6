# integrations/google_drive/management/commands/cleanup_drive_channels.py
from django.core.management.base import BaseCommand
from integrations.google_drive.models import GoogleDriveWorkspace, DriveChannel
from integrations.google_drive.watcher.bootstrap import ensure_channel, renew_channel
from integrations.google_drive.services import get_drive_client
from googleapiclient.errors import HttpError
import logging

logger = logging.getLogger("drive.watcher")

class Command(BaseCommand):
    help = "Clean up existing Drive channels and recreate them for all CONNECTED workspaces."

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of all channels even if they exist',
        )
        parser.add_argument(
            '--workspace-id',
            type=str,
            help='Clean up channels for specific workspace ID only',
        )

    def handle(self, *args, **options):
        force = options['force']
        workspace_id = options['workspace_id']
        
        # Get workspaces to process
        if workspace_id:
            workspaces = GoogleDriveWorkspace.objects.filter(
                id=workspace_id,
                status="CONNECTED",
                is_active=True
            )
        else:
            workspaces = GoogleDriveWorkspace.objects.filter(
                status="CONNECTED",
                is_active=True
            )
        
        self.stdout.write(f"Found {workspaces.count()} CONNECTED workspaces")
        
        for workspace in workspaces:
            self.stdout.write(f"\nProcessing workspace: {workspace.id} ({workspace.google_user_email})")
            
            # Get existing channels for this workspace
            existing_channels = DriveChannel.objects.filter(workspace=workspace)
            
            if existing_channels.exists():
                self.stdout.write(f"  Found {existing_channels.count()} existing channel(s)")
                
                # Stop existing channels on Google's side
                for channel in existing_channels:
                    try:
                        drive_client = get_drive_client(workspace)
                        if drive_client:
                            svc = drive_client.service
                            svc.channels().stop(body={
                                "id": channel.id,
                                "resourceId": channel.resource_id,
                            }).execute()
                            self.stdout.write(f"    ✓ Stopped channel {channel.id}")
                    except HttpError as e:
                        self.stdout.write(f"    ⚠ Channel {channel.id} already stopped or invalid: {e}")
                    except Exception as e:
                        self.stdout.write(f"    ⚠ Error stopping channel {channel.id}: {e}")
                
                # Delete existing channels from database
                deleted_count = existing_channels.delete()[0]
                self.stdout.write(f"  Deleted {deleted_count} channel(s) from database")
            
            # Create new channel
            try:
                ensure_channel(workspace)
                self.stdout.write(f"  ✓ Created new channel for workspace {workspace.id}")
            except Exception as e:
                self.stdout.write(f"  ❌ Failed to create channel for workspace {workspace.id}: {e}")
        
        self.stdout.write(self.style.SUCCESS(f"\n✔ Cleanup and recreation completed for {workspaces.count()} workspaces")) 