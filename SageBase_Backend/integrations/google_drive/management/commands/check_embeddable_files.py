# integrations/google_drive/management/commands/check_embeddable_files.py
from django.core.management.base import BaseCommand
from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
from integrations.google_drive.views import EMBEDDABLE_MIME_TYPES, is_embeddable_file
from integrations.google_drive.services import get_drive_client
import logging

logger = logging.getLogger("drive.watcher")

class Command(BaseCommand):
    help = "Check which files are embeddable and monitor MIME type filtering."

    def add_arguments(self, parser):
        parser.add_argument(
            '--workspace-id',
            type=str,
            help='Check specific workspace ID',
        )
        parser.add_argument(
            '--scan-files',
            action='store_true',
            help='Scan actual files in Google Drive to check embeddable status',
        )

    def handle(self, *args, **options):
        workspace_id = options['workspace_id']
        scan_files = options['scan_files']
        
        self.stdout.write("=== Google Drive Embeddable Files Check ===\n")
        
        # Show supported MIME types
        self.stdout.write("📋 Supported MIME Types:")
        for mime_type in EMBEDDABLE_MIME_TYPES:
            self.stdout.write(f"   ✅ {mime_type}")
        
        # Get workspaces to check
        if workspace_id:
            workspaces = GoogleDriveWorkspace.objects.filter(
                id=workspace_id,
                status="CONNECTED",
                is_active=True
            )
        else:
            workspaces = GoogleDriveWorkspace.objects.filter(
                status="CONNECTED",
                is_active=True
            )
        
        if not workspaces.exists():
            self.stdout.write("❌ No CONNECTED workspaces found")
            return
        
        for workspace in workspaces:
            self.stdout.write(f"\n📁 Workspace: {workspace.id}")
            self.stdout.write(f"   Email: {workspace.google_user_email}")
            self.stdout.write(f"   Company: {workspace.company.name}")
            
            # Check current preferences
            preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace)
            included_count = preferences.filter(is_included=True).count()
            
            self.stdout.write(f"   📋 Current Preferences:")
            self.stdout.write(f"      - Included files: {included_count}")
            self.stdout.write(f"      - Total preferences: {preferences.count()}")
            
            # Scan actual files if requested
            if scan_files:
                self.stdout.write(f"   🔍 Scanning files in Google Drive...")
                try:
                    drive_client = get_drive_client(workspace)
                    if not drive_client:
                        self.stdout.write("   ❌ Failed to get drive client")
                        continue
                    
                    # Get files from root folder
                    files = drive_client.list_files(folder_id='root')
                    
                    embeddable_files = []
                    non_embeddable_files = []
                    
                    for file in files:
                        if is_embeddable_file(file):
                            embeddable_files.append(file)
                        else:
                            non_embeddable_files.append(file)
                    
                    self.stdout.write(f"   📊 File Analysis:")
                    self.stdout.write(f"      - Embeddable files: {len(embeddable_files)}")
                    self.stdout.write(f"      - Non-embeddable files: {len(non_embeddable_files)}")
                    self.stdout.write(f"      - Total files: {len(files)}")
                    
                    # Show some examples
                    if embeddable_files:
                        self.stdout.write(f"   ✅ Embeddable Examples:")
                        for file in embeddable_files[:5]:
                            self.stdout.write(f"      - {file.get('name', 'Unknown')} ({file.get('mimeType', 'Unknown')})")
                        if len(embeddable_files) > 5:
                            self.stdout.write(f"      ... and {len(embeddable_files) - 5} more")
                    
                    if non_embeddable_files:
                        self.stdout.write(f"   ⏭️  Non-embeddable Examples:")
                        for file in non_embeddable_files[:5]:
                            self.stdout.write(f"      - {file.get('name', 'Unknown')} ({file.get('mimeType', 'Unknown')})")
                        if len(non_embeddable_files) > 5:
                            self.stdout.write(f"      ... and {len(non_embeddable_files) - 5} more")
                    
                except Exception as e:
                    self.stdout.write(f"   ❌ Error scanning files: {e}")
            
            # Summary for this workspace
            if included_count > 0:
                self.stdout.write(f"   🎯 Webhook will process changes for {included_count} included files")
            else:
                self.stdout.write(f"   ⚠️  No included files - webhook will skip all changes")
        
        # Overall summary
        total_workspaces = workspaces.count()
        total_included = GoogleDriveFilePreference.objects.filter(
            workspace__in=workspaces,
            is_included=True
        ).count()
        
        self.stdout.write(f"\n=== Summary ===")
        self.stdout.write(f"📊 Total workspaces: {total_workspaces}")
        self.stdout.write(f"📋 Total included files: {total_included}")
        self.stdout.write(f"🎯 Supported MIME types: {len(EMBEDDABLE_MIME_TYPES)}")
        
        if total_included > 0:
            self.stdout.write(self.style.SUCCESS("✅ MIME type filtering is active - only embeddable files are included"))
        else:
            self.stdout.write(self.style.WARNING("⚠️  No included files found"))
        
        self.stdout.write(f"\n💡 Tip: Use --scan-files to analyze actual files in Google Drive")
        self.stdout.write(f"💡 Tip: Use --workspace-id <ID> to check specific workspace") 