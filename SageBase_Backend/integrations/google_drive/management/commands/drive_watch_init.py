# integrations/google_drive/management/commands/drive_watch_init.py
from django.core.management.base import BaseCommand
from integrations.google_drive.models import GoogleDriveWorkspace
from integrations.google_drive.watcher.bootstrap import ensure_channel

class Command(BaseCommand):
    help = "Create a Drive push-channel for every CONNECTED workspace (idempotent)."

    def handle(self, *args, **kwargs):
        qs = GoogleDriveWorkspace.objects.filter(status="CONNECTED", is_active=True)
        for ws in qs:
            ensure_channel(ws)
        self.stdout.write(self.style.SUCCESS(f"✔ ensured channels for {qs.count()} workspaces"))
