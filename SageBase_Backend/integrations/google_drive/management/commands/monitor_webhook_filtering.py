# integrations/google_drive/management/commands/monitor_webhook_filtering.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from integrations.google_drive.models import GoogleDriveWorkspace, DriveChannel, GoogleDriveFilePreference
import logging

logger = logging.getLogger("drive.watcher")

class Command(BaseCommand):
    help = "Monitor webhook filtering based on Google Drive file preferences."

    def add_arguments(self, parser):
        parser.add_argument(
            '--workspace-id',
            type=str,
            help='Monitor specific workspace ID',
        )
        parser.add_argument(
            '--show-preferences',
            action='store_true',
            help='Show detailed file preferences',
        )

    def handle(self, *args, **options):
        workspace_id = options['workspace_id']
        show_preferences = options['show_preferences']
        
        self.stdout.write("=== Google Drive Webhook Filtering Monitor ===\n")
        
        # Get workspaces to monitor
        if workspace_id:
            workspaces = GoogleDriveWorkspace.objects.filter(
                id=workspace_id,
                status="CONNECTED",
                is_active=True
            )
        else:
            workspaces = GoogleDriveWorkspace.objects.filter(
                status="CONNECTED",
                is_active=True
            )
        
        if not workspaces.exists():
            self.stdout.write("❌ No CONNECTED workspaces found")
            return
        
        for workspace in workspaces:
            self.stdout.write(f"\n📁 Workspace: {workspace.id}")
            self.stdout.write(f"   Email: {workspace.google_user_email}")
            self.stdout.write(f"   Company: {workspace.company.name}")
            
            # Check if workspace has channels
            channels = DriveChannel.objects.filter(workspace=workspace)
            if channels.exists():
                self.stdout.write(f"   ✅ Has {channels.count()} webhook channel(s)")
                for channel in channels:
                    self.stdout.write(f"      - Channel: {channel.id}")
                    self.stdout.write(f"        Expires: {channel.expires_at}")
                    self.stdout.write(f"        Last msg: {channel.last_msg_no}")
            else:
                self.stdout.write("   ⚠️  No webhook channels found")
            
            # Get file preferences
            preferences = GoogleDriveFilePreference.objects.filter(workspace=workspace)
            included_count = preferences.filter(is_included=True).count()
            excluded_count = preferences.filter(is_included=False).count()
            
            self.stdout.write(f"   📋 File Preferences:")
            self.stdout.write(f"      - Included files: {included_count}")
            self.stdout.write(f"      - Excluded files: {excluded_count}")
            self.stdout.write(f"      - Total preferences: {preferences.count()}")
            
            if show_preferences and preferences.exists():
                self.stdout.write(f"   📄 Detailed Preferences:")
                for pref in preferences[:10]:  # Show first 10
                    status = "✅ INCLUDED" if pref.is_included else "❌ EXCLUDED"
                    self.stdout.write(f"      - {status}: {pref.file_path} (ID: {pref.file_id})")
                
                if preferences.count() > 10:
                    self.stdout.write(f"      ... and {preferences.count() - 10} more")
            
            # Summary for this workspace
            if included_count > 0:
                self.stdout.write(f"   🎯 Webhook will process changes for {included_count} included files")
            else:
                self.stdout.write(f"   ⚠️  No included files - webhook will skip all changes")
        
        # Overall summary
        total_workspaces = workspaces.count()
        total_channels = DriveChannel.objects.filter(workspace__in=workspaces).count()
        total_included = GoogleDriveFilePreference.objects.filter(
            workspace__in=workspaces,
            is_included=True
        ).count()
        
        self.stdout.write(f"\n=== Summary ===")
        self.stdout.write(f"📊 Total workspaces: {total_workspaces}")
        self.stdout.write(f"📡 Total channels: {total_channels}")
        self.stdout.write(f"📋 Total included files: {total_included}")
        
        if total_included > 0:
            self.stdout.write(self.style.SUCCESS("✅ Webhook filtering is active - only included files will be processed"))
        else:
            self.stdout.write(self.style.WARNING("⚠️  No included files found - webhook will not process any changes"))
        
        self.stdout.write(f"\n💡 Tip: Use --show-preferences to see detailed file preferences")
        self.stdout.write(f"💡 Tip: Use --workspace-id <ID> to monitor specific workspace") 