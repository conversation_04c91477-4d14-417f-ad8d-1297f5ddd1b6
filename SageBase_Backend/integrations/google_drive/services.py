"""
Google Drive integration services.
"""
# std‑lib
import json
import logging
from datetime import datetime, timedelta
from typing import Optional

# 3rd‑party
from django.conf import settings
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build

# project
from .models import GoogleDriveWorkspace
from interfaces_data.google_drive.drive_client import GoogleDriveClient

logger = logging.getLogger(__name__)

REFRESH_HEADROOM = timedelta(minutes=5)           # NEW – refresh 5 min early
def get_drive_client(workspace: GoogleDriveWorkspace) -> Optional[GoogleDriveClient]:
    """
    Get authenticated Google Drive client for a workspace.
    Handles token refresh and saves updated tokens back to the database.
    
    Args:
        workspace: GoogleDriveWorkspace instance
        
    Returns:
        GoogleDriveClient instance or None if authentication fails
    """
    try:
        # Define scopes required for the Google Drive API
        scopes = [
            'https://www.googleapis.com/auth/drive.readonly',
            'https://www.googleapis.com/auth/drive.metadata.readonly'
        ]
        
        # Parse credentials from database
        creds_data = json.loads(workspace.credentials_json)
        
        # Check if we have the required fields for refresh token
        if 'refresh_token' not in creds_data:
            logger.error(f"Missing refresh_token in credentials for workspace {workspace.id}")
            workspace.status = GoogleDriveWorkspace.Status.ERROR
            workspace.save(update_fields=['status', 'updated_at'])
            return None
            
        creds = Credentials.from_authorized_user_info(creds_data, scopes)
                
        # ── 1) proactive refresh (token will expire soon) ────────────────
        if creds.expiry and creds.expiry - datetime.utcnow() < REFRESH_HEADROOM:
            logger.info("Drive token for %s expires soon – refreshing early", workspace.id)
            creds.refresh(Request())
            workspace.credentials_json = creds.to_json()
            workspace.save(update_fields=["credentials_json"])

        # ── 2) fallback: token already expired ────────────────────────────
        if creds.expired and creds.refresh_token:
            logger.info("Drive token for %s already expired – refreshing", workspace.id)
            creds.refresh(Request())
            workspace.credentials_json = creds.to_json()
            workspace.status = GoogleDriveWorkspace.Status.CONNECTED
            workspace.save(update_fields=["credentials_json", "status", "updated_at"])

        
        # Create GoogleDriveClient with proper initialization
        # We need to create it properly but override the credentials
        client = GoogleDriveClient.__new__(GoogleDriveClient)
        client.credentials_file = settings.GDRIVE_OAUTH_CREDENTIALS_FILE
        client.token_file = None
        client.credentials = creds
        
        # Initialize the service with our credentials
        client.service = build(
            "drive",
            "v3",
            credentials=creds,
            cache_discovery=False,   # ← silences the warning
        )
        
        return client
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid credentials JSON for workspace {workspace.workspace}: {e}")
        workspace.status = GoogleDriveWorkspace.Status.ERROR
        workspace.save(update_fields=['status', 'updated_at'])
        return None
        
    except Exception as e:
        logger.error(f"Failed to create drive client for workspace {workspace.workspace}: {e}")
        workspace.status = GoogleDriveWorkspace.Status.ERROR
        workspace.save(update_fields=['status', 'updated_at'])
        return None


def revoke_google_drive_authorization(workspace: GoogleDriveWorkspace) -> bool:
    """
    Revoke Google Drive authorization for a workspace.
    
    Args:
        workspace: GoogleDriveWorkspace instance
        
    Returns:
        True if revocation successful, False otherwise
    """
    try:
        # Define scopes
        scopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/drive.metadata'
        ]
        
        creds_data = json.loads(workspace.credentials_json)
        creds = Credentials.from_authorized_user_info(creds_data, scopes)
        
        # Revoke the token
        if creds.token:
            import requests
            response = requests.post(
                'https://oauth2.googleapis.com/revoke',
                params={'token': creds.token},
                headers={'content-type': 'application/x-www-form-urlencoded'}
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully revoked token for workspace {workspace.workspace}")
                workspace.status = GoogleDriveWorkspace.Status.DISCONNECTED
                workspace.is_active = False
                workspace.save(update_fields=['status', 'is_active', 'updated_at'])
                return True
            else:
                logger.warning(f"Failed to revoke token for workspace {workspace.workspace}: {response.status_code}")
                
    except Exception as e:
        logger.error(f"Error revoking authorization for workspace {workspace.workspace}: {e}")
    
    return False 
