from django.contrib import admin
from django.utils.html import format_html
from .models import GoogleDriveWorkspace, GoogleDriveFilePreference


# ────────────────────────────────────────────────────────────────────────────
#  INLINE  –  file preferences
# ────────────────────────────────────────────────────────────────────────────
class GoogleDriveFilePreferenceInline(admin.TabularInline):
    model = GoogleDriveFilePreference
    extra = 0
    fields = ["file_path", "is_included"]
    readonly_fields = ["created_at"]
    ordering = ["-updated_at"]


# ────────────────────────────────────────────────────────────────────────────
#  WORKSPACE
# ────────────────────────────────────────────────────────────────────────────
@admin.register(GoogleDriveWorkspace)
class GoogleDriveWorkspaceAdmin(admin.ModelAdmin):
    # ── LIST  --------------------------------------------------------------
    list_display = [
        "workspace",
        "company",
        "user",
        "google_user_email",
        "colored_status",          # OAuth connection
        "sync_status",             # NEW  result of last poll
        "page_token_short",        # NEW  truncated cursor
        "folder_name",
        "last_sync",
        "connected_at",
    ]
    list_filter = [
        "status",
        "sync_status",             # NEW
        "is_active",
        "company",
        "connected_at",
    ]
    search_fields = [
        "workspace",
        "folder_name",
        "google_user_email",
        "user__first_name",
        "user__last_name",
        "company__name",
    ]

    # ── DETAIL  ------------------------------------------------------------
    readonly_fields = [
        "id",
        "connected_at",
        "updated_at",
        "last_sync",
        "page_token",              # NEW – show full token in form
    ]

    fieldsets = (
        ("Workspace Information", {
            "fields": ("workspace", "user", "company")
        }),
        ("Google Drive Configuration", {
            "fields": ("folder_id", "folder_name", "google_user_email")
        }),
        ("Status & Settings", {
            "fields": ("status", "sync_status", "is_active")   # NEW sync_status
        }),
        ("Polling Cursor", {                                   # NEW tidy section
            "classes": ("collapse",),
            "fields": ("page_token", "last_sync"),
        }),
        ("Credentials", {
            "classes": ("collapse",),
            "description": "OAuth2 credentials in JSON format (sensitive data)",
            "fields": ("credentials_json",),
        }),
        ("Timestamps", {
            "classes": ("collapse",),
            "fields": ("connected_at", "updated_at"),
        }),
    )

    inlines = [GoogleDriveFilePreferenceInline]

    # ── UTILS  -------------------------------------------------------------
    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user", "company")

    def colored_status(self, obj):
        """OAuth connection status with colour."""
        colors = {
            "CONNECTED":     "green",
            "DISCONNECTED":  "gray",
            "ERROR":         "red",
            "TOKEN_EXPIRED": "orange",
        }
        color = colors.get(obj.status, "black")
        return format_html('<b style="color:{}">{}</b>', color, obj.get_status_display())
    colored_status.short_description = "OAuth status"
    colored_status.admin_order_field = "status"

    # Shortened page_token for list view
    def page_token_short(self, obj):
        return (obj.page_token[:10] + "…") if obj.page_token else "—"
    page_token_short.short_description = "page‑token"
    page_token_short.admin_order_field = "page_token"


# ────────────────────────────────────────────────────────────────────────────
#  FILE PREFERENCE
# ────────────────────────────────────────────────────────────────────────────
@admin.register(GoogleDriveFilePreference)
class GoogleDriveFilePreferenceAdmin(admin.ModelAdmin):
    list_display = ["file_path", "workspace", "colored_preference", "file_state", "updated_at"]
    list_filter  = ["is_included", "file_state", "workspace__company"]
    search_fields = ["file_path", "workspace__workspace"]
    readonly_fields = ["id", "created_at", "updated_at"]

    fieldsets = (
        ("File Information", {
            "fields": ("workspace", "file_path", "file_id")
        }),
        ("Preference", {
            "fields": ("is_included", "file_state")
        }),
        ("Timestamps", {
            "classes": ("collapse",),
            "fields": ("created_at", "updated_at"),
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("workspace", "workspace__company")

    def colored_preference(self, obj):
        if obj.is_included:
            return format_html('<span style="color:green;font-weight:bold;">✓ Include</span>')
        return format_html('<span style="color:red;font-weight:bold;">✗ Exclude</span>')
    colored_preference.short_description = "Preference"
    colored_preference.admin_order_field = "is_included"

# DriveChannel admin removed – webhooks are no longer used
