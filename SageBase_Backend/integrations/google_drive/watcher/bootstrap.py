# integrations/google_drive/watcher/bootstrap.py
import secrets, logging
from uuid import uuid4
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from integrations.google_drive.models import DriveChannel, GoogleDriveWorkspace
from integrations.google_drive.services import get_drive_client
from googleapiclient.errors import HttpError

log = logging.getLogger("drive.watcher")

RENEW_THRESHOLD = timedelta(hours=1)        # renew 1 h before death


def ensure_channel(ws: GoogleDriveWorkspace) -> None:
    """
    Create one push-channel per workspace (if it doesn't exist already),
    with supportsAllDrives so it catches *all* drives the user belongs to.
    """
    if DriveChannel.objects.filter(workspace=ws).exists():
        log.info("Channel already exists for workspace %s", ws.id)
        return

    try:
        drive = get_drive_client(ws)
        if not drive:
            raise Exception(f"Failed to get drive client for workspace {ws.id}")
            
        svc = drive.service

        # Get the latest start page token
        start_token_resp = svc.changes().getStartPageToken(
            supportsAllDrives=True
        ).execute()
        start_token = start_token_resp["startPageToken"]

        # Create channel configuration
        channel_id = uuid4().hex[:64]
        channel_token = secrets.token_hex(32)
        
        body = {
            "id": channel_id,
            "type": "web_hook",
            "address": settings.GDRIVE_WEBHOOK_URL,
            "token": channel_token,
            "params": {"ttl": "2592000"}              # 30 days (max)
        }
        
        # Create the watch channel
        resp = svc.changes().watch(
            pageToken=start_token,
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            includeRemoved=True,  # Include removed files
            body=body
        ).execute()

        # Create the channel record
        channel = DriveChannel.objects.create(
            id=channel_id,
            workspace=ws,
            resource_id=resp["resourceId"],
            page_token=start_token,
            expires_at=datetime.fromtimestamp(
                int(resp["expiration"]) / 1000, tz=timezone.utc
            ),
            secret=channel_token,
        )
        
        log.info("🎉 Drive watch channel created for workspace %s (channel: %s)", ws.id, channel_id)
        
        # Trigger initial sync by getting changes immediately
        _trigger_initial_sync(channel)
        
    except Exception as e:
        log.error("❌ Failed to create channel for workspace %s: %s", ws.id, e)
        raise


def _trigger_initial_sync(channel: DriveChannel) -> None:
    """
    Trigger an initial sync by processing any existing changes.
    This ensures the webhook gets activated and starts receiving events.
    """
    try:
        log.info("🔄 Triggering initial sync for channel %s", channel.id)
        
        # Get the drive client
        drive = get_drive_client(channel.workspace)
        if not drive:
            raise Exception(f"Failed to get drive client for workspace {channel.workspace_id}")
        
        # Get changes using the current page token
        resp = drive.service.changes().list(
            pageToken=channel.page_token,
            includeRemoved=True,
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            pageSize=100,  # Start with smaller page size for initial sync
        ).execute()
        
        changes = resp.get("changes", [])
        new_start_token = resp.get("newStartPageToken")
        
        if new_start_token:
            channel.page_token = new_start_token
            channel.save(update_fields=["page_token", "updated_at"])
            log.info("✅ Initial sync completed for channel %s, new start token: %s", channel.id, new_start_token)
        else:
            log.info("ℹ️  No changes found during initial sync for channel %s", channel.id)
        
        # Log the number of changes found
        if changes:
            log.info("📋 Found %s changes during initial sync for channel %s", len(changes), channel.id)
            
            # Process any existing changes
            from integrations.google_drive.webhooks import _process_changes
            processed = _process_changes(channel)
            if processed > 0:
                log.info("✅ Processed %s changes during initial sync for channel %s", processed, channel.id)
        
    except Exception as e:
        log.error("❌ Failed to trigger initial sync for channel %s: %s", channel.id, e)
        # Don't raise the exception - we don't want to fail channel creation if sync fails


def renew_channel(chan: DriveChannel) -> None:
    """Stop old channel, create fresh one, update the same DB row."""
    try:
        svc = get_drive_client(chan.workspace).service
        if not svc:
            raise Exception(f"Failed to get drive client for workspace {chan.workspace_id}")

        # Stop the old channel
        try:
            svc.channels().stop(body={
                "id": chan.id,
                "resourceId": chan.resource_id,
            }).execute()
            log.info("✅ Stopped old channel %s", chan.id)
        except HttpError as exc:
            log.warning("⚠️ Stopping old channel failed (may already be stopped): %s", exc)

        # Get new start token
        start_token_resp = svc.changes().getStartPageToken(
            supportsAllDrives=True
        ).execute()
        start_token = start_token_resp["startPageToken"]

        # Create new channel
        new_channel_id = uuid4().hex[:64]
        new_channel_token = secrets.token_hex(32)
        
        body = {
            "id": new_channel_id,
            "type": "web_hook",
            "address": settings.GDRIVE_WEBHOOK_URL,
            "token": new_channel_token,
            "params": {"ttl": "2592000"},
        }
        
        resp = svc.changes().watch(
            pageToken=start_token,
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            includeRemoved=True,
            body=body,
        ).execute()

        # Update the database record
        old_pk = chan.id
        new_pk = new_channel_id

        DriveChannel.objects.filter(pk=old_pk).update(
            id=new_pk,
            resource_id=resp["resourceId"],
            page_token=start_token,
            secret=new_channel_token,
            expires_at=datetime.fromtimestamp(
                int(resp["expiration"]) / 1000, tz=timezone.utc
            ),
            last_msg_no=0,
            updated_at=timezone.now(),
        )

        # Keep the in-memory object in sync
        chan.id = new_pk
        chan.resource_id = resp["resourceId"]
        chan.page_token = start_token
        chan.secret = new_channel_token
        chan.expires_at = datetime.fromtimestamp(
            int(resp["expiration"]) / 1000, tz=timezone.utc
        )
        chan.last_msg_no = 0

        log.info("🔄 Drive channel renewed for workspace %s (old: %s, new: %s)", 
                chan.workspace_id, old_pk, new_pk)
        
    except Exception as e:
        log.error("❌ Failed to renew channel %s: %s", chan.id, e)
        raise


def cleanup_expired_channels() -> int:
    """
    Clean up expired channels from the database.
    Returns the number of channels cleaned up.
    """
    try:
        now = timezone.now()
        expired_channels = DriveChannel.objects.filter(expires_at__lt=now)
        count = expired_channels.count()
        
        if count > 0:
            log.info("🧹 Cleaning up %s expired channels", count)
            expired_channels.delete()
            log.info("✅ Cleaned up %s expired channels", count)
        
        return count
        
    except Exception as e:
        log.error("❌ Failed to cleanup expired channels: %s", e)
        return 0


def validate_channel(chan: DriveChannel) -> bool:
    """
    Validate that a channel is still active and working.
    Returns True if the channel is valid, False otherwise.
    """
    try:
        if not chan.expires_at:
            return False
            
        # Check if expired
        if timezone.is_naive(chan.expires_at):
            chan.expires_at = chan.expires_at.replace(tzinfo=timezone.utc)
            
        if chan.expires_at <= timezone.now():
            log.warning("⚠️ Channel %s is expired", chan.id)
            return False
            
        # Try to access the workspace to ensure credentials are still valid
        drive = get_drive_client(chan.workspace)
        if not drive:
            log.warning("⚠️ Channel %s has invalid credentials", chan.id)
            return False
            
        return True
        
    except Exception as e:
        log.error("❌ Error validating channel %s: %s", chan.id, e)
        return False


def check_channel_sync_status(channel: DriveChannel) -> bool:
    """
    Check if a channel is properly synced and receiving webhook events.
    Returns True if the channel is active and synced, False otherwise.
    """
    try:
        # Check if channel is expired
        if channel.expires_at and channel.expires_at <= timezone.now():
            log.warning("⚠️  Channel %s is expired", channel.id)
            return False
        
        # Check if we have a valid page token
        if not channel.page_token:
            log.warning("⚠️  Channel %s has no page token", channel.id)
            return False
        
        # Try to get changes to verify the channel is working
        drive = get_drive_client(channel.workspace)
        if not drive:
            log.warning("⚠️  Channel %s has invalid credentials", channel.id)
            return False
        
        # Get a small number of changes to test the channel
        resp = drive.service.changes().list(
            pageToken=channel.page_token,
            includeRemoved=True,
            supportsAllDrives=True,
            includeItemsFromAllDrives=True,
            pageSize=1,  # Just get 1 change to test
        ).execute()
        
        # If we get a response, the channel is working
        log.info("✅ Channel %s is properly synced and active", channel.id)
        return True
        
    except Exception as e:
        log.error("❌ Channel %s sync check failed: %s", channel.id, e)
        return False


def ensure_all_channels_synced():
    """
    Ensure all existing channels are properly synced.
    This can be called periodically to check and fix idle channels.
    """
    try:
        channels = DriveChannel.objects.all()
        synced_count = 0
        total_count = channels.count()
        
        log.info("🔍 Checking sync status for %s channels", total_count)
        
        for channel in channels:
            if check_channel_sync_status(channel):
                synced_count += 1
            else:
                # Try to trigger sync for this channel
                log.info("🔄 Attempting to sync idle channel %s", channel.id)
                try:
                    _trigger_initial_sync(channel)
                    synced_count += 1
                    log.info("✅ Successfully synced channel %s", channel.id)
                except Exception as e:
                    log.error("❌ Failed to sync channel %s: %s", channel.id, e)
        
        log.info("📊 Channel sync status: %s/%s channels active", synced_count, total_count)
        return synced_count, total_count
        
    except Exception as e:
        log.error("❌ Failed to check channel sync status: %s", e)
        return 0, 0

