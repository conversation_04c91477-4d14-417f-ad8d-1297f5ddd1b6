# integrations/google_drive/signals.py
import threading
import asyncio
from django.db.models.signals import post_save
from django.dispatch          import receiver
from asgiref.sync import sync_to_async
from integrations.google_drive.models  import GoogleDriveWorkspace
from integrations.google_drive.watcher.bootstrap import ensure_channel
import logging

log = logging.getLogger("drive.watcher")

@receiver(post_save, sender=GoogleDriveWorkspace)
def create_drive_channel_on_connect(sender, instance, created, **kwargs):
    """
    Whenever a workspace record •exists• and its status flips to CONNECTED,
    make sure a Drive push-channel is in place (idempotent).
    """
    if instance.status == GoogleDriveWorkspace.Status.CONNECTED:
        # Defer channel creation to avoid async context issues
        _defer_channel_creation(instance)

def _defer_channel_creation(workspace):
    """
    Defer channel creation to a background thread to avoid async context issues.
    """
    def create_channel_async():
        """Create channel in a separate thread."""
        try:
            @sync_to_async
            def ensure_channel_sync(workspace):
                ensure_channel(workspace)
            
            # Run in a new event loop
            def run_async_create():
                async def async_create():
                    try:
                        await ensure_channel_sync(workspace)
                        log.info("✅ Created Drive channel for workspace %s", workspace.id)
                        
                        # Add a small delay to ensure the channel is properly established
                        import time
                        time.sleep(2)
                        
                    except Exception as e:
                        log.exception("❌ failed to create Drive channel for %s: %s", workspace.id, e)
                
                # Create new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(async_create())
                finally:
                    loop.close()
            
            # Run in a separate thread
            create_thread = threading.Thread(
                target=run_async_create,
                name=f"google-drive-channel-create-{workspace.id}",
                daemon=True
            )
            create_thread.start()
            
        except Exception as e:
            log.exception("❌ failed to start channel creation for %s: %s", workspace.id, e)
    
    # Start the channel creation in a separate thread
    create_thread = threading.Thread(
        target=create_channel_async,
        name=f"google-drive-init-{workspace.id}",
        daemon=True
    )
    create_thread.start()
