from django.urls import path
from . import views
from .webhooks import drive_webhook
urlpatterns = [
    path("start/", views.google_drive_oauth_start, name="google-drive-start"),
    path("callback/", views.google_drive_oauth_callback, name="google-drive-callback"),
    path("status/", views.google_drive_status, name="google-drive-status"),
    path("preferences/", views.update_file_preference, name="google-drive-preferences"),
    path("batch-preferences/", views.batch_update_preferences, name="google-drive-batch-preferences"),
    # Removed pattern-rules endpoint - no longer needed with simplified preferences
    path("files/", views.list_folder_files, name="google-drive-files"),
    path("webhook/", drive_webhook, name="google_drive_webhook"),       
] 