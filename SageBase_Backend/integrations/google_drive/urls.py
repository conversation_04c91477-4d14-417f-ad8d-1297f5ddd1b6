from django.urls import path
from . import views
urlpatterns = [
    path("start/", views.google_drive_oauth_start, name="google-drive-start"),
    path("callback/", views.google_drive_oauth_callback, name="google-drive-callback"),
    path("status/", views.google_drive_status, name="google-drive-status"),
    path("preferences/", views.update_file_preference, name="google-drive-preferences"),
    path("batch-preferences/", views.batch_update_preferences, name="google-drive-batch-preferences"),
    path("remove-all-preferences/", views.remove_all_file_preferences, name="google-drive-remove-all-preferences"),
    # Removed pattern-rules endpoint - no longer needed with simplified preferences
    path("files/", views.list_folder_files, name="google-drive-files"),
    path("preferences-list/", views.list_file_preferences, name="google-drive-preferences-list"),
    # Webhook route removed – polling only
    
    # New embedding endpoints
    path("start-embedding/", views.start_embedding_process, name="google-drive-start-embedding"),
    path("embedding-status/", views.get_embedding_status, name="google-drive-embedding-status"),
    path("set-root-folder/", views.set_root_folder, name="google-drive-set-root-folder"),
    
    # Workspace management endpoints
    path("delete-workspace/", views.delete_google_drive_workspace, name="google-drive-delete-workspace"),
    path("disconnect/", views.disconnect_google_drive, name="google-drive-disconnect"),
    path("collection-stats/", views.get_collection_statistics, name="google-drive-collection-stats"),
    
    # File content management endpoints
    path("update-file-content/", views.update_file_content, name="google-drive-update-file-content"),
    path("sync-preferences/", views.sync_file_preferences, name="google-drive-sync-preferences"),
    path("process-folder-files/", views.process_folder_files, name="google-drive-process-folder-files"),
] 