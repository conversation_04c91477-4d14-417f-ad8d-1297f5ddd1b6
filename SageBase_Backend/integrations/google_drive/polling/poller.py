"""
Modular Google Drive poller.

• start_polling()  -> endless loop (run from a runscript / Procfile)
• _poll_single_workspace() orchestrates one workspace
• _handle_* helpers are stubs until GoogleDriveEmbedder is wired back
"""

from __future__ import annotations

import logging
import time
from typing import Dict, List
import uuid

from django.db import transaction
from django.utils import timezone

from integrations.google_drive.models import GoogleDriveWorkspace
from integrations.google_drive.services import get_drive_client
from integrations.google_drive.models import GoogleDriveFilePreference

from integrations.google_drive.embedding.tasks import (
    _work_add_file_to_collection,
    _work_remove_file_from_collection,
    _work_update_file_in_collection,
)

def included_file_ids(workspace_id: uuid.UUID) -> set[str]:
    """Return the IDs the user marked as is_included=True."""
    return set(
        GoogleDriveFilePreference.objects.filter(
            workspace_id=workspace_id,
            is_included=True,
        ).values_list("file_id", flat=True)
    )



logger = logging.getLogger(__name__)
POLL_INTERVAL = 3600        # seconds
INITIAL_SLEEP = 30       # give other services time to boot


# --------------------------------------------------------------------------- #
# public API
# --------------------------------------------------------------------------- #
def start_polling() -> None:
    """
    Blocking loop – run in its own dyno / thread / process.

    Example Procfile:
        gdrive-poller: python manage.py runscript drive_start_poller
    """
    logger.info("Sleeping %ss before starting Drive poller…", INITIAL_SLEEP)
    time.sleep(INITIAL_SLEEP)

    logger.info("📡 Drive‑poller booted (interval=%ss)", POLL_INTERVAL)
    while True:
        try:
            _poll_all_workspaces()
        except Exception as exc:              # noqa: BLE001
            logger.exception("top‑level poll error: %s", exc)
        time.sleep(POLL_INTERVAL)


# --------------------------------------------------------------------------- #
# workspace iteration
# --------------------------------------------------------------------------- #
def _poll_all_workspaces() -> None:
    logger.debug("---------- Polling all google drive changes------------")
    qs = GoogleDriveWorkspace.objects.filter(status="CONNECTED", is_active=True)
    if not qs.exists():
        logger.debug("No connected Drive workspaces – sleeping")
        return

    for ws in qs:
        try:
            _poll_single_workspace(ws)
        except Exception as exc:              # noqa: BLE001
            logger.exception("poll failed for workspace %s: %s", ws.id, exc)
            ws.sync_status = "ERROR"
            ws.save(update_fields=["sync_status", "updated_at"])


def _poll_single_workspace(ws: GoogleDriveWorkspace) -> None:
    # embedder = GoogleDriveEmbedder(ws)   # <-- wire back in later
    drive = get_drive_client(ws)

    # 1. cursor
    page_token = ws.page_token or drive.get_start_page_token()

    # 2. delta feed
    resp        = drive.get_changes(page_token)
    raw_changes = resp.get("changes", [])
    next_token  = resp.get("newStartPageToken", page_token)

    # included_ids = set(embedder.list_included_file_ids())  # later
    included_ids = included_file_ids(ws.id)     # <-- NEW : use helper of filter : TODO : update when embedder is wired back
    processed = 0

    # Aggregated stats per action
    stats = {
        "modified": {"total": 0, "success": 0, "failed": 0},
        "trashed": {"total": 0, "success": 0, "failed": 0},
        "removed": {"total": 0, "success": 0, "failed": 0},
    }

    for ch in _collapse(raw_changes):
        fid   = ch["fileId"]
        fdata = ch.get("file", {})

        if fid not in included_ids:
            continue

        # Mark preference as modified for any detected change
        try:
            GoogleDriveFilePreference.objects.filter(
                workspace_id=ws.id,
                file_id=fid
            ).update(
                file_state=GoogleDriveFilePreference.FileState.MODIFIED,
                updated_at=timezone.now(),
            )
        except Exception as exc:
            logger.warning("Failed to update file_state to MODIFIED for %s: %s", fid, exc)

        if ch.get("removed"):
            action = "removed"
            stats[action]["total"] += 1
            try:
                # Remove from collection and mark preference state
                _work_remove_file_from_collection(str(ws.id), fid)
                GoogleDriveFilePreference.objects.filter(
                    workspace_id=ws.id,
                    file_id=fid,
                ).update(
                    file_state=GoogleDriveFilePreference.FileState.REMOVED,
                    updated_at=timezone.now(),
                )
                stats[action]["success"] += 1
                logger.info("🗑️  handled removed %s", fid)
            except Exception as exc:  # noqa: BLE001
                stats[action]["failed"] += 1
                logger.warning("Failed to handle removed %s: %s", fid, exc)
        elif fdata.get("trashed"):
            action = "trashed"
            stats[action]["total"] += 1
            try:
                # Remove from collection and mark preference state
                _work_remove_file_from_collection(str(ws.id), fid)
                GoogleDriveFilePreference.objects.filter(
                    workspace_id=ws.id,
                    file_id=fid,
                ).update(
                    file_state=GoogleDriveFilePreference.FileState.TRASHED,
                    updated_at=timezone.now(),
                )
                stats[action]["success"] += 1
                logger.info("🚮 handled trashed %s", fid)
            except Exception as exc:  # noqa: BLE001
                stats[action]["failed"] += 1
                logger.warning("Failed to handle trashed %s: %s", fid, exc)
        else:
            action = "modified"
            stats[action]["total"] += 1
            try:
                # Choose add vs update based on current preference state
                pref = GoogleDriveFilePreference.objects.filter(
                    workspace_id=ws.id,
                    file_id=fid,
                ).first()

                if pref and pref.file_state in (
                    GoogleDriveFilePreference.FileState.NOT_EMBEDDED,
                    GoogleDriveFilePreference.FileState.REMOVED,
                    GoogleDriveFilePreference.FileState.TRASHED,
                ):
                    _work_add_file_to_collection(str(ws.id), fid)
                else:
                    _work_update_file_in_collection(str(ws.id), fid)

                # On success mark as EMBEDDED
                GoogleDriveFilePreference.objects.filter(
                    workspace_id=ws.id,
                    file_id=fid,
                ).update(
                    file_state=GoogleDriveFilePreference.FileState.EMBEDDED,
                    updated_at=timezone.now(),
                )
                stats[action]["success"] += 1
                logger.info("✏️  handled modified %s", fid)
            except Exception as exc:  # noqa: BLE001
                stats[action]["failed"] += 1
                logger.warning("Failed to handle modified %s: %s", fid, exc)

        processed += 1

    # 3. persist cursor + bookkeeping
    ws.page_token  = next_token
    ws.last_sync   = timezone.now()
    ws.sync_status = "OK"
    with transaction.atomic():
        ws.save(update_fields=["page_token", "last_sync", "sync_status", "updated_at"])

    if processed:
        logger.info(
            "Workspace %s – processed %s change(s), new token %s",
            ws.id, processed, next_token,
        )


# --------------------------------------------------------------------------- #
# util
# --------------------------------------------------------------------------- #
def _collapse(changes: List[Dict]) -> List[Dict]:
    """
    Drive may emit multiple rows for the same file within one delta feed.
    Keep only the last row per fileId (feed is newest→oldest already).
    """
    latest: Dict[str, Dict] = {}
    for ch in changes:
        latest[ch["fileId"]] = ch
    return list(latest.values())
