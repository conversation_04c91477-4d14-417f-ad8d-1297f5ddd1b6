"""
Modular Google Drive poller.

• start_polling()  -> endless loop (run from a runscript / Procfile)
• _poll_single_workspace() orchestrates one workspace
• _handle_* helpers are stubs until GoogleDriveEmbedder is wired back
"""

from __future__ import annotations

import logging
import time
from typing import Dict, List
import uuid

from django.db import transaction
from django.utils import timezone

from integrations.google_drive.models import GoogleDriveWorkspace
from integrations.google_drive.services import get_drive_client
# from integrations.google_drive.services.google_drive_embedder import GoogleDriveEmbedder


# integrations/google_drive/polling/utils.py  (or inline) --> HELPER FOR NOW (TODO : update when embedder is wired back)
from integrations.google_drive.models import GoogleDriveFilePreference

def included_file_ids(workspace_id: uuid.UUID) -> set[str]:
    """Return the IDs the user marked as is_included=True."""
    return set(
        GoogleDriveFilePreference.objects.filter(
            workspace_id=workspace_id,
            is_included=True,
        ).values_list("file_id", flat=True)
    )



logger = logging.getLogger(__name__)
POLL_INTERVAL = 60        # seconds
INITIAL_SLEEP = 20        # give other services time to boot


# --------------------------------------------------------------------------- #
# public API
# --------------------------------------------------------------------------- #
def start_polling() -> None:
    """
    Blocking loop – run in its own dyno / thread / process.

    Example Procfile:
        gdrive-poller: python manage.py runscript drive_start_poller
    """
    logger.info("Sleeping %ss before starting Drive poller…", INITIAL_SLEEP)
    time.sleep(INITIAL_SLEEP)

    logger.info("📡 Drive‑poller booted (interval=%ss)", POLL_INTERVAL)
    while True:
        try:
            _poll_all_workspaces()
        except Exception as exc:              # noqa: BLE001
            logger.exception("top‑level poll error: %s", exc)
        time.sleep(POLL_INTERVAL)


# --------------------------------------------------------------------------- #
# workspace iteration
# --------------------------------------------------------------------------- #
def _poll_all_workspaces() -> None:
    qs = GoogleDriveWorkspace.objects.filter(status="CONNECTED", is_active=True)
    if not qs.exists():
        logger.debug("No connected Drive workspaces – sleeping")
        return

    for ws in qs:
        try:
            _poll_single_workspace(ws)
        except Exception as exc:              # noqa: BLE001
            logger.exception("poll failed for workspace %s: %s", ws.id, exc)
            ws.sync_status = "ERROR"
            ws.save(update_fields=["sync_status", "updated_at"])


def _poll_single_workspace(ws: GoogleDriveWorkspace) -> None:
    logger.debug("🔄 Polling workspace %s (%s)", ws.id, ws.google_user_email)

    # embedder = GoogleDriveEmbedder(ws)   # <-- wire back in later
    drive = get_drive_client(ws)

    # 1. cursor
    page_token = ws.page_token or drive.get_start_page_token()

    # 2. delta feed
    resp        = drive.get_changes(page_token)
    raw_changes = resp.get("changes", [])
    next_token  = resp.get("newStartPageToken", page_token)

    # included_ids = set(embedder.list_included_file_ids())  # later
    included_ids = included_file_ids(ws.id)     # <-- NEW : use helper of filter : TODO : update when embedder is wired back
    processed = 0

    for ch in _collapse(raw_changes):
        fid   = ch["fileId"]
        fdata = ch.get("file", {})

        if fid not in included_ids:
            continue

        if ch.get("removed"):
            # _handle_removed(embedder, fid)
            logger.info("🗑️  handled removed %s", fid)
        elif fdata.get("trashed"):
            # _handle_trashed(embedder, fid)
            logger.info("🚮 handled trashed %s", fid)
        else:
            # _handle_modified(embedder, fid, fdata)
            logger.info("✏️  handled modified %s", fid)

        processed += 1

    # 3. persist cursor + bookkeeping
    ws.page_token  = next_token
    ws.last_sync   = timezone.now()
    ws.sync_status = "OK"
    with transaction.atomic():
        ws.save(update_fields=["page_token", "last_sync", "sync_status", "updated_at"])

    if processed:
        logger.info(
            "Workspace %s – processed %s change(s), new token %s",
            ws.id, processed, next_token,
        )


# --------------------------------------------------------------------------- #
# change‑type stubs – will call embedder later
# --------------------------------------------------------------------------- #
def _handle_modified(embedder, file_id: str, meta: Dict) -> None:  # type: ignore[valid-type]
    logger.debug("✏️  handled modified %s", file_id)
    # embedder.reembed_if_changed(file_id, meta)


def _handle_trashed(embedder, file_id: str) -> None:               # type: ignore[valid-type]
    logger.debug("🚮 handled trashed %s", file_id)
    # embedder.remove_file(file_id)


def _handle_removed(embedder, file_id: str) -> None:               # type: ignore[valid-type]
    logger.debug("🗑️  handled removed %s", file_id)
    # embedder.remove_file(file_id)


# --------------------------------------------------------------------------- #
# util
# --------------------------------------------------------------------------- #
def _collapse(changes: List[Dict]) -> List[Dict]:
    """
    Drive may emit multiple rows for the same file within one delta feed.
    Keep only the last row per fileId (feed is newest→oldest already).
    """
    latest: Dict[str, Dict] = {}
    for ch in changes:
        latest[ch["fileId"]] = ch
    return list(latest.values())
