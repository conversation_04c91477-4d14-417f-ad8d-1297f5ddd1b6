# integrations/google_drive/apps.py
import logging
import threading
import asyncio
from django.apps import AppConfig
from django.conf import settings
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


class GoogleDriveConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "integrations.google_drive"

    def ready(self):
        """
        Initialise Google-Drive integration.

        ‣ registers post_save signal that auto-creates a Drive push-channel
          whenever a workspace becomes CONNECTED.

        NOTE:  No polling thread is started here; real-time webhooks handle
               all change detection for now.
        """
        # import side-effect: this registers the signal receiver
        from . import signals     # noqa: F401  (import solely for side-effects)
        
        # Defer channel initialization to avoid async context issues
        self._defer_channel_initialization()
    
    def _defer_channel_initialization(self):
        """
        Defer Google Drive channel initialization to avoid async context issues.
        This runs the initialization in a separate thread to avoid blocking
        the async startup process.
        """
        def initialize_channels():
            """Initialize Google Drive channels in a separate thread."""
            try:
                from .watcher.bootstrap import ensure_channel
                from .models import GoogleDriveWorkspace
                
                # Use sync_to_async to safely access the database
                @sync_to_async
                def get_connected_workspaces():
                    return list(GoogleDriveWorkspace.objects.filter(
                        status="CONNECTED", 
                        is_active=True
                    ))
                
                @sync_to_async
                def ensure_channel_sync(workspace):
                    ensure_channel(workspace)
                
                # Run the initialization in a new event loop
                def run_async_init():
                    async def async_init():
                        try:
                            workspaces = await get_connected_workspaces()
                            
                            for workspace in workspaces:
                                try:
                                    await ensure_channel_sync(workspace)
                                    logger.info(f" Initialized channel for workspace {workspace.id}")
                                    
                                    # Add a small delay between workspace initializations
                                    # to avoid overwhelming the Google API
                                    import time
                                    time.sleep(1)
                                    
                                except Exception as e:
                                    logger.warning(f"Failed to initialize channel for workspace {workspace.id}: {e}")
                                    
                        except Exception as e:
                            logger.error(f"Failed to initialize Google Drive channels: {e}")
                    
                    # Create new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(async_init())
                    finally:
                        loop.close()
                
                # Run in a separate thread to avoid blocking
                init_thread = threading.Thread(
                    target=run_async_init,
                    name="google-drive-channel-init",
                    daemon=True
                )
                init_thread.start()
                
                logger.info(" Google Drive channel initialization started in background thread")
                
            except Exception as e:
                logger.error(f"Failed to start Google Drive channel initialization: {e}")
        
        # Start the initialization in a separate thread
        init_thread = threading.Thread(
            target=initialize_channels,
            name="google-drive-init",
            daemon=True
        )
        init_thread.start()

