# integrations/google_drive/apps.py
import logging
import threading
import os
import atexit
if os.name != "nt":
    import fcntl
from django.apps import AppConfig
from django.conf import settings

_poller_thread_started = False
_poller_file_lock_fh = None  # keep the lock FD alive for process lifetime

logger = logging.getLogger(__name__)


class GoogleDriveConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "integrations.google_drive"

    def ready(self):
        """
        Initialise Google-Drive integration.

        Webhooks/push-channels are removed. Polling is used for change detection.
        """
        # Start poller in background thread (non-blocking)
        global _poller_thread_started
        if _poller_thread_started:
            return
        if getattr(settings, "GDRIVE_ENABLE_POLLER", True):
            try:
                # Acquire a cross-process file lock so only one poller starts
                lock_path = getattr(settings, "GDRIVE_POLLER_LOCK_FILE", "/tmp/gdrive_poller.lock")
                os.makedirs(os.path.dirname(lock_path), exist_ok=True)
                fh = open(lock_path, "a+")
                try:
                    fcntl.flock(fh.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                except BlockingIOError:
                    logger.info("Google Drive poller already running (file lock held); skipping start")
                    fh.close()
                    return

                # Keep FD alive; release at interpreter exit
                global _poller_file_lock_fh
                _poller_file_lock_fh = fh

                def _release_lock():
                    try:
                        fcntl.flock(_poller_file_lock_fh.fileno(), fcntl.LOCK_UN)
                    except Exception:
                        pass
                    try:
                        _poller_file_lock_fh.close()
                    except Exception:
                        pass

                atexit.register(_release_lock)

                from .polling import poller
                t = threading.Thread(target=poller.start_polling, name="gdrive-poller", daemon=True)
                t.start()
                _poller_thread_started = True
                logger.info("Google Drive poller started in background thread (file-lock guarded)")
            except Exception as e:
                logger.error(f"Failed to start Google Drive poller thread: {e}")

