from django.db import models
from django.contrib.auth import get_user_model
import uuid
from integrations.models import Company

User = get_user_model()


class GoogleDriveWorkspace(models.Model):
    """Google Drive workspace configuration and credentials storage"""
    
    class Status(models.TextChoices):
        DISCONNECTED = 'DISCONNECTED', 'Disconnected'
        CONNECTED = 'CONNECTED', 'Connected'
        ERROR = 'ERROR', 'Error'
        EXPIRED = 'TOKEN_EXPIRED', 'Token Expired'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='google_drive_workspaces')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='google_drive_workspaces')
    workspace = models.CharField(max_length=255)
    folder_id = models.CharField(max_length=255, help_text='Google Drive root folder ID')
    folder_name = models.Char<PERSON><PERSON>(max_length=500, blank=True, help_text='Human-readable folder name')
    
    # Store OAuth2 credentials as JSON - CRITICAL: DO NOT modify this field
    credentials_json = models.TextField(help_text='OAuth2 credentials as JSON')
    
    google_user_email = models.EmailField(help_text='Google account email')
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.DISCONNECTED)
    is_active = models.BooleanField(default=True)
    
    # Timestamps
    last_sync = models.DateTimeField(null=True, blank=True)
    connected_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # NEW FOR POLLING – see polling/poller.py
    class SyncState(models.TextChoices):
        OK     = "OK",     "OK"
        ERROR  = "ERROR",  "Error"


    page_token = models.CharField(
        max_length=256,
        blank=True,
        null=True,                 # ← optional but nice
        help_text="Last Drive startPageToken cursor",
    )

    sync_status = models.CharField(
        max_length=20,
        choices=SyncState.choices, # ← enum instead of free text
        default=SyncState.OK,
        help_text="Result of the last poll run",
    )

    
    class Meta:
        unique_together = ['user', 'company', 'workspace']
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"{self.workspace} ({self.company.name}) - {self.google_user_email}"


class GoogleDriveFilePreference(models.Model):
    """User preferences for Google Drive file inclusion/exclusion"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(GoogleDriveWorkspace, on_delete=models.CASCADE, related_name='file_preferences')
    
    # Google Drive identifiers
    file_id = models.CharField(max_length=255, null=True, blank=True, db_index=True, help_text='Google Drive file ID')
    file_path = models.TextField(help_text='Human-readable file path for UI')
    
    # Simple preference logic - just include/exclude
    is_included = models.BooleanField(default=False, help_text='True=include, False=exclude')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        # Prevent duplicate file preferences
        unique_together = ['workspace', 'file_id']
        indexes = [
            models.Index(fields=['workspace', 'file_id']),
            models.Index(fields=['workspace', 'is_included']),
        ]
    
    def clean(self):
        """Validate that file_id is provided for explicit preferences"""
        from django.core.exceptions import ValidationError
        if not self.file_id:
            raise ValidationError('file_id is required for file preferences')
    
    def __str__(self):
        action = "Include" if self.is_included else "Exclude"
        return f"{action} {self.file_path}" 



# FOR WEBHOOKS  :
# integrations/google_drive/models.py  (append once, makemigrations && migrate)

class DriveChannel(models.Model):
    id          = models.CharField(primary_key=True, max_length=128)      # X-Goog-Channel-Id
    workspace   = models.ForeignKey(GoogleDriveWorkspace, on_delete=models.CASCADE)
    resource_id = models.CharField(max_length=256)
    page_token  = models.CharField(max_length=256)
    expires_at  = models.DateTimeField()
    secret      = models.CharField(max_length=128)
    last_msg_no = models.BigIntegerField(default=0)
    created_at  = models.DateTimeField(auto_now_add=True)
    updated_at  = models.DateTimeField(auto_now=True)
