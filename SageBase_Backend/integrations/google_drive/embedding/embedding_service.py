"""
Google Drive Embedding Service

Simple service for adding and removing Google Drive files from collections.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
from .utils import process_google_drive_file, remove_file_from_collection
from vectordb.collections.collection_manager import get_collection_manager
from vectordb.models.document import DataSource

logger = logging.getLogger(__name__)


class GoogleDriveEmbeddingService:
    """
    Simple service for embedding Google Drive files into collections.
    """
    
    def __init__(self, workspace: GoogleDriveWorkspace):
        """
        Initialize the embedding service
        
        Args:
            workspace: GoogleDriveWorkspace instance
        """
        self.workspace = workspace
        self.company_id = str(workspace.company.id)
        
        logger.info(f"Initialized Google Drive embedding service for company {self.company_id}")
    
    def add_file_to_collection(self, file_id: str) -> Dict[str, Any]:
        """
        Add a Google Drive file to the company collection
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Dictionary with embedding results
        """
        try:
            logger.info(f"Adding file {file_id} to company collection {self.company_id}")
            
            # Check if file is included in preferences
            try:
                preference = GoogleDriveFilePreference.objects.get(
                    workspace=self.workspace,
                    file_id=file_id,
                    is_included=True
                )
            except GoogleDriveFilePreference.DoesNotExist:
                return {
                    'success': False,
                    'file_id': file_id,
                    'error': 'File is not included in workspace preferences'
                }
            
            # Skip if already embedded to prevent unnecessary re-embedding
            if preference.file_state == GoogleDriveFilePreference.FileState.EMBEDDED:
                logger.info(f"Skipping embedding for {file_id}: already EMBEDDED")
                return {
                    'success': True,
                    'file_id': file_id,
                    'skipped': True,
                    'reason': 'already_embedded'
                }
            
            # Mark as processing (use MODIFIED as 'in-progress' state)
            try:
                preference.file_state = GoogleDriveFilePreference.FileState.MODIFIED
                preference.save(update_fields=['file_state', 'updated_at'])
            except Exception as e:
                logger.warning(f"Failed to set file_state MODIFIED for {file_id}: {e}")

            # Process the file
            result = process_google_drive_file(self.workspace, file_id)
            
            if result['success']:
                logger.info(f"Successfully added file {file_id} to company collection")
                # Mark as embedded
                try:
                    preference.file_state = GoogleDriveFilePreference.FileState.EMBEDDED
                    preference.save(update_fields=['file_state', 'updated_at'])
                except Exception as e:
                    logger.warning(f"Failed to update file_state to EMBEDDED for {file_id}: {e}")
                
            else:
                logger.error(f"Failed to add file {file_id} to company collection: {result.get('error')}")
                # Reset to NOT_EMBEDDED on failure
                try:
                    preference.file_state = GoogleDriveFilePreference.FileState.NOT_EMBEDDED
                    preference.save(update_fields=['file_state', 'updated_at'])
                except Exception as e:
                    logger.warning(f"Failed to update file_state to NOT_EMBEDDED for {file_id}: {e}")
                
                # TODO: Update preference with error info when fields are added
                # preference.embedding_status = 'failed'
                # preference.embedding_error = result.get('error', 'Unknown error')
                # preference.save(update_fields=['embedding_status', 'embedding_error'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error adding file {file_id} to company collection: {e}")
            # Best-effort: set NOT_EMBEDDED on exception
            try:
                preference = GoogleDriveFilePreference.objects.get(
                    workspace=self.workspace,
                    file_id=file_id
                )
                preference.file_state = GoogleDriveFilePreference.FileState.NOT_EMBEDDED
                preference.save(update_fields=['file_state', 'updated_at'])
            except Exception:
                pass
            return {
                'success': False,
                'file_id': file_id,
                'error': str(e)
            }
    
    def remove_file_from_collection(self, file_id: str) -> Dict[str, Any]:
        """
        Remove a Google Drive file from the company collection
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Dictionary with removal results
        """
        try:
            logger.info(f"Removing file {file_id} from company collection {self.company_id}")
            
            # Remove from collection
            success = remove_file_from_collection(self.company_id, file_id)

            # Update preference state if present
            try:
                pref = GoogleDriveFilePreference.objects.get(
                    workspace=self.workspace,
                    file_id=file_id
                )
                pref.file_state = GoogleDriveFilePreference.FileState.NOT_EMBEDDED
                pref.save(update_fields=['file_state', 'updated_at'])
            except GoogleDriveFilePreference.DoesNotExist:
                pass
            
            # TODO: Update preference when embedding status fields are added
            # try:
            #     preference = GoogleDriveFilePreference.objects.get(
            #         workspace=self.workspace,
            #         file_id=file_id
            #     )
            #     preference.embedded_at = None
            #     preference.embedding_status = 'removed'
            #     preference.embedding_error = None
            #     preference.save(update_fields=['embedded_at', 'embedding_status', 'embedding_error'])
            # except GoogleDriveFilePreference.DoesNotExist:
            #     pass
            
            return {
                'success': success,
                'file_id': file_id,
                'removed': success
            }
            
        except Exception as e:
            logger.error(f"Error removing file {file_id} from company collection: {e}")
            return {
                'success': False,
                'file_id': file_id,
                'error': str(e)
            }
    
    def update_file_in_collection(self, file_id: str) -> Dict[str, Any]:
        """
        Update a file in the company collection (remove and re-add)
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Dictionary with update results
        """
        try:
            logger.info(f"Updating file {file_id} in company collection {self.company_id}")
            
            # First, remove the old version
            self.remove_file_from_collection(file_id)
            
            # Then add the updated version
            result = self.add_file_to_collection(file_id)
            # If update succeeded ensure state reflects EMBEDDED
            if result.get('success'):
                try:
                    pref = GoogleDriveFilePreference.objects.get(
                        workspace=self.workspace,
                        file_id=file_id
                    )
                    pref.file_state = GoogleDriveFilePreference.FileState.EMBEDDED
                    pref.save(update_fields=['file_state', 'updated_at'])
                except GoogleDriveFilePreference.DoesNotExist:
                    pass
            return result
            
        except Exception as e:
            logger.error(f"Error updating file {file_id} in company collection: {e}")
            return {
                'success': False,
                'file_id': file_id,
                'error': str(e)
            }
    
    def cleanup_workspace_files(self, file_ids: list) -> Dict[str, Any]:
        """
        Bulk cleanup all files from a workspace in ChromaDB.
        This is used when deleting/disconnecting workspaces.
        
        Args:
            file_ids: List of Google Drive file IDs to remove
            
        Returns:
            Dictionary with cleanup results
        """
        try:
            logger.info(f"Starting bulk cleanup of {len(file_ids)} files from company collection {self.company_id}")
            
            results = {
                'total_files': len(file_ids),
                'removed': 0,
                'failed': 0,
                'errors': []
            }
            
            for file_id in file_ids:
                try:
                    result = self.remove_file_from_collection(file_id)
                    if result['success']:
                        results['removed'] += 1
                        logger.debug(f"✅ Removed file {file_id} from collection")
                        # Mark state as NOT_EMBEDDED
                        try:
                            pref = GoogleDriveFilePreference.objects.get(
                                workspace=self.workspace,
                                file_id=file_id
                            )
                            pref.file_state = GoogleDriveFilePreference.FileState.NOT_EMBEDDED
                            pref.save(update_fields=['file_state', 'updated_at'])
                        except GoogleDriveFilePreference.DoesNotExist:
                            pass
                    else:
                        results['failed'] += 1
                        error_msg = result.get('error', 'Unknown error')
                        results['errors'].append({
                            'file_id': file_id,
                            'error': error_msg
                        })
                        logger.warning(f"⚠️ Failed to remove file {file_id}: {error_msg}")
                except Exception as e:
                    results['failed'] += 1
                    results['errors'].append({
                        'file_id': file_id,
                        'error': str(e)
                    })
                    logger.error(f"❌ Error removing file {file_id}: {e}")
            
            logger.info(f"Bulk cleanup completed: {results['removed']} removed, {results['failed']} failed")
            return results
            
        except Exception as e:
            logger.error(f"Error during bulk cleanup: {e}")
            return {
                'success': False,
                'total_files': len(file_ids),
                'removed': 0,
                'failed': len(file_ids),
                'error': str(e)
            }

    def cleanup_all_google_drive_documents(self) -> Dict[str, Any]:
        """
        Remove all Google Drive sourced documents from this company's collection.

        Returns:
            Dictionary with cleanup results including number removed.
        """
        try:
            collection_manager = get_collection_manager(self.company_id)
            if not collection_manager:
                return {
                    'success': False,
                    'error': 'Collection manager not found'
                }

            deleted_count = collection_manager.delete_documents_by_metadata(
                source=DataSource.GOOGLE_DRIVE,
                metadata_filters={}
            )

            logger.info(
                f"Removed {deleted_count} Google Drive documents from collection for company {self.company_id}"
            )

            return {
                'success': True,
                'removed': deleted_count
            }
        except Exception as e:  # noqa: BLE001
            logger.error(f"Error cleaning all Google Drive documents: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the current collection.
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            from vectordb.collections.collection_manager import get_collection_manager
            
            collection_manager = get_collection_manager(self.company_id)
            if not collection_manager:
                return {
                    'success': False,
                    'error': 'Collection manager not found'
                }
            
            # Get collection stats using the collection manager
            stats = collection_manager.get_all_stats()
            
            if "error" in stats:
                return {
                    'success': False,
                    'error': stats['error']
                }
            
            return {
                'success': True,
                'collection_id': self.company_id,
                'total_documents': stats.get('total_documents', 0),
                'company_id': self.company_id,
                'collection_stats': stats
            }
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def sync_file_preferences(self, preferences_data: list) -> Dict[str, Any]:
        """
        Sync file preferences with ChromaDB collection.
        This ensures ChromaDB only contains files that are currently included.
        
        Args:
            preferences_data: List of dicts with file_id and is_included status
            
        Returns:
            Dictionary with sync results
        """
        try:
            logger.info(f"🔄 Starting file preference sync for company {self.company_id}")
            
            results = {
                'total_files': len(preferences_data),
                'added': 0,
                'removed': 0,
                'errors': []
            }
            
            for pref in preferences_data:
                file_id = pref.get('file_id')
                is_included = pref.get('is_included', False)
                
                try:
                    if is_included:
                        # Add file to collection
                        result = self.add_file_to_collection(file_id)
                        if result['success']:
                            results['added'] += 1
                            logger.debug(f"✅ Added file {file_id} to collection")
                        else:
                            results['errors'].append({
                                'file_id': file_id,
                                'action': 'add',
                                'error': result.get('error', 'Unknown error')
                            })
                    else:
                        # Remove file from collection
                        result = self.remove_file_from_collection(file_id)
                        if result['success']:
                            results['removed'] += 1
                            logger.debug(f"✅ Removed file {file_id} from collection")
                        else:
                            results['errors'].append({
                                'file_id': file_id,
                                'action': 'remove',
                                'error': result.get('error', 'Unknown error')
                            })
                            
                except Exception as e:
                    results['errors'].append({
                        'file_id': file_id,
                        'action': 'add' if is_included else 'remove',
                        'error': str(e)
                    })
                    logger.error(f"❌ Error processing file {file_id}: {e}")
            
            logger.info(f"🔄 Sync completed: {results['added']} added, {results['removed']} removed, {len(results['errors'])} errors")
            return results
            
        except Exception as e:
            logger.error(f"Error during file preference sync: {e}")
            return {
                'success': False,
                'error': str(e)
            }


def create_embedding_service(workspace_id: str) -> Optional[GoogleDriveEmbeddingService]:
    """
    Create an embedding service for a Google Drive workspace
    
    Args:
        workspace_id: GoogleDriveWorkspace ID
        
    Returns:
        GoogleDriveEmbeddingService instance or None if workspace not found
    """
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        return GoogleDriveEmbeddingService(workspace)
    except GoogleDriveWorkspace.DoesNotExist:
        logger.error(f"Google Drive workspace {workspace_id} not found or not active")
        return None 