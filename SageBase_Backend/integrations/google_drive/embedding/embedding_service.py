"""
Google Drive Embedding Service

Simple service for adding and removing Google Drive files from collections.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
from .utils import process_google_drive_file, remove_file_from_collection

logger = logging.getLogger(__name__)


class GoogleDriveEmbeddingService:
    """
    Simple service for embedding Google Drive files into collections.
    """
    
    def __init__(self, workspace: GoogleDriveWorkspace):
        """
        Initialize the embedding service
        
        Args:
            workspace: GoogleDriveWorkspace instance
        """
        self.workspace = workspace
        self.company_id = str(workspace.company.id)
        
        logger.info(f"Initialized Google Drive embedding service for company {self.company_id}")
    
    def add_file_to_collection(self, file_id: str) -> Dict[str, Any]:
        """
        Add a Google Drive file to the company collection
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Dictionary with embedding results
        """
        try:
            logger.info(f"Adding file {file_id} to company collection {self.company_id}")
            
            # Check if file is included in preferences
            try:
                preference = GoogleDriveFilePreference.objects.get(
                    workspace=self.workspace,
                    file_id=file_id,
                    is_included=True
                )
            except GoogleDriveFilePreference.DoesNotExist:
                return {
                    'success': False,
                    'file_id': file_id,
                    'error': 'File is not included in workspace preferences'
                }
            
            # Process the file
            result = process_google_drive_file(self.workspace, file_id)
            
            if result['success']:
                logger.info(f"Successfully added file {file_id} to company collection")
                
                # TODO: Update preference with embedding info when fields are added
                # preference.embedded_at = datetime.now()
                # preference.embedding_status = 'success'
                # preference.save(update_fields=['embedded_at', 'embedding_status'])
                
            else:
                logger.error(f"Failed to add file {file_id} to company collection: {result.get('error')}")
                
                # TODO: Update preference with error info when fields are added
                # preference.embedding_status = 'failed'
                # preference.embedding_error = result.get('error', 'Unknown error')
                # preference.save(update_fields=['embedding_status', 'embedding_error'])
            
            return result
            
        except Exception as e:
            logger.error(f"Error adding file {file_id} to company collection: {e}")
            return {
                'success': False,
                'file_id': file_id,
                'error': str(e)
            }
    
    def remove_file_from_collection(self, file_id: str) -> Dict[str, Any]:
        """
        Remove a Google Drive file from the company collection
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Dictionary with removal results
        """
        try:
            logger.info(f"Removing file {file_id} from company collection {self.company_id}")
            
            # Remove from collection
            success = remove_file_from_collection(self.company_id, file_id)
            
            # TODO: Update preference when embedding status fields are added
            # try:
            #     preference = GoogleDriveFilePreference.objects.get(
            #         workspace=self.workspace,
            #         file_id=file_id
            #     )
            #     preference.embedded_at = None
            #     preference.embedding_status = 'removed'
            #     preference.embedding_error = None
            #     preference.save(update_fields=['embedded_at', 'embedding_status', 'embedding_error'])
            # except GoogleDriveFilePreference.DoesNotExist:
            #     pass
            
            return {
                'success': success,
                'file_id': file_id,
                'removed': success
            }
            
        except Exception as e:
            logger.error(f"Error removing file {file_id} from company collection: {e}")
            return {
                'success': False,
                'file_id': file_id,
                'error': str(e)
            }
    
    def update_file_in_collection(self, file_id: str) -> Dict[str, Any]:
        """
        Update a file in the company collection (remove and re-add)
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            Dictionary with update results
        """
        try:
            logger.info(f"Updating file {file_id} in company collection {self.company_id}")
            
            # First, remove the old version
            self.remove_file_from_collection(file_id)
            
            # Then add the updated version
            return self.add_file_to_collection(file_id)
            
        except Exception as e:
            logger.error(f"Error updating file {file_id} in company collection: {e}")
            return {
                'success': False,
                'file_id': file_id,
                'error': str(e)
            }


def create_embedding_service(workspace_id: str) -> Optional[GoogleDriveEmbeddingService]:
    """
    Create an embedding service for a Google Drive workspace
    
    Args:
        workspace_id: GoogleDriveWorkspace ID
        
    Returns:
        GoogleDriveEmbeddingService instance or None if workspace not found
    """
    try:
        workspace = GoogleDriveWorkspace.objects.get(id=workspace_id, is_active=True)
        return GoogleDriveEmbeddingService(workspace)
    except GoogleDriveWorkspace.DoesNotExist:
        logger.error(f"Google Drive workspace {workspace_id} not found or not active")
        return None 