"""
Google Drive Embedding Utilities

Simple helper methods to extract Google Drive file content and prepare it for the collection manager.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import io

from integrations.google_drive.services import get_drive_client
from integrations.google_drive.models import GoogleDriveWorkspace
from vectordb.collections.collection_manager import get_collection_manager
from vectordb.models.document import DataSource

# Import PyPDF2 for PDF extraction
try:
    from PyPDF2 import PdfReader
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("PyPDF2 not available. PDF extraction will be limited.")

logger = logging.getLogger(__name__)

# Supported MIME types for embedding
EMBEDDABLE_MIME_TYPES = {
    # Google Workspace
    'application/vnd.google-apps.document': 'google_doc',
    'application/vnd.google-apps.spreadsheet': 'google_sheet', 
    'application/vnd.google-apps.presentation': 'google_slide',
    'application/vnd.google-apps.form': 'google_form',
    
    # Microsoft Office
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
    'application/msword': 'doc',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.ms-powerpoint': 'ppt',
    
    # PDF and Text
    'application/pdf': 'pdf',
    'text/plain': 'text',
    'text/markdown': 'markdown',
    'text/csv': 'csv',
    
    # Web formats
    'text/html': 'html',
    'application/json': 'json',
    'application/xml': 'xml',
}


def extract_file_content(drive_service, file_id: str) -> Optional[Dict[str, Any]]:
    """
    Extract content from a Google Drive file
    
    Args:
        drive_service: Authenticated Google Drive service
        file_id: Google Drive file ID
        
    Returns:
        Dictionary with content and metadata, or None if extraction failed
    """
    try:
        # Get file metadata
        file_metadata = drive_service.files().get(fileId=file_id).execute()
        mime_type = file_metadata.get('mimeType', '')
        file_name = file_metadata.get('name', 'Unknown')
        
        logger.info(f"Extracting content from {file_name} ({mime_type})")
        
        # Check if file type is supported
        if mime_type not in EMBEDDABLE_MIME_TYPES:
            logger.warning(f"Unsupported MIME type: {mime_type}")
            return None
        
        content = ""
        
        # Handle Google Workspace files
        if mime_type == 'application/vnd.google-apps.document':
            content = _extract_google_doc_content(drive_service, file_id)
        elif mime_type == 'application/vnd.google-apps.spreadsheet':
            content = _extract_google_sheet_content(drive_service, file_id)
        elif mime_type == 'application/vnd.google-apps.presentation':
            content = _extract_google_slide_content(drive_service, file_id)
        elif mime_type == 'application/vnd.google-apps.form':
            content = _extract_google_form_content(drive_service, file_id)
        
        # Handle downloadable files
        elif mime_type in ['application/pdf', 'text/plain', 'text/markdown', 'text/csv', 
                          'text/html', 'application/json', 'application/xml']:
            content = _extract_downloadable_content(drive_service, file_id)
        
        # Handle Microsoft Office files
        elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                          'application/msword', 'application/vnd.ms-excel', 'application/vnd.ms-powerpoint']:
            content = _extract_office_content(drive_service, file_id)
        
        if not content:
            logger.warning(f"No content extracted from {file_name}")
            return None
        
        return {
            'content': content,
            'file_name': file_name,
            'mime_type': mime_type,
            'file_id': file_id,
            'created_at': file_metadata.get('createdTime'),
            'updated_at': file_metadata.get('modifiedTime'),
            'url': f"https://drive.google.com/file/d/{file_id}/view"
        }
        
    except Exception as e:
        logger.error(f"Error extracting content from file {file_id}: {e}")
        return None


def _extract_google_doc_content(drive_service, file_id: str) -> str:
    """Extract content from Google Docs"""
    try:
        # Export as plain text
        request = drive_service.files().export_media(fileId=file_id, mimeType='text/plain')
        content = request.execute().decode('utf-8')
        return content
    except Exception as e:
        logger.error(f"Error extracting Google Doc content: {e}")
        return ""


def _extract_google_sheet_content(drive_service, file_id: str) -> str:
    """Extract content from Google Sheets"""
    try:
        # Export as CSV
        request = drive_service.files().export_media(fileId=file_id, mimeType='text/csv')
        content = request.execute().decode('utf-8')
        return content
    except Exception as e:
        logger.error(f"Error extracting Google Sheet content: {e}")
        return ""


def _extract_google_slide_content(drive_service, file_id: str) -> str:
    """Extract content from Google Slides"""
    try:
        # Export as plain text
        request = drive_service.files().export_media(fileId=file_id, mimeType='text/plain')
        content = request.execute().decode('utf-8')
        return content
    except Exception as e:
        logger.error(f"Error extracting Google Slide content: {e}")
        return ""


def _extract_google_form_content(drive_service, file_id: str) -> str:
    """Extract content from Google Forms"""
    try:
        # Get form metadata and questions
        form_metadata = drive_service.files().get(fileId=file_id).execute()
        form_name = form_metadata.get('name', 'Unknown Form')
        
        # For forms, we'll just use the form name and description
        description = form_metadata.get('description', '')
        content = f"Form: {form_name}\nDescription: {description}"
        return content
    except Exception as e:
        logger.error(f"Error extracting Google Form content: {e}")
        return ""


def _extract_downloadable_content(drive_service, file_id: str) -> str:
    """Extract content from downloadable files"""
    try:
        request = drive_service.files().get_media(fileId=file_id)
        file_data = request.execute()
        
        # Check if it's binary data (like PDF)
        if isinstance(file_data, bytes):
            # Try to extract PDF text if PyPDF2 is available
            if PDF_AVAILABLE:
                try:
                    # Create a BytesIO object for PdfReader
                    pdf_stream = io.BytesIO(file_data)
                    pdf_reader = PdfReader(pdf_stream)
                    
                    # Extract text from all pages
                    text_content = []
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text.strip():  # Only add non-empty pages
                                text_content.append(f"Page {page_num + 1}:\n{page_text}")
                        except Exception as e:
                            logger.warning(f"Could not extract text from page {page_num + 1}: {e}")
                    
                    if text_content:
                        full_text = "\n\n".join(text_content)
                        logger.info(f"Successfully extracted {len(pdf_reader.pages)} pages from PDF")
                        return full_text
                    else:
                        logger.warning("No text content extracted from PDF")
                        return f"PDF file {file_id} contains no extractable text content"
                        
                except Exception as e:
                    logger.error(f"Error extracting PDF text: {e}")
                    return f"PDF text extraction failed for file {file_id}: {str(e)}"
            else:
                logger.warning(f"Binary file detected (likely PDF). PyPDF2 not available for text extraction.")
                return f"PDF file content extraction not available for file {file_id}. PyPDF2 library required."
        else:
            # Try to decode as text
            try:
                content = file_data.decode('utf-8')
                return content
            except UnicodeDecodeError:
                logger.warning(f"File content is not UTF-8 encoded. Attempting other encodings...")
                # Try other common encodings
                for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        content = file_data.decode(encoding)
                        logger.info(f"Successfully decoded with {encoding} encoding")
                        return content
                    except UnicodeDecodeError:
                        continue
                
                logger.error(f"Could not decode file content with any encoding")
                return f"Could not decode file content for file {file_id}"
                
    except Exception as e:
        logger.error(f"Error extracting downloadable content: {e}")
        return ""


def _extract_office_content(drive_service, file_id: str) -> str:
    """Extract content from Microsoft Office files"""
    try:
        # For Office files, we'll need to use a library like python-docx, openpyxl, etc.
        # For now, return a placeholder
        logger.warning("Office file extraction not implemented yet")
        return f"Office file content extraction not implemented for file {file_id}"
    except Exception as e:
        logger.error(f"Error extracting Office content: {e}")
        return ""


def add_file_to_collection(company_id: str, file_id: str, content: str, metadata: Dict[str, Any]) -> bool:
    """
    Add a Google Drive file to the company's collection
    
    Args:
        company_id: Company ID (used as collection name as STR)
        file_id: Google Drive file ID
        content: File content
        metadata: File metadata
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Ensure company_id is string
        company_id = str(company_id)
        
        # Get collection manager using company ID as collection name
        collection_manager = get_collection_manager(company_id)
        
        # Prepare metadata
        created_at = None
        if metadata.get('created_at'):
            created_at = datetime.fromisoformat(metadata['created_at'].replace('Z', '+00:00'))
        
        updated_at = None
        if metadata.get('updated_at'):
            updated_at = datetime.fromisoformat(metadata['updated_at'].replace('Z', '+00:00'))
        
        # Add document to collection
        document_ids = collection_manager.add_document(
            content=content,
            source=DataSource.GOOGLE_DRIVE,
            source_id=file_id,
            title=metadata.get('file_name', 'Unknown'),
            workspace=company_id,  # Use company ID as workspace
            url=metadata.get('url'),
            content_type=metadata.get('mime_type'),
            created_at=created_at,
            updated_at=updated_at,
            custom_fields={
                'google_drive_file_id': file_id,
                'google_drive_mime_type': metadata.get('mime_type')
            }
        )
        
        logger.info(f"Successfully added file {file_id} to company collection {company_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error adding file {file_id} to company collection {company_id}: {e}")
        return False


def remove_file_from_collection(company_id: str, file_id: str) -> bool:
    """
    Remove a Google Drive file from the company's collection
    
    Args:
        company_id: Company ID (used as collection name)
        file_id: Google Drive file ID
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Ensure company_id is string
        company_id = str(company_id)
        
        # Get collection manager using company ID as collection name
        collection_manager = get_collection_manager(company_id)
        
        # Use the new optimized method to delete documents directly by metadata filter
        removed_count = collection_manager.delete_documents_by_metadata(
            source=DataSource.GOOGLE_DRIVE,
            metadata_filters={'google_drive_file_id': file_id}
        )
            
        logger.info(f"Removed {removed_count} documents for file {file_id} from company collection {company_id}")
        return removed_count > 0
            
    except Exception as e:
        logger.error(f"Error removing file {file_id} from company collection {company_id}: {e}")
        return False


def process_google_drive_file(workspace: GoogleDriveWorkspace, file_id: str) -> Dict[str, Any]:
    """
    Process a single Google Drive file: extract content and add to company collection
    
    Args:
        workspace: GoogleDriveWorkspace instance
        file_id: Google Drive file ID
        
    Returns:
        Dictionary with processing results
    """
    try:
        # Get authenticated Google Drive client
        drive_client = get_drive_client(workspace)
        if not drive_client:
            return {
                'success': False,
                'file_id': file_id,
                'error': 'Failed to get authenticated Google Drive client'
            }
        
        # Extract file content
        file_data = extract_file_content(drive_client.service, file_id)
        if not file_data:
            return {
                'success': False,
                'file_id': file_id,
                'error': 'Failed to extract file content'
            }
        
        # Get company ID for collection (ensure it's string)
        company_id = str(workspace.company.id)
        
        # Add to company collection
        success = add_file_to_collection(
            company_id=company_id,
            file_id=file_id,
            content=file_data['content'],
            metadata=file_data
        )
        
        if success:
            return {
                'success': True,
                'file_id': file_id,
                'file_name': file_data['file_name'],
                'content_length': len(file_data['content']),
                'company_id': company_id
            }
        else:
            return {
                'success': False,
                'file_id': file_id,
                'error': 'Failed to add file to company collection'
            }
            
    except Exception as e:
        logger.error(f"Error processing Google Drive file {file_id}: {e}")
        return {
            'success': False,
            'file_id': file_id,
            'error': str(e)
        } 