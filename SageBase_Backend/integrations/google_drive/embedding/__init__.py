"""
Google Drive Embedding Module

This module handles the embedding of Google Drive files into collections.
"""

from .embedding_service import GoogleDriveEmbeddingService, create_embedding_service
from .utils import process_google_drive_file, add_file_to_collection, remove_file_from_collection

__all__ = [
    'GoogleDriveEmbeddingService',
    'create_embedding_service',
    'process_google_drive_file',
    'add_file_to_collection', 
    'remove_file_from_collection'
] 