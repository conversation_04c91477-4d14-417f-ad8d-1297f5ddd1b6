"""
Background task helpers for Google Drive embedding operations.

These helpers run potentially long-running embedding operations in a
separate daemon thread so HTTP requests can return immediately.
"""

from __future__ import annotations

import threading
import logging
from typing import List, Dict, Any, Optional

from integrations.google_drive.models import GoogleDriveWorkspace

logger = logging.getLogger(__name__)


def _run_in_background(target, *args, **kwargs) -> None:
    """
    Run a callable in a daemon thread. Exceptions are logged and swallowed.
    """
    def runner():
        try:
            target(*args, **kwargs)
        except Exception as e:  # noqa: BLE001
            logger.exception("Background task failed: %s", e)

    threading.Thread(target=runner, daemon=True, name="gdrive-embed-task").start()


# ──────────────────────────────────────────────────────────────────────────────
# Worker functions (synchronous) – these perform the real work
# ──────────────────────────────────────────────────────────────────────────────

def _work_add_file_to_collection(workspace_id: str, file_id: str) -> None:
    from .embedding_service import create_embedding_service
    service = create_embedding_service(workspace_id)
    if not service:
        logger.error("Embedding service not available for workspace %s", workspace_id)
        return
    result = service.add_file_to_collection(file_id)
    logger.info("Add file result (workspace=%s, file=%s): %s", workspace_id, file_id, result)


def _work_remove_file_from_collection(workspace_id: str, file_id: str) -> None:
    from .embedding_service import create_embedding_service
    service = create_embedding_service(workspace_id)
    if not service:
        logger.error("Embedding service not available for workspace %s", workspace_id)
        return
    result = service.remove_file_from_collection(file_id)
    logger.info("Remove file result (workspace=%s, file=%s): %s", workspace_id, file_id, result)


def _work_update_file_in_collection(workspace_id: str, file_id: str) -> None:
    from .embedding_service import create_embedding_service
    service = create_embedding_service(workspace_id)
    if not service:
        logger.error("Embedding service not available for workspace %s", workspace_id)
        return
    result = service.update_file_in_collection(file_id)
    logger.info("Update file result (workspace=%s, file=%s): %s", workspace_id, file_id, result)


def _work_cleanup_workspace_files(company_id: str, file_ids: List[str]) -> None:
    """
    Cleanup documents for a set of Google Drive file IDs from a company collection.
    This version does not require a live workspace record.
    """
    from .utils import remove_file_from_collection

    removed = 0
    failed = 0
    for fid in file_ids:
        try:
            ok = remove_file_from_collection(company_id, fid)
            if ok:
                removed += 1
            else:
                failed += 1
        except Exception as e:  # noqa: BLE001
            failed += 1
            logger.warning("Failed to remove file %s for company %s: %s", fid, company_id, e)

    logger.info("Bulk cleanup finished for company %s → removed=%s failed=%s", company_id, removed, failed)


def _work_sync_file_preferences(workspace_id: str, preferences: List[Dict[str, Any]]) -> None:
    from .embedding_service import create_embedding_service
    service = create_embedding_service(workspace_id)
    if not service:
        logger.error("Embedding service not available for workspace %s", workspace_id)
        return
    result = service.sync_file_preferences(preferences)
    logger.info("Sync preferences result (workspace=%s): %s", workspace_id, result)


def _work_embed_all_included_files(workspace_id: str, notify_email: Optional[str] = None) -> None:
    """
    Embed all files currently marked as included for a workspace, then notify the user.
    """
    try:
        from integrations.google_drive.models import GoogleDriveFilePreference
        from .embedding_service import create_embedding_service

        # Load workspace
        workspace: Optional[GoogleDriveWorkspace] = GoogleDriveWorkspace.objects.filter(id=workspace_id, is_active=True).first()
        if not workspace:
            logger.error("Workspace %s not found or inactive for embed-all task", workspace_id)
            return

        # Prepare service
        service = create_embedding_service(workspace_id)
        if not service:
            logger.error("Embedding service not available for workspace %s", workspace_id)
            return

        included_qs = GoogleDriveFilePreference.objects.filter(
            workspace=workspace,
            is_included=True
        )
        total_files = included_qs.count()
        success_count = 0
        failure_count = 0

        for pref in included_qs:
            # Skip re-embedding already embedded files unless marked MODIFIED
            if pref.file_state == GoogleDriveFilePreference.FileState.EMBEDDED:
                continue
            
            # Mark as processing (reuse MODIFIED state to indicate in-progress)
            try:
                pref.file_state = GoogleDriveFilePreference.FileState.MODIFIED
                pref.save(update_fields=['file_state', 'updated_at'])
            except Exception as e:
                logger.debug("Could not set MODIFIED state for %s: %s", pref.file_id, e)

            try:
                result = service.add_file_to_collection(pref.file_id)
                if result.get('success'):
                    success_count += 1
                else:
                    failure_count += 1
                    # Reset to NOT_EMBEDDED on failure
                    try:
                        pref.refresh_from_db()
                        pref.file_state = GoogleDriveFilePreference.FileState.NOT_EMBEDDED
                        pref.save(update_fields=['file_state', 'updated_at'])
                    except Exception:
                        pass
            except Exception as e:  # noqa: BLE001
                failure_count += 1
                logger.warning("Failed to embed file %s: %s", pref.file_id, e)
                try:
                    pref.refresh_from_db()
                    pref.file_state = GoogleDriveFilePreference.FileState.NOT_EMBEDDED
                    pref.save(update_fields=['file_state', 'updated_at'])
                except Exception:
                    pass

        # Create a Notification record (database only) summarizing the run
        try:
            from integrations.models import User
            from knowledge_spaces_Q_A.models import Notification as QA_Notification

            user_obj = None
            if notify_email:
                user_obj = User.objects.filter(email=notify_email, company=workspace.company, is_active=True).first()

            severity = 'high' if failure_count > 0 else 'medium'
            QA_Notification.objects.create(
                title="Google Drive Documents indexing complete",
                details=(
                    f"Successful: {success_count}\n"
                    f"Failed: {failure_count}"
                ),
                type="embedding",
                severity=severity,
                source="Google Drive",
                user=user_obj,
                company=workspace.company,
                status="pending",
                accepted_at=None,
                ignored_at=None,
            )
        except Exception as notify_err:
            logger.debug("Notification create failed: %s", notify_err)
    except Exception as e:  # noqa: BLE001
        logger.exception("embed-all background task failed: %s", e)


# ──────────────────────────────────────────────────────────────────────────────
# Public enqueue helpers – schedule background work and return immediately
# ──────────────────────────────────────────────────────────────────────────────

def enqueue_add_file_to_collection(workspace_id: str, file_id: str) -> None:
    _run_in_background(_work_add_file_to_collection, workspace_id, file_id)


def enqueue_remove_file_from_collection(workspace_id: str, file_id: str) -> None:
    _run_in_background(_work_remove_file_from_collection, workspace_id, file_id)


def enqueue_update_file_in_collection(workspace_id: str, file_id: str) -> None:
    _run_in_background(_work_update_file_in_collection, workspace_id, file_id)


def enqueue_cleanup_workspace_files_by_company(company_id: str, file_ids: List[str]) -> None:
    _run_in_background(_work_cleanup_workspace_files, company_id, file_ids)


def enqueue_sync_file_preferences(workspace_id: str, preferences: List[Dict[str, Any]]) -> None:
    _run_in_background(_work_sync_file_preferences, workspace_id, preferences)


def enqueue_embed_all_included_files(workspace_id: str, notify_email: Optional[str] = None) -> None:
    _run_in_background(_work_embed_all_included_files, workspace_id, notify_email)


