class GitHubPrompts:
    @staticmethod
    def extract_keywords_prompt(question: str) -> str:
        return f"""
Given the user question: "{question}", generate a short list of important keywords or search terms
that can help search a codebase. Be concise and return only a OR-separated string of max 4 terms like: login OR auth OR token
"""

    @staticmethod
    def extract_cross_repo_keywords_prompt(change_summary: str) -> str:
        return f"""
    You are analyzing a Git commit or code change summary for a codebase.

    Input:
    "{change_summary}"

    Your task is to extract up to 4 **technical keywords or identifiers** that might help identify related code in another codebase. Focus on:
    - Function names
    - Parameter names
    - API endpoints
    - Important object names
    - RPC paths
    Avoid common or generic words. Return the result as an `OR`-separated string, like:

    createClient OR serverUrl OR key

    Only return the list, nothing else.
    """


    @staticmethod
    def classify_intents_prompt(question: str) -> str:
        return f"""
Given the user query: "{question}"
Classify the question into one or more of the following intents (comma-separated if multiple):
code, list_commits, list_open_prs, list_closed_prs, list_prs, summarize_pr, list_issues, list_contributors, summarize_pr_discussion
Only return valid intents from the list above.
"""

    @staticmethod
    def answer_prompt(question: str, context: str) -> str:
        return f"""
User asked:
"{question}"

Here is the data retrieved:
{context}

Answer the user's question in a helpful, complete way.
""" 