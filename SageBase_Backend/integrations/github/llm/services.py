import os
import re
import base64
from urllib.parse import quote_plus
from typing import List, Dict, Any
import google.generativeai as genai
from .prompts import GitHubPrompts
from .cache import github_cache
import logging

logger = logging.getLogger(__name__)

# Configure Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel("gemini-2.0-flash")
else:
    model = None

MAX_KEYWORDS = 4

class GitHubLLMService:
    def __init__(self):
        self.model = model
        if not self.model:
            raise ValueError("GEMINI_API_KEY not configured")

    def extract_keywords_rule_based(self, question: str) -> str:
        """Extract keywords using rule-based approach."""
        keywords = [
            "auth", "login", "logout", "register", "controller",
            "route", "middleware", "token", "session", "user",
            "signup", "signin", "api", "jwt", "http", "endpoint",
            "password", "form", "handler", "service"
        ]
        words = re.findall(r"\w+", question.lower())
        matched = [word for word in words if word in keywords]
        result = " OR ".join(matched[:MAX_KEYWORDS]) if matched else " OR ".join(words[:MAX_KEYWORDS])
        logger.debug(f"Rule-Based extracted keywords: {result}")
        return result

    async def extract_keywords_llm(self, question: str) -> str:
        """Extract keywords using LLM."""
        prompt = GitHubPrompts.extract_keywords_prompt(question)
        response = self.model.generate_content(prompt)
        result = " OR ".join(response.text.strip().split("OR")[:MAX_KEYWORDS])
        logger.debug(f"LLM extracted keywords: {result}")
        return result

    async def extract_cross_repo_keywords_llm(self, question: str) -> str:
        """Extract keywords using LLM."""
        prompt = GitHubPrompts.extract_cross_repo_keywords_prompt(question)
        response = self.model.generate_content(prompt)
        result = " OR ".join(response.text.strip().split("OR")[:MAX_KEYWORDS])
        logger.debug(f"LLM extracted keywords: {result}")
        return result


    async def classify_question_intents(self, question: str) -> List[str]:
        """Classify question into intents."""
        prompt = GitHubPrompts.classify_intents_prompt(question)
        response = self.model.generate_content(prompt)
        intents = [i.strip() for i in response.text.strip().lower().split(",") if i.strip()]
        logger.debug(f"Intent detected intents: {intents}")
        return intents

    async def generate_answer(self, question: str, context: str) -> str:
        """Generate answer using LLM."""
        prompt = GitHubPrompts.answer_prompt(question, context)
        logger.debug("LLM Prompt sending summarization prompt to Gemini")
        response = self.model.generate_content(prompt)
        logger.debug("LLM Response received")
        return response.text.strip()

    def generate_answer_sync(self, question: str, context: str) -> str:
        import asyncio
        return asyncio.run(self.generate_answer(question, context))

# Global LLM service instance
try:
    github_llm_service = GitHubLLMService() if GEMINI_API_KEY else None
except Exception as e:
    logger.exception("Failed to initialize GitHub LLM service")
    github_llm_service = None 