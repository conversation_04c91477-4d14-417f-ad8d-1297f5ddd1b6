import hashlib
import time
from typing import Any, Optional
import logging

logger = logging.getLogger(__name__)

class GitHubCache:
    def __init__(self, ttl: int = 600):  # 10 minutes default
        self.cache = {}
        self.ttl = ttl

    def _cache_key(self, url: str) -> str:
        """Generate a cache key from URL."""
        return hashlib.sha256(url.encode()).hexdigest()

    def get(self, url: str) -> Optional[Any]:
        """Get item from cache if not expired."""
        key = self._cache_key(url)
        if key in self.cache:
            item = self.cache[key]
            if time.time() - item["timestamp"] < self.ttl:
                logger.debug(f"Cache HIT for URL: {url}")
                return item["response"]
            else:
                logger.debug(f"Cache EXPIRED for URL: {url}")
                del self.cache[key]
        logger.debug(f"Cache MISS for URL: {url}")
        return None

    def set(self, url: str, response: Any) -> None:
        """Store item in cache with timestamp."""
        key = self._cache_key(url)
        self.cache[key] = {"response": response, "timestamp": time.time()}
        logger.debug(f"Cache STORED for URL: {url}")

    def clear(self) -> None:
        """Clear all cached items."""
        self.cache.clear()
        logger.debug("Cache CLEARED")

# Global cache instance
github_cache = GitHubCache() 