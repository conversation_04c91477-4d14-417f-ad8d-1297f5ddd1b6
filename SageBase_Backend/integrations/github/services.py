# integrations/github/services.py
import requests
import json
import logging
from datetime import datetime, timedelta
from django.conf import settings
from requests.auth import HTTPBasicAuth

logger = logging.getLogger(__name__)

def exchange_code_for_access_token(code: str) -> dict:
    """Exchange GitHub OAuth code for an access token."""
    client_id = settings.GITHUB_APP_CLIENT_ID
    client_secret = settings.GITHUB_APP_CLIENT_SECRET

    url = "https://github.com/login/oauth/access_token"
    headers = {"Accept": "application/json"}
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "code": code,
    }

    response = requests.post(url, headers=headers, data=data)
    return response.json()


def refresh_github_token(refresh_token: str) -> dict:
    """Refresh a GitHub OAuth access token using the refresh token."""
    client_id = settings.GITHUB_APP_CLIENT_ID
    client_secret = settings.GITHUB_APP_CLIENT_SECRET

    url = "https://github.com/login/oauth/access_token"
    headers = {"Accept": "application/json"}
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "refresh_token",
        "refresh_token": refresh_token,
    }

    try:
        response = requests.post(url, headers=headers, data=data)
        response.raise_for_status()
        token_data = response.json()
        
        logger.info(f"Successfully refreshed GitHub token. New expires_in: {token_data.get('expires_in')}")
        return token_data
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to refresh GitHub token: {e}")
        raise Exception(f"Failed to refresh GitHub token: {e}")


def is_token_expired(token_data: dict) -> bool:
    """Check if a GitHub token is expired or will expire soon (within 5 minutes)."""
    if not token_data:
        return True
    
    # Check if we have expiration info
    expires_in = token_data.get('expires_in')
    if not expires_in:
        # If no expiration info, assume token is valid for now
        return False
    
    # Check if token expires within 5 minutes
    return expires_in < 300  # 5 minutes = 300 seconds


def validate_github_token(access_token: str) -> bool:
    """Validate a GitHub access token by making a test API call."""
    try:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        response = requests.get("https://api.github.com/user", headers=headers)
        return response.status_code == 200
    except Exception as e:
        logger.error(f"Error validating GitHub token: {e}")
        return False


def get_valid_github_token(integration_config: dict) -> str:
    """
    Get a valid GitHub access token, refreshing if necessary.
    
    Args:
        integration_config: The config dict from CompanyIntegration
        
    Returns:
        Valid access token
        
    Raises:
        Exception: If token cannot be refreshed or is invalid
    """
    ghu_token = integration_config.get("ghu_token")
    ghr_token = integration_config.get("ghr_token")  # refresh token
    token_data = integration_config.get("token_data", {})
    
    if not ghu_token:
        raise Exception("No GitHub access token found")
    
    # First, try to validate the current token
    if validate_github_token(ghu_token):
        logger.debug("GitHub token is still valid")
        return ghu_token
    
    # Token is invalid, try to refresh it
    if not ghr_token:
        raise Exception("No refresh token available to refresh expired access token")
    
    try:
        logger.info("GitHub token is expired or invalid, attempting refresh...")
        refresh_data = refresh_github_token(ghr_token)
        
        new_access_token = refresh_data.get("access_token")
        new_refresh_token = refresh_data.get("refresh_token", ghr_token)  # Use new refresh token if provided
        
        if not new_access_token:
            raise Exception("Failed to get new access token from refresh")
        
        # Update the integration config with new tokens
        integration_config.update({
            "ghu_token": new_access_token,
            "ghr_token": new_refresh_token,
            "token_data": refresh_data,
            "last_token_refresh": datetime.now().isoformat()
        })
        
        logger.info("Successfully refreshed GitHub token")
        return new_access_token
        
    except Exception as e:
        logger.error(f"Failed to refresh GitHub token: {e}")
        raise Exception(f"Failed to refresh GitHub token: {e}")


def revoke_github_token(token: str) -> bool:
    """Revoke a GitHub OAuth access token."""
    client_id = settings.GITHUB_CLIENT_ID
    client_secret = settings.GITHUB_CLIENT_SECRET
    
    url = f"https://api.github.com/applications/{client_id}/token"
    auth = HTTPBasicAuth(client_id, client_secret)
    data = {"access_token": token}
    
    response = requests.delete(url, auth=auth, json=data)
    return response.status_code == 204


def revoke_github_authorization(token: str) -> bool:
    """Revoke the OAuth authorization for the app using the access token.
    This will completely remove the app's access to the user's GitHub account."""
    client_id = settings.GITHUB_CLIENT_ID
    client_secret = settings.GITHUB_CLIENT_SECRET
    
    url = f"https://api.github.com/applications/{client_id}/grant"
    auth = HTTPBasicAuth(client_id, client_secret)
    data = {"access_token": token}
    
    response = requests.delete(url, auth=auth, json=data)
    return response.status_code == 204