import httpx
import base64
from typing import List, Dict, Any, Optional
from urllib.parse import quote_plus
from ..llm.services import github_llm_service
from ..llm.cache import github_cache
import os
import time
import jwt
import requests
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

def generate_github_jwt():
    app_id = settings.GITHUB_APP_ID
    private_key = settings.GITHUB_APP_PRIVATE_KEY
    
    logger.debug(f"App ID: {app_id}")
    logger.debug(f"Private key starts with: {private_key[:50]}...")
    logger.debug(f"Private key length: {len(private_key)}")
    logger.debug(f"Private key ends with: {private_key[-50:] if len(private_key) > 50 else private_key}")
    
    # Handle literal \n characters FIRST - convert \n to actual newlines
    if "\\n" in private_key:
        logger.debug("Converting literal \\n to actual newlines in private key")
        private_key = private_key.replace("\\n", "\n")
        logger.debug(f"After newline conversion, key starts with: {private_key[:50]}...")
    
    # Handle different private key formats
    if private_key.startswith("-----BEGIN RSA PRIVATE KEY-----"):
        # Key is already in proper format
        logger.debug("Private key appears to be in correct PEM format")
        pass
    else:
        # Key might be missing headers/footers or have other formatting issues
        logger.debug(f"GitHub App private key format may be incorrect. Expected PEM format with headers.")
    
    # Validate that we have a proper private key
    if private_key == "your_GITHUB_APP_PRIVATE_KEY_here" or not private_key.startswith("-----BEGIN"):
        logger.debug(f"Invalid private key configuration. Key: {private_key[:100]}...")
        raise ValueError("GitHub App private key is not properly configured. Please set GITHUB_APP_PRIVATE_KEY environment variable.")
    
    # Additional validation for PEM format
    if not private_key.endswith("-----END RSA PRIVATE KEY-----"):
        logger.debug(f"Private key does not end with proper footer. Ends with: {private_key[-30:]}")
        raise ValueError("GitHub App private key is not in proper PEM format.")
    
    now = int(time.time())
    payload = {
        "iat": now - 60,
        "exp": now + (8 * 60),  # Reduced from 10 minutes to 8 minutes
        "iss": app_id
    }
    try:
        return jwt.encode(payload, private_key, algorithm="RS256")
    except Exception as e:
        logger.exception("JWT encoding failed")
        logger.debug("Private key format check:")
        logger.debug(f"  - Starts with BEGIN: {private_key.startswith('-----BEGIN')}")
        logger.debug(f"  - Ends with END: {private_key.endswith('-----END RSA PRIVATE KEY-----')}")
        logger.debug(f"  - Contains 'RSA PRIVATE KEY': {'RSA PRIVATE KEY' in private_key}")
        contains_newlines = '\\n' in private_key
        logger.debug(f"  - Contains newlines: {contains_newlines}")
        raise

def get_installation_access_token(installation_id):
    logger.debug(f"Getting installation access token for installation_id: {installation_id}")
    jwt_token = generate_github_jwt()
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Accept": "application/vnd.github+json"
    }
    url = f"https://api.github.com/app/installations/{installation_id}/access_tokens"
    logger.debug(f"Making request to: {url}")
    response = requests.post(url, headers=headers)
    logger.debug(f"Response status: {response.status_code}")
    if response.status_code != 200:
        logger.debug(f"Response text: {response.text}")
    response.raise_for_status()
    token_data = response.json()
    return token_data["token"]

def get_installation_repositories(installation_id):
    """Fetch the list of repositories accessible to a GitHub App installation."""
    try:
        logger.debug(f"Fetching repositories for installation_id: {installation_id}")
        access_token = get_installation_access_token(installation_id)
        url = "https://api.github.com/installation/repositories"
        headers = {
            "Authorization": f"token {access_token}",
            "Accept": "application/vnd.github+json"
        }
        logger.debug(f"Making request to: {url}")
        response = requests.get(url, headers=headers)
        logger.debug(f"Response status: {response.status_code}")
        logger.debug(f"response: {response}")
        if response.status_code != 200:
            logger.debug(f"Response text: {response.text}")
        response.raise_for_status()
        data = response.json()
        logger.debug(f"Response data keys: {list(data.keys())}")
        repositories = data.get("repositories", [])
        logger.debug(f"Found {len(repositories)} repositories")
        return repositories
    except ValueError as e:
        # This will catch the private key configuration error
        logger.exception("GitHub App configuration error")
        raise
    except requests.exceptions.RequestException as e:
        logger.exception("GitHub API request failed")
        if hasattr(e, 'response') and e.response is not None:
            logger.debug(f"GitHub API response: {e.response.text}")
        raise
    except Exception as e:
        logger.exception("Unexpected error fetching GitHub installation repositories")
        raise

def get_cached_repositories(installation_id):
    """Get repositories from cache (set by webhook) or fallback to API."""
    from django.core.cache import cache
    cache_key = f"github_installation_{installation_id}_repositories"
    cached_repos = cache.get(cache_key)
    
    if cached_repos:
        logger.debug(f"Found {len(cached_repos)} cached repositories for installation {installation_id}")
        return cached_repos
    else:
        logger.debug(f"No cached repositories found for installation {installation_id}, using API")
        return get_installation_repositories(installation_id)

class GitHubAPIService:
    def __init__(self, access_token: str):
        self.token = access_token
        self.headers = {"Authorization": f"token {self.token}"}

    async def get_commits(self, owner: str, repo: str, limit: int = 5) -> List[Dict]:
        """Get recent commits from repository."""
        url = f"https://api.github.com/repos/{owner}/{repo}/commits"
        cached = github_cache.get(url)
        if cached:
            return cached[:limit]
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            data = response.json()[:limit]
            github_cache.set(url, data)
            return data

    async def get_pulls(self, owner: str, repo: str, state: str = "all", limit: int = 5) -> List[Dict]:
        """Get pull requests from repository."""
        url = f"https://api.github.com/repos/{owner}/{repo}/pulls?state={state}"
        cached = github_cache.get(url)
        if cached:
            return cached[:limit]
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            data = response.json()[:limit]
            github_cache.set(url, data)
            return data

    async def get_pr_comments(self, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Get comments for a specific PR."""
        url = f"https://api.github.com/repos/{owner}/{repo}/issues/{pr_number}/comments"
        cached = github_cache.get(url)
        if cached:
            return cached
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            data = response.json()
            github_cache.set(url, data)
            return data

    async def get_pr_commits(self, owner: str, repo: str, pr_number: int) -> List[Dict]:
        """Get commits for a specific PR."""
        url = f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/commits"
        cached = github_cache.get(url)
        if cached:
            return cached
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            data = response.json()
            github_cache.set(url, data)
            return data

    async def get_contributors(self, owner: str, repo: str, limit: int = 5) -> List[Dict]:
        """Get contributors from repository."""
        url = f"https://api.github.com/repos/{owner}/{repo}/contributors"
        cached = github_cache.get(url)
        if cached:
            return cached[:limit]
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            data = response.json()[:limit]
            github_cache.set(url, data)
            return data

    async def search_code(self, repo: str, keyword: str) -> List[Dict]:
        """Search for code in repository."""
        encoded_repo = quote_plus(repo)
        encoded_keyword = quote_plus(keyword)
        url = f"https://api.github.com/search/code?q={encoded_keyword}+repo:{encoded_repo}"
        
        cached = github_cache.get(url)
        if cached:
            return cached.get("items", [])
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            data = response.json()
            github_cache.set(url, data)
            return data.get("items", [])

    async def get_file_content(self, file_url: str) -> Optional[Dict]:
        """Get content of a specific file."""
        # Convert search API URL to content API URL
        content_url = file_url.replace("/search/code", "/repos")
        
        cached = github_cache.get(content_url)
        if cached:
            return cached
        
        async with httpx.AsyncClient() as client:
            response = await client.get(content_url, headers=self.headers)
            data = response.json()
            github_cache.set(content_url, data)
            return data

    async def process_intent(self, intent: str, owner: str, repo: str) -> str:
        """Process a specific intent and return formatted data."""
        if intent == "list_commits":
            commits = await self.get_commits(owner, repo)
            summary = "\n".join([f"- {c['commit']['message']} by {c['commit']['author']['name']}" for c in commits])
            return f"Commits:\n{summary}"

        elif intent == "list_open_prs":
            prs = await self.get_pulls(owner, repo, "open")
            summary = "\n".join([f"- PR #{pr['number']}: {pr['title']} by {pr['user']['login']}" for pr in prs])
            return f"Open PRs:\n{summary}"

        elif intent == "list_closed_prs":
            prs = await self.get_pulls(owner, repo, "closed")
            summary = "\n".join([f"- Closed PR #{pr['number']}: {pr['title']} by {pr['user']['login']}" for pr in prs])
            return f"Closed PRs:\n{summary}"

        elif intent == "list_prs":
            prs = await self.get_pulls(owner, repo, "all")
            summary = "\n".join([f"- PR #{pr['number']}: {pr['title']} ({pr['state']}) by {pr['user']['login']}" for pr in prs])
            return f"All PRs:\n{summary}"

        elif intent == "list_contributors":
            contributors = await self.get_contributors(owner, repo)
            summary = "\n".join([f"- {u['login']} ({u['contributions']} commits)" for u in contributors])
            return f"Contributors:\n{summary}"

        return ""

    async def search_code_with_keywords(self, repo: str, keywords: List[str]) -> str:
        """Search for code using keywords and return formatted snippets."""
        seen_urls = set()
        code_snippets = []

        for keyword in keywords:
            items = await self.search_code(repo, keyword)
            
            for item in items:
                file_url = item["url"]
                if file_url in seen_urls:
                    continue
                seen_urls.add(file_url)

                file_data = await self.get_file_content(file_url)
                if file_data:
                    content = file_data.get("content", "")
                    path = file_data.get("path", "")
                    if content:
                        decoded = base64.b64decode(content).decode("utf-8", errors="ignore")
                        logger.debug(f"File {path}: {decoded[:80]}...")
                        code_snippets.append((path, decoded))

        code_snippets = code_snippets[:3]  # Limit to 3 files
        code_context = "\n\n".join([f"[File: {p}]\n{c[:1000]}" for p, c in code_snippets])
        return f"Code Snippets:\n{code_context}" 