from django.urls import path
from .views import (
    GitHubAskView,
    RepoChangeListView,
    RepoChangeDetailView,
    RepoChangeApproveView,
    RepoChangeLikeView,
)
from .webhooks import WebhookListCreateView, WebhookDeleteView
from .webhook_receiver import GitHubWebhookReceiverView
from .app_webhook_receiver import GitHubAppWebhookReceiverView

urlpatterns = [
    path("ask/", GitHubAskView.as_view(), name="github-ask"),
    path("webhooks/", WebhookListCreateView.as_view(), name="github-webhook-list-create"),
    path("webhooks/delete/", WebhookDeleteView.as_view(), name="github-webhook-delete"),
    path("webhook/receive/", GitHubWebhookReceiverView.as_view(), name="github-webhook-receive"),
    path("app-webhook/", GitHubAppWebhookReceiverView.as_view(), name="github-app-webhook"),
    # Repo change endpoints
    path("repo-changes/", RepoChangeListView.as_view(), name="repo-change-list"),
    path("repo-changes/<uuid:pk>/", RepoChangeDetailView.as_view(), name="repo-change-detail"),
    path("repo-changes/<uuid:pk>/approve/", RepoChangeApproveView.as_view(), name="repo-change-approve"),
    path("repo-changes/<uuid:pk>/like/", RepoChangeLikeView.as_view(), name="repo-change-like"),
] 