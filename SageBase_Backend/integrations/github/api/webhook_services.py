import httpx
from typing import List, Dict, Any, Optional

class GitHubWebhookService:
    def __init__(self, token: str):
        self.token = token
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github+json",
            "X-GitHub-Api-Version": "2022-11-28",
        }

    async def list_webhooks(self, owner: str, repo: str) -> List[Dict[str, Any]]:
        url = f"https://api.github.com/repos/{owner}/{repo}/hooks"
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, headers=self.headers)
            resp.raise_for_status()
            return resp.json()

    async def create_webhook(self, owner: str, repo: str, webhook_url: str, events: Optional[list] = None, secret: Optional[str] = None) -> Dict[str, Any]:
        url = f"https://api.github.com/repos/{owner}/{repo}/hooks"
        data = {
            "name": "web",
            "active": True,
            "events": events or ["push"],
            "config": {
                "url": webhook_url,
                "content_type": "json",
                "insecure_ssl": "0",
            },
        }
        if secret:
            data["config"]["secret"] = secret
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers=self.headers, json=data)
            resp.raise_for_status()
            return resp.json()

    async def delete_webhook(self, owner: str, repo: str, hook_id: int) -> bool:
        url = f"https://api.github.com/repos/{owner}/{repo}/hooks/{hook_id}"
        async with httpx.AsyncClient() as client:
            resp = await client.delete(url, headers=self.headers)
            return resp.status_code == 204 