from integrations.models import RepoChange
from rest_framework import serializers

class GitHubQuerySerializer(serializers.Serializer):
    repo = serializers.CharField(help_text="Repository in format 'owner/repo'")
    question = serializers.CharField(help_text="User's question about the repository")
    method = serializers.ChoiceField(
        choices=[("llm", "LLM"), ("rule", "Rule-based")],
        default="llm",
        help_text="Method to extract keywords"
    )
    github_token = serializers.CharField(
        required=True, 
        help_text="GitHub access token"
    )

class GitHubResponseSerializer(serializers.Serializer):
    answer = serializers.CharField(help_text="LLM-generated answer to the user's question") 

class RepoChangeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RepoChange
        fields = '__all__' 