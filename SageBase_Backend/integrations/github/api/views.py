from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
import asyncio
import traceback
from .serializers import GitHubQuerySerializer, GitHubResponseSerializer
from .services import GitHubAPIService
from ..llm.services import github_llm_service
from ..llm.cache import github_cache
import logging
from integrations.models import RepoChange, Company, User
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404

logger = logging.getLogger(__name__)

class GitHubAskView(APIView):
    """
    Ask questions about GitHub repositories using LLM-powered analysis.
    """
    
    def get(self, request):
        """Test endpoint to verify the API is working."""
        return Response({
            "status": "ok",
            "message": "GitHub LLM API is running",
            "llm_service_available": github_llm_service is not None,
            "gemini_configured": bool(settings.get('GEMINI_API_KEY', None))
        })

    def post(self, request):
        try:
            logger.debug(f"Received request data: {request.data}")
            
            serializer = GitHubQuerySerializer(data=request.data)
            if not serializer.is_valid():
                logger.debug(f"Serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            repo = serializer.validated_data['repo']
            question = serializer.validated_data['question']
            method = serializer.validated_data['method']
            github_token = serializer.validated_data['github_token']  # This is now the access token

            logger.debug(f"Processing request - Repo: {repo}, Question: {question}, Method: {method}")

            # Parse repository
            if '/' not in repo:
                return Response(
                    {"error": "Repository must be in format 'owner/repo'"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            owner, repo_name = repo.split("/")

            # Validate github_token (access token)
            if not github_token:
                return Response(
                    {"error": "GitHub access token is required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            logger.debug("Initializing API service with access token")
            # Initialize services with the access token directly
            api_service = GitHubAPIService(github_token)  # Pass the access token directly
            
            logger.debug(f"LLM service available: {github_llm_service is not None}")
            if not github_llm_service:
                return Response(
                    {"error": "LLM service not configured. Please check GEMINI_API_KEY environment variable."}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            logger.debug("Processing question with LLM")
            # Process the question (run async function in sync context)
            try:
                answer = asyncio.run(self._process_question(api_service, owner, repo_name, question, method))
                logger.debug(f"Generated answer: {answer[:100]}...")
            except Exception as async_error:
                logger.exception("Error in async processing")
                return Response(
                    {"error": f"Failed to process question: {str(async_error)}"}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            
            response_data = {"answer": answer}
            response_serializer = GitHubResponseSerializer(response_data)
            return Response(response_serializer.data)

        except Exception as e:
            logger.exception("Error processing GitHub question")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return Response(
                {"error": f"Failed to process question: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def _process_question(self, api_service, owner, repo_name, question, method):
        """Process the question using LLM and GitHub API."""
        logger.debug(f"Request Question: {question}, Method: {method}, Repo: {owner}/{repo_name}")

        # Classify intents
        intents = await github_llm_service.classify_question_intents(question)
        answers = []

        # Process each intent
        for intent in intents:
            if intent in ["list_commits", "list_open_prs", "list_closed_prs", "list_prs", "list_contributors"]:
                result = await api_service.process_intent(intent, owner, repo_name)
                if result:
                    answers.append(result)

        # Handle code search
        if "code" in intents:
            if method == "rule":
                refined_query = github_llm_service.extract_keywords_rule_based(question)
            else:
                refined_query = await github_llm_service.extract_keywords_llm(question)

            keywords = [k.strip() for k in refined_query.split("OR") if k.strip()][:4]
            logger.debug(f"Search Keywords list: {keywords}")
            
            code_context = await api_service.search_code_with_keywords(f"{owner}/{repo_name}", keywords)
            if code_context:
                answers.append(code_context)

        # Generate final answer
        full_context = "\n\n".join(answers)
        answer = await github_llm_service.generate_answer(question, full_context)
        
        return answer 

class RepoChangeListView(APIView):
    """List repo changes filtered by status (pending, approved, etc)."""
    def get(self, request):
        status_param = request.query_params.get("status", "pending").lower()
        company_id = request.query_params.get("company_id")
        #logger.debug(f"[RepoChangeListView] Filter: company_id={company_id}, status={status_param}")
        qs = RepoChange.objects.filter(company_id=company_id, status=status_param)
        #logger.debug(f"[RepoChangeListView] Count: {qs.count()}")
        changes = qs.order_by("-created_at")
        data = [
            {
                "id": str(c.id),
                "internal_repo": c.internal_repo,
                "external_repo": c.external_repo,
                "commit_id": c.commit_id,
                "summary": c.summary,
                "details": c.details,
                "status": c.status,
                "created_at": c.created_at,
                "updated_at": c.updated_at,
                "likes": c.likes.count(),
                "dislikes": c.dislikes.count(),
            }
            for c in changes
        ]
        return Response(data)

class RepoChangeDetailView(APIView):
    """Get details for a single repo change."""
    def get(self, request, pk):
        change = get_object_or_404(RepoChange, pk=pk)
        data = {
            "id": str(change.id),
            "internal_repo": change.internal_repo,
            "external_repo": change.external_repo,
            "commit_id": change.commit_id,
            "summary": change.summary,
            "details": change.details,
            "status": change.status,
            "created_at": change.created_at,
            "updated_at": change.updated_at,
            "likes": change.likes.count(),
            "dislikes": change.dislikes.count(),
        }
        return Response(data)

class RepoChangeApproveView(APIView):
    """Approve or reject a repo change (PATCH)."""
    def patch(self, request, pk):
        change = get_object_or_404(RepoChange, pk=pk)
        action = request.data.get("action")
        approved_by_email = request.data.get("approved_by")
        from django.utils import timezone
        from integrations.models import User
        user_obj = None
        if approved_by_email:
            try:
                user_obj = User.objects.get(email=approved_by_email)
            except User.DoesNotExist:
                user_obj = None
        if action == "approve":
            change.status = "approved"
            change.approved_by = user_obj or request.user
            change.approved_at = timezone.now()
            change.save()
        elif action == "reject":
            change.status = "rejected"
            change.approved_by = user_obj or request.user
            change.approved_at = timezone.now()
            change.save()
        else:
            return Response({"error": "Invalid action"}, status=400)
        return Response({"id": str(change.id), "status": change.status})

class RepoChangeLikeView(APIView):
    """Like or dislike a repo change (POST)."""
    def post(self, request, pk):
        change = get_object_or_404(RepoChange, pk=pk)
        action = request.data.get("action")
        if action == "like":
            change.likes.add(request.user)
            change.dislikes.remove(request.user)
        elif action == "dislike":
            change.dislikes.add(request.user)
            change.likes.remove(request.user)
        else:
            return Response({"error": "Invalid action"}, status=400)
        return Response({"id": str(change.id), "likes": change.likes.count(), "dislikes": change.dislikes.count()}) 