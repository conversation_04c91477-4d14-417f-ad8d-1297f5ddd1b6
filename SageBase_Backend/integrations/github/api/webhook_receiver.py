from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.http import HttpRequest
import hmac
import hashlib
import base64
import json
import os
from ..llm.services import github_llm_service
from .services import GitHubAPIService
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from integrations.models import MonitoredItem, CompanyIntegration, RepoChange, Company, User, CrossRepoMonitor
import logging

logger = logging.getLogger(__name__)

# REMOVE notify_frontend and all WebSocket logic

@method_decorator(csrf_exempt, name='dispatch')
class GitHubWebhookReceiverView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request: HttpRequest):
        logger.debug("Webhook received POST request")
        # Get repo from payload
        payload = request.data if isinstance(request.data, dict) else json.loads(request.body)
        repo = payload.get("repository", {}).get("full_name")
        github_token = None
        company_id = None
        if repo:
            monitored = MonitoredItem.objects.filter(repo=repo.replace("/", "_", 1)).first()
            if monitored:
                company_id = monitored.company_id
                integration = CompanyIntegration.objects.filter(company_id=company_id, tool__slug="github", is_active=True).first()
                if integration and integration.config and integration.config.get("token"):
                    github_token = integration.config["token"]
        if github_token:
            logger.debug(f"GitHub OAuth token loaded from DB for repo {repo}")
        else:
            logger.debug(f"GitHub OAuth token NOT found in DB for repo {repo}")
        secret = os.getenv("GITHUB_WEBHOOK_SECRET")
        if secret:
            signature = request.headers.get("X-Hub-Signature-256")
            logger.debug(f"Webhook checking signature: {signature}")
            if not signature or not self._verify_signature(request.body, secret, signature):
                logger.debug("Webhook invalid signature")
                return Response({"error": "Invalid signature"}, status=400)

        event = request.headers.get("X-GitHub-Event")
        logger.debug(f"Webhook event type: {event}")
        payload = request.data if isinstance(request.data, dict) else json.loads(request.body)
        logger.debug(f"Webhook payload: {json.dumps(payload)[:500]}")
        repo = payload.get("repository", {}).get("full_name")
        owner, repo_name = repo.split("/") if repo else (None, None)
        code_diffs = []
        llm_result = None
        llm_suggested_docs = None
        # Detect doc and code changes for push events
        if event == "push":
            modified = payload.get("head_commit", {}).get("modified", [])
            added = payload.get("head_commit", {}).get("added", [])
            removed = payload.get("head_commit", {}).get("removed", [])
            all_changed = modified + added + removed
            logger.debug(f"Changed files: {all_changed}")
            if github_token and owner and repo_name and all_changed:
                api_service = GitHubAPIService(github_token)
                import asyncio
                async def fetch_diffs():
                    after_sha = payload.get("after")
                    before_sha = payload.get("before")
                    logger.debug(f"Comparing commits: {before_sha}...{after_sha}")
                    if after_sha and before_sha:
                        url = f"https://api.github.com/repos/{owner}/{repo_name}/compare/{before_sha}...{after_sha}"
                        import httpx
                        async with httpx.AsyncClient() as client:
                            resp = await client.get(url, headers=api_service.headers)
                            logger.debug(f"GitHub compare API status: {resp.status_code}")
                            if resp.status_code == 200:
                                files = resp.json().get("files", [])
                                for f in files:
                                    patch = f.get("patch")
                                    if patch:
                                        code_diffs.append({"file": f.get("filename"), "patch": patch})
                                logger.debug(f"Number of code diffs fetched: {len(code_diffs)}")
                            else:
                                logger.debug(f"Failed to fetch diffs: {resp.text}")
                asyncio.run(fetch_diffs())
        # If code changes, ask LLM to extract doc-relevant changes (endpoint params, URLs, etc.)
        engineer_changes_structured = []
        if code_diffs:
            try:
                code_context = "\n\n".join([f"[File: {c['file']} diff]\n{c['patch']}" for c in code_diffs])
                logger.debug(f"LLM input context (truncated): {code_context[:500]}{'...' if len(code_context) > 500 else ''}")
                question = (
                    "You are an expert software engineer and technical documentation assistant. "
                    "Analyze the following code changes (diffs) and extract ONLY the changes that are truly relevant to software engineers—such as API endpoint changes, function signature changes, request/response structure, authentication, breaking changes, deprecations, or anything that would require an engineer to update their integration or usage. "
                    "IGNORE changes that are not relevant to engineers, such as formatting, comments, whitespace, internal variable renames, or non-functional refactors. "
                    "For each relevant change, provide a clear, concise bullet point with the following structure:\n"
                    "- What changed (summarize in one sentence, e.g., 'getClients endpoint changed') with a quick why if can be derived from introduced change.\n"
                    "- Previous state (show code snippet or describe old behavior)\n"
                    "- New state (show code snippet or describe new behavior)\n"
                    "- File and location (if possible)\n"
                    "- Severity of change (use standard software engineering vocabulary: 'breaking', 'major', 'minor', 'deprecation', 'removal', 'addition', 'modification', etc)\n"
                    "- Any other relevant detail a software engineer would want to know (e.g., breaking change, new parameter, deprecation, etc.)\n"
                    "Format your answer as a JSON array of objects, each with keys: 'summary', 'before', 'after', 'file', 'severity', 'details'. Always include the 'severity' key for each change, using the most appropriate professional term for the impact.\n"
                    "If there are NO actionable or relevant changes for engineers, return an empty array [].\n"
                    "\nNow, analyze the following code changes and provide a clear, actionable summary for engineers. Only include changes that are truly relevant and actionable.\n"
                )
                llm_suggested_docs = github_llm_service.generate_answer_sync(question, code_context)
                logger.debug(f"LLM output (truncated): {str(llm_suggested_docs)[:500]}{'...' if len(str(llm_suggested_docs)) > 500 else ''}")
                import json as _json
                import re
                try:
                    # Remove markdown code block if present
                    cleaned = llm_suggested_docs.strip()
                    if cleaned.startswith('```'):
                        cleaned = re.sub(r'^```[a-zA-Z]*\s*', '', cleaned)
                        cleaned = re.sub(r'```$', '', cleaned)
                    cleaned = cleaned.strip()
                    engineer_changes_structured = _json.loads(cleaned)
                    # Normalize severity field for each change
                    for change in engineer_changes_structured:
                        if 'severity' not in change:
                            if 'level' in change:
                                change['severity'] = change['level']
                            elif 'type' in change:
                                change['severity'] = change['type']
                            else:
                                change['severity'] = 'minor'
                    if not engineer_changes_structured:
                        logger.debug("No actionable engineer changes detected by LLM")
                except Exception as e:
                    logger.debug(f"Error parsing LLM result as JSON: {e}\nRaw LLM output: {llm_suggested_docs}")
                    engineer_changes_structured = []
            except Exception as e:
                logger.exception("LLM error")
                engineer_changes_structured = []
        # Notify frontend (WebSocket or placeholder)
        logger.debug("Webhook saving engineer changes to RepoChange model")
        saved_changes = []
        if engineer_changes_structured and company_id:
            company = Company.objects.get(id=company_id)
            # Find the CrossRepoMonitor for this repo
            cross_repo_monitor = CrossRepoMonitor.objects.filter(
                company=company,
                internal_repo=repo
            ).first()
            for change in engineer_changes_structured:
                repo_change = RepoChange.objects.create(
                    company=company,
                    internal_repo=repo,  # repo is the internal repo being monitored
                    external_repo=repo,  # for now, use the same; can be extended for cross-repo
                    cross_repo_monitor=cross_repo_monitor,
                    commit_id=payload.get("after"),
                    summary=change.get("summary", ""),
                    details=change,
                    status="pending",
                )
                saved_changes.append({
                    "id": str(repo_change.id),
                    "summary": repo_change.summary,
                    "details": repo_change.details,
                    "status": repo_change.status,
                })
        logger.debug("Webhook done processing webhook")
        return Response({
            "status": "received",
            "engineer_changes": engineer_changes_structured,
            "code_diffs": code_diffs,
            "llm_suggested_docs": llm_suggested_docs,
            "saved_changes": saved_changes,
        })

    def _verify_signature(self, body, secret, signature):
        mac = hmac.new(secret.encode(), msg=body, digestmod=hashlib.sha256)
        expected = f"sha256={mac.hexdigest()}"
        return hmac.compare_digest(expected, signature) 