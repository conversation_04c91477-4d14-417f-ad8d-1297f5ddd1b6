from rest_framework.views import APIView
from rest_framework.response import Response
from integrations.models import CompanyIntegration
import json
import os
import hmac
import hashlib
from django.conf import settings
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from integrations.models import Company, MonitoredItem
from integrations.github.api.services import get_installation_access_token, GitHubAPIService
from integrations.github.llm.services import github_llm_service
import asyncio
import logging
from integrations.models import CrossRepoMonitor
from rest_framework.decorators import api_view
import requests
from django.urls import path
from .webhook_services import GitHubWebhookService
import httpx
from asgiref.sync import sync_to_async


logger = logging.getLogger(__name__)

# Purpose: Verify GitHub webhook signatures using the secret stored in .env as GITHUB_APP_WEBHOOK_SECRET
# This ensures only requests from GitHub are processed.
# Generate a strong random secret and set it in your .env as GITHUB_APP_WEBHOOK_SECRET
# Example: openssl rand -hex 32
# GITHUB_APP_WEBHOOK_SECRET=your-very-long-random-string

def verify_github_signature(request):
    secret = getattr(settings, 'GITHUB_APP_WEBHOOK_SECRET', None) or os.environ.get('GITHUB_APP_WEBHOOK_SECRET')
    if not secret:
        return False
    signature = request.headers.get('X-Hub-Signature-256')
    if not signature:
        return False
    body = request.body
    mac = hmac.new(secret.encode(), msg=body, digestmod=hashlib.sha256)
    expected = f"sha256={mac.hexdigest()}"
    return hmac.compare_digest(expected, signature)

def send_platform_uninstalled_ws(company_id, platform_slug):
    channel_layer = get_channel_layer()
    group_name = f"company_{company_id}_platforms"
    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            "type": "platform.uninstalled",
            "platform": platform_slug,
            "message": f"Platform {platform_slug} uninstalled."
        }
    )
    logger.info(f"[GitHub App Webhook] Uninstall event sent to frontend as: Platform {platform_slug} uninstalled.")

class GitHubAppWebhookReceiverView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        # Verify webhook signature
        if not verify_github_signature(request):
            return Response({"error": "Invalid signature"}, status=401)

        event = request.headers.get("X-GitHub-Event")
        payload = request.data if isinstance(request.data, dict) else json.loads(request.body)

        if event == "installation":
            installation_id = payload.get("installation", {}).get("id")
            action = payload.get("action")
            
            if action == "deleted":
                logger.info(f"[GitHub App Webhook] Uninstall event received for installation_id={installation_id}")
                deleted = False
                notified_companies = []
                if installation_id:
                    integrations = CompanyIntegration.objects.filter(
                        tool__slug="github",
                        config__installation_id=str(installation_id)
                    )
                    for integration in integrations:
                        company_id = getattr(integration, 'company_id', None) or integration.config.get('company_id')
                        if company_id:
                            # Delete cross repo monitors for this company
                            from integrations.models import CrossRepoMonitor
                            cross_repo_monitors_deleted = CrossRepoMonitor.objects.filter(company_id=company_id).delete()
                            logger.info(f"[GitHub App Webhook] Deleted {cross_repo_monitors_deleted[0]} cross repo monitors for company {company_id}")
                            
                            send_platform_uninstalled_ws(company_id, "github")
                            notified_companies.append(company_id)
                    count = integrations.count()
                    integrations.delete()
                    deleted = count > 0
                return Response({
                    "status": "deleted",
                    "installation_id": installation_id,
                    "integration_deleted": deleted,
                    "notified_companies": notified_companies
                })
            
            elif action == "created":
                logger.info(f"[GitHub App Webhook] Installation created event received for installation_id={installation_id}")
                
                # Extract repositories from the payload
                repositories = payload.get("repositories", [])
                logger.info(f"[GitHub App Webhook] Found {len(repositories)} repositories in payload")
                
                # Format repositories for storage
                formatted_repos = []
                for repo in repositories:
                    formatted_repos.append({
                        "id": repo.get("id"),
                        "full_name": repo.get("full_name"),
                        "name": repo.get("name"),
                        "private": repo.get("private", False)
                    })
                
                # Store the repositories data for later use
                # This can be accessed when the frontend completes the OAuth flow
                from django.core.cache import cache
                cache_key = f"github_installation_{installation_id}_repositories"
                cache.set(cache_key, formatted_repos, timeout=3600)  # Cache for 1 hour
                
                logger.info(f"[GitHub App Webhook] Cached {len(formatted_repos)} repositories for installation {installation_id}")
                
                integrations = CompanyIntegration.objects.filter(
                    tool__slug="github",
                    config__installation_id=str(installation_id)
                )
                for integration in integrations:
                    company = integration.company
                    for repo in repositories:
                        repo_full_name = repo.get("full_name")
                        if not MonitoredItem.objects.filter(company=company, type="github_repo", repo=repo_full_name).exists():
                            MonitoredItem.objects.create(
                                company=company,
                                type="github_repo",
                                name=repo.get("name"),
                                url=f"https://github.com/{repo_full_name}",
                                repo=repo_full_name
                            )
                
                return Response({
                    "status": "created",
                    "installation_id": installation_id,
                    "repositories_count": len(formatted_repos),
                    "repositories": formatted_repos
                })
        
        if event == "push":
            installation_id = payload.get("installation", {}).get("id")
            repo_full_name = payload.get("repository", {}).get("full_name")
            logger.info(f"[GitHub App Webhook][PUSH] installation_id: {installation_id}, repo_full_name: {repo_full_name}")
            integrations = CompanyIntegration.objects.filter(
                tool__slug="github",
                config__installation_id=str(installation_id)
            )  # type: ignore
            logger.info(f"[GitHub App Webhook][PUSH] Found {integrations.count()} integrations for installation_id {installation_id}")
            found_monitored_item = False
            for integration in integrations:
                company = integration.company
                logger.info(f"[GitHub App Webhook][PUSH] Checking company: {company.id} ({company.name})")
                monitored = MonitoredItem.objects.filter(company=company, type="github_repo", name=repo_full_name).first()  # type: ignore
                if monitored:
                    found_monitored_item = True
                    logger.info(f"[GitHub App Webhook][PUSH] Monitored item found for repo {repo_full_name} in company {company.id}")
                    # --- Begin diff/LLM logic for simple monitored item ---
                    owner, repo_name = repo_full_name.split("/") if repo_full_name else (None, None)
                    code_diffs = []
                    llm_suggested_docs = None
                    engineer_changes_structured = []
                    modified = payload.get("head_commit", {}).get("modified", [])
                    added = payload.get("head_commit", {}).get("added", [])
                    removed = payload.get("head_commit", {}).get("removed", [])
                    all_changed = modified + added + removed
                    logger.debug(f"[DEBUG] Changed files: {all_changed}")
                    if installation_id and owner and repo_name and all_changed:
                        access_token = get_installation_access_token(installation_id)  # type: ignore
                        api_service = GitHubAPIService(access_token)  # type: ignore
                        async def fetch_diffs():
                            after_sha = payload.get("after")
                            before_sha = payload.get("before")
                            logger.debug(f"[DEBUG] Comparing commits: {before_sha}...{after_sha}")
                            if after_sha and before_sha:
                                url = f"https://api.github.com/repos/{owner}/{repo_name}/compare/{before_sha}...{after_sha}"
                                import httpx
                                async with httpx.AsyncClient() as client:
                                    resp = await client.get(url, headers=api_service.headers)
                                    logger.debug(f"[DEBUG] GitHub compare API status: {resp.status_code}")
                                    if resp.status_code == 200:
                                        files = resp.json().get("files", [])
                                        for f in files:
                                            patch = f.get("patch")
                                            if patch:
                                                code_diffs.append({"file": f.get("filename"), "patch": patch})
                                        logger.debug(f"[DEBUG] Number of code diffs fetched: {len(code_diffs)}")
                                    else:
                                        logger.debug(f"[DEBUG] Failed to fetch diffs: {resp.text}")
                        asyncio.run(fetch_diffs())
                    # LLM summarization
                    if code_diffs:
                        try:
                            code_context = "\n\n".join([f"[File: {c['file']} diff]\n{c['patch']}" for c in code_diffs])
                            logger.debug(f"[DEBUG] LLM input context (truncated): {code_context[:500]}{'...' if len(code_context) > 500 else ''}")
                            question = (
                                "You are an expert software engineer and technical documentation assistant. "
                                "Analyze the following code changes (diffs) and extract ONLY the changes that are truly relevant to software engineers—such as API endpoint changes, function signature changes, request/response structure, authentication, breaking changes, deprecations, or anything that would require an engineer to update their integration or usage. "
                                "IGNORE changes that are not relevant to engineers, such as formatting, comments, whitespace, internal variable renames, or non-functional refactors. "
                                "For each relevant change, provide a clear, concise bullet point with the following structure:\n"
                                "- What changed (summarize in one sentence, e.g., 'getClients endpoint changed') with a quick why if can be derived from introduced change.\n"
                                "- Previous state (show code snippet or describe old behavior)\n"
                                "- New state (show code snippet or describe new behavior)\n"
                                "- File and location (if possible)\n"
                                "- Severity of change (use standard software engineering vocabulary: 'breaking', 'major', 'minor', 'deprecation', 'removal', 'addition', 'modification', etc)\n"
                                "- Any other relevant detail a software engineer would want to know (e.g., breaking change, new parameter, deprecation, etc.)\n"
                                "Format your answer as a JSON array of objects, each with keys: 'summary', 'before', 'after', 'file', 'severity', 'details'. Always include the 'severity' key for each change, using the most appropriate professional term for the impact.\n"
                                "If there are NO actionable or relevant changes for engineers, return an empty array [].\n"
                                "\nNow, analyze the following code changes and provide a clear, actionable summary for engineers. Only include changes that are truly relevant and actionable.\n"
                            )
                            llm_suggested_docs = github_llm_service.generate_answer_sync(question, code_context)  # type: ignore
                            logger.debug(f"[DEBUG] LLM output (truncated): {str(llm_suggested_docs)[:500]}{'...' if len(str(llm_suggested_docs)) > 500 else ''}")
                            import json as _json
                            import re
                            try:
                                cleaned = llm_suggested_docs.strip()
                                if cleaned.startswith('```'):
                                    cleaned = re.sub(r'^```[a-zA-Z]*\s*', '', cleaned)
                                    cleaned = re.sub(r'```$', '', cleaned)
                                cleaned = cleaned.strip()
                                engineer_changes_structured = _json.loads(cleaned)
                                for change in engineer_changes_structured:
                                    if 'severity' not in change:
                                        if 'level' in change:
                                            change['severity'] = change['level']
                                        elif 'type' in change:
                                            change['severity'] = change['type']
                                        else:
                                            change['severity'] = 'minor'
                                if not engineer_changes_structured:
                                    logger.debug("[DEBUG] No actionable engineer changes detected by LLM.")
                            except Exception as e:
                                logger.debug(f"[DEBUG] Error parsing LLM result as JSON: {e}\nRaw LLM output: {llm_suggested_docs}")
                                engineer_changes_structured = []
                        except Exception as e:
                            logger.debug(f"[DEBUG] LLM error: {str(e)}")
                            engineer_changes_structured = []
                    # --- End diff/LLM logic for simple monitored item ---
                    # Send notification to frontend using the same group name logic as before
                    channel_layer = get_channel_layer()  # type: ignore
                    group_name = f"{owner}_{repo_name}" if owner and repo_name else repo_full_name.replace('/', '_')
                    notification_data = {
                        "repo": f"{owner}_{repo_name}" if owner and repo_name else repo_full_name.replace('/', '_'),
                        "event": event,
                        "engineer_changes": engineer_changes_structured,
                        "code_diffs": code_diffs,
                        "llm_suggested_docs": llm_suggested_docs,
                        "raw": payload,
                    }
                    logger.info(f"[WS][GROUP_SEND] Sending to group '{group_name}' for event '{event}' with data: {json.dumps(notification_data)[:500]}")
                    async_to_sync(channel_layer.group_send)(  # type: ignore
                        group_name,
                        {
                            "type": "notify",
                            "notification": notification_data
                        }
                    )
            # --- CrossRepoMonitor logic: independent block ---
            for integration in integrations:
                company = integration.company
                cross_monitors = CrossRepoMonitor.objects.filter(company=company, external_repos__contains=[repo_full_name])
                if cross_monitors.exists():
                    for monitor in cross_monitors:
                        # --- Begin diff/LLM logic for cross-repo monitor ---
                        owner, repo_name = repo_full_name.split("/") if repo_full_name else (None, None)
                        code_diffs = []
                        llm_suggested_docs = None
                        engineer_changes_structured = []
                        modified = payload.get("head_commit", {}).get("modified", [])
                        added = payload.get("head_commit", {}).get("added", [])
                        removed = payload.get("head_commit", {}).get("removed", [])
                        all_changed = modified + added + removed
                        logger.debug(f"[CROSS-REPO][DEBUG] Changed files: {all_changed}")
                        if installation_id and owner and repo_name and all_changed:
                            access_token = get_installation_access_token(installation_id)  # type: ignore
                            api_service = GitHubAPIService(access_token)  # type: ignore
                            async def fetch_diffs():
                                after_sha = payload.get("after")
                                before_sha = payload.get("before")
                                logger.debug(f"[CROSS-REPO][DEBUG] Comparing commits: {before_sha}...{after_sha}")
                                if after_sha and before_sha:
                                    url = f"https://api.github.com/repos/{owner}/{repo_name}/compare/{before_sha}...{after_sha}"
                                    import httpx
                                    async with httpx.AsyncClient() as client:
                                        resp = await client.get(url, headers=api_service.headers)
                                        logger.debug(f"[CROSS-REPO][DEBUG] GitHub compare API status: {resp.status_code}")
                                        if resp.status_code == 200:
                                            files = resp.json().get("files", [])
                                            for f in files:
                                                patch = f.get("patch")
                                                if patch:
                                                    code_diffs.append({"file": f.get("filename"), "patch": patch})
                                            logger.debug(f"[CROSS-REPO][DEBUG] Number of code diffs fetched: {len(code_diffs)}")
                                        else:
                                            logger.debug(f"[CROSS-REPO][DEBUG] Failed to fetch diffs: {resp.text}")
                            asyncio.run(fetch_diffs())
                        # LLM summarization
                        if code_diffs:
                            try:
                                code_context = "\n\n".join([f"[File: {c['file']} diff]\n{c['patch']}" for c in code_diffs])
                                logger.debug(f"[CROSS-REPO][DEBUG] LLM input context (truncated): {code_context[:500]}{'...' if len(code_context) > 500 else ''}")
                                question = (
                                    "You are an expert software engineer and technical documentation assistant. "
                                    "Analyze the following code changes (diffs) and extract ONLY the changes that are truly relevant to software engineers—such as API endpoint changes, function signature changes, request/response structure, authentication, breaking changes, deprecations, or anything that would require an engineer to update their integration or usage. "
                                    "IGNORE changes that are not relevant to engineers, such as formatting, comments, whitespace, internal variable renames, or non-functional refactors. "
                                    "For each relevant change, provide a clear, concise bullet point with the following structure:\n"
                                    "- What changed (summarize in one sentence, e.g., 'getClients endpoint changed') with a quick why if can be derived from introduced change.\n"
                                    "- Previous state (show code snippet or describe old behavior)\n"
                                    "- New state (show code snippet or describe new behavior)\n"
                                    "- File and location (if possible)\n"
                                    "- Severity of change (use standard software engineering vocabulary: 'breaking', 'major', 'minor', 'deprecation', 'removal', 'addition', 'modification', etc)\n"
                                    "- Any other relevant detail a software engineer would want to know (e.g., breaking change, new parameter, deprecation, etc.)\n"
                                    "Format your answer as a JSON array of objects, each with keys: 'summary', 'before', 'after', 'file', 'severity', 'details'. Always include the 'severity' key for each change, using the most appropriate professional term for the impact.\n"
                                    "If there are NO actionable or relevant changes for engineers, return an empty array [].\n"
                                    "\nNow, analyze the following code changes and provide a clear, actionable summary for engineers. Only include changes that are truly relevant and actionable.\n"
                                )
                                llm_suggested_docs = github_llm_service.generate_answer_sync(question, code_context)  # type: ignore
                                logger.debug(f"[CROSS-REPO][DEBUG] LLM output (truncated): {str(llm_suggested_docs)[:500]}{'...' if len(str(llm_suggested_docs)) > 500 else ''}")
                                import json as _json
                                import re
                                try:
                                    cleaned = llm_suggested_docs.strip()
                                    if cleaned.startswith('```'):
                                        cleaned = re.sub(r'^```[a-zA-Z]*\s*', '', cleaned)
                                        cleaned = re.sub(r'```$', '', cleaned)
                                    cleaned = cleaned.strip()
                                    engineer_changes_structured = _json.loads(cleaned)
                                    for change in engineer_changes_structured:
                                        if 'severity' not in change:
                                            if 'level' in change:
                                                change['severity'] = change['level']
                                            elif 'type' in change:
                                                change['severity'] = change['type']
                                            else:
                                                change['severity'] = 'minor'
                                    if not engineer_changes_structured:
                                        logger.debug("[CROSS-REPO][DEBUG] No actionable engineer changes detected by LLM.")
                                except Exception as e:
                                    logger.debug(f"[CROSS-REPO][DEBUG] Error parsing LLM result as JSON: {e}\nRaw LLM output: {llm_suggested_docs}")
                                    engineer_changes_structured = []
                            except Exception as e:
                                logger.debug(f"[CROSS-REPO][DEBUG] LLM error: {str(e)}")
                                engineer_changes_structured = []
                        # --- End diff/LLM logic for cross-repo monitor ---
                        # For each change, extract keywords from its summary and check usage in internal repo
                        used_changes = []
                        for change in engineer_changes_structured:
                            summary = change.get("summary", "")
                            # Extract keywords for this change only
                            change_keywords = set()
                            try:
                                if github_llm_service:
                                    extracted = github_llm_service.extract_cross_repo_keywords_llm(summary)
                                    for kw in extracted.split("OR"):
                                        kw = kw.strip()
                                        if kw:
                                            change_keywords.add(kw)
                            except Exception:
                                for word in summary.split():
                                    change_keywords.add(word)
                            found = False
                            # Get installation_id and token for the internal repo
                            print("[CROSS_REPO][DEBUG] installation id: ", installation_id)
                            print("[CROSS_REPO][DEBUG] access token: ", access_token)
                            # Wrap the synchronous function
                            search_code_with_keywords_async = async_to_sync(api_service.search_code_with_keywords)

                            # Use it within your async view
                            code_has_keywords = search_code_with_keywords_async(monitor.internal_repo, list(change_keywords))
                            if code_has_keywords:
                                found = True
                            if found:
                                used_changes.append(change)
                        if used_changes:
                            group_name = monitor.internal_repo.replace('/', '_')
                            notification_data = {
                                "repo": group_name,
                                "event": event,
                                "engineer_changes": used_changes,
                                "code_diffs": code_diffs,
                                "llm_suggested_docs": llm_suggested_docs,
                                "raw": payload,
                            }
                            logger.info(f"[WS][CROSS_REPO][GROUP_SEND] Sending to group '{group_name}' for event '{event}' with data: {json.dumps(notification_data)[:500]}")
                            logger.info(f"[CROSS_REPO][WS] Preparing to send notification to group: {group_name}")
                            logger.info(f"[CROSS_REPO][WS] Notification data: {json.dumps(notification_data)[:500]}")
                            try:
                                channel_layer = get_channel_layer()
                                async_to_sync(channel_layer.group_send)(
                                    group_name,
                                    {
                                        "type": "notify",
                                        "notification": notification_data
                                    }
                                )
                                logger.info(f"[CROSS_REPO][WS] Notification sent to group: {group_name}")
                            except Exception as e:
                                logger.error(f"[CROSS_REPO][WS] Error sending notification to group {group_name}: {e}")
                        else:
                            logger.info(f"[CROSS_REPO][WS] No used changes found for internal repo {monitor.internal_repo}, no notification sent.")
        # Optionally handle other app events here
        return Response({"status": "ignored", "event": event})

# --- LLM Summarization Stub ---
def summarize_push_event(payload):
    # TODO: Implement LLM summarization of the push event diffs
    # For now, just return a placeholder summary
    commits = payload.get("commits", [])
    commit_msgs = [c.get("message", "") for c in commits]
    return f"Push with {len(commits)} commits. Messages: " + "; ".join(commit_msgs) 

@api_view(["POST", "GET", "DELETE"])
def company_cross_repo_monitor_create(request):
    if request.method == "GET":
        company_id = request.query_params.get("company_id")
        if not company_id:
            return Response({"error": "company_id is required"}, status=400)
        from integrations.models import Company
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"error": "Company not found"}, status=404)
        monitors = CrossRepoMonitor.objects.filter(company=company)
        return Response([{
            "id": monitor.id,
            "internal_repo": monitor.internal_repo,
            "external_repos": monitor.external_repos,
            "created_at": monitor.created_at
        } for monitor in monitors])
    
    elif request.method == "DELETE":
        monitor_id = request.query_params.get("id")
        if not monitor_id:
            return Response({"error": "id is required"}, status=400)
        try:
            monitor = CrossRepoMonitor.objects.get(id=monitor_id)
            monitor.delete()
            return Response({"message": "Cross-repo monitor deleted successfully"}, status=204)
        except CrossRepoMonitor.DoesNotExist:
            return Response({"error": "Cross-repo monitor not found"}, status=404)
    
    # POST logic (existing)
    data = request.data
    company_id = data.get("company_id")
    internal_repo = data.get("internal_repo")
    external_repos = data.get("external_repos", [])
    github_token = data.get("github_token")  # Optionally pass a token
    webhook_url = data.get("webhook_url")  # Optionally pass a webhook URL
    if not (company_id and internal_repo and external_repos):
        return Response({"error": "company_id, internal_repo, and external_repos are required"}, status=400)
    from integrations.models import Company
    try:
        company = Company.objects.get(id=company_id)
    except Company.DoesNotExist:
        return Response({"error": "Company not found"}, status=404)
    monitor = CrossRepoMonitor.objects.create(
        company=company,
        internal_repo=internal_repo,
        external_repos=external_repos,
    )
    # Create webhooks for each external repo
    webhook_results = []
    for repo_full_name in external_repos:
        try:
            owner, repo_name = repo_full_name.split("/")
            # Use the GitHubWebhookService to create webhook
            # You may want to use an app installation token here
            token = github_token or os.getenv("GITHUB_APP_CLIENT_SECRET")
            service = GitHubWebhookService(token)
            # Use the app-level webhook URL if not provided
            url = webhook_url or os.getenv("GITHUB_APP_WEBHOOK_URL")
            if url:
                import asyncio
                result = asyncio.run(service.create_webhook(owner, repo_name, url, ["push"]))
                webhook_results.append({"repo": repo_full_name, "result": result})
        except Exception as e:
            webhook_results.append({"repo": repo_full_name, "error": str(e)})
    return Response({"id": monitor.id, "internal_repo": monitor.internal_repo, "external_repos": monitor.external_repos, "webhook_results": webhook_results})

# Helper to extract symbol name from change summary (can be improved with LLM)
def extract_symbol_from_summary(summary):
    # Simple heuristic: extract first word or function name in quotes/parens
    import re
    # Try to find function or variable name in summary
    match = re.search(r'([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', summary)
    if match:
        return match.group(1)
    # Try quoted name
    match = re.search(r'"([a-zA-Z_][a-zA-Z0-9_]*)"', summary)
    if match:
        return match.group(1)
    # Fallback: first word
    return summary.split()[0] if summary else None

# Helper to get installation_id for a repo (stub: implement as needed)
def get_installation_id_for_repo(repo_full_name, fallback_installation_id=None):
    # TODO: Implement actual lookup based on your DB or GitHub API
    return fallback_installation_id

# Helper to check symbol usage in internal repo via GitHub API
# Returns True if symbol is found in code, False otherwise
# Optionally, use LLM to improve relevance (stub for now)
def is_symbol_used_in_internal_repo(symbol, internal_repo_full_name, github_token=None):
    if not symbol or not internal_repo_full_name:
        return False
    # Use GitHub code search API
    headers = {"Accept": "application/vnd.github+json"}
    if github_token:
        headers["Authorization"] = f"token {github_token}"
    query = f'{symbol} repo:{internal_repo_full_name}'
    url = f'https://api.github.com/search/code?q={requests.utils.quote(query)}'
    resp = requests.get(url, headers=headers)
    if resp.status_code == 200:
        data = resp.json()
        return data.get('total_count', 0) > 0
    return False 