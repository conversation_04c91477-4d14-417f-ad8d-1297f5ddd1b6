from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .webhook_services import GitHubWebhookService
from urllib.parse import urlparse

def extract_owner_repo(repo_or_url):
    """Accepts 'owner/repo' or a full GitHub repo URL and returns (owner, repo_name)."""
    if repo_or_url.startswith('http'):
        parsed = urlparse(repo_or_url)
        path = parsed.path.strip('/')
        # path is like 'owner/repo' or 'owner/repo.git'
        parts = path.split('/')
        if len(parts) >= 2:
            owner, repo_name = parts[0], parts[1]
            if repo_name.endswith('.git'):
                repo_name = repo_name[:-4]
            return owner, repo_name
        raise ValueError('Invalid GitHub repo URL')
    else:
        # Assume 'owner/repo'
        owner, repo_name = repo_or_url.split('/')
        return owner, repo_name

class WebhookListCreateView(APIView):
    def get(self, request):
        repo = request.query_params.get("repo")
        token = request.query_params.get("github_token")
        if not repo or not token:
            return Response({"error": "repo and github_token required"}, status=400)
        try:
            owner, repo_name = extract_owner_repo(repo)
        except Exception as e:
            return Response({"error": f"Invalid repo: {str(e)}"}, status=400)
        service = GitHubWebhookService(token)
        try:
            import asyncio
            webhooks = asyncio.run(service.list_webhooks(owner, repo_name))
            return Response(webhooks)
        except Exception as e:
            if hasattr(e, 'response') and e.response is not None:
                try:
                    return Response({"error": e.response.text}, status=e.response.status_code)
                except Exception:
                    pass
            return Response({"error": str(e)}, status=500)

    def post(self, request):
        repo = request.data.get("repo")
        token = request.data.get("github_token")
        webhook_url = request.data.get("webhook_url")
        events = request.data.get("events", ["push"])
        secret = request.data.get("secret")
        if not repo or not token or not webhook_url:
            return Response({"error": "repo, github_token, and webhook_url required"}, status=400)
        try:
            owner, repo_name = extract_owner_repo(repo)
        except Exception as e:
            return Response({"error": f"Invalid repo: {str(e)}"}, status=400)
        service = GitHubWebhookService(token)
        try:
            import asyncio
            webhook = asyncio.run(service.create_webhook(owner, repo_name, webhook_url, events, secret))
            return Response(webhook, status=201)
        except Exception as e:
            if hasattr(e, 'response') and e.response is not None:
                try:
                    return Response({"error": e.response.text}, status=e.response.status_code)
                except Exception:
                    pass
            return Response({"error": str(e)}, status=500)

class WebhookDeleteView(APIView):
    def delete(self, request):
        repo = request.data.get("repo")
        token = request.data.get("github_token")
        hook_id = request.data.get("hook_id")
        if not repo or not token or not hook_id:
            return Response({"error": "repo, github_token, and hook_id required"}, status=400)
        try:
            owner, repo_name = extract_owner_repo(repo)
        except Exception as e:
            return Response({"error": f"Invalid repo: {str(e)}"}, status=400)
        service = GitHubWebhookService(token)
        try:
            import asyncio
            ok = asyncio.run(service.delete_webhook(owner, repo_name, int(hook_id)))
            return Response({"deleted": ok})
        except Exception as e:
            if hasattr(e, 'response') and e.response is not None:
                try:
                    return Response({"error": e.response.text}, status=e.response.status_code)
                except Exception:
                    pass
            return Response({"error": str(e)}, status=500) 