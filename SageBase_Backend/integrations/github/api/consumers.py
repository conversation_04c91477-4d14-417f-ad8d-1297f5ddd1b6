import json
from channels.generic.websocket import AsyncWebsocketConsumer
import logging

logger = logging.getLogger(__name__)

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        logger.debug(f"WebSocket CONNECT {self.channel_name} connected")
        logger.info(f"Channel layer backend: {type(self.channel_layer)}")
        await self.accept()
        self.joined_repos = set()

    async def disconnect(self, close_code):
        logger.debug(f"WebSocket DISCONNECT {self.channel_name} disconnected. Leaving repos: {self.joined_repos}")
        for repo in self.joined_repos:
            await self.channel_layer.group_discard(repo, self.channel_name)

    async def receive(self, text_data):
        data = json.loads(text_data)
        action = data.get("action")
        repo = data.get("repo")
        if action == "subscribe" and repo:
            await self.channel_layer.group_add(repo, self.channel_name)
            self.joined_repos.add(repo)
            logger.debug(f"WebSocket SUBSCRIBE {self.channel_name} subscribed to {repo}")
        elif action == "unsubscribe" and repo:
            await self.channel_layer.group_discard(repo, self.channel_name)
            self.joined_repos.discard(repo)
            logger.debug(f"WebSocket UNSUBSCRIBE {self.channel_name} unsubscribed from {repo}")

    async def notify(self, event):
        logger.debug(f"WebSocket NOTIFY sending notification to {self.channel_name}: {event['notification']}")
        await self.send(text_data=json.dumps(event["notification"]))

    async def platform_uninstalled(self, event):
        logger.debug(f"WebSocket PLATFORM_UNINSTALLED sending platform uninstall to {self.channel_name}: {event}")
        await self.send(text_data=json.dumps({
            "type": event.get("type", "platform.uninstalled"),
            "platform": event.get("platform"),
            "message": event.get("message")
        })) 