from django.conf import settings
from django.http import HttpResponseRedirect, JsonResponse
from rest_framework.decorators import api_view
from rest_framework.response import Response
from integrations.models import CompanyIntegration, IntegrationTool
from django.contrib.auth import get_user_model
from .services import exchange_code_for_access_token
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

@api_view(['GET'])
def github_app_install(request):
    """
    Redirects to GitHub App installation page.
    """
    app_slug = settings.GITHUB_APP_SLUG  # This is the app's slug
    install_url = f"https://github.com/apps/{app_slug}/installations/new"
    return HttpResponseRedirect(install_url)

@api_view(['GET'])
def github_app_callback(request):
    """
    Handles the redirect from GitHub App installation with OAuth.
    Receives both code (for OAuth) and installation_id (for app install).
    Exchanges code for tokens and redirects to frontend for database saving.
    """
    code = request.GET.get("code")
    installation_id = request.GET.get("installation_id")
    
    if not code :
        logger.error("Github app callback: No code provided")
        return JsonResponse({"error": "No code provided"}, status=400)
    
    if not installation_id:
        logger.error("Github app callback: No installation_id provided")
        return JsonResponse({"error": "Missing installation_id"}, status=400)
    
    try:
        # Exchange code for access token using the existing service
        token_data = exchange_code_for_access_token(code)
        logger.debug("token response after code exchange: ", token_data)
        if not token_data or "error" in token_data:
            logger.error("Github app callback: Failed to get access token, check the git hub app settings, client id and client secret")
            return JsonResponse({"error": "Failed to get access token"}, status=400)
        
        access_token = token_data.get("access_token")  # ghu_...
        refresh_token = token_data.get("refresh_token")  # ghr_...
        
        if not access_token:
            logger.error("Github app callback: Failed to get access token")
            return JsonResponse({"error": "Failed to get access token"}, status=400)
        
        # Redirect to frontend with tokens and installation_id for database saving
        frontend_redirect_url = f"{settings.FRONTEND_BASE_URL}/github/callback?installation_id={installation_id}&token={access_token}&refresh_token={refresh_token}"
        return HttpResponseRedirect(frontend_redirect_url)
            
    except Exception as e:
        return JsonResponse({"error": f"Unexpected error: {str(e)}"}, status=500)

@api_view(['POST'])
def github_disconnect(request):
    """
    Provide uninstall link for the GitHub App installation based on company_id.
    """
    company_id = request.data.get("company_id")
    if not company_id:
        return Response({"error": "No company_id provided"}, status=400)

    # Find the GitHub integration for this company
    tool = IntegrationTool.objects.filter(slug="github").first() or IntegrationTool.objects.filter(name__iexact="github").first()
    if not tool:
        return Response({"error": "GitHub tool not found"}, status=404)

    integration = CompanyIntegration.objects.filter(company_id=company_id, tool=tool, is_active=True).first()
    if not integration:
        return Response({"error": "GitHub integration not found for this company"}, status=404)

    cfg = integration.config or {}
    installation_id = cfg.get("installation_id")
    if not installation_id:
        return Response({"error": "No installation_id configured for this company"}, status=400)

    uninstall_url = f"https://github.com/settings/installations/{installation_id}"
    return Response({
        "message": "To uninstall the GitHub App, visit this URL in your browser.",
        "uninstall_url": uninstall_url
    })