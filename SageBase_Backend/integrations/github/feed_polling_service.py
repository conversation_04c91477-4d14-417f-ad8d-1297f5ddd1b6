import time
import feedparser
import logging
import requests
from integrations.models import CrossRepoMonitor, CompanyIntegration, IntegrationTool
import datetime
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("atom_feed_poller")
BACKEND_BASE_URL = os.getenv("BACKEND_BASE_URL")
last_seen_commit = {}

def poll_external_repo_commits():
    cross_monitors = CrossRepoMonitor.objects.all()
    if not cross_monitors:
        logger.info("No cross-repo monitors found.")
        return
    logger.info(f"Polling {cross_monitors.count()} cross-repo monitors...")
    for monitor in cross_monitors:
        logger.info(f"Monitor: {monitor.internal_repo} | External repos: {monitor.external_repos}")
        # Get company integration for GitHub
        company = monitor.company
        internal_repo = monitor.internal_repo
        github_tool = IntegrationTool.objects.filter(slug="github").first()
        company_integration = CompanyIntegration.objects.filter(company=company, tool=github_tool, is_active=True).first()
        github_token = None
        if company_integration and company_integration.config:
            try:
                # Use the new automatic token refresh functionality
                github_token = company_integration.get_valid_github_token()
                logger.debug(f"Successfully obtained valid GitHub token for company {company.name}")
            except Exception as e:
                logger.warning(f"Failed to get valid GitHub token for company {company.name}: {e}")
                # Fallback to old method for backward compatibility
                github_token = company_integration.config.get("ghu_token")
        for repo_full_name in monitor.external_repos:
            feed_url = f"https://github.com/{repo_full_name}/commits/main.atom"
            logger.info(f"Fetching feed: {feed_url}")
            parsed = feedparser.parse(feed_url)
            if not parsed.entries:
                logger.info(f"No entries found for {repo_full_name}")
                continue
            last_id = last_seen_commit.get(repo_full_name)
            new_entries = []
            for entry in parsed.entries:
                entry_dt = None
                if hasattr(entry, 'updated'):
                    entry_dt = datetime.datetime.strptime(entry.updated, "%Y-%m-%dT%H:%M:%SZ")
                if entry_dt and entry_dt < monitor.created_at.replace(tzinfo=None):
                    continue
                if entry.id == last_id:
                    break
                new_entries.append(entry)

            if new_entries:
                logger.info(f"Found {len(new_entries)} new commits for {repo_full_name}")
                last_seen_commit[repo_full_name] = new_entries[0].id
                for idx, entry in enumerate(reversed(new_entries)):
                    logger.info(f"New commit: {entry.title} ({entry.link}) at {getattr(entry, 'published', getattr(entry, 'updated', None))}")
                    # Extract commit hash from entry.id (format: tag:github.com,2008:Grit::Commit/<hash>)
                    def extract_hash(commit_id):
                        if commit_id and isinstance(commit_id, str) and '/' in commit_id:
                            return commit_id.split('/')[-1]
                        return commit_id
                    commit_hash = extract_hash(entry.id)
                    previous_commit_id = None
                    if idx < len(new_entries) - 1:
                        previous_commit_id = extract_hash(new_entries[::-1][idx + 1].id)
                    payload = {
                        "repo": repo_full_name,
                        "commit_id": commit_hash,
                        "previous_commit_id": previous_commit_id,
                        "commit_url": entry.link,
                        "github_token": github_token,
                        "internal_repo": internal_repo,
                    }
                    try:
                        r = requests.post(f"{BACKEND_BASE_URL}/api/integrations/process_external_commit/", json=payload, timeout=10)
                        logger.info(f"Notified backend for commit {commit_hash}: {r.status_code}")
                    except Exception as e:
                        logger.error(f"Failed to notify backend for commit {commit_hash}: {e}")
            else:
                logger.info(f"No new commits for {repo_full_name}")

def run_atom_feed_poller():
    while True:
        poll_external_repo_commits()
        time.sleep(60)  # poll every minute

# To run: python manage.py shell -c "from integrations.github.feed_polling_service import