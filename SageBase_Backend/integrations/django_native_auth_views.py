"""
Django-native authentication views to replace Supabase Auth integration.
Provides login, registration, email verification, and password reset functionality.
"""
import logging
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import authenticate
from django.utils.http import urlsafe_base64_decode
from integrations.models import User
from integrations.services.django_auth_service import DjangoAuthService, EnhancedInvitationService

logger = logging.getLogger(__name__)


@api_view(['POST'])
def login(request):
    """
    Authenticate user and return JWT token
    
    Expected payload:
    {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    Returns:
    {
        "success": true,
        "message": "Login successful",
        "token": "jwt_token",
        "user": {
            "id": "user_id",
            "email": "<EMAIL>",
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "role": "USER",
            "company": {
                "id": "company_id",
                "name": "Company Name"
            }
        }
    }
    """
    try:
        email = request.data.get('email', '').strip().lower()
        password = request.data.get('password', '')
        
        if not email or not password:
            return Response({
                'success': False,
                'error': 'Email and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        auth_service = DjangoAuthService()
        user, token = auth_service.authenticate_user(email, password)
        
        if user and token:
            # Prepare user data for response
            user_data = {
                'id': str(user.id),
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'company': {
                    'id': str(user.company.id),
                    'name': user.company.name
                } if user.company else None
            }
            
            return Response({
                'success': True,
                'message': 'Login successful',
                'token': token,
                'user': user_data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': 'Invalid email or password'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
    except Exception as e:
        logger.error(f"Error during login: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred during login'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def register(request):
    """
    Register a new user
    
    Expected payload:
    {
        "email": "<EMAIL>",
        "password": "password123",
        "first_name": "John",
        "last_name": "Doe",
        "company_id": "optional_company_id"
    }
    
    Returns:
    {
        "success": true,
        "message": "Registration successful. Please check your email for verification.",
        "user": {
            "id": "user_id",
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe"
        }
    }
    """
    try:
        email = request.data.get('email', '').strip().lower()
        password = request.data.get('password', '')
        first_name = request.data.get('first_name', '').strip()
        last_name = request.data.get('last_name', '').strip()
        company_id = request.data.get('company_id')
        
        # Validation
        if not all([email, password, first_name, last_name]):
            return Response({
                'success': False,
                'error': 'Email, password, first name, and last name are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if len(password) < 8:
            return Response({
                'success': False,
                'error': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if user already exists
        if User.objects.filter(email=email).exists():
            return Response({
                'success': False,
                'error': 'User with this email already exists'
            }, status=status.HTTP_409_CONFLICT)
        
        # Create user
        auth_service = DjangoAuthService()
        user_metadata = {
            'first_name': first_name,
            'last_name': last_name,
            'company_id': company_id,
            'role': User.Role.USER
        }
        
        user = auth_service.create_django_user(email, password, user_metadata)
        
        if user:
            user_data = {
                'id': str(user.id),
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active
            }
            
            return Response({
                'success': True,
                'message': 'Registration successful. Please check your email for verification.',
                'user': user_data
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'error': 'Failed to create user'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error during registration: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred during registration'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def verify_email(request):
    """
    Verify user email address
    
    Expected payload:
    {
        "uid": "base64_encoded_user_id",
        "token": "verification_token"
    }
    
    Returns:
    {
        "success": true,
        "message": "Email verified successfully",
        "token": "jwt_token",
        "user": {...}
    }
    """
    try:
        uid = request.data.get('uid')
        token = request.data.get('token')
        
        if not uid or not token:
            return Response({
                'success': False,
                'error': 'UID and token are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        auth_service = DjangoAuthService()
        user = auth_service.verify_email(uid, token)
        
        if user:
            # Generate JWT token for the verified user
            jwt_token = auth_service.generate_jwt_token(user)
            
            user_data = {
                'id': str(user.id),
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'company': {
                    'id': str(user.company.id),
                    'name': user.company.name
                } if user.company else None
            }
            
            return Response({
                'success': True,
                'message': 'Email verified successfully',
                'token': jwt_token,
                'user': user_data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': 'Invalid or expired verification link'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error during email verification: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred during email verification'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def request_password_reset(request):
    """
    Request password reset email
    
    Expected payload:
    {
        "email": "<EMAIL>"
    }
    
    Returns:
    {
        "success": true,
        "message": "If an account with this email exists, a password reset email has been sent."
    }
    """
    try:
        email = request.data.get('email', '').strip().lower()
        
        if not email:
            return Response({
                'success': False,
                'error': 'Email is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
            if user.is_active:
                auth_service = DjangoAuthService()
                auth_service.send_password_reset_email(user)
        except User.DoesNotExist:
            # Don't reveal whether the user exists or not
            pass
        
        # Always return success to prevent email enumeration
        return Response({
            'success': True,
            'message': 'If an account with this email exists, a password reset email has been sent.'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error during password reset request: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred while processing your request'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def validate_reset_token(request):
    """
    Validate password reset token without actually resetting the password
    
    Query parameters:
    - uid: base64_encoded_user_id
    - token: reset_token
    
    Returns:
    {
        "success": true,
        "user": {"email": "<EMAIL>", "name": "User Name"}
    }
    """
    try:
        uid = request.GET.get('uid')
        token = request.GET.get('token')
        
        if not all([uid, token]):
            return Response({
                'success': False,
                'error': 'UID and token are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate token without resetting password
        try:
            from django.utils.http import urlsafe_base64_decode
            from django.contrib.auth.tokens import default_token_generator
            
            # Decode user ID
            user_id = urlsafe_base64_decode(uid).decode()
            user = User.objects.get(pk=user_id)
            
            # Check if token is valid
            if default_token_generator.check_token(user, token):
                return Response({
                    'success': True,
                    'user': {
                        'email': user.email,
                        'name': f"{user.first_name} {user.last_name}".strip()
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'error': 'Invalid or expired password reset link'
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response({
                'success': False,
                'error': 'Invalid password reset link'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error validating reset token: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred while validating the reset link'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def reset_password(request):
    """
    Reset password using token
    
    Expected payload:
    {
        "uid": "base64_encoded_user_id",
        "token": "reset_token",
        "new_password": "new_password123"
    }
    
    Returns:
    {
        "success": true,
        "message": "Password reset successfully",
        "token": "jwt_token"
    }
    """
    try:
        uid = request.data.get('uid')
        token = request.data.get('token')
        new_password = request.data.get('new_password')
        
        if not all([uid, token, new_password]):
            return Response({
                'success': False,
                'error': 'UID, token, and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if len(new_password) < 8:
            return Response({
                'success': False,
                'error': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        auth_service = DjangoAuthService()
        user = auth_service.reset_password(uid, token, new_password)
        
        if user:
            # Generate new JWT token
            jwt_token = auth_service.generate_jwt_token(user)
            
            return Response({
                'success': True,
                'message': 'Password reset successfully',
                'token': jwt_token
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': 'Invalid or expired password reset link'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error during password reset: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred during password reset'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def resend_verification_email(request):
    """
    Resend email verification
    
    Expected payload:
    {
        "email": "<EMAIL>"
    }
    
    Returns:
    {
        "success": true,
        "message": "If an unverified account with this email exists, a verification email has been sent."
    }
    """
    try:
        email = request.data.get('email', '').strip().lower()
        
        if not email:
            return Response({
                'success': False,
                'error': 'Email is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
            if not user.is_active:  # Only send to inactive users
                auth_service = DjangoAuthService()
                auth_service.send_email_verification(user)
        except User.DoesNotExist:
            # Don't reveal whether the user exists or not
            pass
        
        # Always return success to prevent email enumeration
        return Response({
            'success': True,
            'message': 'If an unverified account with this email exists, a verification email has been sent.'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error during resend verification: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred while processing your request'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def create_company_admin(request):
    """
    Create company and admin user with Django auth 
    
    Expected payload:
    {
        "company_name": "Company Name",
        "admin_email": "<EMAIL>",
        "admin_first_name": "Admin",
        "admin_last_name": "User",
        "password": "optional_password"
    }
    
    Returns:
    {
        "success": true,
        "message": "Company and admin created successfully",
        "company": {...},
        "admin_user": {...}
    }
    """
    try:
        company_name = request.data.get('company_name', '').strip()
        admin_email = request.data.get('admin_email', '').strip().lower()
        admin_first_name = request.data.get('admin_first_name', '').strip()
        admin_last_name = request.data.get('admin_last_name', '').strip()
        password = request.data.get('password')
        
        # Validation
        if not all([company_name, admin_email, admin_first_name, admin_last_name]):
            return Response({
                'success': False,
                'error': 'Company name, admin email, first name, and last name are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if user already exists
        if User.objects.filter(email=admin_email).exists():
            return Response({
                'success': False,
                'error': 'User with this email already exists'
            }, status=status.HTTP_409_CONFLICT)
        
        # Create company and admin
        invitation_service = EnhancedInvitationService()
        company, admin_user = invitation_service.create_company_admin_with_django_auth(
            company_name=company_name,
            admin_email=admin_email,
            admin_first_name=admin_first_name,
            admin_last_name=admin_last_name,
            password=password
        )
        
        company_data = {
            'id': str(company.id),
            'name': company.name,
            'slug': company.slug
        }
        
        admin_data = {
            'id': str(admin_user.id),
            'email': admin_user.email,
            'first_name': admin_user.first_name,
            'last_name': admin_user.last_name,
            'role': admin_user.role
        }
        
        return Response({
            'success': True,
            'message': 'Company and admin created successfully! Welcome email sent.',
            'company': company_data,
            'admin_user': admin_data
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        logger.error(f"Error creating company admin: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred while creating company and admin'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def accept_invitation(request):
    """
    Accept team invitation with Django auth
    
    Expected payload:
    {
        "token": "invitation_token",
        "first_name": "User",
        "last_name": "Name",
        "password": "password123"
    }
    
    Returns:
    {
        "success": true,
        "message": "Invitation accepted successfully",
        "jwt_token": "jwt_token",
        "user": {...}
    }
    """
    try:
        token = request.data.get('token')
        first_name = request.data.get('first_name', '').strip()
        last_name = request.data.get('last_name', '').strip()
        password = request.data.get('password')
        
        # Validation
        if not all([token, first_name, last_name, password]):
            return Response({
                'success': False,
                'error': 'Token, first name, last name, and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if len(password) < 8:
            return Response({
                'success': False,
                'error': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Accept invitation
        invitation_service = EnhancedInvitationService()
        user = invitation_service.accept_invitation_with_django_auth(
            token=token,
            first_name=first_name,
            last_name=last_name,
            password=password
        )
        
        # Generate JWT token
        auth_service = DjangoAuthService()
        jwt_token = auth_service.generate_jwt_token(user)
        
        user_data = {
            'id': str(user.id),
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.role,
            'company': {
                'id': str(user.company.id),
                'name': user.company.name
            } if user.company else None
        }
        
        return Response({
            'success': True,
            'message': 'Invitation accepted successfully! Welcome to the team.',
            'jwt_token': jwt_token,
            'user': user_data
        }, status=status.HTTP_200_OK)
        
    except ValueError as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error accepting invitation: {str(e)}")
        return Response({
            'success': False,
            'error': 'An error occurred while accepting the invitation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)