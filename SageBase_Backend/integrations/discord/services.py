from integrations.models import CompanyIntegration, IntegrationTool
import logging

logger = logging.getLogger(__name__)


def get_company_discord_credentials(company):
    """
    Get Discord credentials for a company.
    
    Args:
        company: Company instance
        
    Returns:
        Dict with Discord credentials or None if not configured
    """
    try:
        discord_tool = IntegrationTool.objects.get(slug="discord")
        integration = CompanyIntegration.objects.filter(
            company=company,
            tool=discord_tool,
            is_active=True
        ).first()
        
        if integration and integration.config:
            return {
                "bot_token": integration.config.get("bot_token"),
                "guild_id": integration.config.get("server_id"),
                "server_name": integration.config.get("server_name")
            }
        
        return None
        
    except IntegrationTool.DoesNotExist:
        logger.warning(f"Discord integration tool not found")
        return None
    except Exception as e:
        logger.error(f"Error getting Discord credentials for company {company.id}: {e}")
        return None


def get_company_discord_bot_token(company):
    """
    Get Discord bot token for a company.
    
    Args:
        company: Company instance
        
    Returns:
        Discord bot token string or None if not configured
    """
    credentials = get_company_discord_credentials(company)
    return credentials.get("bot_token") if credentials else None


def get_company_discord_guild_id(company):
    """
    Get Discord guild ID for a company.
    
    Args:
        company: Company instance
        
    Returns:
        Discord guild ID string or None if not configured
    """
    credentials = get_company_discord_credentials(company)
    return credentials.get("guild_id") if credentials else None
