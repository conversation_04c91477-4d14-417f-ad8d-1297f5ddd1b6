import os
import sys
import threading
import asyncio
import logging
from django.apps import AppConfig
from django.conf import settings

logger = logging.getLogger(__name__)

# Global flag to prevent multiple bot instances
_bot_started = False

class DiscordConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'integrations.discord'
    
    def ready(self):
        """Called when Django app is ready."""
        global _bot_started
        
        # Skip if bot is already started
        if _bot_started:
            logger.info('Discord bot already started, skipping duplicate initialization')
            return
        
        # Only start in main process (not Django auto-reloader)
        if os.environ.get('RUN_MAIN') != 'true':
            logger.info('Skipping Discord bot start - not main Django process')
            return
        
        # Only auto-start bot if explicitly enabled
        if os.getenv('DISCORD_BOT_AUTO_START', '').lower() == 'true':
            # Avoid starting bot during migrations or other management commands
            if 'runserver' in sys.argv and 'migrate' not in sys.argv:
                self.start_discord_bot_background()
                _bot_started = True
    
    def start_discord_bot_background(self):
        """Start Discord bot in background thread."""
        bot_token = os.getenv('DISCORD_BOT_TOKEN')
        if not bot_token:
            logger.warning('DISCORD_BOT_TOKEN not set - Discord bot will not start')
            return
        
        # Additional safety check for existing bot threads
        existing_threads = [t for t in threading.enumerate() if t.name == 'discord-bot']
        if existing_threads:
            logger.warning(f'Discord bot thread already exists: {existing_threads[0]}')
            return
        
        logger.info(f'🚀 Starting Discord bot in background... (PID: {os.getpid()})')
        logger.info(f'   RUN_MAIN: {os.environ.get("RUN_MAIN", "not set")}')
        logger.info(f'   Command: {" ".join(sys.argv)}')
        
        # Start bot in background thread
        bot_thread = threading.Thread(
            target=self._run_discord_bot,
            daemon=True,
            name='discord-bot'
        )
        bot_thread.start()
        logger.info(f'✅ Discord bot started in background thread: {bot_thread.name}')
    
    def _run_discord_bot(self):
        """Run Discord bot in thread."""
        global _bot_started
        
        try:
            # Add messaging directory to path
            sys.path.append(os.path.join(settings.BASE_DIR, 'messaging', 'discord'))
            
            from discord_bot import DiscordBot
            
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Configure bot
            bot_token = os.getenv('DISCORD_BOT_TOKEN')
            webhook_url = os.getenv('DISCORD_WEBHOOK_URL')
            webhook_secret = os.getenv('DISCORD_WEBHOOK_SECRET', 'default_secret_key')
            
            # Set environment variables
            os.environ['DISCORD_WEBHOOK_URL'] = webhook_url
            os.environ['DISCORD_WEBHOOK_SECRET'] = webhook_secret
            
            # Start bot
            bot = DiscordBot()
            logger.info(f'🤖 Discord bot initialized, connecting to Discord... (Thread: {threading.current_thread().name})')
            
            try:
                bot.run(bot_token)
            except Exception as e:
                logger.error(f'❌ Discord bot error: {e}')
            finally:
                # Reset flag when bot stops
                _bot_started = False
                logger.info('🛑 Discord bot stopped, flag reset')
                
        except Exception as e:
            logger.error(f'❌ Failed to start Discord bot: {e}')
            _bot_started = False 