import os
import sys
import asyncio
import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

# Add the messaging directory to Python path
sys.path.append(os.path.join(settings.BASE_DIR, 'messaging', 'discord'))

# Set up logger for this management command
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Start the Discord bot for real-time message processing and Gateway events'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.help += '\n\nNOTE: You can also enable auto-start by setting DISCORD_BOT_AUTO_START=true in your environment.\nThis will automatically start the bot when Django starts with Uvicorn.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--token',
            type=str,
            help='Discord bot token (or set DISCORD_BOT_TOKEN environment variable)'
        )
        parser.add_argument(
            '--webhook-url',
            type=str,
            help='Django webhook URL for Gateway events'
        )
        parser.add_argument(
            '--webhook-secret',
            type=str,
            default='default_secret_key',
            help='Webhook secret for HMAC verification'
        )
        parser.add_argument(
            '--debug',
            action='store_true',
            help='Enable debug logging'
        )

    def handle(self, *args, **options):
        # Check if auto-start is enabled
        if getattr(settings, 'DISCORD_BOT_AUTO_START', False):
            logger.warning('⚠️  WARNING: Discord bot auto-start is ENABLED!')
            logger.warning('   The bot will automatically start when Django starts with Uvicorn.')
            logger.warning('   Running this command manually may cause duplicate bot instances.')
            logger.warning('   To disable auto-start, set: DISCORD_BOT_AUTO_START=false')
            logger.warning('   Continue anyway? (y/N): ')
            
            response = input().strip().lower()
            if response != 'y':
                logger.info('Cancelled. Use auto-start or disable it first.')
                return
        
        # Configure logging
        log_level = logging.DEBUG if options['debug'] else logging.INFO
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('discord_bot.log', encoding='utf-8')
            ]
        )
        
        # Get configuration
        bot_token = options['token'] or os.getenv('DISCORD_BOT_TOKEN')
        webhook_url = options['webhook_url'] or os.getenv('DISCORD_WEBHOOK_URL')
        webhook_secret = options['webhook_secret'] or os.getenv('DISCORD_WEBHOOK_SECRET', 'default_secret_key')
        
        if not bot_token:
            raise CommandError(
                'Discord bot token is required. Set DISCORD_BOT_TOKEN environment variable '
                'or use --token argument'
            )
        
        # Display configuration
        logger.info('Starting Discord Bot...')
        logger.info('=' * 60)
        logger.info(f'Process ID: {os.getpid()}')
        logger.info(f'Bot token: {"*" * 40}...{bot_token[-4:]}')
        logger.info(f'Webhook URL: {webhook_url}')
        logger.info(f'Webhook secret: {"*" * len(webhook_secret)}')
        logger.info(f'Debug mode: {options["debug"]}')
        logger.info('=' * 60)
        logger.warning('⚠️  WARNING: Make sure only ONE instance of this bot is running!')
        logger.warning('   Multiple instances will cause duplicate message processing.')
        logger.info('=' * 60)
        
        # Set environment variables for the bot
        os.environ['DISCORD_BOT_TOKEN'] = bot_token
        os.environ['DISCORD_WEBHOOK_URL'] = webhook_url
        os.environ['DISCORD_WEBHOOK_SECRET'] = webhook_secret
        
        # Import and run the bot
        try:
            from discord_bot import DiscordBot
            
            logger.info('✅ Discord bot initialized')
            logger.warning('Press Ctrl+C to stop the bot')
            
            # Run the bot
            bot = DiscordBot()
            
            # Create event loop if not exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run the bot
            try:
                bot.run(bot_token)
            except KeyboardInterrupt:
                logger.warning('🛑 Stopping Discord bot...')
                loop.run_until_complete(bot.close())
                logger.info('Discord bot stopped')
            except Exception as e:
                logger.error(f'❌ Bot error: {e}')
                raise CommandError(f'Discord bot failed: {e}')
            
        except ImportError as e:
            raise CommandError(f'Failed to import Discord bot: {e}')
        except Exception as e:
            logger.error(f'❌ Unexpected error: {e}')
            raise CommandError(f'Discord bot failed: {e}') 