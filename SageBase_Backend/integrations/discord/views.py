import json
import hmac
import hashlib
import asyncio
import os
import threading
from typing import List
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.http import HttpResponseRedirect, JsonResponse
from django.utils import timezone
from rest_framework.decorators import api_view
from rest_framework.response import Response
import requests
from urllib.parse import urlencode
import logging

from integrations.models import Company, IntegrationTool, CompanyIntegration

# Import AI agent and Discord service
from knowledge_spaces_Q_A.chat_response_formatting_agent import ChatOutputFormat
from messaging.discord.discord_service import DiscordService

# Set up logging
logger = logging.getLogger(__name__)


@api_view(['GET'])
def discord_oauth_start(request):
    """
    Redirects admin to Discord bot authorization flow.
    """
    company_id = request.GET.get("company_id")
    if not company_id:
        return JsonResponse({"error": "company_id parameter required"}, status=400)

    # Verify company exists
    try:
        Company.objects.get(id=company_id)
    except Company.DoesNotExist:
        return JsonResponse({"error": "Company not found"}, status=404)

    base_url = "https://discord.com/api/oauth2/authorize"
    params = {
        "client_id": settings.DISCORD_CLIENT_ID,
        "permissions": "8",  # Admin permissions
        "scope": "bot applications.commands",
        "redirect_uri": settings.DISCORD_REDIRECT_URI,
        "response_type": "code",
        "state": company_id  # Pass company_id in state
    }

    # Clean the redirect URI to remove any trailing whitespace
    clean_redirect_uri = settings.DISCORD_REDIRECT_URI.strip()
    
    # Manually construct URL to avoid urlencode() bug with redirect_uri
    from urllib.parse import quote
    redirect_uri_encoded = quote(clean_redirect_uri, safe='')
    
    other_params = {
        "client_id": settings.DISCORD_CLIENT_ID,
        "permissions": "8",
        "scope": "bot applications.commands",
        "response_type": "code",
        "state": company_id
    }
    url = f"{base_url}?redirect_uri={redirect_uri_encoded}&{urlencode(other_params)}"
    return HttpResponseRedirect(url)

@api_view(['GET'])
def discord_oauth_callback(request):
    """
    Simplified Discord OAuth callback handler.
    """
    logger.info("=== DISCORD CALLBACK STARTED ===")
    logger.info(f"Full request URL: {request.build_absolute_uri()}")
    logger.info(f"Parameters: {dict(request.GET)}")
    logger.info(f"Frontend URL setting: {settings.FRONTEND_BASE_URL}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")
    
    # Check for Discord OAuth error
    error = request.GET.get("error")
    if error:
        logger.error(f"Discord OAuth error: {error}")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/discord/callback?error={error}"
        logger.info(f"Redirecting to: {redirect_url}")
        response = HttpResponseRedirect(redirect_url)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response
    
    # Extract required parameters
    code = request.GET.get("code")
    company_id = request.GET.get("state")
    guild_id = request.GET.get("guild_id")
    
    logger.info(f"Extracted - code: {code[:10] if code else 'None'}..., company_id: {company_id}, guild_id: {guild_id}")
    
    if not code or not company_id:
        logger.error("Missing code or company_id")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/discord/callback?error=missing_parameters"
        logger.info(f"Redirecting to: {redirect_url}")
        response = HttpResponseRedirect(redirect_url)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response
    
    if not guild_id:
        logger.error("Missing guild_id")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/discord/callback?error=missing_guild_id"
        logger.info(f"Redirecting to: {redirect_url}")
        response = HttpResponseRedirect(redirect_url)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response
    
    try:
        # Get company
        logger.info(f"Looking up company with ID: {company_id}")
        company = Company.objects.get(id=company_id)
        logger.info(f"Found company: {company.name}")
        
        # Get or create Discord tool
        discord_tool, tool_created = IntegrationTool.objects.get_or_create(
            slug="discord",
            defaults={
                "name": "Discord",
                "category": "COMMUNICATION",
                "description": "Discord bot integration for messaging",
                "is_active": True
            }
        )
        logger.info(f"Discord tool: {discord_tool.name} (created: {tool_created})")
        
        # Save integration
        integration, created = CompanyIntegration.objects.update_or_create(
            company=company,
            tool=discord_tool,
            defaults={
                "status": "CONNECTED",
                "is_active": True,
                "connected_at": timezone.now(),
                "config": {
                    "bot_token": settings.DISCORD_BOT_TOKEN,
                    "server_id": guild_id,
                    "server_name": f"Discord Server {guild_id}",
                    "connected_by": "admin"
                }
            }
        )
        
        logger.info(f"Integration saved: {integration.id} (created: {created})")
        logger.info(f"Integration config: {integration.config}")
        
        # Redirect to frontend with success
        redirect_url = f"{settings.FRONTEND_BASE_URL}/discord/callback?guild_id={guild_id}&success=true"
        logger.info(f"SUCCESS! Redirecting to: {redirect_url}")
        
        # Create response with proper headers to ensure redirect works
        response = HttpResponseRedirect(redirect_url)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        response['X-Frame-Options'] = 'SAMEORIGIN'
        response['X-Content-Type-Options'] = 'nosniff'
        
        return response
        
    except Company.DoesNotExist:
        logger.error(f"Company not found: {company_id}")
        redirect_url = f"{settings.FRONTEND_BASE_URL}/discord/callback?error=company_not_found"
        logger.info(f"Redirecting to: {redirect_url}")
        response = HttpResponseRedirect(redirect_url)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response
    except Exception as e:
        logger.error(f"Error saving integration: {str(e)}", exc_info=True)
        redirect_url = f"{settings.FRONTEND_BASE_URL}/discord/callback?error=integration_failed"
        logger.info(f"Redirecting to: {redirect_url}")
        response = HttpResponseRedirect(redirect_url)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response


@api_view(['GET'])
def discord_callback_test(request):
    """
    Test endpoint to verify Discord callback is working.
    """
    return JsonResponse({
        "message": "Discord callback endpoint is working!",
        "method": request.method,
        "parameters": dict(request.GET),
        "headers": dict(request.headers),
        "frontend_url": settings.FRONTEND_BASE_URL,
        "backend_url": getattr(settings, 'BACKEND_BASE_URL', 'Not set'),
        "discord_redirect_uri": settings.DISCORD_REDIRECT_URI,
        "timestamp": timezone.now().isoformat(),
        "callback_url": request.build_absolute_uri().replace('/callback-test/', '/callback/'),
        "test_redirect_url": f"{settings.FRONTEND_BASE_URL}/discord/callback?test=true"
    })


@api_view(['POST'])
def discord_disconnect(request):
    """
    Disconnect Discord integration for a company.
    """
    company_id = request.data.get("company_id")
    if not company_id:
        return Response({"error": "company_id required"}, status=400)
    
    try:
        company = Company.objects.get(id=company_id)
        discord_tool = IntegrationTool.objects.get(slug="discord")
        
        integration = CompanyIntegration.objects.filter(
            company=company,
            tool=discord_tool,
            is_active=True
        ).first()
        
        if not integration:
            return Response({"error": "Discord integration not found for this company"}, status=404)
        
        # Deactivate the integration
        integration.is_active = False
        integration.status = "DISCONNECTED"
        integration.save()
        
        return Response({"detail": "Discord integration disconnected successfully"})
        
    except Company.DoesNotExist:
        return Response({"error": "Company not found"}, status=404)
    except IntegrationTool.DoesNotExist:
        return Response({"error": "Discord integration tool not found"}, status=404)
    except Exception as e:
        return Response({"error": f"Failed to disconnect Discord: {str(e)}"}, status=500) 


@csrf_exempt
@require_http_methods(["POST"])
def discord_webhook(request):
    """
    Discord Gateway webhook endpoint to receive official Gateway events.
    Handles Discord's official Gateway API event format.
    """
    try:
        # Verify content type
        if request.content_type != "application/json":
            return JsonResponse({"error": "Content-Type must be application/json"}, status=400)
        
        # Get request body
        body = request.body
        if not body:
            return JsonResponse({"error": "Empty request body"}, status=400)
        
        # Verify HMAC signature (recommended for security)
        webhook_secret = getattr(settings, 'DISCORD_WEBHOOK_SECRET', 'default_secret_key')
        received_signature = request.headers.get('X-Signature', '')
        if webhook_secret and received_signature:
            expected_signature = hmac.new(
                webhook_secret.encode(),
                body,
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(received_signature, expected_signature):
                logger.warning("Discord webhook signature mismatch")
                return JsonResponse({"error": "Invalid signature"}, status=401)
        
        # Parse Gateway event
        try:
            gateway_event = json.loads(body)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in Discord webhook: {e}")
            return JsonResponse({"error": "Invalid JSON"}, status=400)
        
        # Validate Gateway event format
        if not isinstance(gateway_event, dict):
            return JsonResponse({"error": "Gateway event must be a JSON object"}, status=400)
        
        # Extract Gateway event components
        event_type = gateway_event.get("t")  # Event type (MESSAGE_CREATE, etc.)
        event_data = gateway_event.get("d")  # Event data
        opcode = gateway_event.get("op", 0)  # Operation code
        sequence = gateway_event.get("s")    # Sequence number
        timestamp = gateway_event.get("timestamp")
        
        if not event_type:
            return JsonResponse({"error": "Missing event type 't'"}, status=400)
        
        if event_data is None:
            return JsonResponse({"error": "Missing event data 'd'"}, status=400)
        
        # Log the Gateway event
        logger.info(f"Discord Gateway event received: {event_type}")
        logger.debug(f"Gateway event data: {json.dumps(event_data, indent=2)}")
        
        # GLOBAL SERVER AUTHORIZATION: Only process events from authorized servers
        authorized_integration = None
        guild_id = event_data.get("guild_id")
        
        if guild_id:
            authorized_integration = CompanyIntegration.objects.filter(
                tool__slug="discord",
                is_active=True,
                config__server_id=guild_id
            ).first()
            
            if not authorized_integration:
                logger.info(f"Skipping {event_type} from unauthorized server: {guild_id}")
                return JsonResponse({
                    "status": "ignored",
                    "event_type": event_type,
                    "processed": False,
                    "message": f"Server {guild_id} not authorized",
                    "timestamp": timezone.now().isoformat()
                })
            else:
                company_name = authorized_integration.company.name
                logger.info(f"✅ {event_type} from authorized server: {guild_id} (Company: {company_name})")
        else:
            # Handle DMs or events without guild_id - skip for now
            logger.debug(f"Skipping {event_type} - no guild_id (DM or system event)")
            return JsonResponse({
                "status": "ignored",
                "event_type": event_type,
                "processed": False,
                "message": "Events without guild_id not supported",
                "timestamp": timezone.now().isoformat()
            })
        
        # Process different Gateway event types with authorization context
        if event_type == "MESSAGE_CREATE":
            result = asyncio.run(process_message_create_event(event_data, authorized_integration))
        elif event_type == "BOT_STATUS":
            result = process_bot_status_event(event_data, authorized_integration)
        elif event_type == "TEST_EVENT":
            result = process_test_event(event_data, authorized_integration)
        else:
            logger.info(f"Unhandled Gateway event type: {event_type}")
            result = {"status": "ignored", "message": f"Event type {event_type} not processed"}
        
        # Return success response
        return JsonResponse({
            "status": "success",
            "event_type": event_type,
            "processed": result.get("processed", True),
            "message": result.get("message", "Gateway event processed successfully"),
            "timestamp": timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error processing Discord Gateway event: {e}")
        return JsonResponse({"error": "Internal server error"}, status=500)


# Global message cache for deduplication
_processed_messages = set()
_max_cache_size = 1000

def send_discord_reply_sync(channel_id: str, message: str):
    """Send reply to Discord in background thread with message length validation"""
    def _send():
        try:
            discord_service = DiscordService()
            bot_token = getattr(settings, 'DISCORD_BOT_TOKEN', 'MTM5MTc4NDgxMTE3MzEyMjE3OQ.GQFedD.1lOhb6zxhm5A-36J51jE_5shb701v1B4JBpmls')
            
            # Discord message length limit is 2000 characters
            if len(message) > 2000:
                # Split message into chunks
                chunks = []
                for i in range(0, len(message), 1950):  # Leave some buffer
                    chunk = message[i:i+1950]
                    if i > 0:
                        chunk = "..." + chunk
                    if i + 1950 < len(message):
                        chunk = chunk + "..."
                    chunks.append(chunk)
                
                # Send each chunk
                async def send_chunks():
                    await discord_service.authenticate({"bot_token": bot_token})
                    for i, chunk in enumerate(chunks):
                        if i > 0:
                            await asyncio.sleep(0.5)  # Small delay between chunks
                        result = await discord_service.send_message(channel_id, chunk)
                        logger.info(f"✅ Discord chunk {i+1}/{len(chunks)} sent: {result.get('status', 'success')}")
                    await discord_service.disconnect()
                
                asyncio.run(send_chunks())
            else:
                # Single message
                async def send_message():
                    await discord_service.authenticate({"bot_token": bot_token})
                    result = await discord_service.send_message(channel_id, message)
                    await discord_service.disconnect()
                    return result
                
                result = asyncio.run(send_message())
                logger.info(f"✅ Discord reply sent: {result.get('status', 'success')}")
                
        except Exception as e:
            logger.error(f"❌ Failed to send Discord reply: {e}")
    
    # Send in background thread
    thread = threading.Thread(target=_send)
    thread.daemon = True
    thread.start()

async def process_message_create_event(event_data, authorized_integration=None):
    """Process MESSAGE_CREATE Gateway event with deduplication."""
    global _processed_messages
    
    try:
        # Extract message data from official Discord format
        message_id = event_data.get("id")
        channel_id = event_data.get("channel_id")
        guild_id = event_data.get("guild_id")
        content = event_data.get("content", "")
        timestamp = event_data.get("timestamp")
        
        # DEDUPLICATION: Check if we've already processed this message
        if message_id in _processed_messages:
            logger.warning(f"🔄 DUPLICATE MESSAGE BLOCKED in Django: {message_id}")
            return {
                "processed": False,
                "message": f"Duplicate message {message_id} blocked",
                "duplicate": True
            }
        
        # Add to processed cache immediately
        _processed_messages.add(message_id)
        
        # Clean up cache if it gets too large
        if len(_processed_messages) > _max_cache_size:
            # Remove oldest half
            old_messages = list(_processed_messages)[:_max_cache_size // 2]
            _processed_messages.difference_update(old_messages)
            logger.info(f"🧹 Django message cache cleaned: {len(old_messages)} entries removed")
        
        # Extract author information
        author = event_data.get("author", {})
        author_id = author.get("id")
        author_name = author.get("username", "Unknown")
        is_bot = author.get("bot", False)
        
        # Skip bot messages
        if is_bot:
            logger.debug(f"🤖 Skipping bot message from {author_name}")
            return {
                "processed": False,
                "message": f"Bot message from {author_name} skipped",
                "bot_message": True
            }
        
        # Extract additional message data
        attachments = event_data.get("attachments", [])
        embeds = event_data.get("embeds", [])
        mentions = event_data.get("mentions", [])
        
        logger.info(f"MESSAGE_CREATE: {author_name} in channel {channel_id}: {content[:100]}... (ID: {message_id[:8]})")
        
        # Forward to AI agent for processing and reply using Chat Orchestrator (like Slack)
        if not is_bot and content.strip():
            company_name = authorized_integration.company.name.lower()
            logger.info(f"Forwarding message to Chat Orchestrator (company: {company_name}): {content[:50]}...")
            
            try:
                # Import Chat Orchestrator (same as Slack)
                from knowledge_spaces_Q_A.chat_orchestrator import ChatOrchestrator
                from knowledge_spaces_Q_A.chat_models import ConversationType, ChatRequest, ConversationMessage
            
                # Determine session type based on channel type (like Slack)
                # private chats are not possible in discord
                session_type = ConversationType.GROUP
                
                # Get conversation history (like Slack does)
                conversation_history : List[ConversationMessage] = []
                try:
                    # Import Discord service to get channel history
                    from messaging.discord.discord_service import DiscordService
                    discord_service = DiscordService()
                    await discord_service.authenticate({"bot_token": os.getenv('DISCORD_BOT_TOKEN')})

                    messages = await discord_service.receive_messages(channel_id=channel_id, limit=20, include_bot_messages=True)

                    # Cast each ReceivedMessage to ConversationMessage
                    conversation_history = [
                        ConversationMessage(
                            sender_id=msg.sender_id,
                            sender_name=msg.sender_name,
                            content=msg.content,
                            timestamp=msg.timestamp,
                        )
                        for msg in messages
                    ]
                    # Get recent messages for context (similar to Slack's approach)
                    # Note: This would need to be implemented in DiscordService
                    # For now, we'll use empty history but log the intent
                    logger.info(f"[Discord Webhook] 📚 Would fetch conversation history for channel {channel_id}")
                    
                except Exception as history_error:
                    logger.warning(f"[Discord Webhook] Could not fetch conversation history: {history_error}")
                
                # Create chat request from the received message (same pattern as Slack)
                chat_request = ChatRequest(
                    message=content,
                    user_id=author_id,
                    session_type=session_type,  # Now properly determined
                    company_name=company_name,
                    history=conversation_history,  # Now includes history
                    output_format=ChatOutputFormat.MARKDOWN
                )
                
                # Process the message through the chat orchestrator (same as Slack)
                logger.info(f"[Discord Webhook] 🔍 Processing with Chat Orchestrator...")
                chat_orchestrator = ChatOrchestrator()
                result = await asyncio.create_task(chat_orchestrator.process_chat_request(chat_request))
                
                # Let the Chat Orchestrator handle all decision-making and response logic
                response_text = result.get("response", "")
                agent_decisions = result.get("agent_decisions", [])
                context_used = result.get("context_used", {})
                no_action_needed = result.get("no_action_needed", False)

                logger.info(f"[Discord Webhook] 🤖 Chat Orchestrator Decision:")
                logger.info(f"   Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                logger.info(f"   No Action Needed: {no_action_needed}")
                logger.info(f"   Agent Decisions: {len(agent_decisions)}")
                logger.info(f"   Tools Used: {list(context_used.keys()) if context_used else 'None'}")
                
                # Send response if needed (Chat Orchestrator has already decided)
                if not no_action_needed and response_text and response_text.strip():
                    # Format response with references if available (same as Slack)
                    references = result.get("references", [])

                    response_content = response_text.strip()

                    if references:
                        response_content += f"\n\n📚 **Sources:** {', '.join(references)}"
                    
                    # Send AI response back to Discord
                    send_discord_reply_sync(channel_id, response_content)
                    
                    logger.info(f"[Discord Webhook] ✅ Sent response via Chat Orchestrator")
                    logger.info(f"   Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                    logger.info(f"   Agent Decisions: {len(agent_decisions)}")
                    logger.info(f"   Tools Used: {list(context_used.keys()) if context_used else 'None'}")
                else:
                    logger.info(f"[Discord Webhook] ℹ️ No response sent - Chat Orchestrator determined no action needed")
                    
            except Exception as e:
                logger.error(f"❌ Error processing message with Chat Orchestrator: {e}")
                send_discord_reply_sync(channel_id, f"Sorry, I encountered an error: {str(e)}")
            
        # Log message statistics
        logger.info(f"Message stats - Content: {len(content)} chars, Attachments: {len(attachments)}, Embeds: {len(embeds)}")
        
        return {
            "processed": True,
            "message": f"MESSAGE_CREATE processed for message {message_id}",
            "author": author_name,
            "channel": channel_id,
            "content_length": len(content),
            "has_attachments": len(attachments) > 0,
            "has_embeds": len(embeds) > 0,
            "message_id": message_id
        }
        
    except Exception as e:
        logger.error(f"Error processing MESSAGE_CREATE event: {e}")
        return {
            "processed": False,
            "message": f"Error processing MESSAGE_CREATE: {str(e)}"
        }


def process_bot_status_event(event_data, authorized_integration=None):
    """Process BOT_STATUS Gateway event."""
    try:
        event_type = event_data.get("event", "unknown")
        bot_id = event_data.get("bot_id")
        bot_name = event_data.get("bot_name")
        
        if event_type == "ready":
            guild_count = event_data.get("guild_count", 0)
            start_time = event_data.get("start_time")
            
            logger.info(f"Bot {bot_name} (ID: {bot_id}) is ready - Connected to {guild_count} guilds")
            
            # Update bot status in database if needed
            # TODO: Implement bot status tracking
            
        elif event_type == "disconnect":
            uptime_seconds = event_data.get("uptime_seconds", 0)
            messages_processed = event_data.get("messages_processed", 0)
            
            logger.info(f"Bot {bot_name} disconnected - Uptime: {uptime_seconds}s, Messages: {messages_processed}")
            
        return {
            "processed": True,
            "message": f"BOT_STATUS {event_type} processed for bot {bot_name}",
            "bot_id": bot_id,
            "event_type": event_type
        }
        
    except Exception as e:
        logger.error(f"Error processing BOT_STATUS event: {e}")
        return {
            "processed": False,
            "message": f"Error processing BOT_STATUS: {str(e)}"
        }


def process_test_event(event_data, authorized_integration=None):
    """Process TEST_EVENT Gateway event."""
    try:
        test_message = event_data.get("message", "No message")
        channel_id = event_data.get("channel_id")
        
        logger.info(f"TEST_EVENT received: {test_message} (Channel: {channel_id})")
        
        return {
            "processed": True,
            "message": f"TEST_EVENT processed: {test_message}",
            "channel_id": channel_id
        }
        
    except Exception as e:
        logger.error(f"Error processing TEST_EVENT: {e}")
        return {
            "processed": False,
            "message": f"Error processing TEST_EVENT: {str(e)}"
        } 