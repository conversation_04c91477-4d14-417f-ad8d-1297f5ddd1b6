from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .models import User, Company, IntegrationTool, CompanyIntegration, MonitoredItem, CrossRepoMonitor
import os
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers
import secrets
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
import requests
from integrations.github.services import revoke_github_authorization
from integrations.github.api.webhook_services import GitHubWebhookService
from integrations.github.api.webhooks import WebhookDeleteView
from integrations.github.api.services import get_cached_repositories
import asyncio
from django.conf import settings
from integrations.slack.views import revoke_slack_token
from slack_sdk.web import WebClient
import logging
import time
import uuid
from Notifications.services import notification_service
from integrations.models import RepoChange, Company

logger = logging.getLogger(__name__)


class SimpleUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "first_name", "last_name", "role", "company"]

class IntegrationToolSerializer(serializers.ModelSerializer):
    class Meta:
        model = IntegrationTool
        fields = ["id", "name", "slug", "category", "description"]

class CompanyIntegrationSerializer(serializers.ModelSerializer):
    tool = IntegrationToolSerializer()
    config = serializers.JSONField(required=False)
    class Meta:
        model = CompanyIntegration
        fields = ["id", "tool", "status", "is_active", "connected_at", "created_at", "config"]

class CompanySerializer(serializers.ModelSerializer):
    pending_invitations = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = ["id", "name", "slug", "email", "created_at", "pending_invitations"]
    
    def get_pending_invitations(self, obj):
        """Get list of pending team invitations with user names"""
        from integrations.models import TeamInvitation
        pending_invitations = TeamInvitation.objects.filter(
            company=obj,
            status=TeamInvitation.Status.PENDING
        ).exclude(
            expires_at__lt=timezone.now()  # Exclude expired invitations
        ).order_by('-created_at')
        
        invitation_data = []
        for invitation in pending_invitations:
            invitation_data.append({
                "id": str(invitation.id),
                "email": invitation.email,
                "role": invitation.get_role_display(),
                "inviterName": f"{invitation.invited_by.first_name} {invitation.invited_by.last_name}" if invitation.invited_by else "Unknown",
                "createdAt": invitation.created_at.isoformat(),
                "expiresAt": invitation.expires_at.isoformat(),
                "isExpired": invitation.is_expired()
            })
        
        return invitation_data

class MonitoredItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = MonitoredItem
        fields = ["id", "type", "name", "url", "repo", "created_at"]

def get_company(company_id):
    try:
        return Company.objects.get(id=company_id)
    except Company.DoesNotExist:
        return None



def get_user_from_request(request):
    """Get user from request using various methods"""
    # Try to get from auth_utils (JWT token)
    try:
        from integrations.auth_utils import get_user_from_token
        user = get_user_from_token(request)
        if user:
            return user
    except:
        pass
    
    # Try to get from request.user (Django auth)
    if hasattr(request, 'user') and request.user.is_authenticated:
        return request.user
    
    # Try to get from query params (fallback)
    acting_user_email = request.GET.get("acting_user_email")
    if acting_user_email:
        return User.objects.filter(email=acting_user_email).first()
    
    return None

@api_view(["POST"])
@permission_classes([AllowAny])
def process_external_commit(request):
    """
    Receives notification of new commit from feed poller, fetches diff, runs LLM, sends websocket notification.
    """
    logger.info("[process_external_commit] Incoming request data: %s", request.data)
    repo = request.data.get("repo")
    commit_id = request.data.get("commit_id")
    previous_commit_id = request.data.get("previous_commit_id")
    commit_url = request.data.get("commit_url")
    github_token = request.data.get("github_token")
    logger.info(f"[process_external_commit] repo={repo}, commit_id={commit_id}, previous_commit_id={previous_commit_id}, commit_url={commit_url}, github_token={'set' if github_token else 'unset'}")
    if not repo or not commit_id:
        logger.error("[process_external_commit] Missing repo or commit_id")
        return Response({"error": "repo and commit_id required"}, status=400)

    # Fetch commit diff from GitHub API
    github_api_url = f"https://api.github.com/repos/{repo}/commits/{commit_id}"
    headers = {}
    if github_token:
        headers["Authorization"] = f"token {github_token}"
    else:
        env_token = os.environ.get("GITHUB_TOKEN")
        if env_token:
            headers["Authorization"] = f"token {env_token}"
    logger.info(f"[process_external_commit] Fetching commit from GitHub API: {github_api_url} with headers: {headers}")
    try:
        r = requests.get(github_api_url, headers=headers)
    except Exception as e:
        logger.error(f"[process_external_commit] Exception during requests.get: {e}")
        return Response({"error": "Exception during requests.get", "details": str(e)}, status=500)
    logger.info(f"[process_external_commit] GitHub API response status: {r.status_code}")
    if r.status_code != 200:
        logger.error(f"[process_external_commit] Failed to fetch commit: {r.text}")
        return Response({"error": "Failed to fetch commit from GitHub API", "details": r.text}, status=500)
    try:
        commit_data = r.json()
    except Exception as e:
        logger.error(f"[process_external_commit] Exception parsing JSON: {e}")
        return Response({"error": "Exception parsing JSON", "details": str(e)}, status=500)
    logger.info(f"[process_external_commit] commit_data keys: {list(commit_data.keys())}")
    # Get parent commit hash
    parent_sha = commit_data["parents"][0]["sha"] if commit_data.get("parents") else None
    logger.info(f"[process_external_commit] parent_sha: {parent_sha}")
    # Get diff
    diff_url = f"https://api.github.com/repos/{repo}/compare/{parent_sha}...{commit_id}" if parent_sha else None
    diff = None
    if diff_url:
        logger.info(f"[process_external_commit] Fetching diff from GitHub API: {diff_url}")
        try:
            r2 = requests.get(diff_url, headers=headers)
        except Exception as e:
            logger.error(f"[process_external_commit] Exception during diff requests.get: {e}")
            return Response({"error": "Exception during diff requests.get", "details": str(e)}, status=500)
        logger.info(f"[process_external_commit] Diff API response status: {r2.status_code}")
        if r2.status_code == 200:
            try:
                diff = r2.json().get("files", [])
            except Exception as e:
                logger.error(f"[process_external_commit] Exception parsing diff JSON: {e}")
                return Response({"error": "Exception parsing diff JSON", "details": str(e)}, status=500)
        else:
            logger.error(f"[process_external_commit] Failed to fetch diff: {r2.text}")
    else:
        logger.warning(f"[process_external_commit] No diff_url constructed (parent_sha missing)")

    # --- Begin diff/LLM logic for cross-repo monitor ---
    code_diffs = []
    llm_suggested_docs = None
    engineer_changes_structured = []
    for f in diff or []:
        patch = f.get("patch")
        if patch:
            code_diffs.append({"file": f.get("filename"), "patch": patch})
    logger.info(f"[process_external_commit] Number of code diffs fetched: {len(code_diffs)}")
    # LLM summarization
    if code_diffs:
        try:
            code_context = "\n\n".join([f"[File: {c['file']} diff]\n{c['patch']}" for c in code_diffs])
            logger.info(f"[process_external_commit] LLM input context (truncated): {code_context[:500]}{'...' if len(code_context) > 500 else ''}")
            question = (
                "You are an expert software engineer and technical documentation assistant. "
                "Analyze the following code changes (diffs) and extract ONLY the changes that are truly relevant to software engineers—such as API endpoint changes, function signature changes, request/response structure, authentication, breaking changes, deprecations, or anything that would require an engineer to update their integration or usage. "
                "IGNORE changes that are not relevant to engineers, such as formatting, comments, whitespace, internal variable renames, or non-functional refactors. "
                "For each relevant change, provide a clear, concise bullet point with the following structure:\n"
                "- What changed (summarize in one sentence, e.g., 'getClients endpoint changed') with a quick why if can be derived from introduced change.\n"
                "- Previous state (show code snippet or describe old behavior)\n"
                "- New state (show code snippet or describe new behavior)\n"
                "- File and location (if possible)\n"
                "- Severity of change (use standard software engineering vocabulary: 'breaking', 'major', 'minor', 'deprecation', 'removal', 'addition', 'modification', etc)\n"
                "- Any other relevant detail a software engineer would want to know (e.g., breaking change, new parameter, deprecation, etc.)\n"
                "Format your answer as a JSON array of objects, each with keys: 'summary', 'before', 'after', 'file', 'severity', 'details'. Always include the 'severity' key for each change, using the most appropriate professional term for the impact.\n"
                "If there are NO actionable or relevant changes for engineers, return an empty array [].\n"
                "\nNow, analyze the following code changes and provide a clear, actionable summary for engineers. Only include changes that are truly relevant and actionable.\n"
            )
            # Import the LLM service from the cross-repo monitor logic
            from integrations.github.llm.services import github_llm_service
            llm_suggested_docs = github_llm_service.generate_answer_sync(question, code_context)
            logger.info(f"[process_external_commit] LLM output (truncated): {str(llm_suggested_docs)[:500]}{'...' if len(str(llm_suggested_docs)) > 500 else ''}")
            import json as _json, re
            try:
                cleaned = llm_suggested_docs.strip()
                if cleaned.startswith('```'):
                    cleaned = re.sub(r'^```[a-zA-Z]*\s*', '', cleaned)
                    cleaned = re.sub(r'```$', '', cleaned)
                cleaned = cleaned.strip()
                engineer_changes_structured = _json.loads(cleaned)
                for change in engineer_changes_structured:
                    if 'severity' not in change:
                        if 'level' in change:
                            change['severity'] = change['level']
                        elif 'type' in change:
                            change['severity'] = change['type']
                        else:
                            change['severity'] = 'minor'
                if not engineer_changes_structured:
                    logger.info("[process_external_commit] No actionable engineer changes detected by LLM.")
            except Exception as e:
                logger.info(f"[process_external_commit] Error parsing LLM result as JSON: {e}\nRaw LLM output: {llm_suggested_docs}")
                engineer_changes_structured = []
        except Exception as e:
            logger.info(f"[process_external_commit] LLM error: {str(e)}")
            engineer_changes_structured = []
    # --- End diff/LLM logic for cross-repo monitor ---
    # Check if engineer changes are used in internal repo before sending notification
    # Get internal_repo from request (must be sent by polling service)
    internal_repo = request.data.get("internal_repo")
    used_changes = []
    if internal_repo:
        # Get token for internal repo (use github_token from request)
        access_token = github_token
        # Use GitHubAPIService for code search
        try:
            from integrations.github.api.services import GitHubAPIService
            api_service = GitHubAPIService(access_token)
            from asgiref.sync import async_to_sync
            for change in engineer_changes_structured:
                summary = change.get("summary", "")
                change_keywords = set()
                try:
                    from integrations.github.llm.services import github_llm_service
                    if github_llm_service:
                        extracted = github_llm_service.extract_cross_repo_keywords_llm(summary)
                        for kw in extracted.split("OR"):
                            kw = kw.strip()
                            if kw:
                                change_keywords.add(kw)
                except Exception:
                    for word in summary.split():
                        change_keywords.add(word)
                found = False
                search_code_with_keywords_async = async_to_sync(api_service.search_code_with_keywords)
                code_has_keywords = search_code_with_keywords_async(internal_repo, list(change_keywords))
                if code_has_keywords:
                    found = True
                if found:
                    used_changes.append(change)
        except Exception as e:
            logger.error(f"[process_external_commit] Error during code search for internal repo: {e}")
    else:
        logger.warning("[process_external_commit] No internal_repo provided in request, skipping code search.")

    saved_changes = []
    if used_changes and internal_repo:
        company = None
        # Try to get company from internal_repo's monitored item
        monitored = CrossRepoMonitor.objects.filter(internal_repo=internal_repo).first()
        if monitored:
            company = monitored.company
        if not company:
            company = Company.objects.first()  # fallback, or handle as needed
        for change in used_changes:
            repo_change = RepoChange.objects.create(
                company=company,
                internal_repo=internal_repo,
                external_repo=repo,
                cross_repo_monitor=monitored,
                commit_id=commit_id,
                summary=change.get("summary", ""),
                details={
                    "change": change,
                    "previous_commit_id": previous_commit_id,
                    "commit_url": commit_url,
                    "diff": diff,
                    "code_diffs": code_diffs,
                    "llm_suggested_docs": llm_suggested_docs,
                },
                status="pending",
            )
            saved_changes.append({
                "id": str(repo_change.id),
                "internal_repo": repo_change.internal_repo,
                "external_repo": repo_change.external_repo,
                "commit_id": repo_change.commit_id,
                "previous_commit_id": previous_commit_id,
                "commit_url": commit_url,
                "diff": diff,
                "summary": repo_change.summary,
                "details": repo_change.details,
                "code_diffs": code_diffs,
                "llm_suggested_docs": llm_suggested_docs,
                "status": repo_change.status,
                "created_at": repo_change.created_at,
                "updated_at": repo_change.updated_at,
            })
    return Response({
        "status": "ok",
        "engineer_changes": used_changes,
        "code_diffs": code_diffs,
        "llm_suggested_docs": llm_suggested_docs,
        "saved_changes": saved_changes,
    })



@api_view(["GET", "POST", "DELETE"])
@permission_classes([AllowAny])
def company_users(request):
    """
    List all users in a company or delete a user
    GET /api/integrations/company-users/?company_id=<uuid>
    DELETE /api/integrations/company-users/ (with {"email": "<EMAIL>"} in body)
    
    Note: To invite new team members, use POST /api/integrations/invite-team-member/
    """
    if request.method == "DELETE":
        # Delete user
        user_email = request.data.get("email")
        
        if not user_email:
            return Response({"error": "email required"}, status=400)
            
        # Get acting user from request
        acting_user = get_user_from_request(request)
        if not acting_user:
            return Response({"error": "Authentication required"}, status=401)
        
        # Check if acting user is admin
        if acting_user.role != User.Role.ADMIN:
            return Response({"error": "Only admin users can delete team members"}, status=403)
        
        # Find the target user
        try:
            target_user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            return Response({"error": f"User with email {user_email} not found"}, status=404)
            
        # Check if acting user belongs to the same company as target user
        if acting_user.company != target_user.company:
            return Response({"error": "Forbidden: Not in the same company."}, status=403)
        
    
        # Prevent self-deletion
        if target_user.id == acting_user.id:
            return Response({"error": "Cannot delete yourself"}, status=403)
        
        try:
            # First, delete any pending team invitations for this user's email
            from integrations.models import TeamInvitation
            invitations_deleted = TeamInvitation.objects.filter(
                email__iexact=target_user.email
            ).delete()
            logger.info(f"   Deleted {invitations_deleted[0]} pending team invitations for {target_user.email}")
            
            
            # Store user info for response before deletion
            deleted_user_info = {
                "id": str(target_user.id),
                "email": target_user.email,
                "name": f"{target_user.first_name} {target_user.last_name}",
                "role": target_user.role
            }
            
            # Then delete the user from Django
            target_user.delete()
            logger.info(f"✅ Successfully deleted user: {target_user.email}")
            
            return Response({
                "success": True,
                "message": f"User {deleted_user_info['email']} deleted successfully",
                "data": {
                    "deleted_user": deleted_user_info,
                    "invitations_deleted": invitations_deleted[0] if invitations_deleted else 0
                }
            }, status=200)
            
        except Exception as e:
            logger.error(f"❌ Failed to delete user {target_user.email}: {e}")
            return Response({"error": f"Failed to delete user: {str(e)}"}, status=500)
    
    # GET method - list users
    company_id = request.GET.get("company_id")
    if not company_id:
        return Response({"error": "company_id required"}, status=400)
    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)
    
    # Get users and pending invitations
    users = User.objects.filter(company=company)
    company_serializer = CompanySerializer(company)
    
    return Response({
        "users": SimpleUserSerializer(users, many=True).data,
        "pending_invitations": company_serializer.data["pending_invitations"]
    })

@api_view(["GET"])
@permission_classes([AllowAny])
def get_user_by_email(request):
    email = request.GET.get("email")
    logger.debug(f"get_user_by_email: {email}")
    if not email:
        return Response({"error": "Email required"}, status=400)
    user = User.objects.filter(email=email).first()
    logger.debug(f"user: {user}")
    if not user:
        return Response({"error": "User not found"}, status=404)
    data = {
        "id": str(user.id),
        "email": user.email,
        "company": str(user.company.id) if user.company else None,
        "company_name": user.company.name if user.company else None,
        "role": user.role,
    }
    return Response(data)

@api_view(["DELETE", "PATCH"])
@permission_classes([AllowAny])
def company_user_detail(request, user_id):
    # Get acting user from request
    if request.method == "DELETE":
        acting_user_email = request.GET.get("acting_user_email")
    else:  # PATCH
        acting_user_email = request.data.get("acting_user_email")
    if not acting_user_email:
        return Response({"error": "Acting user email required"}, status=400)
    acting_user = User.objects.filter(email=acting_user_email).first()
    if not acting_user:
        return Response({"error": "Acting user not found"}, status=404)

    try:
        target_user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=404)

    # Only allow admins to perform these actions
    if acting_user.role != "ADMIN":
        return Response({"error": "Forbidden: Only admins can perform this action."}, status=403)
    # Cannot act on self
    if str(acting_user.id) == str(target_user.id):
        return Response({"error": "You cannot perform this action on yourself."}, status=400)
    # Must be in the same company
    if acting_user.company != target_user.company:
        return Response({"error": "Forbidden: Not in the same company."}, status=403)

    if request.method == "DELETE":
        if target_user.role == "ADMIN":
            return Response({"error": "Cannot delete another admin."}, status=403)
        

        try:
            # First, delete any pending team invitations for this user's email
            from integrations.models import TeamInvitation
            invitations_deleted = TeamInvitation.objects.filter(
                email__iexact=target_user.email
            ).delete()
            logger.info(f"   Deleted {invitations_deleted[0]} pending team invitations for {target_user.email}")
            
            # Then delete the user from Django
            target_user.delete()
            logger.info(f"✅ Successfully deleted user: {target_user.email}")
            return Response({"success": "User deleted."}, status=204)
        except Exception as e:
            logger.error(f"❌ Failed to delete user {target_user.email}: {e}")
            return Response({"error": f"Failed to delete user: {str(e)}"}, status=500)

    if request.method == "PATCH":
        new_role = request.data.get("role")
        if new_role not in ["ADMIN", "USER"]:
            return Response({"error": "Invalid role."}, status=400)
        if target_user.role == new_role:
            return Response({"error": "User already has this role."}, status=400)
        target_user.role = new_role
        target_user.save()
        return Response({"success": "User role updated.", "role": new_role}, status=200)

@api_view(["POST", "DELETE"])
@permission_classes([AllowAny])
def company_integrations(request):
    company_id = request.data.get("company_id")
    if not company_id:
        return Response({"error": "company_id required"}, status=400)
    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)
    
    if request.method == "POST":
        # Only admins can add integrations
        acting_user_email = request.data.get("acting_user_email")
        if not acting_user_email:
            return Response({"error": "Acting user email required"}, status=400)
        acting_user = User.objects.filter(email=acting_user_email).first()
        if not acting_user or acting_user.role != "ADMIN" or acting_user.company != company:
            return Response({"error": "Forbidden: Only admins in this company can add integrations."}, status=403)
        tool_slug = request.data.get("tool_slug")
        if not tool_slug:
            return Response({"error": "tool_slug required"}, status=400)
        tool = IntegrationTool.objects.filter(slug=tool_slug).first()
        if not tool:
            return Response({"error": "Integration tool not found."}, status=404)
        # Prevent duplicate
        if CompanyIntegration.objects.filter(company=company, tool=tool, is_active=True).exists():
            return Response({"error": "Integration already connected for this company."}, status=409)
        config = request.data.get("config", {})
        # If GitHub, try to get repositories from cache first, then fallback to API

        if tool.slug == "github" and "installation_id" in config:
            installation_id = config["installation_id"]
            
            # Use the utility function that tries cache first, then API
            from integrations.github.api.services import get_cached_repositories
            try:
                repos = get_cached_repositories(installation_id)
                logger.debug(f"repos: {repos}")
                config["repositories"] = [
                    {"id": r["id"], "full_name": r["full_name"]} for r in repos
                ]
            except ValueError as e:
                # This is a configuration error - the integration should not be created
                logger.debug(f"[ERROR] GitHub App not properly configured: {e}")
                return Response({"error": "GitHub App is not properly configured. Please check the private key configuration."}, status=500)
            except Exception as e:
                logger.debug(f"[WARN] Could not fetch GitHub installation repos: {e}")
                # Continue without repositories - they can be added later
                config["repositories"] = []

        logger.debug(f"config: {config}")

        integration, created = CompanyIntegration.objects.get_or_create(
            company=company,
            tool=tool,
            status="CONNECTED",
            is_active=True,
            config=config
        )
        return Response(CompanyIntegrationSerializer(integration).data, status=201)
    elif request.method == "DELETE":
        # Only admins can delete integrations
        acting_user_email = request.data.get("acting_user_email")
        if not acting_user_email:
            return Response({"error": "Acting user email required"}, status=400)
        acting_user = User.objects.filter(email=acting_user_email).first()
        if not acting_user or acting_user.role != "ADMIN" or acting_user.company != company:
            return Response({"error": "Forbidden: Only admins in this company can delete integrations."}, status=403)
        tool_slug = request.data.get("tool_slug")
        if not tool_slug:
            return Response({"error": "tool_slug required"}, status=400)
        tool = IntegrationTool.objects.filter(slug=tool_slug).first()
        if not tool:
            return Response({"error": "Integration tool not found."}, status=404)
        integration = CompanyIntegration.objects.filter(company=company, tool=tool, is_active=True).first()
        if not integration:
            return Response({"error": "Integration not found for this company."}, status=404)
        # If GitHub, revoke access using stored token
        if tool.slug == "github":
            github_token = integration.config.get("token") if integration.config else None
            if github_token:
                try:
                    success = revoke_github_authorization(github_token)
                    if not success:
                        logger.debug(f"[WARN] Failed to revoke GitHub token via utility.")
                except Exception as e:
                    logger.debug(f"[ERROR] Exception revoking GitHub token: {e}")
            
            # Delete cross repo monitors for this company when GitHub integration is removed
            from integrations.models import CrossRepoMonitor
            cross_repo_monitors_deleted = CrossRepoMonitor.objects.filter(company=company).delete()
            logger.info(f"[Integration Deletion] Deleted {cross_repo_monitors_deleted[0]} cross repo monitors for company {company.id}")
        
        integration.delete()
        # If Slack, revoke tokens using stored tokens
        if tool.slug == "slack":
            slack_bot_token = integration.config.get("slack_bot_token") if integration.config else None
            slack_user_token = integration.config.get("slack_user_token") if integration.config else None
            try:
                if slack_bot_token:
                    success = revoke_slack_token(slack_bot_token)
                    if not success:
                        logger.debug("[WARN] Failed to revoke Slack bot token")
                if slack_user_token:
                    success = revoke_slack_token(slack_user_token)
                    if not success:
                        logger.debug("[WARN] Failed to revoke Slack user token")
            except Exception as e:
                logger.debug(f"[ERROR] Exception revoking Slack token: {e}")

        # If Google Drive, cleanup workspace and related data
        if tool.slug == "google-drive":
            try:
                from integrations.google_drive.models import GoogleDriveWorkspace, GoogleDriveFilePreference
                from integrations.google_drive.services import revoke_google_drive_authorization
                
                workspace_id = integration.config.get("workspace_id") if integration.config else None
                if workspace_id:
                    # Get workspace and revoke authorization
                    workspace = GoogleDriveWorkspace.objects.filter(id=workspace_id).first()
                    if workspace:
                        # Revoke Google Drive authorization
                        revoke_google_drive_authorization(workspace)
                        
                        # Delete related file preferences and logs
                        GoogleDriveFilePreference.objects.filter(workspace=workspace).delete()
                        
                        # Delete workspace
                        workspace.delete()
                        
                        logger.info(f"Cleaned up Google Drive workspace: {workspace_id}")
            except Exception as e:
                logger.debug(f"[ERROR] Exception cleaning up Google Drive: {e}")

        # If Confluence, cleanup AtlassianUserProfile and related data
        if tool.slug == "confluence":
            try:
                from integrations.models import AtlassianUserProfile
                from integrations.confluence.services.django_confluence_service import cleanup_confluence_documents_for_company
                
                # Delete AtlassianUserProfile for users in this company
                atlassian_profiles_deleted = AtlassianUserProfile.objects.filter(
                    user__company=company
                ).delete()
                logger.info(f"[Integration Deletion] Deleted {atlassian_profiles_deleted[0]} AtlassianUserProfile records for company {company.id}")
                
                # Clean up Confluence documents for this company
                cleanup_result = cleanup_confluence_documents_for_company(str(company.id))
                logger.info(f"[Integration Deletion] Cleaned up Confluence documents for company {company.id}: {cleanup_result}")
                
            except Exception as e:
                logger.debug(f"[ERROR] Exception cleaning up Confluence: {e}")

        return Response({"success": "Integration deleted."}, status=200)

@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def company_monitored_items(request):
    company_id = request.GET.get("company_id") or request.data.get("company_id")
    if not company_id:
        return Response({"error": "company_id required"}, status=400)
    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)
    if request.method == "GET":
        items = MonitoredItem.objects.filter(company=company)
        return Response(MonitoredItemSerializer(items, many=True).data)
    elif request.method == "POST":
        logger.debug(f"[DEBUG] Incoming POST to company_monitored_items: {request.data}")
        # Only admins can add monitored items
        acting_user_email = request.data.get("acting_user_email")
        logger.debug(f"[DEBUG] acting_user_email: {acting_user_email}")
        if not acting_user_email:
            logger.debug("[DEBUG] Missing acting_user_email")
            return Response({"error": "Acting user email required"}, status=400)
        acting_user = User.objects.filter(email=acting_user_email).first()
        if not acting_user or acting_user.role != "ADMIN" or acting_user.company != company:
            logger.debug(f"[DEBUG] Forbidden: user={acting_user}, role={getattr(acting_user, 'role', None)}, company={getattr(acting_user, 'company', None)}")
            return Response({"error": "Forbidden: Only admins in this company can add monitored items."}, status=403)
        type_ = request.data.get("type")
        name = request.data.get("name")
        url = request.data.get("url")
        repo = request.data.get("repo")
        logger.debug(f"[DEBUG] type: {type_}, name: {name}, url: {url}, repo: {repo}")
        if not type_ or not name:
            logger.debug("[DEBUG] Missing type or name")
            return Response({"error": "type and name required"}, status=400)
        # Prevent duplicate by name/type
        if MonitoredItem.objects.filter(company=company, type=type_, name=name).exists():
            logger.debug("[DEBUG] Duplicate monitored item")
            return Response({"error": "This item is already being monitored for this company."}, status=409)
        item = MonitoredItem.objects.create(company=company, type=type_, name=name, url=url, repo=repo)
        logger.debug(f"[DEBUG] Created MonitoredItem: {item}")
        # If GitHub repo, do NOT create webhook (handled by app-webhook)
        return Response(MonitoredItemSerializer(item).data, status=201)

@api_view(["DELETE"])
@permission_classes([AllowAny])
def monitored_item_detail(request, item_id):
    company_id = request.GET.get("company_id")
    acting_user_email = request.GET.get("acting_user_email")
    if not company_id or not acting_user_email:
        return Response({"error": "company_id and acting_user_email required"}, status=400)
    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)
    acting_user = User.objects.filter(email=acting_user_email).first()
    if not acting_user or acting_user.role != "ADMIN" or acting_user.company != company:
        return Response({"error": "Forbidden: Only admins in this company can delete monitored items."}, status=403)
    item = get_object_or_404(MonitoredItem, id=item_id, company=company)
    # If GitHub repo, remove webhook
    if item.type == "github_repo":
        integration = CompanyIntegration.objects.filter(company=company, tool__slug="github", is_active=True).first()
        if integration and integration.config and integration.config.get("token") and item.repo:
            github_token = integration.config["token"]
            repo_for_webhook = item.repo.replace("_", "/", 1)
            service = GitHubWebhookService(github_token)
            try:
                owner, repo_name = repo_for_webhook.split("/")
                # Try to find and delete the webhook by URL
                webhook_url = f"{settings.BACKEND_PUBLIC_URL}/api/integrations/github/api/webhook/receive/"
                webhooks = asyncio.run(service.list_webhooks(owner, repo_name))
                deleted = False
                for wh in webhooks:
                    if wh.get("config", {}).get("url") == webhook_url:
                        hook_id = wh.get("id")
                        if hook_id:
                            asyncio.run(service.delete_webhook(owner, repo_name, hook_id))
                            deleted = True
                if not deleted:
                    logger.debug(f"[WARN] No matching webhook found for deletion for repo {repo_for_webhook}")
            except Exception as e:
                logger.debug(f"[ERROR] Failed to delete webhook: {e}")
                return Response({"error": f"Failed to delete webhook: {e}"}, status=400)
    item.delete()
    return Response(status=204)

@api_view(["GET"])
@permission_classes([AllowAny])
def possible_connections(request):
    """Get all possible integrations and their connection status for a company"""
    # logger.debug("possible_connections")
    company_id = request.GET.get("company_id")
    
    # Fixed list of possible connections
    possible_integrations = [
        {"id": "github", "name": "GitHub"},
        {"id": "confluence", "name": "Confluence"},
        {"id": "slack", "name": "Slack"},
        {"id": "microsoft-teams", "name": "Microsoft Teams"},
        {"id": "gitlab", "name": "GitLab"},
        {"id": "notion", "name": "Notion"},
        {"id": "google-drive", "name": "Google Drive"},  # <-- This line adds Google Drive
        {"id": "email", "name": "Email"},
        {"id": "jira", "name": "Jira"},
        {"id": "discord", "name": "Discord"},
    ]
    
    # If company_id is provided, get existing integrations for that company
    company_integrations = {}
    if company_id:
        company = get_company(company_id)
        if company:
            integrations = CompanyIntegration.objects.filter(
                company=company, 
                is_active=True
            ).select_related('tool')
            
            for integration in integrations:
                company_integrations[integration.tool.slug] = integration.status
    
    # Build response
    response_data = []
    for integration in possible_integrations:
        # Determine status based on company integration
        if integration["id"] in company_integrations:
            if company_integrations[integration["id"]] == "CONNECTED":
                status = "connected"
            else:
                status = "available"  # Disconnected or error status
        else:
            # Check if this integration type is available in the system
            tool_exists = IntegrationTool.objects.filter(slug=integration["id"], is_active=True).exists()
            if tool_exists:
                status = "available"
            else:
                status = "will be available soon"
        
        response_data.append({
            "id": integration["id"],
            "name": integration["name"],
            "status": status
        })
    
    return Response(response_data)


@api_view(["GET"])
@permission_classes([AllowAny])
def github_company_repos(request):
    """Return GitHub repositories for the company's GitHub integration from DB/cache.

    If repositories are missing in config, attempt to refresh via cached service.
    """
    company_id = request.GET.get("company_id")
    if not company_id:
        return Response({"error": "company_id required"}, status=400)

    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)

    tool = IntegrationTool.objects.filter(slug="github").first() or IntegrationTool.objects.filter(name__iexact="github").first()
    if not tool:
        return Response({"error": "Please connect to GitHub first!"}, status=404)

    integration = CompanyIntegration.objects.filter(company=company, tool=tool, is_active=True).first()
    if not integration:
        return Response([], status=200)

    cfg = integration.config or {}
    repos = cfg.get("repositories") or []

    # If repos missing but installation_id present, try to refresh from cached service
    if (not repos) and cfg.get("installation_id"):
        try:
            repos = get_cached_repositories(cfg["installation_id"]) or []
            # Persist back to config for faster future reads
            cfg["repositories"] = [
                {"id": r.get("id"), "full_name": r.get("full_name"), "name": (r.get("full_name") or "").split("/")[-1]}
                for r in repos if isinstance(r, dict)
            ]
            integration.config = cfg
            integration.save(update_fields=["config"]) 
        except Exception as e:
            logger.debug(f"[github_company_repos] Could not refresh repos: {e}")
            logger.error(f"❌ Error fetching GitHub repositories for company {company.name}")

    # Normalize output
    normalized = []
    for r in (cfg.get("repositories") or repos or []):
        try:
            rid = r.get("id")
            full_name = r.get("full_name") or r.get("name")
            name = r.get("name") or (full_name.split("/")[-1] if full_name else None)
            if full_name:
                normalized.append({"id": rid, "full_name": full_name, "name": name})
        except Exception:
            continue

    return Response(normalized, status=200)

@api_view(["GET", "PUT"])
@permission_classes([AllowAny])
def notification_settings(request):
    """Get or update notification settings for a company"""
    company_id = request.GET.get("company_id")
    acting_user_email = request.GET.get("acting_user_email")
    
    if not company_id or not acting_user_email:
        return Response({"error": "company_id and acting_user_email required"}, status=400)
    
    company = get_company(company_id)
    if not company:
        return Response({"error": "Company not found"}, status=404)
    
    # Check if user is admin of this company
    acting_user = User.objects.filter(email=acting_user_email).first()
    if not acting_user or acting_user.role != "ADMIN" or acting_user.company != company:
        return Response({"error": "Forbidden: Only admins can manage notification settings."}, status=403)
    
    # Get or create notification settings
    from integrations.models import NotificationSettings
    settings, created = NotificationSettings.objects.get_or_create(
        company=company,
        defaults={
            'frequency_hours': 1,
            'enabled': True
        }
    )
    
    if request.method == "GET":
        # Return current settings
        return Response({
            "company_id": str(company.id),
            "company_name": company.name,
            "frequency_hours": settings.frequency_hours,
            "enabled": settings.enabled,
            "last_notification_time": settings.last_notification_time.isoformat() if settings.last_notification_time else None,
            "updated_at": settings.updated_at.isoformat()
        })
    
    elif request.method == "PUT":
        # Update settings
        data = request.data
        
        # Validate frequency_hours
        if 'frequency_hours' in data:
            try:
                frequency_hours = int(data['frequency_hours'])
                if frequency_hours < 0:
                    return Response({"error": "frequency_hours must be 0 or positive"}, status=400)
                settings.frequency_hours = frequency_hours
            except (ValueError, TypeError):
                return Response({"error": "frequency_hours must be a valid integer"}, status=400)
        
        # Update boolean fields
        if 'enabled' in data:
            settings.enabled = bool(data['enabled'])
        
        settings.save()
        
        logger.info(f"✅ Notification settings updated for company {company.name} by {acting_user_email}")
        
        return Response({
            "message": "Notification settings updated successfully",
            "company_id": str(company.id),
            "company_name": company.name,
            "frequency_hours": settings.frequency_hours,
            "enabled": settings.enabled,
            "last_notification_time": settings.last_notification_time.isoformat() if settings.last_notification_time else None,
            "updated_at": settings.updated_at.isoformat()
        })


@api_view(["GET", "PUT"])
@permission_classes([AllowAny])
def user_profile(request):
    """Get or update user profile information"""
    acting_user_email = request.GET.get("acting_user_email")
    
    if not acting_user_email:
        return Response({"error": "acting_user_email required"}, status=400)
    
    user = User.objects.filter(email=acting_user_email).first()
    if not user:
        return Response({"error": "User not found"}, status=404)
    
    if request.method == "GET":
        # Return user profile data
        return Response({
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role,
            "company": {
                "id": str(user.company.id),
                "name": user.company.name
            } if user.company else None,
            "date_joined": user.date_joined.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "is_active": user.is_active,
            "is_staff": user.is_staff
        })
    
    elif request.method == "PUT":
        # Update user profile
        data = request.data
        
        # Update allowed fields
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        
        user.save()
        
        logger.info(f"✅ User profile updated for {user.email}")
        
        return Response({
            "message": "Profile updated successfully",
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role,
            "company": {
                "id": str(user.company.id),
                "name": user.company.name
            } if user.company else None,
            "date_joined": user.date_joined.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "is_active": user.is_active,
            "is_staff": user.is_staff
        })


@api_view(["GET", "PUT"])
@permission_classes([AllowAny])
def active_search_platforms(request):
    """Get or update user's active search platforms"""
    acting_user_email = request.GET.get("acting_user_email")
    
    if not acting_user_email:
        return Response({"error": "acting_user_email required"}, status=400)
    
    user = User.objects.filter(email=acting_user_email).first()
    if not user:
        return Response({"error": "User not found"}, status=404)
    
    # Get or create the ActiveSearchPlatforms record for the user
    from .models import ActiveSearchPlatforms
    platforms_record, created = ActiveSearchPlatforms.objects.get_or_create(user=user)
    
    if request.method == "GET":
        # Return current active platforms
        return Response({
            "user_email": user.email,
            "platforms": platforms_record.platforms
        })
    
    elif request.method == "PUT":
        # Update the platforms list
        data = request.data
        
        if 'platforms' not in data:
            return Response({"error": "platforms field required"}, status=400)
        
        # Validate platforms is a list of strings
        if not isinstance(data['platforms'], list):
            return Response({"error": "platforms must be a list"}, status=400)
        
        # Validate each platform is a string
        for platform in data['platforms']:
            if not isinstance(platform, str):
                return Response({"error": "Each platform must be a string"}, status=400)
            if len(platform) > 100:  # Match the model's max_length
                return Response({"error": f"Platform name too long: {platform}"}, status=400)
        
        # Update the platforms list
        platforms_record.platforms = data['platforms']
        platforms_record.save()
        
        logger.info(f"✅ Active search platforms updated for {user.email}: {data['platforms']}")
        
        return Response({
            "message": "Active search platforms updated successfully",
            "user_email": user.email,
            "platforms": platforms_record.platforms
        })


@api_view(["POST"])
@permission_classes([AllowAny])
def slack_user_connection_notification(request):
    """
    Endpoint to receive user connection notifications and forward them to Slack webhook.
    Expected payload: {
        "userEmail": "<EMAIL>",
        "userName": "wissem golli", 
        "connectionType": "login",
        "timestamp": "2025-08-26T14:22:03.925Z"
    }
    """
    try:
        # Get the data from request
        data = request.data
        
        # Validate required fields
        required_fields = ['userEmail', 'userName', 'connectionType', 'timestamp']
        for field in required_fields:
            if field not in data:
                return Response({"error": f"Missing required field: {field}"}, status=400)
        
        user_email = data['userEmail']
        user_name = data['userName']
        connection_type = data['connectionType']
        timestamp = data['timestamp']
        
        logger.info(f"📧 [Slack Notification] Received user connection notification: {user_email} - {connection_type}")
        
        # Check if we're in deployment environment (not development)
        from django.conf import settings
        if hasattr(settings, 'DJANGO_ENV') and settings.DJANGO_ENV != 'deployment':
            logger.info(f"🔄 [Slack Notification] Skipping Slack notification in development environment for {user_email}")
            return Response({
                "message": "Notification received but skipped in development environment",
                "user_email": user_email,
                "connection_type": connection_type,
                "environment": "development",
                "note": "Slack notifications are only sent in deployment environments"
            })
        
        # Get Slack webhook URL from environment [[memory:7080550]]
        slack_webhook_url = os.getenv('SLACK_WEBHOOK_URL_NOTIFS')
        if not slack_webhook_url:
            logger.error("❌ [Slack Notification] SLACK_WEBHOOK_URL not configured")
            return Response({"error": "Slack webhook URL not configured"}, status=500)
        
        # Format the message for Slack
        slack_message = {
            "text": f"🔔 User Connection Alert\n"
                   f"👤 *User:* {user_name} ({user_email})\n"
                   f"🔗 *Action:* {connection_type.title()}\n"
                   f"🕐 *Time:* {timestamp}"
        }
        
        # Send to Slack webhook
        response = requests.post(
            slack_webhook_url,
            json=slack_message,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            logger.info(f"✅ [Slack Notification] Successfully sent notification for {user_email}")
            return Response({
                "message": "Notification sent successfully to Slack",
                "user_email": user_email,
                "connection_type": connection_type
            })
        else:
            logger.error(f"❌ [Slack Notification] Failed to send to Slack: {response.status_code} - {response.text}")
            return Response({
                "error": "Failed to send notification to Slack",
                "slack_status": response.status_code
            }, status=500)
            
    except Exception as e:
        logger.exception(f"❌ [Slack Notification] Unexpected error: {e}")
        return Response({
            "error": "Internal server error",
            "details": str(e)
        }, status=500)