"""
Custom Django Admin views for one-click company + admin creation
"""
from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.urls import path
from django import forms
from integrations.models import Company, User
from integrations.services.supabase_centric_service import DjangoAdminWorkflowService
import logging

logger = logging.getLogger(__name__)


class CreateCompanyAdminForm(forms.Form):
    """Form for creating company + admin in one step"""
    
    # Company fields
    company_name = forms.CharField(
        max_length=255,
        label="Company Name",
        help_text="Enter the company name (e.g., 'Acme Corporation')",
        widget=forms.TextInput(attrs={'class': 'vTextField', 'size': '40'})
    )
    
    company_email = forms.EmailField(
        label="Company Email", 
        help_text="Company contact email (optional)",
        required=False,
        widget=forms.EmailInput(attrs={'class': 'vTextField', 'size': '40'})
    )
    
    # Admin user fields
    admin_first_name = forms.CharField(
        max_length=30,
        label="Admin First Name",
        widget=forms.TextInput(attrs={'class': 'vTextField', 'size': '30'})
    )
    
    admin_last_name = forms.CharField(
        max_length=30,
        label="Admin Last Name", 
        widget=forms.TextInput(attrs={'class': 'vTextField', 'size': '30'})
    )
    
    admin_email = forms.EmailField(
        label="Admin Email",
        help_text="Admin will receive Supabase login instructions at this email",
        widget=forms.EmailInput(attrs={'class': 'vTextField', 'size': '40'})
    )
    
    def clean_company_name(self):
        """Validate company name doesn't already exist"""
        company_name = self.cleaned_data['company_name']
        if Company.objects.filter(name=company_name.lower()).exists():
            raise forms.ValidationError(f"Company '{company_name}' already exists.")
        return company_name
    
    def clean_admin_email(self):
        """Validate admin email doesn't already exist"""
        admin_email = self.cleaned_data['admin_email']
        if User.objects.filter(email=admin_email).exists():
            raise forms.ValidationError(f"User with email '{admin_email}' already exists.")
        return admin_email


@staff_member_required
def create_company_admin_view(request):
    """Custom admin view for creating company + admin in one step"""
    
    if request.method == 'POST':
        form = CreateCompanyAdminForm(request.POST)
        
        if form.is_valid():
            try:
                # Use the workflow service to create everything
                workflow_service = DjangoAdminWorkflowService()
                
                company, admin_user, supabase_user = workflow_service.create_company_and_admin_for_django_admin(
                    company_name=form.cleaned_data['company_name'],
                    admin_email=form.cleaned_data['admin_email'],
                    admin_first_name=form.cleaned_data['admin_first_name'],
                    admin_last_name=form.cleaned_data['admin_last_name']
                )
                
                # Set company email if provided
                if form.cleaned_data['company_email']:
                    company.email = form.cleaned_data['company_email']
                    company.save()
                
                # Success message
                messages.success(
                    request,
                    f'✅ Successfully created company "{company.name.title()}" with admin user "{admin_user.first_name} {admin_user.last_name}"! '
                    f'Supabase connection email automatically sent to {admin_user.email}'
                )
                
                # Redirect to company list or admin user detail
                return redirect('/admin/integrations/company/')
                
            except Exception as e:
                logger.exception("Error in create_company_admin_view")
                messages.error(
                    request,
                    f'❌ Failed to create company and admin: {str(e)}'
                )
    else:
        form = CreateCompanyAdminForm()
    
    # Context for template
    context = {
        'form': form,
        'title': 'Create Company + Admin User',
        'site_title': 'SageBase Admin',
        'site_header': 'SageBase Administration',
        'has_permission': True,
        'opts': {'app_label': 'integrations', 'model_name': 'company'},
    }
    
    return render(request, 'admin/integrations/create_company_admin.html', context)


def get_admin_urls():
    """Return URLs for custom admin views"""
    return [
        path('create-company-admin/', create_company_admin_view, name='create_company_admin'),
    ]