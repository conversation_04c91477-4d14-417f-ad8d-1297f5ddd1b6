"""
Custom Django authentication endpoints (alternative to Supabase Auth)
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import authenticate
from integrations.models import User
import jwt
import datetime
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def django_login(request):
    """
    Custom Django login endpoint (alternative to Supabase Auth)
    POST /api/integrations/django-login/
    
    Body:
    {
        "email": "<EMAIL>",
        "password": "AdminPassword123!"
    }
    """
    try:
        data = request.data
        email = data.get('email')
        password = data.get('password')
        
        if not email or not password:
            return Response({
                "error": "Email and password are required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Find user by email
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({
                "error": "Invalid email or password"
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Check password
        if not user.check_password(password):
            return Response({
                "error": "Invalid email or password"
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Check if user is active
        if not user.is_active:
            return Response({
                "error": "Account is deactivated"
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Generate JWT token (similar to Supabase format)
        payload = {
            'user_id': str(user.id),
            'email': user.email,
            'role': user.role,
            'company_id': str(user.company.id) if user.company else None,
            'company_name': user.company.name if user.company else None,
            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24),
            'iat': datetime.datetime.utcnow()
        }
        
        # Use Django secret key for JWT signing
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
        
        return Response({
            "success": True,
            "message": "Login successful",
            "data": {
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "name": f"{user.first_name} {user.last_name}",
                    "role": user.role,
                    "company": {
                        "id": str(user.company.id) if user.company else None,
                        "name": user.company.name if user.company else None
                    }
                },
                "access_token": token,
                "token_type": "Bearer",
                "expires_in": 86400  # 24 hours
            }
        })
        
    except Exception as e:
        logger.exception("Error during Django login")
        return Response({
            "error": f"Login failed: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def django_verify_token(request):
    """
    Verify JWT token endpoint
    POST /api/integrations/django-verify-token/
    
    Headers:
    Authorization: Bearer <token>
    """
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return Response({
                "error": "Authorization header required"
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        token = auth_header.split(' ')[1]
        
        try:
            # Decode and verify token
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            
            # Get user from database
            user = User.objects.get(id=payload['user_id'])
            
            return Response({
                "success": True,
                "data": {
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "name": f"{user.first_name} {user.last_name}",
                        "role": user.role,
                        "company": {
                            "id": str(user.company.id) if user.company else None,
                            "name": user.company.name if user.company else None
                        }
                    },
                    "token_valid": True
                }
            })
            
        except jwt.ExpiredSignatureError:
            return Response({
                "error": "Token has expired"
            }, status=status.HTTP_401_UNAUTHORIZED)
        except jwt.InvalidTokenError:
            return Response({
                "error": "Invalid token"
            }, status=status.HTTP_401_UNAUTHORIZED)
        except User.DoesNotExist:
            return Response({
                "error": "User not found"
            }, status=status.HTTP_401_UNAUTHORIZED)
            
    except Exception as e:
        logger.exception("Error during token verification")
        return Response({
            "error": f"Token verification failed: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)