"""
Supabase-centric workflow API views
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.utils.crypto import get_random_string
from integrations.models import User, Company
from integrations.services.supabase_centric_service import DjangoAdminWorkflowService, SupabaseCentricService
from integrations.auth_utils import get_user_from_token
import logging

logger = logging.getLogger(__name__)


# REMOVED: django_admin_create_company_and_admin function
# Company + Admin creation is now handled via Django Admin Panel UI
# See: integrations/admin_views.py -> create_company_admin_view()
# Access via: Django Admin → Companies → "🚀 Create Company + Admin" button


@api_view(['POST'])
@permission_classes([AllowAny])
def admin_invite_team_member_supabase(request):
    """
    Admin invites team member with Supabase-centric workflow
    POST /api/integrations/admin/invite-team-member-supabase/
    
    Body:
    {
        "email": "<EMAIL>"
    }
    
    Headers:
    Authorization: Bearer <supabase-jwt-token>
    """
    try:
        data = request.data
        
        # Validate required fields
        if not data.get('email'):
            return Response(
                {"error": "email is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the inviting admin user
        admin_user = get_user_from_token(request) or request.user
        
        if not admin_user or not admin_user.is_authenticated:
            return Response(
                {"error": "Authentication required"},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Check if user is admin
        if admin_user.role != User.Role.ADMIN:
            return Response(
                {"error": "Only admin users can invite team members"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if not admin_user.company:
            return Response(
                {"error": "Admin user must belong to a company"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if user already exists in the company
        if User.objects.filter(email=data['email'], company=admin_user.company).exists():
            return Response(
                {"error": f"User with email {data['email']} already exists in company {admin_user.company.name}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Send Supabase-centric invitation
        supabase_service = SupabaseCentricService()
        invitation, supabase_user = supabase_service.send_team_member_supabase_invitation(
            team_member_email=data['email'],
            company=admin_user.company,
            invited_by_admin=admin_user
        )
        
        # Create Django user as well (for consistency)
        team_member_user = User.objects.create_user(
            email=data['email'],
            password=get_random_string(12),  # Random password, not used
            company=admin_user.company,
            role=User.Role.USER,
            is_active=True
        )
        
        return Response({
            "success": True,
            "message": f"Team member invitation sent to {data['email']} with Supabase login instructions",
            "data": {
                "invitation": {
                    "id": str(invitation.id),
                    "email": invitation.email,
                    "role": invitation.role,
                    "status": invitation.status,
                    "expiresAt": invitation.expires_at.isoformat()
                },
                "django_user": {
                    "id": str(team_member_user.id),
                    "email": team_member_user.email,
                    "role": team_member_user.role
                },
                "supabase_auth": {
                    "created": supabase_user is not None,
                    "user_id": supabase_user.get('id') if supabase_user else None
                },
                "email_sent": True
            }
        }, status=status.HTTP_201_CREATED)
        
    except ValueError as e:
        if "already exists" in str(e):
            return Response(
                {"error": str(e)},
                status=status.HTTP_409_CONFLICT
            )
        return Response(
            {"error": str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.exception("Error inviting team member with Supabase workflow")
        return Response(
            {"error": f"Failed to invite team member: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def list_companies_and_admins(request):
    """
    List all companies and their admins (for Django Admin overview)
    GET /api/integrations/django-admin/companies/
    """
    try:
        companies = Company.objects.all().order_by('-created_at')
        
        company_data = []
        for company in companies:
            admins = User.objects.filter(company=company, role=User.Role.ADMIN)
            team_members = User.objects.filter(company=company, role=User.Role.USER)
            
            company_data.append({
                "id": str(company.id),
                "name": company.name.title(),
                "slug": company.slug,
                "email": company.email,
                "created_at": company.created_at.isoformat(),
                "admins": [
                    {
                        "id": str(admin.id),
                        "email": admin.email,
                        "name": f"{admin.first_name} {admin.last_name}",
                        "is_active": admin.is_active
                    }
                    for admin in admins
                ],
                "team_members_count": team_members.count(),
                "total_users": admins.count() + team_members.count()
            })
        
        return Response({
            "success": True,
            "data": company_data,
            "total_companies": len(company_data)
        })
        
    except Exception as e:
        logger.exception("Error listing companies and admins")
        return Response(
            {"error": f"Failed to list companies: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def resend_admin_connection_email(request):
    """
    Resend Supabase connection email to admin
    POST /api/integrations/django-admin/resend-admin-email/
    
    Body:
    {
        "adminEmail": "<EMAIL>"
    }
    """
    try:
        data = request.data
        
        if not data.get('adminEmail'):
            return Response(
                {"error": "adminEmail is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Find the admin user
        try:
            admin_user = User.objects.get(email=data['adminEmail'], role=User.Role.ADMIN)
        except User.DoesNotExist:
            return Response(
                {"error": f"Admin user with email {data['adminEmail']} not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Generate new temp password and send email
        supabase_service = SupabaseCentricService()
        
        # Create new Supabase user with temp password (or update existing)
        user_metadata = {
            'company_id': str(admin_user.company.id),
            'company_name': admin_user.company.name,
            'role': admin_user.role,
            'first_name': admin_user.first_name,
            'last_name': admin_user.last_name
        }
        
        supabase_user, temp_password = supabase_service.create_supabase_user_with_temp_password(
            email=admin_user.email,
            user_metadata=user_metadata
        )
        
        # Send connection email
        supabase_service.send_admin_supabase_connection_email(admin_user, temp_password)
        
        return Response({
            "success": True,
            "message": f"Supabase connection email resent to {admin_user.email}",
            "data": {
                "admin": {
                    "email": admin_user.email,
                    "name": f"{admin_user.first_name} {admin_user.last_name}",
                    "company": admin_user.company.name.title()
                },
                "email_sent": True
            }
        })
        
    except Exception as e:
        logger.exception("Error resending admin connection email")
        return Response(
            {"error": f"Failed to resend email: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['DELETE'])
@permission_classes([AllowAny])
def delete_user_with_supabase_cleanup(request):
    """
    Delete user from both Django and Supabase
    DELETE /api/integrations/admin/delete-user-supabase/
    
    Body:
    {
        "userEmail": "<EMAIL>"
    }
    
    Headers:
    Authorization: Bearer <supabase-jwt-token>
    """
    try:
        data = request.data
        
        if not data.get('userEmail'):
            return Response(
                {"error": "userEmail is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the acting user (must be admin)
        acting_user = get_user_from_token(request) or request.user
        
        if not acting_user or not acting_user.is_authenticated:
            return Response(
                {"error": "Authentication required"},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Check if acting user is admin
        if acting_user.role != User.Role.ADMIN:
            return Response(
                {"error": "Only admin users can delete team members"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Find the target user
        try:
            target_user = User.objects.get(
                email=data['userEmail'], 
                company=acting_user.company
            )
        except User.DoesNotExist:
            return Response(
                {"error": f"User with email {data['userEmail']} not found in company {acting_user.company.name}"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Prevent deletion of admin users
        if target_user.role == User.Role.ADMIN:
            return Response(
                {"error": "Cannot delete admin users"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Prevent self-deletion
        if target_user.id == acting_user.id:
            return Response(
                {"error": "Cannot delete yourself"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Store user info for response
        deleted_user_info = {
            "id": str(target_user.id),
            "email": target_user.email,
            "name": f"{target_user.first_name} {target_user.last_name}",
            "role": target_user.role
        }
        
        # Step 1: Delete any pending team invitations
        from integrations.models import TeamInvitation
        invitations_deleted = TeamInvitation.objects.filter(
            email__iexact=target_user.email
        ).delete()
        logger.info(f"   Deleted {invitations_deleted[0]} pending team invitations for {target_user.email}")
        
        # Step 2: Delete from Supabase
        supabase_deletion_success = False
        supabase_error = None
        try:
            supabase_service = SupabaseCentricService()
            supabase_deletion_success = supabase_service.delete_supabase_user_by_email(target_user.email)
            if supabase_deletion_success:
                logger.info(f"✅ Deleted Supabase user: {target_user.email}")
            else:
                supabase_error = "Failed to delete from Supabase"
                logger.warning(f"⚠️ Failed to delete Supabase user: {target_user.email}")
        except Exception as e:
            supabase_error = str(e)
            logger.warning(f"⚠️ Error deleting Supabase user {target_user.email}: {str(e)}")
        
        # Step 3: Delete from Django
        target_user.delete()
        logger.info(f"✅ Successfully deleted Django user: {deleted_user_info['email']}")
        
        return Response({
            "success": True,
            "message": f"User {deleted_user_info['email']} deleted successfully",
            "data": {
                "deleted_user": deleted_user_info,
                "supabase_deletion_success": supabase_deletion_success,
                "supabase_error": supabase_error,
                "invitations_deleted": invitations_deleted[0] if invitations_deleted else 0
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.exception("Error deleting user with Supabase cleanup")
        return Response(
            {"error": f"Failed to delete user: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )