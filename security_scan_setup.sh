#!/bin/bash

# SageBase Security Scanning Setup Script
# This script sets up and runs various free security scanning tools

echo "🔒 SageBase Security Scanning Setup"
echo "=================================="

# Create security reports directory
mkdir -p security_reports
cd security_reports

echo "📦 Installing security scanning tools..."

# 1. Install Python security tools
echo "Installing Python security scanners..."
pip install safety bandit semgrep

# 2. Install Docker security scanner (if not already installed)
echo "Checking for Docker security tools..."
if ! command -v docker &> /dev/null; then
    echo "Docker not found. Please install Docker first."
fi

# 3. Install Node.js security scanner for frontend
echo "Installing Node.js security tools..."
npm install -g audit-ci retire

echo "✅ Security tools installation complete!"
echo ""

echo "🔍 Running Security Scans..."
echo "============================"

# 1. Python Dependency Vulnerability Scan
echo "1. Scanning Python dependencies for known vulnerabilities..."
cd ../SageBase_Backend
safety check --json > ../security_reports/python_vulnerabilities.json 2>/dev/null || safety check > ../security_reports/python_vulnerabilities.txt
echo "   ✅ Python vulnerability scan complete"

# 2. Python Static Analysis Security Testing (SAST)
echo "2. Running Python SAST with Bandit..."
bandit -r . -f json -o ../security_reports/bandit_sast_report.json 2>/dev/null || bandit -r . > ../security_reports/bandit_sast_report.txt
echo "   ✅ Python SAST scan complete"

# 3. Semgrep Security Scan
echo "3. Running Semgrep security analysis..."
semgrep --config=auto --json --output=../security_reports/semgrep_report.json . 2>/dev/null || semgrep --config=auto . > ../security_reports/semgrep_report.txt
echo "   ✅ Semgrep scan complete"

# 4. Docker Image Security Scan
echo "4. Scanning Docker images..."
cd ..
if command -v docker &> /dev/null; then
    # Scan the main application image
    docker build -t sagebase-security-scan SageBase_Backend/ 2>/dev/null
    
    # Use Docker Scout if available (free tier)
    docker scout cves sagebase-security-scan > security_reports/docker_scout_report.txt 2>/dev/null || echo "Docker Scout not available"
    
    # Alternative: Use Trivy if available
    if command -v trivy &> /dev/null; then
        trivy image sagebase-security-scan > security_reports/trivy_report.txt
    else
        echo "   ⚠️  Trivy not installed. Consider installing for comprehensive container scanning."
    fi
else
    echo "   ⚠️  Docker not available for container scanning"
fi

# 5. Frontend Security Scan
echo "5. Scanning frontend dependencies..."
cd SageBase_FrontEnd-pe 2>/dev/null || echo "Frontend directory not found"
if [ -f "package.json" ]; then
    npm audit --json > ../security_reports/npm_audit.json 2>/dev/null || npm audit > ../security_reports/npm_audit.txt
    retire --outputformat json --outputpath ../security_reports/retire_js.json 2>/dev/null || retire > ../security_reports/retire_js.txt
    echo "   ✅ Frontend security scan complete"
else
    echo "   ⚠️  Frontend package.json not found"
fi

cd ..

echo ""
echo "📊 Generating Security Summary..."
echo "================================"

# Create a summary report
cat > security_reports/security_scan_summary.md << 'EOF'
# SageBase Security Scan Summary

## Scan Date
$(date)

## Tools Used
- **Safety**: Python dependency vulnerability scanner
- **Bandit**: Python SAST (Static Application Security Testing)
- **Semgrep**: Multi-language static analysis
- **Docker Scout/Trivy**: Container vulnerability scanning
- **npm audit**: Node.js dependency scanning
- **RetireJS**: JavaScript library vulnerability scanning

## Report Files Generated
- `python_vulnerabilities.json/txt`: Known vulnerabilities in Python dependencies
- `bandit_sast_report.json/txt`: Python code security issues
- `semgrep_report.json/txt`: Multi-language security patterns
- `docker_scout_report.txt`: Container image vulnerabilities
- `trivy_report.txt`: Alternative container scanning
- `npm_audit.json/txt`: Frontend dependency vulnerabilities
- `retire_js.json/txt`: JavaScript library vulnerabilities

## Next Steps
1. Review each report for critical and high-severity issues
2. Update vulnerable dependencies where possible
3. Address code-level security issues identified by SAST tools
4. Implement security fixes and re-scan
5. Set up automated security scanning in CI/CD pipeline

## Recommended Actions
- Set up automated dependency scanning
- Implement security linting in development workflow
- Regular security updates and patches
- Consider implementing SonarQube for comprehensive analysis
EOF

echo "✅ Security scanning complete!"
echo ""
echo "📁 Reports saved in: security_reports/"
echo "📋 Summary available in: security_reports/security_scan_summary.md"
echo ""
echo "🔍 To review results:"
echo "   ls -la security_reports/"
echo "   cat security_reports/security_scan_summary.md"
echo ""
echo "⚠️  Please review all reports and address critical/high severity issues"
