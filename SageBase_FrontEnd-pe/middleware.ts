import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/spaces',
  '/settings',
  '/documents',
  '/qa',
  '/editor',
  '/users',
  '/team-contribution-map',

  '/ai-search',
  '/search',
  '/qa-history',
  '/repo-change'
]

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/signup',
  '/forgot-password',
  '/reset-password',
  '/auth/callback',
  '/logo-demo',
  '/test-qa',
  '/qa-test',
  '/websocket-test',
  '/debug-cache',
  '/collaboration-demo'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  console.log(`🔒 Middleware processing: ${pathname}`)

  // Skip middleware for API routes, static files, and Next.js internals
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    console.log(`⏭️ Skipping middleware for: ${pathname}`)
    return NextResponse.next()
  }

  // Check for any auth-related cookies
  const hasAnyAuthCookie = request.cookies.has('sessionid') || 
                           request.cookies.has('csrftoken') ||
                           request.cookies.has('authToken') ||
                           request.cookies.has('user')

  console.log(`🔍 Session check for ${pathname}:`, {
    hasAnyAuthCookie,
    cookies: Array.from(request.cookies.getAll().map(c => c.name))
  })

  // For now, let all routes through and let the frontend handle authentication
  // This prevents the redirect loop while maintaining security through the frontend
  console.log(`✅ Allowing access to: ${pathname}`)
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
