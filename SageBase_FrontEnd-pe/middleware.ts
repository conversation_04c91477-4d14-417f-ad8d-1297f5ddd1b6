import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'

// Public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/signup',
  '/forgot-password',
  '/reset-password',
  '/auth/callback'
]

// Routes that should redirect to login if not authenticated
const protectedRoutes = [
  '/dashboard',
  '/ai-search',
  '/search',
  '/documents',
  '/editor',
  '/spaces',
  '/settings',
  '/users',
  '/insights',
  '/qa-history',
  '/teams-integration',
  '/team-knowledge-map'
]

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const pathname = req.nextUrl.pathname

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.') ||
    pathname.startsWith('/images')
  ) {
    return res
  }

  try {
    // Create a Supabase client configured to use cookies
    const supabase = createMiddlewareClient({ req, res })
    
    // Check if route is public or protected
    const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))
    const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))
    
    let session = null
    if (isProtectedRoute || isPublicRoute) {
      // Only call getSession when we actually need to check authentication
      console.log('🔐 Middleware: Getting session for route:', pathname);
      const { data: { session: sessionData } } = await supabase.auth.getSession()
      session = sessionData
    }

    // If user is not authenticated and trying to access protected route
    if (!session && isProtectedRoute) {
      const loginUrl = new URL('/login', req.url)
      loginUrl.searchParams.set('redirectTo', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // If user is authenticated and trying to access auth pages, redirect to dashboard
    if (session && isPublicRoute && pathname !== '/auth/callback') {
      return NextResponse.redirect(new URL('/ai-search', req.url))
    }

    return res
  } catch (error) {
    // If there's an error with Supabase, allow access but redirect to login for protected routes
    console.error('Middleware error:', error)
    
    const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))
    if (isProtectedRoute) {
      return NextResponse.redirect(new URL('/login', req.url))
    }
    
    return res
  }
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico|.*\\.png$).*)"],
}
