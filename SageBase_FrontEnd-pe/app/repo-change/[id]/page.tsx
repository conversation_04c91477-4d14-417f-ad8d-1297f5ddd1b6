"use client";

import { useState, useEffect } from "react";
import { getBackendUrl } from '@/lib/api-config';
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Loader2, ThumbsUp, ThumbsDown } from "lucide-react";
import SideNavigation from "@/components/side-navigation";

export default function RepoChangeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const [change, setChange] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [voting, setVoting] = useState(false);

  const BACKEND_BASE_URL = getBackendUrl();

  useEffect(() => {
    const fetchChange = async () => {
      setLoading(true);
      try {
        const res = await fetch(`${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/${id}/`);
        if (res.ok) {
          setChange(await res.json());
        } else {
          setError("Not found");
        }
      } catch {
        setError("Failed to load");
      } finally {
        setLoading(false);
      }
    };
    if (id) fetchChange();
  }, [id]);

  const handleVote = async (action: "like" | "dislike") => {
    if (!change || voting) return;
    setVoting(true);
    try {
      await fetch(`${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/${id}/like/`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action }),
      });
      setChange((prev: any) => prev ? { ...prev, likes: action === "like" ? prev.likes + 1 : prev.likes, dislikes: action === "dislike" ? prev.dislikes + 1 : prev.dislikes } : prev);
    } finally {
      setVoting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <SideNavigation />
        <div className="flex-1 flex items-center justify-center">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading change...</span>
        </div>
      </div>
    );
  }
  if (error || !change) {
    return (
      <div className="flex h-screen bg-gray-50">
        <SideNavigation />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Change Not Found</h2>
            <p className="text-gray-600 mb-4">{error || "The requested change could not be found."}</p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  function parseLLMSuggestedDocs(raw: any): any[] {
    if (!raw) return [];
    let str = raw;
    if (typeof str !== "string") return [];
    str = str.trim();
    // Remove triple backticks and optional 'json' after them
    if (str.startsWith("```")) {
      str = str.replace(/^```[a-zA-Z]*\s*/, "").replace(/```$/, "").trim();
    }
    // Remove leading 'json' if present
    if (str.toLowerCase().startsWith("json")) {
      str = str.slice(4).trim();
    }
    try {
      const parsed = JSON.parse(str);
      if (Array.isArray(parsed)) return parsed;
      return [];
    } catch {
      return [];
    }
  }

  const llmDocs = parseLLMSuggestedDocs(change.details.llm_suggested_docs);

  return (
    <div className="flex h-screen bg-gray-50">
      <SideNavigation />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-1.5 h-4 w-4" /> Back
            </Button>
            <span className="text-xs text-gray-500">Repo Change Detail</span>
          </div>
        </header>
        <div className="flex-1 overflow-auto bg-white">
          <div className="max-w-3xl mx-auto py-8 px-4">
            <h1 className="text-xl font-bold text-purple-800 mb-2">{change.summary}</h1>
            <div className="mb-2 text-xs text-gray-500">{change.internal_repo} &larr; {change.external_repo}</div>
            <div className="mb-2 text-xs text-gray-500">Commit: {change.commit_id}</div>
            <div className="mb-2 text-xs text-gray-500">Status: <Badge>{change.status}</Badge></div>
            <div className="mb-4 text-xs text-gray-400">Created: {new Date(change.created_at).toLocaleString()}</div>
            {llmDocs.length > 0 && (
              <div className="mb-4">
                <h2 className="font-semibold text-gray-700 mb-2">LLM Suggested Changes</h2>
                <div className="space-y-4">
                  {llmDocs.map((doc, idx) => (
                    <div key={idx} className="bg-purple-50 border border-purple-200 rounded p-4">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-bold text-purple-800">{doc.summary}</span>
                        {doc.severity && (
                          <Badge className={
                            doc.severity.toLowerCase() === "breaking" ? "bg-red-200 text-red-800" :
                            doc.severity.toLowerCase() === "major" ? "bg-orange-200 text-orange-800" :
                            doc.severity.toLowerCase() === "addition" ? "bg-green-200 text-green-800" :
                            doc.severity.toLowerCase() === "removal" ? "bg-pink-200 text-pink-800" :
                            doc.severity.toLowerCase() === "modification" ? "bg-blue-200 text-blue-800" :
                            "bg-gray-200 text-gray-700"
                          }>{doc.severity}</Badge>
                        )}
                      </div>
                      {doc.file && <div className="text-xs text-gray-500 mb-1">File: {doc.file}</div>}
                      {doc.before && (
                        <div className="mb-1">
                          <div className="text-xs text-gray-500">Before:</div>
                          <pre className="bg-gray-100 rounded p-2 text-xs overflow-x-auto"><code>{doc.before}</code></pre>
                        </div>
                      )}
                      {doc.after && (
                        <div className="mb-1">
                          <div className="text-xs text-gray-500">After:</div>
                          <pre className="bg-gray-100 rounded p-2 text-xs overflow-x-auto"><code>{doc.after}</code></pre>
                        </div>
                      )}
                      {doc.details && <div className="text-xs text-gray-700 mt-1">{doc.details}</div>}
                    </div>
                  ))}
                </div>
              </div>
            )}
            <div className="flex items-center space-x-4 mt-6">
              <Button variant="ghost" size="sm" onClick={() => handleVote("like")}
                disabled={voting} className="flex items-center space-x-1 hover:bg-green-100 text-gray-600">
                <ThumbsUp className="h-4 w-4" />
                <span className="font-medium">{change.likes}</span>
              </Button>
              <Button variant="ghost" size="sm" onClick={() => handleVote("dislike")}
                disabled={voting} className="flex items-center space-x-1 hover:bg-red-100 text-gray-600">
                <ThumbsDown className="h-4 w-4" />
                <span className="font-medium">{change.dislikes}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 