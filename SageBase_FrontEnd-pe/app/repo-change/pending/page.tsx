"use client";

import { useState, useEffect } from "react";
import { getBackendUrl } from "@/lib/api-config";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import SideNavigation from "@/components/side-navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function PendingRepoChangesPage() {
  const { connectedUserCompany } = useAuth();
  const companyId = connectedUserCompany?.company_id;
  const [repoChanges, setRepoChanges] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [bulkLoading, setBulkLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    id: string;
    action: "approve" | "reject" | null;
  } | null>(null);

  const BACKEND_BASE_URL = getBackendUrl();

  // Poll for pending repo changes
  useEffect(() => {
    if (!companyId) return;
    let interval: any;
    const fetchRepoChanges = async () => {
      setLoading(true);
      console.log("[RepoChangePolling] Fetching pending repo changes...");
      try {
        const res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/?status=pending&company_id=${companyId}`
        );
        if (res.ok) {
          const data = await res.json();
          console.log(
            "[RepoChangePolling] Pending Details repo changes Page received:",
            data
          );
          setRepoChanges(data);
        } else {
          console.log("[RepoChangePolling] Error: Non-OK response", res.status);
          setRepoChanges([]);
        }
      } catch (err) {
        console.log("[RepoChangePolling] Error:", err);
        setRepoChanges([]);
      } finally {
        setLoading(false);
      }
    };
    fetchRepoChanges();
    interval = setInterval(fetchRepoChanges, 6000000);
    return () => clearInterval(interval);
  }, [companyId]);

  const handleAction = async (id: string, action: "approve" | "reject") => {
    await fetch(
      `${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/${id}/approve/`,
      {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action }),
      }
    );
    setRepoChanges((prev) => prev.filter((c) => c.id !== id));
  };

  const handleBulkAction = async (action: "approve" | "reject") => {
    setBulkLoading(true);
    for (const change of repoChanges) {
      await handleAction(change.id, action);
    }
    setBulkLoading(false);
  };

  const confirmRepoChangeAction = async () => {
    if (!confirmDialog) return;
    await handleAction(confirmDialog.id, confirmDialog.action);
    setConfirmDialog(null);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <SideNavigation />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
          <h1 className="text-xl font-bold text-purple-800">
            Pending Repo Change Approvals
          </h1>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
              onClick={() => handleBulkAction("approve")}
              disabled={bulkLoading || loading}
            >
              <CheckCircle className="h-4 w-4 mr-1" /> Approve All
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
              onClick={() => handleBulkAction("reject")}
              disabled={bulkLoading || loading}
            >
              <XCircle className="h-4 w-4 mr-1" /> Reject All
            </Button>
          </div>
        </header>
        <div className="flex-1 overflow-auto bg-white">
          <div className="max-w-3xl mx-auto py-8 px-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading pending repo changes...</span>
              </div>
            ) : repoChanges.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No pending repo changes found.
              </div>
            ) : (
              repoChanges.map((change) => (
                <div
                  key={change.id}
                  className="mb-4 p-4 bg-yellow-50 rounded shadow border border-yellow-200"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-700 font-semibold">
                      {change.internal_repo} &larr; {change.external_repo}
                    </span>
                    <span className="text-xs text-gray-400">
                      {new Date(change.created_at).toLocaleString()}
                    </span>
                  </div>
                  <div className="text-sm font-medium text-yellow-900 mb-1">
                    {change.summary}
                  </div>
                  <div className="text-xs text-gray-600 mb-2">
                    Commit: {change.commit_id}
                  </div>
                  <Button
                    size="sm"
                    variant="link"
                    className="text-blue-600 px-0"
                    onClick={() =>
                      (window.location.href = `/repo-change/${change.id}`)
                    }
                  >
                    View Details
                  </Button>
                  <div className="flex space-x-2 mt-2">
                    <Button
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() =>
                        setConfirmDialog({ id: change.id, action: "approve" })
                      }
                    >
                      Approve
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                      onClick={() =>
                        setConfirmDialog({ id: change.id, action: "reject" })
                      }
                    >
                      Reject
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
      {/* Confirmation Dialog for Approve/Reject */}
      <AlertDialog
        open={!!confirmDialog}
        onOpenChange={(open) => !open && setConfirmDialog(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmDialog?.action === "approve"
                ? "Approve Repo Change"
                : "Reject Repo Change"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {confirmDialog?.action} this repo change?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRepoChangeAction}
              className={
                confirmDialog?.action === "approve"
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {confirmDialog?.action === "approve" ? "Approve" : "Reject"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
