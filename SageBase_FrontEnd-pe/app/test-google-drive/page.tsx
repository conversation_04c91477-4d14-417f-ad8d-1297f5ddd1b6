"use client";
import { useState } from "react";
import { getBackendUrl } from '@/lib/api-config';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, CheckCircle2, XCircle, FileText, AlertCircle } from "lucide-react";

const BACKEND_BASE_URL = getBackendUrl();

export default function TestGoogleDrivePage() {
  const [status, setStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showGoogleDriveApproval, setShowGoogleDriveApproval] = useState(false);

  // Test Google Drive status endpoint
  const testGoogleDriveStatus = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Using test parameters with company ID only
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/google-drive/status/?company_id=50936f0a-8ea4-4bc5-a80d-c3022a5133a7&workspace=default`
      );

      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        const errorData = await response.json();
        setError(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test Google Drive OAuth start
  const testGoogleDriveOAuth = async () => {
    // Show Google Drive approval popup instead of proceeding with OAuth
    setShowGoogleDriveApproval(true);
    return;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Google Drive Integration Test</h1>
        <p className="text-gray-600">
          Test the Google Drive integration endpoints
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Status Endpoint</CardTitle>
            <CardDescription>
              Test the Google Drive status API endpoint
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testGoogleDriveStatus}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle2 className="mr-2 h-4 w-4" />
              )}
              Test Status API
            </Button>

            {status && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium mb-2">Response:</h3>
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(status, null, 2)}
                </pre>
              </div>
            )}

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-medium text-red-800 mb-2">Error:</h3>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test OAuth Flow</CardTitle>
            <CardDescription>
              Test the Google Drive OAuth authorization flow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testGoogleDriveOAuth}
              disabled={isLoading}
              variant="outline"
              className="w-full"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <XCircle className="mr-2 h-4 w-4" />
              )}
              Test OAuth Flow
            </Button>

            <div className="text-sm text-gray-600">
              <p>This will open a new tab with the Google OAuth flow.</p>
              <p className="mt-2">
                <strong>Test Parameters:</strong>
              </p>
              <ul className="mt-1 space-y-1">
                <li>• Company ID: 50936f0a-8ea4-4bc5-a80d-c3022a5133a7</li>
                <li>• Workspace: default</li>
                <li>• User: First admin user from company</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Integration Links</CardTitle>
          <CardDescription>
            Access the full Google Drive integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="/google-drive-integration" className="block">
              <Button className="w-full">
                Full Google Drive Integration
              </Button>
            </a>

          </div>
        </CardContent>
      </Card>

      {/* Google Drive Approval Popup */}
      <Dialog open={showGoogleDriveApproval} onOpenChange={setShowGoogleDriveApproval}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Google Drive Integration
            </DialogTitle>
            <DialogDescription>
              SageBase is currently under Google approval process
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-yellow-800 mb-2">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">Under Google Approval</span>
              </div>
              <p className="text-sm text-yellow-700">
                SageBase is currently going through Google's approval process for Google Drive integration. 
                This process ensures security and compliance with Google's policies.
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-800 mb-2">
                {/* <MailIcon className="h-5 w-5" /> */}
                <span className="font-medium">Contact Admin for Access</span>
              </div>
              <p className="text-sm text-blue-700">
                To get early access to Google Drive integration, please send an email to{' '}
                <a 
                  href="mailto:<EMAIL>" 
                  className="font-medium underline hover:text-blue-900"
                >
                  <EMAIL>
                </a>
                {' '}with your company details and use case.
              </p>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button 
                variant="outline" 
                onClick={() => setShowGoogleDriveApproval(false)}
              >
                Abort
              </Button>
              <Button 
                onClick={() => {
                  setShowGoogleDriveApproval(false);
                  // Continue with the original Google Drive OAuth flow
                  setIsLoading(true);
                  try {
                    const authUrl = `${BACKEND_BASE_URL}/api/integrations/google-drive/start/?company_id=50936f0a-8ea4-4bc5-a80d-c3022a5133a7&workspace=default`;
                    window.open(authUrl, '_blank');
                  } catch (err) {
                    console.error(`OAuth error: ${err instanceof Error ? err.message : 'Unknown error'}`);
                  } finally {
                    setIsLoading(false);
                  }
                }}
                variant="outline"
                className="border-green-600 text-green-600 hover:bg-green-50"
              >
                Continue Anyway
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 