"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, CheckCircle2, XCircle } from "lucide-react";

const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

export default function TestGoogleDrivePage() {
  const [status, setStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Test Google Drive status endpoint
  const testGoogleDriveStatus = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Using test parameters with company ID only
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/google-drive/status/?company_id=50936f0a-8ea4-4bc5-a80d-c3022a5133a7&workspace=default`
      );

      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        const errorData = await response.json();
        setError(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test Google Drive OAuth start
  const testGoogleDriveOAuth = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const authUrl = `${BACKEND_BASE_URL}/api/integrations/google-drive/start/?company_id=50936f0a-8ea4-4bc5-a80d-c3022a5133a7&workspace=default`;
      window.open(authUrl, '_blank');
    } catch (err) {
      setError(`OAuth error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Google Drive Integration Test</h1>
        <p className="text-gray-600">
          Test the Google Drive integration endpoints
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Status Endpoint</CardTitle>
            <CardDescription>
              Test the Google Drive status API endpoint
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testGoogleDriveStatus}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle2 className="mr-2 h-4 w-4" />
              )}
              Test Status API
            </Button>

            {status && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium mb-2">Response:</h3>
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(status, null, 2)}
                </pre>
              </div>
            )}

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-medium text-red-800 mb-2">Error:</h3>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test OAuth Flow</CardTitle>
            <CardDescription>
              Test the Google Drive OAuth authorization flow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testGoogleDriveOAuth}
              disabled={isLoading}
              variant="outline"
              className="w-full"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <XCircle className="mr-2 h-4 w-4" />
              )}
              Test OAuth Flow
            </Button>

            <div className="text-sm text-gray-600">
              <p>This will open a new tab with the Google OAuth flow.</p>
              <p className="mt-2">
                <strong>Test Parameters:</strong>
              </p>
              <ul className="mt-1 space-y-1">
                <li>• Company ID: 50936f0a-8ea4-4bc5-a80d-c3022a5133a7</li>
                <li>• Workspace: default</li>
                <li>• User: First admin user from company</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Integration Links</CardTitle>
          <CardDescription>
            Access the full Google Drive integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="/google-drive-integration" className="block">
              <Button className="w-full">
                Full Google Drive Integration
              </Button>
            </a>
            <a href="/teams-integration" className="block">
              <Button variant="outline" className="w-full">
                Teams Integration Page
              </Button>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 