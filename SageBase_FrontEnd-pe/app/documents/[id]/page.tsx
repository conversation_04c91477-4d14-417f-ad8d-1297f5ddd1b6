"use client"

import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Database, FileText, MessageSquare, Code, Mail, Clock, User, Pencil } from "lucide-react"
import SideNavigation from "@/components/side-navigation"
import { DrawingCanvas } from "@/components/drawing-canvas"
import TopNavigation from "@/components/top-navigation"

export default function DocumentPage() {
  const params = useParams()
  const documentId = params.id
  const [showDrawingMode, setShowDrawingMode] = useState(false)

  // In a real app, we would fetch the document data based on the ID
  // For now, we'll just simulate different document types based on the ID
  const getDocumentData = () => {
    // Special case for babel-errors document
    if (documentId === "babel-errors") {
      return {
        id: documentId,
        title: "Fixing Babel Module Errors in Development Environment",
        source: "github",
        author: "Wissem Riahi",
        date: "Yesterday",
      }
    }

    const sources = ["confluence", "jira", "slack", "github", "email"]
    const sourceIndex = Number(documentId) % sources.length
    const source = sources[sourceIndex]

    const titles = [
      "API Authentication Documentation",
      "Auth Service Refactoring",
      "Fix OAuth token refresh logic",
      "AUTH-245: Implement token refresh mechanism",
      "Re: OAuth Implementation Timeline",
    ]

    const authors = ["Michael Chen", "Sarah Johnson", "Alex Rodriguez", "You", "Jamie Smith"]

    return {
      id: documentId,
      title: titles[Number(documentId) % titles.length],
      source,
      author: authors[Number(documentId) % authors.length],
      date: "2 days ago",
    }
  }

  const document = getDocumentData()

  const getSourceIcon = (source: string) => {
    if (!source) return <FileText className="h-5 w-5 text-gray-500" />

    switch (source) {
      case "confluence":
        return <Database className="h-5 w-5 text-emerald-500" />
      case "jira":
        return <FileText className="h-5 w-5 text-blue-500" />
      case "slack":
        return <MessageSquare className="h-5 w-5 text-purple-500" />
      case "github":
        return <Code className="h-5 w-5 text-gray-700" />
      case "email":
        return <Mail className="h-5 w-5 text-red-500" />
      default:
        return <FileText className="h-5 w-5 text-gray-500" />
    }
  }

  const getSourceLabel = (source: string) => {
    if (!source) return "Document"
    return source.charAt(0).toUpperCase() + source.slice(1)
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Document Content */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-4xl mx-auto">
            {/* Document Header */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 mb-6">
              <div className="flex items-center mb-4">
                {getSourceIcon(document.source)}
                <span className="ml-2 text-sm font-medium text-gray-600">{getSourceLabel(document.source)}</span>
                <span className="mx-2 text-gray-300">•</span>
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="ml-1 text-sm text-gray-500">{document.date}</span>
                <span className="mx-2 text-gray-300">•</span>
                <User className="h-4 w-4 text-gray-400" />
                <span className="ml-1 text-sm text-gray-500">{document.author}</span>
              </div>

              <h1 className="text-2xl font-bold text-gray-800 mb-2">{document.title}</h1>

              <div className="flex flex-wrap gap-2 mt-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                  Documentation
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  API
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Authentication
                </span>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                  onClick={() => setShowDrawingMode(!showDrawingMode)}
                >
                  <Pencil className="mr-1.5 h-4 w-4" />
                  {showDrawingMode ? "Hide Drawing Mode" : "Show Drawing Mode"}
                </Button>
              </div>
            </div>

            {/* Babel Error Document Content */}
            {documentId === "babel-errors" && (
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 mb-20">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Fixing Babel Preset Env Module Error</h2>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-700">Error Description</h3>
                  <p className="text-gray-600">
                    When running the development environment, you may encounter this error:
                  </p>
                  <div className="bg-gray-100 p-4 rounded-md font-mono text-sm text-red-600 overflow-x-auto">
                    Error: Cannot find module &apos;@babel/preset-env&apos;
                  </div>

                  <h3 className="text-lg font-semibold text-gray-700 mt-6">Solution</h3>
                  <p className="text-gray-600">
                    This error occurs when the Babel dependencies are not properly installed or when the npm cache is
                    corrupted. To fix this issue, run the following command in your terminal:
                  </p>
                  <div className="bg-gray-100 p-4 rounded-md font-mono text-sm overflow-x-auto">
                    npm install --save-dev @babel/preset-env && npm cache clean --force
                  </div>

                  <h3 className="text-lg font-semibold text-gray-700 mt-6">Explanation</h3>
                  <p className="text-gray-600">The command above does two things:</p>
                  <ol className="list-decimal pl-5 space-y-2 text-gray-600">
                    <li>Installs the missing @babel/preset-env package as a dev dependency</li>
                    <li>Cleans the npm cache to resolve any cache-related issues</li>
                  </ol>

                  <h3 className="text-lg font-semibold text-gray-700 mt-6">Prevention</h3>
                  <p className="text-gray-600">To prevent this issue in the future:</p>
                  <ul className="list-disc pl-5 space-y-2 text-gray-600">
                    <li>Make sure your package.json includes all necessary Babel dependencies</li>
                    <li>Run npm install after pulling new changes from the repository</li>
                    <li>Consider using a .npmrc file to ensure consistent dependency installation</li>
                  </ul>

                  <div className="bg-blue-50 p-4 rounded-md mt-6 border-l-4 border-blue-500">
                    <p className="text-blue-700">
                      <strong>Note:</strong> This solution was shared by Wissem Riahi in the Wifi_team channel on
                      Microsoft Teams.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Simulation Notice */}
            {documentId !== "babel-errors" && (
              <div className="bg-amber-50 border border-amber-200 rounded-xl p-6 mb-6 text-center">
                <h2 className="text-xl font-bold text-amber-700 mb-2">Simulation Mode</h2>
                <p className="text-amber-600">
                  This is a simulated document page. In a real application, this would display the actual content of
                  document ID: {documentId}.
                </p>
              </div>
            )}

            {/* Drawing Canvas */}
            {showDrawingMode && (
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 mb-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">Document Drawing</h2>
                <DrawingCanvas />
              </div>
            )}

            {/* Fake Document Content */}
            {documentId !== "babel-errors" && (
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 mb-20">
                <div className="space-y-4">
                  <div className="h-6 bg-gray-100 rounded w-3/4 animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded w-5/6 animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded w-4/5 animate-pulse"></div>

                  <div className="h-4 w-1/3 bg-gray-100 rounded animate-pulse mt-8"></div>
                  <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded w-5/6 animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded w-4/5 animate-pulse"></div>

                  <div className="h-4 w-1/3 bg-gray-100 rounded animate-pulse mt-8"></div>
                  <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded w-5/6 animate-pulse"></div>
                  <div className="h-6 bg-gray-100 rounded w-3/4 animate-pulse"></div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
