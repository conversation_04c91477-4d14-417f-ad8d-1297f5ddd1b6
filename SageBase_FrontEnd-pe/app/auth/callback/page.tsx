"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

export default function AuthCallbackPage() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isPasswordReset, setIsPasswordReset] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const supabase = createClientComponentClient();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get the code and type from the URL
        const url = new URL(window.location.href);
        const code = url.searchParams.get("code");
        const type = url.searchParams.get("type");


        for (const [key, value] of url.searchParams.entries()) {
          console.log(`  - ${key}: ${value}`);
        }

        if (!code) {
          setError("No authorization code found in the URL");
          return;
        }

        // Check if this is a password reset - multiple ways to detect
        const isPasswordReset =
          type === "recovery" ||
          type === "recovery_email" ||
          type === "email_change" ||
          window.location.href.includes("recovery") ||
          window.location.href.includes("reset");

        console.log("🔍 Checking password reset conditions:");
        console.log("  - type:", type);
        console.log(
          "  - URL includes 'recovery':",
          window.location.href.includes("recovery")
        );
        console.log(
          "  - URL includes 'reset':",
          window.location.href.includes("reset")
        );

        if (isPasswordReset) {
          console.log("🔄 Password reset flow detected");
          setIsPasswordReset(true);
          return;
        }

        // Also check if there's a recovery token in the URL
        const recoveryToken = url.searchParams.get("recovery_token");
        if (recoveryToken) {
          console.log("🔄 Recovery token detected");
          setIsPasswordReset(true);
          return;
        }

        // Check for any recovery-related parameters
        const hasRecoveryParams = Array.from(url.searchParams.entries()).some(
          ([key, value]) =>
            key.toLowerCase().includes("recovery") ||
            key.toLowerCase().includes("reset") ||
            value.toLowerCase().includes("recovery") ||
            value.toLowerCase().includes("reset")
        );

        if (hasRecoveryParams) {
          console.log("🔄 Recovery parameters detected in URL");
          setIsPasswordReset(true);
          return;
        }

        // If we have a code but no type parameter, it might be a password reset
        if (code && !type) {
          console.log("🔄 Code present but no type - likely password reset");
          console.log(
            "🔄 Will attempt OAuth exchange, but fallback to password reset if it fails"
          );
        }

        // Handle OAuth authentication
        console.log("🔐 OAuth authentication flow");
        const { data, error } = await supabase.auth.exchangeCodeForSession(
          code
        );
        console.log("🔐 OAuth exchange result:", {
          error: error?.message || "success",
          data: data ? "session created" : "no session",
        });

        if (error) {
          console.error("Error exchanging code for session:", error);

          // If the error suggests this might be a password reset, show the form
          if (
            error.message.includes("recovery") ||
            error.message.includes("reset") ||
            error.message.includes("code verifier") ||
            error.message.includes("auth code") ||
            error.message.includes("invalid request")
          ) {
            console.log("🔄 Detected password reset from error message");
            setIsPasswordReset(true);
            console.log(
              "🔄 Setting password reset mode - preventing further authentication"
            );
            return;
          }

          setError(error.message);
          return;
        }

        // Check if this is a recovery session (even if successful)
        if (data?.session?.user) {
          const user = data.session.user;
          console.log("🔍 Session created - checking if recovery session");
          console.log("🔍 User recovery sent at:", user.recovery_sent_at);
          console.log("🔍 User email confirmed at:", user.email_confirmed_at);
          console.log("🔍 User created at:", user.created_at);
          console.log("🔍 User last sign in at:", user.last_sign_in_at);

          // If this is a recovery session, show password reset form
          if (
            user.recovery_sent_at ||
            window.location.href.includes("recovery")
          ) {
            console.log(
              "🔄 Detected recovery session - showing password reset form"
            );
            setIsPasswordReset(true);
            return;
          }
        }

        // If we're in password reset mode, don't verify session
        if (isPasswordReset) {
          console.log(
            "🔄 In password reset mode - skipping session verification"
          );
          return;
        }

        // Verify the session was created
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session) {
          console.error("No session after code exchange");
          setError("Failed to create session");
          return;
        }

        console.log(
          "✅ Authentication successful, session created:",
          session.user.id
        );
        console.log("✅ Session details:", {
          user: session.user.email,
          confirmed: session.user.email_confirmed_at,
          lastSignIn: session.user.last_sign_in_at,
        });

        // Check if this is a password reset session (user might need to set password)
        const {
          data: { user },
        } = await supabase.auth.getUser();
        console.log("🔍 User after authentication:", user);
        console.log("🔍 User email confirmed:", user?.email_confirmed_at);
        console.log("🔍 User last sign in:", user?.last_sign_in_at);

        // If this is a recovery session, show password reset form
        if (
          user &&
          !user.email_confirmed_at &&
          window.location.href.includes("recovery")
        ) {
          console.log(
            "🔄 Detected recovery session - showing password reset form"
          );
          setIsPasswordReset(true);
          return;
        }

        // If we detected password reset earlier, don't redirect
        if (isPasswordReset) {
          console.log("🔄 Staying on password reset form - not redirecting");
          return;
        }

        // Also check if we're in password reset mode before redirecting
        console.log("🔄 About to redirect - checking if password reset mode");
        if (isPasswordReset) {
          console.log("🔄 In password reset mode - not redirecting");
          return;
        }

        // Redirect to home page after successful authentication
        console.log("🔄 Redirecting to home page");
        router.push("/");
      } catch (err) {
        console.error("Error in auth callback:", err);
        setError("An unexpected error occurred during authentication");
      }
    };

    handleCallback();
  }, [router, supabase.auth]);

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (newPassword.length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }

    setIsUpdatingPassword(true);
    setError(null);

    try {
      // We don't need the code for password update - we already have an active session


      // Update the password using the recovery session
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        console.error("❌ Password update error:", error);
        setError(`Failed to update password: ${error.message}`);
        return;
      }


      // Sign out and redirect to login page
      await supabase.auth.signOut();
      console.log("✅ Signed out after password update");

      router.push("/login?message=password-updated");
    } catch (err) {
      console.error("❌ Unexpected error during password update:", err);
      setError("An unexpected error occurred while updating your password");
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  // Password reset form - render this first to prevent redirect
  if (isPasswordReset) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Reset Your Password
            </h1>
            <p className="mt-2 text-gray-600">Enter your new password below</p>
          </div>

          <form onSubmit={handlePasswordUpdate} className="space-y-4">
            <div>
              <label
                htmlFor="newPassword"
                className="block text-sm font-medium text-gray-700"
              >
                New Password
              </label>
              <input
                type="password"
                id="newPassword"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your new password"
                required
                minLength={6}
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700"
              >
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Confirm your new password"
                required
                minLength={6}
              />
            </div>

            {error && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={isUpdatingPassword}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdatingPassword ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Updating Password...
                </>
              ) : (
                "Update Password"
              )}
            </button>
          </form>

          <div className="mt-4 text-center">
            <button
              onClick={() => router.push("/login")}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center max-w-md p-6 bg-white rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-red-600">
            Authentication Error
          </h1>
          <p className="mt-2 text-gray-700">{error}</p>
          <button
            onClick={() => router.push("/login")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Authenticating...</h1>
        <p className="mt-2">You will be redirected shortly.</p>
        <div className="mt-4 animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
      </div>
    </div>
  );
}
