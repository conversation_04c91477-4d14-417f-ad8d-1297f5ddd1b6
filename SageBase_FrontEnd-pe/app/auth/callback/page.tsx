"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { getBackendUrl } from "@/lib/api-config";
import { slackNotificationService } from "@/services/slack-notification-service";
// Slack notifications disabled

export default function AuthCallbackPage() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isPasswordReset, setIsPasswordReset] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get the code and type from the URL
        const url = new URL(window.location.href);
        const code = url.searchParams.get("code");
        const type = url.searchParams.get("type");
        const token = url.searchParams.get("token");

        console.log("🔍 Auth callback parameters:");
        for (const [key, value] of url.searchParams.entries()) {
          console.log(`  - ${key}: ${value}`);
        }

        // Check if this is a password reset
        const isPasswordReset =
          type === "recovery" ||
          type === "recovery_email" ||
          type === "email_change" ||
          type === "password_reset" ||
          window.location.href.includes("recovery") ||
          window.location.href.includes("reset") ||
          window.location.href.includes("password");

        console.log("🔍 Checking password reset conditions:");
        console.log("  - type:", type);
        console.log(
          "  - URL includes 'recovery':",
          window.location.href.includes("recovery")
        );
        console.log(
          "  - URL includes 'reset':",
          window.location.href.includes("reset")
        );
        console.log(
          "  - URL includes 'password':",
          window.location.href.includes("password")
        );

        if (isPasswordReset) {
          console.log("🔄 Password reset flow detected");
          setIsPasswordReset(true);
          return;
        }

        // Handle Django authentication
        if (token) {
          console.log("🔐 Django token authentication flow");
          try {
            // For Django JWT, we typically don't need to verify with backend
            // Just store the token and redirect
            console.log(
              "✅ Django JWT token received, storing and redirecting"
            );

            // Store the token (assuming it's a valid JWT)
            localStorage.setItem("authToken", token);

            // Redirect to home page
            router.push("/");
          } catch (err) {
            console.error("Token storage error:", err);
            setError("Failed to store authentication token");
          }
        } else if (code) {
          // Handle OAuth callback (if you're still using OAuth with Django)
          console.log("🔐 OAuth callback flow");
          try {
            const backendUrl = getBackendUrl();
            const response = await fetch(
              `${backendUrl}/api/integrations/auth/oauth-callback/`,
              {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ code }),
              }
            );

            if (response.ok) {
              const userData = await response.json();
              console.log("✅ OAuth authentication successful:", userData);

              // Store user data
              if (userData.token) {
                localStorage.setItem("authToken", userData.token);
              }
              if (userData.user) {
                localStorage.setItem("user", JSON.stringify(userData.user));
              }

              // Send Slack notification for OAuth authentication
              try {
                await slackNotificationService.sendUserConnectionNotification({
                  userEmail: userData.user.email,
                  userName:
                    `${userData.user.first_name || ""} ${
                      userData.user.last_name || ""
                    }`.trim() || userData.user.email,
                  connectionType: "oauth",
                  timestamp: new Date().toISOString(),
                  userAgent: navigator.userAgent,
                });
              } catch (error) {
                console.error(
                  "❌ Failed to send Slack notification for OAuth:",
                  error
                );
              }

              // Redirect to home page
              router.push("/");
            } else {
              const errorData = await response.json();
              setError(errorData.message || "OAuth authentication failed");
            }
          } catch (err) {
            console.error("OAuth callback error:", err);
            setError("Failed to complete OAuth authentication");
          }
        } else {
          setError("No authentication token or code found in the URL");
        }
      } catch (err) {
        console.error("Error in auth callback:", err);
        setError("An unexpected error occurred during authentication");
      }
    };

    handleCallback();
  }, [router]);

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (newPassword.length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }

    setIsUpdatingPassword(true);
    setError(null);

    try {
      const url = new URL(window.location.href);
      const token =
        url.searchParams.get("token") || url.searchParams.get("code");

      if (!token) {
        setError("No reset token found");
        return;
      }

      const backendUrl = getBackendUrl();
      const response = await fetch(
        `${backendUrl}/api/integrations/auth/reset-password/`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            token,
            new_password: newPassword,
          }),
        }
      );

      if (response.ok) {
        // Password updated successfully
        console.log("✅ Password updated successfully");
        router.push("/login?message=password-updated");
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Failed to update password");
      }
    } catch (err) {
      console.error("Password update error:", err);
      setError("An unexpected error occurred while updating your password");
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  // Password reset form - render this first to prevent redirect
  if (isPasswordReset) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Reset Your Password
            </h1>
            <p className="mt-2 text-gray-600">Enter your new password below</p>
          </div>

          <form onSubmit={handlePasswordUpdate} className="space-y-4">
            <div>
              <label
                htmlFor="newPassword"
                className="block text-sm font-medium text-gray-700"
              >
                New Password
              </label>
              <input
                type="password"
                id="newPassword"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your new password"
                required
                minLength={6}
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700"
              >
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Confirm your new password"
                required
                minLength={6}
              />
            </div>

            {error && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={isUpdatingPassword}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdatingPassword ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Updating Password...
                </>
              ) : (
                "Update Password"
              )}
            </button>
          </form>

          <div className="mt-4 text-center">
            <button
              onClick={() => router.push("/login")}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center max-w-md p-6 bg-white rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-red-600">
            Authentication Error
          </h1>
          <p className="mt-2 text-gray-700">{error}</p>
          <button
            onClick={() => router.push("/login")}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Authenticating...</h1>
        <p className="mt-2">You will be redirected shortly.</p>
        <div className="mt-4 animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
      </div>
    </div>
  );
}
