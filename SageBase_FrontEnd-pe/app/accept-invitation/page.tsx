"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getBackendUrl } from "@/lib/api-config";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  Clock,
  Building,
  User,
  Mail,
  Calendar,
  Shield,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface InvitationData {
  email: string;
  companyName: string;
  role: string;
  inviterName: string;
  expiresAt: string;
}

interface AcceptInvitationResponse {
  user_id: string;
  company_id: string;
}

export default function AcceptInvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [accepting, setAccepting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [expired, setExpired] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [password, setPassword] = useState("");

  const BACKEND_BASE_URL = getBackendUrl();

  useEffect(() => {
    if (!token) {
      setError("No invitation token provided");
      setLoading(false);
      return;
    }

    fetchInvitationDetails();
  }, [token]);

  const fetchInvitationDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = `${BACKEND_BASE_URL}/api/integrations/invitation/${token}/`;
      console.log("🔍 Fetching invitation details:");
      console.log("  - Token:", token);
      console.log("  - API URL:", apiUrl);
      console.log("  - Backend Base URL:", BACKEND_BASE_URL);

      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      console.log("📡 Response received:");
      console.log("  - Status:", response.status);
      console.log("  - Status Text:", response.statusText);
      console.log(
        "  - Headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        console.error("❌ HTTP Error Response:");
        console.error("  - Status:", response.status);
        console.error("  - Status Text:", response.statusText);

        if (response.status === 404) {
          setError("Invitation not found or has expired");
        } else {
          const errorData = await response.json();
          console.error("  - Error Response Body:", errorData);
          setError(errorData.error || "Failed to fetch invitation details");
        }
        return;
      }

      const responseText = await response.text();
      console.log("📄 Raw Response Text:", responseText);

      let result;
      try {
        result = JSON.parse(responseText);
        console.log("✅ Parsed JSON Response:", result);
      } catch (parseError) {
        console.error("❌ JSON Parse Error:", parseError);
        console.error("  - Raw Response:", responseText);
        setError("Invalid JSON response from server");
        return;
      }

      console.log("🔍 Response Analysis:");
      console.log("  - Success:", result.success);
      console.log("  - Has Data:", !!result.data);
      console.log("  - Data Type:", typeof result.data);
      console.log(
        "  - Data Keys:",
        result.data ? Object.keys(result.data) : "No data"
      );
      console.log(
        "  - Has Required Fields:",
        !!(
          result.data?.email &&
          result.data?.companyName &&
          result.data?.role &&
          result.data?.inviterName &&
          result.data?.expiresAt
        )
      );

      if (
        result.success &&
        result.data &&
        result.data.email &&
        result.data.companyName &&
        result.data.role &&
        result.data.inviterName &&
        result.data.expiresAt
      ) {
        console.log("✅ Valid invitation data found:", result.data);
        setInvitation(result.data);

        // Check if invitation is expired
        const expiresAt = new Date(result.data.expiresAt);
        const now = new Date();
        console.log("⏰ Expiration Check:");
        console.log("  - Expires At:", expiresAt);
        console.log("  - Now:", now);
        console.log("  - Is Expired:", expiresAt < now);

        if (expiresAt < now) {
          setExpired(true);
          setError("This invitation has expired");
        }
      } else {
        console.error("❌ Invalid Response Structure:");
        console.error(
          "  - Expected: { success: true, data: { email, companyName, role, inviterName, expiresAt } }"
        );
        console.error("  - Received:", result);
        setError("Invalid invitation data received");
      }
    } catch (err: any) {
      console.error("❌ Fetch Error:", err);
      console.error("  - Error Type:", typeof err);
      console.error("  - Error Message:", err.message);
      console.error("  - Error Stack:", err.stack);
      setError(err.message || "Failed to fetch invitation details");
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    if (!firstName.trim() || !lastName.trim() || !password.trim()) {
      setError("Please provide first name, last name, and password");
      return;
    }

    setAccepting(true);
    setError(null);
    try {
      console.log("🚀 Accepting invitation:");
      console.log("  - Token:", token);
      console.log(
        "  - API URL:",
        `${BACKEND_BASE_URL}/api/integrations/invitation/${token}/accept/`
      );
      console.log("  - First Name:", firstName);
      console.log("  - Last Name:", lastName);
      console.log("  - Password:", "[HIDDEN]");

      const apiUrl = `${BACKEND_BASE_URL}/api/integrations/invitation/${token}/accept/`;
      console.log("🚀 Accepting invitation:");
      console.log("  - Token:", token);
      console.log("  - API URL:", apiUrl);

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName: firstName,
          lastName: lastName,
          password: password,
        }),
      });

      console.log("📡 Accept Response received:");
      console.log("  - Status:", response.status);
      console.log("  - Status Text:", response.statusText);
      console.log(
        "  - Headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        console.error("❌ Accept HTTP Error Response:");
        console.error("  - Status:", response.status);
        console.error("  - Status Text:", response.statusText);

        const errorData = await response.json();
        console.error("  - Error Response Body:", errorData);
        throw new Error(errorData.error || "Failed to accept invitation");
      }

      const responseText = await response.text();
      console.log("📄 Accept Raw Response Text:", responseText);

      let result;
      try {
        result = JSON.parse(responseText);
        console.log("✅ Accept Parsed JSON Response:", result);
      } catch (parseError) {
        console.error("❌ Accept JSON Parse Error:", parseError);
        console.error("  - Raw Response:", responseText);
        throw new Error("Invalid JSON response from server");
      }

      console.log("🔍 Accept Response Analysis:");
      console.log("  - Success:", result.success);
      console.log("  - Message:", result.message);
      console.log("  - Has Data:", !!result.data);
      console.log(
        "  - Data Keys:",
        result.data ? Object.keys(result.data) : "No data"
      );

      if (result.success) {
        console.log("✅ Invitation accepted successfully!");
        setSuccess(true);

        // Redirect to login after a short delay
        setTimeout(() => {
          console.log("🔄 Redirecting to login page...");
          router.push("/login?message=invitation-accepted");
        }, 2000);
      } else {
        console.error("❌ Accept failed in response:");
        console.error("  - Success:", result.success);
        console.error("  - Message:", result.message);
        throw new Error(result.message || "Failed to accept invitation");
      }
    } catch (err: any) {
      console.error("❌ Accept Error:", err);
      console.error("  - Error Type:", typeof err);
      console.error("  - Error Message:", err.message);
      console.error("  - Error Stack:", err.stack);
      setError(err.message || "Failed to accept invitation");
    } finally {
      setAccepting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading invitation details...</p>
        </div>
      </div>
    );
  }

  if (error && !invitation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100 flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardHeader className="text-center">
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-xl text-red-600">
              Invitation Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button
              onClick={() => router.push("/login")}
              className="w-full"
              variant="outline"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!invitation) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
            <Building className="h-8 w-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl text-gray-900">
            Company Invitation
          </CardTitle>
          <p className="text-gray-600 mt-2">
            You've been invited to join a company workspace
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="bg-green-50 text-green-800 border-green-200">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Invitation accepted successfully! Redirecting to login...
              </AlertDescription>
            </Alert>
          )}

          {/* Company Info */}
          <div className="flex items-center gap-3">
            <Building className="h-5 w-5 text-gray-500" />
            <span className="font-medium text-gray-900">
              {invitation.companyName}
            </span>
          </div>

          {/* Inviter Info */}
          <div className="flex items-center gap-3">
            <User className="h-5 w-5 text-gray-500" />
            <span className="text-gray-700">
              Invited by: {invitation.inviterName}
            </span>
          </div>

          {/* Email Info */}
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-gray-500" />
            <span className="text-gray-700">Email: {invitation.email}</span>
          </div>

          {/* Role Info */}
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-gray-500" />
            <span className="text-gray-700">Role: {invitation.role}</span>
          </div>

          {/* Expiration Info */}
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-gray-500" />
            <span className="text-gray-700">
              Expires: {formatDate(invitation.expiresAt)}
            </span>
          </div>

          {/* Status Badge */}
          <div className="flex justify-center">
            <Badge
              variant={invitation.role === "pending" ? "default" : "secondary"}
              className="text-sm px-3 py-1"
            >
              <Clock className="h-3 w-3 mr-1" />
              {invitation.role.toUpperCase()}
            </Badge>
          </div>

          {/* Name Input Fields */}
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <div>
              <Label
                htmlFor="firstName"
                className="text-sm font-medium text-gray-700"
              >
                First Name *
              </Label>
              <Input
                id="firstName"
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="Enter your first name"
                className="mt-1"
                required
              />
            </div>
            <div>
              <Label
                htmlFor="lastName"
                className="text-sm font-medium text-gray-700"
              >
                Last Name *
              </Label>
              <Input
                id="lastName"
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Enter your last name"
                className="mt-1"
                required
              />
            </div>
            <div>
              <Label
                htmlFor="password"
                className="text-sm font-medium text-gray-700"
              >
                Password *
              </Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="mt-1"
                required
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {expired ? (
              <Button
                onClick={() => router.push("/login")}
                className="w-full"
                variant="outline"
              >
                Go to Login
              </Button>
            ) : (
              <>
                <Button
                  onClick={() => router.push("/login")}
                  className="flex-1"
                  variant="outline"
                >
                  Decline
                </Button>
                <Button
                  onClick={handleAcceptInvitation}
                  disabled={
                    accepting ||
                    !!success ||
                    !firstName ||
                    !lastName ||
                    !password
                  }
                  className="flex-1"
                >
                  {accepting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Accepting...
                    </>
                  ) : success ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Accepted!
                    </>
                  ) : (
                    "Accept Invitation"
                  )}
                </Button>
              </>
            )}
          </div>

          {/* Additional Info */}
          <div className="text-center text-sm text-gray-500">
            <p>
              By accepting this invitation, you'll be added to the company
              workspace and can start collaborating with your team.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
