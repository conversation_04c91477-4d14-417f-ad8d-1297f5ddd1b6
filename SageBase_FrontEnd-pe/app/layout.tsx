import type React from "react";
import type { Metadata } from "next";
import "./globals.css";
import { Inter } from "next/font/google";
import { AuthProvider } from "@/contexts/auth-context";
import { ConnectedPlatformsProvider } from "@/contexts/connected-platforms-context";
import { SidebarRefreshProvider } from "@/contexts/sidebar-refresh-context";
import { QAPageRefreshProvider } from "@/contexts/qa-page-refresh-context";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "v0 App",
  description: "Created with v0",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthProvider>
          <ConnectedPlatformsProvider>
            <SidebarRefreshProvider>
              <QAPageRefreshProvider>
                <ThemeProvider
                  attribute="class"
                  defaultTheme="system"
                  enableSystem
                  disableTransitionOnChange
                >
                  <Toaster />
                  {children}
                </ThemeProvider>
              </QAPageRefreshProvider>
            </SidebarRefreshProvider>
          </ConnectedPlatformsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
