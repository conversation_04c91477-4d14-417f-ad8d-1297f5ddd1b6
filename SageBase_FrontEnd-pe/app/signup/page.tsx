"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"

export default function SignupPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [isGuestLoading, setIsGuestLoading] = useState(false)
  const { signUp, signInWithGoogle, enterGuestMode } = useAuth()
  const router = useRouter()

  // Check if Supa<PERSON> is configured
  const hasSupabaseConfig = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    // Validate passwords match
    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return
    }

    // Validate password strength
    if (password.length < 8) {
      setError("Password must be at least 8 characters long")
      return
    }

    setIsLoading(true)

    try {
      await signUp(email, password)
      // Navigation is handled by the auth context
    } catch (err: any) {
      console.error("Signup error:", err)
      setError(err.message || "Failed to create account. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true)
    setError("")

    try {
      await signInWithGoogle()
      // No need to redirect here as it's handled by the auth context
    } catch (err: any) {
      console.error("Google sign in error:", err)
      setError(err.message || "Failed to sign up with Google.")
    } finally {
      setIsGoogleLoading(false)
    }
  }

  const handleGuestMode = () => {
    setIsGuestLoading(true)
    setError("")

    try {
      // Use the auth context's enterGuestMode function
      enterGuestMode()

      // Force a hard navigation to the root page
      window.location.href = "/"
    } catch (err) {
      console.error("Error entering guest mode:", err)
      setError("Failed to enter guest mode. Please try again.")
    } finally {
      setIsGuestLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div className="flex flex-col items-center">
            <Image
              src="/images/stacklens-logo-gradient.svg"
              alt="SageBase Logo"
              width={220}
              height={50}
              className="mb-8"
              priority
            />
            <h2 className="text-2xl font-bold leading-9 tracking-tight text-gray-900">Create your account</h2>
            <p className="mt-2 text-sm text-gray-600">
              Or{" "}
              <Link href="/login" className="font-medium text-primary hover:text-primary-500">
                sign in to your existing account
              </Link>
            </p>
          </div>

          <div className="mt-10">
            {/* Demo mode notice */}
            {!hasSupabaseConfig && (
              <div className="mb-4 text-sm text-blue-600 bg-blue-50 p-3 rounded-md">
                <strong>Demo Mode:</strong> Email signup is not available. Use "Continue as guest" to explore the app.
              </div>
            )}

            {error && <div className="mb-4 text-sm text-red-600 bg-red-50 p-3 rounded-md">{error}</div>}

            {/* Only show email signup form if Supabase is configured */}
            {hasSupabaseConfig && (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <Label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
                    Email address
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="px-4 py-1.5"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900">
                    Password
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="px-4 py-1.5"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">Must be at least 8 characters long</p>
                </div>

                <div>
                  <Label htmlFor="confirmPassword" className="block text-sm font-medium leading-6 text-gray-900">
                    Confirm Password
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="px-4 py-1.5"
                    />
                  </div>
                </div>

                <div>
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Creating account..." : "Create account"}
                  </Button>
                </div>
              </form>
            )}

            <div className={hasSupabaseConfig ? "mt-6" : ""}>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-gray-50 px-2 text-gray-500">
                    {hasSupabaseConfig ? "Or continue with" : "Get started with"}
                  </span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-1 gap-3">
                {hasSupabaseConfig && (
                  <Button
                    variant="outline"
                    type="button"
                    onClick={handleGoogleSignIn}
                    disabled={isGoogleLoading}
                    className="flex w-full items-center justify-center gap-3"
                  >
                    <Image src="/images/google-logo.png" alt="Google logo" width={18} height={18} />
                    <span>{isGoogleLoading ? "Signing up..." : "Sign up with Google"}</span>
                  </Button>
                )}

                <Button
                  variant="outline"
                  type="button"
                  onClick={handleGuestMode}
                  disabled={isGuestLoading}
                  className="flex w-full items-center justify-center"
                >
                  {isGuestLoading ? "Loading..." : "Continue as guest"}
                </Button>
              </div>
            </div>

            {/* Terms and Privacy */}
            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                By creating an account, you agree to our{" "}
                <Link href="/terms" className="text-primary hover:text-primary-600">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy" className="text-primary hover:text-primary-600">
                  Privacy Policy
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="relative hidden w-0 flex-1 lg:block">
        <Image
          src="/images/login-background.png"
          alt="SageBase platform integrations"
          fill
          priority
          className="object-cover"
          sizes="(max-width: 1024px) 0vw, 50vw"
        />
      </div>
    </div>
  )
}
