"use client";

import React from "react";

import "../animated-gradient.css";
import { useAuth } from "@/contexts/auth-context";
import { useEffect, useState } from "react";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import SimplifiedSearch from "@/components/simplified-search";
import { useRouter } from "next/navigation";
import QuestionsSection from "@/components/questions-section";
import AuthGuard from "@/components/auth-guard";


export default function AISearchPage() {
  return (
    <AuthGuard requireAuth={true}>
      <AISearchContent />
    </AuthGuard>
  );
}

function AISearchContent() {
  const { user, isLoading, isGuest } = useAuth();

  // If still loading, show a loading indicator
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If authenticated or in guest mode, show the AI search
  return (
    <div className="flex h-screen bg-gray-50 animated-gradient-background">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Guest Mode Banner (only show if in guest mode) */}
        {isGuest && (
          <div className="bg-amber-50 px-4 py-2 text-center hidden">
            <p className="text-amber-800 text-sm">
              You are browsing as a guest. Some features may be limited.{" "}
              <a
                href="/login"
                className="font-medium underline hover:text-amber-900"
              >
                Sign in
              </a>{" "}
              for full access.
            </p>
          </div>
        )}

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-6">
          <SimplifiedSearch />
        </main>
      </div>
    </div>
  );
}
