"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useUserInfo } from "@/hooks/use-user-info";

const BACKEND_BASE_URL =
  process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

export default function HomePage() {
  const router = useRouter();
  const {
    user,
    isLoading,
    connectedUserCompany,
    setConnectedUserCompany,
    setUserRole,
    setBackendUserId,
  } = useAuth();

  // Use the new user info hook with caching
  const { userInfo } = useUserInfo(user?.email);

  useEffect(() => {
    if (isLoading) return; // Wait for auth to load

    if (user) {
      // User is authenticated, redirect to AI search
      console.log("Authenticated user info:", user);
      router.push("/ai-search");
    } else {
      // User is not authenticated, redirect to login
      router.push("/login");
    }
  }, [user, isLoading, router]);

  // Update user info when it's fetched from the hook
  useEffect(() => {
    if (userInfo) {
      console.log("User and company info from backend:", userInfo);
      console.log("🏢 Setting company_id to:", userInfo.company);
      console.log("🏢 Company name:", userInfo.company_name);
      console.log("🏢 Full userInfo object keys:", Object.keys(userInfo));

      // Use the company field directly since it's already the UUID
      const companyId = userInfo.company;
      console.log("🏢 Using company_id:", companyId);
      console.log("🏢 userInfo.company:", userInfo.company);

      // Check if the company data is actually different to prevent infinite loops
      const newCompanyData = {
        company_id: companyId,
        company_name: userInfo.company_name,
      };

      const currentCompanyData = connectedUserCompany;
      const isDifferent =
        !currentCompanyData ||
        currentCompanyData.company_id !== newCompanyData.company_id ||
        currentCompanyData.company_name !== newCompanyData.company_name;

      if (isDifferent) {
        console.log("🏢 Company data changed - updating");
        setConnectedUserCompany(newCompanyData);
        setUserRole(userInfo.role || null);
        setBackendUserId(userInfo.id || null);
      } else {
        console.log("🏢 Company data unchanged - skipping update");
      }
    }
  }, [
    userInfo,
    connectedUserCompany,
    setConnectedUserCompany,
    setUserRole,
    setBackendUserId,
  ]);

  useEffect(() => {
    if (connectedUserCompany) {
      console.log("connectedUserCompany updated: ", connectedUserCompany);
    }
  }, [connectedUserCompany]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
