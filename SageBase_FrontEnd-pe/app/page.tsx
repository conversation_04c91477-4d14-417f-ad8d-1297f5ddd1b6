"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useUserInfo } from "@/hooks/use-user-info";
import { getBackendUrl } from "@/lib/api-config";

const BACKEND_BASE_URL = getBackendUrl();

export default function HomePage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  // Use the new user info hook with caching
  const { userInfo } = useUserInfo(user?.email);

  useEffect(() => {
    if (isLoading) return; // Wait for auth to load

    if (user) {
      // User is authenticated, redirect to AI search
      console.log("Authenticated user info:", user);
      router.push("/ai-search");
    } else {
      // User is not authenticated, redirect to login
      router.push("/login");
    }
  }, [user, isLoading, router]);

  // Log user info when it's fetched from the hook
  useEffect(() => {
    if (userInfo) {
      console.log("User and company info from backend:", userInfo);
      console.log("🏢 Company ID:", userInfo.company);
      console.log("🏢 Company name:", userInfo.company_name);
      console.log("🏢 User role:", userInfo.role);
      console.log("🏢 Full userInfo object keys:", Object.keys(userInfo));
    }
  }, [userInfo]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
