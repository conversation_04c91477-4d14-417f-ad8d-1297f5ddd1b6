"use client"
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";

// Simple toast notification function
const showToast = (message: string, type: 'success' | 'error' = 'error') => {
  // Create toast element
  const toast = document.createElement('div');
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg ${
    type === 'error' ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
  }`;
  toast.textContent = message;
  
  // Add to page
  document.body.appendChild(toast);
  
  // Remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 5000);
};

export default function DiscordCallbackPage() {
  const { connectPlatform } = useConnectedPlatforms();

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const guild_id = params.get("guild_id");
    const success = params.get("success");
    const error = params.get("error");

    if (error) {
      // User cancelled or there was an error
      console.error("Discord OAuth error:", error);
      const errorMessage = error || "Discord connection was cancelled";
      showToast(`Discord connection failed: ${errorMessage}`, 'error');
      return;
    }

    if (success === "true" && guild_id) {
      connectPlatform("discord", { guild_id });
      showToast("Discord connected successfully!", 'success');
    } else {
      showToast("Failed to connect Discord.", 'error');
    }
  }, [connectPlatform]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 text-center">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-green-600">Discord Connection Complete!</h1>
        <p className="text-gray-600 mb-6">
          Your Discord integration has been processed. You can now close this window and refresh your main page to see the updated connection status.
        </p>
        <div className="space-y-3">
          <button 
            onClick={() => window.close()} 
            className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Close Window
          </button>
          <p className="text-sm text-gray-500">
            If the window doesn't close automatically, you can close it manually and refresh your main page.
          </p>
        </div>
      </div>
    </div>
  );
} 