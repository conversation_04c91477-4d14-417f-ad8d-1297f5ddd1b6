"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { useAuth } from "@/contexts/auth-context";

// Simple toast notification function
const showToast = (message: string, type: "success" | "error" = "error") => {
  // Create toast element
  const toast = document.createElement("div");
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg ${
    type === "error" ? "bg-red-500 text-white" : "bg-green-500 text-white"
  }`;
  toast.textContent = message;

  // Add to page
  document.body.appendChild(toast);

  // Remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 5000);
};

export default function SlackCallbackPage() {
  const router = useRouter();
  const { connectPlatform } = useConnectedPlatforms();
  const { user } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      const params = new URLSearchParams(window.location.search);
      const token = params.get("token");
      const error = params.get("error");
      const errorDescription = params.get("error_description");

      if (error) {
        const errorMessage =
          errorDescription || "Slack connection was cancelled";
        showToast(`Slack connection failed: ${errorMessage}`, "error");
        router.push("/ai-search");
        return;
      }

      if (token) {
        let slackData;
        try {
          slackData = JSON.parse(token);
        } catch (e) {
          showToast("Invalid Slack token data.", "error");
          router.push("/ai-search");
          return;
        }
        // 1. Call connectPlatform (company-level)
        await connectPlatform("slack", {
          slack_user_id: slackData.slack_user_id,
          bot_token: slackData.slack_bot_token,
          user_token: slackData.slack_user_token,
        });
        // 2. Call user profile API (user-level)
        if (user?.email) {
          fetch(
            `${process.env.NEXT_PUBLIC_BACKEND_NGROK_BASE_URL}/api/integrations/slack/save-slack-user-profile/`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                email: user.email,
                slack_user_id: slackData.slack_user_id,
                bot_token: slackData.slack_bot_token,
                user_token: slackData.slack_user_token,
              }),
            }
          )
            .then(async (res) => {
              if (!res.ok) {
                const data = await res.json();
                throw new Error(data.error || "Failed to save Slack profile");
              }
              // Optionally show a toast or log success
            })
            .catch((err) => {
              // Optionally show a toast or log error
            });
        }
        showToast("Slack connected successfully!", "success");

        // Notify other tabs about the successful connection
        localStorage.setItem("slack-connected", Date.now().toString());
        // Dispatch custom event for immediate notification
        window.dispatchEvent(
          new CustomEvent("platform-connected", {
            detail: { platform: "slack" },
          })
        );

        router.push("/ai-search");
      } else {
        showToast("Failed to receive Slack token.", "error");
        router.push("/ai-search");
      }
    };

    handleCallback();
  }, [connectPlatform, router, user]);

  return <p>Finishing Slack connection...</p>;
}
