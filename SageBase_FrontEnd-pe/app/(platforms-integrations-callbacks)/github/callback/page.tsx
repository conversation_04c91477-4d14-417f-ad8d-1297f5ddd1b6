"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";

// Simple toast notification function
const showToast = (message: string, type: "success" | "error" = "error") => {
  // Create toast element
  const toast = document.createElement("div");
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg ${
    type === "error" ? "bg-red-500 text-white" : "bg-green-500 text-white"
  }`;
  toast.textContent = message;

  // Add to page
  document.body.appendChild(toast);

  // Remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 5000);
};

export default function GitHubCallbackPage() {
  const { connectPlatform, refreshPlatforms } = useConnectedPlatforms();
  const router = useRouter();
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [message, setMessage] = useState("");

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const installation_id = params.get("installation_id");
    const ghu_token = params.get("token");
    const ghr_token = params.get("refresh_token");
    const error = params.get("error");
    const errorDescription = params.get("error_description");

    if (error) {
      setStatus("error");
      setMessage(`GitHub connection failed: ${errorDescription || error}`);
      showToast(
        `GitHub connection failed: ${errorDescription || error}`,
        "error"
      );
      setTimeout(() => {
        router.push("/ai-search");
      }, 3000);
      return;
    }

    if (installation_id && ghu_token && ghr_token) {
      // Backend has exchanged the code for tokens, now save to database with user's company
      connectPlatform("github", {
        installation_id,
        ghu_token,
        ghr_token,
      })
        .then(async () => {
          setStatus("success");
          setMessage("GitHub connected successfully!");
          showToast("GitHub connected successfully!", "success");

          // Refresh the connected platforms list
          try {
            await refreshPlatforms();
            console.log("✅ Platforms refreshed after GitHub connection");
          } catch (error) {
            console.warn("⚠️ Failed to refresh platforms:", error);
          }

          // Notify other tabs about the successful connection
          setTimeout(() => {
            localStorage.setItem("github-connected", Date.now().toString());
            // Dispatch custom event for immediate notification
            window.dispatchEvent(
              new CustomEvent("platform-connected", {
                detail: { platform: "github" },
              })
            );
            // Try to close this tab after a delay
            setTimeout(() => {
              window.close();
            }, 2000);
          }, 1000);
        })
        .catch((e) => {
          setStatus("error");
          setMessage("Failed to connect GitHub: " + (e?.message || e));
          showToast("Failed to connect GitHub: " + (e?.message || e), "error");
          setTimeout(() => {
            router.push("/ai-search");
          }, 3000);
        });
    } else {
      setStatus("error");
      setMessage("Failed to receive GitHub tokens or installation ID.");
      showToast("Failed to receive GitHub tokens or installation ID.", "error");
      setTimeout(() => {
        router.push("/ai-search");
      }, 3000);
    }
  }, [connectPlatform, router]);

  if (status === "loading") {
    return (
      <div className="flex flex-col items-center justify-center h-screen text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
        <p>Processing GitHub connection...</p>
      </div>
    );
  }

  if (status === "error") {
    return (
      <div className="flex flex-col items-center justify-center h-screen text-center">
        <div className="text-red-500 text-6xl mb-4">❌</div>
        <h1 className="text-xl font-bold mb-2">Connection Failed</h1>
        <p className="mb-4 text-gray-600">{message}</p>
        <p className="text-sm text-gray-500">Redirecting to main page...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center h-screen text-center">
      <div className="text-green-500 text-6xl mb-4">✅</div>
      <h1 className="text-xl font-bold mb-2">GitHub Connected!</h1>
      <p className="mb-4 text-gray-600">{message}</p>
      <p className="mb-2 text-sm text-gray-500">
        You can now close this tab and return to the main SageBase window.
      </p>
      <p className="text-xs text-gray-400">
        This window will close automatically in a few seconds...
      </p>
    </div>
  );
}
