"use client";
import { useEffect, useMemo, useState } from "react";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { useAuth } from "@/contexts/auth-context";

// Simple toast notification function
const showToast = (message: string, type: "success" | "error" = "error") => {
  // Create toast element
  const toast = document.createElement("div");
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg ${
    type === "error" ? "bg-red-500 text-white" : "bg-green-500 text-white"
  }`;
  toast.textContent = message;

  // Add to page
  document.body.appendChild(toast);

  // Remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 5000);
};

export default function ConfluenceCallbackPage() {
  const { refreshPlatforms } = useConnectedPlatforms();
  const { user } = useAuth();
  const [status, setStatus] = useState<"idle" | "success" | "error">("idle");
  const [message, setMessage] = useState<string>("Finishing Confluence connection...");
  const searchParams = useMemo(() => (typeof window !== "undefined" ? new URLSearchParams(window.location.search) : null), []);

  useEffect(() => {
    const handleCallback = async () => {
      if (!searchParams) return;
      const success = searchParams.get("success");
      const error = searchParams.get("error");
      const errorDescription = searchParams.get("error_description");

      if (success !== "true") {
        const errorMessage = errorDescription || error || "Confluence connection was cancelled";
        setStatus("error");
        setMessage(errorMessage);
        showToast(`Confluence connection failed: ${errorMessage}`, "error");
        return;
      }

      try {
        // Refresh platforms to get updated connection status
        await refreshPlatforms();

        // Notifications for other tabs
        try {
          localStorage.setItem("confluence-connected", Date.now().toString());
          window.dispatchEvent(new CustomEvent("platform-connected", { detail: { platform: "confluence" } }));
        } catch {}

        setStatus("success");
        setMessage("Confluence authorization complete. You can close this window.");
        showToast("Confluence connected successfully!", "success");
      } catch (e: any) {
        setStatus("error");
        setMessage(e?.message || "Confluence connection failed.");
        showToast(e?.message || "Confluence connection failed.", "error");
      }
    };

    handleCallback();
  }, [refreshPlatforms, searchParams]);

  return (
    <div className="min-h-screen w-full flex items-center justify-center p-6">
      <div className="w-full max-w-md rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        {status === "success" && (
          <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">Confluence authorization complete</h1>
            <p className="text-gray-600 mb-4">
              Close this window. You'll be notified when your documents are ready.
            </p>
            <button
              onClick={() => window.close()}
              className="inline-flex items-center px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700"
            >
              Close window
            </button>
          </div>
        )}

        {status === "error" && (
          <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">Confluence authorization failed</h1>
            <p className="text-red-600 mb-4">{message}</p>
            <button
              onClick={() => window.close()}
              className="inline-flex items-center px-4 py-2 rounded-md bg-gray-700 text-white hover:bg-gray-800"
            >
              Close window
            </button>
          </div>
        )}

        {status === "idle" && (
          <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">Finishing Confluence connection…</h1>
            <p className="text-gray-600">Please wait.</p>
          </div>
        )}
      </div>
    </div>
  );
}
