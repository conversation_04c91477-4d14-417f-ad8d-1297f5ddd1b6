"use client";
import { useAuth } from "@/contexts/auth-context";
import { clearUserInfoCache } from "@/hooks/use-user-info";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";

export default function DebugCachePage() {
  const { clearCachedCompanyData, connectedUserCompany } = useAuth();
  const [localStorageData, setLocalStorageData] = useState<{
    connectedUserCompany: string | null;
    userRole: string | null;
    backendUserId: string | null;
  }>({
    connectedUserCompany: null,
    userRole: null,
    backendUserId: null,
  });

  // Load localStorage data only on client side
  useEffect(() => {
    if (typeof window !== "undefined") {
      setLocalStorageData({
        connectedUserCompany: localStorage.getItem("connectedUserCompany"),
        userRole: localStorage.getItem("userRole"),
        backendUserId: localStorage.getItem("backendUserId"),
      });
    }
  }, []);

  const clearAllCache = () => {
    // Clear user info cache
    clearUserInfoCache();

    // Clear auth context cache
    clearCachedCompanyData();

    // Clear localStorage manually (only on client side)
    if (typeof window !== "undefined") {
      localStorage.removeItem("connectedUserCompany");
      localStorage.removeItem("userRole");
      localStorage.removeItem("backendUserId");
      localStorage.removeItem("<EMAIL>");
    }

    console.log("🗑️ All cache cleared!");
    alert("Cache cleared! Please refresh the page.");

    // Refresh localStorage data display
    if (typeof window !== "undefined") {
      setLocalStorageData({
        connectedUserCompany: localStorage.getItem("connectedUserCompany"),
        userRole: localStorage.getItem("userRole"),
        backendUserId: localStorage.getItem("backendUserId"),
      });
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Cache</h1>

      <div className="space-y-4">
        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">Current Company Data</h2>
          <pre className="bg-gray-100 p-2 rounded text-sm">
            {JSON.stringify(connectedUserCompany, null, 2)}
          </pre>
        </div>

        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">LocalStorage Data</h2>
          <div className="space-y-2">
            <div>
              <strong>connectedUserCompany:</strong>{" "}
              {localStorageData.connectedUserCompany || "null"}
            </div>
            <div>
              <strong>userRole:</strong> {localStorageData.userRole || "null"}
            </div>
            <div>
              <strong>backendUserId:</strong>{" "}
              {localStorageData.backendUserId || "null"}
            </div>
          </div>
        </div>

        <Button onClick={clearAllCache} className="bg-red-500 hover:bg-red-600">
          Clear All Cache
        </Button>

        <div className="p-4 bg-yellow-100 rounded">
          <h3 className="font-semibold mb-2">Expected Company ID</h3>
          <p className="text-sm">553717d5-c08e-4f00-98c3-0bb62eeffab4</p>
        </div>

        <div className="p-4 bg-red-100 rounded">
          <h3 className="font-semibold mb-2">Wrong Company ID (if present)</h3>
          <p className="text-sm">01b2fbea-774e-45d0-a0d5-04bd21586966</p>
        </div>
      </div>
    </div>
  );
}
