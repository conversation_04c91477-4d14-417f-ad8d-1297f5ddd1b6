"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { clearUserInfoCache } from "@/hooks/use-user-info";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Users } from "lucide-react";

const BACKEND_BASE_URL =
  process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

export default function UsersPage() {
  const { user, userRole, connectedUserCompany, backendUserId } = useAuth();
  const [email, setEmail] = useState("");
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [roleLoadingId, setRoleLoadingId] = useState<string | null>(null);
  const [changeRoleModal, setChangeRoleModal] = useState<{
    open: boolean;
    user: any | null;
  }>({ open: false, user: null });
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [search, setSearch] = useState("");

  // Redirect non-admin users
  useEffect(() => {
    if (userRole && userRole !== "ADMIN") {
      window.location.href = "/dashboard";
    }
  }, [userRole]);

  // Fetch users for this company on mount or when company_id changes
  useEffect(() => {
    async function fetchUsers() {
      setLoading(true);
      setError(null);
      try {
        const companyId = connectedUserCompany?.company_id;
        if (!companyId)
          throw new Error("No company ID found for connected user");

        // Get Supabase session for authorization
        const supabase = createClientComponentClient();
        const {
          data: { session },
        } = await supabase.auth.getSession();

        const res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/company-users/?company_id=${companyId}`,
          {
            headers: {
              Authorization: `Bearer ${session?.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );
        if (!res.ok) throw new Error("Failed to fetch users");
        const data = await res.json();
        setUsers(data);
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    }
    if (connectedUserCompany?.company_id && userRole === "ADMIN") fetchUsers();
  }, [connectedUserCompany?.company_id, userRole]);

  const handleAddUser = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      // Get Supabase session for authorization
      const supabase = createClientComponentClient();
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error("No valid session found. Please log in again.");
      }

      // Use the new invitation API
      const res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/admin/invite-team-member-supabase/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ email }),
        }
      );

      const result = await res.json();

      if (!res.ok) {
        setError(result.error || "Failed to send invitation");
        setLoading(false);
        return;
      }

      // Success - user will receive email automatically
      setSuccess(
        `Invitation sent to ${email}! They will receive login instructions via email.`
      );
      setEmail("");

      // Refresh the user list to show new user
      const companyId = connectedUserCompany?.company_id;
      if (companyId) {
        const usersRes = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/company-users/?company_id=${companyId}`,
          {
            headers: {
              Authorization: `Bearer ${session.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );
        if (usersRes.ok) {
          const userData = await usersRes.json();
          setUsers(userData);
        }
      }

      // Optional: Log invitation details for debugging
      console.log("Invitation details:", result.data?.invitation);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleKickout = async (userId: string) => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      // Find the user to be removed to get their email
      const userToRemove = users.find(u => u.id === userId);
      if (!userToRemove?.email) {
        throw new Error("User email not found");
      }

      console.log("🗑️ Removing user:", {
        userId,
        userToRemoveEmail: userToRemove.email,
        actingUserEmail: user?.email
      });

      // Get Supabase session for authorization
      const supabase = createClientComponentClient();
      const {
        data: { session },
      } = await supabase.auth.getSession();

      const requestBody = {
        email: userToRemove.email,
      };

      console.log("🌐 DELETE request:", {
        url: `${BACKEND_BASE_URL}/api/integrations/company-users/`,
        body: requestBody
      });

      const res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-users/`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${session?.access_token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );
      if (!res.ok) throw new Error("Failed to kick out user");
      setUsers((prev) => prev.filter((u) => u.id !== userId));
      setSuccess("User kicked out and deleted successfully");
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeRole = async (userId: string, newRole: string) => {
    setRoleLoadingId(userId);
    setError(null);
    setSuccess(null);
    try {
      // Get Supabase session for authorization
      const supabase = createClientComponentClient();
      const {
        data: { session },
      } = await supabase.auth.getSession();

      const res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-users/${userId}/`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.access_token}`,
          },
          body: JSON.stringify({
            role: newRole,
            acting_user_email: user?.email,
          }),
        }
      );
      if (!res.ok) throw new Error("Failed to change user role");

      // Find the user whose role was changed and clear their cache
      const changedUser = users.find((u) => u.id === userId);
      if (changedUser?.email) {
        console.log("🗑️ Clearing cache for user:", changedUser.email);
        clearUserInfoCache();
      }

      setUsers((prev) =>
        prev.map((u) => (u.id === userId ? { ...u, role: newRole } : u))
      );
      setSuccess("User role updated successfully");
    } catch (e: any) {
      setError(e.message);
    } finally {
      setRoleLoadingId(null);
    }
  };

  const handleChangeRoleModal = (user: any) => {
    setChangeRoleModal({ open: true, user });
    setSelectedRole(user.role);
  };

  const handleConfirmChangeRole = async () => {
    if (
      !changeRoleModal.user ||
      !selectedRole ||
      selectedRole === changeRoleModal.user.role
    )
      return;
    await handleChangeRole(changeRoleModal.user.id, selectedRole);
    setChangeRoleModal({ open: false, user: null });
  };

  // Only show other users in the list (not the current admin)
  const otherUsers = users.filter(
    (u) => String(u.id) !== String(backendUserId)
  );

  // Split users into admins and users
  const filteredUsers = otherUsers.filter((u) =>
    u.email.toLowerCase().includes(search.toLowerCase())
  );
  const admins = filteredUsers.filter((u) => u.role === "ADMIN");
  const normalUsers = filteredUsers.filter((u) => u.role !== "ADMIN");

  // Show loading or redirect message for non-admin users
  if (userRole && userRole !== "ADMIN") {
    return (
      <div className="flex h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Access Denied
            </h1>
            <p className="text-gray-600 mb-4">
              Only administrators can access this page.
            </p>
            <p className="text-sm text-gray-500">Redirecting to dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100">
      {/* Left Sidebar */}
      <SideNavigation />
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />
        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-8">
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center mb-2">
              <Users className="h-8 w-8 text-primary-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">
                User Management
              </h1>
            </div>
            <p className="text-gray-600 mb-8 text-base">
              Invite, manage, and control access for your company's users. Only
              admins can add, remove, or change user roles.
            </p>

            <Card className="mb-8 shadow-md border border-gray-100">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-800">
                  Invite a New User
                </CardTitle>
              </CardHeader>
              <CardContent>
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                {success && (
                  <Alert className="mb-4 bg-primary-50 text-primary-800 border-primary-200">
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}
                <div className="flex gap-4 items-end">
                  <div className="flex-1">
                    <Label
                      htmlFor="email"
                      className="text-sm font-medium text-gray-700"
                    >
                      User Email
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={loading}
                      className="mt-1"
                    />
                  </div>
                  <Button
                    onClick={handleAddUser}
                    disabled={!email || loading}
                    className="h-10 mt-6 w-36"
                  >
                    {loading ? "Inviting..." : "Invite User"}
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Invited users will receive an email with instructions to join
                  your company workspace.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-md border border-gray-100">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-800">
                  Current Company Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex items-center gap-2">
                  <Input
                    type="text"
                    placeholder="Search users by email..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="w-full max-w-xs"
                    autoComplete="off"
                    name="user-search"
                    id="user-search"
                  />
                </div>
                {loading ? (
                  <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : admins.length === 0 && normalUsers.length === 0 ? (
                  <div className="text-gray-500 py-8 text-center">
                    <div className="mb-2">No users found for this company.</div>
                    <div className="text-xs text-gray-400">
                      Invite your teammates to get started.
                    </div>
                  </div>
                ) : (
                  <div>
                    {admins.length > 0 && (
                      <div className="mb-2">
                        <div className="text-xs font-semibold text-gray-500 uppercase mb-1 tracking-wider">
                          Admins
                        </div>
                        <ul className="divide-y divide-gray-200">
                          {admins.map((u) => (
                            <li
                              key={u.id}
                              className="py-4 flex items-center justify-between"
                            >
                              <div className="flex items-center gap-3 min-w-0 flex-1">
                                <span className="truncate text-gray-800 text-base font-medium">
                                  {u.email}
                                </span>
                              </div>
                              <div className="w-28 flex-shrink-0 flex items-center justify-center">
                                <span className="text-xs font-semibold px-3 py-1 rounded-full bg-gray-100 text-gray-700 border border-gray-200">
                                  {u.role}
                                </span>
                              </div>
                              <div className="flex gap-2">
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button variant="destructive" size="sm">
                                      Remove
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        Remove User?
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        This will{" "}
                                        <span className="font-bold text-red-600">
                                          permanently delete
                                        </span>{" "}
                                        the user's profile and revoke their
                                        access to this company. This action
                                        cannot be undone.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel asChild>
                                        <Button variant="outline" size="sm">
                                          Cancel
                                        </Button>
                                      </AlertDialogCancel>
                                      <AlertDialogAction asChild>
                                        <Button
                                          variant="destructive"
                                          size="sm"
                                          onClick={() => handleKickout(u.id)}
                                          disabled={loading}
                                        >
                                          {loading
                                            ? "Removing..."
                                            : "Yes, Remove"}
                                        </Button>
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  onClick={() => handleChangeRoleModal(u)}
                                  disabled={roleLoadingId === u.id}
                                >
                                  Change Role
                                </Button>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {admins.length > 0 && normalUsers.length > 0 && (
                      <hr className="my-6 border-gray-200" />
                    )}
                    {normalUsers.length > 0 && (
                      <div>
                        <div className="text-xs font-semibold text-gray-500 uppercase mb-1 tracking-wider">
                          Users
                        </div>
                        <ul className="divide-y divide-gray-200">
                          {normalUsers.map((u) => (
                            <li
                              key={u.id}
                              className="py-4 flex items-center justify-between"
                            >
                              <div className="flex items-center gap-3 min-w-0 flex-1">
                                <span className="truncate text-gray-800 text-base font-medium">
                                  {u.email}
                                </span>
                              </div>
                              <div className="w-28 flex-shrink-0 flex items-center justify-center">
                                <span className="text-xs font-semibold px-3 py-1 rounded-full bg-gray-100 text-gray-700 border border-gray-200">
                                  {u.role}
                                </span>
                              </div>
                              <div className="flex gap-2">
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button variant="destructive" size="sm">
                                      Remove
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        Remove User?
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        This will{" "}
                                        <span className="font-bold text-red-600">
                                          permanently delete
                                        </span>{" "}
                                        the user's profile and revoke their
                                        access to this company. This action
                                        cannot be undone.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel asChild>
                                        <Button variant="outline" size="sm">
                                          Cancel
                                        </Button>
                                      </AlertDialogCancel>
                                      <AlertDialogAction asChild>
                                        <Button
                                          variant="destructive"
                                          size="sm"
                                          onClick={() => handleKickout(u.id)}
                                          disabled={loading}
                                        >
                                          {loading
                                            ? "Removing..."
                                            : "Yes, Remove"}
                                        </Button>
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  onClick={() => handleChangeRoleModal(u)}
                                  disabled={roleLoadingId === u.id}
                                >
                                  Change Role
                                </Button>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>

      {/* Change Role Modal */}
      <AlertDialog
        open={changeRoleModal.open}
        onOpenChange={(open) =>
          setChangeRoleModal({ open, user: changeRoleModal.user })
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change User Role</AlertDialogTitle>
            <AlertDialogDescription>
              Select a new role for{" "}
              <span className="font-semibold">
                {changeRoleModal.user?.email}
              </span>
              .
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Select
              value={selectedRole || undefined}
              onValueChange={setSelectedRole}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={changeRoleModal.user?.role || ""}>
                  {changeRoleModal.user?.role}{" "}
                  <Badge variant="secondary" className="ml-2">
                    Current
                  </Badge>
                </SelectItem>
                {changeRoleModal.user?.role === "ADMIN" ? (
                  <SelectItem value="USER">USER</SelectItem>
                ) : (
                  <SelectItem value="ADMIN">ADMIN</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setChangeRoleModal({ open: false, user: null })}
              >
                Cancel
              </Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleConfirmChangeRole}
                disabled={
                  roleLoadingId === changeRoleModal.user?.id ||
                  selectedRole === changeRoleModal.user?.role
                }
              >
                {roleLoadingId === changeRoleModal.user?.id
                  ? "Changing..."
                  : "Confirm"}
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
