"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { User, Shield, Bell, Info, Clock } from "lucide-react";
import MFASetup from "@/components/mfa-setup";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";

export default function SettingsPage() {
  const { user, connectedUserCompany } = useAuth();
  const { platforms } = useConnectedPlatforms();
  const [email, setEmail] = useState(user?.email || "");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Password update state
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);

  // Notification settings state
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [frequencyHours, setFrequencyHours] = useState(16);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);
  const [notificationError, setNotificationError] = useState<string | null>(
    null
  );
  const [notificationSuccess, setNotificationSuccess] = useState<string | null>(
    null
  );

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    // Simulate API call
    setTimeout(() => {
      setSuccess("Profile updated successfully!");
      setIsLoading(false);
    }, 1000);
  };

  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      setPasswordError("All fields are required");
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }
    
    if (newPassword.length < 8) {
      setPasswordError("New password must be at least 8 characters long");
      return;
    }
    
    if (newPassword === currentPassword) {
      setPasswordError("New password must be different from current password");
      return;
    }
    
    setIsUpdatingPassword(true);
    setPasswordError(null);
    setPasswordSuccess(null);
    
    try {
      // Use Supabase auth to update password
      const { createClientComponentClient } = await import("@supabase/auth-helpers-nextjs");
      const supabase = createClientComponentClient();
      
      // First, verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || "",
        password: currentPassword,
      });
      
      if (signInError) {
        throw new Error("Current password is incorrect");
      }
      
      // Update password using Supabase
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });
      
      if (updateError) {
        throw new Error(updateError.message || "Failed to update password");
      }
      
      setPasswordSuccess("Password updated successfully!");
      
      // Clear form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      
    } catch (error) {
      console.error("Error updating password:", error);
      setPasswordError(
        error instanceof Error ? error.message : "Failed to update password"
      );
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  // Notification settings API functions
  const updateNotificationSettings = async (
    enabled?: boolean,
    frequency?: number
  ) => {
    if (!user?.email || !connectedUserCompany?.company_id) {
      setNotificationError("User or company information not available");
      return;
    }

    setIsLoadingNotifications(true);
    setNotificationError(null);
    setNotificationSuccess(null);

    try {
      const payload: any = {};
      if (enabled !== undefined) payload.enabled = enabled;
      if (frequency !== undefined) payload.frequency_hours = frequency;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/integrations/notification-settings/?company_id=${connectedUserCompany.company_id}&acting_user_email=${user.email}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setNotificationSuccess("Notification settings updated successfully!");

      // Update local state
      if (enabled !== undefined) setNotificationsEnabled(enabled);
      if (frequency !== undefined) setFrequencyHours(frequency);
    } catch (error) {
      console.error("Error updating notification settings:", error);
      setNotificationError(
        "Failed to update notification settings. Please try again."
      );
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  const handleToggleNotifications = async (enabled: boolean) => {
    await updateNotificationSettings(enabled);
  };

  const handleUpdateFrequency = async (frequency: number) => {
    await updateNotificationSettings(undefined, frequency);
  };

  // Clear success messages after 3 seconds
  useEffect(() => {
    if (notificationSuccess) {
      const timer = setTimeout(() => {
        setNotificationSuccess(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [notificationSuccess]);

  useEffect(() => {
    if (passwordSuccess) {
      const timer = setTimeout(() => {
        setPasswordSuccess(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [passwordSuccess]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-800 mb-6">
              Account Settings
            </h1>

            <Tabs defaultValue="profile" className="space-y-6">
              <TabsList>
                <TabsTrigger value="profile" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </TabsTrigger>
                <TabsTrigger value="security" className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Security
                </TabsTrigger>
                <TabsTrigger
                  value="notifications"
                  className="flex items-center"
                >
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                </TabsTrigger>
              </TabsList>

              <TabsContent value="profile" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>
                      Update your account profile information
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <Alert variant="destructive" className="mb-4">
                        <AlertDescription>{error}</AlertDescription>
                      </Alert>
                    )}

                    {success && (
                      <Alert className="mb-4 bg-primary-50 text-primary-800 border-primary-200">
                        <AlertDescription>{success}</AlertDescription>
                      </Alert>
                    )}

                    <form onSubmit={handleUpdateProfile} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          disabled
                        />
                        <p className="text-sm text-gray-500">
                          Your email address is used for login and cannot be
                          changed
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <Input id="name" placeholder="Enter your full name" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="job-title">Job Title</Label>
                        <Input
                          id="job-title"
                          placeholder="Enter your job title"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="department">Department</Label>
                        <Input
                          id="department"
                          placeholder="Enter your department"
                        />
                      </div>
                    </form>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      type="submit"
                      className="bg-primary hover:bg-primary-600"
                      disabled={isLoading}
                      onClick={handleUpdateProfile}
                    >
                      {isLoading ? "Saving..." : "Save Changes"}
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="security" className="space-y-6 mb-20">
                <MFASetup />

                <Card>
                  <CardHeader>
                    <CardTitle>Change Password</CardTitle>
                    <CardDescription>
                      Update your account password
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {passwordError && (
                      <Alert variant="destructive" className="mb-4">
                        <AlertDescription>{passwordError}</AlertDescription>
                      </Alert>
                    )}

                    {passwordSuccess && (
                      <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
                        <AlertDescription>{passwordSuccess}</AlertDescription>
                      </Alert>
                    )}

                    <form onSubmit={handleUpdatePassword} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-password">
                          Current Password
                        </Label>
                        <Input 
                          id="current-password" 
                          type="password"
                          value={currentPassword}
                          onChange={(e) => {
                            setCurrentPassword(e.target.value);
                            if (passwordError) setPasswordError(null);
                          }}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="new-password">New Password</Label>
                        <Input 
                          id="new-password" 
                          type="password"
                          value={newPassword}
                          onChange={(e) => {
                            setNewPassword(e.target.value);
                            if (passwordError) setPasswordError(null);
                          }}
                          required
                          minLength={8}
                        />
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500">
                            Password must be at least 8 characters long
                          </p>
                          {newPassword && (
                            <div className="text-xs">
                              <div className={`flex items-center space-x-1 ${
                                newPassword.length >= 8 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                <div className={`w-2 h-2 rounded-full ${
                                  newPassword.length >= 8 ? 'bg-green-500' : 'bg-red-500'
                                }`}></div>
                                <span>At least 8 characters</span>
                              </div>
                              <div className={`flex items-center space-x-1 ${
                                /[A-Z]/.test(newPassword) ? 'text-green-600' : 'text-red-600'
                              }`}>
                                <div className={`w-2 h-2 rounded-full ${
                                  /[A-Z]/.test(newPassword) ? 'bg-green-500' : 'bg-red-500'
                                }`}></div>
                                <span>Contains uppercase letter</span>
                              </div>
                              <div className={`flex items-center space-x-1 ${
                                /[0-9]/.test(newPassword) ? 'text-green-600' : 'text-red-600'
                              }`}>
                                <div className={`w-2 h-2 rounded-full ${
                                  /[0-9]/.test(newPassword) ? 'bg-green-500' : 'bg-red-500'
                                }`}></div>
                                <span>Contains number</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">
                          Confirm New Password
                        </Label>
                        <Input 
                          id="confirm-password" 
                          type="password"
                          value={confirmPassword}
                          onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            if (passwordError) setPasswordError(null);
                          }}
                          required
                        />
                      </div>

                      <div className="flex justify-end pt-4">
                        <Button 
                          type="submit"
                          className="bg-primary hover:bg-primary-600"
                          disabled={isUpdatingPassword}
                        >
                          {isUpdatingPassword ? "Updating..." : "Update Password"}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notifications" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Settings</CardTitle>
                    <CardDescription>
                      Configure notification frequency for assignees and admins
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {notificationError && (
                      <Alert variant="destructive">
                        <AlertDescription>{notificationError}</AlertDescription>
                      </Alert>
                    )}

                    {notificationSuccess && (
                      <Alert className="bg-green-50 text-green-800 border-green-200">
                        <AlertDescription>
                          {notificationSuccess}
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Enable/Disable Notifications */}
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Bell className="h-5 w-5 text-gray-600" />
                        <div>
                          <Label className="text-base font-medium">
                            Enable Notifications
                          </Label>
                          <p className="text-sm text-gray-500">
                            Allow assignees and admins to receive notifications
                            through Slack/Discord
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={notificationsEnabled}
                        onCheckedChange={handleToggleNotifications}
                        disabled={isLoadingNotifications}
                      />
                    </div>

                    {/* Frequency Settings */}
                    {notificationsEnabled && (
                      <div className="space-y-4 p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Clock className="h-5 w-5 text-gray-600" />
                          <div>
                            <Label className="text-base font-medium">
                              Set Frequency
                            </Label>
                            <p className="text-sm text-gray-500">
                              Set the frequency at which assignees and admins
                              will get notifications through Slack/Discord
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="flex-1">
                            <Label
                              htmlFor="frequency"
                              className="text-sm font-medium"
                            >
                              Frequency (hours)
                            </Label>
                            <Input
                              id="frequency"
                              type="number"
                              min="0"
                              max="168"
                              value={frequencyHours}
                              onChange={(e) =>
                                setFrequencyHours(Number(e.target.value))
                              }
                              className="mt-1"
                              disabled={isLoadingNotifications}
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              0 = No limit, 1-168 = Hours between notifications
                            </p>
                          </div>
                          <Button
                            onClick={() =>
                              handleUpdateFrequency(frequencyHours)
                            }
                            disabled={isLoadingNotifications}
                            className="mt-6"
                          >
                            {isLoadingNotifications
                              ? "Updating..."
                              : "Update Frequency"}
                          </Button>
                        </div>

                        <div className="flex items-start space-x-2 p-3 bg-blue-50 rounded-lg">
                          <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                          <div className="text-sm text-blue-800">
                            <p className="font-medium">How it works:</p>
                            <ul className="mt-1 space-y-1 text-xs">
                              <li>
                                • <strong>0 hours:</strong> No frequency limit -
                                notifications sent immediately
                              </li>
                              <li>
                                • <strong>1-24 hours:</strong> Notifications
                                sent at most once per specified period
                              </li>
                              <li>
                                • <strong>24+ hours:</strong> Useful for
                                daily/weekly digest notifications
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Connected Platforms Info */}
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">
                        Connected Platforms
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {platforms.filter((p) => p.connected).length > 0 ? (
                          platforms
                            .filter((p) => p.connected)
                            .map((platform) => (
                              <div
                                key={platform.id}
                                className="flex items-center space-x-2 px-3 py-1 bg-white border border-gray-200 rounded-full text-sm"
                              >
                                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span className="capitalize">
                                  {platform.id}
                                </span>
                              </div>
                            ))
                        ) : (
                          <p className="text-sm text-gray-500">
                            No platforms connected. Connect platforms in the
                            sidebar to enable notifications.
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  );
}
