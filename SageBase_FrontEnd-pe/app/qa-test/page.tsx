"use client";

import { useQAApprovals } from "@/hooks/use-qa-approvals";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, X, RefreshCw, Edit } from "lucide-react";
import QAEditDialog from "@/components/qa-edit-dialog";
import { PendingQAApproval, QAEditData } from "@/services/qa-approval-api";
import { useState } from "react";

export default function QATestPage() {
  const [editingQA, setEditingQA] = useState<PendingQAApproval | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  const {
    pendingApprovals,
    isLoading,
    error,
    fetchPendingApprovals,
    approveQA,
    rejectQA,
    editQA,
  } = useQAApprovals();

  const handleEditQA = async (
    qaId: string,
    projectId: string,
    editData: QAEditData
  ) => {
    try {
      await editQA(qaId, projectId, editData);
      setShowEditDialog(false);
      setEditingQA(null);
    } catch (error) {
      console.error("Error editing QA:", error);
    }
  };

  const openEditDialog = (qa: PendingQAApproval) => {
    setEditingQA(qa);
    setShowEditDialog(true);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Q&A Approval Test</h1>
        <p className="text-gray-600">
          Test the Q&A approval system. This page shows pending Q&A approvals
          and allows you to approve or reject them.
        </p>
      </div>

      {/* Status Section */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            onClick={fetchPendingApprovals}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <div className="text-sm text-gray-600">
            {isLoading
              ? "Loading..."
              : `${pendingApprovals.length} pending approvals`}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <p className="text-red-800">Error: {error}</p>
          </div>
        )}
      </div>

      {/* Q&A List */}
      <div className="space-y-4">
        {pendingApprovals.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center text-gray-500">
              No pending Q&A approvals found.
            </CardContent>
          </Card>
        ) : (
          pendingApprovals.map((qa) => (
            <Card key={qa.id}>
              <CardHeader>
                <CardTitle className="text-lg">{qa.question.title}</CardTitle>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>Source: {qa.source}</span>
                  <span>•</span>
                  <span>Channel: {qa.channel}</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Question */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Question</h4>
                  <p className="text-gray-700">{qa.question.content}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-2">
                    <span>Author: {qa.question.author.name}</span>
                    <span>•</span>
                    <span>{qa.question.date}</span>
                  </div>
                  {qa.question.tags && qa.question.tags.length > 0 && (
                    <div className="flex gap-1 mt-2">
                      {qa.question.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Answer */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Answer</h4>
                  <p className="text-gray-700">{qa.answer.content}</p>
                  {qa.answer.code && (
                    <pre className="bg-gray-100 p-3 rounded-md mt-2 text-sm overflow-x-auto">
                      <code>{qa.answer.code}</code>
                    </pre>
                  )}
                  {qa.answer.explanation && (
                    <p className="text-gray-600 text-sm mt-2">
                      {qa.answer.explanation}
                    </p>
                  )}
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-2">
                    <span>Author: {qa.answer.author.name}</span>
                    <span>•</span>
                    <span>{qa.answer.date}</span>
                    <span>•</span>
                    <span>Verified: {qa.answer.isVerified ? "Yes" : "No"}</span>
                  </div>
                </div>

                {/* Votes */}
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center gap-4">
                    <span>Upvotes: {qa.votes.upvotes}</span>
                    <span>Downvotes: {qa.votes.downvotes}</span>
                  </div>
                  {qa.tags && qa.tags.length > 0 && (
                    <div className="flex gap-1">
                      {qa.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    onClick={() => approveQA(qa.id)}
                    disabled={isLoading}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    {isLoading ? "Approving..." : "Approve"}
                  </Button>
                  <Button
                    onClick={() => openEditDialog(qa)}
                    disabled={isLoading}
                    variant="outline"
                    className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Before Approving
                  </Button>
                  <Button
                    onClick={() => rejectQA(qa.id, "Rejected via test page")}
                    disabled={isLoading}
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <X className="h-4 w-4 mr-2" />
                    {isLoading ? "Rejecting..." : "Reject"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* QA Edit Dialog */}
      <QAEditDialog
        qa={editingQA}
        isOpen={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={handleEditQA}
        isLoading={isLoading}
      />
    </div>
  );
}
