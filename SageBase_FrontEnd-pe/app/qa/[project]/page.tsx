"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  MessageSquare,
  CheckCircle,
  ThumbsUp,
  Eye,
  Clock,
  Loader2,
  Trash2,
  MoreHorizontal,
  Edit3,
} from "lucide-react";
import SideNavigation from "@/components/side-navigation";
import SageBaseLogo from "@/components/sagebase-logo";
import { QA } from "@/types/qa";
import { deleteQA } from "@/lib/api-utils";
import { useAuth } from "@/contexts/auth-context";

export default function ProjectQAListPage() {
  const params = useParams();
  const project = params.project as string;
  const { userRole } = useAuth();

  const [qas, setQas] = useState<QA[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    qaId: string | null;
  }>({
    show: false,
    x: 0,
    y: 0,
    qaId: null,
  });

  // Delete confirmation state
  const [deleteConfirm, setDeleteConfirm] = useState<{
    show: boolean;
    qaId: string | null;
    qaTitle: string;
  }>({
    show: false,
    qaId: null,
    qaTitle: "",
  });

  // Fetch Q&As for the project
  useEffect(() => {
    const fetchQAs = async () => {
      try {
        setLoading(true);

        // Get auth headers
        const getAuthHeaders = async (): Promise<HeadersInit> => {
          try {
            const { createClientComponentClient } = await import(
              "@supabase/auth-helpers-nextjs"
            );
            const supabase = createClientComponentClient();
            const {
              data: { session },
            } = await supabase.auth.getSession();

            if (session?.access_token) {
              return {
                "Content-Type": "application/json",
                Authorization: `Bearer ${session.access_token}`,
              };
            }
          } catch (error) {
            console.warn("Failed to get auth token:", error);
          }
          return { "Content-Type": "application/json" };
        };

        const headers = await getAuthHeaders();
        const API_BASE_URL =
          process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

        const response = await fetch(
          `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${project}`,
          { headers }
        );
        const result = await response.json();

        if (result.success) {
          setQas(result.data);
        } else {
          setError(result.error || "Failed to load Q&As");
        }
      } catch (error) {
        console.error("Error fetching Q&As:", error);
        setError("Failed to load Q&As");
      } finally {
        setLoading(false);
      }
    };

    if (project) {
      fetchQAs();
    }
  }, [project]);

  // Filter Q&As based on search term
  const filteredQAs = qas.filter(
    (qa) =>
      qa.question.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      qa.question.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      qa.question.tags.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const formatTimeAgo = (date: Date | string) => {
    const now = new Date();
    const qaDate = new Date(date);
    const diffInHours = Math.floor(
      (now.getTime() - qaDate.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return "Yesterday";
    return qaDate.toLocaleDateString();
  };

  // Context menu handlers
  const handleContextMenu = (e: React.MouseEvent, qaId: string) => {
    e.preventDefault();
    if (userRole === "admin") {
      setContextMenu({
        show: true,
        x: e.clientX,
        y: e.clientY,
        qaId,
      });
    }
  };

  const closeContextMenu = () => {
    setContextMenu({ ...contextMenu, show: false });
  };

  const handleDeleteQA = (qaId: string, qaTitle: string) => {
    setDeleteConfirm({
      show: true,
      qaId,
      qaTitle,
    });
    closeContextMenu();
  };

  const confirmDeleteQA = async () => {
    if (!deleteConfirm.qaId) return;

    const result = await deleteQA(deleteConfirm.qaId);

    if (result.success) {
      // Remove the Q&A from the local state
      setQas((prev) => prev.filter((qa) => qa.id !== deleteConfirm.qaId));
    }

    setDeleteConfirm({ show: false, qaId: null, qaTitle: "" });
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <SageBaseLogo />
              </Link>
              <nav className="hidden md:flex items-center">
                <h1 className="text-lg font-semibold text-gray-900">
                  {project.charAt(0).toUpperCase() + project.slice(1)} Q&A
                </h1>
              </nav>
            </div>

            <div className="flex items-center space-x-2">
              <Link href="/dashboard">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-1.5 h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
              <Button size="sm" className="flex items-center">
                <Plus className="mr-1.5 h-4 w-4" />
                New Q&A
              </Button>
            </div>
          </div>
        </header>

        {/* Search and Filters */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search Q&As..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <Button variant="outline" size="sm" className="flex items-center">
              <Filter className="mr-1.5 h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>

        {/* Q&A List */}
        <div className="flex-1 overflow-auto">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading Q&As...</span>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Error Loading Q&As
                </h2>
                <p className="text-gray-600">{error}</p>
              </div>
            </div>
          ) : filteredQAs.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {searchTerm ? "No Q&As Found" : "No Q&As Yet"}
                </h2>
                <p className="text-gray-600 mb-6">
                  {searchTerm
                    ? "Try adjusting your search terms"
                    : "Be the first to create a Q&A for this project"}
                </p>
                {!searchTerm && (
                  <Button className="flex items-center">
                    <Plus className="mr-2 h-4 w-4" />
                    Create First Q&A
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="p-6">
              <div className="space-y-4">
                {filteredQAs.map((qa) => (
                  <div
                    key={qa.id}
                    className="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary hover:shadow-sm transition-all cursor-pointer"
                    onContextMenu={(e) => handleContextMenu(e, qa.id)}
                  >
                    <Link href={`/qa/${project}/${qa.id}`}>
                      <div className="w-full">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            {/* Question Header */}
                            <div className="flex items-center space-x-3 mb-3">
                              <div className="bg-blue-100 rounded-full p-1.5">
                                <MessageSquare className="h-4 w-4 text-blue-600" />
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900 hover:text-primary">
                                {qa.question.title}
                              </h3>
                            </div>

                            {/* Question Content Preview */}
                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                              {qa.question.content}
                            </p>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-2 mb-4">
                              {qa.question.tags.slice(0, 3).map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="bg-blue-50 text-blue-700 text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))}
                              {qa.question.tags.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{qa.question.tags.length - 3}
                                </Badge>
                              )}
                            </div>

                            {/* Answer Status */}
                            {qa.answer && (
                              <div className="flex items-center space-x-2 mb-3">
                                <div className="bg-green-100 rounded-full p-1">
                                  <CheckCircle className="h-3 w-3 text-green-600" />
                                </div>
                                <span className="text-sm text-green-700 font-medium">
                                  {qa.answer.isVerified
                                    ? "Verified Solution"
                                    : "Solution Available"}
                                </span>
                              </div>
                            )}

                            {/* Meta Information */}
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Avatar className="h-4 w-4">
                                  <AvatarImage
                                    src={
                                      qa.question.author.avatar ||
                                      "/placeholder.svg"
                                    }
                                    alt={qa.question.author.name}
                                  />
                                  <AvatarFallback className="text-xs">
                                    {qa.question.author.name.charAt(0)}
                                  </AvatarFallback>
                                </Avatar>
                                <span>{qa.question.author.name}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{formatTimeAgo(qa.createdAt)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Eye className="h-3 w-3" />
                                <span>{qa.metadata.views} views</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <ThumbsUp className="h-3 w-3" />
                                <span>{qa.votes.upvotes} votes</span>
                              </div>
                            </div>
                          </div>

                          {/* Stats Column */}
                          <div className="flex flex-col items-end space-y-2 ml-6">
                            {qa.answer?.isVerified && (
                              <Badge className="bg-green-600 text-white text-xs">
                                Verified
                              </Badge>
                            )}
                            <div className="text-right text-xs text-gray-500">
                              <div>ID: {qa.id}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Context Menu */}
      {contextMenu.show && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={closeContextMenu}
        >
          <div
            className="bg-white rounded-lg shadow-lg w-full max-w-md"
            style={{
              position: "absolute",
              left: contextMenu.x,
              top: contextMenu.y,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Q&A Options
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={closeContextMenu}
                className="h-8 w-8"
              >
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </div>

            <div className="p-4">
              <Button
                variant="outline"
                className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                onClick={() => {
                  const qa = qas.find((q) => q.id === contextMenu.qaId);
                  if (qa) {
                    // TODO: Implement rename functionality
                    console.log("Rename Q&A:", qa.id);
                  }
                }}
              >
                <Edit3 className="h-4 w-4 mr-2" />
                Rename Q&A
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 mt-2"
                onClick={() => {
                  const qa = qas.find((q) => q.id === contextMenu.qaId);
                  if (qa) {
                    handleDeleteQA(qa.id, qa.question.title);
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Q&A
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Confirm Delete
              </h2>
            </div>

            <div className="p-4">
              <p className="text-gray-600 mb-4">
                Are you sure you want to delete the Q&A "{deleteConfirm.qaTitle}
                "? This action cannot be undone.
              </p>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() =>
                    setDeleteConfirm({ show: false, qaId: null, qaTitle: "" })
                  }
                >
                  Cancel
                </Button>
                <Button variant="destructive" onClick={confirmDeleteQA}>
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
