"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  ArrowLeft,
  Share2,
  MoreHorizontal,
  Clock,
  User,
  ChevronRight,
  ThumbsUp,
  ThumbsDown,
  Loader2,
  Trash2,
  X,
} from "lucide-react";
import Link from "next/link";
import SideNavigation from "@/components/side-navigation";
import AIAssistButton from "@/components/ai-assist-button";
import SageBaseLogo from "@/components/sagebase-logo";
import { QA } from "@/types/qa";
import { useQAPageRefresh } from "@/contexts/qa-page-refresh-context";
import { deleteQA } from "@/lib/api-utils";
import { qaAPI } from "@/services/qa-api";
import viewsAPI from "@/services/views-api";
import CommentsSection from "@/components/comments-section";
import { getBackendUrl } from "@/lib/api-config";

export default function QAPage() {
  const params = useParams();
  const router = useRouter();
  const project = params.project as string;
  const id = params.id as string;
  const { registerQAPage, unregisterQAPage } = useQAPageRefresh();

  const [qa, setQa] = useState<QA | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [voting, setVoting] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [views, setViews] = useState<number>(0);
  const [viewsLoading, setViewsLoading] = useState(true);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [editData, setEditData] = useState<{
    question_title: string;
    question_content: string;
    question_tags: string[];
    answer_content: string;
  } | null>(null);
  const { toast } = useToast();

  // Get auth headers function
  const getAuthHeaders = async (): Promise<HeadersInit> => {
    // Get auth token from localStorage (Django JWT)
    const authToken = localStorage.getItem("authToken");
    
    // Debug authentication
    console.log("🔍 Authentication Debug:");
    console.log("- Auth token exists:", !!authToken);
    console.log("- Token length:", authToken?.length || 0);
    
    if (!authToken) {
      console.error("❌ No authentication token found in localStorage");
      throw new Error("No authentication token found");
    }
    
    // Log token details (without exposing the full token)
    try {
      const tokenParts = authToken.split('.');
      if (tokenParts.length === 3) {
        const payload = JSON.parse(atob(tokenParts[1]));
        console.log("- Token payload:", {
          exp: payload.exp,
          iat: payload.iat,
          user_id: payload.user_id,
          email: payload.email
        });
        
        // Check if token is expired
        const now = Date.now() / 1000;
        if (payload.exp < now) {
          console.error("❌ Token is expired");
          throw new Error("Authentication token is expired");
        }
      }
    } catch (error) {
      console.error("❌ Error parsing token:", error);
    }
    
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${authToken}`,
    };
    
    console.log("✅ Headers prepared:", {
      "Content-Type": headers["Content-Type"],
      "Authorization": `Bearer ${authToken.substring(0, 20)}...`
    });
    
    return headers;
  };

  // Fetch Q&A data function
  const fetchQA = async () => {
    try {
      setLoading(true);
      const API_BASE_URL = getBackendUrl();

      console.log("🔍 Fetching Q&A:");
      console.log("- Project:", project);
      console.log("- Q&A ID:", id);
      console.log("- API URL:", `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${project}/qa/${id}`);

      const headers = await getAuthHeaders();
      
      console.log("📡 Making request with headers:", {
        "Content-Type": "application/json",
        "Authorization": headers.Authorization ? "Bearer [TOKEN]" : "None"
      });

      const response = await fetch(
        `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${project}/qa/${id}`,
        {
          headers,
        }
      );
      
      console.log("📡 Response status:", response.status);
      console.log("📡 Response headers:", Object.fromEntries(response.headers.entries()));
      
      const result = await response.json();
      console.log("📡 Response body:", result);

      if (result.success) {
        setQa(result.data);
      } else {
        console.error("❌ API returned error:", result.error);
        setError(result.error || "Failed to load Q&A");
      }
    } catch (error) {
      console.error("❌ Error in fetchQA:", error);
      setError(error instanceof Error ? error.message : "Failed to load Q&A");
    } finally {
      setLoading(false);
    }
  };

  // Load views function
  const loadViews = async () => {
    try {
      setViewsLoading(true);
      const viewsCount = await viewsAPI.getViews(id);
      setViews(viewsCount);
    } catch (error) {
      console.error("Error loading views:", error);
      // Don't set error state for views, just log it
    } finally {
      setViewsLoading(false);
    }
  };

  // Track view function
  const trackView = async () => {
    try {
      await viewsAPI.trackView(id);
      // Update local view count optimistically
      setViews((prev) => prev + 1);
    } catch (error) {
      console.error("Error tracking view:", error);
      // Don't show error to user for view tracking
    }
  };

  // Fetch Q&A data and views
  useEffect(() => {
    if (id) {
      fetchQA();
      loadViews();
      trackView(); // Track the view when component mounts
    }
  }, [id]);

  // Handle edit Q&A
  const handleEditQA = () => {
    if (!qa) return;

    setEditData({
      question_title: qa.question.title,
      question_content: qa.question.content,
      question_tags: qa.question.tags,
      answer_content: qa.answer?.content || "",
    });
    setShowEditModal(true);
  };

  // Handle share solution
  const handleShareSolution = async () => {
    setIsSharing(true);
    try {
      const currentUrl = window.location.href;
      await navigator.clipboard.writeText(currentUrl);

      toast({
        title: "Link copied!",
        description: "The QA link has been copied to your clipboard.",
        variant: "success",
        duration: 4000,
      });
    } catch (error) {
      console.error("Failed to copy URL:", error);
      toast({
        title: "Failed to copy link",
        description: "Please try copying the URL manually from your browser.",
        variant: "destructive",
        duration: 4000,
      });
    } finally {
      setIsSharing(false);
    }
  };

  // Handle delete Q&A
  const handleDeleteQA = async () => {
    if (!qa) return;

    setDeleting(true);
    try {
      console.log("🗑️ Deleting Q&A:", qa.id);

      const result = await deleteQA(project, qa.id);

      if (result.success) {
        console.log("✅ Q&A deleted successfully");
        // Go back to the previous page or stay on current page
        router.back();
      } else {
        console.error("❌ Failed to delete Q&A:", result.error);
        setError(result.error || "Failed to delete Q&A");
      }
    } catch (error) {
      console.error("❌ Error deleting Q&A:", error);
      setError("Failed to delete Q&A");
    } finally {
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Register this page with the refresh context
  useEffect(() => {
    if (project && id) {
      registerQAPage(project, id, fetchQA);

      // Cleanup: unregister when component unmounts
      return () => {
        unregisterQAPage(project, id);
      };
    }
  }, [project, id]);

  // Voting handlers
  const handleVote = async (voteType: "up" | "down") => {
    if (!qa || voting) {
      return;
    }

    console.log(
      "Voting started:",
      voteType,
      "for Q&A:",
      id,
      "in project:",
      project
    );
    setVoting(true);
    try {
      const API_BASE_URL = getBackendUrl();
      const headers = await getAuthHeaders();

      const response = await fetch(
        `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${project}/qa/${id}/vote`,
        {
          method: "POST",
          headers: {
            ...headers,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ type: voteType }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setQa((prev) =>
          prev
            ? {
                ...prev,
                votes: result.data.votes,
              }
            : null
        );
      } else {
        console.error("Failed to vote:", result.error);
      }
    } catch (error) {
      console.error("Error voting:", error);
    } finally {
      setVoting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <SideNavigation />
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading Q&A...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !qa) {
    return (
      <div className="flex h-screen bg-gray-50">
        <SideNavigation />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Q&A Not Found
            </h2>
            <p className="text-gray-600 mb-4">
              {error || "The requested Q&A could not be found."}
            </p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <SageBaseLogo />
              </Link>
              <nav className="hidden md:flex items-center">
                <Button
                  variant="ghost"
                  className="text-sm font-normal text-gray-600"
                >
                  {project.charAt(0).toUpperCase() + project.slice(1)} / Q&A /
                </Button>
              </nav>
            </div>

            <div className="flex items-center space-x-2">
              <Link href="/dashboard">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-1.5 h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center"
                onClick={handleShareSolution}
                disabled={isSharing}
              >
                {isSharing ? (
                  <>
                    <Loader2 className="mr-1.5 h-4 w-4 animate-spin" />
                    Copying...
                  </>
                ) : (
                  <>
                    <Share2 className="mr-1.5 h-4 w-4" />
                    Share
                  </>
                )}
              </Button>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Document Info */}
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="w-full">
              <div className="flex items-center text-xs text-gray-500 mb-1">
                <Link href="/" className="hover:text-primary">
                  SageBase
                </Link>
                <ChevronRight className="h-3 w-3 mx-1" />
                <Link
                  href={`/spaces/${project}`}
                  className="capitalize hover:text-primary"
                >
                  {project}
                </Link>
                <ChevronRight className="h-3 w-3 mx-1" />
                <span className="text-gray-500">Q&A</span>
                <ChevronRight className="h-3 w-3 mx-1" />
                <span className="text-gray-700">{qa.id}</span>
              </div>
              <h1 className="text-xl font-bold text-gray-800">
                {qa.question.title}
              </h1>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <span className="bg-blue-100 text-blue-600 px-2 py-0.5 rounded text-xs">
                  Q&A FORMAT
                </span>
                <span className="mx-1.5">•</span>
                <Clock className="h-3 w-3 mr-1" />
                <span>
                  Last updated{" "}
                  {qa.updatedAt
                    ? new Date(qa.updatedAt).toLocaleDateString()
                    : "Today"}
                </span>
                <span className="mx-1.5">•</span>
                <User className="h-3 w-3 mr-1" />
                <span>{qa.question.author.name}</span>
                <span className="mx-1.5">•</span>
                <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs">
                  {viewsLoading ? (
                    <span className="inline-flex items-center">
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Loading...
                    </span>
                  ) : (
                    `${views} views`
                  )}
                </span>
              </div>
            </div>
            <AIAssistButton />
          </div>
        </div>

        {/* Q&A Content */}
        <div className="flex-1 overflow-auto bg-white">
          <div className="max-w-4xl mx-auto py-8 px-4">
            {/* Question Section */}
            <div className="mb-8">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6 shadow-lg">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-3 shadow-lg">
                        <svg
                          className="w-6 h-6 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-400 rounded-full animate-pulse"></div>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                        Question
                      </h2>
                      <div className="flex items-center text-sm text-gray-600 mt-1">
                        <span className="font-semibold text-blue-700">
                          {qa.question.author.name}
                        </span>
                        <span className="mx-2 text-blue-400">•</span>
                        <span className="text-gray-500">
                          {qa.question.date} at {qa.question.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-800 mb-4 bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                  {qa.question.title}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-6 text-lg">
                  {qa.question.content}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {qa.question.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 hover:from-blue-200 hover:to-blue-300 border border-blue-300 shadow-sm"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Answer Section */}
            {qa.answer && (
              <div className="mb-8">
                <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-xl p-6 shadow-lg">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-3 shadow-lg">
                          <svg
                            className="w-6 h-6 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </div>
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
                          Answer
                        </h2>
                        <div className="flex items-center text-sm text-gray-600 mt-1">
                          <span className="font-semibold text-green-700">
                            {qa.answer.author.name}
                          </span>
                          <span className="mx-2 text-green-400">•</span>
                          <span className="text-gray-500">
                            {qa.answer.date} at {qa.answer.timestamp}
                          </span>
                        </div>
                      </div>
                    </div>
                    {qa.answer.isVerified && (
                      <Badge className="bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg border-0">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        Verified Answer
                      </Badge>
                    )}
                  </div>

                  <p className="text-gray-700 leading-relaxed mb-6 text-lg">
                    {qa.answer.content}
                  </p>

                  {/* Code Block */}
                  {qa.answer.code && (
                    <div className="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                      <code className="text-green-400 font-mono text-sm whitespace-pre">
                        {qa.answer.code}
                      </code>
                    </div>
                  )}

                  {qa.answer.explanation && (
                    <p className="text-gray-700 leading-relaxed mb-6">
                      {qa.answer.explanation}
                    </p>
                  )}

                  {/* Voting Section */}
                  <div className="flex items-center justify-between pt-6 border-t border-green-200 bg-gradient-to-r from-green-50/50 to-transparent rounded-lg p-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleVote("up")}
                          disabled={voting}
                          className="flex items-center space-x-2 hover:bg-green-100 text-gray-600 rounded-full px-4 py-2 transition-all duration-200 hover:shadow-md"
                        >
                          <ThumbsUp className="h-4 w-4 text-green-600" />
                          <span className="font-semibold text-green-700">
                            {qa.votes.upvotes}
                          </span>
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleVote("down")}
                          disabled={voting}
                          className="flex items-center space-x-2 hover:bg-red-100 text-gray-600 rounded-full px-4 py-2 transition-all duration-200 hover:shadow-md"
                        >
                          <ThumbsDown className="h-4 w-4 text-red-600" />
                          <span className="font-semibold text-red-700">
                            {qa.votes.downvotes}
                          </span>
                        </Button>
                      </div>

                      <div className="text-sm text-gray-500 font-medium">
                        {qa.votes.upvotes + qa.votes.downvotes} total votes
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 text-sm text-gray-500 font-medium">
                      <span>Was this helpful?</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Metadata Section */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="grid grid-flow-col auto-cols-max justify-between gap-4">
                {/* Edited By */}
                {qa.metadata.editedBy && (
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500">Edited by</span>
                      <span className="font-medium text-gray-700 mx-2">
                        {qa.metadata.editedBy.name}
                      </span>
                      <span className="text-gray-500 ml-2">
                        • {qa.metadata.editedBy.date}
                      </span>
                    </div>
                  </div>
                )}

                {/* Created By */}
                {qa.metadata.createdBy && (
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500">Created by</span>
                      <span className="font-medium text-gray-700 mx-2">
                        {qa.metadata.createdBy.name}
                      </span>
                      <span className="text-gray-500 ml-2">
                        • {qa.metadata.createdBy.date}
                      </span>
                    </div>
                  </div>
                )}

                {/* Approved By */}
                {qa.metadata.approvedBy && (
                  <div className="flex items-end space-x-3">
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500">Approved by</span>
                      <span className="font-medium text-gray-700 mx-2">
                        {qa.metadata.approvedBy.name}
                      </span>
                      <span className="text-gray-500 ml-2">
                        • {qa.metadata.approvedBy.date}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Comments Section */}
            <div className="mt-8">
              <CommentsSection qaId={id} />
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex justify-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleShareSolution}
                disabled={isSharing}
              >
                {isSharing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Copying...
                  </>
                ) : (
                  <>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Answer
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={handleEditQA}>
                Edit Q&A
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={deleting}
              >
                {deleting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Q&A
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Delete Q&A
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowDeleteConfirm(false)}
                className="h-8 w-8"
                disabled={deleting}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-6">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete this Q&A? This action cannot be
                undone.
              </p>
              {qa && (
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <p className="font-medium text-gray-900">
                    {qa.question.title}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {qa.question.content.substring(0, 100)}
                    {qa.question.content.length > 100 && "..."}
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-2 p-6 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteQA}
                disabled={deleting}
              >
                {deleting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  "Delete Q&A"
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Q&A Modal */}
      {showEditModal && editData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">Edit Q&A</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setShowEditModal(false);
                  setEditData(null);
                }}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <form
              onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);

                const updateData = {
                  question_title: formData.get("question_title") as string,
                  question_content: formData.get("question_content") as string,
                  question_tags: (formData.get("question_tags") as string)
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag),
                  answer_content: formData.get("answer_content") as string,
                };

                try {
                  const result = await qaAPI.updateQA(project, id, updateData);

                  if (result.success) {
                    // Refresh the Q&A data
                    fetchQA();
                    setShowEditModal(false);
                    setEditData(null);
                  } else {
                    setError(result.message || "Failed to update Q&A");
                  }
                } catch (error) {
                  console.error("Error updating Q&A:", error);
                  setError("Failed to update Q&A");
                }
              }}
              className="p-4 space-y-4"
            >
              <div>
                <label
                  htmlFor="question_title"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Title
                </label>
                <Input
                  id="question_title"
                  name="question_title"
                  defaultValue={editData.question_title}
                  placeholder="Enter question title"
                  className="w-full"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="question_content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Content
                </label>
                <textarea
                  id="question_content"
                  name="question_content"
                  defaultValue={editData.question_content}
                  placeholder="Enter question content"
                  className="w-full min-h-[100px] p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="question_tags"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Tags (comma-separated)
                </label>
                <Input
                  id="question_tags"
                  name="question_tags"
                  defaultValue={editData.question_tags.join(", ")}
                  placeholder="Enter tags separated by commas"
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="answer_content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Answer Content
                </label>
                <textarea
                  id="answer_content"
                  name="answer_content"
                  defaultValue={editData.answer_content}
                  placeholder="Enter answer content"
                  className="w-full min-h-[120px] p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowEditModal(false);
                    setEditData(null);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
