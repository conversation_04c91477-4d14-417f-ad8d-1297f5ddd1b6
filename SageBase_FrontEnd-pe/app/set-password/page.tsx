"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

export default function SetPasswordPage() {
  const [email, setEmail] = useState("");
  const [temporaryPassword, setTemporaryPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [step, setStep] = useState<"login" | "set-password">("login");
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient();

  // Get email from URL params if available
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  const handleTemporaryLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");
    setIsLoading(true);

    if (!email || !temporaryPassword) {
      setError("Please enter both email and temporary password");
      setIsLoading(false);
      return;
    }

    try {
      // Attempt to sign in with temporary password
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password: temporaryPassword,
      });

      if (error) {
        console.error("Login error:", error);
        setError("Invalid email or temporary password. Please check your invitation email.");
      } else {
        console.log("Temporary login successful:", data);
        setMessage("Temporary login successful! Now set your new password.");
        setStep("set-password");
      }
    } catch (err: any) {
      console.error("Unexpected error:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetNewPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");
    setIsLoading(true);

    // Validation
    if (!newPassword || !confirmPassword) {
      setError("Please enter both new password and confirmation");
      setIsLoading(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("New passwords do not match");
      setIsLoading(false);
      return;
    }

    if (newPassword.length < 8) {
      setError("New password must be at least 8 characters long");
      setIsLoading(false);
      return;
    }

    if (newPassword === temporaryPassword) {
      setError("New password must be different from temporary password");
      setIsLoading(false);
      return;
    }

    try {
      // Check if user is still authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        setError("Your session has expired. Please sign in again with your temporary password.");
        setStep("login");
        setIsLoading(false);
        return;
      }

      // Update password using Supabase
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (updateError) {
        console.error("Password update error:", updateError);
        setError(updateError.message || "Failed to update password");
      } else {
        setMessage("Password updated successfully! Redirecting to dashboard...");
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push("/ai-search");
        }, 2000);
      }
    } catch (err: any) {
      console.error("Unexpected error:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-white">
      <div className="flex flex-[0.35] flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div className="flex flex-col items-center">
            <Image
              src="/images/sagebase-purple-full.svg"
              alt="SageBase Logo"
              width={220}
              height={50}
              className="mb-8"
              priority
            />
            <h2 className="text-2xl font-bold leading-9 tracking-tight text-gray-900">
              {step === "login" ? "Welcome to SageBase!" : "Set Your Password"}
            </h2>
            <p className="mt-2 text-sm text-gray-600 text-center">
              {step === "login" 
                ? "You've been invited to join SageBase. Please enter your temporary password to get started."
                : "Set a secure password for your account."
              }
            </p>
          </div>

          <div className="mt-10">
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {message && (
              <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
                <AlertDescription>{message}</AlertDescription>
              </Alert>
            )}

            {isLoading && (
              <Alert className="mb-4 bg-blue-50 text-blue-800 border-blue-200">
                <AlertDescription>
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span>
                      {step === "login" ? "Signing in..." : "Updating password..."}
                    </span>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {step === "login" ? (
              <form onSubmit={handleTemporaryLogin} className="space-y-6">
                <div>
                  <Label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
                    Email address
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="px-4 py-1.5"
                      placeholder="Enter your email"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="temporary-password" className="block text-sm font-medium leading-6 text-gray-900">
                    Temporary Password
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="temporary-password"
                      name="temporary-password"
                      type="password"
                      autoComplete="current-password"
                      required
                      value={temporaryPassword}
                      onChange={(e) => setTemporaryPassword(e.target.value)}
                      className="px-4 py-1.5"
                      placeholder="Enter your temporary password"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Check your invitation email for the temporary password
                  </p>
                </div>

                <div>
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Signing in..." : "Continue"}
                  </Button>
                </div>
              </form>
            ) : (
              <form onSubmit={handleSetNewPassword} className="space-y-6">
                <div>
                  <Label htmlFor="new-password" className="block text-sm font-medium leading-6 text-gray-900">
                    New Password
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="new-password"
                      name="new-password"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="px-4 py-1.5"
                      placeholder="Enter your new password"
                      minLength={8}
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Password must be at least 8 characters long
                  </p>
                </div>

                <div>
                  <Label htmlFor="confirm-password" className="block text-sm font-medium leading-6 text-gray-900">
                    Confirm New Password
                  </Label>
                  <div className="mt-2">
                    <Input
                      id="confirm-password"
                      name="confirm-password"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="px-4 py-1.5"
                      placeholder="Confirm your new password"
                    />
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => setStep("login")}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                  <Button type="submit" className="flex-1" disabled={isLoading}>
                    {isLoading ? "Setting password..." : "Set Password"}
                  </Button>
                </div>
              </form>
            )}

            <p className="mt-4 text-center text-sm text-gray-600">
              Already have an account?{" "}
              <Link
                href="/login"
                className="font-medium text-primary hover:text-primary-500"
              >
                Sign in
              </Link>
            </p>
          </div>

          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              © 2024 SageBase. All rights reserved.
            </p>
          </div>
        </div>
      </div>
      <div className="relative hidden w-0 flex-[0.65] lg:block">
        <Image
          src="/images/login-background-new.png"
          alt="SageBase platform integrations"
          fill
          priority
          className="object-cover"
          sizes="(max-width: 1024px) 0vw, 50vw"
        />
      </div>
    </div>
  );
} 