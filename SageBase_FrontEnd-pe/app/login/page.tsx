"use client";

import type React from "react";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth-context";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false); // Add submission state
  const submitRef = useRef(false); // Add ref to track submission
  const { signIn, user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/ai-search";
  const message = searchParams.get("message");

  // Redirect if already authenticated
  useEffect(() => {
    if (user) {
      console.log("Redirecting - user:", !!user, "redirectTo:", redirectTo);
      router.push(redirectTo);
    }
  }, [user, router, redirectTo]);

  // Show success message if password was updated
  useEffect(() => {
    if (message === "password-updated") {
      setSuccessMessage(
        "Your password has been updated successfully! You can now sign in with your new password."
      );
    }
  }, [message]);

  // Check if Supabase is configured
  const hasSupabaseConfig =
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Prevent rapid successive submissions
    if (isSubmitting || submitRef.current) {
      console.log("Form submission blocked - already processing");
      return;
    }
    
    setError("");
    setIsLoading(true);
    setIsSubmitting(true);
    submitRef.current = true;

    try {
      await signIn(email, password);
      // Redirect to the intended page after successful login
      router.push(redirectTo);
    } catch (err: any) {
      console.error("Login error:", err);
      setError(
        err.message || "Failed to sign in. Please check your credentials."
      );
    } finally {
      setIsLoading(false);
      // Add a small delay before allowing new submissions
      setTimeout(() => {
        setIsSubmitting(false);
        submitRef.current = false;
      }, 1000);
    }
  };

  return (
    <div className="flex min-h-screen bg-white">
      <div className="flex flex-[0.35] flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div className="flex flex-col items-center">
            <Image
              src="/images/sagebase-purple-full.svg"
              alt="SageBase Logo"
              width={220}
              height={50}
              className="mb-8"
              priority
            />
            <h2 className="text-2xl font-bold leading-9 tracking-tight text-gray-900">
              Sign in to your account
            </h2>
          </div>

          <div className="mt-10">
            {/* Demo mode notice */}
            {!hasSupabaseConfig && (
              <div className="mb-4 text-sm text-blue-600 bg-blue-50 p-3 rounded-md">
                <strong>Demo Mode:</strong> Use email "<EMAIL>" and
                password "demo123".
              </div>
            )}

            {successMessage && (
              <div className="mb-4 text-sm text-green-600 bg-green-50 p-3 rounded-md">
                {successMessage}
              </div>
            )}

            {error && (
              <div className="mb-4 text-sm text-red-600 bg-red-50 p-3 rounded-md">
                {error}
                {(error.includes("rate limit") ||
                  error.includes("too many requests")) && (
                  <div className="mt-2 text-xs text-red-500">
                    <p className="font-medium">💡 Tips:</p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Wait a few minutes before trying again</li>
                      <li>Try using a different network connection</li>
                      <li>Use "Continue as guest" mode temporarily</li>
                      <li>Contact support if the issue persists</li>
                    </ul>
                  </div>
                )}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label
                  htmlFor="email"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Email address
                </Label>
                <div className="mt-2">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="px-4 py-1.5"
                    placeholder={
                      !hasSupabaseConfig
                        ? "<EMAIL>"
                        : "Enter your email"
                    }
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <Label
                    htmlFor="password"
                    className="block text-sm font-medium leading-6 text-gray-900"
                  >
                    Password
                  </Label>
                </div>
                <div className="mt-2">
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="px-4 py-1.5"
                    placeholder={!hasSupabaseConfig ? "********" : "********"}
                  />
                </div>
              </div>

              {hasSupabaseConfig && (
                <div className="flex items-center justify-between">
                  <Link
                    href="/forgot-password"
                    className="text-sm font-medium text-primary hover:text-primary-500"
                  >
                    Forgot your password?
                  </Link>
                </div>
              )}

              <div>
                <Button type="submit" className="w-full" disabled={isLoading || isSubmitting}>
                  {isLoading ? "Signing in..." : "Sign in"}
                </Button>
              </div>
            </form>

            <div className="mt-8 text-center">
              <p className="text-xs text-gray-500">
                © 2024 SageBase. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="relative hidden w-0 flex-[0.65] lg:block">
        <Image
          src="/images/login-background-new.png"
          alt="SageBase platform integrations"
          fill
          priority
          className="object-cover"
          sizes="(max-width: 1024px) 0vw, 50vw"
        />
      </div>
    </div>
  );
}
