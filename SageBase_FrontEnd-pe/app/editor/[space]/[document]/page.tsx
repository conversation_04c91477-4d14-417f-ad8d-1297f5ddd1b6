"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Save, ArrowLeft, Share2, MoreHorizontal, Clock, User, ChevronRight } from "lucide-react"
import Link from "next/link"
import SideNavigation from "@/components/side-navigation"
import RichTextEditor from "@/components/rich-text-editor"
import AIAssistButton from "@/components/ai-assist-button"
import SageBaseLogo from "@/components/sagebase-logo"
import QADocumentPage from "./qa-page"

export default function DocumentEditorPage() {
  const params = useParams()
  const spaceId = params.space as string
  const documentId = params.document as string

  const [documentTitle, setDocumentTitle] = useState("")
  const [content, setContent] = useState("")
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [spaceName, setSpaceName] = useState("")
  const [documentPath, setDocumentPath] = useState("")
  const [documentFormat, setDocumentFormat] = useState<"standard" | "qa">("standard")

  useEffect(() => {
    // In a real app, we would fetch the document data
    // For now, we'll just set a title based on the document ID
    const formattedDocTitle = documentId
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")

    setDocumentTitle(formattedDocTitle)

    // Set space name based on spaceId
    const formattedSpaceName = spaceId
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")

    setSpaceName(formattedSpaceName)

    // Set document path
    setDocumentPath(`${formattedSpaceName} / ${formattedDocTitle} /`)

    // Set content based on document type
    if (documentId === "system-overview") {
      // System Overview document - High-level architecture
      setContent(`
<h1 style="color: #1e40af; font-size: 32px; margin-bottom: 20px;">System Architecture Overview</h1>

<p style="font-size: 16px; color: #4b5563; margin-bottom: 30px;">
This document provides a comprehensive overview of our microservices-based architecture, designed for scalability, reliability, and maintainability.
</p>

<h2 style="color: #059669; font-size: 24px; margin-top: 30px; margin-bottom: 15px;">System Architecture Diagram</h2>

<div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 30px; overflow-x: auto;">
  <img src="/placeholder.svg?height=500&width=800&query=System%20Architecture%20Diagram%20showing%20microservices%20with%20frontend%20layer%20API%20gateway%20microservices%20and%20databases" alt="System Architecture Diagram" style="width: 100%; max-width: 800px; height: auto; margin: 0 auto; display: block;" />
</div>

<p style="font-size: 14px; color: #64748b; font-style: italic; text-align: center; margin-bottom: 30px;">
  Figure 1: High-level architecture diagram showing the interaction between system components
</p>

<h2 style="color: #059669; font-size: 24px; margin-top: 30px; margin-bottom: 15px;">Architecture Layers</h2>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">1. Presentation Layer</h3>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>Web Application:</strong> React-based SPA with Next.js for SSR/SSG</li>
  <li><strong>Mobile Applications:</strong> React Native for iOS and Android</li>
  <li><strong>Admin Dashboard:</strong> Vue.js-based internal management system</li>
</ul>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">2. API Gateway Layer</h3>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>Kong API Gateway:</strong> Handles routing, authentication, rate limiting</li>
  <li><strong>GraphQL Federation:</strong> Apollo Federation for unified API</li>
  <li><strong>WebSocket Server:</strong> Real-time communication using Socket.io</li>
</ul>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">3. Microservices Layer</h3>
<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
  <tr>
    <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: left; background-color: #f3f4f6;">Service</th>
    <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: left; background-color: #f3f4f6;">Technology</th>
    <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: left; background-color: #f3f4f6;">Purpose</th>
  </tr>
  <tr>
    <td style="border: 1px solid #e5e7eb; padding: 12px;"><strong>Auth Service</strong></td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Node.js + Express</td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">JWT authentication, OAuth2, user management</td>
  </tr>
  <tr>
    <td style="border: 1px solid #e5e7eb; padding: 12px;"><strong>User Service</strong></td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Java Spring Boot</td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">User profiles, preferences, permissions</td>
  </tr>
  <tr>
    <td style="border: 1px solid #e5e7eb; padding: 12px;"><strong>Payment Service</strong></td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Python FastAPI</td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Payment processing, billing, invoicing</td>
  </tr>
  <tr>
    <td style="border: 1px solid #e5e7eb; padding: 12px;"><strong>Notification Service</strong></td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Go + Gin</td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Email, SMS, push notifications</td>
  </tr>
  <tr>
    <td style="border: 1px solid #e5e7eb; padding: 12px;"><strong>Analytics Service</strong></td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Python + Apache Spark</td>
    <td style="border: 1px solid #e5e7eb; padding: 12px;">Data processing, reporting, ML pipelines</td>
  </tr>
</table>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">4. Data Layer</h3>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>PostgreSQL:</strong> Primary relational database for transactional data</li>
  <li><strong>MongoDB:</strong> Document store for flexible schemas</li>
  <li><strong>Redis:</strong> In-memory cache and session storage</li>
  <li><strong>Elasticsearch:</strong> Full-text search and log aggregation</li>
  <li><strong>Apache Kafka:</strong> Event streaming and message queuing</li>
</ul>

<h2 style="color: #059669; font-size: 24px; margin-top: 30px; margin-bottom: 15px;">Infrastructure Components</h2>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">Container Orchestration</h3>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>Kubernetes (EKS):</strong> Container orchestration and management</li>
  <li><strong>Docker:</strong> Containerization of all services</li>
  <li><strong>Helm:</strong> Kubernetes package management</li>
</ul>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">CI/CD Pipeline</h3>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>GitLab CI:</strong> Source control and CI/CD automation</li>
  <li><strong>ArgoCD:</strong> GitOps continuous deployment</li>
  <li><strong>SonarQube:</strong> Code quality and security scanning</li>
</ul>

<h3 style="color: #0891b2; font-size: 20px; margin-top: 25px; margin-bottom: 10px;">Monitoring & Observability</h3>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>Prometheus + Grafana:</strong> Metrics collection and visualization</li>
  <li><strong>ELK Stack:</strong> Centralized logging (Elasticsearch, Logstash, Kibana)</li>
  <li><strong>Jaeger:</strong> Distributed tracing</li>
  <li><strong>PagerDuty:</strong> Incident management and alerting</li>
</ul>

<h2 style="color: #059669; font-size: 24px; margin-top: 30px; margin-bottom: 15px;">Security Architecture</h2>
<ul style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>WAF (Cloudflare):</strong> Web application firewall and DDoS protection</li>
  <li><strong>Vault (HashiCorp):</strong> Secrets management and encryption</li>
  <li><strong>OWASP ZAP:</strong> Security testing automation</li>
  <li><strong>TLS 1.3:</strong> End-to-end encryption for all communications</li>
</ul>

<h2 style="color: #059669; font-size: 24px; margin-top: 30px; margin-bottom: 15px;">Key Design Principles</h2>
<ol style="margin-left: 20px; margin-bottom: 20px;">
  <li><strong>Microservices Architecture:</strong> Loosely coupled, independently deployable services</li>
  <li><strong>Event-Driven Design:</strong> Asynchronous communication via Kafka</li>
  <li><strong>API-First Development:</strong> All features exposed through well-documented APIs</li>
  <li><strong>Infrastructure as Code:</strong> Terraform for infrastructure provisioning</li>
  <li><strong>Zero-Trust Security:</strong> Every request is authenticated and authorized</li>
</ol>

<div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-top: 30px;">
  <p style="margin: 0;"><strong>Note:</strong> This architecture supports 10M+ daily active users with 99.99% uptime SLA. All components are designed for horizontal scalability and fault tolerance.</p>
</div>
`)
      setDocumentFormat("standard")
    } else if (documentId === "qa") {
      setDocumentFormat("qa")
    } else {
      // Default content for other documents
      setContent(`
<div style="text-align: center;">
  <h1 style="font-size: 28px; color: #10b981; margin-bottom: 10px;">Rich text editor</h1>
</div>
`)
    }
  }, [documentId, spaceId])

  const handleSave = () => {
    setIsSaving(true)

    // Simulate saving
    setTimeout(() => {
      setIsSaving(false)
      setLastSaved(new Date())
    }, 1000)
  }

  // If this is a Q&A document, render the Q&A page
  if (documentFormat === "qa") {
    return <QADocumentPage />
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <SageBaseLogo size={32} variant="icon" />
              </Link>
              <nav className="hidden md:flex items-center">
                <Button variant="ghost" className="text-sm font-normal text-gray-600">
                  {documentPath}
                </Button>
              </nav>
            </div>

            <div className="flex items-center space-x-2">
              <Link href="/">
                <Button variant="outline" size="sm" className="flex items-center">
                  <ArrowLeft className="mr-1.5 h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
              <Button variant="outline" size="sm" className="flex items-center">
                <Share2 className="mr-1.5 h-4 w-4" />
                Share
              </Button>
              <Button
                size="sm"
                className="bg-emerald-600 hover:bg-emerald-700"
                onClick={handleSave}
                disabled={isSaving}
              >
                <Save className="mr-1.5 h-4 w-4" />
                {isSaving ? "Saving..." : "Save"}
              </Button>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Document Info */}
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="w-full">
              <div className="flex items-center text-xs text-gray-500 mb-1">
                <Link href="/" className="hover:text-emerald-600">
                  SageBase
                </Link>
                <ChevronRight className="h-3 w-3 mx-1" />
                <Link href={`/spaces/${spaceId}`} className="capitalize hover:text-emerald-600">
                  {spaceName}
                </Link>
                <ChevronRight className="h-3 w-3 mx-1" />
                <span className="text-gray-700">{documentTitle}</span>
              </div>
              <input
                type="text"
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
                className="text-xl font-bold text-gray-800 border-0 focus:ring-0 focus:outline-none p-0 w-full"
              />
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <span className="bg-gray-200 text-gray-600 px-2 py-0.5 rounded text-xs">DRAFT</span>
                <span className="mx-1.5">•</span>
                <Clock className="h-3 w-3 mr-1" />
                <span>{lastSaved ? `Last saved ${lastSaved.toLocaleTimeString()}` : "Not saved yet"}</span>
                <span className="mx-1.5">•</span>
                <User className="h-3 w-3 mr-1" />
                <span>You</span>
              </div>
            </div>
            <AIAssistButton />
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 overflow-auto">
          <div className="max-w-4xl mx-auto py-8 px-4 mb-20">
            <RichTextEditor initialValue={content} onChange={setContent} />
          </div>
        </div>
      </div>
    </div>
  )
}
