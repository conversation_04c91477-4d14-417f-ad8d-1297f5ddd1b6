"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ArrowLeft,
  Share2,
  MoreHorizontal,
  Clock,
  User,
  ChevronRight,
  MessageSquare,
  CheckCircle,
  Edit3,
  UserCheck,
} from "lucide-react";
import Link from "next/link";
import SideNavigation from "@/components/side-navigation";
import AIAssistButton from "@/components/ai-assist-button";
import SageBaseLogo from "@/components/sagebase-logo";

export default function QADocumentPage() {
  const params = useParams();
  const spaceId = params.space as string;
  const documentId = params.document as string;

  const [documentTitle, setDocumentTitle] = useState("");
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [spaceName, setSpaceName] = useState("");
  const [documentPath, setDocumentPath] = useState("");

  // Q&A data structure
  const [qaData] = useState({
    question: {
      title: "Error: Cannot find module '@babel/preset-env'",
      content:
        "ERROR: \"Error: Cannot find module '@babel/preset-env' - This is a very weird error!\"",
      author: {
        name: "Developer",
        avatar: "/diverse-avatars.png",
      },
      timestamp: "18:53",
      date: "Today",
      tags: ["babel", "npm", "error", "dev-environment", "build-tools"],
    },
    answer: {
      content:
        "Yes, I also had it yesterday, and I've done this command to fix it:",
      code: "npm install --save-dev @babel/preset-env && npm cache clean --force",
      explanation:
        "This is a solution that I found myself after debugging the build process. It seems there's an issue with the babel dependencies not being properly installed or cached incorrectly.",
      author: {
        name: "Wissem",
        avatar: "/diverse-group-avatars.png",
      },
      timestamp: "19:53",
      date: "Today",
    },
    metadata: {
      editedBy: {
        name: "Sarah Johnson",
        avatar: "/diverse-group-avatars.png",
        date: "Today at 20:15",
      },
      approvedBy: {
        name: "Tech Lead",
        avatar: "/diverse-avatars.png",
        date: "Today at 20:30",
      },
      views: 42,
      helpful: 12,
    },
  });

  useEffect(() => {
    // Set document title
    const formattedDocTitle = documentId
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    setDocumentTitle(formattedDocTitle);

    // Set space name based on spaceId
    const formattedSpaceName = spaceId
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    setSpaceName(formattedSpaceName);

    // Set document path to Nova/Tips/QA
    setDocumentPath(`${formattedSpaceName} / Tips / QA /`);
  }, [documentId, spaceId]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <SageBaseLogo size={32} variant="icon" />
              </Link>
              <nav className="hidden md:flex items-center">
                <Button
                  variant="ghost"
                  className="text-sm font-normal text-gray-600"
                >
                  {documentPath}
                </Button>
              </nav>
            </div>

            <div className="flex items-center space-x-2">
              <Link href="/">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-1.5 h-4 w-4" />
                  Back to Dashboard
                </Button>
              </Link>
              <Button variant="outline" size="sm" className="flex items-center">
                <Share2 className="mr-1.5 h-4 w-4" />
                Share
              </Button>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Document Info */}
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="w-full">
              <div className="flex items-center text-xs text-gray-500 mb-1">
                <Link href="/" className="hover:text-primary">
                  SageBase
                </Link>
                <ChevronRight className="h-3 w-3 mx-1" />
                <Link
                  href={`/spaces/${spaceId}`}
                  className="capitalize hover:text-primary"
                >
                  {spaceName}
                </Link>
                <ChevronRight className="h-3 w-3 mx-1" />
                <span className="text-gray-500">Tips</span>
                <ChevronRight className="h-3 w-3 mx-1" />
                <span className="text-gray-700">QA</span>
              </div>
              <input
                type="text"
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
                className="text-xl font-bold text-gray-800 border-0 focus:ring-0 focus:outline-none p-0 w-full"
              />
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <span className="bg-blue-100 text-blue-600 px-2 py-0.5 rounded text-xs">
                  Q&A FORMAT
                </span>
                <span className="mx-1.5">•</span>
                <Clock className="h-3 w-3 mr-1" />
                <span>
                  {lastSaved
                    ? `Last saved ${lastSaved.toLocaleTimeString()}`
                    : "Last updated Today at 20:30"}
                </span>
                <span className="mx-1.5">•</span>
                <User className="h-3 w-3 mr-1" />
                <span>You</span>
                <span className="mx-1.5">•</span>
                <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs">
                  {qaData.metadata.views} views
                </span>
              </div>
            </div>
            <AIAssistButton />
          </div>
        </div>

        {/* Q&A Content */}
        <div className="flex-1 overflow-auto bg-white">
          <div className="max-w-4xl mx-auto py-8 px-4">
            {/* Question Section */}
            <div className="mb-8">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-100 rounded-full p-2">
                      <MessageSquare className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">
                        Question
                      </h2>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <Avatar className="h-5 w-5 mr-1">
                          <AvatarImage
                            src={
                              qaData.question.author.avatar ||
                              "/placeholder.svg"
                            }
                            alt={qaData.question.author.name}
                          />
                          <AvatarFallback>
                            {qaData.question.author.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">
                          {qaData.question.author.name}
                        </span>
                        <span className="mx-2">•</span>
                        <span>
                          {qaData.question.date} at {qaData.question.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold text-gray-800 mb-3">
                  {qaData.question.title}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {qaData.question.content}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {qaData.question.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="bg-blue-100 text-blue-700 hover:bg-blue-200"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Answer Section */}
            <div className="mb-8">
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-green-100 rounded-full p-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">
                        Answer
                      </h2>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <Avatar className="h-5 w-5 mr-1">
                          <AvatarImage
                            src={
                              qaData.answer.author.avatar || "/placeholder.svg"
                            }
                            alt={qaData.answer.author.name}
                          />
                          <AvatarFallback>
                            {qaData.answer.author.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">
                          {qaData.answer.author.name}
                        </span>
                        <span className="mx-2">•</span>
                        <span>
                          {qaData.answer.date} at {qaData.answer.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-green-600 text-white">
                    Verified Answer
                  </Badge>
                </div>

                <p className="text-gray-700 leading-relaxed mb-4">
                  {qaData.answer.content}
                </p>

                {/* Code Block */}
                <div className="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                  <code className="text-green-400 font-mono text-sm whitespace-pre">
                    {qaData.answer.code}
                  </code>
                </div>

                <p className="text-gray-700 leading-relaxed">
                  {qaData.answer.explanation}
                </p>
              </div>
            </div>

            {/* Metadata Section */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Edited By */}
                {qaData.metadata.editedBy && (
                  <div className="flex items-center space-x-3">
                    <Edit3 className="h-4 w-4 text-gray-400" />
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500">Edited by</span>
                      <Avatar className="h-5 w-5 mx-2">
                        <AvatarImage
                          src={
                            qaData.metadata.editedBy.avatar ||
                            "/placeholder.svg"
                          }
                          alt={qaData.metadata.editedBy.name}
                        />
                        <AvatarFallback>
                          {qaData.metadata.editedBy.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium text-gray-700">
                        {qaData.metadata.editedBy.name}
                      </span>
                      <span className="text-gray-500 ml-2">
                        • {qaData.metadata.editedBy.date}
                      </span>
                    </div>
                  </div>
                )}

                {/* Approved By */}
                {qaData.metadata.approvedBy && (
                  <div className="flex items-center space-x-3">
                    <UserCheck className="h-4 w-4 text-gray-400" />
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500">Approved by</span>
                      <Avatar className="h-5 w-5 mx-2">
                        <AvatarImage
                          src={
                            qaData.metadata.approvedBy.avatar ||
                            "/placeholder.svg"
                          }
                          alt={qaData.metadata.approvedBy.name}
                        />
                        <AvatarFallback>
                          {qaData.metadata.approvedBy.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium text-gray-700">
                        {qaData.metadata.approvedBy.name}
                      </span>
                      <span className="text-gray-500 ml-2">
                        • {qaData.metadata.approvedBy.date}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex justify-center space-x-4">
              <Button variant="outline" size="sm">
                Mark as Helpful
              </Button>
              <Button variant="outline" size="sm">
                Add Follow-up Question
              </Button>
              <Button variant="outline" size="sm">
                Share Answer
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
