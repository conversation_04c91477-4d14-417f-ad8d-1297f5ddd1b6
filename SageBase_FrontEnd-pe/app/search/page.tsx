"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import SideNavigation from "@/components/side-navigation"
import TopNavigation from "@/components/top-navigation"
import { searchDocuments, highlightMatches, type SearchResult } from "@/lib/search-service"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Tag, User, FileText, Search, AlertCircle } from "lucide-react"
import Link from "next/link"

export default function SearchPage() {
  const searchParams = useSearchParams()
  const query = searchParams.get("q") || ""
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (query) {
      setIsLoading(true)
      // Simulate network delay
      const timer = setTimeout(() => {
        const searchResults = searchDocuments(query)
        setResults(searchResults)
        setIsLoading(false)
      }, 500)

      return () => clearTimeout(timer)
    } else {
      setResults([])
      setIsLoading(false)
    }
  }, [query])

  // Format date to readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-6xl mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Search Results {query ? `for "${query}"` : ""}</h2>
              {results.length > 0 && (
                <p className="text-gray-600">
                  Found {results.length} {results.length === 1 ? "result" : "results"}
                </p>
              )}
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && results.length === 0 && query && (
              <Card className="bg-white shadow-sm border border-gray-200 rounded-lg p-8 text-center">
                <CardContent className="pt-6 flex flex-col items-center">
                  <div className="bg-gray-100 p-4 rounded-full mb-4">
                    <Search className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    We couldn't find any documents matching "{query}". Try using different keywords or check your
                    spelling.
                  </p>
                  <div className="mt-6 bg-gray-50 rounded-lg p-4 w-full max-w-md">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-primary-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Search tips</h4>
                        <ul className="mt-1 text-sm text-gray-600 list-disc list-inside space-y-1">
                          <li>Check for typos or try alternative spellings</li>
                          <li>Use more general keywords</li>
                          <li>Try searching in specific spaces or by tags</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Search Results */}
            {!isLoading && results.length > 0 && (
              <div className="space-y-4">
                {results.map((result) => (
                  <Link href={`/documents/${result.id}`} key={result.id}>
                    <Card className="bg-white shadow-sm border border-gray-200 hover:border-primary-300 hover:shadow transition-all duration-200">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-1">
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: highlightMatches(result.title, query),
                                }}
                              />
                            </h3>
                            <div className="flex items-center text-sm text-gray-500 mb-3 flex-wrap gap-3">
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                <span>{formatDate(result.createdAt)}</span>
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1" />
                                <span>Updated {formatDate(result.updatedAt)}</span>
                              </div>
                              <div className="flex items-center">
                                <User className="h-4 w-4 mr-1" />
                                <span>{result.author.name}</span>
                              </div>
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-1" />
                                <span>{result.space}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            <Badge variant="outline" className="bg-primary-50 text-primary-700 border-primary-200">
                              {result.space}
                            </Badge>
                          </div>
                        </div>

                        <div className="text-sm text-gray-600 mb-3 bg-gray-50 p-3 rounded-md">
                          <span
                            dangerouslySetInnerHTML={{
                              __html: highlightMatches(result.highlightedContent, query),
                            }}
                          />
                        </div>

                        <div className="flex flex-wrap gap-2">
                          {result.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              <Tag className="h-3 w-3 mr-1" />
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: highlightMatches(tag, query),
                                }}
                              />
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
