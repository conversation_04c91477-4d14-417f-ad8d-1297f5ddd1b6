import SideNavigation from "@/components/side-navigation"
import TopNavigation from "@/components/top-navigation"

export default function SearchLoading() {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-6xl mx-auto">
            <div className="mb-6">
              <div className="h-8 w-64 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="h-5 w-32 bg-gray-200 rounded animate-pulse"></div>
            </div>

            {/* Loading Skeleton */}
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
                  <div className="flex items-start justify-between">
                    <div className="w-full">
                      <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse mb-3"></div>
                      <div className="flex flex-wrap gap-3 mb-4">
                        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                      <div className="h-20 w-full bg-gray-200 rounded animate-pulse mb-4"></div>
                      <div className="flex flex-wrap gap-2">
                        <div className="h-5 w-16 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-5 w-20 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-5 w-14 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="h-6 w-20 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
