"use client";
import { useAuth } from "@/contexts/auth-context";
import { useEffect, useState } from "react";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Clock,
  FilePlus2,
  AlertTriangle,
  Activity,
  Zap,
  Download,
  RefreshCw,
  BarChart3,
  Target,
  AlertCircle,
  HelpCircle,
  Filter,
  CopyIcon as CopyWarning,
} from "lucide-react";
import {
  ResponsiveContaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts";
import RobotHealthIndicator from "@/components/robot-health-indicator";

// Data for different periods
const dataByPeriod = {
  "7d": {
    teamProductivity: [
      { day: "Mon", hoursSaved: 8, questionsAsked: 12 },
      { day: "Tue", hoursSaved: 6, questionsAsked: 15 },
      { day: "Wed", hoursSaved: 9, questionsAsked: 8 },
      { day: "Thu", hoursSaved: 7, questionsAsked: 18 },
      { day: "Fri", hoursSaved: 12, questionsAsked: 17 },
    ],
    metrics: {
      totalHours: 42,
      questionsAnswered: "64/70",
      newDocuments: 5,
      updatedDocuments: 12,
      autoAnswered: 45,
      humanAnswered: 19,
      unanswered: 6,
    },
    questionDistribution: [
      { name: "Infrastructure/General", value: 25, color: "#8884d8" },
      { name: "Project Gamma", value: 20, color: "#82ca9d" },
      { name: "Project Alpha", value: 15, color: "#ffc658" },
      { name: "Project Beta", value: 10, color: "#ff7300" },
    ],
  },
  "30d": {
    teamProductivity: [
      { day: "Week 1", hoursSaved: 35, questionsAsked: 48 },
      { day: "Week 2", hoursSaved: 42, questionsAsked: 52 },
      { day: "Week 3", hoursSaved: 38, questionsAsked: 45 },
      { day: "Week 4", hoursSaved: 51, questionsAsked: 58 },
    ],
    metrics: {
      totalHours: 166,
      questionsAnswered: "203/220",
      newDocuments: 22,
      updatedDocuments: 58,
      autoAnswered: 178,
      humanAnswered: 25,
      unanswered: 17,
    },
    questionDistribution: [
      { name: "Infrastructure/General", value: 85, color: "#8884d8" },
      { name: "Project Gamma", value: 72, color: "#82ca9d" },
      { name: "Project Alpha", value: 48, color: "#ffc658" },
      { name: "Project Beta", value: 35, color: "#ff7300" },
    ],
  },
  "90d": {
    teamProductivity: [
      { day: "Month 1", hoursSaved: 145, questionsAsked: 185 },
      { day: "Month 2", hoursSaved: 162, questionsAsked: 198 },
      { day: "Month 3", hoursSaved: 178, questionsAsked: 212 },
    ],
    metrics: {
      totalHours: 485,
      questionsAnswered: "595/620",
      newDocuments: 75,
      updatedDocuments: 180,
      autoAnswered: 542,
      humanAnswered: 53,
      unanswered: 25,
    },
    questionDistribution: [
      { name: "Infrastructure/General", value: 245, color: "#8884d8" },
      { name: "Project Gamma", value: 198, color: "#82ca9d" },
      { name: "Project Alpha", value: 152, color: "#ffc658" },
      { name: "Project Beta", value: 125, color: "#ff7300" },
    ],
  },
};

const qaData = [
  { month: "Week 1", captured: 8, documented: 6 },
  { month: "Week 2", captured: 12, documented: 9 },
  { month: "Week 3", captured: 15, documented: 8 },
  { month: "Week 4", captured: 37, documented: 25 },
];

const questionResolutionData = [
  { type: "Auto-answered", value: 45, color: "#10b981" },
  { type: "Human-answered", value: 19, color: "#3b82f6" },
  { type: "Unanswered", value: 6, color: "#ef4444" },
];

// Custom tooltip components
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium">{`${label}`}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const PieTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium">{`${payload[0].name}: ${payload[0].value} questions`}</p>
      </div>
    );
  }
  return null;
};

export default function DashboardPage() {
  const { user, isLoading, isGuest } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<"7d" | "30d" | "90d">(
    "7d"
  );
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Get connected platforms to monitor for changes
  const { platforms, refreshPlatforms } = useConnectedPlatforms();

  const currentData = dataByPeriod[selectedPeriod];

  // Monitor for platform connection changes and refresh dashboard
  useEffect(() => {
    const handlePlatformChange = () => {
      console.log("🔄 Platform connection changed, refreshing dashboard...");
      setLastRefresh(new Date());
      // You can add additional dashboard refresh logic here
      // For example, refetch analytics data, update metrics, etc.
    };

    // Listen for storage events (when platforms are connected/disconnected)
    const handleStorageEvent = (event: StorageEvent) => {
      if (
        event.key === "github-connected" ||
        event.key === "slack-connected" ||
        event.key === "teams-connected" ||
        event.key?.includes("platform-connected")
      ) {
        handlePlatformChange();
      }
    };

    // Listen for custom platform connection events
    const handlePlatformConnection = (event: Event) => {
      if (
        event.type === "platform-connected" ||
        event.type === "platform-disconnected"
      ) {
        handlePlatformChange();
      }
    };

    // Add event listeners
    window.addEventListener("storage", handleStorageEvent);
    window.addEventListener("platform-connected", handlePlatformConnection);
    window.addEventListener("platform-disconnected", handlePlatformConnection);

    // Cleanup
    return () => {
      window.removeEventListener("storage", handleStorageEvent);
      window.removeEventListener(
        "platform-connected",
        handlePlatformConnection
      );
      window.removeEventListener(
        "platform-disconnected",
        handlePlatformConnection
      );
    };
  }, []);

  // Refresh platforms when dashboard mounts or when needed
  useEffect(() => {
    refreshPlatforms();
  }, [refreshPlatforms]);

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case "7d":
        return "Last 7 days";
      case "30d":
        return "Last 30 days";
      case "90d":
        return "Last 90 days";
    }
  };

  // If still loading, show a loading indicator
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-6 pb-12">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Knowledge Dashboard(!Simulation-Mode)
                </h1>
                <p className="text-gray-600 mt-1">
                  Technical knowledge management overview
                  {lastRefresh && (
                    <span className="text-xs text-blue-600 ml-2">
                      • Last refreshed: {lastRefresh.toLocaleTimeString()}
                    </span>
                  )}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Select
                  value={selectedPeriod}
                  onValueChange={(value: "7d" | "30d" | "90d") =>
                    setSelectedPeriod(value)
                  }
                >
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Knowledge Base Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="border-0">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-base font-medium text-gray-900">
                    Knowledge Base Health
                  </CardTitle>
                  <Activity className="h-5 w-5 text-gray-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-4xl font-bold text-green-600">
                    {Math.round(
                      ((currentData.metrics.autoAnswered +
                        currentData.metrics.humanAnswered) /
                        (currentData.metrics.autoAnswered +
                          currentData.metrics.humanAnswered +
                          currentData.metrics.unanswered)) *
                        100
                    )}
                    %
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    {currentData.metrics.autoAnswered +
                      currentData.metrics.humanAnswered}{" "}
                    of{" "}
                    {currentData.metrics.autoAnswered +
                      currentData.metrics.humanAnswered +
                      currentData.metrics.unanswered}{" "}
                    questions resolved
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-base font-medium text-gray-900">
                    Documentation Activity
                  </CardTitle>
                  <FilePlus2 className="h-5 w-5 text-gray-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-4xl font-bold text-blue-600">
                    {currentData.metrics.newDocuments +
                      currentData.metrics.updatedDocuments}
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    {currentData.metrics.newDocuments} new,{" "}
                    {currentData.metrics.updatedDocuments} updated
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 bg-red-50">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-base font-medium text-gray-900">
                    Unanswered Questions
                  </CardTitle>
                  <AlertCircle className="h-5 w-5 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-4xl font-bold text-red-600">
                    {currentData.metrics.unanswered}
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    Requires immediate attention
                  </p>
                  <div className="mt-4">
                    <Button variant="destructive" size="sm" className="w-full">
                      Review Questions
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Question-Topics Breakdown and Most Repeated Questions */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-0">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2" />
                    Question-Topics Breakdown
                  </CardTitle>
                  <CardDescription>
                    Distribution of questions by topic ({getPeriodLabel()})
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={currentData.questionDistribution}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, value }) => `${name}: ${value}`}
                        >
                          {currentData.questionDistribution.map(
                            (entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            )
                          )}
                        </Pie>
                        <Tooltip content={<PieTooltip />} />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Most Repeated Questions
                  </CardTitle>
                  <CardDescription>
                    Top questions that need documentation ({getPeriodLabel()})
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">Backend Team</p>
                        <p className="text-xs text-gray-600">
                          "How do I deploy the backend?"
                        </p>
                      </div>
                      <Badge variant="destructive">
                        {selectedPeriod === "7d"
                          ? "6"
                          : selectedPeriod === "30d"
                          ? "24"
                          : "72"}{" "}
                        times
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">Frontend Team</p>
                        <p className="text-xs text-gray-600">
                          "Where is the latest API key?"
                        </p>
                      </div>
                      <Badge
                        variant="secondary"
                        className="bg-yellow-400 hover:bg-yellow-500 text-yellow-950"
                      >
                        {selectedPeriod === "7d"
                          ? "4"
                          : selectedPeriod === "30d"
                          ? "16"
                          : "48"}{" "}
                        times
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">DevOps</p>
                        <p className="text-xs text-gray-600">
                          "CI/CD pipeline configuration"
                        </p>
                      </div>
                      <Badge variant="outline">
                        {selectedPeriod === "7d"
                          ? "3"
                          : selectedPeriod === "30d"
                          ? "12"
                          : "36"}{" "}
                        times
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
