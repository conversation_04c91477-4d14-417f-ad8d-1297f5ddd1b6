"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  Search,
  Plus,
  Folder,
  FileText,
  ChevronRight,
  Eye,
  Settings,
  Clock,
  Filter,
  Loader2,
  Lock,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { useSpaces } from "@/hooks/use-spaces";
import { Space, SpaceItem } from "@/services/spaces-api";

export default function SpacesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");

  // Use the spaces hook for dynamic data
  const { spaces, isLoading, error, fetchSpaces } = useSpaces();

  const filteredSpaces = spaces.filter((space) => {
    if (searchQuery) {
      return space.name.toLowerCase().includes(searchQuery.toLowerCase());
    }
    return true;
  });

  const getTextColor = (bgColor: string) => {
    if (bgColor.includes("primary")) return "text-primary-600";
    if (bgColor.includes("blue")) return "text-blue-600";
    if (bgColor.includes("green")) return "text-green-600";
    if (bgColor.includes("orange")) return "text-orange-600";
    if (bgColor.includes("purple")) return "text-purple-600";
    if (bgColor.includes("red")) return "text-red-600";
    if (bgColor.includes("yellow")) return "text-yellow-600";
    if (bgColor.includes("pink")) return "text-pink-600";
    return "text-gray-600";
  };

  const handleCreateSpace = () => {
    // In a real app, this would open a modal to create a new space
    console.log("Create new space");
  };

  const countDocuments = (items: SpaceItem[]): number => {
    let count = 0;
    items.forEach((item) => {
      if (item.type === "document") {
        count++;
      } else if (item.type === "folder" && item.children) {
        count += item.children.length;
      }
    });
    return count;
  };

  const countFolders = (items: SpaceItem[]): number => {
    return items.filter((item) => item.type === "folder").length;
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading spaces...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Error Loading Spaces
              </h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchSpaces}>Try Again</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Side Navigation */}
      <SideNavigation />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopNavigation />

        <div className="flex-1 overflow-auto">
          {/* Hero Section with KB Background */}
          <div
            className="relative border-b border-gray-200 overflow-hidden min-h-[300px]"
            style={{
              backgroundImage: "url(/images/kb-background.jpg)",
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              backgroundAttachment: "scroll",
            }}
          >
            {/* Overlay for better text readability */}
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>

            <div className="relative z-10 container mx-auto py-16 px-4 max-w-7xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-8">
                  <div className="relative">
                    <Image
                      src="/images/branding/sphere-blue.png"
                      alt="Knowledge Spaces"
                      width={140}
                      height={140}
                      className="drop-shadow-2xl"
                      priority
                    />
                  </div>
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-3 drop-shadow-lg">
                      Knowledge Spaces
                    </h1>
                    <p className="text-xl text-white/90 drop-shadow-md">
                      Organize and explore your team's collective knowledge
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="container mx-auto py-8 px-4 max-w-7xl">
            <div className="flex flex-col space-y-8">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="relative w-full md:w-64">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="search"
                      placeholder="Search spaces..."
                      className="pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setActiveFilter("all")}>
                        All Spaces
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setActiveFilter("recent")}
                      >
                        Recently Updated
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setActiveFilter("most-docs")}
                      >
                        Most Documents
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Button onClick={handleCreateSpace}>
                    <Plus className="h-4 w-4 mr-2" />
                    New Space
                  </Button>
                </div>
              </div>

              <Tabs defaultValue="grid" className="w-full">
                <div className="flex justify-between items-center mb-6">
                  <TabsList>
                    <TabsTrigger value="grid">Grid View</TabsTrigger>
                    <TabsTrigger value="list">List View</TabsTrigger>
                  </TabsList>
                  <div className="text-sm text-gray-500">
                    {filteredSpaces.length}{" "}
                    {filteredSpaces.length === 1 ? "space" : "spaces"}
                  </div>
                </div>

                <TabsContent value="grid" className="mt-0">
                  {filteredSpaces.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <Folder className="h-12 w-12 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No spaces found
                      </h3>
                      <p className="text-gray-500 mb-4">
                        {searchQuery
                          ? "Try adjusting your search criteria."
                          : "Create your first knowledge space to get started."}
                      </p>
                      {!searchQuery && (
                        <Button onClick={handleCreateSpace}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create First Space
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredSpaces.map((space) => (
                        <TooltipProvider key={space.id}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Card
                                className="overflow-hidden hover:shadow-lg transition-all duration-300 group"
                              >
                          <CardHeader className="pb-3 relative">
                            <div className="flex justify-between items-start relative z-10">
                                                                <div className="flex items-center">
                                    <div className="relative mr-3">
                                      {space.brandImage ? (
                                        <Image
                                          src={
                                            space.brandImage || "/placeholder.svg"
                                          }
                                          alt={space.name}
                                          width={40}
                                          height={40}
                                          className="rounded-md"
                                        />
                                      ) : (
                                        <div
                                          className={`w-10 h-10 ${space.color} rounded-md flex items-center justify-center relative`}
                                        >
                                          <span
                                            className={`${getTextColor(
                                              space.color
                                            )} text-lg font-medium`}
                                          >
                                            {space.initial}
                                          </span>
                                          {space.private && (
                                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                                              <Lock className="h-2.5 w-2.5 text-white" />
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                <div>
                                  <CardTitle className="text-xl">
                                    {space.name}
                                  </CardTitle>
                                  <CardDescription className="flex items-center mt-1">
                                    <Clock className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                    <span>Updated {space.lastUpdated}</span>
                                  </CardDescription>
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  router.push(`/spaces/${space.id}`)
                                }
                              >
                                <ChevronRight className="h-5 w-5" />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className="pb-3">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center text-gray-600">
                                <FileText className="h-4 w-4 mr-1.5 text-gray-400" />
                                <span>{space.documentCount} documents</span>
                              </div>
                              <div className="flex items-center text-gray-600">
                                <Folder className="h-4 w-4 mr-1.5 text-gray-400" />
                                <span>{space.folderCount} folders</span>
                              </div>
                            </div>
                          </CardContent>
                          <CardFooter className="pt-2 border-t flex justify-between">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-primary-600"
                              asChild
                            >
                              <Link href={`/spaces/${space.id}/qas`}>
                                <Eye className="h-4 w-4 mr-1.5" />
                                View Q&As
                              </Link>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-600"
                              asChild
                            >
                              <Link href={`/spaces/${space.id}/settings`}>
                                <Settings className="h-4 w-4 mr-1.5" />
                                Settings
                              </Link>
                            </Button>
                          </CardFooter>
                        </Card>
                            </TooltipTrigger>
                            {space.private && (
                              <TooltipContent className="max-w-xs bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200 shadow-lg">
                                <div className="flex items-start space-x-2">
                                  <div className="flex-shrink-0 mt-0.5">
                                    <div className="w-6 h-6 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                                      <Lock className="h-3 w-3 text-white" />
                                    </div>
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-1 mb-1">
                                      <span className="text-xs font-semibold text-purple-700 uppercase tracking-wide">
                                        Private Space
                                      </span>
                                    </div>
                                    <p className="text-xs text-gray-700 leading-relaxed">
                                      This is your private Space, only you has access to its content.
                                    </p>
                                  </div>
                                </div>
                              </TooltipContent>
                            )}
                          </Tooltip>
                        </TooltipProvider>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="list" className="mt-0">
                  <div className="bg-white rounded-lg border overflow-hidden">
                    <div className="grid grid-cols-12 gap-4 p-4 border-b bg-gray-50 font-medium text-gray-500 text-sm">
                      <div className="col-span-5">Name</div>
                      <div className="col-span-2">Documents</div>
                      <div className="col-span-2">Folders</div>
                      <div className="col-span-2">Last Updated</div>
                      <div className="col-span-1"></div>
                    </div>
                    {filteredSpaces.map((space) => (
                      <div
                        key={space.id}
                        className="grid grid-cols-12 gap-4 p-4 border-b hover:bg-gray-50 transition-colors items-center"
                      >
                        <div className="col-span-5 flex items-center">
                          <div className="relative mr-3">
                            {space.brandImage ? (
                              <Image
                                src={space.brandImage || "/placeholder.svg"}
                                alt={space.name}
                                width={32}
                                height={32}
                                className="rounded"
                              />
                            ) : (
                              <div
                                className={`w-8 h-8 ${space.color} rounded flex items-center justify-center relative`}
                              >
                                <span
                                  className={`${getTextColor(
                                    space.color
                                  )} text-sm font-medium`}
                                >
                                  {space.initial}
                                </span>
                                {space.private && (
                                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-purple-500 rounded-full flex items-center justify-center">
                                    <Lock className="h-2 w-2 text-white" />
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                          <span className="font-medium">{space.name}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-400" />
                          {space.documentCount}
                        </div>
                        <div className="col-span-2 flex items-center">
                          <Folder className="h-4 w-4 mr-2 text-gray-400" />
                          {space.folderCount}
                        </div>
                        <div className="col-span-2 text-gray-500">
                          {space.lastUpdated}
                        </div>
                        <div className="col-span-1 flex justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              router.push(`/spaces/${space.id}/qas`)
                            }
                          >
                            <Eye className="h-4 w-4 mr-1.5" />
                            View Q&As
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
