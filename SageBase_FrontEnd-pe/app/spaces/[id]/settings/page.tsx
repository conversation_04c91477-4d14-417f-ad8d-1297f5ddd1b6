"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  Settings,
  User,
  Users,
  Edit,
  Save,
  X,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { useToast } from "@/hooks/use-toast";

type Space = {
  id: string;
  name: string;
  color: string;
  initial: string;
  description?: string;
  brandImage?: string;
  doc_responsible?: {
    id: string;
    email: string;
    name: string;
  } | null;
  secondary_responsible?: {
    id: string;
    email: string;
    name: string;
  } | null;
};

type ResponsibleUsers = {
  doc_responsible: {
    id: string;
    email: string;
    name: string;
  } | null;
  secondary_responsible: {
    id: string;
    email: string;
    name: string;
  } | null;
};

type TeamMember = {
  id: string;
  name?: string;
  email: string;
  role?: string;
  first_name?: string;
  last_name?: string;
};

export default function SpaceSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const spaceId = params.id as string;
  const { toast } = useToast();

  const [space, setSpace] = useState<Space | null>(null);
  const [responsibleUsers, setResponsibleUsers] =
    useState<ResponsibleUsers | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Assignment modal state
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState("");
  const [assignmentType, setAssignmentType] = useState<"main" | "secondary">(
    "main"
  );
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [modalError, setModalError] = useState<string | null>(null);

  // Helper function to check if a user is already assigned to the other role
  const isUserAlreadyAssigned = (
    userId: string,
    currentAssignmentType: "main" | "secondary"
  ) => {
    if (currentAssignmentType === "main") {
      return responsibleUsers?.secondary_responsible?.id === userId;
    } else {
      return responsibleUsers?.doc_responsible?.id === userId;
    }
  };

  // Fetch space information
  const fetchSpaceData = async () => {
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      // Fetch space information
      const spaceResponse = await fetch(
        `${API_BASE_URL}/api/knowledge-spaces`,
        { headers }
      );
      if (spaceResponse.ok) {
        const spacesData = await spaceResponse.json();
        const spacesArray = spacesData.data || spacesData;
        const currentSpace = spacesArray.find((s: any) => s.id === spaceId);
        if (currentSpace) {
          setSpace(currentSpace);
          // Set responsible users from the space data
          if (
            currentSpace.doc_responsible ||
            currentSpace.secondary_responsible
          ) {
            setResponsibleUsers({
              doc_responsible: currentSpace.doc_responsible || null,
              secondary_responsible: currentSpace.secondary_responsible || null,
            });
          }
        } else {
          throw new Error(`Space not found: ${spaceId}`);
        }
      }

      // Fetch team members
      const teamResponse = await fetch(`${API_BASE_URL}/api/team-members/`, {
        headers,
      });
      if (teamResponse.ok) {
        const data = await teamResponse.json();
        if (data.success) {
          setTeamMembers(data.data || data);
        }
      }
    } catch (error) {
      console.error("Error fetching space data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load space data"
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch team members for assignment
  const fetchTeamMembers = async () => {
    setLoadingUsers(true);
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      // First, get the current user's company ID
      const userResponse = await fetch(
        `${API_BASE_URL}/api/integrations/get-user-by-email/`,
        { headers }
      );

      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.company_id) {
          // Fetch company users using the integrations/company-users endpoint
          const response = await fetch(
            `${API_BASE_URL}/api/integrations/company-users/?company_id=${userData.company_id}`,
            { headers }
          );
          if (response.ok) {
            const data = await response.json();
            setTeamMembers(data);
          } else {
            console.error("Failed to fetch team members:", response.status);
          }
        } else {
          console.error("No company ID found for current user");
        }
      } else {
        console.error("Failed to fetch user info:", userResponse.status);
      }
    } catch (error) {
      console.error("Error fetching team members:", error);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Handle assignment
  const handleAssignment = async () => {
    if (!selectedMember || !spaceId) return;

    // Check if the selected member is already assigned to the other role
    if (isUserAlreadyAssigned(selectedMember, assignmentType)) {
      const roleName = assignmentType === "main" ? "secondary" : "primary";
      setModalError(
        `This user is already assigned as the ${roleName} assignee. Please choose a different user for the ${assignmentType} assignee role.`
      );
      return;
    }

    setIsUpdating(true);
    setModalError(null); // Clear any previous errors
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      const response = await fetch(
        `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${spaceId}/responsible-users`,
        {
          method: "PUT",
          headers,
          body: JSON.stringify({
            [assignmentType === "main"
              ? "doc_responsible"
              : "secondary_responsible"]: selectedMember,
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Refresh responsible users
          await fetchSpaceData();
          setShowAssignModal(false);
          setSelectedMember("");
          setAssignmentType("main");
          setModalError(null);
          setSuccess(
            `${
              assignmentType === "main" ? "Primary" : "Secondary"
            } assignee updated successfully!`
          );
          toast({
            title: "Success!",
            description: `${
              assignmentType === "main" ? "Primary" : "Secondary"
            } assignee updated successfully!`,
            variant: "success",
          });
        }
      } else {
        throw new Error("Failed to update assignee");
      }
    } catch (error) {
      console.error("Error assigning responsibility:", error);
      setModalError("Failed to update assignee. Please try again.");
      toast({
        title: "Error",
        description: "Failed to update assignee. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    fetchSpaceData();
    fetchTeamMembers(); // Fetch team members when component loads
  }, [spaceId]);

  // Clear success/error messages after 3 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  if (loading) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="flex items-center justify-center h-full">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading settings...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !space) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Error Loading Settings
                </h2>
                <p className="text-gray-600">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      <SideNavigation />
      <div className="flex-1 flex flex-col overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        {/* Enhanced Header */}
        <div className="sticky top-0 z-30 bg-white/90 backdrop-blur-xl border-b border-gray-200/60 shadow-sm">
          <div className="px-6 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/spaces/${spaceId}/qas`)}
                  className="border-gray-300 hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Q&As
                </Button>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="p-3 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-2xl shadow-lg ring-4 ring-blue-100 animate-pulse">
                      <Settings className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                  </div>
                  <div>
                    <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      Knowledge Space Settings
                    </div>
                    <div className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                      {space?.name}
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          <div className="container mx-auto py-8 px-6 max-w-5xl">
            <div className="space-y-8">
              {/* Enhanced Success/Error Messages */}
              {success && (
                <div className="animate-in slide-in-from-top-2 duration-300">
                  <Alert className="bg-gradient-to-r from-green-50 to-emerald-50 text-green-800 border-green-200 shadow-lg">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="font-medium">
                      {success}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {error && (
                <div className="animate-in slide-in-from-top-2 duration-300">
                  <Alert variant="destructive" className="shadow-lg">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="font-medium">
                      {error}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Enhanced Responsible Users Section */}
              <div className="relative">
                {/* Background decoration */}
                <div className="absolute -inset-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-3xl blur-xl"></div>
                <Card className="relative bg-white/80 backdrop-blur-sm border-0 shadow-2xl hover:shadow-3xl transition-all duration-300">
                  <CardHeader className="pb-6">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg animate-pulse">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          Responsible Users
                        </CardTitle>
                        <CardDescription className="text-gray-600 mt-1">
                          Manage the primary and secondary assignees for this
                          knowledge space
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-8">
                    {/* Primary Assignee */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full animate-pulse"></div>
                          <Label className="text-lg font-semibold text-gray-900">
                            Primary Assignee
                          </Label>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setAssignmentType("main");
                            setSelectedMember(
                              responsibleUsers?.doc_responsible?.id || ""
                            );
                            fetchTeamMembers();
                            setShowAssignModal(true);
                            setModalError(null);
                          }}
                          className="text-blue-600 border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          {responsibleUsers?.doc_responsible
                            ? "Change"
                            : "Assign"}
                        </Button>
                      </div>
                      {responsibleUsers?.doc_responsible ? (
                        <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-50 to-blue-100/50 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <div className="relative p-6">
                            <div className="flex items-center gap-4">
                              <div className="relative">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center text-white font-bold shadow-lg ring-4 ring-blue-100 animate-pulse">
                                  {(() => {
                                    const user =
                                      responsibleUsers?.doc_responsible;
                                    if (user?.name) {
                                      return user.name
                                        .split(" ")
                                        .map((n: string) => n[0])
                                        .join("")
                                        .toUpperCase();
                                    } else if (user?.email) {
                                      return user.email.charAt(0).toUpperCase();
                                    } else {
                                      return "U";
                                    }
                                  })()}
                                </div>
                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-400 rounded-full border-2 border-white animate-pulse"></div>
                              </div>
                              <div className="flex-1">
                                <p className="font-bold text-lg text-blue-900">
                                  {responsibleUsers?.doc_responsible?.name ||
                                    responsibleUsers?.doc_responsible?.email ||
                                    "Loading..."}
                                </p>
                                <p className="text-blue-600 font-medium">
                                  Primary Assignee
                                </p>
                                <div className="flex items-center gap-2 mt-1">
                                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                  <span className="text-sm text-blue-600">
                                    Documentation Lead
                                  </span>
                                </div>
                                {responsibleUsers?.doc_responsible?.email && (
                                  <p className="text-sm text-gray-500 mt-1">
                                    {responsibleUsers.doc_responsible.email}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100/50 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                          <div className="absolute inset-0 bg-gradient-to-r from-gray-400/10 to-gray-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <div className="relative p-6">
                            <div className="flex items-center gap-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-gray-300 to-gray-400 rounded-2xl flex items-center justify-center text-gray-500 shadow-lg">
                                <User className="w-6 h-6" />
                              </div>
                              <div>
                                <p className="font-semibold text-gray-700">
                                  No primary assignee assigned
                                </p>
                                <p className="text-gray-500 text-sm">
                                  Click "Assign" to set a primary assignee
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Secondary Assignee */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full animate-pulse"></div>
                          <Label className="text-lg font-semibold text-gray-900">
                            Secondary Assignee
                          </Label>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setAssignmentType("secondary");
                            setSelectedMember(
                              responsibleUsers?.secondary_responsible?.id || ""
                            );
                            fetchTeamMembers();
                            setShowAssignModal(true);
                            setModalError(null);
                          }}
                          className="text-purple-600 border-purple-300 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          {responsibleUsers?.secondary_responsible
                            ? "Change"
                            : "Assign"}
                        </Button>
                      </div>
                      {responsibleUsers?.secondary_responsible ? (
                        <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-purple-50 to-purple-100/50 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                          <div className="absolute inset-0 bg-gradient-to-r from-purple-400/10 to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <div className="relative p-6">
                            <div className="flex items-center gap-4">
                              <div className="relative">
                                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold shadow-lg ring-4 ring-purple-100 animate-pulse">
                                  {(() => {
                                    const user =
                                      responsibleUsers?.secondary_responsible;
                                    if (user?.name) {
                                      return user.name
                                        .split(" ")
                                        .map((n: string) => n[0])
                                        .join("")
                                        .toUpperCase();
                                    } else if (user?.email) {
                                      return user.email.charAt(0).toUpperCase();
                                    } else {
                                      return "U";
                                    }
                                  })()}
                                </div>
                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full border-2 border-white animate-pulse"></div>
                              </div>
                              <div className="flex-1">
                                <p className="font-bold text-lg text-purple-900">
                                  {responsibleUsers?.secondary_responsible
                                    ?.name ||
                                    responsibleUsers?.secondary_responsible
                                      ?.email ||
                                    "Loading..."}
                                </p>
                                <p className="text-purple-600 font-medium">
                                  Secondary Assignee
                                </p>
                                <div className="flex items-center gap-2 mt-1">
                                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                                  <span className="text-sm text-purple-600">
                                    Support Role
                                  </span>
                                </div>
                                {responsibleUsers?.secondary_responsible
                                  ?.email && (
                                  <p className="text-sm text-gray-500 mt-1">
                                    {
                                      responsibleUsers.secondary_responsible
                                        .email
                                    }
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100/50 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                          <div className="absolute inset-0 bg-gradient-to-r from-gray-400/10 to-gray-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          <div className="relative p-6">
                            <div className="flex items-center gap-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-gray-300 to-gray-400 rounded-2xl flex items-center justify-center text-gray-500 shadow-lg">
                                <User className="w-6 h-6" />
                              </div>
                              <div>
                                <p className="font-semibold text-gray-700">
                                  No secondary assignee assigned
                                </p>
                                <p className="text-gray-500 text-sm">
                                  Click "Assign" to set a secondary assignee
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Assignment Modal */}
        {showAssignModal && (
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-200"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowAssignModal(false);
                setSelectedMember("");
                setAssignmentType("main");
                setModalError(null);
              }
            }}
          >
            <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md mx-4 animate-in slide-in-from-bottom-4 duration-300">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Assign {assignmentType === "main" ? "Primary" : "Secondary"}{" "}
                    Assignee
                  </h2>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    setShowAssignModal(false);
                    setSelectedMember("");
                    setAssignmentType("main");
                    setModalError(null);
                  }}
                  className="h-12 w-12 hover:bg-gray-100 rounded-xl border border-gray-300 shadow-sm bg-white hover:border-gray-400 transition-all duration-200 hover:scale-105"
                >
                  <X className="h-6 w-6 text-gray-600" />
                </Button>
              </div>

              <div className="p-6">
                <div className="mb-6">
                  <Label className="text-sm font-semibold text-gray-700">
                    Select {assignmentType === "main" ? "Primary" : "Secondary"}{" "}
                    Assignee
                  </Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Choose a team member to assign as{" "}
                    {assignmentType === "main" ? "primary" : "secondary"}{" "}
                    assignee
                  </p>
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-700">
                      <strong>Note:</strong> A user cannot be assigned to both
                      primary and secondary roles simultaneously.
                    </p>
                  </div>
                </div>

                {/* Modal Error Display */}
                {modalError && (
                  <div className="mb-6 animate-in slide-in-from-top-2 duration-300">
                    <Alert variant="destructive" className="shadow-lg">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="font-medium">
                        {modalError}
                      </AlertDescription>
                    </Alert>
                  </div>
                )}

                <div className="space-y-3 mb-6 max-h-64 overflow-y-auto">
                  {loadingUsers ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-3">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span className="text-gray-600 font-medium">
                          Loading users...
                        </span>
                      </div>
                    </div>
                  ) : teamMembers.length > 0 ? (
                    teamMembers.map((member) => {
                      // Handle the new data structure with first_name and last_name
                      const fullName =
                        member.first_name && member.last_name
                          ? `${member.first_name} ${member.last_name}`.trim()
                          : member.first_name ||
                            member.last_name ||
                            member.email;

                      const initials =
                        member.first_name && member.last_name
                          ? `${member.first_name.charAt(
                              0
                            )}${member.last_name.charAt(0)}`.toUpperCase()
                          : member.first_name
                          ? member.first_name.charAt(0).toUpperCase()
                          : member.last_name
                          ? member.last_name.charAt(0).toUpperCase()
                          : member.email?.charAt(0).toUpperCase() || "U";

                      // Check if this member is already assigned to the other role
                      const isAlreadyAssigned = isUserAlreadyAssigned(
                        member.id,
                        assignmentType
                      );

                      return (
                        <label
                          key={member.id}
                          className={`group relative flex items-center gap-4 p-4 border-2 rounded-2xl cursor-pointer transition-all duration-200 hover:shadow-lg ${
                            selectedMember === member.id
                              ? "bg-gradient-to-r from-blue-50 to-purple-50 border-blue-300 shadow-lg"
                              : isAlreadyAssigned
                              ? "bg-gray-50 border-gray-200 opacity-50 cursor-not-allowed"
                              : "bg-white border-gray-200 hover:border-blue-200"
                          }`}
                        >
                          <input
                            type="radio"
                            name="assignee"
                            value={member.id}
                            checked={selectedMember === member.id}
                            onChange={(e) => {
                              if (!isAlreadyAssigned) {
                                setSelectedMember(e.target.value);
                                setModalError(null); // Clear error when user selects a valid member
                              }
                            }}
                            className="sr-only"
                            disabled={isAlreadyAssigned}
                          />
                          <div className="relative">
                            <div
                              className={`w-12 h-12 rounded-2xl flex items-center justify-center text-white font-bold shadow-lg transition-all duration-200 ${
                                selectedMember === member.id
                                  ? "bg-gradient-to-br from-blue-400 to-purple-500 ring-4 ring-blue-100"
                                  : isAlreadyAssigned
                                  ? "bg-gradient-to-br from-gray-300 to-gray-400"
                                  : "bg-gradient-to-br from-gray-400 to-gray-500"
                              }`}
                            >
                              {initials}
                            </div>
                            {selectedMember === member.id && (
                              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                            )}
                            {isAlreadyAssigned && (
                              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gray-400 rounded-full border-2 border-white">
                                <X className="w-2 h-2 text-white mx-auto mt-0.5" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <span
                              className={`font-semibold block ${
                                isAlreadyAssigned
                                  ? "text-gray-400"
                                  : "text-gray-900"
                              }`}
                            >
                              {fullName}
                              {isAlreadyAssigned && (
                                <span className="ml-2 text-xs text-gray-400 font-normal">
                                  (Already assigned as{" "}
                                  {assignmentType === "main"
                                    ? "secondary"
                                    : "primary"}{" "}
                                  assignee)
                                </span>
                              )}
                            </span>
                            {member.email && fullName !== member.email && (
                              <p
                                className={`text-sm ${
                                  isAlreadyAssigned
                                    ? "text-gray-300"
                                    : "text-gray-500"
                                }`}
                              >
                                {member.email}
                              </p>
                            )}
                            {member.role && (
                              <p
                                className={`text-sm ${
                                  isAlreadyAssigned
                                    ? "text-gray-300"
                                    : "text-gray-500"
                                }`}
                              >
                                {member.role}
                              </p>
                            )}
                          </div>
                          {selectedMember === member.id &&
                            !isAlreadyAssigned && (
                              <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                                <CheckCircle className="w-4 h-4 text-white" />
                              </div>
                            )}
                        </label>
                      );
                    })
                  ) : (
                    <div className="text-center py-8">
                      <div className="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Users className="w-8 h-8 text-gray-400" />
                      </div>
                      <p className="text-gray-500 font-medium">
                        No users found
                      </p>
                      <p className="text-gray-400 text-sm">
                        Please add users in the Admin section
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex gap-3">
                  <Button
                    onClick={() => {
                      setShowAssignModal(false);
                      setSelectedMember("");
                      setAssignmentType("main");
                      setModalError(null);
                    }}
                    variant="outline"
                    className="flex-1 h-12 rounded-xl border-gray-300 hover:bg-gray-50 transition-all duration-200"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAssignment}
                    disabled={
                      !selectedMember ||
                      isUpdating ||
                      isUserAlreadyAssigned(selectedMember, assignmentType)
                    }
                    className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdating ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Updating...
                      </>
                    ) : isUserAlreadyAssigned(
                        selectedMember,
                        assignmentType
                      ) ? (
                      <>
                        <X className="w-4 h-4 mr-2" />
                        Already Assigned
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Assign
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
