"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import {
  Search,
  Plus,
  Folder,
  FileText,
  ChevronLeft,
  Settings,
  Clock,
  MoreHorizontal,
  Edit,
  Download,
  Share,
  Trash2,
  User,
  Calendar,
  Tag,
  Filter,
  Grid,
  List,
  SortAsc,
  SortDesc,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";

type SpaceItem = {
  id: string;
  name: string;
  type: "folder" | "document";
  children?: SpaceItem[];
  lastUpdated?: string;
  author?: string;
  content?: string;
  tags?: string[];
  size?: string;
  parentFolder?: string;
};

type Space = {
  id: string;
  name: string;
  color: string;
  initial: string;
  items: SpaceItem[];
  lastUpdated?: string;
  documentCount?: number;
  folderCount?: number;
  description?: string;
  brandImage?: string;
  docResponsible?: string | null;
  secondaryResponsible?: string | null;
};

type ResponsibleUsers = {
  doc_responsible: string | null;
  secondary_responsible: string | null;
};

export default function SpaceDetailPage() {
  const router = useRouter();
  const params = useParams();
  const spaceId = params.id as string;
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  const [sortBy, setSortBy] = useState<"name" | "date" | "author">("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [space, setSpace] = useState<Space | null>(null);
  const [flattenedItems, setFlattenedItems] = useState<SpaceItem[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<SpaceItem | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [responsibleUsers, setResponsibleUsers] =
    useState<ResponsibleUsers | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState("");
  const [assignmentType, setAssignmentType] = useState<"main" | "secondary">(
    "main"
  );
  const [companyUsers, setCompanyUsers] = useState<any[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Fetch responsible users
  const fetchResponsibleUsers = async () => {
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      const response = await fetch(
        `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${spaceId}/responsible-users`,
        { headers }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setResponsibleUsers(data.data);
        }
      }
    } catch (error) {
      console.error("Error fetching responsible users:", error);
    }
  };

  // Fetch company users
  const fetchCompanyUsers = async () => {
    setLoadingUsers(true);
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      // Get company ID from the current user's company
      const userResponse = await fetch(
        `${API_BASE_URL}/api/integrations/get-user-by-email/`,
        { headers }
      );
      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.company_id) {
          const res = await fetch(
            `${API_BASE_URL}/api/integrations/company-users/?company_id=${userData.company_id}`,
            { headers }
          );
          if (res.ok) {
            const data = await res.json();
            setCompanyUsers(data);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching company users:", error);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Fetch space data
  useEffect(() => {
    const fetchSpaceData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("🔄 Fetching space data for:", spaceId);

        // Get auth headers
        const getAuthHeaders = async (): Promise<HeadersInit> => {
          try {
            const { createClientComponentClient } = await import(
              "@supabase/auth-helpers-nextjs"
            );
            const supabase = createClientComponentClient();
            const {
              data: { session },
            } = await supabase.auth.getSession();

            if (session?.access_token) {
              return {
                "Content-Type": "application/json",
                Authorization: `Bearer ${session.access_token}`,
              };
            }
          } catch (error) {
            console.warn("Failed to get auth token:", error);
          }
          return { "Content-Type": "application/json" };
        };

        const headers = await getAuthHeaders();
        const API_BASE_URL =
          process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

        // Fetch space information
        const spaceResponse = await fetch(
          `${API_BASE_URL}/api/knowledge-spaces`,
          { headers }
        );

        if (spaceResponse.ok) {
          const spaceResult = await spaceResponse.json();
          if (spaceResult.success) {
            const currentSpace = spaceResult.data.find(
              (s: any) => s.id === spaceId
            );
            if (currentSpace) {
              console.log("✅ Found space:", currentSpace);

              // Convert API data to Space format
              const spaceData: Space = {
                id: currentSpace.id,
                name: currentSpace.name,
                color: currentSpace.color || "bg-primary-100",
                initial:
                  currentSpace.initial ||
                  currentSpace.name.charAt(0).toUpperCase(),
                description: currentSpace.description,
                brandImage: currentSpace.brandImage,
                lastUpdated: currentSpace.lastUpdated || "Recently",
                documentCount: currentSpace.documentCount || 0,
                folderCount: currentSpace.folderCount || 0,
                items: [], // We'll fetch Q&As separately if needed
              };

              setSpace(spaceData);

              // For now, we'll use an empty flattened items array
              // since the space detail page is more about space info than Q&As
              setFlattenedItems([]);

              // Fetch responsible users
              await fetchResponsibleUsers();
            } else {
              console.log("❌ Space not found:", spaceId);
              console.log("Available spaces:", spaceResult.data);
              setError(`Space with ID ${spaceId} not found`);
            }
          } else {
            console.error("❌ Failed to load spaces:", spaceResult.error);
            setError(spaceResult.error || "Failed to load space information");
          }
        } else {
          console.error("❌ API request failed:", spaceResponse.status);
          setError(`Failed to load space information: ${spaceResponse.status}`);
        }
      } catch (error) {
        console.error("❌ Error fetching space data:", error);
        setError("Failed to load space data");
      } finally {
        setLoading(false);
      }
    };

    if (spaceId) {
      fetchSpaceData();
    }
  }, [spaceId]);

  // Handle assignment
  const handleAssignment = async () => {
    if (!selectedMember || !spaceId) return;

    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      const response = await fetch(
        `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${spaceId}/responsible-users`,
        {
          method: "PUT",
          headers,
          body: JSON.stringify({
            [assignmentType === "main"
              ? "doc_responsible"
              : "secondary_responsible"]: selectedMember,
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Refresh responsible users
          await fetchResponsibleUsers();

          // Close modal and reset state
          setShowAssignModal(false);
          setSelectedMember("");
          setAssignmentType("main");
        }
      }
    } catch (error) {
      console.error("Error assigning responsibility:", error);
    }
  };

  const filteredItems = flattenedItems
    .filter((item) => {
      if (searchQuery) {
        return (
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.tags?.some((tag) =>
            tag.toLowerCase().includes(searchQuery.toLowerCase())
          )
        );
      }
      if (activeFilter === "documents") {
        return item.type === "document";
      }
      if (activeFilter === "folders") {
        return item.type === "folder";
      }
      return true;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "date":
          comparison =
            new Date(a.lastUpdated || "").getTime() -
            new Date(b.lastUpdated || "").getTime();
          break;
        case "author":
          comparison = (a.author || "").localeCompare(b.author || "");
          break;
      }
      return sortOrder === "asc" ? comparison : -comparison;
    });

  const getTextColor = (bgColor: string) => {
    if (bgColor.includes("primary")) return "text-primary-600";
    if (bgColor.includes("blue")) return "text-blue-600";
    if (bgColor.includes("green")) return "text-green-600";
    if (bgColor.includes("orange")) return "text-orange-600";
    if (bgColor.includes("purple")) return "text-purple-600";
    if (bgColor.includes("red")) return "text-red-600";
    if (bgColor.includes("yellow")) return "text-yellow-600";
    if (bgColor.includes("pink")) return "text-pink-600";
    return "text-gray-600";
  };

  const handleDocumentClick = (document: SpaceItem) => {
    if (document.type === "document") {
      setSelectedDocument(document);
    }
  };

  const handleBackToList = () => {
    setSelectedDocument(null);
  };

  if (loading) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto py-8 px-4 max-w-7xl">
              <div className="flex justify-center items-center h-64">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <p className="text-gray-500">Loading space...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto py-8 px-4 max-w-7xl">
              <div className="flex justify-center items-center h-64">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Error Loading Space
                  </h2>
                  <p className="text-gray-600">{error}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!space) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto py-8 px-4 max-w-7xl">
              <div className="flex justify-center items-center h-64">
                <p className="text-gray-500">Space not found</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (selectedDocument) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto py-8 px-4 max-w-4xl">
              <div className="mb-6">
                <Button
                  variant="ghost"
                  onClick={handleBackToList}
                  className="mb-4"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back to {space.name}
                </Button>
                <div className="bg-white rounded-lg border p-6">
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        {selectedDocument.name}
                      </h1>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {selectedDocument.author}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {selectedDocument.lastUpdated}
                        </div>
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-1" />
                          {selectedDocument.size}
                        </div>
                        {selectedDocument.parentFolder && (
                          <div className="flex items-center">
                            <Folder className="h-4 w-4 mr-1" />
                            {selectedDocument.parentFolder}
                          </div>
                        )}
                      </div>
                      {selectedDocument.tags && (
                        <div className="flex flex-wrap gap-2 mt-3">
                          {selectedDocument.tags.map((tag) => (
                            <Badge
                              key={tag}
                              variant="secondary"
                              className="text-xs"
                            >
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {selectedDocument.content}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      <SideNavigation />
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopNavigation />
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto py-8 px-4 max-w-7xl">
            <div className="flex flex-col space-y-8">
              <div className="flex items-center mb-2">
                <Button
                  variant="ghost"
                  onClick={() => router.push("/spaces")}
                  className="mr-2"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Back to Spaces
                </Button>
              </div>

              {/* Enhanced Space Header with Brand Image */}
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-8 border border-gray-200">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                  <div className="flex items-center">
                    <div className="relative mr-6">
                      {space.brandImage ? (
                        <Image
                          src={space.brandImage || "/placeholder.svg"}
                          alt={space.name}
                          width={80}
                          height={80}
                          className="drop-shadow-lg"
                        />
                      ) : (
                        <div
                          className={`w-12 h-12 ${space.color} rounded-md flex items-center justify-center`}
                        >
                          <span
                            className={`${getTextColor(
                              space.color
                            )} text-xl font-medium`}
                          >
                            {space.initial}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900">
                        {space.name}
                      </h1>
                      <p className="text-gray-500 mt-1">{space.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Button variant="outline">
                      <Share className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                    <Button variant="outline" asChild>
                      <Link href={`/spaces/${spaceId}/settings`}>
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </Link>
                    </Button>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      New Document
                    </Button>
                  </div>
                </div>
              </div>

              {/* Responsible Users Section */}
              <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-sm">
                      <User className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      Documentation Responsibility
                    </h3>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      fetchCompanyUsers();
                      setShowAssignModal(true);
                    }}
                    className="text-blue-600 border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Assign Responsibility
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Main Assignee */}
                  <div>
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-sm">
                        <User className="w-4 h-4 text-white" />
                      </div>
                      <h4 className="font-semibold text-gray-900">
                        Primary Assignee
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      The primary assignee will be responsible for all
                      documentation and content management for this knowledge
                      space.
                    </p>
                    {responsibleUsers?.doc_responsible ? (
                      <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 border-l-4 border-blue-500 rounded-r-lg shadow-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold shadow-md">
                              {(() => {
                                const user = companyUsers.find(
                                  (u) =>
                                    u.id === responsibleUsers.doc_responsible
                                );
                                return user?.name
                                  ? user.name
                                      .split(" ")
                                      .map((n: string) => n[0])
                                      .join("")
                                  : responsibleUsers.doc_responsible
                                      ?.charAt(0)
                                      .toUpperCase() || "U";
                              })()}
                            </div>
                            <div>
                              <p className="font-semibold text-blue-900">
                                {(() => {
                                  const user = companyUsers.find(
                                    (u) =>
                                      u.id === responsibleUsers.doc_responsible
                                  );
                                  return (
                                    user?.name ||
                                    responsibleUsers.doc_responsible
                                  );
                                })()}
                              </p>
                              <p className="text-xs text-blue-600">
                                Documentation Lead
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedMember(
                                responsibleUsers.doc_responsible || ""
                              );
                              setAssignmentType("main");
                              fetchCompanyUsers();
                              setShowAssignModal(true);
                            }}
                            className="text-blue-600 border-blue-300 hover:bg-blue-100 hover:border-blue-400 transition-all duration-200 shadow-sm"
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Change
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 font-semibold">
                              <User className="w-5 h-5" />
                            </div>
                            <div>
                              <p className="font-semibold text-gray-900">
                                No primary assignee
                              </p>
                              <p className="text-xs text-gray-500">
                                Click "Assign Responsibility" to set one
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Secondary Assignee */}
                  <div>
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-sm">
                        <User className="w-4 h-4 text-white" />
                      </div>
                      <h4 className="font-semibold text-gray-900">
                        Secondary Assignee
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      The secondary assignee will support the primary assignee
                      and can take over when needed.
                    </p>
                    {responsibleUsers?.secondary_responsible ? (
                      <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 border-l-4 border-purple-500 rounded-r-lg shadow-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold shadow-md">
                              {(() => {
                                const user = companyUsers.find(
                                  (u) =>
                                    u.id ===
                                    responsibleUsers.secondary_responsible
                                );
                                return user?.name
                                  ? user.name
                                      .split(" ")
                                      .map((n: string) => n[0])
                                      .join("")
                                  : responsibleUsers.secondary_responsible
                                      ?.charAt(0)
                                      .toUpperCase() || "U";
                              })()}
                            </div>
                            <div>
                              <p className="font-semibold text-purple-900">
                                {(() => {
                                  const user = companyUsers.find(
                                    (u) =>
                                      u.id ===
                                      responsibleUsers.secondary_responsible
                                  );
                                  return (
                                    user?.name ||
                                    responsibleUsers.secondary_responsible
                                  );
                                })()}
                              </p>
                              <p className="text-xs text-purple-600">
                                Support Role
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedMember(
                                responsibleUsers.secondary_responsible || ""
                              );
                              setAssignmentType("secondary");
                              fetchCompanyUsers();
                              setShowAssignModal(true);
                            }}
                            className="text-purple-600 border-purple-300 hover:bg-purple-100 hover:border-purple-400 transition-all duration-200 shadow-sm"
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Change
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 font-semibold">
                              <User className="w-5 h-5" />
                            </div>
                            <div>
                              <p className="font-semibold text-gray-900">
                                No secondary assignee
                              </p>
                              <p className="text-xs text-gray-500">
                                Click "Assign Responsibility" to set one
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-6">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-gray-500" />
                    <span className="text-gray-700 font-medium">
                      {space.documentCount} documents
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Folder className="h-5 w-5 mr-2 text-gray-500" />
                    <span className="text-gray-700 font-medium">
                      {space.folderCount} folders
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-gray-500" />
                    <span className="text-gray-700">
                      Updated {space.lastUpdated}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="relative w-full md:w-64">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="search"
                      placeholder="Search documents..."
                      className="pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setActiveFilter("all")}>
                        All Items
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setActiveFilter("documents")}
                      >
                        Documents Only
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setActiveFilter("folders")}
                      >
                        Folders Only
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        {sortOrder === "asc" ? (
                          <SortAsc className="h-4 w-4" />
                        ) : (
                          <SortDesc className="h-4 w-4" />
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setSortBy("name")}>
                        Sort by Name
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy("date")}>
                        Sort by Date
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSortBy("author")}>
                        Sort by Author
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() =>
                          setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                        }
                      >
                        {sortOrder === "asc" ? "Descending" : "Ascending"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <div className="flex border rounded-md">
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                      className="rounded-r-none"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <Tabs defaultValue="all" className="w-full">
                <div className="flex justify-between items-center mb-6">
                  <TabsList>
                    <TabsTrigger
                      value="all"
                      onClick={() => setActiveFilter("all")}
                    >
                      All ({filteredItems.length})
                    </TabsTrigger>
                    <TabsTrigger
                      value="documents"
                      onClick={() => setActiveFilter("documents")}
                    >
                      Documents (
                      {
                        filteredItems.filter((item) => item.type === "document")
                          .length
                      }
                      )
                    </TabsTrigger>
                    <TabsTrigger
                      value="folders"
                      onClick={() => setActiveFilter("folders")}
                    >
                      Folders (
                      {
                        filteredItems.filter((item) => item.type === "folder")
                          .length
                      }
                      )
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="all" className="mt-0">
                  {filteredItems.length === 0 && (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        {searchQuery
                          ? "No items found matching your search"
                          : "No items in this space yet"}
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>

      {/* Assignment Modal */}
      {showAssignModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-md p-0 overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900">
                Assign Documentation Responsibility
              </h3>
              <p className="text-gray-600 mt-2">
                {assignmentType === "main"
                  ? "Select the primary assignee who will be responsible for all documentation and content management for this knowledge space."
                  : "Select the secondary assignee who will support the primary assignee and can take over when needed."}
              </p>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assignment Type
                </label>
                <div className="flex gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="assignmentType"
                      value="main"
                      checked={assignmentType === "main"}
                      onChange={(e) =>
                        setAssignmentType(
                          e.target.value as "main" | "secondary"
                        )
                      }
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Primary Assignee
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="assignmentType"
                      value="secondary"
                      checked={assignmentType === "secondary"}
                      onChange={(e) =>
                        setAssignmentType(
                          e.target.value as "main" | "secondary"
                        )
                      }
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Secondary Assignee
                    </span>
                  </label>
                </div>
              </div>

              <div className="space-y-2 mb-6 max-h-64 overflow-y-auto">
                {loadingUsers ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-600">Loading users...</span>
                  </div>
                ) : companyUsers.length > 0 ? (
                  companyUsers.map((user) => (
                    <label
                      key={user.id}
                      className={`flex items-center gap-3 p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors ${
                        selectedMember === user.id
                          ? "bg-blue-50 border-blue-300"
                          : "border-gray-200"
                      }`}
                    >
                      <input
                        type="radio"
                        name="docResponsible"
                        value={user.id}
                        checked={selectedMember === user.id}
                        onChange={(e) => setSelectedMember(e.target.value)}
                        className="text-blue-600 focus:ring-blue-500"
                      />
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                          {user.name
                            ? user.name
                                .split(" ")
                                .map((n: string) => n[0])
                                .join("")
                            : user.email?.charAt(0).toUpperCase() || "U"}
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">
                            {user.name || user.email}
                          </span>
                          {user.role && (
                            <p className="text-xs text-gray-500">{user.role}</p>
                          )}
                        </div>
                      </div>
                    </label>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    No users found. Please add users in the Admin section.
                  </div>
                )}
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    setShowAssignModal(false);
                    setSelectedMember("");
                    setAssignmentType("main");
                  }}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAssignment}
                  disabled={!selectedMember}
                  className={`flex-1 ${
                    !selectedMember ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  Assign
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
