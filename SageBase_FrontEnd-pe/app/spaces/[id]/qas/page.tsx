"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  MessageSquare,
  CheckCircle,
  ThumbsUp,
  Eye,
  Clock,
  Loader2,
  Trash2,
  MoreHorizontal,
  Edit3,
  ChevronLeft,
  Settings,
  Grid,
  List,
  SortAsc,
  SortDesc,
  X,
  Sparkles,
  Zap,
  TrendingUp,
  Users,
  BookOpen,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import SageBaseLogo from "@/components/sagebase-logo";
import { QA } from "@/types/qa";
import { deleteQA } from "@/lib/api-utils";
import { useAuth } from "@/contexts/auth-context";
import { qaAPI } from "@/services/qa-api";
import { commentsAPI } from "@/services/comments-api";

type Space = {
  id: string;
  name: string;
  color: string;
  initial: string;
  description?: string;
  brandImage?: string;
  doc_responsible?: {
    id: string;
    email: string;
    name: string;
  } | null;
  secondary_responsible?: {
    id: string;
    email: string;
    name: string;
  } | null;
};

type ResponsibleUsers = {
  doc_responsible: {
    id: string;
    email: string;
    name: string;
  } | null;
  secondary_responsible: {
    id: string;
    email: string;
    name: string;
  } | null;
};

type TeamMember = {
  id: string;
  name?: string;
  email: string;
  role?: string;
  first_name?: string;
  last_name?: string;
};

export default function SpaceQAsPage() {
  const params = useParams();
  const router = useRouter();
  const spaceId = params.id as string;
  const { userRole } = useAuth();

  console.log("SpaceQAsPage - params:", params);
  console.log("SpaceQAsPage - spaceId:", spaceId);

  const [space, setSpace] = useState<Space | null>(null);
  const [qas, setQas] = useState<QA[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"title" | "date" | "author" | "votes">(
    "date"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [hoveredQAId, setHoveredQAId] = useState<string | null>(null);
  const [selectedQAId, setSelectedQAId] = useState<string | null>(null);
  const [qaComments, setQaComments] = useState<Record<string, number>>({});
  const [responsibleUsers, setResponsibleUsers] =
    useState<ResponsibleUsers | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    qaId: string | null;
  }>({
    show: false,
    x: 0,
    y: 0,
    qaId: null,
  });

  // Delete confirmation state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingQAId, setDeletingQAId] = useState<string | null>(null);
  const [deletingQATitle, setDeletingQATitle] = useState<string>("");

  // New Q&A modal state
  const [showNewQAModal, setShowNewQAModal] = useState(false);
  const [newQATitle, setNewQATitle] = useState("");
  const [newQAContent, setNewQAContent] = useState("");
  const [newQAAnswer, setNewQAAnswer] = useState("");
  const [newQATags, setNewQATags] = useState("");
  const [isCreatingQA, setIsCreatingQA] = useState(false);

  // Fetch space and Q&As data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const getAuthHeaders = async (): Promise<HeadersInit> => {
          const { supabase } = await import("@/lib/supabase");
          const {
            data: { session },
          } = await supabase.auth.getSession();
          return {
            Authorization: `Bearer ${session?.access_token}`,
            "Content-Type": "application/json",
          };
        };

        const headers = await getAuthHeaders();
        const backendUrl =
          process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8001";

        console.log("🔍 Q&As Page Debug:");
        console.log("  - Backend URL:", backendUrl);
        console.log("  - Space ID:", spaceId);
        console.log("  - Headers:", headers);

        // Fetch space information
        console.log(
          "📡 Fetching spaces from:",
          `${backendUrl}/api/knowledge-spaces`
        );
        const spaceResponse = await fetch(
          `${backendUrl}/api/knowledge-spaces`,
          { headers }
        );
        console.log("📡 Space response status:", spaceResponse.status);

        if (!spaceResponse.ok) {
          const errorText = await spaceResponse.text();
          console.error("❌ Space API error:", errorText);
          throw new Error(
            `Failed to fetch spaces: ${spaceResponse.status} - ${errorText}`
          );
        }

        const spacesData = await spaceResponse.json();
        console.log("📡 Spaces data:", spacesData);

        // Handle the API response structure: {success: true, data: Array}
        const spacesArray = spacesData.data || spacesData;
        console.log("📡 Spaces array:", spacesArray);

        const currentSpace = spacesArray.find((s: any) => s.id === spaceId);
        if (!currentSpace) {
          throw new Error(`Space not found: ${spaceId}`);
        }
        console.log("📡 Current space:", currentSpace);
        setSpace(currentSpace);

        // Set responsible users from the space data
        if (
          currentSpace.doc_responsible ||
          currentSpace.secondary_responsible
        ) {
          setResponsibleUsers({
            doc_responsible: currentSpace.doc_responsible || null,
            secondary_responsible: currentSpace.secondary_responsible || null,
          });
        }

        // Fetch Q&As for this space
        console.log(
          "📡 Fetching Q&As from:",
          `${backendUrl}/api/knowledge-spaces/knowledge-space/${spaceId}`
        );
        const qasResponse = await fetch(
          `${backendUrl}/api/knowledge-spaces/knowledge-space/${spaceId}`,
          { headers }
        );
        console.log("📡 Q&As response status:", qasResponse.status);

        if (!qasResponse.ok) {
          const errorText = await qasResponse.text();
          console.error("❌ Q&As API error:", errorText);
          throw new Error(
            `Failed to fetch Q&As: ${qasResponse.status} - ${errorText}`
          );
        }

        const qasData = await qasResponse.json();
        console.log("📡 Q&As data:", qasData);

        // Handle the API response structure: {success: true, data: Array}
        const qasArray = qasData.data || qasData;
        console.log("📡 Q&As array:", qasArray);
        console.log("📡 First Q&A structure:", qasArray[0]);

        // Transform snake_case to camelCase for date fields
        const transformedQAs = qasArray.map((qa: any) => ({
          ...qa,
          createdAt: qa.created_at || qa.createdAt,
          updatedAt: qa.updated_at || qa.updatedAt,
        }));

        setQas(transformedQAs);

        // Fetch comments for each Q&A
        transformedQAs.forEach((qa: QA) => {
          fetchCommentsForQA(qa.id);
        });

        // Fetch team members for displaying names
        fetchTeamMembers();
      } catch (error) {
        console.error("Error fetching data:", error);
        setError(
          error instanceof Error ? error.message : "Failed to load data"
        );
      } finally {
        setLoading(false);
      }
    };

    if (spaceId) {
      fetchData();
    }
  }, [spaceId]);

  // Filter and sort Q&As
  const filteredAndSortedQAs = qas
    .filter((qa) => {
      if (!searchTerm.trim()) {
        return true; // Show all Q&As when no search term
      }

      const searchLower = searchTerm.toLowerCase().trim();
      return (
        qa.question.title.toLowerCase().includes(searchLower) ||
        qa.question.content.toLowerCase().includes(searchLower) ||
        qa.question.tags.some((tag) =>
          tag.toLowerCase().includes(searchLower)
        ) ||
        qa.question.author.name.toLowerCase().includes(searchLower)
      );
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "title":
          comparison = a.question.title.localeCompare(b.question.title);
          break;
        case "date":
          const dateA = new Date(
            (b as any).createdAt || (b as any).created_at || 0
          );
          const dateB = new Date(
            (a as any).createdAt || (a as any).created_at || 0
          );
          comparison = dateA.getTime() - dateB.getTime();
          break;
        case "author":
          comparison = a.question.author.name.localeCompare(
            b.question.author.name
          );
          break;
        case "votes":
          comparison = b.votes.upvotes - a.votes.upvotes;
          break;
      }
      return sortOrder === "asc" ? -comparison : comparison;
    });

  // Get top tags for quick filtering
  const topTags = Array.from(
    new Set(qas.flatMap((qa) => qa.question.tags).slice(0, 8))
  );

  const formatDate = (date: Date | string | null | undefined) => {
    try {
      if (!date) {
        return "Unknown date";
      }

      const past = new Date(date);

      // Check if the date is valid
      if (isNaN(past.getTime())) {
        console.warn("Invalid date:", date);
        return "Unknown date";
      }

      // Always show the formatted date
      return past.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error, date);
      return "Unknown date";
    }
  };

  // Fetch comments for a Q&A
  const fetchCommentsForQA = async (qaId: string) => {
    try {
      const comments = await commentsAPI.getComments(qaId);
      setQaComments((prev) => ({
        ...prev,
        [qaId]: comments.length,
      }));
    } catch (error) {
      console.error(`Error fetching comments for QA ${qaId}:`, error);
      // Set to 0 if there's an error
      setQaComments((prev) => ({
        ...prev,
        [qaId]: 0,
      }));
    }
  };

  // Fetch team members for displaying names
  const fetchTeamMembers = async () => {
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      // First, get the current user's company ID
      const userResponse = await fetch(
        `${API_BASE_URL}/api/integrations/get-user-by-email/`,
        { headers }
      );

      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.company_id) {
          // Fetch company users using the integrations/company-users endpoint
          const response = await fetch(
            `${API_BASE_URL}/api/integrations/company-users/?company_id=${userData.company_id}`,
            { headers }
          );
          if (response.ok) {
            const data = await response.json();
            setTeamMembers(data);
          } else {
            console.error("Failed to fetch team members:", response.status);
          }
        } else {
          console.error("No company ID found for current user");
        }
      } else {
        console.error("Failed to fetch user info:", userResponse.status);
      }
    } catch (error) {
      console.error("Error fetching team members:", error);
    }
  };

  const getTextColor = (bgColor: string) => {
    // Simple logic to determine text color based on background
    if (bgColor.includes("blue") || bgColor.includes("indigo"))
      return "text-white";
    if (bgColor.includes("gray") || bgColor.includes("slate"))
      return "text-white";
    return "text-gray-900";
  };

  const handleContextMenu = (e: React.MouseEvent, qaId: string) => {
    e.preventDefault();
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      qaId,
    });
    setSelectedQAId(qaId);
  };

  const closeContextMenu = () => {
    setContextMenu({ show: false, x: 0, y: 0, qaId: null });
    setSelectedQAId(null);
  };

  const handleDeleteQA = (qaId: string, qaTitle: string) => {
    setDeletingQAId(qaId);
    setDeletingQATitle(qaTitle);
    setShowDeleteConfirm(true);
    closeContextMenu();
  };

  const confirmDeleteQA = async () => {
    if (!deletingQAId) return;

    try {
      const result = await deleteQA(spaceId, deletingQAId);
      if (result.success) {
        setQas(qas.filter((qa) => qa.id !== deletingQAId));
        setShowDeleteConfirm(false);
        setDeletingQAId(null);
        setDeletingQATitle("");
      } else {
        console.error("Failed to delete Q&A:", result.error);
      }
    } catch (error) {
      console.error("Error deleting Q&A:", error);
    }
  };

  const handleCreateNewQA = async () => {
    if (!newQATitle.trim() || !newQAContent.trim()) {
      alert("Please fill in all required fields");
      return;
    }

    setIsCreatingQA(true);
    try {
      const tags = newQATags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0);

      const result = await qaAPI.createQA(spaceId, {
        question_title: newQATitle,
        question_content: newQAContent,
        question_tags: tags,
        answer_content: newQAAnswer,
      });

      if (result.success) {
        // Reset form
        setNewQATitle("");
        setNewQAContent("");
        setNewQAAnswer("");
        setNewQATags("");
        setShowNewQAModal(false);

        // Show success message
        console.log("✅ Q&A created successfully:", result.message);

        // Refresh the Q&As list
        window.location.reload();
      } else {
        console.error("❌ Failed to create Q&A:", result.message);
        throw new Error(result.message || "Failed to create Q&A");
      }
    } catch (error) {
      console.error("❌ Error creating Q&A:", error);
      alert("Failed to create Q&A. Please try again.");
    } finally {
      setIsCreatingQA(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="flex items-center justify-center h-full">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading Q&As...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full">
        <SideNavigation />
        <div className="flex-1 flex flex-col overflow-hidden">
          <TopNavigation />
          <div className="flex-1 overflow-auto">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Error Loading Q&As
                </h2>
                <p className="text-gray-600">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      <SideNavigation />
      <div className="flex-1 flex flex-col overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        {/* Enhanced Header */}
        <div className="sticky top-0 z-30 bg-white/90 backdrop-blur-xl border-b border-gray-200/60 shadow-sm">
          <div className="px-6 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  {space?.brandImage ? (
                    <img
                      src={space.brandImage}
                      alt={space?.name || "Space"}
                      className="w-12 h-12 rounded-xl shadow-lg ring-2 ring-white"
                    />
                  ) : (
                    <div
                      className={`w-12 h-12 ${space?.color} rounded-xl flex items-center justify-center shadow-lg ring-2 ring-white`}
                    >
                      <span
                        className={`${getTextColor(
                          space?.color || ""
                        )} text-lg font-bold`}
                      >
                        {space?.initial}
                      </span>
                    </div>
                  )}
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Knowledge Space
                  </div>
                  <div className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    {space?.name}
                    <Sparkles className="h-5 w-5 text-yellow-500" />
                  </div>

                  {/* Responsible Users Display */}
                  {(responsibleUsers?.doc_responsible ||
                    responsibleUsers?.secondary_responsible) && (
                    <div className="flex items-center gap-4 mt-2">
                      {responsibleUsers?.doc_responsible && (
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span className="text-xs font-medium text-gray-600">
                              Primary:
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                              {(() => {
                                const user = teamMembers.find(
                                  (u) =>
                                    u.id ===
                                    responsibleUsers?.doc_responsible?.id
                                );
                                if (user) {
                                  // Handle the new data structure with first_name and last_name
                                  if (user.first_name && user.last_name) {
                                    return `${user.first_name.charAt(
                                      0
                                    )}${user.last_name.charAt(
                                      0
                                    )}`.toUpperCase();
                                  } else if (user.first_name) {
                                    return user.first_name
                                      .charAt(0)
                                      .toUpperCase();
                                  } else if (user.last_name) {
                                    return user.last_name
                                      .charAt(0)
                                      .toUpperCase();
                                  } else {
                                    return (
                                      user.email?.charAt(0).toUpperCase() || "U"
                                    );
                                  }
                                }
                                return (
                                  responsibleUsers?.doc_responsible?.name
                                    ?.charAt(0)
                                    .toUpperCase() || "U"
                                );
                              })()}
                            </div>
                            <span className="text-sm font-medium text-gray-900">
                              {(() => {
                                const user = teamMembers.find(
                                  (u) =>
                                    u.id ===
                                    responsibleUsers?.doc_responsible?.id
                                );
                                if (user) {
                                  // Handle the new data structure with first_name and last_name
                                  const fullName =
                                    user.first_name && user.last_name
                                      ? `${user.first_name} ${user.last_name}`.trim()
                                      : user.first_name ||
                                        user.last_name ||
                                        user.email;
                                  return fullName;
                                }
                                return (
                                  responsibleUsers?.doc_responsible?.name ||
                                  responsibleUsers?.doc_responsible?.email
                                );
                              })()}
                            </span>
                          </div>
                        </div>
                      )}

                      {responsibleUsers?.secondary_responsible && (
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span className="text-xs font-medium text-gray-600">
                              Secondary:
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-6 h-6 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                              {(() => {
                                const user = teamMembers.find(
                                  (u) =>
                                    u.id ===
                                    responsibleUsers?.secondary_responsible?.id
                                );
                                if (user) {
                                  // Handle the new data structure with first_name and last_name
                                  if (user.first_name && user.last_name) {
                                    return `${user.first_name.charAt(
                                      0
                                    )}${user.last_name.charAt(
                                      0
                                    )}`.toUpperCase();
                                  } else if (user.first_name) {
                                    return user.first_name
                                      .charAt(0)
                                      .toUpperCase();
                                  } else if (user.last_name) {
                                    return user.last_name
                                      .charAt(0)
                                      .toUpperCase();
                                  } else {
                                    return (
                                      user.email?.charAt(0).toUpperCase() || "U"
                                    );
                                  }
                                }
                                return (
                                  responsibleUsers?.secondary_responsible?.name
                                    ?.charAt(0)
                                    .toUpperCase() || "U"
                                );
                              })()}
                            </div>
                            <span className="text-sm font-medium text-gray-900">
                              {(() => {
                                const user = teamMembers.find(
                                  (u) =>
                                    u.id ===
                                    responsibleUsers?.secondary_responsible?.id
                                );
                                if (user) {
                                  // Handle the new data structure with first_name and last_name
                                  const fullName =
                                    user.first_name && user.last_name
                                      ? `${user.first_name} ${user.last_name}`.trim()
                                      : user.first_name ||
                                        user.last_name ||
                                        user.email;
                                  return fullName;
                                }
                                return (
                                  responsibleUsers?.secondary_responsible
                                    ?.name ||
                                  responsibleUsers?.secondary_responsible?.email
                                );
                              })()}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/spaces")}
                  className="border-gray-300 hover:bg-gray-50"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Spaces
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    setShowNewQAModal(true);
                    setNewQATitle("");
                    setNewQAContent("");
                    setNewQAAnswer("");
                    setNewQATags("");
                  }}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Q&A
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          <div className="container mx-auto py-8 px-6 max-w-7xl">
            <div className="flex flex-col space-y-8">
              {/* Enhanced Search and Filters */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200/60 p-6">
                <div className="flex flex-col lg:flex-row gap-6 items-center">
                  <div className="w-full lg:flex-1">
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        placeholder="Search Q&As by title, content, tags, or author..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-12 h-12 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-xl"
                      />
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Select
                      value={sortBy}
                      onValueChange={(v: any) => setSortBy(v)}
                    >
                      <SelectTrigger className="w-[160px] h-12 rounded-xl border-gray-300">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="title">Title</SelectItem>
                        <SelectItem value="date">Date</SelectItem>
                        <SelectItem value="author">Author</SelectItem>
                        <SelectItem value="votes">Votes</SelectItem>
                      </SelectContent>
                    </Select>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-12 w-12 rounded-xl border-gray-300"
                        >
                          <Filter className="h-5 w-5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setSortBy("title")}>
                          Sort by Title
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setSortBy("date")}>
                          Sort by Date
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setSortBy("author")}>
                          Sort by Author
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setSortBy("votes")}>
                          Sort by Votes
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                          }
                        >
                          {sortOrder === "asc" ? "Descending" : "Ascending"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <div className="flex border border-gray-300 rounded-xl overflow-hidden">
                      <Button
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className="rounded-r-none h-12 px-4"
                      >
                        <Grid className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className="rounded-l-none h-12 px-4"
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                {topTags.length > 0 && (
                  <div className="mt-6 flex items-center gap-2 overflow-x-auto pb-2">
                    <span className="text-sm font-medium text-gray-600 mr-2">
                      Quick filters:
                    </span>
                    {topTags.map((tag) => (
                      <Button
                        key={tag}
                        variant="outline"
                        size="sm"
                        className="whitespace-nowrap rounded-full border-gray-300 hover:bg-blue-50 hover:border-blue-300"
                        onClick={() => setSearchTerm(tag)}
                      >
                        #{tag}
                      </Button>
                    ))}
                  </div>
                )}
              </div>

              {/* Q&A List */}
              {filteredAndSortedQAs.length === 0 ? (
                <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50/30">
                  <CardContent className="p-16 text-center">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <MessageSquare className="h-10 w-10 text-blue-600" />
                    </div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-3">
                      {searchTerm ? "No Q&As Found" : "No Q&As Yet"}
                    </h2>
                    <p className="text-gray-600 mb-8 text-lg">
                      {searchTerm
                        ? "Try adjusting your search terms"
                        : "Be the first to create a Q&A for this space"}
                    </p>
                    {!searchTerm && (
                      <Button
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-8 py-3 rounded-xl"
                        onClick={() => setShowNewQAModal(true)}
                      >
                        <Plus className="mr-2 h-5 w-5" />
                        Create First Q&A
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div
                  className={
                    viewMode === "grid"
                      ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6"
                      : "space-y-4"
                  }
                >
                  {filteredAndSortedQAs.map((qa) => {
                    const isActive =
                      hoveredQAId === qa.id || selectedQAId === qa.id;
                    return (
                      <Card
                        key={qa.id}
                        className={`transition-all duration-300 cursor-pointer rounded-2xl border-0 shadow-lg hover:shadow-2xl ${
                          isActive
                            ? "ring-2 ring-blue-500/50 shadow-xl scale-[1.02] bg-gradient-to-br from-white to-blue-50/30"
                            : "hover:scale-[1.02] bg-white hover:bg-gradient-to-br hover:from-white hover:to-blue-50/20"
                        }`}
                        onMouseEnter={() => setHoveredQAId(qa.id)}
                        onMouseLeave={() => setHoveredQAId(null)}
                        onContextMenu={(e) => handleContextMenu(e, qa.id)}
                      >
                        <CardContent className="p-6">
                          <div className="h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-t-2xl -mt-6 -mx-6 mb-6" />
                          <Link href={`/qa/${spaceId}/${qa.id}`}>
                            <div className="w-full">
                              <div className="flex items-start justify-between mb-4">
                                <div className="flex-1">
                                  {/* Question Header */}
                                  <div className="flex items-center space-x-3 mb-4">
                                    {qaComments[qa.id] > 0 && (
                                      <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full p-2 shadow-lg">
                                        <MessageSquare className="h-5 w-5 text-white" />
                                      </div>
                                    )}
                                    <h3 className="text-lg font-bold text-gray-900 hover:text-blue-600 line-clamp-2 transition-colors">
                                      {qa.question.title}
                                    </h3>
                                  </div>

                                  {/* Question Content Preview */}
                                  <p className="text-gray-700 text-sm mb-4 line-clamp-3 leading-relaxed">
                                    {qa.question.content}
                                  </p>

                                  {/* Tags */}
                                  <div className="flex flex-wrap gap-2 mb-4">
                                    {qa.question.tags.slice(0, 3).map((tag) => (
                                      <Badge
                                        key={tag}
                                        variant="secondary"
                                        className="bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 text-xs font-medium px-3 py-1 rounded-full border border-blue-200"
                                      >
                                        {tag}
                                      </Badge>
                                    ))}
                                    {qa.question.tags.length > 3 && (
                                      <Badge
                                        variant="secondary"
                                        className="text-xs font-medium px-3 py-1 rounded-full bg-gray-100 text-gray-600"
                                      >
                                        +{qa.question.tags.length - 3}
                                      </Badge>
                                    )}
                                  </div>

                                  {/* Meta Information */}
                                  <div className="flex flex-wrap items-center gap-x-6 gap-y-3 text-xs text-gray-500">
                                    <div className="flex items-center space-x-2">
                                      <span className="font-medium">
                                        {qa.question.author.name}
                                      </span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <Clock className="h-3 w-3" />
                                      <span>
                                        {formatDate(
                                          (qa as any).createdAt ||
                                            (qa as any).created_at
                                        )}
                                      </span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <Eye className="h-3 w-3" />
                                      <span>{qa.metadata.views} views</span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <ThumbsUp className="h-3 w-3" />
                                      <span>{qa.votes.upvotes} votes</span>
                                    </div>
                                  </div>
                                </div>

                                {/* Stats Column */}
                                <div className="flex flex-col items-end space-y-3 ml-6">
                                  {qa.answer?.isVerified && (
                                    <Badge className="bg-gradient-to-r from-green-600 to-emerald-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                                      Verified
                                    </Badge>
                                  )}
                                  <div className="text-right text-xs text-gray-400">
                                    <div>ID: {qa.id}</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Link>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Context Menu */}
      {contextMenu.show && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
          onClick={closeContextMenu}
        >
          <div
            className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-full max-w-md p-0 overflow-hidden"
            style={{
              position: "absolute",
              left: contextMenu.x,
              top: contextMenu.y,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-100">
              <h3 className="font-semibold text-gray-900">Q&A Actions</h3>
            </div>
            <div className="p-2">
              <button
                className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors flex items-center"
                onClick={() => {
                  if (contextMenu.qaId) {
                    router.push(`/qa/${spaceId}/${contextMenu.qaId}`);
                  }
                  closeContextMenu();
                }}
              >
                <Edit3 className="h-4 w-4 mr-3 text-blue-600" />
                Edit Q&A
              </button>
              <button
                className="w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors flex items-center"
                onClick={() => {
                  if (contextMenu.qaId) {
                    const qa = qas.find((q) => q.id === contextMenu.qaId);
                    if (qa) {
                      handleDeleteQA(qa.id, qa.question.title);
                    }
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-3" />
                Delete Q&A
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-red-100 rounded-full p-2">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Delete Q&A
                </h3>
                <p className="text-sm text-gray-600">
                  Are you sure you want to delete this Q&A?
                </p>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-6 font-medium">
              "{deletingQATitle}"
            </p>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeletingQAId(null);
                  setDeletingQATitle("");
                }}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteQA}
                className="flex-1"
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* New Q&A Modal */}
      {showNewQAModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Create New Q&A
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowNewQAModal(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Question Title *
                </label>
                <Input
                  placeholder="Enter your question title..."
                  value={newQATitle}
                  onChange={(e) => setNewQATitle(e.target.value)}
                  className="rounded-xl"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Question Content *
                </label>
                <textarea
                  placeholder="Describe your question in detail..."
                  value={newQAContent}
                  onChange={(e) => setNewQAContent(e.target.value)}
                  className="w-full h-32 p-3 border border-gray-300 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Answer Content
                </label>
                <textarea
                  placeholder="Provide an answer to your question..."
                  value={newQAAnswer}
                  onChange={(e) => setNewQAAnswer(e.target.value)}
                  className="w-full h-32 p-3 border border-gray-300 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags (comma-separated)
                </label>
                <Input
                  placeholder="e.g., javascript, react, frontend"
                  value={newQATags}
                  onChange={(e) => setNewQATags(e.target.value)}
                  className="rounded-xl"
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowNewQAModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateNewQA}
                disabled={isCreatingQA}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              >
                {isCreatingQA ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Q&A
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
