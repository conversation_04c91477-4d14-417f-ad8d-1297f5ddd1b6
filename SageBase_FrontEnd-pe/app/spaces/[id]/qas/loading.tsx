import { Loader2 } from "lucide-react";

export default function Loading() {
  return (
    <div className="flex h-full">
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-auto">
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading Q&As...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
