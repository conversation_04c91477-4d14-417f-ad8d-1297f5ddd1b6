"use client";

import React, { useState, useCallback, useRef } from "react";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { useTeamKnowledge } from "@/hooks/useTeamKnowledge";
import { Project, TeamMember } from "@/services/team-knowledge-api";
import {
  Users,
  Search,
  Plus,
  Filter,
  BookOpen,
  Star,
  TrendingUp,
  Calendar,
  User,
  Edit3,
  UserCheck,
  AlertTriangle,
  Award,
  GitCommit,
  MessageSquare,
  FileText,
  Settings,
  Shield,
  Trash2,
  ExternalLink,
  Github,
  GitBranch,
  FolderOpen,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { createProject } from "@/lib/api-utils";
import { spacesAPI } from "@/services/spaces-api";
import { useSidebarRefresh } from "@/contexts/sidebar-refresh-context";
import { useAuth } from "@/contexts/auth-context";

// Separate form component to prevent focus issues
const AddProjectForm = ({
  onSubmit,
  onCancel,
}: {
  onSubmit: (data: any) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    categories: [] as string[],
    categoriesInput: "",
    repoPath: "",
    docsPath: "",
    repoType: "github" as "github" | "gitlab",
  });
  const [showRepositoryFields, setShowRepositoryFields] = useState(false);

  const handleSubmit = () => {
    if (!formData.name || !formData.description) return;

    const projectData = {
      name: formData.name,
      description: formData.description,
      categories: formData.categories,
      repoPath: formData.repoPath,
      docsPath: formData.docsPath,
      repoType: formData.repoType,
    };

    onSubmit(projectData);
  };

  return (
    <div className="space-y-6">
      {/* Project Name */}
      <div className="group">
        <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          Project Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, name: e.target.value }))
          }
          placeholder="e.g., Atlas Platform, Phoenix API, Nexus Dashboard"
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 bg-white/90 backdrop-blur-sm shadow-sm hover:shadow-md"
        />
      </div>

      {/* Project Description */}
      <div className="group">
        <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          Description *
        </label>
        <textarea
          value={formData.description}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, description: e.target.value }))
          }
          placeholder="Describe your project, its purpose, and key features..."
          rows={4}
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 bg-white/90 backdrop-blur-sm shadow-sm hover:shadow-md resize-none"
        />
      </div>

      {/* Categories */}
      <div className="group">
        <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          Categories (comma-separated)
        </label>
        <input
          type="text"
          value={formData.categoriesInput}
          onChange={(e) => {
            const value = e.target.value;
            setFormData((prev) => ({
              ...prev,
              categoriesInput: value,
              categories: value
                .split(",")
                .map((c) => c.trim())
                .filter((c) => c),
            }));
          }}
          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 bg-white/90 backdrop-blur-sm shadow-sm hover:shadow-md"
          placeholder="API, Documentation, Frontend, Backend, DevOps, Design"
        />
        <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          Enter categories separated by commas (e.g., "API, Documentation,
          Security")
        </p>
      </div>

      {/* Repository Information - Optional */}
      <div className="border-t border-gray-200/50 pt-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <GitBranch className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="text-lg font-bold text-gray-900">
                Repository Information
              </h4>
              <p className="text-sm text-gray-600">
                Optional - Add if your project has external repositories
              </p>
            </div>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowRepositoryFields(!showRepositoryFields)}
            className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            {showRepositoryFields ? "Hide" : "Show"} Repository Fields
          </Button>
        </div>

        {showRepositoryFields && (
          <div className="space-y-4">
            {/* Repository Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Repository Type
              </label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="repoType"
                    value="github"
                    checked={formData.repoType === "github"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        repoType: e.target.value as "github" | "gitlab",
                      }))
                    }
                    className="mr-2 text-blue-600"
                  />
                  <Github className="w-4 h-4 mr-1" />
                  GitHub
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="repoType"
                    value="gitlab"
                    checked={formData.repoType === "gitlab"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        repoType: e.target.value as "github" | "gitlab",
                      }))
                    }
                    className="mr-2 text-blue-600"
                  />
                  <GitBranch className="w-4 h-4 mr-1" />
                  GitLab
                </label>
              </div>
            </div>

            {/* Repository URLs */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Repository URL
                </label>
                <input
                  type="url"
                  value={formData.repoPath}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      repoPath: e.target.value,
                    }))
                  }
                  placeholder="https://github.com/company/project"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Documentation URL
                </label>
                <input
                  type="url"
                  value={formData.docsPath}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      docsPath: e.target.value,
                    }))
                  }
                  placeholder="https://docs.company.com/project"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <Button
          onClick={handleSubmit}
          disabled={!formData.name || !formData.description}
          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Plus className="w-5 h-5 mr-2" />
          Create Project
        </Button>
      </div>
    </div>
  );
};

export default function TeamKnowledgeBasePage() {
  const { toast } = useToast();
  const { refreshSidebar } = useSidebarRefresh();
  const { connectedUserCompany } = useAuth();
  const [selectedProjectTab, setSelectedProjectTab] = useState("atlas");

  // Function to fetch company users (same as admin page)
  const fetchCompanyUsers = async () => {
    if (!connectedUserCompany?.company_id) return;

    setLoadingUsers(true);
    try {
      const BACKEND_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";
      const res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-users/?company_id=${connectedUserCompany.company_id}`
      );
      if (!res.ok) throw new Error("Failed to fetch users");
      const data = await res.json();
      setCompanyUsers(data);
    } catch (error) {
      console.error("Error fetching company users:", error);
    } finally {
      setLoadingUsers(false);
    }
  };
  const [searchTerm, setSearchTerm] = useState("");
  const [timeRange, setTimeRange] = useState("3months");
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedProjectForAssign, setSelectedProjectForAssign] = useState("");
  const [selectedMember, setSelectedMember] = useState("");
  const [assignmentType, setAssignmentType] = useState<"main" | "secondary">(
    "main"
  );
  const [showAddProjectModal, setShowAddProjectModal] = useState(false);
  const [companyUsers, setCompanyUsers] = useState<any[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState("");
  const [newProject, setNewProject] = useState({
    name: "",
    description: "",
    categories: [] as string[],
    repoPath: "",
    docsPath: "",
    repoType: "github" as "github" | "gitlab",
  });
  const [categoriesInput, setCategoriesInput] = useState("");
  const [showRepositoryFields, setShowRepositoryFields] = useState(false);

  // Refs to maintain focus
  const nameInputRef = useRef<HTMLInputElement>(null);
  const descriptionInputRef = useRef<HTMLTextAreaElement>(null);
  const categoriesInputRef = useRef<HTMLInputElement>(null);
  const repoPathInputRef = useRef<HTMLInputElement>(null);
  const docsPathInputRef = useRef<HTMLInputElement>(null);

  // Simple onChange handlers without useCallback to prevent re-render issues
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewProject((prev) => ({
      ...prev,
      name: e.target.value,
    }));
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setNewProject((prev) => ({
      ...prev,
      description: e.target.value,
    }));
  };

  const handleCategoriesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCategoriesInput(e.target.value);
  };

  // Debounced categories update to prevent focus loss
  const debouncedCategoriesUpdate = (value: string) => {
    const categories = value
      .split(",")
      .map((c) => c.trim())
      .filter((c) => c);
    if (JSON.stringify(categories) !== JSON.stringify(newProject.categories)) {
      setNewProject((prev) => ({
        ...prev,
        categories,
      }));
    }
  };

  const handleRepoPathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewProject((prev) => ({
      ...prev,
      repoPath: e.target.value,
    }));
  };

  const handleDocsPathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewProject((prev) => ({
      ...prev,
      docsPath: e.target.value,
    }));
  };

  const handleRepoTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewProject((prev) => ({
      ...prev,
      repoType: e.target.value as "github" | "gitlab",
    }));
  };

  // Use the dynamic API hook
  const {
    projects,
    teamMembers,
    loading,
    error,
    createProject,
    updateProject,
    deleteProject,
    assignDocumentationResponsibility,
    searchProjects,
    refetchProjects,
  } = useTeamKnowledge(timeRange);

  // Set initial selected project when projects are loaded
  React.useEffect(() => {
    if (Array.isArray(projects) && projects.length > 0) {
      // If no project is selected or the selected project doesn't exist, select the first one
      if (
        !selectedProjectTab ||
        !projects.find((p) => p.id === selectedProjectTab)
      ) {
        setSelectedProjectTab(projects[0].id);
      }
    }
  }, [projects, selectedProjectTab]);

  // Fetch company users when component mounts and when assignment modal opens
  React.useEffect(() => {
    if (connectedUserCompany?.company_id) {
      fetchCompanyUsers();
    }
  }, [connectedUserCompany?.company_id]);

  // Fetch company users when assignment modal opens
  React.useEffect(() => {
    if (showAssignModal) {
      fetchCompanyUsers();
    }
  }, [showAssignModal]);

  // Project icons mapping
  const getProjectIcon = (projectId: string) => {
    switch (projectId) {
      case "atlas":
        return <BookOpen className="w-4 h-4" />;
      case "phoenix":
        return <Settings className="w-4 h-4" />;
      case "nexus":
        return <Shield className="w-4 h-4" />;
      case "quantum":
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  // Get the currently selected project for display
  const currentProject = Array.isArray(projects)
    ? projects.find((p) => p.id === selectedProjectTab) || projects[0]
    : undefined;

  const getHealthColor = (score: number) => {
    if (score >= 85) return "text-green-600 bg-green-50";
    if (score >= 70) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low":
        return "text-green-700 bg-green-100";
      case "Medium":
        return "text-yellow-700 bg-yellow-100";
      case "High":
        return "text-red-700 bg-red-100";
      default:
        return "text-gray-700 bg-gray-100";
    }
  };

  const handleAssignment = async () => {
    if (!selectedMember || !selectedProjectForAssign) return;

    try {
      console.log("🎯 Assigning documentation responsibility:", {
        projectId: selectedProjectForAssign,
        memberEmail: selectedMember,
        type: assignmentType,
      });

      await assignDocumentationResponsibility(
        selectedProjectForAssign,
        selectedMember, // This is now the user's email
        assignmentType
      );

      // Close modal and reset state
      setShowAssignModal(false);
      setSelectedMember("");
      setSelectedProjectForAssign("");
      setAssignmentType("main");

      // Show success toast
      toast({
        title: "Assignment Successful",
        description: `${
          assignmentType === "main" ? "Main" : "Secondary"
        } assignee has been set successfully.`,
        variant: "default",
      });
    } catch (error) {
      console.error("❌ Error assigning documentation responsibility:", error);
      toast({
        title: "Assignment Failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to assign responsibility. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddProject = async (projectData: any) => {
    try {
      const projectId = projectData.name.toLowerCase().replace(/\s+/g, "-");
      const newProjectData = {
        id: projectId,
        name: projectData.name,
        description: projectData.description,
        lastActivity: "Just created",
        totalContributors: 0,
        categories: projectData.categories,
        docResponsible: null,
        secondaryResponsible: null,
        repoPath: projectData.repoPath,
        docsPath: projectData.docsPath,
        repoType: projectData.repoType,
        topContributors: [],
      };

      // Call the createProject function from the hook (this already updates the local state)
      await createProject(newProjectData);

      // Fetch updated knowledge spaces from the API
      try {
        await spacesAPI.getSpaces();
        console.log("✅ Knowledge spaces refreshed after project creation");
      } catch (error) {
        console.warn("⚠️ Failed to refresh knowledge spaces:", error);
      }

      // Refresh the sidebar to show updated knowledge spaces
      refreshSidebar();

      // Close modal
      setShowAddProjectModal(false);

      // Switch to the new project tab
      setSelectedProjectTab(projectId);

      // Show success toast
      toast({
        title: "Project Created",
        description: "Your project has been successfully created!",
        variant: "default",
      });
    } catch (error) {
      console.error("❌ Error creating project:", error);
      toast({
        title: "Creation Failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to create the project. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteProject = async () => {
    if (!projectToDelete) return;

    try {
      console.log(
        "🗑️ handleDeleteProject called for project:",
        projectToDelete
      );

      await deleteProject(projectToDelete);

      // Fetch updated knowledge spaces from the API
      try {
        await spacesAPI.getSpaces();
        console.log("✅ Knowledge spaces refreshed after project deletion");
      } catch (error) {
        console.warn("⚠️ Failed to refresh knowledge spaces:", error);
      }

      // Fetch updated projects list
      await refetchProjects();

      // Refresh the sidebar to show updated knowledge spaces
      refreshSidebar();

      // If we're deleting the currently selected project, switch to the first available project
      if (selectedProjectTab === projectToDelete) {
        const remainingProjects = Array.isArray(projects)
          ? projects.filter((p) => p.id !== projectToDelete)
          : [];
        if (remainingProjects.length > 0) {
          setSelectedProjectTab(remainingProjects[0].id);
        } else {
          setSelectedProjectTab("");
        }
      }

      setShowDeleteConfirm(false);
      setProjectToDelete("");

      toast({
        title: "Project Deleted",
        description:
          "The project has been successfully deleted and knowledge spaces updated.",
        variant: "default",
      });
    } catch (error) {
      console.error("❌ Error deleting project:", error);
      toast({
        title: "Delete Failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete the project. Please try again.",
        variant: "destructive",
      });
    }
  };

  const AssignDocumentationModal = () => {
    if (!showAssignModal) return null;

    const selectedProject = Array.isArray(projects)
      ? projects.find((p) => p.id === selectedProjectForAssign)
      : undefined;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl">
          <h3 className="text-xl font-bold mb-2 text-gray-900">
            Assign Documentation Responsibility
          </h3>
          <p className="text-gray-600 mb-1">
            Project:{" "}
            <span className="font-semibold text-blue-600">
              {selectedProject?.name}
            </span>
          </p>

          {/* Assignment Type Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Assignment Type
            </label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="assignmentType"
                  value="main"
                  checked={assignmentType === "main"}
                  onChange={(e) =>
                    setAssignmentType(e.target.value as "main" | "secondary")
                  }
                  className="mr-2 text-blue-600"
                />
                <span className="font-medium text-blue-600">Main Assignee</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="assignmentType"
                  value="secondary"
                  checked={assignmentType === "secondary"}
                  onChange={(e) =>
                    setAssignmentType(e.target.value as "main" | "secondary")
                  }
                  className="mr-2 text-blue-600"
                />
                <span className="font-medium text-purple-600">
                  Secondary Assignee
                </span>
              </label>
            </div>
          </div>

          <p className="text-gray-500 text-sm mb-6">
            Select a team member to be{" "}
            {assignmentType === "main" ? "the main" : "the secondary"} person
            responsible for maintaining documentation for this project.
          </p>

          <div className="space-y-2 mb-6 max-h-64 overflow-y-auto">
            {loadingUsers ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading users...</span>
              </div>
            ) : companyUsers.length > 0 ? (
              companyUsers.map((user) => (
                <label
                  key={user.id}
                  className={`flex items-center gap-3 p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors ${
                    selectedMember === user.id
                      ? "bg-blue-50 border-blue-300"
                      : "border-gray-200"
                  }`}
                >
                  <input
                    type="radio"
                    name="docResponsible"
                    value={user.email}
                    checked={selectedMember === user.email}
                    onChange={(e) => setSelectedMember(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                      {user.name
                        ? user.name
                            .split(" ")
                            .map((n: string) => n[0])
                            .join("")
                        : user.email?.charAt(0).toUpperCase() || "U"}
                    </div>
                    <div>
                      <span className="font-medium text-gray-900">
                        {user.name || user.email}
                      </span>
                      {user.role && (
                        <p className="text-xs text-gray-500">{user.role}</p>
                      )}
                    </div>
                  </div>
                </label>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">
                No users found. Please add users in the Admin section.
              </div>
            )}
          </div>

          <div className="flex gap-3">
            <Button
              onClick={() => {
                setShowAssignModal(false);
                setSelectedMember("");
                setSelectedProjectForAssign("");
                setAssignmentType("main");
              }}
              variant="outline"
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignment}
              disabled={!selectedMember}
              className={`flex-1 ${
                !selectedMember ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              OK
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const AddProjectModal = () => {
    if (!showAddProjectModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-2xl shadow-2xl max-h-[90vh] overflow-y-auto">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            Add New Project
          </h3>

          <AddProjectForm
            onSubmit={handleAddProject}
            onCancel={() => {
              setShowAddProjectModal(false);
              setNewProject({
                name: "",
                description: "",
                categories: [],
                repoPath: "",
                docsPath: "",
                repoType: "github",
              });
              setCategoriesInput("");
              setShowRepositoryFields(false);
            }}
          />
        </div>
      </div>
    );
  };

  const DeleteConfirmModal = () => {
    if (!showDeleteConfirm) return null;

    const projectName = Array.isArray(projects)
      ? projects.find((p) => p.id === projectToDelete)?.name
      : undefined;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-red-100 rounded-full">
              <Trash2 className="w-5 h-5 text-red-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Delete Project</h3>
          </div>

          <p className="text-gray-600 mb-2">
            Are you sure you want to delete the project:
          </p>
          <p className="font-semibold text-red-600 mb-4">"{projectName}"</p>
          <p className="text-sm text-gray-500 mb-6">
            This action cannot be undone. All project data and assignments will
            be permanently removed.
          </p>

          <div className="flex gap-3">
            <Button
              onClick={() => {
                setShowDeleteConfirm(false);
                setProjectToDelete("");
              }}
              variant="outline"
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteProject}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <SideNavigation />

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopNavigation />

        <main className="flex-1 overflow-auto p-6">
          <style jsx>{`
            .scrollbar-hide {
              -ms-overflow-style: none;
              scrollbar-width: none;
            }
            .scrollbar-hide::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          <div className="max-w-7xl mx-auto">
            {/* Loading State */}
            {loading && (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">
                  Loading team knowledge base...
                </span>
              </div>
            )}

            {/* Error State - Only show if we have no projects and there's an error */}
            {error && Array.isArray(projects) && projects.length === 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <h3 className="font-semibold text-red-800">
                    Error Loading Data
                  </h3>
                </div>
                <p className="text-red-700 mt-1">{error}</p>
              </div>
            )}

            {/* Main Content - only show when not loading */}
            {!loading && Array.isArray(projects) && projects.length > 0 && (
              <>
                {/* Header */}
                <div className="mb-8">
                  <div className="flex items-center justify-between">
                    <Button
                      onClick={() => setShowAddProjectModal(true)}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Project
                    </Button>
                  </div>
                </div>

                {/* Project Tabs */}
                <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6 overflow-hidden">
                  <div className="flex border-b border-gray-100 overflow-x-auto scrollbar-hide">
                    {Array.isArray(projects) &&
                      projects.map((project) => {
                        const isActive = selectedProjectTab === project.id;
                        return (
                          <button
                            key={project.id}
                            onClick={() => setSelectedProjectTab(project.id)}
                            className={`group relative px-8 py-5 font-semibold text-sm transition-all duration-300 whitespace-nowrap min-w-fit flex items-center gap-3 ${
                              isActive
                                ? "text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg"
                                : "text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border-r border-gray-100 last:border-r-0"
                            }`}
                          >
                            {/* Tab Icon */}
                            <div
                              className={`transition-all duration-300 ${
                                isActive
                                  ? "text-white scale-110"
                                  : "text-gray-400 group-hover:text-blue-500"
                              }`}
                            >
                              {getProjectIcon(project.id)}
                            </div>

                            {/* Tab Title */}
                            <span className="font-medium tracking-wide">
                              {project.name}
                            </span>

                            {/* Delete Button (always visible) */}
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                setProjectToDelete(project.id);
                                setShowDeleteConfirm(true);
                              }}
                              className={`p-1.5 rounded transition-colors duration-200 ml-2 cursor-pointer ${
                                isActive
                                  ? "hover:bg-red-200 text-red-300 hover:text-red-100"
                                  : "hover:bg-red-100 text-red-500 hover:text-red-700"
                              }`}
                              title="Delete project"
                            >
                              <Trash2 className="w-4 h-4" />
                            </div>

                            {/* Active Tab Accent */}
                            {isActive && (
                              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-t-full" />
                            )}

                            {/* Hover Glow Effect */}
                            {!isActive && (
                              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 to-indigo-500/0 group-hover:from-blue-500/5 group-hover:to-indigo-500/5 transition-all duration-300 rounded-lg" />
                            )}
                          </button>
                        );
                      })}
                  </div>

                  {/* Time Range Controls */}
                  <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50/30 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-blue-600" />
                          <span className="text-sm font-semibold text-gray-700">
                            Time Range:
                          </span>
                        </div>
                        <select
                          value={timeRange}
                          onChange={(e) => setTimeRange(e.target.value)}
                          className="px-4 py-2 border border-gray-200 rounded-lg text-sm font-medium bg-white shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-blue-300"
                        >
                          <option value="1month">📅 Last Month</option>
                          <option value="3months">📊 Last 3 Months</option>
                          <option value="6months">📈 Last 6 Months</option>
                          <option value="1year">🗓️ Last Year</option>
                        </select>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-white/70 rounded-lg border border-gray-200 shadow-sm">
                        <span className="text-sm font-medium text-gray-700">
                          Viewing:{" "}
                          <span className="text-blue-600 font-semibold">
                            {currentProject?.name}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Single Large Project Card */}
                {currentProject && (
                  <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
                    {/* Project Header */}
                    <div className="p-6 border-b">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-2xl font-bold text-gray-900">
                              {currentProject.name}
                            </h3>
                          </div>
                          <p className="text-gray-600 mb-3">
                            {currentProject.description}
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {currentProject.categories.map((category) => (
                              <span
                                key={category}
                                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                              >
                                {category}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="text-right">
                          <p className="text-sm text-gray-500">Last Activity</p>
                          <p className="font-medium">
                            {currentProject.lastActivity}
                          </p>
                          <p className="text-sm text-gray-500 mt-2">
                            {currentProject.totalContributors} contributors
                          </p>

                          {/* Delete Project Button */}
                          <Button
                            onClick={() => {
                              setProjectToDelete(currentProject.id);
                              setShowDeleteConfirm(true);
                            }}
                            variant="outline"
                            size="sm"
                            className="mt-3 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Project
                          </Button>

                          {/* Repository and Docs Links */}
                          <div className="flex gap-2 mt-3 justify-end">
                            {currentProject.repoPath && (
                              <a
                                href={currentProject.repoPath}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1 px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-xs text-gray-700 transition-colors"
                              >
                                {currentProject.repoType === "github" ? (
                                  <Github className="w-3 h-3" />
                                ) : (
                                  <GitBranch className="w-3 h-3" />
                                )}
                                Repo
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            )}
                            {currentProject.docsPath && (
                              <a
                                href={currentProject.docsPath}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1 px-2 py-1 bg-blue-100 hover:bg-blue-200 rounded text-xs text-blue-700 transition-colors"
                              >
                                <FolderOpen className="w-3 h-3" />
                                Docs
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="grid md:grid-cols-2 gap-8">
                        {/* Top Contributors */}
                        <div>
                          <div className="flex items-center gap-2 mb-4">
                            <Star className="w-5 h-5 text-yellow-500" />
                            <h4 className="font-semibold text-gray-900">
                              Top Contributors
                            </h4>
                          </div>

                          {currentProject.topContributors.length > 0 ? (
                            <div className="space-y-3">
                              {currentProject.topContributors.map(
                                (contributor, index) => (
                                  <div
                                    key={contributor.name}
                                    className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                                  >
                                    <div className="flex items-center gap-2">
                                      <span
                                        className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                          index === 0
                                            ? "bg-yellow-100 text-yellow-800"
                                            : index === 1
                                            ? "bg-gray-100 text-gray-800"
                                            : "bg-orange-100 text-orange-800"
                                        }`}
                                      >
                                        {index + 1}
                                      </span>
                                      <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                        {contributor.name
                                          .split(" ")
                                          .map((n) => n[0])
                                          .join("")}
                                      </div>
                                    </div>

                                    <div className="flex-1">
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium text-gray-900">
                                          {contributor.name}
                                        </span>
                                        <span className="text-xs text-gray-500">
                                          ({contributor.role})
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-4 text-xs text-gray-600 mt-1">
                                        <span className="flex items-center gap-1">
                                          <GitCommit className="w-3 h-3" />
                                          {contributor.commits} commits
                                        </span>
                                        <span className="flex items-center gap-1">
                                          <MessageSquare className="w-3 h-3" />
                                          {contributor.reviews} reviews
                                        </span>
                                        <span className="flex items-center gap-1">
                                          <FileText className="w-3 h-3" />
                                          {contributor.docs} docs
                                        </span>
                                      </div>
                                    </div>

                                    <div className="text-right">
                                      <span className="text-lg font-bold text-blue-600">
                                        {contributor.contributions}
                                      </span>
                                      <p className="text-xs text-gray-500">
                                        contributions
                                      </p>
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          ) : (
                            <div className="space-y-3">
                              {/* Dummy data with note */}
                              <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div className="flex items-center gap-2">
                                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                                  <span className="text-sm text-yellow-800 font-medium">
                                    Using dummy data for demonstration
                                  </span>
                                </div>
                              </div>

                              {[
                                {
                                  name: "Sarah Chen",
                                  role: "Senior Developer",
                                  contributions: 156,
                                  commits: 89,
                                  reviews: 45,
                                  docs: 22,
                                },
                                {
                                  name: "Marcus Rodriguez",
                                  role: "Tech Lead",
                                  contributions: 134,
                                  commits: 67,
                                  reviews: 52,
                                  docs: 15,
                                },
                                {
                                  name: "Emily Watson",
                                  role: "Full Stack Developer",
                                  contributions: 98,
                                  commits: 54,
                                  reviews: 28,
                                  docs: 16,
                                },
                                {
                                  name: "David Kim",
                                  role: "Backend Developer",
                                  contributions: 87,
                                  commits: 43,
                                  reviews: 31,
                                  docs: 13,
                                },
                                {
                                  name: "Lisa Thompson",
                                  role: "Frontend Developer",
                                  contributions: 76,
                                  commits: 38,
                                  reviews: 25,
                                  docs: 13,
                                },
                              ].map((contributor, index) => (
                                <div
                                  key={contributor.name}
                                  className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg opacity-75"
                                >
                                  <div className="flex items-center gap-2">
                                    <span
                                      className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                        index === 0
                                          ? "bg-yellow-100 text-yellow-800"
                                          : index === 1
                                          ? "bg-gray-100 text-gray-800"
                                          : "bg-orange-100 text-orange-800"
                                      }`}
                                    >
                                      {index + 1}
                                    </span>
                                    <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                      {contributor.name
                                        .split(" ")
                                        .map((n) => n[0])
                                        .join("")}
                                    </div>
                                  </div>

                                  <div className="flex-1">
                                    <div className="flex items-center gap-2">
                                      <span className="font-medium text-gray-900">
                                        {contributor.name}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        ({contributor.role})
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-4 text-xs text-gray-600 mt-1">
                                      <span className="flex items-center gap-1">
                                        <GitCommit className="w-3 h-3" />
                                        {contributor.commits} commits
                                      </span>
                                      <span className="flex items-center gap-1">
                                        <MessageSquare className="w-3 h-3" />
                                        {contributor.reviews} reviews
                                      </span>
                                      <span className="flex items-center gap-1">
                                        <FileText className="w-3 h-3" />
                                        {contributor.docs} docs
                                      </span>
                                    </div>
                                  </div>

                                  <div className="text-right">
                                    <span className="text-lg font-bold text-blue-600">
                                      {contributor.contributions}
                                    </span>
                                    <p className="text-xs text-gray-500">
                                      contributions
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Documentation Responsibility */}
                        <div>
                          <div className="flex items-center gap-2 mb-4">
                            <BookOpen className="w-5 h-5 text-blue-500" />
                            <h4 className="font-semibold text-gray-900">
                              Documentation Responsibility
                            </h4>
                          </div>

                          {/* Main Assignee */}
                          <div className="mb-4">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-sm">
                                <User className="w-4 h-4 text-white" />
                              </div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="cursor-help">
                                      <h5 className="font-semibold text-gray-900">
                                        Main Assignee
                                      </h5>
                                      <p className="text-xs text-gray-500">
                                        Primary documentation owner
                                      </p>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>
                                      The assignee will be responsible for all
                                      the documentation and notifications for
                                      this project, example, approving new Q&A
                                    </p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            {currentProject.docResponsible ? (
                              <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 border-l-4 border-blue-500 rounded-r-lg shadow-sm">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold shadow-md">
                                      {(() => {
                                        // Find user by ID (since backend returns IDs)
                                        const user = companyUsers.find(
                                          (u) =>
                                            u.id ===
                                            currentProject.docResponsible
                                        );

                                        return user?.name
                                          ? user.name
                                              .split(" ")
                                              .map((n: string) => n[0])
                                              .join("")
                                          : currentProject.docResponsible
                                              ?.charAt(0)
                                              .toUpperCase() || "U";
                                      })()}
                                    </div>
                                    <div>
                                      <p className="font-semibold text-blue-900">
                                        {(() => {
                                          // Find user by ID and display their email
                                          const user = companyUsers.find(
                                            (u) =>
                                              u.id ===
                                              currentProject.docResponsible
                                          );
                                          return (
                                            user?.email ||
                                            currentProject.docResponsible
                                          );
                                        })()}
                                      </p>
                                      <p className="text-xs text-blue-600">
                                        Documentation Lead
                                      </p>
                                    </div>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedProjectForAssign(
                                        currentProject.id
                                      );
                                      setAssignmentType("main");
                                      setShowAssignModal(true);
                                    }}
                                    className="text-blue-600 border-blue-300 hover:bg-blue-100 hover:border-blue-400 transition-all duration-200 shadow-sm"
                                  >
                                    <Edit3 className="w-4 h-4 mr-1" />
                                    Change
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 rounded-r-lg shadow-sm">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-md">
                                      <AlertTriangle className="w-5 h-5 text-white" />
                                    </div>
                                    <div>
                                      <p className="font-semibold text-amber-900">
                                        Not Assigned
                                      </p>
                                      <p className="text-xs text-amber-600">
                                        Needs primary owner
                                      </p>
                                    </div>
                                  </div>
                                  <Button
                                    onClick={() => {
                                      setSelectedProjectForAssign(
                                        currentProject.id
                                      );
                                      setAssignmentType("main");
                                      setShowAssignModal(true);
                                    }}
                                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all duration-200"
                                  >
                                    <User className="w-4 h-4 mr-2" />
                                    Assign Now
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Secondary Assignee */}
                          <div className="mb-4">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-sm">
                                <Users className="w-4 h-4 text-white" />
                              </div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="cursor-help">
                                      <h5 className="font-semibold text-gray-900">
                                        Secondary Assignee
                                      </h5>
                                      <p className="text-xs text-gray-500">
                                        Backup & support role
                                      </p>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>
                                      The assignee will be responsible for all
                                      the documentation and notifications for
                                      this project, example, approving new Q&A
                                    </p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            {currentProject.secondaryResponsible ? (
                              <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 border-l-4 border-purple-500 rounded-r-lg shadow-sm">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold shadow-md">
                                      {(() => {
                                        // Find user by ID (since backend returns IDs)
                                        const user = companyUsers.find(
                                          (u) =>
                                            u.id ===
                                            currentProject.secondaryResponsible
                                        );

                                        return user?.name
                                          ? user.name
                                              .split(" ")
                                              .map((n: string) => n[0])
                                              .join("")
                                          : currentProject.secondaryResponsible
                                              ?.charAt(0)
                                              .toUpperCase() || "U";
                                      })()}
                                    </div>
                                    <div>
                                      <p className="font-semibold text-purple-900">
                                        {(() => {
                                          // Find user by ID and display their email
                                          const user = companyUsers.find(
                                            (u) =>
                                              u.id ===
                                              currentProject.secondaryResponsible
                                          );
                                          return (
                                            user?.email ||
                                            currentProject.secondaryResponsible
                                          );
                                        })()}
                                      </p>
                                      <p className="text-xs text-purple-600">
                                        Support Role
                                      </p>
                                    </div>
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedProjectForAssign(
                                        currentProject.id
                                      );
                                      setAssignmentType("secondary");
                                      setShowAssignModal(true);
                                    }}
                                    className="text-purple-600 border-purple-300 hover:bg-purple-100 hover:border-purple-400 transition-all duration-200 shadow-sm"
                                  >
                                    <Edit3 className="w-4 h-4 mr-1" />
                                    Change
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="p-4 bg-gradient-to-r from-gray-50 to-slate-50 border-l-4 border-gray-300 rounded-r-lg shadow-sm">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center shadow-md">
                                      <User className="w-5 h-5 text-white" />
                                    </div>
                                    <div>
                                      <p className="font-semibold text-gray-700">
                                        Not Assigned
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        Optional backup role
                                      </p>
                                    </div>
                                  </div>
                                  <Button
                                    onClick={() => {
                                      setSelectedProjectForAssign(
                                        currentProject.id
                                      );
                                      setAssignmentType("secondary");
                                      setShowAssignModal(true);
                                    }}
                                    className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-lg transition-all duration-200"
                                  >
                                    <Users className="w-4 h-4 mr-2" />
                                    Assign
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Project Metrics */}
                          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                            <h5 className="font-medium text-gray-900 mb-3">
                              Project Metrics
                            </h5>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">Contributors</p>
                                <p className="font-semibold text-lg">
                                  {currentProject.totalContributors}
                                </p>
                              </div>
                              <div>
                                <p className="text-gray-600">Last Activity</p>
                                <p className="font-semibold text-lg">
                                  {currentProject.lastActivity}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* No Projects State - Show comprehensive add project interface */}
            {!loading && Array.isArray(projects) && projects.length === 0 && (
              <div className="max-w-4xl mx-auto">
                {/* Header */}

                {/* Welcome Card */}
                <div className="bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30 rounded-2xl shadow-2xl border border-blue-100/50 p-8 mb-8 relative overflow-hidden">
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full -translate-y-16 translate-x-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-indigo-400/10 to-blue-400/10 rounded-full translate-y-12 -translate-x-12"></div>

                  <div className="text-center mb-8 relative z-10">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg mb-6">
                      <BookOpen className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent mb-4">
                      Welcome to Your Team Knowledge Map
                    </h3>
                    <p className="text-gray-600 max-w-2xl mx-auto text-lg leading-relaxed">
                      Get started by adding your first project. This will help
                      you track documentation, assign responsibilities, and
                      monitor team contributions across your projects.
                    </p>
                  </div>

                  {/* Add Project Form */}
                  <div className="max-w-3xl mx-auto relative z-10">
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/50">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                          <Plus className="w-4 h-4 text-white" />
                        </div>
                        <h4 className="text-xl font-bold text-gray-900">
                          Add Your First Project
                        </h4>
                      </div>

                      <AddProjectForm
                        onSubmit={handleAddProject}
                        onCancel={() => {
                          setShowAddProjectModal(false);
                          setNewProject({
                            name: "",
                            description: "",
                            categories: [],
                            repoPath: "",
                            docsPath: "",
                            repoType: "github",
                          });
                          setCategoriesInput("");
                          setShowRepositoryFields(false);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Help Section */}
                <div className="bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 rounded-2xl p-8 shadow-lg border border-gray-100/50">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                      <BookOpen className="w-5 h-5 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">
                      Getting Started Tips
                    </h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                    <div className="flex items-start gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/50 shadow-sm hover:shadow-md transition-all duration-200">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                        <span className="text-white font-bold text-sm">1</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900 mb-2">
                          Add Project Details
                        </p>
                        <p className="text-gray-600 leading-relaxed">
                          Provide a clear name and description to help your team
                          understand the project's purpose.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/50 shadow-sm hover:shadow-md transition-all duration-200">
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                        <span className="text-white font-bold text-sm">2</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900 mb-2">
                          Link Repositories (Optional)
                        </p>
                        <p className="text-gray-600 leading-relaxed">
                          Optionally connect your GitHub or GitLab repositories
                          to track code contributions and activity.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/50 shadow-sm hover:shadow-md transition-all duration-200">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                        <span className="text-white font-bold text-sm">3</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900 mb-2">
                          Assign Responsibilities
                        </p>
                        <p className="text-gray-600 leading-relaxed">
                          Designate team members as primary and secondary
                          documentation owners.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

      <AssignDocumentationModal />
      <AddProjectModal />
      <DeleteConfirmModal />
    </div>
  );
}
