"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Users,
  Brain,
  Shield,
  Database,
  Layers,
  Activity,
} from "lucide-react";

interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar: string;
  department: string;
  expertiseLevel: number;
  totalContributions: number;
}

interface Project {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: any;
  category: string;
  experts: TeamMember[];
}

// Mock data - in real app this would come from API based on project ID
const projectsData: Record<string, Project> = {
  atlas: {
    id: "atlas",
    name: "Atlas",
    description: "Core API and authentication system",
    color: "bg-blue-500",
    icon: Database,
    category: "Backend",
    experts: [
      {
        id: "1",
        name: "Sarah <PERSON>",
        role: "Senior Developer",
        avatar: "/avatars/sarah.jpg",
        department: "Engineering",
        expertiseLevel: 95,
        totalContributions: 633,
      },
      {
        id: "4",
        name: "David Kim",
        role: "Backend Developer",
        avatar: "/avatars/david.jpg",
        department: "Engineering",
        expertiseLevel: 78,
        totalContributions: 445,
      },
      {
        id: "5",
        name: "Lisa Chen",
        role: "DevOps Engineer",
        avatar: "/avatars/lisa.jpg",
        department: "Engineering",
        expertiseLevel: 62,
        totalContributions: 302,
      },
      {
        id: "2",
        name: "Michael Chen",
        role: "Tech Lead",
        avatar: "/avatars/michael.jpg",
        department: "Engineering",
        expertiseLevel: 45,
        totalContributions: 135,
      },
      {
        id: "6",
        name: "Alex Martinez",
        role: "UI/UX Developer",
        avatar: "/avatars/alex.jpg",
        department: "Design",
        expertiseLevel: 30,
        totalContributions: 110,
      },
      {
        id: "7",
        name: "James Wilson",
        role: "Junior Developer",
        avatar: "/avatars/james.jpg",
        department: "Engineering",
        expertiseLevel: 25,
        totalContributions: 75,
      },
      {
        id: "8",
        name: "Emma Davis",
        role: "QA Engineer",
        avatar: "/avatars/emma.jpg",
        department: "Quality",
        expertiseLevel: 15,
        totalContributions: 50,
      },
    ],
  },
  nova: {
    id: "nova",
    name: "Nova",
    description: "Frontend UI library and components",
    color: "bg-purple-500",
    icon: Layers,
    category: "Frontend",
    experts: [
      {
        id: "2",
        name: "Michael Chen",
        role: "Tech Lead",
        avatar: "/avatars/michael.jpg",
        department: "Engineering",
        expertiseLevel: 92,
        totalContributions: 810,
      },
      {
        id: "1",
        name: "Sarah Johnson",
        role: "Senior Developer",
        avatar: "/avatars/sarah.jpg",
        department: "Engineering",
        expertiseLevel: 78,
        totalContributions: 432,
      },
      {
        id: "6",
        name: "Alex Martinez",
        role: "UI/UX Developer",
        avatar: "/avatars/alex.jpg",
        department: "Design",
        expertiseLevel: 71,
        totalContributions: 420,
      },
      {
        id: "3",
        name: "Emily Rodriguez",
        role: "Full Stack Developer",
        avatar: "/avatars/emily.jpg",
        department: "Engineering",
        expertiseLevel: 55,
        totalContributions: 290,
      },
      {
        id: "8",
        name: "Emma Davis",
        role: "QA Engineer",
        avatar: "/avatars/emma.jpg",
        department: "Quality",
        expertiseLevel: 40,
        totalContributions: 180,
      },
    ],
  },
  pulse: {
    id: "pulse",
    name: "Pulse",
    description: "Real-time monitoring and analytics",
    color: "bg-green-500",
    icon: Activity,
    category: "Analytics",
    experts: [
      {
        id: "3",
        name: "Emily Rodriguez",
        role: "Full Stack Developer",
        avatar: "/avatars/emily.jpg",
        department: "Engineering",
        expertiseLevel: 88,
        totalContributions: 640,
      },
      {
        id: "2",
        name: "Michael Chen",
        role: "Tech Lead",
        avatar: "/avatars/michael.jpg",
        department: "Engineering",
        expertiseLevel: 65,
        totalContributions: 285,
      },
      {
        id: "5",
        name: "Lisa Chen",
        role: "DevOps Engineer",
        avatar: "/avatars/lisa.jpg",
        department: "Engineering",
        expertiseLevel: 50,
        totalContributions: 200,
      },
      {
        id: "4",
        name: "David Kim",
        role: "Backend Developer",
        avatar: "/avatars/david.jpg",
        department: "Engineering",
        expertiseLevel: 35,
        totalContributions: 150,
      },
    ],
  },
  sentinel: {
    id: "sentinel",
    name: "Sentinel",
    description: "Security and compliance monitoring",
    color: "bg-orange-500",
    icon: Shield,
    category: "Security",
    experts: [
      {
        id: "7",
        name: "James Wilson",
        role: "Security Engineer",
        avatar: "/avatars/james.jpg",
        department: "Security",
        expertiseLevel: 85,
        totalContributions: 455,
      },
      {
        id: "3",
        name: "Emily Rodriguez",
        role: "Full Stack Developer",
        avatar: "/avatars/emily.jpg",
        department: "Engineering",
        expertiseLevel: 72,
        totalContributions: 258,
      },
      {
        id: "4",
        name: "David Kim",
        role: "Backend Developer",
        avatar: "/avatars/david.jpg",
        department: "Engineering",
        expertiseLevel: 65,
        totalContributions: 200,
      },
    ],
  },
};

// Define a set of colors for the pie chart
const COLORS = [
  "#3B82F6", // blue
  "#8B5CF6", // purple
  "#10B981", // green
  "#F59E0B", // amber
  "#EF4444", // red
  "#06B6D4", // cyan
  "#EC4899", // pink
  "#F97316", // orange
  "#6B7280", // gray
];

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [project, setProject] = useState<Project | null>(null);

  useEffect(() => {
    const projectId = params.id as string;
    const projectData = projectsData[projectId];
    if (projectData) {
      setProject(projectData);
    }
  }, [params.id]);

  if (!project) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p>Loading project data...</p>
      </div>
    );
  }

  const ProjectIcon = project.icon;

  // Prepare data for pie chart
  const pieChartData = project.experts.map((expert) => ({
    name: expert.name,
    value: expert.expertiseLevel,
    role: expert.role,
  }));

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 rounded-lg shadow-lg border">
          <p className="font-semibold">{payload[0].payload.name}</p>
          <p className="text-sm text-gray-600">{payload[0].payload.role}</p>
          <p className="text-sm font-medium mt-1">
            Knowledge: {payload[0].value}%
          </p>
        </div>
      );
    }
    return null;
  };

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (percent < 0.05) return null; // Don't show label for very small slices

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        className="text-sm font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <SideNavigation />

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopNavigation />

        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-6xl mx-auto space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push("/team-knowledge-map")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
              <div className="flex items-center gap-3">
                <div
                  className={`w-12 h-12 ${project.color} rounded-lg flex items-center justify-center shadow-lg`}
                >
                  <ProjectIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    {project.name}
                  </h1>
                  <p className="text-gray-600">{project.description}</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pie Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Brain className="h-5 w-5" />
                    Team Knowledge Distribution
                  </CardTitle>
                  <CardDescription>
                    Visual breakdown of expertise levels for {project.name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <ResponsiveContainer width="100%" height={400}>
                    <PieChart>
                      <Pie
                        data={pieChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={renderCustomizedLabel}
                        outerRadius={140}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieChartData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>

                  {/* Legend */}
                  <div className="mt-6 grid grid-cols-2 gap-3">
                    {pieChartData.map((entry, index) => (
                      <div key={entry.name} className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded"
                          style={{
                            backgroundColor: COLORS[index % COLORS.length],
                          }}
                        />
                        <span className="text-sm text-gray-700">
                          {entry.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Team Members List */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Users className="h-5 w-5" />
                    Team Members
                  </CardTitle>
                  <CardDescription>
                    People contributing to {project.name}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {project.experts
                      .sort((a, b) => b.expertiseLevel - a.expertiseLevel)
                      .map((expert, index) => (
                        <div
                          key={expert.id}
                          className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div
                            className="w-2 h-12 rounded-full"
                            style={{
                              backgroundColor: COLORS[index % COLORS.length],
                            }}
                          />
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={expert.avatar} />
                            <AvatarFallback>
                              {expert.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">
                              {expert.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {expert.role} • {expert.department}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900">
                              {expert.expertiseLevel}%
                            </div>
                            <div className="text-xs text-gray-500">
                              knowledge
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
