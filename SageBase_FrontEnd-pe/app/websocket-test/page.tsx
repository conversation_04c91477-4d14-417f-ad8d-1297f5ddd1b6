"use client";
import { useState } from "react";
import { NotificationTest } from "@/components/notification-test";
import { useAuth } from "@/contexts/auth-context";

export default function WebSocketTestPage() {
  const { user } = useAuth();
  const [userEmail, setUserEmail] = useState(user?.email || "<EMAIL>");

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            🔌 WebSocket Test & Logging
          </h1>
          <p className="text-gray-600 mb-4">
            This page tests the WebSocket connection and logs all messages. Open
            your browser console to see detailed logs.
          </p>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              User Email for WebSocket:
            </label>
            <input
              type="email"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">
              📋 Console Logs to Watch For:
            </h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>
                🔌 <strong>WebSocket Connected:</strong> Connection established
              </li>
              <li>
                🔌 <strong>WebSocket Message Sent:</strong> Authentication &
                subscription messages
              </li>
              <li>
                🔌 <strong>WebSocket Message Received:</strong> All incoming
                messages
              </li>
              <li>
                📩 <strong>Processing notification:</strong> Notification
                details
              </li>
              <li>
                🔌 <strong>Sending WebSocket ping:</strong> Health check pings
              </li>
              <li>
                🔌 <strong>WebSocket Disconnected:</strong> Connection closed
              </li>
              <li>
                🔄 <strong>WebSocket Reconnecting:</strong> Auto-reconnection
                attempts
              </li>
            </ul>
          </div>
        </div>

        <NotificationTest />
      </div>
    </div>
  );
}
