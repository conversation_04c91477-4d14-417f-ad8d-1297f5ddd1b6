"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

export default function ResetPasswordPage() {
  console.log("🚀 ResetPasswordPage component rendered");
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const router = useRouter();
  const supabase = createClientComponentClient();

  useEffect(() => {
    console.log("🚀 ResetPasswordPage component mounted");
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    console.log("📝 Form submitted!");
    e.preventDefault();
    console.log("📝 Form prevented default");
    setError("");
    setMessage("");
    setIsLoading(true);
    console.log("📝 Form state updated");

    console.log("🔄 Starting password reset process for:", email);
    console.log("🌐 Current origin:", window.location.origin);
    console.log("🔗 Redirect URL:", `${window.location.origin}/auth/callback`);
    console.log("🔧 Supabase client:", supabase);
    console.log("🔧 Supabase URL:", process.env.NEXT_PUBLIC_SUPABASE_URL);

    try {
      console.log("📡 Calling Supabase resetPasswordForEmail...");

      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/callback`,
      });

      console.log("📡 Supabase response:", { data, error });

      if (error) {
        console.error("❌ Password reset error:", error);
        setError(`Reset failed: ${error.message || "Unknown error"}`);
      } else {
        console.log("✅ Password reset email sent successfully");
        setMessage(
          "✅ Password reset email sent! Please check your inbox for the reset link."
        );
      }
    } catch (err: any) {
      console.error("❌ Unexpected error during password reset:", err);
      setError(
        `Unexpected error: ${err.message || "Network or configuration issue"}`
      );
    } finally {
      setIsLoading(false);
      console.log("🏁 Password reset process completed");
    }
  };

  return (
    <div className="flex min-h-screen bg-white">
      <div className="flex flex-[0.35] flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div className="flex flex-col items-center">
            <Image
              src="/images/sagebase-purple-full.svg"
              alt="SageBase Logo"
              width={220}
              height={50}
              className="mb-8"
              priority
            />
            <h2 className="text-2xl font-bold leading-9 tracking-tight text-gray-900">
              Reset your password
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Enter your email address and we'll send you a link to reset your
              password.
            </p>
          </div>

          <div className="mt-10">
            {error && (
              <div className="mb-4 text-sm text-red-600 bg-red-50 p-3 rounded-md">
                <div className="flex items-center gap-2">
                  <span>❌</span>
                  <span>{error}</span>
                </div>
              </div>
            )}

            {message && (
              <div className="mb-4 text-sm text-green-600 bg-green-50 p-3 rounded-md">
                <div className="flex items-center gap-2">
                  <span>✅</span>
                  <span>{message}</span>
                </div>
              </div>
            )}

            {isLoading && (
              <div className="mb-4 text-sm text-blue-600 bg-blue-50 p-3 rounded-md">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Sending reset email...</span>
                </div>
              </div>
            )}

            <form
              onSubmit={handleSubmit}
              className="space-y-6"
              id="reset-password-form"
            >
              <div>
                <Label
                  htmlFor="email"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Email address
                </Label>
                <div className="mt-2">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="px-4 py-1.5"
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              <div>
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? "Sending..." : "Send reset link"}
                </Button>
              </div>

              <p className="mt-4 text-center text-sm text-gray-600">
                Remember your password?{" "}
                <Link
                  href="/login"
                  className="font-medium text-primary hover:text-primary-500"
                >
                  Sign in
                </Link>
              </p>
            </form>

            <div className="mt-8 text-center">
              <p className="text-xs text-gray-500">
                © 2024 SageBase. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="relative hidden w-0 flex-[0.65] lg:block">
        <Image
          src="/images/login-background-new.png"
          alt="SageBase platform integrations"
          fill
          priority
          className="object-cover"
          sizes="(max-width: 1024px) 0vw, 50vw"
        />
      </div>
    </div>
  );
}
