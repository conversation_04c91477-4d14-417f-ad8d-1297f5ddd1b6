"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Lock, Mail, ArrowLeft } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function ResetPasswordPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  // Check for reset parameters
  const uid = searchParams.get("uid");
  const token = searchParams.get("token");
  const isResetMode = uid && token; // If both exist, show password reset form

  // For reset mode (uid + token in URL)
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // For request mode (no parameters)
  const [email, setEmail] = useState("");

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    try {
      const response = await fetch(
        "http://localhost:8001/api/integrations/auth/request-password-reset/",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email }),
        }
      );

      const data = await response.json();

      if (data.success) {
        setMessage({
          type: "success",
          text: "Password reset link sent to your email!",
        });
        setEmail("");
      } else {
        setMessage({
          type: "error",
          text: data.error || "Failed to send reset link",
        });
      }
    } catch (error) {
      setMessage({ type: "error", text: "Network error occurred" });
    }
    setLoading(false);
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newPassword !== confirmPassword) {
      setMessage({ type: "error", text: "Passwords do not match" });
      return;
    }

    if (newPassword.length < 8) {
      setMessage({
        type: "error",
        text: "Password must be at least 8 characters",
      });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const response = await fetch(
        "http://localhost:8001/api/integrations/auth/reset-password/",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            uid,
            token,
            new_password: newPassword,
          }),
        }
      );

      const data = await response.json();

      if (data.success) {
        // Save JWT token and redirect
        localStorage.setItem("authToken", data.token);
        setMessage({
          type: "success",
          text: "Password updated successfully! Redirecting to login...",
        });

        // Redirect to login after a short delay
        setTimeout(() => {
          router.push("/login?message=password-updated");
        }, 2000);
      } else {
        setMessage({
          type: "error",
          text: data.error || "Failed to update password",
        });
      }
    } catch (error) {
      setMessage({ type: "error", text: "Network error occurred" });
    }
    setLoading(false);
  };

  // Show create new password form if uid and token are in URL
  if (isResetMode) {
    return (
      <div className="flex min-h-screen bg-white">
        <div className="flex flex-[0.35] flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            <div className="flex flex-col items-center">
              <Image
                src="/images/sagebase-purple-full.svg"
                alt="SageBase Logo"
                width={220}
                height={50}
                className="mb-8"
                priority
              />

              <Card className="w-full">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary-100">
                    <Lock className="h-6 w-6 text-primary-600" />
                  </div>
                  <CardTitle className="text-2xl">
                    Create New Password
                  </CardTitle>
                  <CardDescription>
                    Enter your new password below
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  {message && (
                    <Alert
                      className={`mb-4 ${
                        message.type === "success"
                          ? "border-green-200 bg-green-50"
                          : "border-red-200 bg-red-50"
                      }`}
                    >
                      <AlertDescription
                        className={
                          message.type === "success"
                            ? "text-green-800"
                            : "text-red-800"
                        }
                      >
                        {message.text}
                      </AlertDescription>
                    </Alert>
                  )}

                  <form onSubmit={handlePasswordReset} className="space-y-4">
                    <div>
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        placeholder="Enter new password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        minLength={8}
                        required
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">
                        Confirm New Password
                      </Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        placeholder="Confirm new password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                        className="mt-1"
                      />
                    </div>

                    <Button type="submit" disabled={loading} className="w-full">
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating Password...
                        </>
                      ) : (
                        "Update Password"
                      )}
                    </Button>
                  </form>

                  <div className="mt-6 text-center">
                    <Link
                      href="/login"
                      className="text-sm text-primary-600 hover:text-primary-500 flex items-center justify-center"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Login
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <div className="flex-1 bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center">
          <Image
            src="/images/login-background-new.png"
            alt="Background"
            width={800}
            height={600}
            className="object-cover"
          />
        </div>
      </div>
    );
  }

  // Show request password reset form (current form)
  return (
    <div className="flex min-h-screen bg-white">
      <div className="flex flex-[0.35] flex-col justify-center items-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div className="flex flex-col items-center">
            <Image
              src="/images/sagebase-purple-full.svg"
              alt="SageBase Logo"
              width={220}
              height={50}
              className="mb-8"
              priority
            />

            <Card className="w-full">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary-100">
                  <Mail className="h-6 w-6 text-primary-600" />
                </div>
                <CardTitle className="text-2xl">Reset Password</CardTitle>
                <CardDescription>
                  Enter your email address and we'll send you a link to reset
                  your password.
                </CardDescription>
              </CardHeader>

              <CardContent>
                {message && (
                  <Alert
                    className={`mb-4 ${
                      message.type === "success"
                        ? "border-green-200 bg-green-50"
                        : "border-red-200 bg-red-50"
                    }`}
                  >
                    <AlertDescription
                      className={
                        message.type === "success"
                          ? "text-green-800"
                          : "text-red-800"
                      }
                    >
                      {message.text}
                    </AlertDescription>
                  </Alert>
                )}

                <form onSubmit={handleRequestReset} className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="mt-1"
                    />
                  </div>

                  <Button type="submit" disabled={loading} className="w-full">
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      "Send Reset Link"
                    )}
                  </Button>
                </form>

                <div className="mt-6 text-center">
                  <Link
                    href="/login"
                    className="text-sm text-primary-600 hover:text-primary-500 flex items-center justify-center"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Login
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <div className="flex-1 bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center">
        <Image
          src="/images/login-background-new.png"
          alt="Background"
          width={800}
          height={600}
          className="object-cover"
        />
      </div>
    </div>
  );
}
