"use client"
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";

// Simple toast notification function
const showToast = (message: string, type: 'success' | 'error' = 'error') => {
  // Create toast element
  const toast = document.createElement('div');
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg ${
    type === 'error' ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
  }`;
  toast.textContent = message;
  
  // Add to page
  document.body.appendChild(toast);
  
  // Remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 5000);
};

export default function GoogleDriveCallbackPage() {
  const { connectPlatform } = useConnectedPlatforms();
  const router = useRouter();

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const workspace_id = params.get("workspace_id");
    const success = params.get("success");
    const error = params.get("error");

    if (error) {
      // User cancelled or there was an error
      console.error("Google Drive OAuth error:", error);
      const errorMessage = error || "Google Drive connection was cancelled";
      showToast(`Google Drive connection failed: ${errorMessage}`, 'error');
      
      // Redirect to home page after error
      setTimeout(() => {
        router.push('/');
      }, 3000);
      return;
    }

    if (success === "true" && workspace_id) {
      // Connect the platform first
      connectPlatform("google-drive", { workspace_id });
      showToast("Google Drive connected successfully! Redirecting to integration page...", 'success');
      
      // Redirect to the Google Drive integration page where user can select file preferences
      setTimeout(() => {
        router.push(`/google-drive-integration?success=true&workspace_id=${workspace_id}`);
      }, 1500);
    } else {
      showToast("Failed to connect Google Drive.", 'error');
      
      // Redirect to home page after error
      setTimeout(() => {
        router.push('/');
      }, 3000);
    }
  }, [connectPlatform, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 text-center">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-green-600">Google Drive Connection Complete!</h1>
        <p className="text-gray-600 mb-6">
          Your Google Drive integration has been processed. You will be redirected to the integration page where you can select which files to include.
        </p>
        <div className="space-y-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto"></div>
          <p className="text-sm text-gray-500">
            Redirecting to Google Drive integration page...
          </p>
        </div>
      </div>
    </div>
  );
} 