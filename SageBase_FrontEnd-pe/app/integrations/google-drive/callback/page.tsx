"use client"
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";

// Simple toast notification function
const showToast = (message: string, type: 'success' | 'error' = 'error') => {
  // Create toast element
  const toast = document.createElement('div');
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md shadow-lg ${
    type === 'error' ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
  }`;
  toast.textContent = message;
  
  // Add to page
  document.body.appendChild(toast);
  
  // Remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast);
    }
  }, 5000);
};

export default function GoogleDriveCallbackPage() {
  const { connectPlatform } = useConnectedPlatforms();

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const workspace_id = params.get("workspace_id");
    const success = params.get("success");
    const error = params.get("error");

    if (error) {
      // User cancelled or there was an error
      console.error("Google Drive OAuth error:", error);
      const errorMessage = error || "Google Drive connection was cancelled";
      showToast(`Google Drive connection failed: ${errorMessage}`, 'error');
      return;
    }

    if (success === "true" && workspace_id) {
      connectPlatform("google-drive", { workspace_id });
      showToast("Google Drive connected successfully!", 'success');
    } else {
      showToast("Failed to connect Google Drive.", 'error');
    }
  }, [connectPlatform]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 text-center">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-green-600">Google Drive Connection Complete!</h1>
        <p className="text-gray-600 mb-6">
          Your Google Drive integration has been processed. You can now close this window and refresh your main page to see the updated connection status.
        </p>
        <div className="space-y-3">
          <button 
            onClick={() => window.close()} 
            className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Close Window
          </button>
          <p className="text-sm text-gray-500">
            If the window doesn't close automatically, you can close it manually and refresh your main page.
          </p>
        </div>
      </div>
    </div>
  );
} 