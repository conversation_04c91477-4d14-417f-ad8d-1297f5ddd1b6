"use client";

import { useState, useEffect } from "react";
import {
  Users,
  GitCommit,
  Code,
  GitPullRequest,
  FileText,
  Calendar,
  RefreshCw,
  FolderOpen,
  Plus,
  Github,
} from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { useAuth } from "@/contexts/auth-context";
import { useUserInfo } from "@/hooks/use-user-info";

// Types for the API responses
interface TeamMember {
  id: string;
  name: string;
  avatar: string;
  commits: number;
  linesOfCode: number;
  pullRequests: number;
  docContributions: number;
  role: string;
  contributionScore: number;
  email: string;
  lastActive: string;
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: string;
  repositoryUrl: string;
  documentationUrl: string;
  teamData: TeamMember[];
  lastUpdated: string;
  contributorsReady?: boolean;
  lastFetched?: string;
  totalCommits: number;
  totalLinesOfCode: number;
  totalPullRequests: number;
  totalDocContributions: number;
}

interface TimePeriod {
  id: string;
  label: string;
}

interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

interface GitHubRepository {
  id: number;
  full_name: string;
  name: string;
}

// Hardcoded time periods as per backend documentation
const TIME_PERIODS: TimePeriod[] = [
  { id: "lifetime", label: "Lifetime" },
  { id: "last-7-days", label: "Last 7 Days" },
  { id: "last-30-days", label: "Last 30 Days" },
  { id: "last-3-months", label: "Last 3 Months" },
  { id: "last-6-months", label: "Last 6 Months" },
];

// Backend configuration
const BACKEND_BASE_URL =
  process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8001";

export default function TeamDashboard() {
  const { user, connectedUserCompany } = useAuth();
  const { userInfo } = useUserInfo(user?.email);

  const [selectedPeriod, setSelectedPeriod] = useState("last_30_days");
  const [isRecalculating, setIsRecalculating] = useState(false);
  const [showPeriodSelector, setShowPeriodSelector] = useState(false);
  const [activeProject, setActiveProject] = useState("");
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Add project modal states
  const [showAddProjectModal, setShowAddProjectModal] = useState(false);
  const [githubRepos, setGithubRepos] = useState<GitHubRepository[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<string>("");
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [isLoadingRepos, setIsLoadingRepos] = useState(false);
  const [isAddingProject, setIsAddingProject] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);

  // Check if company ID is available
  const isCompanyIdAvailable = !!(
    userInfo?.company || connectedUserCompany?.company_id
  );

  // API Functions
  const fetchProjects = async (timeRange: string): Promise<Project[]> => {
    try {
      // For lifetime, don't send timeRange parameter
      const url =
        timeRange === "lifetime"
          ? `${BACKEND_BASE_URL}/api/projects/`
          : `${BACKEND_BASE_URL}/api/projects/?timeRange=${timeRange}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("authToken")}`,
        },
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;

        // Try to parse error details from response body
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (parseError) {
          // If we can't parse JSON, use status-based messages
          if (response.status === 400) {
            errorMessage = "Invalid request parameters";
          } else if (response.status === 401) {
            errorMessage = "Authentication failed. Please log in again.";
          } else if (response.status === 403) {
            errorMessage =
              "Access denied. You don't have permission to view projects.";
          } else if (response.status === 500) {
            errorMessage = "Server error. Please try again later.";
          }
        }

        throw new Error(errorMessage);
      }

      const result: ApiResponse<Project[]> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to fetch projects");
      }

      return result.data;
    } catch (error) {
      console.error("Error fetching projects:", error);
      throw error;
    }
  };

  const fetchProjectDetails = async (
    projectId: string,
    timeRange: string
  ): Promise<Project> => {
    try {
      // For lifetime, don't send timeRange parameter
      const url =
        timeRange === "lifetime"
          ? `${BACKEND_BASE_URL}/api/projects/${projectId}/`
          : `${BACKEND_BASE_URL}/api/projects/${projectId}/?timeRange=${timeRange}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("authToken")}`,
        },
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;

        // Try to parse error details from response body
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (parseError) {
          // If we can't parse JSON, use status-based messages
          if (response.status === 400) {
            errorMessage = "Invalid request parameters";
          } else if (response.status === 401) {
            errorMessage = "Authentication failed. Please log in again.";
          } else if (response.status === 403) {
            errorMessage =
              "Access denied. You don't have permission to view this project.";
          } else if (response.status === 404) {
            errorMessage = "Project not found";
          } else if (response.status === 500) {
            errorMessage = "Server error. Please try again later.";
          }
        }

        throw new Error(errorMessage);
      }

      const result: ApiResponse<Project> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to fetch project details");
      }

      return result.data;
    } catch (error) {
      console.error("Error fetching project details:", error);
      throw error;
    }
  };

  const recalculateMetrics = async (
    projectId: string,
    timeRange: string
  ): Promise<void> => {
    try {
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/projects/${projectId}/recalculate/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
          body: JSON.stringify({
            timeRange: timeRange === "lifetime" ? "" : timeRange,
          }),
        }
      );

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;

        // Try to parse error details from response body
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (parseError) {
          // If we can't parse JSON, use status-based messages
          if (response.status === 400) {
            errorMessage = "Invalid request parameters";
          } else if (response.status === 401) {
            errorMessage = "Authentication failed. Please log in again.";
          } else if (response.status === 403) {
            errorMessage =
              "Access denied. You don't have permission to recalculate metrics.";
          } else if (response.status === 404) {
            errorMessage = "Project not found";
          } else if (response.status === 500) {
            errorMessage = "Server error. Please try again later.";
          }
        }

        throw new Error(errorMessage);
      }

      const result: ApiResponse<{ message: string }> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to recalculate metrics");
      }

      // Refresh the data after recalculation
      await loadProjects();
    } catch (error) {
      console.error("Error recalculating metrics:", error);
      throw error;
    }
  };

  // GitHub API Functions
  const fetchGithubRepositories = async () => {
    try {
      setIsLoadingRepos(true);
      setError(null);

      // Get the company ID from the system (it's available in the logs)
      // Use the actual company ID from your system
      const companyId = userInfo?.company || connectedUserCompany?.company_id;

      if (!companyId) {
        throw new Error(
          "Company ID not available. Please ensure GitHub integration is connected."
        );
      }

      console.log("Using company ID:", companyId); // Debug log
      console.log(
        "Fetching GitHub repos from:",
        `${BACKEND_BASE_URL}/api/integrations/github-company-repos/?company_id=${companyId}`
      ); // Debug log

      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/github-company-repos/?company_id=${companyId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;

        // Try to parse error details from response body
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (parseError) {
          // If we can't parse JSON, use status-based messages
          if (response.status === 400) {
            errorMessage = "Company ID is required";
          } else if (response.status === 401) {
            errorMessage = "Authentication failed. Please log in again.";
          } else if (response.status === 403) {
            errorMessage =
              "Access denied. You don't have permission to view repositories.";
          } else if (response.status === 404) {
            errorMessage = "Company not found";
          } else if (response.status === 500) {
            errorMessage = "Server error. Please try again later.";
          }
        }

        throw new Error(errorMessage);
      }

      const repos = await response.json();
      console.log("GitHub repositories received:", repos); // Debug log
      console.log("Response status:", response.status); // Debug log
      console.log(
        "Response headers:",
        Object.fromEntries(response.headers.entries())
      ); // Debug log

      setGithubRepos(repos);

      if (repos.length === 0) {
        console.log("No repositories found - this might indicate:"); // Debug log
        console.log("1. GitHub integration not connected"); // Debug log
        console.log("2. No repositories configured"); // Debug log
        console.log("3. Permission issues"); // Debug log
        console.log("4. Company ID:", companyId); // Debug log
      } else {
        console.log(`Successfully loaded ${repos.length} repositories`); // Debug log
      }
    } catch (error) {
      console.error("Error fetching GitHub repositories:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        backendUrl: BACKEND_BASE_URL,
      });
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch GitHub repositories"
      );
    } finally {
      setIsLoadingRepos(false);
    }
  };

  const handleAddProject = async () => {
    if (!selectedRepo || !projectName.trim()) {
      setError("Please select a repository and enter a project name");
      return;
    }

    try {
      setIsAddingProject(true);
      setError(null);

      // Find the selected repository details
      const repo = githubRepos.find((r) => r.id.toString() === selectedRepo);
      if (!repo) {
        throw new Error("Selected repository not found");
      }

      // Call the backend API to create the project
      const createProjectResponse = await fetch(
        `${BACKEND_BASE_URL}/api/projects/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
          body: JSON.stringify({
            name: projectName,
            description: projectDescription,
            repository_url: repo.full_name,
            repository_id: repo.id,
            timeRange: selectedPeriod,
          }),
        }
      );

      if (!createProjectResponse.ok) {
        let errorMessage = `HTTP error! status: ${createProjectResponse.status}`;
        try {
          const errorData = await createProjectResponse.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (parseError) {
          if (createProjectResponse.status === 400) {
            errorMessage = "Invalid project data";
          } else if (createProjectResponse.status === 401) {
            errorMessage = "Authentication failed. Please log in again.";
          } else if (createProjectResponse.status === 403) {
            errorMessage =
              "Access denied. You don't have permission to create projects.";
          } else if (createProjectResponse.status === 500) {
            errorMessage = "Server error. Please try again later.";
          }
        }
        throw new Error(errorMessage);
      }

      const createdProject = await createProjectResponse.json();
      console.log("Project created successfully:", createdProject);

      // Close modal and refresh
      setShowAddProjectModal(false);
      setSelectedRepo("");
      setProjectName("");
      setProjectDescription("");

      // Refresh projects list and wait for contributors to be ready, showing loader
      await loadProjects();
      try {
        // Poll the single project endpoint briefly until contributorsReady is true (non-invasive, short-lived)
        // Limit to ~10 seconds
        const maxTries = 20;
        for (let i = 0; i < maxTries; i++) {
          // For lifetime, don't send timeRange parameter
          const url =
            selectedPeriod === "lifetime"
              ? `${BACKEND_BASE_URL}/api/projects/${createdProject.data.id}/`
              : `${BACKEND_BASE_URL}/api/projects/${createdProject.data.id}/?timeRange=${selectedPeriod}`;

          const res = await fetch(url, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("authToken")}`,
            },
          });
          if (res.ok) {
            const detail: ApiResponse<Project> = await res.json();
            if (detail?.data?.contributorsReady) {
              // Replace that project in list and set active
              setProjects((prev) =>
                prev.map((p) => (p.id === detail.data.id ? detail.data : p))
              );
              setActiveProject(detail.data.id);
              break;
            }
          }
          // small delay before re-check
          await new Promise((r) => setTimeout(r, 500));
        }
      } catch (e) {
        console.warn("Contributors readiness check failed", e);
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to add project"
      );
    } finally {
      setIsAddingProject(false);
    }
  };

  const openAddProjectModal = () => {
    console.log("Opening Add Project modal..."); // Debug log

    // Check if company ID is available
    if (!isCompanyIdAvailable) {
      setError(
        "Company ID not available. Please wait for user data to load or contact support."
      );
      return;
    }

    // Clean up any test data from localStorage
    if (localStorage.getItem("companyId") === "test-company-123") {
      localStorage.removeItem("companyId");
      console.log("Removed test company ID from localStorage");
    }

    setShowAddProjectModal(true);
    // fetchGithubRepositories(); // This will be triggered by the useEffect
  };

  const closeAddProjectModal = () => {
    setShowAddProjectModal(false);
    setSelectedRepo("");
    setProjectName("");
    setProjectDescription("");
    setError(null);
  };

  // Load initial data
  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Debug log for company ID
      console.log("Team Dashboard - Company ID Debug:", {
        userInfoCompany: userInfo?.company,
        connectedUserCompany: connectedUserCompany?.company_id,
        isCompanyIdAvailable,
        user: user?.email,
      });

      // Set default period
      setSelectedPeriod("lifetime");

      // Load projects with the selected period
      const projectsData = await fetchProjects("lifetime");
      setProjects(projectsData);

      // Set active project to first one if none selected
      if (projectsData.length > 0 && !activeProject) {
        setActiveProject(projectsData[0].id);
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to load initial data"
      );
    } finally {
      setLoading(false);
    }
  };

  // Load projects when period changes
  const loadProjects = async () => {
    if (!selectedPeriod) return;

    try {
      setLoading(true);
      setError(null);
      const projectsData = await fetchProjects(selectedPeriod);
      setProjects(projectsData);

      // Set active project to first one if none selected or if current active project doesn't exist
      if (
        projectsData.length > 0 &&
        (!activeProject || !projectsData.find((p) => p.id === activeProject))
      ) {
        setActiveProject(projectsData[0].id);
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to load projects"
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle period change
  const handlePeriodChange = (newPeriod: string) => {
    // Just update the selected period state
    // No API calls are made until "Update Data" button is clicked
    setSelectedPeriod(newPeriod);
  };

  // Handle project deletion
  const handleDeleteProject = async (projectId: string) => {
    if (!projectId) return;

    try {
      setError(null);

      const response = await fetch(
        `${BACKEND_BASE_URL}/api/projects/${projectId}/`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete project");
      }

      // Remove the project from the local state
      setProjects((prev) => prev.filter((p) => p.id !== projectId));

      // If the deleted project was active, set the first remaining project as active
      if (activeProject === projectId) {
        const remainingProjects = projects.filter((p) => p.id !== projectId);
        if (remainingProjects.length > 0) {
          setActiveProject(remainingProjects[0].id);
        } else {
          setActiveProject("");
        }
      }

      // Show success message
      setSuccessMessage("Project deleted successfully!");
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to delete project"
      );
    }
  };

  // Handle recalculation
  const handleRecalculate = async () => {
    if (!activeProject) return;

    try {
      setIsRecalculating(true);
      setError(null);

      // Call the recalculate endpoint to fetch fresh data
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/projects/${activeProject}/recalculate/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
          body: JSON.stringify({
            timeRange: selectedPeriod === "lifetime" ? "" : selectedPeriod,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to recalculate metrics");
      }

      const result = await response.json();

      // Refresh all projects data
      await loadProjects();

      // Also refresh the current project details specifically
      if (activeProject) {
        try {
          const projectResponse = await fetch(
            `${BACKEND_BASE_URL}/api/projects/${activeProject}/`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("authToken")}`,
              },
            }
          );

          if (projectResponse.ok) {
            const projectData = await projectResponse.json();
            if (projectData.success) {
              // Update the current project in the projects array
              setProjects((prev) =>
                prev.map((p) => (p.id === activeProject ? projectData.data : p))
              );
            }
          }
        } catch (refreshError) {
          console.log(
            "Could not refresh current project details:",
            refreshError
          );
        }
      }

      // Show success message
      setSuccessMessage(
        `Data updated successfully! Found ${result.data.contributors_found} contributors. Refreshing data...`
      );

      // Force a small delay to ensure backend processing is complete
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Refresh projects one more time to get the latest data
      await loadProjects();

      // Force a page refresh to show the new stats
      setTimeout(() => {
        window.location.reload();
      }, 1500);

      setTimeout(() => setSuccessMessage(null), 5000); // Clear after 5 seconds
    } catch (error) {
      setSuccessMessage(null); // Clear any success message
      setError(
        error instanceof Error ? error.message : "Failed to recalculate metrics"
      );
    } finally {
      setIsRecalculating(false);
    }
  };

  // Load initial data when component mounts
  useEffect(() => {
    loadInitialData();
  }, []);

  // Fetch GitHub repositories when company ID becomes available
  useEffect(() => {
    if (isCompanyIdAvailable && showAddProjectModal) {
      fetchGithubRepositories();
    }
  }, [isCompanyIdAvailable, showAddProjectModal]);

  // Note: Projects are only loaded when "Update Data" button is clicked
  // Period changes don't automatically trigger API calls

  const getContributionLevel = (score: number, teamSize: number) => {
    if (teamSize <= 0) {
      return {
        label: "N/A",
        color: "bg-gray-100 text-gray-800",
        bar: "bg-gray-500",
      };
    }

    const expected = 100.0 / teamSize;
    const ratio = expected > 0 ? score / expected : 0.0;

    if (ratio >= 1.8) {
      return {
        label: "Outstanding",
        color: "bg-green-100 text-green-800",
        bar: "bg-green-500",
      };
    } else if (ratio >= 1.3) {
      return {
        label: "Strong",
        color: "bg-green-100 text-green-800",
        bar: "bg-green-500",
      };
    } else if (ratio >= 0.9) {
      return {
        label: "Good",
        color: "bg-blue-100 text-blue-800",
        bar: "bg-blue-500",
      };
    } else if (ratio >= 0.5) {
      return {
        label: "Below Average",
        color: "bg-yellow-100 text-yellow-800",
        bar: "bg-yellow-500",
      };
    } else {
      return {
        label: "Needs Attention",
        color: "bg-red-100 text-red-800",
        bar: "bg-red-500",
      };
    }
  };

  // Helper function to format date
  const formatLastFetched = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMinutes = Math.floor(
        (now.getTime() - date.getTime()) / (1000 * 60)
      );

      if (diffInMinutes < 1) return "Just now";
      if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) return `${diffInHours} hours ago`;

      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays < 7) return `${diffInDays} days ago`;

      return date.toLocaleDateString();
    } catch {
      return "Unknown";
    }
  };

  // Get current project data
  const currentProject = projects.find((p) => p.id === activeProject);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-3 text-gray-600 text-sm">
            Loading Team Dashboard...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg mb-3">
            Error loading dashboard
          </div>
          <div className="text-gray-600 mb-3 text-sm">{error}</div>
          <button
            onClick={loadInitialData}
            className="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Do not early-return on empty state; keep Dialog mounted

  return (
    <div className="flex h-screen bg-gray-50">
      <SideNavigation />
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopNavigation />
        <main className="flex-1 overflow-auto p-4">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Users className="h-8 w-8 text-blue-600" />
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                      Team Contribution Dashboard
                    </h1>
                    <p className="text-base text-gray-600 mt-1">
                      Monitor team performance and contribution metrics per
                      project
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {/* Last Updated Info */}
                  {currentProject && currentProject.lastFetched && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Last updated:</span>
                      <span className="font-medium">
                        {formatLastFetched(currentProject.lastFetched)}
                      </span>
                    </div>
                  )}

                  <Button
                    onClick={openAddProjectModal}
                    disabled={!isCompanyIdAvailable}
                    className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 text-sm rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Plus className="h-4 w-4" />
                    {isCompanyIdAvailable ? "Add Project" : "Loading..."}
                  </Button>
                  <button
                    onClick={() => setShowPeriodSelector(true)}
                    disabled={!currentProject || isRecalculating}
                    className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 text-sm rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isRecalculating ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        <span>Calculating...</span>
                      </div>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4" />
                        Update Stats
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Period Selector Modal - Only shown when Update Stats is clicked */}
            {showPeriodSelector && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Select Time Period for New Stats
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-gray-500" />
                      <select
                        value={selectedPeriod}
                        onChange={(e) => setSelectedPeriod(e.target.value)}
                        className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {TIME_PERIODS.map((period) => (
                          <option key={period.id} value={period.id}>
                            {period.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button
                        onClick={() => setShowPeriodSelector(false)}
                        variant="outline"
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={async () => {
                          setShowPeriodSelector(false);
                          await handleRecalculate();
                        }}
                        disabled={isRecalculating}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isRecalculating ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                            <span>Calculating...</span>
                          </div>
                        ) : (
                          "Calculate Stats"
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Success Message */}
            {successMessage && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-800 font-medium">
                    {successMessage}
                  </span>
                </div>
              </div>
            )}

            {/* Project Tabs or Empty State */}
            {projects.length === 0 ? (
              <div className="flex h-64 items-center justify-center">
                <div className="text-center">
                  <div className="text-gray-500 text-lg mb-3">
                    No projects found
                  </div>
                  <div className="text-gray-600 mb-4">
                    Create your first project to get started
                  </div>
                  <Button
                    onClick={openAddProjectModal}
                    disabled={!isCompanyIdAvailable}
                    className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 text-sm rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Plus className="h-4 w-4" />
                    {isCompanyIdAvailable
                      ? "Add Your First Project"
                      : "Loading..."}
                  </Button>
                </div>
              </div>
            ) : (
              <Tabs
                value={activeProject}
                onValueChange={setActiveProject}
                className="flex gap-8"
              >
                <div className="flex-1">
                  {currentProject && (
                    <TabsContent
                      key={currentProject.id}
                      value={currentProject.id}
                      className="space-y-6"
                    >
                      {/* Project Tabs - Above the Table */}
                      <div className="mb-6">
                        {/* Project Tabs */}
                        <div className="border-b-2 border-gray-300">
                          {projects.length === 0 ? (
                            <div className="text-center py-8">
                              <FolderOpen className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                              <p className="text-gray-500 text-sm">
                                No projects yet
                              </p>
                              <p className="text-gray-400 text-xs">
                                Create your first project to get started
                              </p>
                            </div>
                          ) : (
                            <div className="flex space-x-1">
                              {projects.map((project) => (
                                <button
                                  key={project.id}
                                  onClick={() => setActiveProject(project.id)}
                                  className={`group relative px-4 py-2 text-sm font-medium transition-all duration-200 ${
                                    project.id === activeProject
                                      ? "text-blue-900 bg-white border-2 border-gray-300 border-b-0 rounded-t-lg"
                                      : "text-gray-600 bg-gray-50 hover:text-gray-800 hover:bg-gray-100 border-2 border-transparent hover:border-gray-200 rounded-t-lg"
                                  }`}
                                >
                                  <div className="flex items-center gap-2">
                                    <FolderOpen className="w-4 h-4" />
                                    <span className="font-semibold text-sm">
                                      {project.name || "Unnamed Project"}
                                    </span>
                                    {/* Active status dot */}
                                    <div
                                      className={`w-2 h-2 rounded-full ${
                                        project.status === "active"
                                          ? "bg-green-500"
                                          : project.status === "completed"
                                          ? "bg-blue-500"
                                          : project.status === "paused"
                                          ? "bg-yellow-500"
                                          : "bg-gray-400"
                                      }`}
                                    ></div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Connected Content Area */}
                        {currentProject && (
                          <div className="border-2 border-gray-300 border-t-0 rounded-b-lg bg-white p-6">
                            {/* Project Info */}
                            <div className="mb-4">
                              <div className="flex items-center justify-between">
                                <div>
                                  <h2 className="text-lg font-bold text-gray-900 mb-2">
                                    Project:{" "}
                                    {currentProject.name || "Unnamed Project"}
                                  </h2>
                                  {currentProject.description && (
                                    <p className="text-gray-600 text-base">
                                      {currentProject.description}
                                    </p>
                                  )}
                                </div>
                                <div className="flex items-center gap-3">
                                  {currentProject.repositoryUrl && (
                                    <a
                                      href={currentProject.repositoryUrl}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-md transition-colors text-sm font-medium"
                                    >
                                      <svg
                                        className="w-4 h-4"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                        />
                                      </svg>
                                      {currentProject.repositoryUrl
                                        ?.replace("https://github.com/", "")
                                        .replace("https://gitlab.com/", "") ||
                                        "View Repository"}
                                    </a>
                                  )}
                                  <Button
                                    variant="outline"
                                    onClick={() =>
                                      setProjectToDelete(currentProject)
                                    }
                                    className="text-red-700 hover:text-white hover:bg-red-600 border-red-300 bg-white"
                                  >
                                    <svg
                                      className="w-4 h-4 mr-2"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M6 18L18 6M6 6l12 12"
                                      />
                                    </svg>
                                    Delete Project
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Team Members Table - Enhanced and Prominent */}
                      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-4 py-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Users className="w-5 h-5 text-white" />
                              <h2 className="text-lg font-bold text-white">
                                Team Contributions
                              </h2>
                            </div>
                            <div className="text-blue-100 text-sm">
                              {currentProject.teamData?.length || 0} Team
                              Members
                            </div>
                          </div>
                        </div>

                        {!currentProject.contributorsReady &&
                        (!currentProject.teamData ||
                          currentProject.teamData.length === 0) ? (
                          <div className="p-10 flex items-center justify-center">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
                              <p className="mt-3 text-gray-600">
                                Fetching contributors…
                              </p>
                            </div>
                          </div>
                        ) : (
                          <div className="overflow-x-auto">
                            <table className="w-full">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                                    Team Member
                                  </th>
                                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                                    Commits
                                  </th>
                                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                                    Lines of Code
                                  </th>
                                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                                    Pull Requests
                                  </th>

                                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                                    Performance Score
                                  </th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {(currentProject.teamData || [])
                                  .sort(
                                    (a, b) =>
                                      (b.contributionScore || 0) -
                                      (a.contributionScore || 0)
                                  )
                                  .map((member, index) => {
                                    const level = getContributionLevel(
                                      member.contributionScore,
                                      currentProject.teamData?.length || 0
                                    );
                                    return (
                                      <tr
                                        key={member.id}
                                        className={`hover:bg-blue-50 transition-colors ${
                                          index === 0 ? "bg-blue-50/50" : ""
                                        }`}
                                      >
                                        <td className="px-4 py-3 whitespace-nowrap">
                                          <div className="flex items-center">
                                            <div
                                              className={`h-10 w-10 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                                                index === 0
                                                  ? "bg-yellow-500"
                                                  : index === 1
                                                  ? "bg-gray-400"
                                                  : index === 2
                                                  ? "bg-amber-600"
                                                  : "bg-blue-600"
                                              }`}
                                            >
                                              {member.avatar}
                                            </div>
                                            <div className="ml-3">
                                              <div className="text-sm font-semibold text-gray-900">
                                                {member.name ||
                                                  "Unknown Member"}
                                                {index === 0 && (
                                                  <span className="ml-2 inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                                                    🏆 Top Contributor
                                                  </span>
                                                )}
                                              </div>
                                              <div className="text-sm text-gray-600">
                                                {member.role || "No Role"}
                                              </div>
                                            </div>
                                          </div>
                                        </td>
                                        <td className="px-4 py-3 whitespace-nowrap">
                                          <div className="flex items-center">
                                            <GitCommit className="h-4 w-4 text-blue-500 mr-2" />
                                            <span className="text-base font-semibold text-gray-900">
                                              {member.commits || 0}
                                            </span>
                                          </div>
                                        </td>
                                        <td className="px-4 py-3 whitespace-nowrap">
                                          <div className="flex items-center">
                                            <Code className="h-4 w-4 text-green-500 mr-2" />
                                            <span className="text-base font-semibold text-gray-900">
                                              {(
                                                member.linesOfCode || 0
                                              ).toLocaleString()}
                                            </span>
                                          </div>
                                        </td>
                                        <td className="px-4 py-3 whitespace-nowrap">
                                          <div className="flex items-center">
                                            <GitPullRequest className="h-4 w-4 text-purple-500 mr-2" />
                                            <span className="text-base font-semibold text-gray-900">
                                              {member.pullRequests || 0}
                                            </span>
                                          </div>
                                        </td>

                                        <td className="px-4 py-3 whitespace-nowrap">
                                          <div className="flex items-center gap-3">
                                            <div className="flex-1">
                                              <span
                                                className={`text-base font-semibold ${level.color}`}
                                              >
                                                {(
                                                  member.contributionScore || 0
                                                ).toFixed(2)}
                                              </span>
                                            </div>
                                            <span
                                              className={`px-2 py-1 text-xs font-semibold rounded-full ${level.color}`}
                                            >
                                              {level.label}
                                            </span>
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                              </tbody>
                            </table>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  )}
                </div>
              </Tabs>
            )}
          </div>
        </main>
      </div>

      {/* Delete Project Confirmation Dialog */}
      <AlertDialog
        open={!!projectToDelete}
        onOpenChange={() => setProjectToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Project</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{projectToDelete?.name}"? This
              action cannot be undone and will remove all associated data
              including team contributions and metrics.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setProjectToDelete(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (projectToDelete) {
                  handleDeleteProject(projectToDelete.id);
                  setProjectToDelete(null);
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Project
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Project Modal */}
      <Dialog open={showAddProjectModal} onOpenChange={setShowAddProjectModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Github className="h-5 w-5" />
              Add New Project from GitHub
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Select a GitHub repository to create a new project.
            </p>
          </DialogHeader>

          <div className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {!isCompanyIdAvailable && (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md text-sm">
                <div className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                  <span className="font-medium">Company ID not available</span>
                </div>
                <p className="mt-1">
                  Please wait for user data to load or contact support if this
                  persists.
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="project-name">Project Name</Label>
              <Input
                id="project-name"
                placeholder="Enter project name"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="project-description">
                Description (Optional)
              </Label>
              <Input
                id="project-description"
                placeholder="Enter project description"
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="repository">Select GitHub Repository</Label>
              <Select value={selectedRepo} onValueChange={setSelectedRepo}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a repository" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingRepos ? (
                    <div className="flex items-center justify-center py-6">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-3"></div>
                        <p className="text-sm text-gray-600 font-medium">
                          Loading GitHub repositories...
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          This may take a few moments
                        </p>
                      </div>
                    </div>
                  ) : githubRepos.length === 0 ? (
                    <div className="space-y-3">
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center gap-2 text-yellow-800">
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                            />
                          </svg>
                          <span className="text-sm font-medium">
                            No repositories found
                          </span>
                        </div>
                        <p className="text-sm text-yellow-700 mt-1">
                          This could mean:
                        </p>
                        <ul className="text-sm text-yellow-700 mt-1 ml-4 list-disc">
                          <li>GitHub integration is not connected</li>
                          <li>
                            No repositories are configured for this company
                          </li>
                          <li>
                            Repository access permissions need to be updated
                          </li>
                        </ul>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => window.open("/integrations", "_blank")}
                        className="w-full"
                      >
                        <Github className="w-4 h-4 mr-2" />
                        Connect GitHub Integration
                      </Button>
                    </div>
                  ) : (
                    githubRepos.map((repo) => (
                      <SelectItem key={repo.id} value={repo.id.toString()}>
                        <div className="flex items-center gap-2">
                          <Github className="h-4 w-4" />
                          <span className="font-medium">{repo.name}</span>
                          <span className="text-gray-500 text-xs">
                            ({repo.full_name})
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="outline"
                onClick={closeAddProjectModal}
                disabled={isAddingProject}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddProject}
                disabled={
                  !selectedRepo ||
                  !projectName.trim() ||
                  isAddingProject ||
                  !isCompanyIdAvailable
                }
                className="bg-green-600 hover:bg-green-700"
              >
                {isAddingProject ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Adding...
                  </>
                ) : (
                  "Add Project"
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
