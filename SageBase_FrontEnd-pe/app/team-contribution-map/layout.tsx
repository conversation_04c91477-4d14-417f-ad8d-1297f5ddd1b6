import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import { Open_Sans } from "next/font/google"

const montserrat = Montserrat({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-montserrat",
  weight: ["400", "600", "700", "900"],
})

const openSans = Open_Sans({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-open-sans",
  weight: ["400", "500", "600"],
})

export const metadata: Metadata = {
  title: "Team Knowledge Map | SageBase",
  description: "Track team performance and project contributions across your organization",
}

export default function TeamKnowledgeMapLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className={`${montserrat.variable} ${openSans.variable} antialiased`}>
      {children}
    </div>
  )
}
