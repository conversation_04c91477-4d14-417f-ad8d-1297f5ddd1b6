import { NextRequest, NextResponse } from 'next/server';
import { QA, UpdateQARequest } from '@/types/qa';

// Mock data - in production this would be a database query
const getAllQAs = (): QA[] => {
  return [
    {
      id: 'qa-1',
      projectId: 'nova',
      question: {
        title: "Error: Cannot find module '@babel/preset-env'",
        content: "ERROR: \"Error: Cannot find module '@babel/preset-env' - This is a very weird error!\"",
        tags: ['babel', 'npm', 'error', 'dev-environment', 'build-tools'],
        author: {
          id: 'dev-1',
          name: 'Developer',
          avatar: '/diverse-avatars.png',
        },
        timestamp: '18:53',
        date: 'Today',
      },
      answer: {
        content: "Yes, I also had it yesterday, and I've done this command to fix it:",
        code: 'npm install --save-dev @babel/preset-env && npm cache clean --force',
        explanation: "This is a solution that I found myself after debugging the build process. It seems there's an issue with the babel dependencies not being properly installed or cached incorrectly.",
        author: {
          id: 'wissem-1',
          name: 'Wissem',
          avatar: '/diverse-group-avatars.png',
        },
        timestamp: '19:53',
        date: 'Today',
        isVerified: true,
      },
      votes: {
        upvotes: 12,
        downvotes: 0,
      },
      metadata: {
        views: 42,
        helpful: 12,
        editedBy: {
          name: 'Sarah Johnson',
          avatar: '/diverse-group-avatars.png',
          date: 'Today at 20:15',
        },
        approvedBy: {
          name: 'Tech Lead',
          avatar: '/diverse-avatars.png',
          date: 'Today at 20:30',
        },
      },
      createdAt: new Date('2024-02-08T18:53:00'),
      updatedAt: new Date('2024-02-08T20:30:00'),
    },
    {
      id: 'qa-2',
      projectId: 'nova',
      question: {
        title: 'How to setup TypeScript with Next.js?',
        content: 'I am trying to add TypeScript to my existing Next.js project. What is the best way to do this?',
        tags: ['typescript', 'nextjs', 'setup', 'configuration'],
        author: {
          id: 'dev-2',
          name: 'Alex Chen',
          avatar: '/diverse-avatars.png',
        },
        timestamp: '14:20',
        date: 'Yesterday',
      },
      answer: {
        content: 'You can add TypeScript to an existing Next.js project by following these steps:',
        code: 'npm install --save-dev typescript @types/react @types/node\nnpx tsc --init\n# Then rename your files from .js to .tsx/.ts',
        explanation: 'Next.js has built-in TypeScript support. After installing the dependencies and creating a tsconfig.json, Next.js will automatically configure TypeScript for you.',
        author: {
          id: 'dev-3',
          name: 'Maria Rodriguez',
          avatar: '/diverse-group-avatars.png',
        },
        timestamp: '14:45',
        date: 'Yesterday',
        isVerified: true,
      },
      votes: {
        upvotes: 8,
        downvotes: 1,
      },
      metadata: {
        views: 28,
        helpful: 8,
        approvedBy: {
          name: 'Senior Dev',
          avatar: '/diverse-avatars.png',
          date: 'Yesterday at 15:30',
        },
      },
      createdAt: new Date('2024-02-07T14:20:00'),
      updatedAt: new Date('2024-02-07T15:30:00'),
    },
    {
      id: 'qa-3',
      projectId: 'atlas',
      question: {
        title: 'Docker container fails to start',
        content: 'Getting "port already in use" error when starting Docker container. How can I fix this?',
        tags: ['docker', 'port', 'error', 'container'],
        author: {
          id: 'dev-4',
          name: 'John Smith',
          avatar: '/diverse-avatars.png',
        },
        timestamp: '09:15',
        date: 'Today',
      },
      answer: {
        content: 'This error occurs when the port is already being used by another process. Here are a few solutions:',
        code: '# Check what is using the port\nlsof -i :3000\n\n# Kill the process\nkill -9 <PID>\n\n# Or use a different port\ndocker run -p 3001:3000 myapp',
        explanation: 'Always check which process is using the port before killing it. Alternatively, you can configure your application to use a different port.',
        author: {
          id: 'dev-5',
          name: 'Emily Davis',
          avatar: '/diverse-group-avatars.png',
        },
        timestamp: '09:30',
        date: 'Today',
        isVerified: true,
      },
      votes: {
        upvotes: 15,
        downvotes: 0,
      },
      metadata: {
        views: 67,
        helpful: 15,
      },
      createdAt: new Date('2024-02-08T09:15:00'),
      updatedAt: new Date('2024-02-08T09:30:00'),
    },
  ];
};

let mockQAs = getAllQAs();

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // TODO: Replace with actual database query
    // const qa = await db.qa.findUnique({
    //   where: { id },
    //   include: {
    //     question: { include: { author: true } },
    //     answer: { include: { author: true } },
    //     votes: true,
    //     metadata: true,
    //   },
    // });

    const qa = mockQAs.find(q => q.id === id);

    if (!qa) {
      return NextResponse.json(
        {
          success: false,
          error: 'Q&A not found',
        },
        { status: 404 }
      );
    }

    // Increment view count
    qa.metadata.views += 1;

    return NextResponse.json({
      success: true,
      data: qa,
    });
  } catch (error) {
    console.error('Error fetching Q&A:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch Q&A',
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body: UpdateQARequest = await request.json();

    // TODO: Replace with actual database update
    const qaIndex = mockQAs.findIndex(q => q.id === id);
    
    if (qaIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Q&A not found',
        },
        { status: 404 }
      );
    }

    const qa = mockQAs[qaIndex];

    // Update question fields
    if (body.question) {
      if (body.question.title) qa.question.title = body.question.title;
      if (body.question.content) qa.question.content = body.question.content;
      if (body.question.tags) qa.question.tags = body.question.tags;
    }

    // Update answer fields
    if (body.answer && qa.answer) {
      if (body.answer.content) qa.answer.content = body.answer.content;
      if (body.answer.code !== undefined) qa.answer.code = body.answer.code;
      if (body.answer.explanation !== undefined) qa.answer.explanation = body.answer.explanation;
      if (body.answer.isVerified !== undefined) qa.answer.isVerified = body.answer.isVerified;
    }

    qa.updatedAt = new Date();

    mockQAs[qaIndex] = qa;

    return NextResponse.json({
      success: true,
      data: qa,
    });
  } catch (error) {
    console.error('Error updating Q&A:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update Q&A',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // TODO: Replace with actual database deletion
    const qaIndex = mockQAs.findIndex(q => q.id === id);
    
    if (qaIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Q&A not found',
        },
        { status: 404 }
      );
    }

    mockQAs.splice(qaIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'Q&A deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting Q&A:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete Q&A',
      },
      { status: 500 }
    );
  }
}