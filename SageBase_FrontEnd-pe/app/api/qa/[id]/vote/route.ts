import { NextRequest, NextResponse } from 'next/server';
import { VoteRequest } from '@/types/qa';

// Mock data - in production this would be a database query
const getAllQAs = () => {
  return [
    {
      id: 'qa-1',
      projectId: 'nova',
      question: {
        title: "Error: Cannot find module '@babel/preset-env'",
        content: "ERROR: \"Error: Cannot find module '@babel/preset-env' - This is a very weird error!\"",
        tags: ['babel', 'npm', 'error', 'dev-environment', 'build-tools'],
        author: {
          id: 'dev-1',
          name: 'Developer',
          avatar: '/diverse-avatars.png',
        },
        timestamp: '18:53',
        date: 'Today',
      },
      answer: {
        content: "Yes, I also had it yesterday, and I've done this command to fix it:",
        code: 'npm install --save-dev @babel/preset-env && npm cache clean --force',
        explanation: "This is a solution that I found myself after debugging the build process. It seems there's an issue with the babel dependencies not being properly installed or cached incorrectly.",
        author: {
          id: 'wissem-1',
          name: 'Wissem',
          avatar: '/diverse-group-avatars.png',
        },
        timestamp: '19:53',
        date: 'Today',
        isVerified: true,
      },
      votes: {
        upvotes: 12,
        downvotes: 0,
      },
      metadata: {
        views: 42,
        helpful: 12,
        editedBy: {
          name: 'Sarah <PERSON>',
          avatar: '/diverse-group-avatars.png',
          date: 'Today at 20:15',
        },
        approvedBy: {
          name: 'Tech Lead',
          avatar: '/diverse-avatars.png',
          date: 'Today at 20:30',
        },
      },
      createdAt: new Date('2024-02-08T18:53:00'),
      updatedAt: new Date('2024-02-08T20:30:00'),
    },
    {
      id: 'qa-2',
      projectId: 'nova',
      votes: { upvotes: 8, downvotes: 1 },
    },
    {
      id: 'qa-3',
      projectId: 'atlas',
      votes: { upvotes: 15, downvotes: 0 },
    },
  ];
};

let mockQAs = getAllQAs();

// Mock user votes storage - in production this would be in database
const userVotes: Record<string, Record<string, "up" | "down">> = {};

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body: VoteRequest = await request.json();
    
    const { type } = body;

    if (type !== 'up' && type !== 'down') {
      return NextResponse.json(
        {
          success: false,
          error: 'Vote type must be "up" or "down"',
        },
        { status: 400 }
      );
    }

    // TODO: Get user ID from authentication
    const userId = 'current-user';

    // Find the Q&A
    const qa = mockQAs.find(q => q.id === id);
    
    if (!qa) {
      return NextResponse.json(
        {
          success: false,
          error: 'Q&A not found',
        },
        { status: 404 }
      );
    }

    // Initialize user votes for this user if not exists
    if (!userVotes[userId]) {
      userVotes[userId] = {};
    }

    const previousVote = userVotes[userId][id];

    // Handle vote logic
    if (previousVote === type) {
      // User is removing their vote
      if (type === 'up') {
        qa.votes.upvotes = Math.max(0, qa.votes.upvotes - 1);
      } else {
        qa.votes.downvotes = Math.max(0, qa.votes.downvotes - 1);
      }
      delete userVotes[userId][id];
    } else {
      // User is changing their vote or voting for the first time
      if (previousVote) {
        // Remove previous vote
        if (previousVote === 'up') {
          qa.votes.upvotes = Math.max(0, qa.votes.upvotes - 1);
        } else {
          qa.votes.downvotes = Math.max(0, qa.votes.downvotes - 1);
        }
      }

      // Add new vote
      if (type === 'up') {
        qa.votes.upvotes += 1;
      } else {
        qa.votes.downvotes += 1;
      }
      userVotes[userId][id] = type;
    }

    // TODO: In production, update database with vote counts and user vote record
    // await db.qa.update({
    //   where: { id },
    //   data: { 
    //     votes: {
    //       update: {
    //         upvotes: qa.votes.upvotes,
    //         downvotes: qa.votes.downvotes,
    //       }
    //     }
    //   }
    // });
    // await db.userVote.upsert({
    //   where: { userId_qaId: { userId, qaId: id } },
    //   create: { userId, qaId: id, type },
    //   update: { type },
    // });

    return NextResponse.json({
      success: true,
      data: {
        votes: qa.votes,
        userVote: userVotes[userId][id] || null,
      },
    });
  } catch (error) {
    console.error('Error processing vote:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process vote',
      },
      { status: 500 }
    );
  }
}