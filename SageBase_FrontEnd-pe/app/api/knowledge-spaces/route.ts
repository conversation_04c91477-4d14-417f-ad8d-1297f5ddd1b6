import { NextRequest, NextResponse } from 'next/server';
import { Project } from '@/types/qa';

// Mock data for development - replace with actual database calls
const mockProjects: Project[] = [
  {
    id: 'atlas',
    name: 'Atlas',
    color: '#3b82f6',
    initial: 'A',
    qaCount: 12,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-10'),
  },
  {
    id: 'nova',
    name: 'Nova',
    color: '#10b981',
    initial: 'N',
    qaCount: 8,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-02-08'),
  },
  {
    id: 'phoenix',
    name: '<PERSON>',
    color: '#f59e0b',
    initial: 'P',
    qaCount: 5,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-05'),
  },
];

export async function GET(request: NextRequest) {
  try {
    // TODO: Replace with actual database query
    // const projects = await db.project.findMany({
    //   select: {
    //     id: true,
    //     name: true,
    //     color: true,
    //     initial: true,
    //     _count: { select: { qas: true } },
    //     createdAt: true,
    //     updatedAt: true,
    //   },
    // });

    return NextResponse.json({
      success: true,
      data: mockProjects,
    });
  } catch (error) {
    console.error('Error fetching knowledge spaces:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch knowledge spaces',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, color, initial } = body;

    if (!name || !color || !initial) {
      return NextResponse.json(
        {
          success: false,
          error: 'Name, color, and initial are required',
        },
        { status: 400 }
      );
    }

    // TODO: Replace with actual database creation
    const newProject: Project = {
      id: name.toLowerCase().replace(/\s+/g, '-'),
      name,
      color,
      initial: initial.toUpperCase(),
      qaCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // await db.project.create({ data: newProject });
    mockProjects.push(newProject);

    return NextResponse.json({
      success: true,
      data: newProject,
    });
  } catch (error) {
    console.error('Error creating knowledge space:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create knowledge space',
      },
      { status: 500 }
    );
  }
}