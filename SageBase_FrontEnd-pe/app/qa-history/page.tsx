"use client";

import { useState } from "react";
import {
  Search,
  Archive,
  ExternalLink,
  MessageSquare,
  Calendar,
  User,
  Hash,
  CheckCircle,
  XCircle,
  X,
  Edit,
  Bell,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import SideNavigation from "@/components/side-navigation";
import TopNavigation from "@/components/top-navigation";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useQAApprovals } from "@/hooks/use-qa-approvals";
import { useGenericNotifications } from "@/hooks/use-generic-notifications";
import QAEditDialog from "@/components/qa-edit-dialog";
import { PendingQAApproval, QAEditData } from "@/services/qa-approval-api";
import { Notification } from "@/types/notifications";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function QAPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["all"]);
  const [activeTab, setActiveTab] = useState("qa");

  // State for bulk action confirmation dialogs
  const [showApproveAllDialog, setShowApproveAllDialog] = useState(false);
  const [showRejectAllDialog, setShowRejectAllDialog] = useState(false);
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false);

  // State for edit dialog
  const [editingQA, setEditingQA] = useState<PendingQAApproval | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Use the Q&A approvals hook
  const {
    pendingApprovals,
    isLoading: qaLoading,
    error: qaError,
    approveQA,
    rejectQA,
    editQA,
    fetchPendingApprovals,
  } = useQAApprovals();

  // Use the generic notifications hook
  const {
    notifications: genericNotifications,
    isLoading: notificationsLoading,
    error: notificationsError,
    acceptNotification,
    ignoreNotification,
    fetchNotifications,
  } = useGenericNotifications();

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "slack":
        return "💬";
      case "teams":
        return "👥";
      case "github":
        return "🐙";
      case "discord":
        return "🎮";
      case "google-drive":
        return "📁";
      default:
        return "📝";
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const togglePlatform = (platform: string) => {
    if (platform === "all") {
      setSelectedPlatforms(["all"]);
    } else {
      setSelectedPlatforms((prev) => {
        const filtered = prev.filter((p) => p !== "all");
        if (filtered.includes(platform)) {
          const newSelection = filtered.filter((p) => p !== platform);
          return newSelection.length === 0 ? ["all"] : newSelection;
        } else {
          return [...filtered, platform];
        }
      });
    }
  };

  const filteredApprovals = pendingApprovals.filter((qa) => {
    const matchesSearch =
      qa.question?.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      qa.answer?.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      qa.question?.tags?.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      );
    const matchesPlatform =
      selectedPlatforms.includes("all") ||
      selectedPlatforms.includes("knowledge-base");

    return matchesSearch && matchesPlatform;
  });

  // Bulk action functions
  const handleApproveAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Approving all Q&A approvals...");
      const qaApprovalIds = pendingApprovals.map((qa) => qa.id);

      // Approve each Q&A one by one
      for (const qaId of qaApprovalIds) {
        await approveQA(qaId);
      }

      console.log("✅ All Q&A approvals completed successfully");
      setShowApproveAllDialog(false);
    } catch (error) {
      console.error("❌ Error approving all Q&As:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleRejectAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Rejecting all Q&A approvals...");
      const qaApprovalIds = pendingApprovals.map((qa) => qa.id);

      // Reject each Q&A one by one
      for (const qaId of qaApprovalIds) {
        await rejectQA(qaId, "Bulk rejected by admin");
      }

      console.log("✅ All Q&A rejections completed successfully");
      setShowRejectAllDialog(false);
    } catch (error) {
      console.error("❌ Error rejecting all Q&As:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleEditQA = async (
    qaId: string,
    projectId: string,
    editData: QAEditData
  ) => {
    try {
      await editQA(qaId, projectId, editData);
      setShowEditDialog(false);
      setEditingQA(null);
    } catch (error) {
      console.error("Error editing QA:", error);
    }
  };

  const openEditDialog = (qa: PendingQAApproval) => {
    setEditingQA(qa);
    setShowEditDialog(true);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Side Navigation */}
      <SideNavigation />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigation />

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Q&A and Notifications
              </h1>
              <p className="text-gray-600">
                Review and approve knowledge contributions from your team
              </p>
              {qaError && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-800 text-sm">
                    Error loading Q&A approvals: {qaError}
                  </p>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={fetchPendingApprovals}
                    className="mt-2"
                  >
                    Retry
                  </Button>
                </div>
              )}
            </div>

            {/* Loading State */}
            {qaLoading && (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading Q&A approvals...</p>
                </CardContent>
              </Card>
            )}

            {/* Content when not loading */}
            {!qaLoading && (
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="qa">Q&A Approvals</TabsTrigger>
                  <TabsTrigger value="notifications">Notifications</TabsTrigger>
                </TabsList>

                <TabsContent value="qa" className="space-y-6">
                  {/* Filters and Search */}
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex flex-col lg:flex-row gap-4">
                        {/* Search */}
                        <div className="flex-1">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <Input
                              placeholder="Search questions, answers, or tags..."
                              value={searchQuery}
                              onChange={(e) => setSearchQuery(e.target.value)}
                              className="pl-10"
                            />
                          </div>
                        </div>

                        {/* Platform Filter - Circular Icons */}
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
                            Platforms:
                          </span>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => togglePlatform("all")}
                              className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all ${
                                selectedPlatforms.includes("all")
                                  ? "border-primary bg-primary text-white"
                                  : "border-gray-300 bg-white hover:border-gray-400"
                              }`}
                              title="All Platforms"
                            >
                              <span className="text-xs font-bold">All</span>
                            </button>
                            <button
                              onClick={() => togglePlatform("slack")}
                              className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all ${
                                selectedPlatforms.includes("slack")
                                  ? "border-purple-500 bg-purple-500"
                                  : "border-gray-300 bg-white hover:border-purple-300"
                              }`}
                              title="Slack"
                            >
                              <img
                                src="/images/platform-logos/slack.svg"
                                alt="Slack"
                                className={`w-5 h-5 ${
                                  selectedPlatforms.includes("slack")
                                    ? "filter brightness-0 invert"
                                    : ""
                                }`}
                              />
                            </button>
                            <button
                              onClick={() => togglePlatform("teams")}
                              className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all ${
                                selectedPlatforms.includes("teams")
                                  ? "border-blue-500 bg-blue-500"
                                  : "border-gray-300 bg-white hover:border-blue-300"
                              }`}
                              title="Microsoft Teams"
                            >
                              <img
                                src="/images/platform-logos/teams.svg"
                                alt="Teams"
                                className={`w-5 h-5 ${
                                  selectedPlatforms.includes("teams")
                                    ? "filter brightness-0 invert"
                                    : ""
                                }`}
                              />
                            </button>
                            <button
                              onClick={() => togglePlatform("github")}
                              className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all ${
                                selectedPlatforms.includes("github")
                                  ? "border-gray-800 bg-gray-800"
                                  : "border-gray-300 bg-white hover:border-gray-400"
                              }`}
                              title="GitHub"
                            >
                              <img
                                src="/images/platform-logos/github.svg"
                                alt="GitHub"
                                className={`w-5 h-5 ${
                                  selectedPlatforms.includes("github")
                                    ? "filter brightness-0 invert"
                                    : ""
                                }`}
                              />
                            </button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Bulk Action Buttons */}
                  {filteredApprovals.length > 5 && (
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">
                            Bulk Actions ({filteredApprovals.length} items)
                          </span>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setShowApproveAllDialog(true)}
                              disabled={isBulkActionLoading}
                              className="text-xs bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Approve All ({filteredApprovals.length})
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setShowRejectAllDialog(true)}
                              disabled={isBulkActionLoading}
                              className="text-xs bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
                            >
                              <XCircle className="h-3 w-3 mr-1" />
                              Reject All ({filteredApprovals.length})
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Results Summary */}
                  <div>
                    <p className="text-sm text-gray-600">
                      Showing {filteredApprovals.length} pending Q&A approvals
                    </p>
                  </div>

                  {/* Q&A List */}
                  <div className="space-y-6">
                    {filteredApprovals.map((qa) => (
                      <Card
                        key={qa.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardContent className="p-6">
                          {/* Header with Platform and Time */}
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center space-x-2 bg-white rounded-full px-3 py-1 shadow-sm border">
                                <span className="text-base">
                                  {getPlatformIcon("knowledge-base")}
                                </span>
                                <span className="text-sm font-semibold text-gray-900">
                                  Knowledge Space
                                </span>
                                <span className="text-xs text-gray-500">
                                  • Q&A
                                </span>
                              </div>
                            </div>
                            <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                              {formatDate(qa.created_at)}
                            </span>
                          </div>

                          {/* Question Section */}
                          <div className="mb-4 bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-xs font-bold text-blue-700 bg-blue-200 px-2 py-1 rounded-full">
                                QUESTION
                              </span>
                              <div className="flex items-center space-x-1">
                                <span className="text-xs font-medium text-blue-800">
                                  {qa.question?.author?.name || "Unknown"}
                                </span>
                                <span className="text-xs text-blue-600">
                                  • {qa.question?.timestamp || ""}
                                </span>
                              </div>
                            </div>
                            <p className="text-sm font-medium text-gray-900 leading-relaxed">
                              {typeof qa.question === "string"
                                ? qa.question
                                : qa.question?.content || "No question content"}
                            </p>
                            {qa.question?.tags && (
                              <div className="flex flex-wrap gap-1 mt-3">
                                {qa.question.tags.map((tag, index) => (
                                  <span
                                    key={index}
                                    className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium"
                                  >
                                    #{tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* Answer Section */}
                          <div className="mb-4 bg-green-50 rounded-lg p-4 border-l-4 border-green-400">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-xs font-bold text-green-700 bg-green-200 px-2 py-1 rounded-full">
                                SOLUTION
                              </span>
                              <div className="flex items-center space-x-1">
                                <span className="text-xs font-medium text-green-800">
                                  {qa.answer?.author?.name || "Unknown"}
                                </span>
                                <span className="text-xs text-green-600">
                                  • {qa.answer?.timestamp || ""}
                                </span>
                                {qa.answer?.isVerified && (
                                  <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full font-bold">
                                    ✓ VERIFIED
                                  </span>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-gray-800 leading-relaxed">
                              {typeof qa.answer === "string"
                                ? qa.answer
                                : qa.answer?.content || "No answer content"}
                            </p>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex space-x-3">
                            <Button
                              size="sm"
                              onClick={() => approveQA(qa.id)}
                              disabled={qaLoading}
                              className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white font-medium shadow-sm"
                            >
                              <Archive className="h-4 w-4 mr-2" />
                              {qaLoading ? "Approving..." : "Approve & Store"}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openEditDialog(qa)}
                              disabled={qaLoading}
                              className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 font-medium"
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit Before Approving
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                rejectQA(qa.id, "Rejected by admin")
                              }
                              disabled={qaLoading}
                              className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 font-medium"
                            >
                              <X className="h-4 w-4 mr-1" />
                              {qaLoading ? "Rejecting..." : "Reject"}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {filteredApprovals.length === 0 && (
                    <Card>
                      <CardContent className="p-12 text-center">
                        <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          No pending Q&A approvals found
                        </h3>
                        <p className="text-gray-500">
                          All Q&A approvals have been processed or try adjusting
                          your search criteria
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                {/* Notifications Tab */}
                <TabsContent value="notifications" className="space-y-6">
                  {/* Notifications Error */}
                  {notificationsError && (
                    <Card>
                      <CardContent className="p-4">
                        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                          <p className="text-red-800 text-sm">
                            Error loading notifications: {notificationsError}
                          </p>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={fetchNotifications}
                            className="mt-2"
                          >
                            Retry
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Notifications Loading */}
                  {notificationsLoading && (
                    <Card>
                      <CardContent className="p-12 text-center">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-gray-600">
                          Loading notifications...
                        </p>
                      </CardContent>
                    </Card>
                  )}

                  {/* Notifications Content */}
                  {!notificationsLoading && (
                    <>
                      {/* Search for Notifications */}
                      <Card>
                        <CardContent className="p-6">
                          <div className="flex flex-col lg:flex-row gap-4">
                            <div className="flex-1">
                              <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <Input
                                  placeholder="Search notifications..."
                                  value={searchQuery}
                                  onChange={(e) =>
                                    setSearchQuery(e.target.value)
                                  }
                                  className="pl-10"
                                />
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Notifications Summary */}
                      <div>
                        <p className="text-sm text-gray-600">
                          Showing {genericNotifications.length} notifications
                        </p>
                      </div>

                      {/* Notifications List */}
                      <div className="space-y-6">
                        {genericNotifications
                          .filter((notification) => {
                            const matchesSearch =
                              notification.title
                                ?.toLowerCase()
                                .includes(searchQuery.toLowerCase()) ||
                              notification.details
                                ?.toLowerCase()
                                .includes(searchQuery.toLowerCase()) ||
                              notification.type
                                ?.toLowerCase()
                                .includes(searchQuery.toLowerCase());
                            return matchesSearch;
                          })
                          .map((notification) => (
                            <Card
                              key={notification.id}
                              className="hover:shadow-md transition-shadow"
                            >
                              <CardContent className="p-6">
                                {/* Header with Type and Time */}
                                <div className="flex items-center justify-between mb-4">
                                  <div className="flex items-center space-x-3">
                                    <div className="flex items-center space-x-2 bg-blue-50 rounded-full px-3 py-1 shadow-sm border border-blue-200">
                                      <Bell className="h-4 w-4 text-blue-600" />
                                      <span className="text-sm font-semibold text-blue-800">
                                        {notification.type.replace("_", " ")}
                                      </span>
                                      <span className="text-xs text-blue-600">
                                        • {notification.severity}
                                      </span>
                                    </div>
                                  </div>
                                  <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                                    {formatDate(notification.created_at)}
                                  </span>
                                </div>

                                {/* Notification Content */}
                                <div className="mb-4">
                                  <h4 className="text-sm font-semibold text-gray-900 mb-2">
                                    {notification.title}
                                  </h4>
                                  <p className="text-sm text-gray-700 mb-3">
                                    {notification.details}
                                  </p>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex space-x-3">
                                  <Button
                                    size="sm"
                                    onClick={() =>
                                      acceptNotification(notification.id)
                                    }
                                    disabled={notificationsLoading}
                                    className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white font-medium shadow-sm"
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    {notificationsLoading
                                      ? "Acknowledging..."
                                      : "Acknowledge"}
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                      ignoreNotification(notification.id)
                                    }
                                    disabled={notificationsLoading}
                                    className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 font-medium"
                                  >
                                    <X className="h-4 w-4 mr-1" />
                                    {notificationsLoading
                                      ? "Ignoring..."
                                      : "Ignore"}
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          ))}

                        {genericNotifications.length === 0 && (
                          <Card>
                            <CardContent className="p-12 text-center">
                              <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                              <h3 className="text-lg font-medium text-gray-900 mb-2">
                                No notifications found
                              </h3>
                              <p className="text-gray-500">
                                All notifications have been processed or try
                                adjusting your search criteria
                              </p>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    </>
                  )}
                </TabsContent>
              </Tabs>
            )}

            {/* Approve All Confirmation Dialog */}
            <AlertDialog
              open={showApproveAllDialog}
              onOpenChange={setShowApproveAllDialog}
            >
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Approve All Q&A Approvals</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to approve all{" "}
                    {filteredApprovals.length} pending Q&A approvals? This
                    action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isBulkActionLoading}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleApproveAll}
                    disabled={isBulkActionLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isBulkActionLoading ? "Approving..." : "Approve All"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Reject All Confirmation Dialog */}
            <AlertDialog
              open={showRejectAllDialog}
              onOpenChange={setShowRejectAllDialog}
            >
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Reject All Q&A Approvals</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to reject all{" "}
                    {filteredApprovals.length} pending Q&A approvals? This
                    action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isBulkActionLoading}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleRejectAll}
                    disabled={isBulkActionLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {isBulkActionLoading ? "Rejecting..." : "Reject All"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </main>
      </div>

      {/* QA Edit Dialog */}
      <QAEditDialog
        qa={editingQA}
        isOpen={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={handleEditQA}
        isLoading={qaLoading}
      />
    </div>
  );
}
