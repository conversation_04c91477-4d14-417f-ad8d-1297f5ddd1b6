"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import UserNotificationModal from "@/components/user-notification-modal";

export default function NotificationDemoPage() {
  const [showProcessingNotification, setShowProcessingNotification] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showWarningNotification, setShowWarningNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">User Notification Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Processing Notification */}
        <div className="border border-gray-200 rounded-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Content Processing Notification</h3>
          <p className="text-gray-600 mb-4">
            Shows progress and next steps for content processing workflows.
          </p>
          <Button 
            onClick={() => setShowProcessingNotification(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Show Processing Notification
          </Button>
        </div>

        {/* Success Notification */}
        <div className="border border-gray-200 rounded-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Success Notification</h3>
          <p className="text-gray-600 mb-4">
            Confirms successful completion of operations.
          </p>
          <Button 
            onClick={() => setShowSuccessNotification(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            Show Success Notification
          </Button>
        </div>

        {/* Warning Notification */}
        <div className="border border-gray-200 rounded-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Warning Notification</h3>
          <p className="text-gray-600 mb-4">
            Alerts users about potential issues or important information.
          </p>
          <Button 
            onClick={() => setShowWarningNotification(true)}
            className="bg-yellow-600 hover:bg-yellow-700"
          >
            Show Warning Notification
          </Button>
        </div>

        {/* Error Notification */}
        <div className="border border-gray-200 rounded-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Error Notification</h3>
          <p className="text-gray-600 mb-4">
            Informs users about errors and provides guidance.
          </p>
          <Button 
            onClick={() => setShowErrorNotification(true)}
            className="bg-red-600 hover:bg-red-700"
          >
            Show Error Notification
          </Button>
        </div>
      </div>

      {/* Processing Notification Modal */}
      <UserNotificationModal
        isOpen={showProcessingNotification}
        onClose={() => setShowProcessingNotification(false)}
        message="Folders processed."
        type="info"
        details={[
          { label: "Folders", value: 1 },
          { label: "Files queued for inclusion", value: 1 }
        ]}
        nextStep="Click 'Start Embedding' to process the content."
        additionalInfo="You'll receive a notification when embedding completes."
        buttonText="OK"
      />

      {/* Success Notification Modal */}
      <UserNotificationModal
        isOpen={showSuccessNotification}
        onClose={() => setShowSuccessNotification(false)}
        message="Document successfully uploaded and processed!"
        type="success"
        details={[
          { label: "Document Name", value: "project_report.pdf" },
          { label: "Processing Time", value: "2.3 seconds" }
        ]}
        nextStep="Document is now available in your workspace."
        additionalInfo="You can now search and analyze this content."
        buttonText="Great!"
      />

      {/* Warning Notification Modal */}
      <UserNotificationModal
        isOpen={showWarningNotification}
        onClose={() => setShowWarningNotification(false)}
        message="Storage space is running low."
        type="warning"
        details={[
          { label: "Available Space", value: "2.1 GB" },
          { label: "Used Space", value: "47.9 GB" }
        ]}
        nextStep="Consider cleaning up old documents or upgrading your plan."
        additionalInfo="You'll be notified when space is critically low."
        buttonText="I Understand"
      />

      {/* Error Notification Modal */}
      <UserNotificationModal
        isOpen={showErrorNotification}
        onClose={() => setShowErrorNotification(false)}
        message="Failed to connect to external service."
        type="error"
        details={[
          { label: "Service", value: "GitHub API" },
          { label: "Error Code", value: "401 Unauthorized" }
        ]}
        nextStep="Please check your authentication credentials and try again."
        additionalInfo="If the problem persists, contact support."
        buttonText="Try Again"
      />
    </div>
  );
}
