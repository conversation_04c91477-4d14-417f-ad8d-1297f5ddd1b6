"use client";

import { useEffect, useState } from "react";
import { getBackendUrl } from "@/lib/api-config";

export default function TestQAPage() {
  const [projects, setProjects] = useState([]);
  const [qas, setQas] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get auth headers
        const getAuthHeaders = async (): Promise<HeadersInit> => {
          // Get auth token from localStorage (Django JWT)
          const authToken = localStorage.getItem("authToken");
          if (authToken) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${authToken}`,
            };
          }
          return { "Content-Type": "application/json" };
        };

        const headers = await getAuthHeaders();
        const API_BASE_URL = getBackendUrl();

        // Fetch knowledge spaces
        const projectsResponse = await fetch(
          `${API_BASE_URL}/api/knowledge-spaces`,
          { headers }
        );
        if (projectsResponse.ok) {
          const projectsResult = await projectsResponse.json();
          console.log("Projects API response:", projectsResult);
          setProjects(projectsResult.data || []);

          // Fetch Q&As for each project
          for (const project of projectsResult.data || []) {
            const qaResponse = await fetch(
              `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${project.id}`,
              { headers }
            );
            if (qaResponse.ok) {
              const qaResult = await qaResponse.json();
              console.log(`Q&As for ${project.id}:`, qaResult);
              setQas((prev) => ({
                ...prev,
                [project.id]: qaResult.data || [],
              }));
            }
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Q&A API Test</h1>

      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Knowledge Spaces</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {projects.map((project: any) => (
              <div key={project.id} className="bg-white border rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <div
                    className="w-8 h-8 rounded flex items-center justify-center mr-3"
                    style={{ backgroundColor: project.color }}
                  >
                    <span className="text-white font-bold text-sm">
                      {project.initial}
                    </span>
                  </div>
                  <h3 className="font-semibold">{project.name}</h3>
                </div>
                <p className="text-sm text-gray-600">Q&As: {project.qaCount}</p>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Q&As by Project</h2>
          {Object.entries(qas).map(([projectId, projectQAs]) => (
            <div key={projectId} className="mb-6">
              <h3 className="text-lg font-semibold mb-3 capitalize">
                {projectId}
              </h3>
              <div className="space-y-3">
                {projectQAs.map((qa: any) => (
                  <div key={qa.id} className="bg-white border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium">{qa.question.title}</h4>
                      <div className="text-sm text-gray-500">ID: {qa.id}</div>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">
                      {qa.question.content}
                    </p>
                    {qa.answer && (
                      <div className="bg-green-50 border border-green-200 rounded p-3">
                        <p className="text-sm mb-2">{qa.answer.content}</p>
                        {qa.answer.code && (
                          <pre className="bg-gray-900 text-green-400 text-xs p-2 rounded overflow-x-auto">
                            {qa.answer.code}
                          </pre>
                        )}
                      </div>
                    )}
                    <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                      <span>Author: {qa.question.author.name}</span>
                      <span>
                        Votes: ↑{qa.votes.upvotes} ↓{qa.votes.downvotes}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
