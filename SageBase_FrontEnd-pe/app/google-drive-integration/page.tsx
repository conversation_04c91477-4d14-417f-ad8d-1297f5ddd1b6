"use client";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Folder, 
  Link, 
  Unlink, 
  Loader2, 
  CheckCircle2, 
  XCircle,
  Settings,
  FileText,
  Eye,
  EyeOff
} from "lucide-react";
import GoogleDriveFileBrowser from "@/components/google-drive-file-browser";

const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

interface GoogleDriveStatus {
  connected: boolean;
  status: string;
  workspace_id?: string;
  folder_id?: string;
  folder_name?: string;
  google_user_email?: string;
  last_sync?: string;
  connected_at?: string;
}

export default function GoogleDriveIntegrationPage() {
  const { user, connectedUserCompany } = useAuth();
  const { platforms, refreshPlatforms, disconnectPlatform } = useConnectedPlatforms();
  const [googleDriveStatus, setGoogleDriveStatus] = useState<GoogleDriveStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("overview");

  const googleDrivePlatform = platforms.find(p => p.id === "google-drive");

  // Check Google Drive connection status
  const checkGoogleDriveStatus = async () => {
    if (!user || !connectedUserCompany) return;

    try {
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/google-drive/status/?company_id=${connectedUserCompany.company_id}&workspace=default`
      );

      if (response.ok) {
        const status = await response.json();
        setGoogleDriveStatus(status);
      } else {
        setGoogleDriveStatus({ connected: false, status: "DISCONNECTED" });
      }
    } catch (error) {
      console.error('Error checking Google Drive status:', error);
      setGoogleDriveStatus({ connected: false, status: "ERROR" });
    }
  };

  // Connect Google Drive
  const connectGoogleDrive = async () => {
    if (!user || !connectedUserCompany) return;

    setIsLoading(true);
    try {
      const authUrl = `${BACKEND_BASE_URL}/api/integrations/google-drive/start/?company_id=${connectedUserCompany.company_id}&workspace=default`;
      window.open(
        authUrl,
        "_blank",
        "width=600,height=700,scrollbars=yes,resizable=yes"
      );
    } catch (error) {
      console.error('Error starting Google Drive OAuth:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Disconnect Google Drive
  const disconnectGoogleDrive = async () => {
    if (!user || !connectedUserCompany) return;

    setIsLoading(true);
    try {
      // Use the platform disconnect method instead of the custom endpoint
      await disconnectPlatform("google-drive");
      await checkGoogleDriveStatus();
    } catch (error) {
      console.error('Error disconnecting Google Drive:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Set root folder
  const setRootFolder = async (folderId: string, folderName: string) => {
    if (!user || !googleDriveStatus?.workspace_id) return;

    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/set-root-folder/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspace_id: googleDriveStatus.workspace_id,
          folder_id: folderId,
          folder_name: folderName,
        }),
      });

      if (response.ok) {
        await checkGoogleDriveStatus();
      }
    } catch (error) {
      console.error('Error setting root folder:', error);
    }
  };

  useEffect(() => {
    checkGoogleDriveStatus();
  }, [user, connectedUserCompany]);

  // Listen for popup window messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'google-drive-connected') {
        checkGoogleDriveStatus();
        refreshPlatforms();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  if (!user || !connectedUserCompany) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <p className="text-center text-gray-500">Please log in to access Google Drive integration.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Google Drive Integration</h1>
        <p className="text-gray-600">
          Connect your Google Drive account to browse and select files for SageBase
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="files" disabled={!googleDriveStatus?.connected}>
            Files & Folders
          </TabsTrigger>
          <TabsTrigger value="settings" disabled={!googleDriveStatus?.connected}>
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Connection Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Connection Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {googleDriveStatus ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {googleDriveStatus.connected ? (
                        <CheckCircle2 className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircle className="h-6 w-6 text-red-500" />
                      )}
                      <div>
                        <p className="font-medium">
                          {googleDriveStatus.connected ? "Connected" : "Not Connected"}
                        </p>
                        <p className="text-sm text-gray-500">
                          {googleDriveStatus.google_user_email || "No account linked"}
                        </p>
                      </div>
                    </div>
                    <Badge variant={googleDriveStatus.connected ? "default" : "secondary"}>
                      {googleDriveStatus.status}
                    </Badge>
                  </div>

                  {googleDriveStatus.connected && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Root Folder</p>
                        <p className="text-gray-500">{googleDriveStatus.folder_name || "My Drive"}</p>
                      </div>
                      <div>
                        <p className="font-medium">Connected Since</p>
                        <p className="text-gray-500">
                          {googleDriveStatus.connected_at 
                            ? new Date(googleDriveStatus.connected_at).toLocaleDateString()
                            : "Unknown"
                          }
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex gap-3">
                    {!googleDriveStatus.connected ? (
                      <Button
                        onClick={connectGoogleDrive}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Link className="h-4 w-4" />
                        )}
                        Connect Google Drive
                      </Button>
                    ) : (
                      <Button
                        variant="destructive"
                        onClick={disconnectGoogleDrive}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Unlink className="h-4 w-4" />
                        )}
                        Disconnect
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          {googleDriveStatus?.connected && (
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Manage your Google Drive integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab("files")}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Browse Files
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab("settings")}
                    className="flex items-center gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="files" className="space-y-6">
          {googleDriveStatus?.connected && googleDriveStatus.workspace_id ? (
            <GoogleDriveFileBrowser
              workspaceId={googleDriveStatus.workspace_id}
              onFileSelectionChange={setSelectedFiles}
            />
          ) : (
            <Card>
              <CardContent className="p-6">
                <p className="text-center text-gray-500">
                  Please connect your Google Drive account first.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {googleDriveStatus?.connected ? (
            <Card>
              <CardHeader>
                <CardTitle>Integration Settings</CardTitle>
                <CardDescription>
                  Configure your Google Drive integration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Root Folder</h3>
                  <p className="text-sm text-gray-500 mb-3">
                    Currently set to: {googleDriveStatus.folder_name || "My Drive"}
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setRootFolder("root", "My Drive")}
                  >
                    Reset to My Drive
                  </Button>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Account Information</h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Email:</span> {googleDriveStatus.google_user_email}
                    </div>
                    <div>
                      <span className="font-medium">Connected:</span>{" "}
                      {googleDriveStatus.connected_at 
                        ? new Date(googleDriveStatus.connected_at).toLocaleString()
                        : "Unknown"
                      }
                    </div>
                    {googleDriveStatus.last_sync && (
                      <div>
                        <span className="font-medium">Last Sync:</span>{" "}
                        {new Date(googleDriveStatus.last_sync).toLocaleString()}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6">
                <p className="text-center text-gray-500">
                  Please connect your Google Drive account first.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 