"use client";
import { useState, useEffect } from "react";
import { getBackendUrl, getFrontendUrl } from '@/lib/api-config';
import { useAuth } from "@/contexts/auth-context";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Folder, 
  Link as LinkIcon, 
  Unlink, 
  Loader2, 
  CheckCircle2, 
  XCircle,
  FileText,
  Eye,
  EyeOff,
  Play,
  CheckCircle,
  Home,
  AlertCircle
} from "lucide-react";
import GoogleDriveFileBrowser from "@/components/google-drive-file-browser";
import Link from "next/link";

const BACKEND_BASE_URL = getBackendUrl();

interface GoogleDriveStatus {
  connected: boolean;
  status: string;
  workspace_id?: string;
  folder_id?: string;
  folder_name?: string;
  google_user_email?: string;
  last_sync?: string;
  connected_at?: string;
}

export default function GoogleDriveIntegrationPage() {
  const { user, connectedUserCompany } = useAuth();
  const { platforms, refreshPlatforms, disconnectPlatform } = useConnectedPlatforms();
  const [googleDriveStatus, setGoogleDriveStatus] = useState<GoogleDriveStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("overview");
  const [isEmbedding, setIsEmbedding] = useState(false);
  const [embeddingProgress, setEmbeddingProgress] = useState<string>("");
  const [showEmbeddingNotice, setShowEmbeddingNotice] = useState(false);
  const [oauthSuccess, setOauthSuccess] = useState(false);
  const [showGoogleDriveApproval, setShowGoogleDriveApproval] = useState(false);

  const googleDrivePlatform = platforms.find(p => p.id === "google-drive");

  // Check for OAuth success redirect
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const workspaceId = urlParams.get('workspace_id');
    
    if (success === 'true' && workspaceId) {
      setOauthSuccess(true);
      // Clear the URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
      // Check status to get the workspace info
      checkGoogleDriveStatus();
      // Switch to files tab
      setActiveTab("files");
    }
  }, []);

  // Check Google Drive connection status
  const checkGoogleDriveStatus = async () => {
    if (!user || !connectedUserCompany) return;

    try {
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/google-drive/status/?company_id=${connectedUserCompany.company_id}&workspace=default`
      );

      if (response.ok) {
        const status = await response.json();
        setGoogleDriveStatus(status);
      } else {
        setGoogleDriveStatus({ connected: false, status: "DISCONNECTED" });
      }
    } catch (error) {
      console.error('Error checking Google Drive status:', error);
      setGoogleDriveStatus({ connected: false, status: "ERROR" });
    }
  };

  // Connect Google Drive
  const connectGoogleDrive = async () => {
    // Show Google Drive approval popup instead of proceeding with connection
    setShowGoogleDriveApproval(true);
    return;
  };

  // Disconnect Google Drive
  const disconnectGoogleDrive = async () => {
    if (!user || !connectedUserCompany) return;

    setIsLoading(true);
    try {
      // Use the platform disconnect method instead of the custom endpoint
      await disconnectPlatform("google-drive");
      await checkGoogleDriveStatus();
    } catch (error) {
      console.error('Error disconnecting Google Drive:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Start embedding process
  const startEmbedding = async () => {
    if (!googleDriveStatus?.workspace_id) return;

    setIsEmbedding(true);
    setShowEmbeddingNotice(true);
    setEmbeddingProgress("Starting embedding process...");

    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/start-embedding/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspace_id: googleDriveStatus.workspace_id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start indexing process');
      }

      const result = await response.json();
      
             if (result.success) {
         setEmbeddingProgress(`Embedding completed! ${result.summary.successful} files processed successfully, ${result.summary.failed} failed.`);
         
         // Show completion message for 5 seconds, then allow user to close
         setTimeout(() => {
           setShowEmbeddingNotice(false);
           setIsEmbedding(false);
           
           // Show completion message instead of redirecting
           setEmbeddingProgress(`✅ Embedding completed successfully! ${result.summary.successful} files processed, ${result.summary.failed} failed. You may now close this window and your files will be processed for a while.`);
         }, 2000);
       } else {
        throw new Error(result.error || 'Embedding failed');
      }
    } catch (error) {
      console.error('Error starting embedding:', error);
      setEmbeddingProgress(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Hide notice after 5 seconds
      setTimeout(() => {
        setShowEmbeddingNotice(false);
        setIsEmbedding(false);
      }, 5000);
    }
  };

  useEffect(() => {
    checkGoogleDriveStatus();
  }, [user, connectedUserCompany]);

  // Listen for popup window messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'google-drive-connected') {
        checkGoogleDriveStatus();
        refreshPlatforms();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  if (!user || !connectedUserCompany) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <p className="text-center text-gray-500">Please log in to access Google Drive integration.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
             {/* OAuth Success Notice */}
       {oauthSuccess && (
         <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
           <div className="flex items-center gap-3">
             <CheckCircle className="h-5 w-5 text-green-500" />
             <div>
               <h3 className="font-medium text-green-800">Google Drive Connected Successfully!</h3>
               <p className="text-sm text-green-600">
                 Your Google Drive account has been connected. You can now browse and select files for indexing.
               </p>
             </div>
           </div>
         </div>
       )}

       {/* Embedding Completion Notice */}
       {!isEmbedding && embeddingProgress && embeddingProgress.includes('✅') && (
         <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
           <div className="flex items-center gap-3">
             <CheckCircle className="h-5 w-5 text-blue-500" />
             <div>
               
               <p className="text-xs text-blue-600 mt-2">
                 You may now close this window Now. Check the page {getFrontendUrl()}/ai-search, youll get notifications when indexing is complete.
               </p>
             </div>
           </div>
         </div>
       )}

      {/* Embedding Notice Popup */}
      {showEmbeddingNotice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <div className="flex items-center gap-3 mb-4">
              {isEmbedding ? (
                <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
              ) : (
                <CheckCircle className="h-6 w-6 text-green-500" />
              )}
              <h3 className="text-lg font-medium">
                {isEmbedding ? 'Processing Your Files' : 'Embedding Complete!'}
              </h3>
            </div>
                         <p className="text-gray-600 mb-4">
               {embeddingProgress}
             </p>
             {!isEmbedding && (
               <p className="text-sm text-gray-500 mb-4">
                 You may now close this window and your files will be processed for a while.
               </p>
             )}
            <div className="text-center">
              <Button
                onClick={() => setShowEmbeddingNotice(false)}
                variant="outline"
                disabled={isEmbedding}
              >
                {isEmbedding ? 'Processing...' : 'Close'}
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <Link href="/ai-search">
            <Button variant="outline" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Home
            </Button>
          </Link>
        </div>
        <h1 className="text-3xl font-bold mb-2">Google Drive Integration</h1>
        <p className="text-gray-600">
          Connect your Google Drive account to browse and select files for SageBase
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="files" disabled={!googleDriveStatus?.connected}>
            Files & Folders
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Connection Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Connection Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {googleDriveStatus ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {googleDriveStatus.connected ? (
                        <CheckCircle2 className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircle className="h-6 w-6 text-red-500" />
                      )}
                      <div>
                        <p className="font-medium">
                          {googleDriveStatus.connected ? "Connected" : "Not Connected"}
                        </p>
                        <p className="text-sm text-gray-500">
                          {googleDriveStatus.google_user_email || "No account linked"}
                        </p>
                      </div>
                    </div>
                    <Badge variant={googleDriveStatus.connected ? "default" : "secondary"}>
                      {googleDriveStatus.status}
                    </Badge>
                  </div>

                  {googleDriveStatus.connected && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Root Folder</p>
                        <p className="text-gray-500">{googleDriveStatus.folder_name || "My Drive"}</p>
                      </div>
                      <div>
                        <p className="font-medium">Connected Since</p>
                        <p className="text-gray-500">
                          {googleDriveStatus.connected_at 
                            ? new Date(googleDriveStatus.connected_at).toLocaleDateString()
                            : "Unknown"
                          }
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex gap-3">
                    {!googleDriveStatus.connected ? (
                      <Button
                        onClick={connectGoogleDrive}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <LinkIcon className="h-4 w-4" />
                        )}
                        Connect Google Drive
                      </Button>
                    ) : (
                      <Button
                        variant="destructive"
                        onClick={disconnectGoogleDrive}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Unlink className="h-4 w-4" />
                        )}
                        Disconnect
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          {googleDriveStatus?.connected && (
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Manage your Google Drive integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab("files")}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Browse Files
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="files" className="space-y-6">
          {googleDriveStatus?.connected && googleDriveStatus.workspace_id ? (
            <div className="space-y-6">
              {/* Continue Button for Embedding */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="h-5 w-5" />
                    Start Indexing Process
                  </CardTitle>
                  <CardDescription>
                    After selecting your file preferences, click continue to start indexing your files into SageBase
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={startEmbedding}
                    disabled={isEmbedding}
                    className="flex items-center gap-2"
                    size="lg"
                  >
                    {isEmbedding ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                    {isEmbedding ? 'Processing...' : 'Continue & Start Indexing'}
                  </Button>
                  <p className="text-sm text-gray-500 mt-2">
                    This will process all files marked as "Included" and may take several minutes depending on the number of files.
                  </p>
                </CardContent>
              </Card>

              {/* File Browser */}
              <GoogleDriveFileBrowser
                workspaceId={googleDriveStatus.workspace_id}
                onFileSelectionChange={setSelectedFiles}
              />
            </div>
          ) : (
            <Card>
              <CardContent className="p-6">
                <p className="text-center text-gray-500">
                  Please connect your Google Drive account first.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Google Drive Approval Popup */}
      <Dialog open={showGoogleDriveApproval} onOpenChange={setShowGoogleDriveApproval}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Google Drive Integration
            </DialogTitle>
            <DialogDescription>
              SageBase is currently under Google approval process
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-yellow-800 mb-2">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">Under Google Approval</span>
              </div>
              <p className="text-sm text-yellow-700">
                SageBase is currently going through Google's approval process for Google Drive integration. 
                This process ensures security and compliance with Google's policies.
              </p>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button 
                variant="outline" 
                onClick={() => setShowGoogleDriveApproval(false)}
              >
                Abort
              </Button>
              <Button 
                onClick={() => {
                  setShowGoogleDriveApproval(false);
                  // Continue with the original Google Drive OAuth flow
                  if (!user || !connectedUserCompany) return;
                  setIsLoading(true);
                  try {
                    const authUrl = `${BACKEND_BASE_URL}/api/integrations/google-drive/start/?company_id=${connectedUserCompany.company_id}&workspace=default`;
                    window.open(authUrl, "_blank", "width=600,height=700,scrollbars=yes,resizable=yes");
                  } catch (error) {
                    console.error('Error starting Google Drive OAuth:', error);
                  } finally {
                    setIsLoading(false);
                  }
                }}
                variant="outline"
                className="border-green-600 text-green-600 hover:bg-green-50"
              >
                Continue Anyway
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 