export interface Author {
  id: string;
  name: string;
  avatar?: string;
}

export interface CommentVotes {
  upvotes: number;
  downvotes: number;
}

export interface Comment {
  id: string;
  content: string;
  author: Author;
  timestamp: string;
  date: string;
  votes: CommentVotes;
  is_edited: boolean;
  created_at: string;
  updated_at: string;
}

export interface CommentsResponse {
  success: boolean;
  qa_id: string;
  comments: Comment[];
  total_comments: number;
  error?: string;
}

export interface CreateCommentRequest {
  content: string;
}

export interface CreateCommentResponse {
  success: boolean;
  message: string;
  comment: Comment;
  error?: string;
  details?: {
    content?: string[];
  };
}

export interface UpdateCommentRequest {
  content: string;
}

export interface UpdateCommentResponse {
  success: boolean;
  message: string;
  comment: Comment;
  error?: string;
  details?: {
    content?: string[];
  };
}

export interface DeleteCommentResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface VoteRequest {
  vote_type: 'up' | 'down';
}

export interface VoteResponse {
  votes: CommentVotes;
  user_vote: 'up' | 'down' | 'none';
}

export interface VoteCommentResponse {
  success: boolean;
  message: string;
  vote_data: VoteResponse;
  error?: string;
  details?: {
    vote_type?: string[];
  };
}
