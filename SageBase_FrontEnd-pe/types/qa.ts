export interface Project {
  id: string;
  name: string;
  color: string;
  initial: string;
  qaCount: number;
  private?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Author {
  id: string;
  name: string;
  avatar?: string;
}

export interface Question {
  title: string;
  content: string;
  tags: string[];
  author: Author;
  timestamp: string;
  date: string;
}

export interface Answer {
  content: string;
  code?: string;
  explanation?: string;
  author: Author;
  timestamp: string;
  date: string;
  isVerified: boolean;
}

export interface QAMetadata {
  views: number;
  helpful: number;
  editedBy?: {
    name: string;
    avatar?: string;
    date: string;
  };
  approvedBy?: {
    name: string;
    avatar?: string;
    date: string;
  };
  createdBy?: {
    name: string;
    avatar?: string;
    date: string;
  };
}

export interface Votes {
  upvotes: number;
  downvotes: number;
  userVote?: "up" | "down" | null;
}

export interface QA {
  id: string;
  projectId: string;
  question: Question;
  answer?: Answer;
  votes: Votes;
  metadata: QAMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateQARequest {
  projectId: string;
  question: {
    title: string;
    content: string;
    tags: string[];
  };
  answer?: {
    content: string;
    code?: string;
    explanation?: string;
  };
}

export interface UpdateQARequest {
  question?: {
    title?: string;
    content?: string;
    tags?: string[];
  };
  answer?: {
    content?: string;
    code?: string;
    explanation?: string;
    isVerified?: boolean;
  };
}

export interface VoteRequest {
  type: "up" | "down";
}