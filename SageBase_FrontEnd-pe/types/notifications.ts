export interface Notification {
  id: string;
  type: 'qa_approval' | 'project_update' | 'document_approval' | 'team_invite' | 'NEW_KNOWLEDGE_BASE' | 'generic_notification';
  title: string;
  message: string;
  data: QAApprovalData | ProjectUpdateData | DocumentApprovalData | TeamInviteData | NewKnowledgeBaseData | GenericNotificationData;
  createdAt: string;
  readAt?: string;
  expiresAt?: string;
  priority: 'low' | 'medium' | 'high';
  userId: string;
  organizationId?: string;
}

export interface QAApprovalData {
  qaId: string;
  question: string;
  answer: string;
  source: string;
  channel: string;
  author: string;
  questionAuthor: string;
  platform: 'teams' | 'slack' | 'github' | 'discord';
  tags?: string[];
  verified?: boolean;
  timestamp: string;
  questionTime: string;
  answerTime: string;
}

export interface ProjectUpdateData {
  projectId: string;
  projectName: string;
  updateType: 'documentation' | 'contribution' | 'milestone';
  description: string;
  author: string;
}

export interface DocumentApprovalData {
  documentId: string;
  documentTitle: string;
  documentType: 'knowledge_base' | 'procedure' | 'guide';
  author: string;
  changes: string[];
}

export interface TeamInviteData {
  teamId: string;
  teamName: string;
  inviterName: string;
  role: string;
}

export interface NewKnowledgeBaseData {
  knowledgeId: string;
  title: string;
  content: string;
  author: string;
  category: string;
  tags?: string[];
  source: string;
  timestamp: string;
}

export interface GenericNotificationData {
  type?: string;
  severity?: 'low' | 'medium' | 'high';
  title?: string;
  message?: string;
  description?: string;
  [key: string]: any; // Allow additional properties
}

export interface NotificationResponse {
  success: boolean;
  data?: any;
  message?: string;
}

export interface NotificationAction {
  type: 'approve' | 'reject' | 'dismiss' | 'view';
  data?: any;
}