# Q&A Knowledge Spaces - Backend Implementation Guide

## Overview
The frontend now supports dynamic Q&A knowledge spaces with a hierarchical structure showing "Project/qa1, Project/qa2" format in the sidebar navigation. This document outlines the required backend API endpoints and data structures.

## Required API Endpoints

### 1. Knowledge Spaces (Projects)

#### GET `/api/knowledge-spaces`
Returns all projects/knowledge spaces.

**Response Format:**
```json
{
  "success": true,
  "data": [
    {
      "id": "nova",
      "name": "Nova",
      "color": "#3b82f6",
      "initial": "N",
      "qaCount": 8,
      "createdAt": "2024-01-20T00:00:00Z",
      "updatedAt": "2024-02-08T00:00:00Z"
    }
  ]
}
```

#### POST `/api/knowledge-spaces`
Creates a new project/knowledge space.

**Request Body:**
```json
{
  "name": "Project Name",
  "color": "#10b981",
  "initial": "P"
}
```

### 2. Project Q&As

#### GET `/api/knowledge-spaces/{project}`
Returns all Q&As for a specific project.

**Response Format:**
```json
{
  "success": true,
  "data": [
    {
      "id": "qa-1",
      "projectId": "nova",
      "question": {
        "title": "Error: Cannot find module '@babel/preset-env'",
        "content": "ERROR: \"Error: Cannot find module '@babel/preset-env' - This is a very weird error!\"",
        "tags": ["babel", "npm", "error"],
        "author": {
          "id": "dev-1",
          "name": "Developer",
          "avatar": "/diverse-avatars.png"
        },
        "timestamp": "18:53",
        "date": "Today"
      },
      "answer": {
        "content": "Yes, I also had it yesterday, and I've done this command to fix it:",
        "code": "npm install --save-dev @babel/preset-env && npm cache clean --force",
        "explanation": "This solution fixes the babel dependency issue...",
        "author": {
          "id": "wissem-1",
          "name": "Wissem",
          "avatar": "/diverse-group-avatars.png"
        },
        "timestamp": "19:53",
        "date": "Today",
        "isVerified": true
      },
      "votes": {
        "upvotes": 12,
        "downvotes": 0
      },
      "metadata": {
        "views": 42,
        "helpful": 12,
        "editedBy": {
          "name": "Sarah Johnson",
          "avatar": "/diverse-group-avatars.png",
          "date": "Today at 20:15"
        },
        "approvedBy": {
          "name": "Tech Lead",
          "avatar": "/diverse-avatars.png",
          "date": "Today at 20:30"
        }
      },
      "createdAt": "2024-02-08T18:53:00Z",
      "updatedAt": "2024-02-08T20:30:00Z"
    }
  ]
}
```

#### POST `/api/knowledge-spaces/{project}`
Creates a new Q&A in the specified project.

**Request Body:**
```json
{
  "question": {
    "title": "Question title",
    "content": "Question content",
    "tags": ["tag1", "tag2"]
  },
  "answer": {
    "content": "Answer content",
    "code": "optional code block",
    "explanation": "optional explanation"
  }
}
```

### 3. Individual Q&As

#### GET `/api/qa/{id}`
Returns a specific Q&A by ID. Increments view count.

#### PUT `/api/qa/{id}`
Updates a Q&A (question or answer fields).

**Request Body:**
```json
{
  "question": {
    "title": "Updated title",
    "content": "Updated content",
    "tags": ["updated", "tags"]
  },
  "answer": {
    "content": "Updated answer",
    "isVerified": true
  }
}
```

#### DELETE `/api/qa/{id}`
Deletes a Q&A.

### 4. Voting System

#### POST `/api/qa/{id}/vote`
Votes on a Q&A answer.

**Request Body:**
```json
{
  "type": "up" | "down"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "votes": {
      "upvotes": 13,
      "downvotes": 0
    },
    "userVote": "up"
  }
}
```

## Database Schema Recommendations

### Projects Table
```sql
CREATE TABLE projects (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  color VARCHAR(7) NOT NULL,
  initial VARCHAR(1) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Q&As Table
```sql
CREATE TABLE qas (
  id VARCHAR(50) PRIMARY KEY,
  project_id VARCHAR(50) REFERENCES projects(id),
  question_title TEXT NOT NULL,
  question_content TEXT NOT NULL,
  question_tags JSON,
  question_author_id VARCHAR(50),
  answer_content TEXT,
  answer_code TEXT,
  answer_explanation TEXT,
  answer_author_id VARCHAR(50),
  answer_is_verified BOOLEAN DEFAULT FALSE,
  upvotes INT DEFAULT 0,
  downvotes INT DEFAULT 0,
  views INT DEFAULT 0,
  helpful_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### User Votes Table
```sql
CREATE TABLE user_votes (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  qa_id VARCHAR(50) REFERENCES qas(id),
  vote_type ENUM('up', 'down') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_qa (user_id, qa_id)
);
```

## Frontend Integration

### Navigation Structure
The sidebar will show Q&As in this format:
- **Nova** (expandable)
  - Nova/qa1
  - Nova/qa2
  - Nova/qa3
  - View all Q&As

### URL Structure
- `/qa/{project}` - List all Q&As for project
- `/qa/{project}/{id}` - Individual Q&A page

## Testing Endpoints

### Quick Test Commands
```bash
# Test knowledge spaces
curl http://localhost:3000/api/knowledge-spaces

# Test project Q&As
curl http://localhost:3000/api/knowledge-spaces/nova

# Test specific Q&A
curl http://localhost:3000/api/qa/qa-1

# Test voting
curl -X POST http://localhost:3000/api/qa/qa-1/vote \
  -H "Content-Type: application/json" \
  -d '{"type": "up"}'
```

### Frontend Test Page
Visit `/test-qa` to see the API integration in action and verify data flow.

## Error Handling
All endpoints should return consistent error responses:
```json
{
  "success": false,
  "error": "Error message description"
}
```

## Authentication
- User information should be extracted from authentication context
- All create/update/vote operations require authenticated user
- Include user permissions for project access if needed

## Next Steps
1. Implement the backend API endpoints
2. Replace mock data with database queries
3. Test all endpoints using the frontend test page
4. Integrate with your existing authentication system
5. Add any additional business logic (permissions, notifications, etc.)

The frontend is ready and will automatically connect to your backend once these endpoints are implemented!