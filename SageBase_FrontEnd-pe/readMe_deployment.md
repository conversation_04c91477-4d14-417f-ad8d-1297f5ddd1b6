# SageBase Deployment Documentation

This document contains the deployment setup and configuration for the SageBase application with SSL certificates, Docker containers, and Nginx reverse proxy.

## Table of Contents

1. [SSL Certificate Setup](#ssl-certificate-setup)
2. [Access URLs](#access-urls)
3. [SSH Configuration](#ssh-configuration)
4. [Project Setup](#project-setup)
5. [Container Deployment](#container-deployment)
6. [Nginx Configuration](#nginx-configuration)
7. [Pending Changes](#pending-changes)

## SSL Certificate Setup

Install Certbot and generate SSL certificates for the domain:

```bash
apt install certbot
certbot certonly --standalone -d sagebaseserverfrontend.sagebase.tech
```

## Access URLs

All services are accessible via HTTPS:

- **Frontend**: [https://sagebaseserverfrontend.sagebase.tech](https://sagebaseserverfrontend.sagebase.tech)
- **Django Admin**: [https://sagebaseserverfrontend.sagebase.tech:8000/admin](https://sagebaseserverfrontend.sagebase.tech:8000/admin)
- **Portainer**: [https://sagebaseserverfrontend.sagebase.tech:9443/#!/auth](https://sagebaseserverfrontend.sagebase.tech:9443/#!/auth)
  - **Credentials**: admin / m8Wq0v1g6VK1W0ImzxeEzNei

## SSH Configuration

Configure SSH keys for GitHub repositories:

```bash
nano /root/.ssh/config
chmod 600 /root/.ssh/config
```

Add the following SSH configuration:

```ssh-config
Host github-backend
HostName github.com
User git
IdentityFile /root/.ssh/sagebase_backend
IdentitiesOnly yes

Host github-frontend
HostName github.com
User git
IdentityFile /root/.ssh/sagebase_frontend
IdentitiesOnly yes
```

## Project Setup

Create project directory and clone repositories:

```bash
mkdir /data
cd /data
git clone git@github-backend:wiswis15/SageBase_BackEnd.git
git clone git@github-frontend:wiswis15/SageBase_FrontEnd-pe.git
```

## Container Deployment

### Portainer Container

```bash
docker run -d -p 9443:9443 \
  --name portainer \
  --restart=always \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v portainer_data:/data \
  -v /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem:/certs/cert.pem \
  -v /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/privkey.pem:/certs/key.pem \
  portainer/portainer-ce \
  --ssl \
  --sslcert /certs/cert.pem \
  --sslkey /certs/key.pem \
  --host unix:///var/run/docker.sock
```

### Frontend Container


To access a shell in the frontend:
sudo docker exec -it sagebase_frontend /bin/sh

A `start.sh` script has been added to the frontend repository for automated deployment and updates:

**File**: `start.sh`

```bash
npm i --f && npm run dev
cd /data/SageBase_BackEnd && git reset --hard && git pull
docker restart sagebase_backend-web-1
cd /app && git reset --hard && git pull
npm i --f && npm run dev
```

Deploy the frontend container:

```bash
docker rm -f sagebase_frontend
docker run -d \
    -v /data/SageBase_FrontEnd-pe:/app:rw \
    -v /data/SageBase_BackEnd:/data/SageBase_BackEnd:rw \
    --name sagebase_frontend \
    -w /app \
    node:22-alpine \
    /bin/sh -c "./start.sh"
docker network create sagebase-network
```

Connect frontend to the Docker network:

```bash
docker network connect sagebase-network sagebase_frontend
```

**Note**: The `start.sh` script automates the process of:

1. Installing dependencies and starting the development server for nextjs
2. Pulling latest changes from both backend and frontend repositories
3. Restarting the backend container to apply updates

## Nginx Configuration

### Create Nginx Configuration Directory

```bash
mkdir -p /data/nginx/conf.d
```

### Create Nginx Configuration File

```bash
cat <<EOF > /data/nginx/conf.d/sagebase.conf
# Block IP-based access on HTTP only
server {
    listen 80 default_server;
    server_name _; # Matches any unmatched requests (e.g., IP address)
    return 301 https://sagebaseserverfrontend.sagebase.tech\$request_uri;
}

# Backend server on port 8000 with SSL
server {
    listen 8000 ssl;
    server_name sagebaseserverfrontend.sagebase.tech;
    client_max_body_size 100M;

    ssl_certificate /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/privkey.pem;

    # SSL configurations
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Serve static files for backend
    location /static/ {
        alias /data/SageBase_BackEnd/staticfiles/;
        autoindex on;
    }

    # WebSocket endpoint for GitHub notifications
    location /ws/github/notifications/ {
        proxy_pass http://sagebase_backend-web-1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
```

### Deploy Nginx Container

```bash
docker run -d \
  --name nginx \
  --restart=always \
  --network sagebase-network \
  -p 80:80 \
  -p 443:443 \
  -p 8000:8000 \
  -v /data/nginx/conf.d:/etc/nginx/conf.d \
  -v /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem:/etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/fullchain.pem:ro \
  -v /etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/privkey.pem:/etc/letsencrypt/live/sagebaseserverfrontend.sagebase.tech/privkey.pem:ro \
  -v /data/SageBase_BackEnd/staticfiles:/data/SageBase_BackEnd/staticfiles:ro \
  nginx:alpine

docker network connect bridge nginx
```

## Pending Changes

### Django Static Files Configuration

The Django backend needs to be configured to properly collect static files.

**Issue**: Django backend only sets `STATIC_URL = 'static/'` without defining `STATIC_ROOT`.

**Solution**: Add the following to Django settings:

```python
STATIC_URL = 'static/'
STATIC_ROOT = '/app/staticfiles/'  # Add this line
```

Then collect static files in the backend container:

```bash
python manage.py collectstatic --noinput
```

### Frontend Configuration Changes

**File**: `.env.local`

```diff
# Site URL for local development
-NEXT_PUBLIC_SITE_URL=http://localhost:3000
+NEXT_PUBLIC_SITE_URL=https://sagebaseserverfrontend.sagebase.tech

# Backend API Configuration
-NEXT_PUBLIC_BACKEND_API_URL=http://localhost:8000
+NEXT_PUBLIC_BACKEND_API_URL=https://sagebaseserverfrontend.sagebase.tech:8000


**File**: `package.json`

```diff
"scripts": {
-  "dev": "next dev",
+  "dev": "next dev -H 0.0.0.0 -p 3000",
   "build": "next build",
   "start": "next start",
   "lint": "next lint"
}
```

### Backend Configuration Changes

**File**: `SageBase_Backend/settings.py`

```diff
# https://docs.djangoproject.com/en/4.2/howto/static-files/
STATIC_URL = 'static/'
+STATIC_ROOT = '/app/staticfiles/'
```

**File**: `docker-compose.yml`

```diff
services:
  web:
    build: .
    ports:
-     - "8000:8000"
      - "5678:5678"  # debugpy port for VS Code debugging (Django)
      - "5679:5679"  # debugpy port for Document Embedding debugging
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - .env
    depends_on:
      - redis
-   command: sh -c "python manage.py migrate && tail -f /dev/null"
+   command: sh -c "python manage.py migrate && python -Xfrozen_modules=off manage.py runserver 0.0.0.0:8000"
```

## Notes

- All services run with SSL enabled (HTTPS)
- Nginx acts as a reverse proxy for the backend services
- Docker containers are connected via the `sagebase-network` for internal communication
- Static files are served directly by Nginx for better performance
