import { getBackendUrl } from "@/lib/api-config";

export interface SlackNotificationData {
  userEmail: string;
  userName: string;
  companyName?: string;
  connectionType: 'login' | 'signup' | 'oauth';
  platform?: string;
  timestamp: string;
  userAgent?: string;
}

export class SlackNotificationService {
  private channel: string;
  private botName: string;
  private botIcon: string;

  constructor(channel: string = "#general", botName: string = "SageBase Bot", botIcon: string = ":robot_face:") {
    this.channel = channel;
    this.botName = botName;
    this.botIcon = botIcon;
  }

  /**
   * Send a notification to <PERSON>lack when a user connects to the app
   */
  async sendUserConnectionNotification(data: SlackNotificationData): Promise<boolean> {
    try {
      console.log('🔍 SlackNotificationService: User connection notification requested');
      console.log('🔍 SlackNotificationService: Data received:', data);
      
      const backendUrl = getBackendUrl();
      const endpoint = `${backendUrl}/api/integrations/notifications/slack/user-connection/`;
      
      console.log('🌐 Sending request to:', endpoint);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: data.userEmail,
          userName: data.userName,
          connectionType: data.connectionType,
          timestamp: data.timestamp,
          ...(data.companyName && { companyName: data.companyName }),
          ...(data.platform && { platform: data.platform }),
          ...(data.userAgent && { userAgent: data.userAgent }),
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Slack notification request failed:', response.status, errorText);
        return false;
      }
      
      const result = await response.json();
      console.log('✅ Slack notification sent successfully:', result);
      return true;
      
    } catch (error) {
      console.error('❌ Error in Slack notification service:', error);
      return false;
    }
  }

  /**
   * Send a simple text message to Slack
   */
  async sendSimpleMessage(text: string, channel?: string): Promise<boolean> {
    try {
      console.log('🔍 SlackNotificationService: Simple message requested');
      console.log('🔍 SlackNotificationService: Text:', text);
      console.log('🔍 SlackNotificationService: Channel:', channel || this.channel);
      
      const backendUrl = getBackendUrl();
      const endpoint = `${backendUrl}/api/integrations/notifications/slack/simple-message/`;
      
      console.log('🌐 Sending simple message to:', endpoint);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          channel: channel || this.channel,
          botName: this.botName,
          botIcon: this.botIcon,
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Simple message request failed:', response.status, errorText);
        return false;
      }
      
      const result = await response.json();
      console.log('✅ Simple message sent successfully:', result);
      return true;
      
    } catch (error) {
      console.error('❌ Error in Slack notification service:', error);
      return false;
    }
  }

  /**
   * Send a custom notification with blocks (for more complex messages)
   */
  async sendCustomNotification(blocks: any[], channel?: string): Promise<boolean> {
    try {
      console.log('🔍 SlackNotificationService: Custom notification requested');
      console.log('🔍 SlackNotificationService: Blocks:', blocks);
      console.log('🔍 SlackNotificationService: Channel:', channel || this.channel);
      
      const backendUrl = getBackendUrl();
      const endpoint = `${backendUrl}/api/integrations/notifications/slack/custom/`;
      
      console.log('🌐 Sending custom notification to:', endpoint);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          blocks,
          channel: channel || this.channel,
          botName: this.botName,
          botIcon: this.botIcon,
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Custom notification request failed:', response.status, errorText);
        return false;
      }
      
      const result = await response.json();
      console.log('✅ Custom notification sent successfully:', result);
      return true;
      
    } catch (error) {
      console.error('❌ Error in Slack notification service:', error);
      return false;
    }
  }

  /**
   * Test the Slack notification service
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 SlackNotificationService: Testing connection...');
      console.log('🔍 SlackNotificationService: Channel:', this.channel);
      console.log('🔍 SlackNotificationService: Bot name:', this.botName);
      console.log('🔍 SlackNotificationService: Bot icon:', this.botIcon);
      
      const backendUrl = getBackendUrl();
      const endpoint = `${backendUrl}/api/integrations/notifications/slack/test/`;
      
      console.log('🌐 Testing connection to:', endpoint);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel: this.channel,
          botName: this.botName,
          botIcon: this.botIcon,
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Connection test failed:', response.status, errorText);
        return false;
      }
      
      const result = await response.json();
      console.log('✅ Connection test successful:', result);
      return true;
      
    } catch (error) {
      console.error('❌ Error testing Slack notification service:', error);
      return false;
    }
  }
}

// Export a default instance
export const slackNotificationService = new SlackNotificationService();
