import { getAuthHeaders } from '@/lib/api-utils';

export interface SpaceItem {
  id: string;
  name: string;
  type: "folder" | "document";
  children?: SpaceItem[];
  href?: string;
}

export interface Space {
  id: string;
  name: string;
  color: string;
  initial: string;
  items: SpaceItem[];
  lastUpdated?: string;
  documentCount?: number;
  folderCount?: number;
  brandImage?: string;
}

export interface SpacesResponse {
  success: boolean;
  data: Space[];
  error?: string;
}

class SpacesAPI {
  private baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

  async getSpaces(): Promise<Space[]> {
    try {
      console.log("🚀 Fetching spaces from API...");
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: SpacesResponse = await response.json();
      
      if (result.success) {
        console.log("✅ Spaces fetched successfully:", result.data.length, "spaces");
        return result.data;
      } else {
        console.error("❌ Failed to fetch spaces:", result.error);
        throw new Error(result.error || "Failed to fetch spaces");
      }
    } catch (error) {
      console.error("❌ Error fetching spaces:", error);
      throw error;
    }
  }

  async createSpace(spaceData: Partial<Space>): Promise<{ success: boolean; message: string; data?: Space }> {
    try {
      console.log("🚀 Creating new space...");
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(spaceData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log("✅ Space created successfully");
        return { success: true, message: "Space created successfully", data: result.data };
      } else {
        console.error("❌ Failed to create space:", result.error);
        return { success: false, message: result.error || "Failed to create space" };
      }
    } catch (error) {
      console.error("❌ Error creating space:", error);
      return { success: false, message: "Failed to create space" };
    }
  }

  async updateSpace(spaceId: string, spaceData: Partial<Space>): Promise<{ success: boolean; message: string }> {
    try {
      console.log("🚀 Updating space:", spaceId);
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/${spaceId}`;
      
      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(spaceData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log("✅ Space updated successfully");
        return { success: true, message: "Space updated successfully" };
      } else {
        console.error("❌ Failed to update space:", result.error);
        return { success: false, message: result.error || "Failed to update space" };
      }
    } catch (error) {
      console.error("❌ Error updating space:", error);
      return { success: false, message: "Failed to update space" };
    }
  }

  async deleteSpace(spaceId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log("🚀 Deleting space:", spaceId);
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/${spaceId}`;
      
      const response = await fetch(url, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log("✅ Space deleted successfully");
        return { success: true, message: "Space deleted successfully" };
      } else {
        console.error("❌ Failed to delete space:", result.error);
        return { success: false, message: result.error || "Failed to delete space" };
      }
    } catch (error) {
      console.error("❌ Error deleting space:", error);
      return { success: false, message: "Failed to delete space" };
    }
  }
}

export const spacesAPI = new SpacesAPI(); 