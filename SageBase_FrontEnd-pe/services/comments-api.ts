import { getAuthHeaders } from '@/lib/api-utils';
import type {
  Comment,
  CommentsResponse,
  CreateCommentRequest,
  CreateCommentResponse,
  UpdateCommentRequest,
  UpdateCommentResponse,
  DeleteCommentResponse,
  VoteRequest,
  VoteCommentResponse,
} from '@/types/comments';

class CommentsAPI {
  private baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

  /**
   * Get all comments for a QA entry
   */
  async getComments(qaId: string): Promise<Comment[]> {
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/comments`, {
        method: 'GET',
        headers,
      });

      const data: CommentsResponse = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch comments');
      }

      return data.comments;
    } catch (error) {
      console.error('Error fetching comments:', error);
      throw error;
    }
  }

  /**
   * Create a new comment
   */
  async createComment(qaId: string, content: string): Promise<Comment> {
    try {
      const headers = await getAuthHeaders();
      const requestBody: CreateCommentRequest = { content };

      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/comments`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      const data: CreateCommentResponse = await response.json();
      
      if (!response.ok) {
        if (data.details?.content) {
          throw new Error(data.details.content.join(', '));
        }
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to create comment');
      }

      return data.comment;
    } catch (error) {
      console.error('Error creating comment:', error);
      throw error;
    }
  }

  /**
   * Update an existing comment
   */
  async updateComment(qaId: string, commentId: string, content: string): Promise<Comment> {
    try {
      const headers = await getAuthHeaders();
      const requestBody: UpdateCommentRequest = { content };

      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/comments/${commentId}/update`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(requestBody),
      });

      const data: UpdateCommentResponse = await response.json();
      
      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('You can only edit your own comments');
        }
        if (data.details?.content) {
          throw new Error(data.details.content.join(', '));
        }
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to update comment');
      }

      return data.comment;
    } catch (error) {
      console.error('Error updating comment:', error);
      throw error;
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(qaId: string, commentId: string): Promise<void> {
    try {
      const headers = await getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/comments/${commentId}/delete`, {
        method: 'DELETE',
        headers,
      });

      const data: DeleteCommentResponse = await response.json();
      
      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('You can only delete your own comments');
        }
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to delete comment');
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      throw error;
    }
  }

  /**
   * Vote on a comment (thumbs up/down)
   */
  async voteComment(qaId: string, commentId: string, voteType: 'up' | 'down'): Promise<{ votes: { upvotes: number; downvotes: number }; user_vote: 'up' | 'down' | 'none' }> {
    try {
      const headers = await getAuthHeaders();
      const requestBody: VoteRequest = { vote_type: voteType };

      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/comments/${commentId}/vote`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      const data: VoteCommentResponse = await response.json();
      
      if (!response.ok) {
        if (data.details?.vote_type) {
          throw new Error(data.details.vote_type.join(', '));
        }
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to vote on comment');
      }

      return data.vote_data;
    } catch (error) {
      console.error('Error voting on comment:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const commentsAPI = new CommentsAPI();
export default commentsAPI;
