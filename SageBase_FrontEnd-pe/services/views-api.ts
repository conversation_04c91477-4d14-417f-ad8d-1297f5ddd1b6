import { getAuthHeaders } from '@/lib/api-utils';
import { getBackendUrl } from '@/lib/api-config';

interface ViewsResponse {
  success: boolean;
  views: number;
  error?: string;
}

class ViewsAPI {
  private baseUrl = getBackendUrl();

  /**
   * Get view count for a QA entry
   */
  async getViews(qaId: string): Promise<number> {
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/views`, {
        method: 'GET',
        headers,
      });

      const data: ViewsResponse = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch views');
      }

      return data.views;
    } catch (error) {
      console.error('Error fetching views:', error);
      throw error;
    }
  }

  /**
   * Track a view for a QA entry
   */
  async trackView(qaId: string): Promise<void> {
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/qa/${qaId}/views`, {
        method: 'POST',
        headers,
      });

      const data: ViewsResponse = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to track view');
      }
    } catch (error) {
      console.error('Error tracking view:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const viewsAPI = new ViewsAPI();
export default viewsAPI;
