import { getAuthHeaders } from '@/lib/api-utils';
import { getBackendUrl } from "@/lib/api-config";

export interface QAItem {
  id: string;
  question: {
    title?: string;
    content: string;
    tags?: string[];
    author?: {
      name: string;
    };
    timestamp?: string;
  };
  answer: {
    content: string;
    author?: {
      name: string;
    };
    timestamp?: string;
    isVerified?: boolean;
  };
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

export interface QAEditData {
  question_title: string;
  question_content: string;
  question_tags: string[];
  answer_content: string;
}

export interface QACreateData {
  question_title: string;
  question_content: string;
  question_tags: string[];
  answer_content: string;
  answer_code?: string;
  answer_explanation?: string;
  question_tokens_count?: number;
  answer_tokens_count?: number;
}

export interface QAResponse {
  success: boolean;
  data: QAItem | QAItem[];
  error?: string;
}

class QAAPI {
  private baseUrl = getBackendUrl();

  async deleteQA(knowledgeSpaceId: string, qaId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log("🚀 Deleting Q&A:", qaId, "from knowledge space:", knowledgeSpaceId);
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}/qa/${qaId}`;
      
      const response = await fetch(url, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log("✅ Q&A deleted successfully");
        return { success: true, message: "Q&A deleted successfully" };
      } else {
        console.error("❌ Failed to delete Q&A:", result.error);
        return { success: false, message: result.error || "Failed to delete Q&A" };
      }
    } catch (error) {
      console.error("❌ Error deleting Q&A:", error);
      return { success: false, message: "Failed to delete Q&A" };
    }
  }

  async getQAs(knowledgeSpaceId: string): Promise<QAItem[]> {
    try {
      console.log("🚀 Fetching Q&As for knowledge space:", knowledgeSpaceId);
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: QAResponse = await response.json();
      
      if (result.success) {
        console.log("✅ Q&As fetched successfully:", Array.isArray(result.data) ? result.data.length : 1, "items");
        return Array.isArray(result.data) ? result.data : [result.data];
      } else {
        console.error("❌ Failed to fetch Q&As:", result.error);
        throw new Error(result.error || "Failed to fetch Q&As");
      }
    } catch (error) {
      console.error("❌ Error fetching Q&As:", error);
      throw error;
    }
  }

  async updateQA(knowledgeSpaceId: string, qaId: string, editData: QAEditData): Promise<{ success: boolean; message: string }> {
    try {
      console.log("🚀 Updating Q&A:", qaId, "in knowledge space:", knowledgeSpaceId);
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}/qa/${qaId}`;
      
      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(editData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log("✅ Q&A updated successfully");
        return { success: true, message: "Q&A updated successfully" };
      } else {
        console.error("❌ Failed to update Q&A:", result.error);
        return { success: false, message: result.error || "Failed to update Q&A" };
      }
    } catch (error) {
      console.error("❌ Error updating Q&A:", error);
      return { success: false, message: "Failed to update Q&A" };
    }
  }

  async createQA(knowledgeSpaceId: string, createData: QACreateData): Promise<{ success: boolean; message: string; data?: QAItem }> {
    try {
      console.log("🚀 Creating new Q&A in knowledge space:", knowledgeSpaceId);
      console.log("📤 Sending data to backend:", createData);
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}`;
      
      console.log("🌐 Making POST request to:", url);
      console.log("📋 Request headers:", headers);
      console.log("📦 Request body:", JSON.stringify(createData));
      
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(createData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ HTTP error! status:", response.status);
        console.error("❌ Response text:", errorText);
        throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`);
      }

      const result = await response.json();
      
      console.log("📡 Backend response for Q&A creation:", result);
      console.log("📡 Response success:", result.success);
      console.log("📡 Response message:", result.message);
      console.log("📡 Response data:", result.data);
      
      if (result.success) {
        console.log("✅ Q&A created successfully");
        console.log("📡 Backend message:", result.message);
        return { 
          success: true, 
          message: result.message || "Q&A created successfully", // Use backend message if available
          data: result.data 
        };
      } else {
        console.error("❌ Failed to create Q&A:", result.message);
        
        // Handle nested message structure: { message: { message: "actual message" } }
        let errorMessage = "Failed to create Q&A";
        if (result.message) {
          if (typeof result.message === 'string') {
            errorMessage = result.message;
          } else if (result.message.message) {
            errorMessage = result.message.message;
          }
        } else if (result.error) {
          errorMessage = result.error;
        }
        
        return { success: false, message: errorMessage };
      }
    } catch (error) {
      console.error("❌ Error creating Q&A:", error);
      return { success: false, message: "Failed to create Q&A" };
    }
  }


}

export const qaAPI = new QAAPI(); 