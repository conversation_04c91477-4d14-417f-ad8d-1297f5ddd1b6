import { getAuthHeaders } from '@/lib/api-utils';
import { getBackendUrl } from "@/lib/api-config";

export interface QAHistoryItem {
  id: string;
  question: string;
  answer: string;
  source: string;
  channel: string;
  author: string;
  timestamp: string;
  platform: 'slack' | 'teams' | 'github' | 'discord' | 'knowledge-base';
  status: 'pending' | 'stored' | 'dismissed';
  documentId?: string;
  tags: string[];
  upvotes: number;
  views: number;
  createdAt: string;
  updatedAt: string;
}

export interface QAHistoryResponse {
  success: boolean;
  data: QAHistoryItem[];
  error?: string;
}

class QAHistoryAPI {
  private baseUrl = getBackendUrl();

  async getQAHistory(): Promise<QAHistoryItem[]> {
    try {
      console.log('🔐 Getting QA history with auth token...');
      console.log('🌐 Base URL:', this.baseUrl);
      
      const headers = await getAuthHeaders();
      console.log('🔑 Auth headers:', headers);
      
      const url = `${this.baseUrl}/api/knowledge-spaces/qa-history`;
      console.log('📡 Making request to:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      console.log('📡 QA history response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ QA history error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data: QAHistoryResponse = await response.json();
      console.log('✅ QA history success:', data);
      
      return data.data || [];
    } catch (error) {
      console.error('❌ Error fetching QA history:', error);
      throw error;
    }
  }

  async storeQA(qaId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🎯 storeQA called with qaId:', qaId);
      
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/qa-history/${qaId}/store`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Store QA result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error storing QA:', error);
      throw error;
    }
  }

  async dismissQA(qaId: string, reason?: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🎯 dismissQA called with qaId:', qaId, 'reason:', reason);
      
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/qa-history/${qaId}/dismiss`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          ...headers,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: reason || 'Dismissed by user' }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Dismiss QA result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error dismissing QA:', error);
      throw error;
    }
  }

  async restoreQA(qaId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🎯 restoreQA called with qaId:', qaId);
      
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/qa-history/${qaId}/restore`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Restore QA result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error restoring QA:', error);
      throw error;
    }
  }
}

export const qaHistoryAPI = new QAHistoryAPI(); 