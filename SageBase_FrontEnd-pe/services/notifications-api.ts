import { Notification, NotificationResponse, NotificationAction } from '@/types/notifications';
import { getAuthHeaders } from '@/lib/api-utils';

export interface GenericNotification {
  id: string;
  title: string;
  details: string;
  type: 'system_notification' | 'knowledge_base_conflict' | 'file_upload_result' | 'integration_status' | 'user_activity';
  severity: 'low' | 'medium' | 'high';
  source: string;
  timestamp: string;
  created_at: string;
  updated_at: string;
}

export interface NotificationActionResponse {
  success: boolean;
  message: string;
  data?: any;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

class NotificationsAPI {
  private baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

  async getNotifications(): Promise<GenericNotification[]> {
    try {
      console.log('📡 Fetching notifications from:', `${this.baseUrl}/api/notifications`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/notifications`, {
        method: 'GET',
        headers,
      });

      console.log('📡 Notifications response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Notifications error response:', errorText);
        
        // Check if the response is HTML (indicating a 404 or server error page)
        if (errorText.includes('<!DOCTYPE') || errorText.includes('<html')) {
          console.warn('⚠️ Server returned HTML instead of JSON - endpoint may not exist');
          return []; // Return empty array instead of throwing error
        }
        
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      // Check if response is JSON before parsing
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.warn('⚠️ Response is not JSON:', responseText.substring(0, 200));
        return []; // Return empty array instead of throwing error
      }

      const data = await response.json();
      console.log('✅ Notifications success:', data);
      
      return data.data || [];
      
    } catch (error) {
      console.error('❌ Error fetching notifications:', error);
      
      // If it's a JSON parsing error, the endpoint likely doesn't exist
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        console.warn('⚠️ JSON parsing error - notifications endpoint may not be implemented');
        return []; // Return empty array instead of throwing error
      }
      
      throw error;
    }
  }

  async acceptNotification(notificationId: string): Promise<NotificationActionResponse> {
    try {
      console.log('🚀 Accepting notification:', notificationId, 'at endpoint:', `${this.baseUrl}/api/notifications/${notificationId}/accept`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/notifications/${notificationId}/accept`, {
        method: 'POST',
        headers,
      });

      console.log('📡 Accept response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Accept error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Accept success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error accepting notification:', error);
      throw error;
    }
  }

  async ignoreNotification(notificationId: string): Promise<NotificationActionResponse> {
    try {
      console.log('🚀 Ignoring notification:', notificationId, 'at endpoint:', `${this.baseUrl}/api/notifications/${notificationId}/ignore`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/notifications/${notificationId}/ignore`, {
        method: 'POST',
        headers,
      });

      console.log('📡 Ignore response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Ignore error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Ignore success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error ignoring notification:', error);
      throw error;
    }
  }
}

export const notificationsAPI = new NotificationsAPI();

// Test function to check if the API is reachable
export const testNotificationsConnection = async () => {
  try {
    console.log('🧪 Testing Notifications API connection...');
    const baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';
    console.log('🌐 Testing connection to:', baseUrl);
    
    const response = await fetch(`${baseUrl}/api/notifications`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('🧪 Test response status:', response.status);
    console.log('🧪 Test response ok:', response.ok);
    
    if (response.ok) {
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('🧪 Test response data:', data);
      } else {
        const responseText = await response.text();
        console.log('🧪 Test response is not JSON:', responseText.substring(0, 200));
      }
    } else {
      const errorText = await response.text();
      console.log('🧪 Test error response:', errorText);
      
      // Check if it's an HTML error page
      if (errorText.includes('<!DOCTYPE') || errorText.includes('<html')) {
        console.warn('🧪 Server returned HTML - notifications endpoint may not exist');
      }
    }
  } catch (error) {
    console.error('🧪 Test connection error:', error);
    
    // If it's a JSON parsing error, the endpoint likely doesn't exist
    if (error instanceof SyntaxError && error.message.includes('JSON')) {
      console.warn('🧪 JSON parsing error - notifications endpoint may not be implemented');
    }
  }
};

export const notificationsApi = {
  getNotifications: async (params?: {
    unreadOnly?: boolean;
    limit?: number;
    offset?: number;
    type?: string;
  }): Promise<Notification[]> => {
    const searchParams = new URLSearchParams();
    if (params?.unreadOnly) searchParams.append('unreadOnly', 'true');
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());
    if (params?.type) searchParams.append('type', params.type);

    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications?${searchParams}`, {
      headers
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch notifications');
    }
    
    const result = await response.json();
    return result.data || result;
  },

  getNotificationById: async (notificationId: string): Promise<Notification> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/${notificationId}`, {
      headers
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch notification');
    }
    
    const result = await response.json();
    return result.data || result;
  },

  markAsRead: async (notificationId: string): Promise<NotificationResponse> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/${notificationId}/read`, {
      method: 'PUT',
      headers
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark notification as read');
    }
    
    return response.json();
  },

  markAllAsRead: async (): Promise<NotificationResponse> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/read-all`, {
      method: 'PUT',
      headers
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark all notifications as read');
    }
    
    return response.json();
  },

  performAction: async (
    notificationId: string, 
    action: NotificationAction
  ): Promise<NotificationResponse> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/${notificationId}/action`, {
      method: 'POST',
      headers,
      body: JSON.stringify(action)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to perform ${action.type} action`);
    }
    
    return response.json();
  },

  deleteNotification: async (notificationId: string): Promise<NotificationResponse> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/${notificationId}`, {
      method: 'DELETE',
      headers
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete notification');
    }
    
    return response.json();
  },

  getUnreadCount: async (): Promise<{ count: number }> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/unread-count`, {
      headers
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch unread count');
    }
    
    const result = await response.json();
    return result.data || result;
  },

  createTestNotification: async (type: string, data: any): Promise<NotificationResponse> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/notifications/test`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ type, data })
    });
    
    if (!response.ok) {
      throw new Error('Failed to create test notification');
    }
    
    return response.json();
  }
};

export const setupNotificationEventSource = (
  userId: string,
  onNotification: (notification: Notification) => void,
  onUnreadCountUpdate: (count: number) => void
): EventSource => {
  const eventSource = new EventSource(`${API_BASE_URL}/api/notifications/stream/${userId}`);
  
  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      
      if (data.type === 'notification') {
        onNotification(data.notification);
      } else if (data.type === 'unread_count') {
        onUnreadCountUpdate(data.count);
      }
    } catch (error) {
      console.error('Failed to parse notification event:', error);
    }
  };
  
  eventSource.onerror = (error) => {
    console.error('Notification EventSource error:', error);
  };
  
  return eventSource;
};