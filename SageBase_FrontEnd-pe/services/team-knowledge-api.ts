// API service functions for Team Knowledge Base

export interface Contributor {
  name: string;
  role: string;
  contributions: number;
  commits: number;
  reviews: number;
  docs: number;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  lastActivity: string;
  totalContributors: number;
  categories: string[];
  docResponsible: string | null;
  secondaryResponsible: string | null;
  repoPath: string;
  docsPath: string;
  repoType: 'github' | 'gitlab';
  topContributors: Contributor[];
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
}

export interface ProjectMetrics {
  id: string;
  projectId: string;
  healthScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  lastUpdated: string;
  documentationCoverage: number;
  activeContributors: number;
}

import { getAuthHeaders } from '@/lib/api-utils';

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

// Projects API
export const projectsApi = {
  // Get all projects
  getProjects: async (timeRange?: string): Promise<Project[]> => {
    const params = timeRange ? `?timeRange=${timeRange}` : '';
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${params}`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch projects');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Get single project by ID
  getProject: async (projectId: string): Promise<Project> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${projectId}/`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch project');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Create new project
  createProject: async (project: Omit<Project, 'id' | 'lastActivity' | 'totalContributors' | 'topContributors'>): Promise<Project> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/`, {
      method: 'POST',
      headers,
      body: JSON.stringify(project),
    });
    if (!response.ok) throw new Error('Failed to create project');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Update project
  updateProject: async (projectId: string, updates: Partial<Project>): Promise<Project> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${projectId}/`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(updates),
    });
    if (!response.ok) throw new Error('Failed to update project');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Delete project
  deleteProject: async (projectId: string): Promise<void> => {
    try {
      console.log('🗑️ Deleting project:', projectId);
      
      const headers = await getAuthHeaders();
      console.log('🔑 Auth headers for delete:', headers);
      
      const url = `${API_BASE_URL}/api/projects/${projectId}/`;
      console.log('📡 Making DELETE request to:', url);
      
      const response = await fetch(url, {
        method: 'DELETE',
        headers
      });
      
      console.log('📡 Delete response status:', response.status);
      console.log('📡 Delete response ok:', response.ok);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Delete error response:', errorText);
        throw new Error(`Failed to delete project: ${response.status} - ${errorText}`);
      }
      
      console.log('✅ Project deleted successfully:', projectId);
    } catch (error) {
      console.error('❌ Error deleting project:', error);
      throw error;
    }
  },

  // Assign documentation responsibility
  assignDocumentationResponsibility: async (
    projectId: string,
    memberEmail: string,
    type: 'main' | 'secondary'
  ): Promise<Project> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${projectId}/assign-documentation/`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        memberEmail,
        type,
      }),
    });
    if (!response.ok) throw new Error('Failed to assign documentation responsibility');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },
};

// Team Members API
export const teamMembersApi = {
  // Get all team members
  getTeamMembers: async (): Promise<TeamMember[]> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/team-members/`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch team members');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Get team member by ID
  getTeamMember: async (memberId: string): Promise<TeamMember> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/team-members/${memberId}/`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch team member');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },
};

// Contributors API
export const contributorsApi = {
  // Get top contributors for a project
  getProjectContributors: async (projectId: string, timeRange?: string): Promise<Contributor[]> => {
    const params = timeRange ? `?timeRange=${timeRange}` : '';
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${projectId}/contributors/${params}`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch contributors');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Get contributor activity for a specific time range
  getContributorActivity: async (
    projectId: string,
    contributorId: string,
    timeRange?: string
  ): Promise<Contributor> => {
    const params = timeRange ? `?timeRange=${timeRange}` : '';
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${projectId}/contributors/${contributorId}/${params}`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch contributor activity');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },
};

// Metrics API
export const metricsApi = {
  // Get project metrics
  getProjectMetrics: async (projectId: string): Promise<ProjectMetrics> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/${projectId}/metrics/`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch project metrics');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },

  // Get metrics for all projects
  getAllProjectMetrics: async (): Promise<ProjectMetrics[]> => {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/metrics/`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to fetch all project metrics');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },
};

// Search API
export const searchApi = {
  // Search projects
  searchProjects: async (query: string, filters?: {
    categories?: string[];
    contributors?: string[];
    timeRange?: string;
  }): Promise<Project[]> => {
    const params = new URLSearchParams({
      q: query,
      ...(filters?.timeRange && { timeRange: filters.timeRange }),
      ...(filters?.categories && { categories: filters.categories.join(',') }),
      ...(filters?.contributors && { contributors: filters.contributors.join(',') }),
    });
    
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/projects/search/?${params}`, {
      headers
    });
    if (!response.ok) throw new Error('Failed to search projects');
    const result = await response.json();
    // Extract the data property from the response
    return result.data || result;
  },
};