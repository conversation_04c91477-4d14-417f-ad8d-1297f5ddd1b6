export interface PendingQAApproval {
  id: string;
  projectId: string;
  source: string;
  channel: string;
  knowledgeSpaceName?: string; // Add knowledge space name field
  question: {
    title: string;
    content: string;
    tags: string[];
    author: {
      id: string;
      name: string;
      avatar: string | null;
    };
    timestamp: string;
    date: string;
  };
  answer: {
    content: string;
    code?: string;
    explanation?: string;
    author: {
      id: string;
      name: string;
      avatar: string | null;
    };
    timestamp: string;
    date: string;
    isVerified: boolean;
  };
  votes: {
    upvotes: number;
    downvotes: number;
  };
  metadata: {
    views: number;
    helpful: number;
    editedBy: string | null;
    approvedBy: string | null;
    approvalStatus: string;
    approvalReason: string | null;
  };
  created_at: string;
  updated_at: string;
}

export interface PendingGenericNotification {
  id: string;
  type: 'system_notification' | 'knowledge_base_conflict' | 'file_upload_result' | 'integration_status';
  title: string;
  message: string;
  description?: string;
  severity: 'low' | 'medium' | 'high';
  source: string;
  channel: string;
  author?: {
    id: string;
    name: string;
    avatar: string | null;
  };
  tags?: string[];
  metadata: {
    views: number;
    helpful: number;
    editedBy: string | null;
    approvedBy: string | null;
    approvalStatus: string;
    approvalReason: string | null;
  };
  created_at: string;
  updated_at: string;
}

export type PendingApproval = PendingQAApproval | PendingGenericNotification;

export interface ApproveRejectResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface QAEditData {
  question_title?: string;
  question_content?: string;
  question_tags?: string[];
  answer_content?: string;
  answer_code?: string;
  answer_explanation?: string;
  question_tokens_count?: number;
  answer_tokens_count?: number;
}

// Import the centralized getAuthHeaders function
import { getAuthHeaders } from '@/lib/api-utils';

class QAApprovalAPI {
  private baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

  async getPendingApprovals(): Promise<PendingApproval[]> {
    try {
      
      const headers = await getAuthHeaders();
      
      // Use the original endpoint - it works correctly for admin users
      const url = `${this.baseUrl}/api/knowledge-spaces/pending-approvals`;
      console.log('📡 Making request to:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      console.log('📡 Pending approvals response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Pending approvals error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Pending approvals success:', data);
      
      // Return the data array from the response
      return data.data || [];
      
    } catch (error) {
      console.error('❌ Error fetching pending approvals:', error);
      if (error instanceof Error) {
        console.error('❌ Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }
      throw error;
    }
  }

  async approveQA(qaId: string): Promise<ApproveRejectResponse> {
    try {
      console.log('🚀 Approving QA:', qaId, 'at endpoint:', `${this.baseUrl}/api/knowledge-spaces/qa/${qaId}/approve`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/knowledge-spaces/qa/${qaId}/approve`, {
        method: 'POST',
        headers,
      });

      console.log('📡 Approve response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Approve error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Approve success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error approving QA:', error);
      throw error;
    }
  }

  async rejectQA(qaId: string, reason: string): Promise<ApproveRejectResponse> {
    try {
      console.log('🚀 Rejecting QA:', qaId, 'with reason:', reason, 'at endpoint:', `${this.baseUrl}/api/knowledge-spaces/qa/${qaId}/reject`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/knowledge-spaces/qa/${qaId}/reject`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ reason }),
      });

      console.log('📡 Reject response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Reject error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Reject success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error rejecting QA:', error);
      throw error;
    }
  }

  async editQA(qaId: string, knowledgeSpaceId: string, editData: QAEditData): Promise<ApproveRejectResponse> {
    try {
      console.log('🚀 Editing QA:', qaId, 'in knowledge space:', knowledgeSpaceId, 'with data:', editData);
      
      const headers = await getAuthHeaders();
      const url = `${this.baseUrl}/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}/qa/${qaId}`;
      
      console.log('📡 Making request to:', url);
      
      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(editData),
      });

      console.log('📡 Edit response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Edit error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Edit success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error editing QA:', error);
      throw error;
    }
  }

  async acceptGenericNotification(notificationId: string): Promise<ApproveRejectResponse> {
    try {
      console.log('🚀 Accepting generic notification:', notificationId, 'at endpoint:', `${this.baseUrl}/api/knowledge-spaces/notifications/${notificationId}/accept`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/knowledge-spaces/notifications/${notificationId}/accept`, {
        method: 'POST',
        headers,
      });

      console.log('📡 Accept response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Accept error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Accept success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error accepting notification:', error);
      throw error;
    }
  }

  async ignoreGenericNotification(notificationId: string): Promise<ApproveRejectResponse> {
    try {
      console.log('🚀 Ignoring generic notification:', notificationId, 'at endpoint:', `${this.baseUrl}/api/knowledge-spaces/notifications/${notificationId}/ignore`);
      
      const headers = await getAuthHeaders();
      const response = await fetch(`${this.baseUrl}/api/knowledge-spaces/notifications/${notificationId}/ignore`, {
        method: 'POST',
        headers,
      });

      console.log('📡 Ignore response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Ignore error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Ignore success:', data);
      return data;
    } catch (error) {
      console.error('❌ Error ignoring notification:', error);
      throw error;
    }
  }
}

export const qaApprovalAPI = new QAApprovalAPI();

// Test function to check if the API is reachable
export const testQAApiConnection = async () => {
  try {
    console.log('🧪 Testing Q&A API connection...');
    const baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';
    console.log('🌐 Testing connection to:', baseUrl);
    
    const response = await fetch(`${baseUrl}/api/knowledge-spaces/pending-approvals`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('🧪 Test response status:', response.status);
    console.log('🧪 Test response ok:', response.ok);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🧪 Test response data:', data);
    } else {
      const errorText = await response.text();
      console.log('🧪 Test error response:', errorText);
    }
  } catch (error) {
    console.error('🧪 Test connection error:', error);
  }
}; 