# Notification System Backend API Requirements

## Overview
This document outlines the backend API requirements for the dynamic notification system. The frontend has been updated to support real-time notifications for Q&A approvals, project updates, document approvals, and team invitations.

## Base URL
All API endpoints should be prefixed with your backend base URL (e.g., `http://localhost:8000/api/notifications`).

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <user_access_token>
```

## Data Types

### Notification Object
```typescript
interface Notification {
  id: string;                    // Unique identifier
  type: 'qa_approval' | 'project_update' | 'document_approval' | 'team_invite';
  title: string;                 // Short notification title
  message: string;               // Detailed notification message
  data: QAApprovalData | ProjectUpdateData | DocumentApprovalData | TeamInviteData;
  createdAt: string;             // ISO 8601 timestamp
  readAt?: string;               // ISO 8601 timestamp (null if unread)
  expiresAt?: string;            // ISO 8601 timestamp (optional)
  priority: 'low' | 'medium' | 'high';
  userId: string;                // Target user ID
  organizationId?: string;       // Optional organization context
}
```

### QA Approval Data
```typescript
interface QAApprovalData {
  qaId: string;
  question: string;
  answer: string;
  source: string;               // e.g., "Teams", "Slack"
  channel: string;              // Channel/room name
  author: string;               // Answer author
  questionAuthor: string;       // Question author
  platform: 'teams' | 'slack' | 'github' | 'discord';
  tags?: string[];              // Optional tags
  verified?: boolean;           // Answer verification status
  timestamp: string;            // Original timestamp from platform
  questionTime: string;         // When question was asked
  answerTime: string;           // When answer was provided
}
```

### Project Update Data
```typescript
interface ProjectUpdateData {
  projectId: string;
  projectName: string;
  updateType: 'documentation' | 'contribution' | 'milestone';
  description: string;
  author: string;
}
```

### Document Approval Data
```typescript
interface DocumentApprovalData {
  documentId: string;
  documentTitle: string;
  documentType: 'knowledge_base' | 'procedure' | 'guide';
  author: string;
  changes: string[];
}
```

### Team Invite Data
```typescript
interface TeamInviteData {
  teamId: string;
  teamName: string;
  inviterName: string;
  role: string;
}
```

## API Endpoints

### 1. Get Notifications
**GET** `/api/notifications`

**Query Parameters:**
- `unreadOnly` (boolean, optional): Filter to only unread notifications
- `limit` (integer, optional): Number of notifications to return (default: 20)
- `offset` (integer, optional): Pagination offset (default: 0)
- `type` (string, optional): Filter by notification type

**Response:**
```json
{
  "data": [
    {
      "id": "notification-123",
      "type": "qa_approval",
      "title": "New Q&A needs approval",
      "message": "A Q&A from Teams channel needs your review",
      "data": {
        "qaId": "qa-456",
        "question": "How do we handle errors in our API?",
        "answer": "We use structured error responses with proper HTTP status codes...",
        "source": "Teams",
        "channel": "Developer Team",
        "author": "John Doe",
        "questionAuthor": "Jane Smith",
        "platform": "teams",
        "tags": ["api", "error-handling", "best-practices"],
        "verified": false,
        "timestamp": "2 hours ago",
        "questionTime": "Today at 14:30",
        "answerTime": "Today at 15:45"
      },
      "createdAt": "2024-01-15T15:45:00Z",
      "readAt": null,
      "priority": "high",
      "userId": "user-789",
      "organizationId": "org-123"
    }
  ],
  "totalCount": 15,
  "hasMore": true
}
```

### 2. Get Notification by ID
**GET** `/api/notifications/{notificationId}`

**Response:**
```json
{
  "data": {
    // Single notification object
  }
}
```

### 3. Mark Notification as Read
**PUT** `/api/notifications/{notificationId}/read`

**Response:**
```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

### 4. Mark All Notifications as Read
**PUT** `/api/notifications/read-all`

**Response:**
```json
{
  "success": true,
  "message": "All notifications marked as read",
  "data": {
    "updatedCount": 5
  }
}
```

### 5. Perform Notification Action
**POST** `/api/notifications/{notificationId}/action`

**Request Body:**
```json
{
  "type": "approve" | "reject" | "dismiss" | "view",
  "data": {}  // Optional additional data
}
```

**Response for Approve Action:**
```json
{
  "success": true,
  "message": "Q&A approved and stored in knowledge base",
  "data": {
    "documentId": "doc-456",
    "knowledgeBaseUrl": "/documents/doc-456"
  }
}
```

**Response for Reject Action:**
```json
{
  "success": true,
  "message": "Q&A rejected"
}
```

### 6. Delete Notification
**DELETE** `/api/notifications/{notificationId}`

**Response:**
```json
{
  "success": true,
  "message": "Notification deleted"
}
```

### 7. Get Unread Count
**GET** `/api/notifications/unread-count`

**Response:**
```json
{
  "data": {
    "count": 3
  }
}
```

### 8. Create Test Notification (Development Only)
**POST** `/api/notifications/test`

**Request Body:**
```json
{
  "type": "qa_approval",
  "data": {
    "qaId": "test-qa-123",
    "question": "Test question?",
    "answer": "Test answer",
    "source": "Teams",
    "channel": "Test Channel",
    "author": "Test User",
    "questionAuthor": "Test Questioner",
    "platform": "teams",
    "timestamp": "1 minute ago",
    "questionTime": "Today at 16:00",
    "answerTime": "Today at 16:01"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test notification created",
  "data": {
    "notificationId": "notification-test-123"
  }
}
```

## Real-time Updates

### Server-Sent Events (SSE)
**GET** `/api/notifications/stream/{userId}`

**Description:** Establishes a Server-Sent Events connection for real-time notification updates.

**Event Types:**
1. **New Notification:**
```json
{
  "type": "notification",
  "notification": {
    // Full notification object
  }
}
```

2. **Unread Count Update:**
```json
{
  "type": "unread_count",
  "count": 5
}
```

**Frontend Usage:**
```javascript
const eventSource = new EventSource(`/api/notifications/stream/${userId}`);
eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'notification') {
    // Handle new notification
  } else if (data.type === 'unread_count') {
    // Update unread count
  }
};
```

## Backend Implementation Requirements

### 1. Database Schema
You'll need tables/collections for:
- **Notifications**: Store notification data
- **NotificationActions**: Track user actions on notifications
- **NotificationSettings**: User notification preferences

### 2. Integration Points
- **Q&A System**: When a new Q&A is detected from external platforms
- **Knowledge Base**: When approving Q&As, create documents
- **User Management**: Link notifications to users and organizations
- **External Platforms**: Webhooks from Teams, Slack, etc.

### 3. Business Logic
- **Auto-expire**: Notifications should auto-expire based on `expiresAt`
- **Deduplication**: Prevent duplicate notifications for the same content
- **Permission Checks**: Ensure users can only see their notifications
- **Action Processing**: Handle approve/reject actions appropriately

### 4. Real-time Updates
Implement Server-Sent Events or WebSocket connections for real-time updates. Consider using:
- Redis for pub/sub messaging
- Message queues for reliable delivery
- Connection management for user sessions

## Error Handling

All endpoints should return appropriate HTTP status codes and error messages:

**400 Bad Request:**
```json
{
  "error": "Invalid notification type",
  "code": "INVALID_TYPE"
}
```

**401 Unauthorized:**
```json
{
  "error": "Authentication required",
  "code": "AUTH_REQUIRED"
}
```

**403 Forbidden:**
```json
{
  "error": "Access denied to this notification",
  "code": "ACCESS_DENIED"
}
```

**404 Not Found:**
```json
{
  "error": "Notification not found",
  "code": "NOT_FOUND"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Internal server error",
  "code": "INTERNAL_ERROR"
}
```

## Security Considerations

1. **Authentication**: Validate Bearer tokens for all requests
2. **Authorization**: Users can only access their own notifications
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Input Validation**: Sanitize all input data
5. **CORS**: Configure appropriate CORS headers for frontend domain

## Testing

Create test endpoints or fixtures for:
1. Creating sample notifications of each type
2. Testing real-time updates
3. Testing different user permissions
4. Performance testing with large notification volumes

## Frontend Integration

The frontend expects:
1. All timestamps in ISO 8601 format
2. Consistent response structure with `data` wrapper
3. Real-time updates via Server-Sent Events
4. Proper error handling with structured error responses

This completes the backend API requirements for the dynamic notification system.