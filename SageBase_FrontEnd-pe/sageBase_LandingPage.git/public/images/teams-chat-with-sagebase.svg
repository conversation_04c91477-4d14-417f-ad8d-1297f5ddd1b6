<svg width="600" height="500" viewBox="0 0 600 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Teams Interface Background -->
  <rect width="600" height="500" fill="#f3f2f1" rx="8"/>
  
  <!-- Header Bar -->
  <rect width="600" height="60" fill="#6264a7" rx="8 8 0 0"/>
  <text x="20" y="35" font-family="Segoe UI, Arial, sans-serif" font-size="16" font-weight="600" fill="white">
    Microsoft Teams - Development Team
  </text>
  
  <!-- Chat Messages Container -->
  <rect x="0" y="60" width="600" height="380" fill="white"/>
  
  <!-- Message 1: JD asking question -->
  <g transform="translate(20, 80)">
    <circle cx="20" cy="20" r="16" fill="#6264a7"/>
    <text x="20" y="26" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">JD</text>
    <rect x="45" y="5" width="480" height="55" fill="#f8f8f8" rx="8"/>
    <text x="55" y="22" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#252423">JD</text>
    <text x="55" y="38" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#605e5c">Hey team, I'm getting this error with @babel/preset-env:</text>
    <text x="55" y="52" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#605e5c">"Module not found: Can't resolve '@babel/preset-env'"</text>
  </g>
  
  <!-- Message 2: Wissem's solution -->
  <g transform="translate(20, 155)">
    <circle cx="20" cy="20" r="16" fill="#00bcf2"/>
    <text x="20" y="26" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">W</text>
    <rect x="45" y="5" width="480" height="70" fill="#f8f8f8" rx="8"/>
    <text x="55" y="22" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#252423">Wissem</text>
    <text x="55" y="38" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#605e5c">Try this command:</text>
    <rect x="55" y="45" width="450" height="22" fill="#f1f1f1" rx="4"/>
    <text x="65" y="59" font-family="Consolas, Monaco, monospace" font-size="11" fill="#0078d4">npm install --save-dev @babel/preset-env</text>
  </g>
  
  <!-- Message 3: JD's confirmation -->
  <g transform="translate(20, 245)">
    <circle cx="20" cy="20" r="16" fill="#6264a7"/>
    <text x="20" y="26" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">JD</text>
    <rect x="45" y="5" width="480" height="40" fill="#f8f8f8" rx="8"/>
    <text x="55" y="22" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#252423">JD</text>
    <text x="55" y="38" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#605e5c">Perfect! That fixed it. Thanks Wissem! 🎉</text>
  </g>
  
  <!-- SageBase Notification -->
  <g transform="translate(20, 305)">
    <!-- SageBase Bot Avatar with Much Bigger Logo -->
    <rect x="2" y="2" width="64" height="64" fill="#4907E2" rx="32"/>
    <!-- Much Larger SageBase "S" Logo with better styling -->
    <text x="34" y="44" font-family="Segoe UI, Arial, sans-serif" font-size="36" font-weight="bold" fill="white" text-anchor="middle">S</text>
    <!-- Add a subtle inner glow effect -->
    <circle cx="34" cy="34" r="30" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
    
    <!-- Message Bubble - redesigned like standard Teams message -->
    <rect x="80" y="5" width="485" height="100" fill="#f8f8f8" rx="8"/>
    
    <!-- SageBase as sender name (like other Teams users) -->
    <text x="90" y="22" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#6264A7">SageBase</text>
    
    <!-- Message content -->
    <text x="90" y="40" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#252423">SageBase captured important information from your team discussion.</text>
    <text x="90" y="55" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#252423">A draft documentation is ready for your review and approval.</text>
    
    <!-- Knowledge Path in green -->
    <text x="90" y="75" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#22C55E">Knowledge Spaces/Nova/Tips/Q/A</text>
    
    <!-- Timestamp at bottom right -->
    <text x="540" y="95" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#a19f9d">05:42 PM</text>
  </g>
  
  <!-- Timestamp indicators -->
  <text x="540" y="100" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#a19f9d">2:30 PM</text>
  <text x="540" y="175" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#a19f9d">2:32 PM</text>
  <text x="540" y="265" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#a19f9d">2:33 PM</text>
  <text x="540" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#a19f9d">2:33 PM</text>
  
  <!-- Bottom input area -->
  <rect x="0" y="440" width="600" height="60" fill="#f3f2f1"/>
  <rect x="20" y="455" width="520" height="30" fill="white" stroke="#e1dfdd" rx="15"/>
  <text x="35" y="475" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#a19f9d">Type a message...</text>
  
  <!-- Send button -->
  <circle cx="565" cy="470" r="15" fill="#6264a7"/>
  <path d="M560 470 L570 470 M565 465 L570 470 L565 475" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
</svg> 