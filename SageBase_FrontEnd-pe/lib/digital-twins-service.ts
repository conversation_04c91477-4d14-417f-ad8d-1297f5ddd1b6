import { createClient } from "@supabase/supabase-js"
import type { DigitalTwin } from "./digital-twins-service" // Ensure this type definition exists or is created if not already present

// Check if Supabase environment variables are available
const hasSupabaseConfig = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Only create Supabase client if environment variables are available
const supabase = hasSupabaseConfig 
  ? createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
  : null

// Static fallback data using the provided images
const FALLBACK_DIGITAL_TWINS: DigitalTwin[] = [
  {
    id: "1",
    name: "<PERSON>",
    role: "Senior Backend Engineer",
    last_sync_date: "2025-05-10T10:00:00Z",
    coverage_score: 92,
    avatar_url:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Middle_Eastern_Woman_Professional_Headshot-C7dalrC4hEMi88IPIRluqWetUKxBJI.png",
  },
  {
    id: "2",
    name: "<PERSON> S.",
    role: "Product Manager",
    last_sync_date: "2025-04-18T10:00:00Z",
    coverage_score: 88,
    avatar_url:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Black_Man_Professional_Headshot-9DAGGZRt0csLKnG84s43MGAZnlfQCK.png",
  },
  {
    id: "3",
    name: "Lena K.",
    role: "UX Lead",
    last_sync_date: "2025-05-20T10:00:00Z",
    coverage_score: 75,
    avatar_url:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Hispanic_Woman_Professional_Headshot-5vBkK7uJFFQF36xtB2kDRe2FASqceU.png",
  },
  {
    id: "4",
    name: "David B.",
    role: "DevOps Specialist",
    last_sync_date: "2025-03-29T10:00:00Z",
    coverage_score: 95,
    avatar_url:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/White_Man_Professional_Headshot-mi0SIGt1po5kuirSrxhewehFZ2pLeV.png",
  },
]

export interface CreateDigitalTwinInput {
  name: string
  role: string
  coverage_score?: number
  avatar_file?: File
}

export interface UpdateDigitalTwinInput extends Partial<CreateDigitalTwinInput> {
  id: string
}

export class DigitalTwinsService {
  private async checkTableExists(): Promise<boolean> {
    if (!supabase) return false;
    
    try {
      const { error } = await supabase.from("digital_twins").select("id").limit(1)
      return !error
    } catch {
      return false
    }
  }

  /**
   * Get all digital twins
   */
  async getDigitalTwins(): Promise<DigitalTwin[]> {
    if (!supabase) {
      console.warn("Supabase client not initialized, using fallback data")
      return FALLBACK_DIGITAL_TWINS
    }

    try {
      // Check if table exists first
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        console.warn("Digital twins table not found, using fallback data")
        return FALLBACK_DIGITAL_TWINS
      }

      const { data, error } = await supabase
        .from("digital_twins")
        .select("*")
        .eq("status", "active")
        .order("created_at", { ascending: false })

      if (error) {
        console.error("Error fetching digital twins:", error)
        console.warn("Falling back to static data")
        return FALLBACK_DIGITAL_TWINS
      }

      return data || FALLBACK_DIGITAL_TWINS
    } catch (error) {
      console.error("Error in getDigitalTwins:", error)
      console.warn("Falling back to static data")
      return FALLBACK_DIGITAL_TWINS
    }
  }

  /**
   * Get digital twin by ID
   */
  async getDigitalTwin(id: string): Promise<DigitalTwin | null> {
    if (!supabase) {
      return FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === id) || null
    }

    try {
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        return FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === id) || null
      }

      const { data, error } = await supabase.from("digital_twins").select("*").eq("id", id).single()

      if (error) {
        if (error.code === "PGRST116") {
          return FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === id) || null
        }
        console.error("Error fetching digital twin:", error)
        return FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === id) || null
      }

      return data
    } catch (error) {
      console.error("Error in getDigitalTwin:", error)
      return FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === id) || null
    }
  }

  /**
   * Create new digital twin (fallback to local storage if no database)
   */
  async createDigitalTwin(input: CreateDigitalTwinInput): Promise<DigitalTwin> {
    if (!supabase) {
      // Create a mock digital twin for demo purposes
      const newTwin: DigitalTwin = {
        id: crypto.randomUUID(),
        name: input.name,
        role: input.role,
        last_sync_date: new Date().toISOString(),
        coverage_score: input.coverage_score || 0,
        avatar_url: undefined,
      }

      console.warn("Supabase client not initialized, created mock digital twin")
      return newTwin
    }

    try {
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        // Create a mock digital twin for demo purposes
        const newTwin: DigitalTwin = {
          id: crypto.randomUUID(),
          name: input.name,
          role: input.role,
          last_sync_date: new Date().toISOString(),
          coverage_score: input.coverage_score || 0,
          avatar_url: undefined,
        }

        console.warn("Database not available, created mock digital twin")
        return newTwin
      }

      // Database implementation would go here
      throw new Error("Database implementation not yet available")
    } catch (error) {
      console.error("Error in createDigitalTwin:", error)
      throw error
    }
  }

  /**
   * Update digital twin
   */
  async updateDigitalTwin(input: UpdateDigitalTwinInput): Promise<DigitalTwin> {
    if (!supabase) {
      const existingTwin = FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === input.id)
      if (!existingTwin) {
        throw new Error("Digital twin not found")
      }

      // Return updated mock twin
      return {
        ...existingTwin,
        ...input,
        updated_at: new Date().toISOString(),
      }
    }

    try {
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        const existingTwin = FALLBACK_DIGITAL_TWINS.find((twin) => twin.id === input.id)
        if (!existingTwin) {
          throw new Error("Digital twin not found")
        }

        // Return updated mock twin
        return {
          ...existingTwin,
          ...input,
          updated_at: new Date().toISOString(),
        }
      }

      // Database implementation would go here
      throw new Error("Database implementation not yet available")
    } catch (error) {
      console.error("Error in updateDigitalTwin:", error)
      throw error
    }
  }

  /**
   * Delete digital twin (soft delete)
   */
  async deleteDigitalTwin(id: string): Promise<boolean> {
    if (!supabase) {
      console.warn("Supabase client not initialized, mock delete operation")
      return true
    }

    try {
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        console.warn("Database not available, mock delete operation")
        return true
      }

      // Database implementation would go here
      throw new Error("Database implementation not yet available")
    } catch (error) {
      console.error("Error in deleteDigitalTwin:", error)
      return false
    }
  }

  /**
   * Search digital twins
   */
  async searchDigitalTwins(query: string): Promise<DigitalTwin[]> {
    if (!supabase) {
      return FALLBACK_DIGITAL_TWINS.filter(
        (twin) =>
          twin.name.toLowerCase().includes(query.toLowerCase()) ||
          twin.role.toLowerCase().includes(query.toLowerCase()) ||
          (twin.department && twin.department.toLowerCase().includes(query.toLowerCase())),
      )
    }

    try {
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        return FALLBACK_DIGITAL_TWINS.filter(
          (twin) =>
            twin.name.toLowerCase().includes(query.toLowerCase()) ||
            twin.role.toLowerCase().includes(query.toLowerCase()) ||
            (twin.department && twin.department.toLowerCase().includes(query.toLowerCase())),
        )
      }

      const { data, error } = await supabase
        .from("digital_twins")
        .select("*")
        .eq("status", "active")
        .or(`name.ilike.%${query}%,role.ilike.%${query}%,department.ilike.%${query}%`)
        .order("created_at", { ascending: false })

      if (error) {
        console.error("Error searching digital twins:", error)
        return FALLBACK_DIGITAL_TWINS.filter(
          (twin) =>
            twin.name.toLowerCase().includes(query.toLowerCase()) ||
            twin.role.toLowerCase().includes(query.toLowerCase()) ||
            (twin.department && twin.department.toLowerCase().includes(query.toLowerCase())),
        )
      }

      return data || []
    } catch (error) {
      console.error("Error in searchDigitalTwins:", error)
      return FALLBACK_DIGITAL_TWINS.filter(
        (twin) =>
          twin.name.toLowerCase().includes(query.toLowerCase()) ||
          twin.role.toLowerCase().includes(query.toLowerCase()) ||
          (twin.department && twin.department.toLowerCase().includes(query.toLowerCase())),
      )
    }
  }
}

export const digitalTwinsService = new DigitalTwinsService()

// Mock function if you don't have one, for type checking in the page
export const getDigitalTwinsMock = async (): Promise<DigitalTwin[]> => {
  // This is a mock. Replace with your actual data fetching logic.
  return [
    {
      id: "1",
      name: "Alice Wonderland",
      role: "Lead Engineer",
      last_sync_date: "2024-05-15",
      coverage_score: 95,
      avatar_url: "/placeholder.svg?width=40&height=40",
    },
    {
      id: "2",
      name: "Bob The Builder",
      role: "Product Manager",
      last_sync_date: "2024-05-20",
      coverage_score: 88,
      avatar_url: "/placeholder.svg?width=40&height=40",
    },
  ]
}
