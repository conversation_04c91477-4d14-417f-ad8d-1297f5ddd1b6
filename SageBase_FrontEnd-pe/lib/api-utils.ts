import { toast } from "@/components/ui/use-toast";

// Helper function to convert Tailwind classes to hex colors
const convertTailwindToHex = (tailwindClass: string): string => {
  const colorMap: { [key: string]: string } = {
    'bg-primary-100': '#dbeafe',
    'bg-primary-50': '#eff6ff',
    'bg-primary-200': '#bfdbfe',
    'bg-gray-100': '#f3f4f6',
    'bg-blue-100': '#dbeafe',
    'bg-green-100': '#dcfce7',
    'bg-yellow-100': '#fef3c7',
    'bg-red-100': '#fee2e2',
    'bg-purple-100': '#f3e8ff',
    'bg-pink-100': '#fce7f3',
    'bg-indigo-100': '#e0e7ff',
    'bg-orange-100': '#ffedd5',
    'bg-teal-100': '#ccfbf1',
    'bg-cyan-100': '#cffafe',
  };
  
  return colorMap[tailwindClass] || '#10b981'; // Default to green if not found
};

// Session cache to reduce repeated auth calls
let sessionCache: { token: string; expiresAt: number } | null = null;
const SESSION_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Request debouncing to prevent excessive auth calls
let authRequestPromise: Promise<HeadersInit> | null = null;
const AUTH_REQUEST_DEBOUNCE = 1000; // 1 second debounce

// Global rate limiting to prevent excessive auth calls
let lastAuthCallTime = 0;
const MIN_AUTH_CALL_INTERVAL = 5000; // 5 seconds minimum between auth calls

// Use centralized auth service
export const getAuthHeaders = async (): Promise<HeadersInit> => {
  try {
    const { getAuthHeaders: getAuthHeadersFromService } = await import('./auth-service');
    return await getAuthHeadersFromService();
  } catch (error) {
    console.warn('Failed to get auth headers:', error);
    return { 'Content-Type': 'application/json' };
  }
};

// Function to clear session cache (useful for logout)
export const clearSessionCache = () => {
  sessionCache = null;
};

// Generic API call function with error handling
export const apiCall = async <T>(
  endpoint: string,
  options: RequestInit = {},
  successMessage?: string,
  errorTitle?: string
): Promise<{ success: boolean; data?: T; error?: string }> => {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

    
    const headers = await getAuthHeaders();
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });



    const result = await response.json();

    if (response.ok && result.success) {
      // Success
      if (successMessage) {
        toast({
          title: "Success",
          description: successMessage,
          variant: "default",
        });
      }
      return { success: true, data: result.data };
    } else {
      // API returned an error
      
      // Build detailed error message
      let errorMessage = result.error || 'Operation failed';
      
      // Add details if available
      if (result.details) {
        if (typeof result.details === 'object') {
          // Handle field-specific errors
          const fieldErrors = Object.entries(result.details)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          errorMessage = `${errorMessage}: ${fieldErrors}`;
        } else {
          errorMessage = `${errorMessage}: ${result.details}`;
        }
      }
      
      
      // Force the toast to show
      setTimeout(() => {
        toast({
          title: errorTitle || "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }, 100);
      
      return { success: false, error: errorMessage };
    }
  } catch (error) {
    // Network or other error
    console.error('API call error:', error);
    const errorMessage = "Network error or server unavailable. Please try again.";
    toast({
      title: errorTitle || "Error",
      description: errorMessage,
      variant: "destructive",
    });
    return { success: false, error: errorMessage };
  }
};

// Project API functions
export const createProject = async (projectData: {
  name: string;
  description: string;
  categories: string[];
  repo_path?: string;
  docs_path?: string;
  repo_type?: 'github' | 'gitlab';
  color?: string;
  doc_responsible_id?: string;
  secondary_responsible_id?: string;
}) => {

  
  // Convert to backend expected format
  const backendData: any = {
    name: projectData.name,
    color: convertTailwindToHex(projectData.color || "") || "#10b981", // Convert Tailwind class to hex
    initial: projectData.name.charAt(0).toUpperCase(),
  };

  // Add assignees if provided
  if (projectData.doc_responsible_id) {
    backendData.doc_responsible_id = projectData.doc_responsible_id;
  }
  if (projectData.secondary_responsible_id) {
    backendData.secondary_responsible_id = projectData.secondary_responsible_id;
  }
  
  
  const result = await apiCall(
    '/api/knowledge-spaces',
    {
      method: 'POST',
      body: JSON.stringify(backendData),
    },
    `Successfully created project "${projectData.name}"`,
    "Error Creating Project"
  );
  
  console.log("createProject result:", result);
  return result;
};

// Q&A API functions
export const createQA = async (knowledgeSpaceId: string, qaData: {
  question_title: string;
  question_content: string;
  question_tags?: string[];
  answer_content?: string;
  answer_code?: string;
  answer_explanation?: string;
}) => {
  return apiCall(
    `/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}`,
    {
      method: 'POST',
      body: JSON.stringify(qaData),
    },
    "Successfully created Q&A",
    "Error Creating Q&A"
  );
};

export const voteOnQA = async (knowledgeSpaceId: string, qaId: string, voteType: 'up' | 'down') => {
  return apiCall(
    `/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}/qa/${qaId}/vote`,
    {
      method: 'POST',
      body: JSON.stringify({ type: voteType }),
    },
    undefined, // No success toast for voting
    "Error Voting"
  );
};

export const updateQA = async (knowledgeSpaceId: string, qaId: string, updates: {
  question_title?: string;
  question_content?: string;
  question_tags?: string[];
  answer_content?: string;
  answer_code?: string;
  answer_explanation?: string;
}) => {
  return apiCall(
    `/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}/qa/${qaId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updates),
    },
    "Successfully updated Q&A",
    "Error Updating Q&A"
  );
};

export const deleteQA = async (knowledgeSpaceId: string, qaId: string) => {
  return apiCall(
    `/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}/qa/${qaId}`,
    {
      method: 'DELETE',
    },
    "Successfully deleted Q&A",
    "Error Deleting Q&A"
  );
};

export const deleteProject = async (knowledgeSpaceId: string) => {
  return apiCall(
    `/api/knowledge-spaces/knowledge-space/${knowledgeSpaceId}`,
    {
      method: 'DELETE',
    },
    "Successfully deleted project",
    "Error Deleting Project"
  );
}; 