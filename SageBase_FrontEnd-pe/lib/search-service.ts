// Mock document data structure
export interface Document {
  id: string
  title: string
  content: string
  createdAt: string
  updatedAt: string
  author: {
    name: string
    avatar: string
  }
  tags: string[]
  space: string
}

// Mock document data
const mockDocuments: Document[] = [
  {
    id: "doc-1",
    title: "Authentication Service Architecture",
    content:
      "This document outlines the architecture of our authentication service. It uses OAuth 2.0 for secure API authentication. All requests must include a valid token in the Authorization header.",
    createdAt: "2023-05-15T10:30:00Z",
    updatedAt: "2023-05-20T14:45:00Z",
    author: {
      name: "<PERSON>",
      avatar: "/current-user-profile.png",
    },
    tags: ["authentication", "security", "architecture"],
    space: "Engineering",
  },
  {
    id: "doc-2",
    title: "API Documentation Guidelines",
    content:
      "Guidelines for documenting APIs. All endpoints should be documented with request parameters, response format, and example usage. Authentication requirements should be clearly specified.",
    createdAt: "2023-04-10T09:15:00Z",
    updatedAt: "2023-04-12T11:20:00Z",
    author: {
      name: "<PERSON>",
      avatar: "/current-user-profile.png",
    },
    tags: ["api", "documentation", "guidelines"],
    space: "Engineering",
  },
  {
    id: "doc-3",
    title: "Product Roadmap Q3 2023",
    content:
      "This document outlines our product roadmap for Q3 2023. Key features include enhanced authentication, improved search functionality, and integration with third-party services.",
    createdAt: "2023-06-01T13:45:00Z",
    updatedAt: "2023-06-05T16:30:00Z",
    author: {
      name: "Alex Rodriguez",
      avatar: "/current-user-profile.png",
    },
    tags: ["roadmap", "product", "planning"],
    space: "Product",
  },
  {
    id: "doc-4",
    title: "User Onboarding Flow",
    content:
      "Detailed description of the user onboarding flow. The process includes sign-up, email verification, profile completion, and tutorial walkthrough.",
    createdAt: "2023-05-05T11:20:00Z",
    updatedAt: "2023-05-10T09:15:00Z",
    author: {
      name: "Jamie Smith",
      avatar: "/current-user-profile.png",
    },
    tags: ["onboarding", "user experience", "flow"],
    space: "Design",
  },
  {
    id: "doc-5",
    title: "Security Best Practices",
    content:
      "This document covers security best practices for our applications. Topics include authentication, authorization, data encryption, and secure API design.",
    createdAt: "2023-03-20T14:30:00Z",
    updatedAt: "2023-03-25T10:45:00Z",
    author: {
      name: "Taylor Wong",
      avatar: "/current-user-profile.png",
    },
    tags: ["security", "best practices", "guidelines"],
    space: "Security",
  },
]

// Search result interface with highlighted content
export interface SearchResult extends Document {
  highlightedContent: string
  relevanceScore: number
}

// Function to search documents
export function searchDocuments(query: string): SearchResult[] {
  if (!query.trim()) {
    return []
  }

  const normalizedQuery = query.toLowerCase().trim()

  // Search through documents
  return mockDocuments
    .filter((doc) => {
      // Check if query matches title, content, or tags
      return (
        doc.title.toLowerCase().includes(normalizedQuery) ||
        doc.content.toLowerCase().includes(normalizedQuery) ||
        doc.tags.some((tag) => tag.toLowerCase().includes(normalizedQuery)) ||
        doc.space.toLowerCase().includes(normalizedQuery)
      )
    })
    .map((doc) => {
      // Calculate relevance score (higher is better)
      let relevanceScore = 0

      // Title matches are most important
      if (doc.title.toLowerCase().includes(normalizedQuery)) {
        relevanceScore += 10
      }

      // Content matches
      if (doc.content.toLowerCase().includes(normalizedQuery)) {
        relevanceScore += 5
      }

      // Tag matches
      if (doc.tags.some((tag) => tag.toLowerCase().includes(normalizedQuery))) {
        relevanceScore += 3
      }

      // Space matches
      if (doc.space.toLowerCase().includes(normalizedQuery)) {
        relevanceScore += 2
      }

      // Create highlighted content
      let highlightedContent = doc.content

      // Find the position of the query in the content
      const queryIndex = doc.content.toLowerCase().indexOf(normalizedQuery)

      if (queryIndex !== -1) {
        // Extract a snippet around the match
        const snippetStart = Math.max(0, queryIndex - 50)
        const snippetEnd = Math.min(doc.content.length, queryIndex + normalizedQuery.length + 50)
        highlightedContent = doc.content.substring(snippetStart, snippetEnd)

        // Add ellipsis if we're not at the beginning or end
        if (snippetStart > 0) {
          highlightedContent = "..." + highlightedContent
        }
        if (snippetEnd < doc.content.length) {
          highlightedContent = highlightedContent + "..."
        }
      }

      return {
        ...doc,
        highlightedContent,
        relevanceScore,
      }
    })
    .sort((a, b) => b.relevanceScore - a.relevanceScore) // Sort by relevance
}

// Function to highlight matched terms in text
export function highlightMatches(text: string, query: string): string {
  if (!query.trim()) {
    return text
  }

  const normalizedQuery = query.toLowerCase().trim()
  const normalizedText = text.toLowerCase()

  let result = ""
  let lastIndex = 0

  // Find all occurrences of the query in the text
  let index = normalizedText.indexOf(normalizedQuery, lastIndex)

  while (index !== -1) {
    // Add text before the match
    result += text.substring(lastIndex, index)

    // Add the highlighted match
    result += `<mark class="bg-yellow-200 px-0.5 rounded">${text.substring(index, index + normalizedQuery.length)}</mark>`

    // Update lastIndex
    lastIndex = index + normalizedQuery.length

    // Find the next occurrence
    index = normalizedText.indexOf(normalizedQuery, lastIndex)
  }

  // Add the remaining text
  result += text.substring(lastIndex)

  return result
}

// Number of previous chat messages (question/answer pairs) to send to the backend for context
export const HISTORY_CONTEXT_LENGTH = 5;
