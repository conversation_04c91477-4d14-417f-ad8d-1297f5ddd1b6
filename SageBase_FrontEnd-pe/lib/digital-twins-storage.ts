import { createClient } from "@supabase/supabase-js"

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

export interface DigitalTwinImageUpload {
  file: File
  twinId: string
  fileName?: string
}

export interface DigitalTwinImageMetadata {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  width?: number
  height?: number
  uploadedAt: Date
}

export class DigitalTwinsStorageService {
  private readonly BUCKET_NAME = "digital-twins-avatars"
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
  private readonly ALLOWED_TYPES = ["image/jpeg", "image/png", "image/webp"]

  /**
   * Upload avatar image for a digital twin
   */
  async uploadAvatar(upload: DigitalTwinImageUpload): Promise<{
    url: string
    filePath: string
    metadata: DigitalTwinImageMetadata
  } | null> {
    try {
      // Validate file
      const validation = this.validateFile(upload.file)
      if (!validation.isValid) {
        throw new Error(validation.error)
      }

      // Generate unique file path
      const fileExtension = upload.file.name.split(".").pop()
      const fileName = upload.fileName || `${upload.twinId}-avatar-${Date.now()}.${fileExtension}`
      const filePath = `avatars/${upload.twinId}/${fileName}`

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage.from(this.BUCKET_NAME).upload(filePath, upload.file, {
        cacheControl: "3600",
        upsert: true,
      })

      if (error) {
        console.error("Storage upload error:", error)
        throw new Error(`Failed to upload image: ${error.message}`)
      }

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from(this.BUCKET_NAME).getPublicUrl(data.path)

      // Get image dimensions
      const dimensions = await this.getImageDimensions(upload.file)

      // Create metadata
      const metadata: DigitalTwinImageMetadata = {
        id: upload.twinId,
        fileName: fileName,
        fileSize: upload.file.size,
        fileType: upload.file.type,
        width: dimensions.width,
        height: dimensions.height,
        uploadedAt: new Date(),
      }

      return {
        url: publicUrl,
        filePath: data.path,
        metadata,
      }
    } catch (error) {
      console.error("Error uploading avatar:", error)
      return null
    }
  }

  /**
   * Delete avatar image
   */
  async deleteAvatar(filePath: string): Promise<boolean> {
    try {
      const { error } = await supabase.storage.from(this.BUCKET_NAME).remove([filePath])

      if (error) {
        console.error("Storage delete error:", error)
        return false
      }

      return true
    } catch (error) {
      console.error("Error deleting avatar:", error)
      return false
    }
  }

  /**
   * Get optimized avatar URL with transformations
   */
  getOptimizedAvatarUrl(
    filePath: string,
    options?: {
      width?: number
      height?: number
      quality?: number
    },
  ): string {
    const {
      data: { publicUrl },
    } = supabase.storage.from(this.BUCKET_NAME).getPublicUrl(filePath)

    if (!options) return publicUrl

    // Add transformation parameters if supported
    const params = new URLSearchParams()
    if (options.width) params.append("width", options.width.toString())
    if (options.height) params.append("height", options.height.toString())
    if (options.quality) params.append("quality", options.quality.toString())

    return params.toString() ? `${publicUrl}?${params.toString()}` : publicUrl
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: File): { isValid: boolean; error?: string } {
    if (!file) {
      return { isValid: false, error: "No file provided" }
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return { isValid: false, error: "File size exceeds 5MB limit" }
    }

    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return { isValid: false, error: "File type not supported. Use JPEG, PNG, or WebP" }
    }

    return { isValid: true }
  }

  /**
   * Get image dimensions
   */
  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({ width: img.width, height: img.height })
      }
      img.onerror = () => {
        resolve({ width: 0, height: 0 })
      }
      img.src = URL.createObjectURL(file)
    })
  }
}

export const digitalTwinsStorage = new DigitalTwinsStorageService()
