import React from 'react';

/**
 * Utility function to convert text with URLs into JSX with clickable links
 * @param message - The message string that may contain URLs
 * @returns React.ReactNode with clickable links
 */
export const formatMessageWithLinks = (message: string): React.ReactNode => {
  // URL regex pattern to match http and https URLs
  const urlPattern = /(https?:\/\/[^\s]+)/g;
  
  // Split the message by URLs
  const parts = message.split(urlPattern);
  
  return parts.map((part, index) => {
    // Check if this part is a URL
    if (urlPattern.test(part)) {
      return React.createElement('a', {
        key: index,
        href: part,
        target: '_blank',
        rel: 'noopener noreferrer',
        className: 'text-blue-600 hover:text-blue-800 underline decoration-2 underline-offset-2 transition-colors duration-200 font-medium'
      }, part);
    }
    // Regular text
    return part;
  });
};

/**
 * Helper function to create toast content with clickable links
 * @param message - The message from API response
 * @param fallbackMessage - Default message if API message is empty
 * @returns Formatted message with clickable links or fallback
 */
export const createToastMessage = (message?: string, fallbackMessage?: string): React.ReactNode => {
  if (message) {
    return formatMessageWithLinks(message);
  }
  return fallbackMessage || 'Operation completed successfully';
};
