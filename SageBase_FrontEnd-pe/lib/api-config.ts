/**
 * Get the backend API URL based on environment
 * - Development mode (npm run dev): Use NEXT_PUBLIC_BACKEND_API_URL
 * - Production/deployment: Use NEXT_PUBLIC_BACKEND_API_URL
 * Note: NGROK URL is only used for OAuth callbacks, not for regular API calls
 */
export function getBackendApiUrl(): string {
  // Always use the regular API URL for API calls
  const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL;
  if (apiUrl) {
    return apiUrl.replace(/\/$/, ''); // Remove trailing slash
  }
  
  // Default fallback
  return 'http://localhost:8000';
}

// Export a constant for convenience
export const BACKEND_API_URL = getBackendApiUrl();

// Debug logging (only in development)
if (process.env.NODE_ENV === 'development' && typeof window === 'undefined') {
  console.log('🌐 API Configuration:');
  console.log('  - NODE_ENV:', process.env.NODE_ENV);
  console.log('  - NEXT_PUBLIC_BACKEND_API_URL:', process.env.NEXT_PUBLIC_BACKEND_API_URL);
  console.log('  - Selected Backend URL:', getBackendApiUrl());
} 