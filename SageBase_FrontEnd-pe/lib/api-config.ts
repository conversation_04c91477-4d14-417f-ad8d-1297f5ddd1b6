/**
 * Centralized API configuration
 * 
 * This file provides centralized configuration for all API endpoints and URLs.
 * Environment variables are used with sensible defaults for development.
 * 
 * - Development: Use environment variables or defaults
 * - Production/deployment: Use NEXT_PUBLIC_BACKEND_API_URL
 */

// Frontend configuration
export const getFrontendUrl = (): string => {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  if (siteUrl) {
    return siteUrl;
  }
  
  // Development fallback - should be overridden by environment variable
  console.warn('⚠️ NEXT_PUBLIC_SITE_URL not set, using development default');
  return 'http://localhost:3000';
};

// Backend API configuration
export const getBackendUrl = (): string => {
  const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL;
  if (apiUrl) {
    return apiUrl;
  }
  
  // Development fallback - should be overridden by environment variable
  console.warn('⚠️ NEXT_PUBLIC_BACKEND_API_URL not set, using development default');
  return 'http://localhost:8000';
};

// Log configuration for debugging
export const logApiConfig = (): void => {
  console.log('🌐 API Configuration:');
  console.log('  - Frontend URL:', getFrontendUrl());
  console.log('  - Backend API URL:', getBackendUrl());
  console.log('  - NEXT_PUBLIC_SITE_URL:', process.env.NEXT_PUBLIC_SITE_URL);
  console.log('  - NEXT_PUBLIC_BACKEND_API_URL:', process.env.NEXT_PUBLIC_BACKEND_API_URL);
}; 