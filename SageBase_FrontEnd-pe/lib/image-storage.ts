import { createClient } from "@supabase/supabase-js"

// Initialize the Supabase client
const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

// Interface for image metadata
export interface ImageMetadata {
  fileName: string
  fileSize: number
  fileType: string
  width?: number
  height?: number
  tags?: string[]
  description?: string
  uploadedAt: Date
}

// Function to upload an image to Supabase storage
export async function uploadImage(
  file: File,
  bucket = "app-assets",
  path = "images",
  metadata?: Partial<ImageMetadata>,
): Promise<{ url: string; metadata: ImageMetadata } | null> {
  try {
    // Create a unique file path
    const filePath = `${path}/${Date.now()}_${file.name}`

    // Upload the file to Supabase storage
    const { data, error } = await supabase.storage.from(bucket).upload(filePath, file, {
      cacheControl: "3600",
      upsert: false,
    })

    if (error) {
      console.error("Error uploading image:", error)
      return null
    }

    // Get the public URL for the uploaded file
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(data.path)

    // Create metadata object
    const imageMetadata: ImageMetadata = {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadedAt: new Date(),
      ...metadata,
    }

    // Store metadata in Supabase database
    const { error: metadataError } = await supabase.from("image_metadata").insert([
      {
        file_path: data.path,
        public_url: publicUrl,
        file_name: imageMetadata.fileName,
        file_size: imageMetadata.fileSize,
        file_type: imageMetadata.fileType,
        width: imageMetadata.width,
        height: imageMetadata.height,
        tags: imageMetadata.tags,
        description: imageMetadata.description,
        uploaded_at: imageMetadata.uploadedAt,
      },
    ])

    if (metadataError) {
      console.error("Error storing image metadata:", metadataError)
    }

    return {
      url: publicUrl,
      metadata: imageMetadata,
    }
  } catch (error) {
    console.error("Unexpected error during image upload:", error)
    return null
  }
}

// Function to get image by filename
export async function getImageByFileName(fileName: string): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .from("image_metadata")
      .select("public_url")
      .eq("file_name", fileName)
      .single()

    // If table doesn't exist or other database error, return null gracefully
    if (error) {
      if (error.code === "PGRST116" || error.message.includes("does not exist")) {
        console.log("Image metadata table not found, using fallback")
        return null
      }
      console.error("Error fetching image:", error)
      return null
    }

    return data?.public_url || null
  } catch (error) {
    console.log("Database not available, using fallback image")
    return null
  }
}

// Function to get image with metadata by filename
export async function getImageWithMetadata(fileName: string): Promise<{ url: string; metadata: any } | null> {
  try {
    const { data, error } = await supabase.from("image_metadata").select("*").eq("file_name", fileName).single()

    // If table doesn't exist or other database error, return null gracefully
    if (error) {
      if (error.code === "PGRST116" || error.message.includes("does not exist")) {
        console.log("Image metadata table not found, using fallback")
        return null
      }
      console.error("Error fetching image metadata:", error)
      return null
    }

    if (!data) return null

    return {
      url: data.public_url,
      metadata: {
        fileName: data.file_name,
        fileSize: data.file_size,
        fileType: data.file_type,
        width: data.width,
        height: data.height,
        tags: data.tags,
        description: data.description,
        uploadedAt: new Date(data.uploaded_at),
      },
    }
  } catch (error) {
    console.log("Database not available, using fallback image")
    return null
  }
}
