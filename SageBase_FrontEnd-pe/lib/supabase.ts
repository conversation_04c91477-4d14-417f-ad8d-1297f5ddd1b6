import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"

export const createClient = () => {
  // Check if environment variables are available
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.warn("Supabase environment variables are missing. Using demo mode with mock functionality.")

    // Return a mock client when environment variables are missing
    return createClientComponentClient({
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || "https://example.supabase.co",
      supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "demo-anon-key",
    })
  }

  try {
    // Return the actual client when environment variables are available
    const client = createClientComponentClient()
    
    // Don't test the connection on every client creation - this causes excessive auth calls
    // The connection will be tested when actually needed
    
    return client
  } catch (error) {
    console.error("Failed to create Supabase client:", error)
    
    // Fallback to mock client
    return createClientComponentClient({
      supabaseUrl: "https://example.supabase.co",
      supabaseKey: "demo-anon-key",
    })
  }
}

export const supabase = createClient()
