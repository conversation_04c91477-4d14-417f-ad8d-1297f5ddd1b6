import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Session, User } from '@supabase/supabase-js';

// Centralized auth state
let currentSession: Session | null = null;
let currentUser: User | null = null;
let isInitialized = false;
let initializationPromise: Promise<void> | null = null;
let lastSessionCheck = 0;
const SESSION_CHECK_INTERVAL = 30000; // 30 seconds

// Auth state listeners
const authStateListeners: Set<(session: Session | null, user: User | null) => void> = new Set();

// Initialize auth service
export const initializeAuth = async (): Promise<void> => {
  if (isInitialized) {
    return;
  }

  if (initializationPromise) {
    return initializationPromise;
  }

  initializationPromise = (async () => {
    try {
      console.log('🔐 Auth Service: Initializing...');
      const supabase = createClientComponentClient();
      
      // Get initial session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Auth Service: Error getting initial session:', error);
        currentSession = null;
        currentUser = null;
      } else {
        currentSession = session;
        currentUser = session?.user ?? null;
        console.log('🔐 Auth Service: Initial session loaded:', !!session);
      }

      // Set up auth state change listener
      supabase.auth.onAuthStateChange((event, session) => {
        console.log('🔐 Auth Service: Auth state changed:', event, {
          hasSession: !!session,
          hasUser: !!session?.user,
          eventType: event
        });
        
        currentSession = session;
        currentUser = session?.user ?? null;
        lastSessionCheck = Date.now();
        
        // Notify all listeners
        console.log('🔐 Auth Service: Notifying', authStateListeners.size, 'listeners...');
        authStateListeners.forEach((listener, index) => {
          try {
            listener(session, session?.user ?? null);
          } catch (error) {
            console.error(`Auth Service: Error in listener ${index}:`, error);
          }
        });
      });

      isInitialized = true;
      lastSessionCheck = Date.now();
    } catch (error) {
      console.error('Auth Service: Initialization error:', error);
      currentSession = null;
      currentUser = null;
      isInitialized = true;
    }
  })();

  return initializationPromise;
};

// Get current session (cached)
export const getCurrentSession = (): Session | null => {
  return currentSession;
};

// Get current user (cached)
export const getCurrentUser = (): User | null => {
  return currentUser;
};

// Get auth headers for API calls
export const getAuthHeaders = async (): Promise<HeadersInit> => {
  // Ensure auth is initialized
  if (!isInitialized) {
    await initializeAuth();
  }

  // Check if we need to refresh the session
  const now = Date.now();
  if (now - lastSessionCheck > SESSION_CHECK_INTERVAL) {
    console.log('🔐 Auth Service: Refreshing session...');
    const supabase = createClientComponentClient();
    const { data: { session } } = await supabase.auth.getSession();
    currentSession = session;
    currentUser = session?.user ?? null;
    lastSessionCheck = now;
  }

  if (currentSession?.access_token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${currentSession.access_token}`
    };
  }

  return { 'Content-Type': 'application/json' };
};

// Subscribe to auth state changes
export const subscribeToAuthChanges = (
  listener: (session: Session | null, user: User | null) => void
): (() => void) => {
  authStateListeners.add(listener);
  
  // Return unsubscribe function
  return () => {
    authStateListeners.delete(listener);
  };
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    console.log('🔐 Auth Service: Starting sign out...');
    const supabase = createClientComponentClient();
    
    // Check if there's an active session before trying to sign out
    const { data: { session } } = await supabase.auth.getSession();
    console.log('🔐 Auth Service: Current session check:', {
      hasSession: !!session,
      sessionId: session?.access_token?.substring(0, 10) + '...'
    });
    
    if (!session) {
      console.log('🔐 Auth Service: No active session found, clearing local state only...');
      // No session to sign out from, just clear local state
      currentSession = null;
      currentUser = null;
      
      // Notify listeners
      console.log('🔐 Auth Service: Notifying', authStateListeners.size, 'listeners...');
      authStateListeners.forEach(listener => {
        try {
          listener(null, null);
        } catch (error) {
          console.error('Auth Service: Error in signOut listener:', error);
        }
      });
      
      console.log('🔐 Auth Service: Sign out completed (no session)');
      return;
    }
    
    console.log('🔐 Auth Service: Calling Supabase signOut...');
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('🔐 Auth Service: Supabase signOut error:', error);
      throw error;
    }
    
    console.log('🔐 Auth Service: Supabase signOut successful, clearing local state...');
    currentSession = null;
    currentUser = null;
    
    // Notify listeners
    console.log('🔐 Auth Service: Notifying', authStateListeners.size, 'listeners...');
    authStateListeners.forEach(listener => {
      try {
        listener(null, null);
      } catch (error) {
        console.error('Auth Service: Error in signOut listener:', error);
      }
    });
    
    console.log('🔐 Auth Service: Sign out completed successfully');
  } catch (error) {
    console.error('🔐 Auth Service: Error during sign out:', error);
    
    // Even if there's an error, clear local state and notify listeners
    console.log('🔐 Auth Service: Clearing local state despite error...');
    currentSession = null;
    currentUser = null;
    
    authStateListeners.forEach(listener => {
      try {
        listener(null, null);
      } catch (error) {
        console.error('Auth Service: Error in error handler listener:', error);
      }
    });
    
    throw error;
  }
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  return !!currentSession && !!currentUser;
};

// Get user email
export const getUserEmail = (): string | null => {
  return currentUser?.email ?? null;
}; 