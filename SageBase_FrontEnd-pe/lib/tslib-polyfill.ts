// This file provides a polyfill for the __rest helper function
// that TypeScript uses for object destructuring with rest parameters

// Only run in browser environment
if (typeof window !== "undefined") {
  // Define the __rest function globally
  ;(window as any).__rest = (s: any, e: string[]) => {
    const t: any = {}
    for (const p in s) {
      if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) {
        t[p] = s[p]
      }
    }
    return t
  }

  // Try to add it to tslib if it exists
  try {
    const tslib = require("tslib")
    if (tslib && !tslib.__rest) {
      tslib.__rest = (window as any).__rest
    }
  } catch (e) {
    // tslib not available, using global fallback
    console.warn("tslib not available, using global __rest fallback")
  }
}

// Export a function to explicitly call the polyfill
export function polyfillTslib() {
  // Function already runs on import, this is just for explicit calling if needed
  return true
}
