import { useEffect } from 'react';
import { message<PERSON>out<PERSON>, MessageHand<PERSON> } from './use-notifications';

export const useMessageHandler = (handler: MessageHandler) => {
  useEffect(() => {
    // Register the handler when the component mounts
    messageRouter.registerHandler(handler);
    
    // Unregister when the component unmounts
    return () => {
      messageRouter.unregisterHandler(handler.id);
    };
  }, [handler]);
}; 