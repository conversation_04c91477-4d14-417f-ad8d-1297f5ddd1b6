import { useState, useEffect, useCallback } from 'react';
import {
  Project,
  TeamMember,
  ProjectMetrics,
  projectsApi,
  teamMembersApi,
  contributorsApi,
  metricsApi,
  searchApi,
} from '@/services/team-knowledge-api';

interface UseTeamKnowledgeState {
  projects: Project[];
  teamMembers: TeamMember[];
  projectMetrics: ProjectMetrics[];
  loading: boolean;
  error: string | null;
}

interface UseTeamKnowledgeActions {
  refetchProjects: () => Promise<void>;
  refetchTeamMembers: () => Promise<void>;
  refetchProjectMetrics: () => Promise<void>;
  createProject: (project: Omit<Project, 'id' | 'lastActivity' | 'totalContributors' | 'topContributors'>) => Promise<Project>;
  updateProject: (projectId: string, updates: Partial<Project>) => Promise<Project>;
  deleteProject: (projectId: string) => Promise<void>;
  assignDocumentationResponsibility: (
    projectId: string,
    memberEmail: string,
    type: 'main' | 'secondary'
  ) => Promise<Project>;
  searchProjects: (query: string, filters?: {
    categories?: string[];
    contributors?: string[];
    timeRange?: string;
  }) => Promise<Project[]>;
}

export const useTeamKnowledge = (timeRange?: string): UseTeamKnowledgeState & UseTeamKnowledgeActions => {
  const [state, setState] = useState<UseTeamKnowledgeState>({
    projects: [],
    teamMembers: [],
    projectMetrics: [],
    loading: true,
    error: null,
  });

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const refetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const projects = await projectsApi.getProjects(timeRange);
      setState(prev => ({ ...prev, projects }));
    } catch (error) {
      console.error('Error fetching projects:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch projects');
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  const refetchTeamMembers = useCallback(async () => {
    try {
      setError(null);
      const teamMembers = await teamMembersApi.getTeamMembers();
      setState(prev => ({ ...prev, teamMembers }));
    } catch (error) {
      console.error('Error fetching team members:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch team members');
    }
  }, []);

  const refetchProjectMetrics = useCallback(async () => {
    try {
      setError(null);
      const projectMetrics = await metricsApi.getAllProjectMetrics();
      setState(prev => ({ ...prev, projectMetrics }));
    } catch (error) {
      console.error('Error fetching project metrics:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch project metrics');
    }
  }, []);

  const createProject = useCallback(async (project: Omit<Project, 'id' | 'lastActivity' | 'totalContributors' | 'topContributors'>) => {
    try {
      setLoading(true);
      setError(null);
      const newProject = await projectsApi.createProject(project);
      setState(prev => ({ ...prev, projects: [...prev.projects, newProject] }));
      return newProject;
    } catch (error) {
      console.error('Error creating project:', error);
      setError(error instanceof Error ? error.message : 'Failed to create project');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProject = useCallback(async (projectId: string, updates: Partial<Project>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedProject = await projectsApi.updateProject(projectId, updates);
      setState(prev => ({
        ...prev,
        projects: prev.projects.map(p => p.id === projectId ? updatedProject : p)
      }));
      return updatedProject;
    } catch (error) {
      console.error('Error updating project:', error);
      setError(error instanceof Error ? error.message : 'Failed to update project');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProject = useCallback(async (projectId: string) => {
    try {
      console.log('🎯 deleteProject called with projectId:', projectId);
      setLoading(true);
      setError(null);
      
      await projectsApi.deleteProject(projectId);
      
      console.log('✅ Project deleted from API, updating local state...');
      setState(prev => ({
        ...prev,
        projects: prev.projects.filter(p => p.id !== projectId)
      }));
      
      console.log('✅ Project deleted successfully from local state');
    } catch (error) {
      console.error('❌ Error deleting project:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete project';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const assignDocumentationResponsibility = useCallback(async (
    projectId: string,
    memberEmail: string,
    type: 'main' | 'secondary'
  ) => {
    try {
      setLoading(true);
      setError(null);
      const updatedProject = await projectsApi.assignDocumentationResponsibility(projectId, memberEmail, type);
      setState(prev => ({
        ...prev,
        projects: prev.projects.map(p => p.id === projectId ? updatedProject : p)
      }));
      return updatedProject;
    } catch (error) {
      console.error('Error assigning documentation responsibility:', error);
      setError(error instanceof Error ? error.message : 'Failed to assign documentation responsibility');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const searchProjects = useCallback(async (query: string, filters?: {
    categories?: string[];
    contributors?: string[];
    timeRange?: string;
  }) => {
    try {
      setLoading(true);
      setError(null);
      const results = await searchApi.searchProjects(query, filters);
      return results;
    } catch (error) {
      console.error('Error searching projects:', error);
      setError(error instanceof Error ? error.message : 'Failed to search projects');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch projects first (most important)
        let projects: Project[] = [];
        let teamMembers: TeamMember[] = [];
        let projectMetrics: ProjectMetrics[] = [];
        
        try {
          projects = await projectsApi.getProjects(timeRange);
        } catch (error) {
          console.error('Error fetching projects:', error);
          // Don't set error for projects failure, just log it
        }
        
        // Try to fetch team members (non-critical)
        try {
          teamMembers = await teamMembersApi.getTeamMembers();
        } catch (error) {
          console.warn('Error fetching team members:', error);
          // Continue without team members
        }
        
        // Try to fetch project metrics (non-critical)
        try {
          projectMetrics = await metricsApi.getAllProjectMetrics();
        } catch (error) {
          console.warn('Error fetching project metrics:', error);
          // Continue without metrics
        }

        setState(prev => ({
          ...prev,
          projects,
          teamMembers,
          projectMetrics,
        }));
      } catch (error) {
        console.error('Error in fetchInitialData:', error);
        // Only set error if we couldn't fetch any data at all
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [timeRange]);

  return {
    ...state,
    refetchProjects,
    refetchTeamMembers,
    refetchProjectMetrics,
    createProject,
    updateProject,
    deleteProject,
    assignDocumentationResponsibility,
    searchProjects,
  };
};