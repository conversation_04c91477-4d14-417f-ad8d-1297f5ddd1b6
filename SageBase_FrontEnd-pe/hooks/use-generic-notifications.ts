import { useState, useEffect, useCallback } from 'react';
import { notificationsAPI, GenericNotification, NotificationActionResponse } from '@/services/notifications-api';
import { useToast } from '@/hooks/use-toast';

export const useGenericNotifications = () => {
  const [notifications, setNotifications] = useState<GenericNotification[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchNotifications = useCallback(async () => {
    console.log('🚀 fetchNotifications called');
    try {
      setIsLoading(true);
      setError(null);
      console.log('📞 Calling notificationsAPI.getNotifications()...');
      const notificationsData = await notificationsAPI.getNotifications();
      setNotifications(notificationsData);
      console.log('📋 Fetched notifications:', notificationsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
      setError(errorMessage);
      console.error('❌ Error fetching notifications:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const acceptNotification = useCallback(async (notificationId: string) => {
    console.log('🚀 acceptNotification called for:', notificationId);
    try {
      setIsLoading(true);
      const response = await notificationsAPI.acceptNotification(notificationId);
      
      if (response.success) {
        // Remove the notification from the list
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
        
        toast({
          title: "Success",
          description: response.message || "Notification accepted",
        });
      } else {
        throw new Error(response.message || 'Failed to accept notification');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to accept notification';
      console.error('❌ Error accepting notification:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const ignoreNotification = useCallback(async (notificationId: string) => {
    console.log('🚀 ignoreNotification called for:', notificationId);
    try {
      setIsLoading(true);
      const response = await notificationsAPI.ignoreNotification(notificationId);
      
      if (response.success) {
        // Remove the notification from the list
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
        
        toast({
          title: "Success",
          description: response.message || "Notification ignored",
        });
      } else {
        throw new Error(response.message || 'Failed to ignore notification');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to ignore notification';
      console.error('❌ Error ignoring notification:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications,
    isLoading,
    error,
    fetchNotifications,
    acceptNotification,
    ignoreNotification,
  };
}; 