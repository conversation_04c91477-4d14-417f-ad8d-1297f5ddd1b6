import { useState, useEffect, useCallback } from "react";
import { getBackendUrl } from "@/lib/api-config";
import { useAuth } from "@/contexts/auth-context";

const BACKEND_BASE_URL = getBackendUrl();

export interface ActiveSearchPlatform {
  id: string;
  name: string;
  logo: string;
  activeColor: string;
  inactiveColor: string;
  hoverColor: string;
}

export function useActiveSearchPlatforms() {
  const [platforms, setPlatforms] = useState<ActiveSearchPlatform[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Get platform configuration for consistent styling
  const getPlatformConfig = (platformId: string) => {
    const configs: Record<string, ActiveSearchPlatform> = {
      github: {
        id: "github",
        name: "GitH<PERSON>",
        logo: "/images/platform-logos/github.svg",
        activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
      },
      confluence: {
        id: "confluence",
        name: "Confluence",
        logo: "/images/platform-logos/confluence.svg",
        activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
      },
      slack: {
        id: "slack",
        name: "Slack",
        logo: "/images/platform-logos/slack.svg",
        activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
      },
      "google-drive": {
        id: "google-drive",
        name: "Google Drive",
        logo: "/images/platform-logos/google-drive.svg",
        activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
      },
      discord: {
        id: "discord",
        name: "Discord",
        logo: "/images/platform-logos/discord.svg",
        activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
      },
      internet: {
        id: "internet",
        name: "Internet",
        logo: "/images/platform-logos/internet.svg",
        activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
      },
    };

    return configs[platformId] || {
      id: platformId,
      name: platformId.charAt(0).toUpperCase() + platformId.slice(1),
      logo: "/images/platform-logos/internet.svg",
      activeColor: "bg-gray-800 text-white",
        inactiveColor: "bg-gray-100 text-gray-600",
        hoverColor: "hover:bg-gray-200",
    };
  };

  // Fetch active search platforms from backend with retry logic
  const fetchActivePlatforms = useCallback(async (retryCount = 0) => {
    if (!user?.email) {
      setError("User email not available");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log(`🔍 Fetching active search platforms from: ${BACKEND_BASE_URL}/api/integrations/active-search-platforms/?acting_user_email=${encodeURIComponent(user.email)}`);
      
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/active-search-platforms/?acting_user_email=${encodeURIComponent(user.email)}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Active search platforms endpoint not found. Please check if the backend service is running.");
        } else if (response.status >= 500) {
          throw new Error("Backend server error. Please try again later.");
        } else {
          throw new Error(`Failed to fetch active platforms: ${response.status}`);
        }
      }

      const data = await response.json();
      
      // Transform backend data to include platform configurations
      const activePlatforms = data.platforms?.map((platformId: string) => 
        getPlatformConfig(platformId)
      ) || [];

      // Always include internet as a default option
      if (!activePlatforms.find(p => p.id === "internet")) {
        activePlatforms.push(getPlatformConfig("internet"));
      }

      setPlatforms(activePlatforms);
    } catch (err) {
      console.error("Error fetching active search platforms:", err);
      
      // Retry logic for network errors
      if (retryCount < 2 && err instanceof Error && 
          (err.message.includes("Failed to fetch") || err.message.includes("Backend server error"))) {
        console.log(`Retrying fetch (attempt ${retryCount + 1})...`);
        setTimeout(() => fetchActivePlatforms(retryCount + 1), 1000 * (retryCount + 1));
        return;
      }
      
      setError(err instanceof Error ? err.message : "Failed to fetch platforms");
      
      // Fallback to default platforms if API fails
      setPlatforms([
        getPlatformConfig("github"),
        getPlatformConfig("confluence"),
        getPlatformConfig("internet"),
      ]);
    } finally {
      setIsLoading(false);
    }
  }, [user?.email]);

  // Update active search platforms on backend
  const updateActivePlatforms = useCallback(async (platformIds: string[]) => {
    if (!user?.email) {
      setError("User email not available");
      return;
    }

    try {
      setError(null);

      console.log(`🔄 Updating active search platforms to: ${platformIds.join(', ')}`);
      console.log(`🔍 PUT request to: ${BACKEND_BASE_URL}/api/integrations/active-search-platforms/?acting_user_email=${encodeURIComponent(user.email)}`);
      
      const response = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/active-search-platforms/?acting_user_email=${encodeURIComponent(user.email)}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ platforms: platformIds }),
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Active search platforms endpoint not found. Please check if the backend service is running.");
        } else if (response.status >= 500) {
          throw new Error("Backend server error. Please try again later.");
        } else if (response.status === 400) {
          throw new Error("Invalid request. Please check the platform IDs.");
        } else {
          throw new Error(`Failed to update active platforms: ${response.status}`);
        }
      }

      // Refresh the platforms after successful update
      await fetchActivePlatforms();
    } catch (err) {
      console.error("Error updating active search platforms:", err);
      setError(err instanceof Error ? err.message : "Failed to update platforms");
    }
  }, [user?.email, fetchActivePlatforms]);

  // Fetch platforms on mount and when user changes
  useEffect(() => {
    if (user?.email) {
      fetchActivePlatforms();
    }
  }, [user?.email, fetchActivePlatforms]);

  return {
    platforms,
    isLoading,
    error,
    fetchActivePlatforms,
    updateActivePlatforms,
  };
}
