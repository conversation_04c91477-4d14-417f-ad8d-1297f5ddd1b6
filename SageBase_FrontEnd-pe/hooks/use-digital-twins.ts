"use client"

import { useState, useEffect } from "react"
import {
  digitalTwinsService,
  type DigitalTwin,
  type CreateDigitalTwinInput,
  type UpdateDigitalTwinInput,
} from "@/lib/digital-twins-service"

export function useDigitalTwins() {
  const [twins, setTwins] = useState<DigitalTwin[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [usingFallbackData, setUsingFallbackData] = useState(false)

  const fetchTwins = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await digitalTwinsService.getDigitalTwins()
      setTwins(data)

      // Check if we're using fallback data (static data)
      if (data.length > 0 && data[0].id === "1") {
        setUsingFallbackData(true)
      } else {
        setUsingFallbackData(false)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch digital twins"
      setError(errorMessage)
      console.error("Error fetching digital twins:", err)

      // Try to use fallback data even on error
      try {
        const fallbackData = await digitalTwinsService.getDigitalTwins()
        setTwins(fallbackData)
        setUsingFallbackData(true)
      } catch (fallbackError) {
        console.error("Even fallback data failed:", fallbackError)
      }
    } finally {
      setLoading(false)
    }
  }

  const createTwin = async (input: CreateDigitalTwinInput): Promise<DigitalTwin | null> => {
    try {
      setError(null)
      const newTwin = await digitalTwinsService.createDigitalTwin(input)
      setTwins((prev) => [newTwin, ...prev])
      return newTwin
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create digital twin"
      setError(errorMessage)
      return null
    }
  }

  const updateTwin = async (input: UpdateDigitalTwinInput): Promise<DigitalTwin | null> => {
    try {
      setError(null)
      const updatedTwin = await digitalTwinsService.updateDigitalTwin(input)
      setTwins((prev) => prev.map((twin) => (twin.id === input.id ? updatedTwin : twin)))
      return updatedTwin
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update digital twin"
      setError(errorMessage)
      return null
    }
  }

  const deleteTwin = async (id: string): Promise<boolean> => {
    try {
      setError(null)
      const success = await digitalTwinsService.deleteDigitalTwin(id)
      if (success) {
        setTwins((prev) => prev.filter((twin) => twin.id !== id))
      }
      return success
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete digital twin"
      setError(errorMessage)
      return false
    }
  }

  useEffect(() => {
    fetchTwins()
  }, [])

  return {
    twins,
    loading,
    error,
    usingFallbackData,
    refetch: fetchTwins,
    createTwin,
    updateTwin,
    deleteTwin,
  }
}
