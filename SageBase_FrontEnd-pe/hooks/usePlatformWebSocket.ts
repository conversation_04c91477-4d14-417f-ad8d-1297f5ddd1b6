import { useEffect } from "react";

export function usePlatformWebSocket(companyId: string | undefined, onPlatformUninstalled: (platform: string) => void) {
  useEffect(() => {
    if (!companyId) return;

    // Use wss:// in production, ws:// in development
    const wsScheme =
      typeof window !== "undefined" && window.location.protocol === "https:"
        ? "wss"
        : "ws";
    const wsHost =
      typeof window !== "undefined" ? window.location.hostname : "localhost";
    const wsPort = 8000; // adjust if needed
    const wsUrl = `${wsScheme}://${wsHost}:${wsPort}/ws/github/notifications/`;

    const socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      // Subscribe to the company group
      socket.send(JSON.stringify({ action: "subscribe", repo: `company_${companyId}_platforms` }));
      console.log(`subscribed to: company_${companyId}_platforms`);
      
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === "platform.uninstalled" && data.platform) {
            console.log(`received uninstall event for: company_${companyId}_platforms`);
          onPlatformUninstalled(data.platform);
        }
      } catch (e) {
        // Ignore parse errors
      }
    };

    return () => {
      // Unsubscribe and close socket on cleanup
      try {
        socket.send(JSON.stringify({ action: "unsubscribe", repo: `company_${companyId}_platforms` }));
      } catch {}
      socket.close();
    };
  }, [companyId]); // Removed onPlatformUninstalled from dependencies
} 