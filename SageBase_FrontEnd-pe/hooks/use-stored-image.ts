"use client"

import { useState, useEffect } from "react"
import { getImageWithMetadata, type ImageMetadata } from "@/lib/image-storage"

interface UseStoredImageResult {
  url: string | null
  metadata: Partial<ImageMetadata> | null
  isLoading: boolean
  error: Error | null
}

export function useStoredImage(fileName: string, fallbackUrl?: string): UseStoredImageResult {
  const [result, setResult] = useState<UseStoredImageResult>({
    url: fallbackUrl || null,
    metadata: null,
    isLoading: true,
    error: null,
  })

  useEffect(() => {
    let isMounted = true

    async function fetchImage() {
      try {
        const imageData = await getImageWithMetadata(fileName)

        if (!isMounted) return

        if (imageData) {
          setResult({
            url: imageData.url,
            metadata: imageData.metadata,
            isLoading: false,
            error: null,
          })
        } else {
          setResult({
            url: fallbackUrl || null,
            metadata: null,
            isLoading: false,
            error: new Error(`Image ${fileName} not found`),
          })
        }
      } catch (error) {
        if (!isMounted) return

        setResult({
          url: fallbackUrl || null,
          metadata: null,
          isLoading: false,
          error: error instanceof Error ? error : new Error("Failed to load image"),
        })
      }
    }

    fetchImage()

    return () => {
      isMounted = false
    }
  }, [fileName, fallbackUrl])

  return result
}
