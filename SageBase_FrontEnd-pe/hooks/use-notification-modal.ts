import { useState, useCallback } from 'react';

interface NotificationDetails {
  label: string;
  value: string | number;
}

interface NotificationOptions {
  title?: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  details?: NotificationDetails[];
  nextStep?: string;
  additionalInfo?: string;
  buttonText?: string;
}

export function useNotificationModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [notificationConfig, setNotificationConfig] = useState<NotificationOptions>({
    message: '',
    type: 'info',
    buttonText: 'OK'
  });

  const showNotification = useCallback((message: string, options: NotificationOptions = {}) => {
    setNotificationConfig({
      message,
      type: 'info',
      buttonText: 'OK',
      ...options
    });
    setIsOpen(true);
  }, []);

  const showInfo = useCallback((message: string, options: Omit<NotificationOptions, 'type'> = {}) => {
    showNotification(message, { ...options, type: 'info' });
  }, [showNotification]);

  const showSuccess = useCallback((message: string, options: Omit<NotificationOptions, 'type'> = {}) => {
    showNotification(message, { ...options, type: 'success' });
  }, [showNotification]);

  const showWarning = useCallback((message: string, options: Omit<NotificationOptions, 'type'> = {}) => {
    showNotification(message, { ...options, type: 'warning' });
  }, [showNotification]);

  const showError = useCallback((message: string, options: Omit<NotificationOptions, 'type'> = {}) => {
    showNotification(message, { ...options, type: 'error' });
  }, [showNotification]);

  const showProcessing = useCallback((message: string, details: NotificationDetails[], nextStep: string, additionalInfo?: string) => {
    showNotification(message, {
      type: 'info',
      details,
      nextStep,
      additionalInfo,
      buttonText: 'OK'
    });
  }, [showNotification]);

  const closeNotification = useCallback(() => {
    setIsOpen(false);
  }, []);

  return {
    isOpen,
    notificationConfig,
    showNotification,
    showInfo,
    showSuccess,
    showWarning,
    showError,
    showProcessing,
    closeNotification
  };
}
