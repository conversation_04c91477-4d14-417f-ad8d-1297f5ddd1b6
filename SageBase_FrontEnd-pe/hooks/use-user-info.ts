import { useState, useEffect, useCallback, useRef } from 'react';
import { getBackendApiUrl } from '@/lib/api-config';

interface UserInfo {
  id: string;
  email: string;
  company: string;
  company_name: string;
  role: string | null;
}

interface CacheEntry {
  data: UserInfo;
  timestamp: number;
  expiresAt: number;
}

// Cache configuration - shorter duration for role-sensitive data
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
const cache = new Map<string, CacheEntry>();

// Function to clear all cache (for debugging)
export const clearUserInfoCache = () => {
  cache.clear();
};

export const useUserInfo = (userEmail: string | undefined) => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastFetchTime = useRef<number>(0);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  
  // Check if user info fetching is disabled via environment variable
  const isUserInfoDisabled = process.env.NEXT_PUBLIC_DISABLE_USER_INFO === 'true';

  const fetchUserInfo = useCallback(async (forceRefresh = false) => {
    if (!userEmail || isDisabled || isUserInfoDisabled) {
      setUserInfo(null);
      return;
    }

    const now = Date.now();
    const cacheKey = `user_info_${userEmail}`;
    const cached = cache.get(cacheKey);

    // Return cached data if it's still valid and not forcing refresh
    if (!forceRefresh && cached && now < cached.expiresAt) {
      setUserInfo(cached.data);
      setIsLoading(false);
      return;
    }

    // Prevent multiple simultaneous requests
    if (isLoading && !forceRefresh) {
      return;
    }

    // Prevent excessive requests (minimum 5 seconds between requests)
    if (!forceRefresh && (now - lastFetchTime.current) < 5000) {
      return;
    }

        try {
      setIsLoading(true);
      setError(null);
      setHasAttemptedFetch(true);

      const backendUrl = getBackendApiUrl();
      console.log('🌐 Fetching user info from:', `${backendUrl}/api/integrations/get-user-by-email/?email=${encodeURIComponent(userEmail)}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(
        `${backendUrl}/api/integrations/get-user-by-email/?email=${encodeURIComponent(userEmail)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();

      // Cache the successful response
      cache.set(cacheKey, {
        data,
        timestamp: now,
        expiresAt: now + CACHE_DURATION,
      });
      
      setUserInfo(data);
      lastFetchTime.current = now;
    } catch (err) {
      console.error('Error fetching user info:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch user info');
      
      // After multiple failures, disable the hook to prevent repeated errors
      if (hasAttemptedFetch && !isDisabled) {
        console.warn('Disabling user info fetch due to repeated failures');
        setIsDisabled(true);
      }
      
      // Don't clear user info on error, keep the last known good state
      // setUserInfo(null);
    } finally {
      setIsLoading(false);
    }
  }, [userEmail]);

  // Fetch user info when email changes
  useEffect(() => {
    if (userEmail && !isDisabled && !isUserInfoDisabled) {
      fetchUserInfo();
    } else {
      setUserInfo(null);
    }
  }, [userEmail, fetchUserInfo, isDisabled, isUserInfoDisabled]);

  // Clear cache for specific user
  const clearUserCache = useCallback((email: string) => {
    const cacheKey = `user_info_${email}`;
    cache.delete(cacheKey);
  }, []);

  // Force refresh user info
  const refreshUserInfo = useCallback(() => {
    if (userEmail) {
      fetchUserInfo(true);
    }
  }, [userEmail, fetchUserInfo]);

  return {
    userInfo,
    isLoading,
    error,
    refreshUserInfo,
    clearUserCache,
    hasAttemptedFetch,
    isDisabled,
  };
}; 