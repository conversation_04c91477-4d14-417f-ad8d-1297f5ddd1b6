import { useState, useEffect, useCallback } from 'react';
import { spacesAPI, Space } from '@/services/spaces-api';
import { useToast } from '@/hooks/use-toast';

export const useSpaces = () => {
  const [spaces, setSpaces] = useState<Space[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchSpaces = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const spacesData = await spacesAPI.getSpaces();
      setSpaces(spacesData);
      console.log("✅ Spaces loaded successfully:", spacesData.length, "spaces");
    } catch (error) {
      console.error("❌ Error loading spaces:", error);
      setError(error instanceof Error ? error.message : "Failed to load spaces");
      toast({
        title: "Error",
        description: "Failed to load spaces. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const createSpace = useCallback(async (spaceData: Partial<Space>) => {
    try {
      const result = await spacesAPI.createSpace(spaceData);
      
      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
          variant: "success",
        });
        // Refresh the spaces list
        await fetchSpaces();
        return result.data;
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("❌ Error creating space:", error);
      toast({
        title: "Error",
        description: "Failed to create space. Please try again.",
        variant: "destructive",
      });
    }
  }, [fetchSpaces, toast]);

  const updateSpace = useCallback(async (spaceId: string, spaceData: Partial<Space>) => {
    try {
      const result = await spacesAPI.updateSpace(spaceId, spaceData);
      
      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
          variant: "success",
        });
        // Refresh the spaces list
        await fetchSpaces();
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("❌ Error updating space:", error);
      toast({
        title: "Error",
        description: "Failed to update space. Please try again.",
        variant: "destructive",
      });
    }
  }, [fetchSpaces, toast]);

  const deleteSpace = useCallback(async (spaceId: string) => {
    try {
      const result = await spacesAPI.deleteSpace(spaceId);
      
      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
          variant: "success",
        });
        // Refresh the spaces list
        await fetchSpaces();
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("❌ Error deleting space:", error);
      toast({
        title: "Error",
        description: "Failed to delete space. Please try again.",
        variant: "destructive",
      });
    }
  }, [fetchSpaces, toast]);

  // Load spaces on component mount
  useEffect(() => {
    fetchSpaces();
  }, [fetchSpaces]);

  return {
    spaces,
    isLoading,
    error,
    fetchSpaces,
    createSpace,
    updateSpace,
    deleteSpace,
  };
}; 