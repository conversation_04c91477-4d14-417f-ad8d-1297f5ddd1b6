import { useState, useEffect, useCallback, useRef } from 'react';
import { getBackendUrl } from '@/lib/api-config';

export interface Notification {
  id?: string;
  type: 'notification' | 'NEW_KNOWLEDGE_BASE' | 'new_knowledge_base' | 'qa_approval' | 'project_update' | 'team_notification';
  title: string;
  message: string;
  level: 'low' | 'medium' | 'high';
  data?: Record<string, any>;
  timestamp: string;
}

interface WebSocketMessage {
  action: 'authenticate' | 'subscribe' | 'ping';
  user_email?: string;
  target_type?: 'user' | 'repository' | 'knowledge_base' | 'qa_approvals' | 'project' | 'team';
  target_id?: string;
  subscription_type?: string;
}

// Message routing system
export interface MessageHandler {
  id: string;
  types: string[];
  handler: (message: any) => void;
  priority?: number;
}

class WebSocketMessageRouter {
  private handlers: Map<string, MessageHandler[]> = new Map();
  private static instance: WebSocketMessageRouter;

  static getInstance(): WebSocketMessageRouter {
    if (!WebSocketMessageRouter.instance) {
      WebSocketMessageRouter.instance = new WebSocketMessageRouter();
    }
    return WebSocketMessageRouter.instance;
  }

  // Register a message handler
  registerHandler(handler: MessageHandler) {
    handler.types.forEach(type => {
      if (!this.handlers.has(type)) {
        this.handlers.set(type, []);
      }
      this.handlers.get(type)!.push(handler);
      
      // Sort by priority (higher priority first)
      this.handlers.get(type)!.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    });
    

  }

  // Unregister a message handler
  unregisterHandler(handlerId: string) {
    this.handlers.forEach((handlers, type) => {
      const index = handlers.findIndex(h => h.id === handlerId);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    });
  }

  // Route a message to all registered handlers
  routeMessage(message: any) {
    const messageType = message.type;
    const handlers = this.handlers.get(messageType) || [];
    


    handlers.forEach(handler => {
      try {
        handler.handler(message);
      } catch (error) {
        console.error('❌ Error in message handler:', {
          handlerId: handler.id,
          messageType: messageType,
          error: error
        });
      }
    });
  }

  // Get all registered handlers (for debugging)
  getHandlers() {
    const result: Record<string, string[]> = {};
    this.handlers.forEach((handlers, type) => {
      result[type] = handlers.map(h => h.id);
    });
    return result;
  }
}

// Global message router instance
export const messageRouter = WebSocketMessageRouter.getInstance();

export const useNotifications = (userEmail: string) => {
  // WebSocket disable flag - set to true to disable all WebSocket functionality
  const WEBSOCKET_DISABLED = true;
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 6;

  // WebSocket URL - use environment variable or default
  const getWebSocketUrl = () => {
    const backendUrl = getBackendUrl();
    // Convert HTTP URL to WebSocket URL
    const wsUrl = backendUrl.replace('http://', 'ws://').replace('https://', 'wss://');
    return `${wsUrl}/ws/notifications/`;
  };

  // Send message to WebSocket
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (WEBSOCKET_DISABLED) {
      return;
    }
    
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const messageStr = JSON.stringify(message);
      wsRef.current.send(messageStr);
      
    } else {
      console.warn('⚠️ Cannot send message - WebSocket not open:', {
        readyState: wsRef.current?.readyState,
        userEmail: userEmail
      });
    }
  }, [userEmail]);

  // Authenticate with the WebSocket
  const authenticate = useCallback(() => {
    if (userEmail) {
      sendMessage({
        action: 'authenticate',
        user_email: userEmail
      });
    }
  }, [userEmail, sendMessage]);

  // Subscribe to user notifications
  const subscribeToUser = useCallback(() => {
    if (userEmail) {
      sendMessage({
        action: 'subscribe',
        target_type: 'user',
        target_id: userEmail
      });
    }
  }, [userEmail, sendMessage]);

  // Send ping for health check
  const sendPing = useCallback(() => {
    sendMessage({ action: 'ping' });
  }, [sendMessage]);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (WEBSOCKET_DISABLED) {
      setIsConnected(false);
      setConnectionError('WebSocket functionality is disabled');
      return;
    }
    
    try {
      const wsUrl = getWebSocketUrl();
      
      const websocket = new WebSocket(wsUrl);
      wsRef.current = websocket;

      websocket.onopen = () => {

        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;
        
        // Authenticate immediately after connection
        authenticate();
        subscribeToUser();
      };

      websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // Log ALL received WebSocket messages
          
          // Process all notification types
          if (data.type === 'notification' || data.type === 'new_knowledge_base' || 
              data.type === 'qa_approval' || data.type === 'project_update' || 
              data.type === 'team_notification') {
  
            
            const notification: Notification = {
              id: data.id || Date.now().toString(),
              ...data
            };
            
            setNotifications(prev => [notification, ...prev]);
            
            // Show browser notification if permission granted
            if (Notification.permission === 'granted') {
              new Notification(data.title, { 
                body: data.message,
                icon: '/images/robot-logo.png' // Your app icon
              });
            }
          }
          
          // Handle platform uninstall events
          if (data.type === 'platform.uninstalled') {

            
            // Create a notification for the platform uninstall
            const uninstallNotification: Notification = {
              id: `uninstall-${Date.now()}`,
              type: 'notification',
              title: `Platform Uninstalled`,
              message: data.message || `${data.platform} has been uninstalled`,
              level: 'medium',
              timestamp: new Date().toISOString(),
              data: {
                platform: data.platform,
                type: 'platform_uninstalled'
              }
            };
            
            setNotifications(prev => [uninstallNotification, ...prev]);
            
            // Show browser notification if permission granted
            if (Notification.permission === 'granted') {
              new Notification(uninstallNotification.title, { 
                body: uninstallNotification.message,
                icon: '/images/robot-logo.png'
              });
            }
          }
          
          // Route message to all registered handlers (for future use)
          messageRouter.routeMessage(data); 
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', {
            error: error,
            rawData: event.data,
            userEmail: userEmail
          });
        }
      };

      websocket.onclose = (event) => {

        setIsConnected(false);
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000);
 
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          console.error('❌ WebSocket Max Reconnect Attempts Reached:', {
            timestamp: new Date().toISOString(),
            userEmail: userEmail,
            attempts: reconnectAttempts.current
          });
          setConnectionError('Failed to reconnect after multiple attempts');
        }
      };

      websocket.onerror = (error) => {
        console.warn('⚠️ WebSocket Error (app will continue working):', {
          timestamp: new Date().toISOString(),
          error: error,
          userEmail: userEmail
        });
        setConnectionError('WebSocket connection error');
        // Don't throw error - let the app continue without WebSocket
      };

    } catch (error) {
      console.warn('⚠️ Error creating WebSocket connection (app will continue working):', {
        timestamp: new Date().toISOString(),
        error: error,
        userEmail: userEmail
      });
      setConnectionError('Failed to create WebSocket connection');
      // Don't throw error - let the app continue without WebSocket
    }
  }, [userEmail, authenticate, subscribeToUser]);

  // Start ping interval
  const startPingInterval = useCallback(() => {
    if (WEBSOCKET_DISABLED) {
      return null;
    }
    
    const pingInterval = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        sendPing();
      }
    }, 30000); // Ping every 30 seconds

    return pingInterval;
  }, [sendPing, userEmail]);

  // Stop ping interval
  const stopPingInterval = useCallback((interval: NodeJS.Timeout | null) => {
    if (WEBSOCKET_DISABLED || !interval) {
      return;
    }
    

    clearInterval(interval);
  }, [userEmail]);

  // Connect on mount and when userEmail changes
  useEffect(() => {
    if (userEmail) {
      // Try to connect to WebSocket, but don't let failures break the app
      try {
        connect();
        
        // Start ping interval
        const pingInterval = startPingInterval();
        
        return () => {
          if (wsRef.current) {
            wsRef.current.close();
          }
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }
          if (pingInterval) {
            stopPingInterval(pingInterval);
          }
        };
      } catch (error) {
        console.warn('⚠️ WebSocket connection failed, but app will continue working:', {
          error: error,
          userEmail: userEmail,
          timestamp: new Date().toISOString()
        });
        // Don't throw error - let the app continue without WebSocket
      }
    }
  }, [userEmail, connect, startPingInterval, stopPingInterval]);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Remove specific notification
  const removeNotification = useCallback((notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  }, []);

  // Reconnect manually
  const reconnect = useCallback(() => {
    if (WEBSOCKET_DISABLED) {
      return;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
    }
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  return {
    notifications,
    isConnected,
    connectionError,
    clearNotifications,
    removeNotification,
    reconnect
  };
}; 