import { useState, useEffect, useCallback, useRef } from 'react';

type PlatformConnection = {
  id: string;
  name: string;
  status: "connected" | "will be available soon" | "available";
  icon?: string;
};

interface CacheEntry {
  data: PlatformConnection[];
  timestamp: number;
  expiresAt: number;
}

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const cache = new Map<string, CacheEntry>();

export function usePlatformStatus(companyId?: string) {
  const [possibleConnections, setPossibleConnections] = useState<PlatformConnection[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Refs to track state
  const lastFetchTime = useRef<number>(0);
  const isPolling = useRef<boolean>(false);
  const pollingInterval = useRef<NodeJS.Timeout | null>(null);

  // Fetch possible connections from backend with caching
  const fetchPossibleConnections = useCallback(async (forceRefresh = false) => {
    const now = Date.now();
    const cacheKey = 'possible_connections';
    const cached = cache.get(cacheKey);

    // Return cached data if it's still valid and not forcing refresh
    if (!forceRefresh && cached && now < cached.expiresAt) {
      setPossibleConnections(cached.data);
      setIsLoading(false);
      return;
    }

    // Prevent multiple simultaneous requests
    if (isLoading && !forceRefresh) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL;

      if (!apiUrl) {
        console.error("NEXT_PUBLIC_BACKEND_API_URL is not set");
        setError("Backend API URL not configured");
        // Fallback to some basic platforms if API URL is not set
        const fallbackData: PlatformConnection[] = [
          { id: "github", name: "GitHub", status: "available" },
          { id: "confluence", name: "Confluence", status: "available" },
          { id: "teams", name: "Microsoft Teams", status: "available" },
          { id: "slack", name: "Slack", status: "available" },
        ];
        setPossibleConnections(fallbackData);
        
        // Cache the fallback data
        cache.set(cacheKey, {
          data: fallbackData,
          timestamp: now,
          expiresAt: now + CACHE_DURATION,
        });
        
        setIsLoading(false);
        return;
      }

      // Use the correct endpoint that actually exists
      try {
        if (!companyId) {
          console.log("⚠️ No companyId available, skipping API call");
          return;
        }
        
        const response = await fetch(`${apiUrl}/api/possible_connections/?company_id=${companyId}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        console.log("API Response status:", response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("✅ Successfully fetched possible connections:", data);
        
        // Cache the successful response
        cache.set(cacheKey, {
          data,
          timestamp: now,
          expiresAt: now + CACHE_DURATION,
        });
        
        setPossibleConnections(data);
        lastFetchTime.current = now;
      } catch (fetchError) {
        console.error("❌ Fetch error:", fetchError);
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          console.log("⏰ API request timed out after 3 seconds");
          setError("API request timed out - using fallback platforms");
        } else {
          throw fetchError;
        }
      }
    } catch (err) {
      console.error("Error fetching possible connections:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch connections"
      );

      // Fallback to some basic platforms if API fails
      const fallbackData: PlatformConnection[] = [
        { id: "github", name: "GitHub", status: "available" },
        { id: "confluence", name: "Confluence", status: "available" },
        { id: "teams", name: "Microsoft Teams", status: "available" },
        { id: "slack", name: "Slack", status: "available" },
      ];
      setPossibleConnections(fallbackData);
      
      // Cache the fallback data
      cache.set(cacheKey, {
        data: fallbackData,
        timestamp: now,
        expiresAt: now + CACHE_DURATION,
      });
    } finally {
      setIsLoading(false);
    }
  }, [isLoading]);

  // Start polling for connection status
  const startPolling = useCallback(() => {
    if (isPolling.current) {
      return;
    }

    isPolling.current = true;
    pollingInterval.current = setInterval(() => {
      fetchPossibleConnections(true); // Force refresh during polling
    }, 3000); // Poll every 3 seconds

    // Stop polling after 30 seconds
    setTimeout(() => {
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
        pollingInterval.current = null;
      }
      isPolling.current = false;
    }, 30000);
  }, [fetchPossibleConnections]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingInterval.current) {
      clearInterval(pollingInterval.current);
      pollingInterval.current = null;
    }
    isPolling.current = false;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }
    };
  }, []);

  return {
    possibleConnections,
    isLoading,
    error,
    fetchPossibleConnections,
    startPolling,
    stopPolling,
    isPolling: isPolling.current,
  };
}
