import { useState, useEffect, useCallback } from 'react';
import { qaHistoryAPI, QAHistoryItem } from '@/services/qa-history-api';
import { useToast } from '@/hooks/use-toast';

export const useQAHistory = () => {
  const [qaHistory, setQAHistory] = useState<QAHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchQAHistory = useCallback(async () => {
    console.log('🚀 fetchQAHistory called');
    try {
      setIsLoading(true);
      setError(null);
      console.log('📞 Calling qaHistoryAPI.getQAHistory()...');
      const history = await qaHistoryAPI.getQAHistory();
      setQAHistory(history);
      console.log('📋 Fetched QA history:', history);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch QA history';
      setError(errorMessage);
      console.error('❌ Error fetching QA history:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const storeQA = useCallback(async (qaId: string) => {
    console.log('🎯 storeQA called with qaId:', qaId);
    try {
      setIsLoading(true);
      console.log('📞 Calling qaHistoryAPI.storeQA...');
      const result = await qaHistoryAPI.storeQA(qaId);
      
      console.log('📥 storeQA result:', result);
      
      if (result.success) {
        // Update the QA status in the history
        setQAHistory(prev => prev.map(qa => 
          qa.id === qaId 
            ? { ...qa, status: 'stored' as const }
            : qa
        ));
        
        toast({
          title: "Success",
          description: "Q&A stored in knowledge base",
          variant: "default",
        });
        
        console.log('✅ Q&A stored:', qaId);
      } else {
        throw new Error(result.message || 'Failed to store Q&A');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to store Q&A';
      console.error('❌ Error storing Q&A:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const dismissQA = useCallback(async (qaId: string, reason?: string) => {
    console.log('🎯 dismissQA called with qaId:', qaId, 'reason:', reason);
    try {
      setIsLoading(true);
      console.log('📞 Calling qaHistoryAPI.dismissQA...');
      const result = await qaHistoryAPI.dismissQA(qaId, reason);
      
      console.log('📥 dismissQA result:', result);
      
      if (result.success) {
        // Update the QA status in the history
        setQAHistory(prev => prev.map(qa => 
          qa.id === qaId 
            ? { ...qa, status: 'dismissed' as const }
            : qa
        ));
        
        toast({
          title: "Q&A Dismissed",
          description: "Q&A has been dismissed",
          variant: "default",
        });
        
        console.log('❌ Q&A dismissed:', qaId, 'Reason:', reason);
      } else {
        throw new Error(result.message || 'Failed to dismiss Q&A');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to dismiss Q&A';
      console.error('❌ Error dismissing Q&A:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const restoreQA = useCallback(async (qaId: string) => {
    console.log('🎯 restoreQA called with qaId:', qaId);
    try {
      setIsLoading(true);
      console.log('📞 Calling qaHistoryAPI.restoreQA...');
      const result = await qaHistoryAPI.restoreQA(qaId);
      
      console.log('📥 restoreQA result:', result);
      
      if (result.success) {
        // Update the QA status in the history
        setQAHistory(prev => prev.map(qa => 
          qa.id === qaId 
            ? { ...qa, status: 'pending' as const }
            : qa
        ));
        
        toast({
          title: "Q&A Restored",
          description: "Q&A has been restored",
          variant: "default",
        });
        
        console.log('✅ Q&A restored:', qaId);
      } else {
        throw new Error(result.message || 'Failed to restore Q&A');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to restore Q&A';
      console.error('❌ Error restoring Q&A:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Fetch QA history on mount
  useEffect(() => {
    fetchQAHistory();
  }, [fetchQAHistory]);

  return {
    qaHistory,
    isLoading,
    error,
    fetchQAHistory,
    storeQA,
    dismissQA,
    restoreQA,
  };
}; 