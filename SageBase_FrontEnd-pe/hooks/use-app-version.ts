"use client";

import { useEffect, useState } from "react";
import { getBackendApiUrl } from "@/lib/api-config";

type AppVersion = {
  version: string;
  env: string;
  commit: string;
  build_time: string;
};

const CACHE_KEY = "app_version_cache";
const CACHE_TTL_MS = 30 * 60 * 1000; // 30 minutes

type Cached<T> = {
  data: T;
  expiresAt: number;
};

export function useAppVersion() {
  const [data, setData] = useState<AppVersion | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function load() {
      try {
        setLoading(true);
        setError(null);

        // Try cache first
        const cachedRaw = typeof window !== "undefined" ? sessionStorage.getItem(CACHE_KEY) : null;
        if (cachedRaw) {
          try {
            const cached: Cached<AppVersion> = JSON.parse(cachedRaw);
            if (cached && cached.expiresAt > Date.now()) {
              setData(cached.data);
              setLoading(false);
              return;
            }
          } catch {
            // ignore parse errors
          }
        }

        const baseUrl = getBackendApiUrl();
        const res = await fetch(`${baseUrl}/api/version`, {
          headers: { "Content-Type": "application/json" },
          cache: "no-store",
        });
        if (!res.ok) {
          const txt = await res.text().catch(() => "");
          throw new Error(`Failed to fetch version: ${res.status} ${txt}`);
        }
        const json = (await res.json()) as AppVersion;
        setData(json);

        // Save to cache
        if (typeof window !== "undefined") {
          const toCache: Cached<AppVersion> = {
            data: json,
            expiresAt: Date.now() + CACHE_TTL_MS,
          };
          sessionStorage.setItem(CACHE_KEY, JSON.stringify(toCache));
        }
      } catch (e: any) {
        setError(e?.message || "Failed to load version");
      } finally {
        setLoading(false);
      }
    }

    load();
  }, []);

  return { data, loading, error };
}

