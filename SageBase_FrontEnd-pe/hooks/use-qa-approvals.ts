import { useState, useEffect, useCallback } from 'react';
import { qaApprovalAPI, PendingQAApproval, ApproveRejectResponse, QAEditData } from '@/services/qa-approval-api';
import { useToast } from '@/hooks/use-toast';

export const useQAApprovals = () => {
  const [pendingApprovals, setPendingApprovals] = useState<PendingQAApproval[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchPendingApprovals = useCallback(async () => {
    console.log('🚀 fetchPendingApprovals called');
    try {
      setIsLoading(true);
      setError(null);
      console.log('📞 Calling qaApprovalAPI.getPendingApprovals()...');
      const approvals = await qaApprovalAPI.getPendingApprovals();
      setPendingApprovals(approvals);
      console.log('📋 Fetched pending Q&A approvals:', approvals);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch pending approvals';
      setError(errorMessage);
      console.error('❌ Error fetching pending approvals:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const approveQA = useCallback(async (qaId: string) => {
    console.log('🎯 approveQA called with qaId:', qaId);
    try {
      setIsLoading(true);
      console.log('📞 Calling qaApprovalAPI.approveQA...');
      const result = await qaApprovalAPI.approveQA(qaId);
      
      console.log('📥 approveQA result:', result);
      
      if (result.success) {
        // Remove the approved QA from the pending list
        setPendingApprovals(prev => prev.filter(qa => qa.id !== qaId));
        
        toast({
          title: "Success",
          description: "Q&A approved and added to knowledge base",
          variant: "default",
        });
        
        console.log('✅ Q&A approved:', qaId);
      } else {
        throw new Error(result.message || 'Failed to approve Q&A');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to approve Q&A';
      console.error('❌ Error approving Q&A:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const rejectQA = useCallback(async (qaId: string, reason: string) => {
    console.log('🎯 rejectQA called with qaId:', qaId, 'reason:', reason);
    try {
      setIsLoading(true);
      console.log('📞 Calling qaApprovalAPI.rejectQA...');
      const result = await qaApprovalAPI.rejectQA(qaId, reason);
      
      console.log('📥 rejectQA result:', result);
      
      if (result.success) {
        // Remove the rejected QA from the pending list
        setPendingApprovals(prev => prev.filter(qa => qa.id !== qaId));
        
        toast({
          title: "Q&A Rejected",
          description: "Q&A has been rejected and removed",
          variant: "default",
        });
        
        console.log('❌ Q&A rejected:', qaId, 'Reason:', reason);
      } else {
        throw new Error(result.message || 'Failed to reject Q&A');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reject Q&A';
      console.error('❌ Error rejecting Q&A:', err);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const editQA = useCallback(async (qaId: string, projectId: string, editData: QAEditData) => {
    console.log('🎯 editQA called with qaId:', qaId, 'projectId:', projectId, 'editData:', editData);
    try {
      setIsLoading(true);
      console.log('📞 Calling qaApprovalAPI.editQA...');
      const result = await qaApprovalAPI.editQA(qaId, projectId, editData);
      console.log('✅ editQA result:', result);

      // Update the pending approvals list with the edited data
      setPendingApprovals(prev => 
        prev.map(qa => {
          if (qa.id === qaId) {
            return {
              ...qa,
              question: {
                ...qa.question,
                title: editData.question_title || qa.question.title,
                content: editData.question_content || qa.question.content,
                tags: editData.question_tags || qa.question.tags,
              },
              answer: {
                ...qa.answer,
                content: editData.answer_content || qa.answer.content,
                code: editData.answer_code || qa.answer.code,
                explanation: editData.answer_explanation || qa.answer.explanation,
              },
            };
          }
          return qa;
        })
      );

      toast({
        title: "Q&A Updated",
        description: "The Q&A has been successfully updated.",
        variant: "default",
      });
    } catch (error) {
      console.error('❌ Error in editQA:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update the Q&A. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Fetch pending approvals on mount
  useEffect(() => {
    fetchPendingApprovals();
  }, [fetchPendingApprovals]);

  return {
    pendingApprovals,
    isLoading,
    error,
    fetchPendingApprovals,
    approveQA,
    rejectQA,
    editQA,
  };
}; 