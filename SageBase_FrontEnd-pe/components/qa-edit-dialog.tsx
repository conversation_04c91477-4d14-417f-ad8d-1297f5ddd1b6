"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { X, Edit, Save, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PendingQAApproval, QAEditData } from "@/services/qa-approval-api";

interface QAEditDialogProps {
  qa: PendingQAApproval | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (
    qaId: string,
    projectId: string,
    editData: QAEditData
  ) => Promise<void>;
  isLoading?: boolean;
}

export default function QAEditDialog({
  qa,
  isOpen,
  onOpenChange,
  onSave,
  isLoading = false,
}: QAEditDialogProps) {
  const [editData, setEditData] = useState<QAEditData>({});
  const [tags, setTags] = useState<string>("");

  // Initialize edit data when QA changes
  useEffect(() => {
    if (qa) {
      setEditData({
        question_title: qa.question.title,
        question_content: qa.question.content,
        question_tags: qa.question.tags,
        answer_content: qa.answer.content,
        answer_code: qa.answer.code || "",
        answer_explanation: qa.answer.explanation || "",
      });
      setTags(qa.question.tags.join(", "));
    }
  }, [qa]);

  const handleSave = async () => {
    if (!qa) return;

    const finalEditData: QAEditData = {
      ...editData,
      question_tags: tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag),
      // Ensure optional fields are never null
      answer_code: editData.answer_code || "",
      answer_explanation: editData.answer_explanation || "",
    };

    await onSave(qa.id, qa.projectId, finalEditData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (!qa) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Q&A Before Approval
          </DialogTitle>
          <DialogDescription>
            Review and edit the question and answer content before approving
            this Q&A.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Question Section */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="question-title" className="text-sm font-medium">
                Question Title
              </Label>
              <Input
                id="question-title"
                value={editData.question_title || ""}
                onChange={(e) =>
                  setEditData((prev) => ({
                    ...prev,
                    question_title: e.target.value,
                  }))
                }
                placeholder="Enter question title..."
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="question-content" className="text-sm font-medium">
                Question Content
              </Label>
              <Textarea
                id="question-content"
                value={editData.question_content || ""}
                onChange={(e) =>
                  setEditData((prev) => ({
                    ...prev,
                    question_content: e.target.value,
                  }))
                }
                placeholder="Enter question content..."
                className="mt-1 min-h-[100px]"
              />
            </div>

            <div>
              <Label htmlFor="question-tags" className="text-sm font-medium">
                Tags (comma-separated)
              </Label>
              <Input
                id="question-tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="Enter tags separated by commas..."
                className="mt-1"
              />
              <div className="mt-2 flex flex-wrap gap-1">
                {tags.split(",").map((tag, index) => {
                  const trimmedTag = tag.trim();
                  return trimmedTag ? (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {trimmedTag}
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          </div>

          {/* Answer Section */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="answer-content" className="text-sm font-medium">
                Answer Content
              </Label>
              <Textarea
                id="answer-content"
                value={editData.answer_content || ""}
                onChange={(e) =>
                  setEditData((prev) => ({
                    ...prev,
                    answer_content: e.target.value,
                  }))
                }
                placeholder="Enter answer content..."
                className="mt-1 min-h-[120px]"
              />
            </div>

            <div>
              <Label htmlFor="answer-code" className="text-sm font-medium">
                Code (optional)
              </Label>
              <Textarea
                id="answer-code"
                value={editData.answer_code || ""}
                onChange={(e) =>
                  setEditData((prev) => ({
                    ...prev,
                    answer_code: e.target.value,
                  }))
                }
                placeholder="Enter code snippet..."
                className="mt-1 min-h-[100px] font-mono text-sm"
              />
            </div>

            <div>
              <Label
                htmlFor="answer-explanation"
                className="text-sm font-medium"
              >
                Explanation (optional)
              </Label>
              <Textarea
                id="answer-explanation"
                value={editData.answer_explanation || ""}
                onChange={(e) =>
                  setEditData((prev) => ({
                    ...prev,
                    answer_explanation: e.target.value,
                  }))
                }
                placeholder="Enter explanation..."
                className="mt-1 min-h-[80px]"
              />
            </div>
          </div>

          {/* Original QA Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Original Information
            </h4>
            <div className="text-xs text-gray-600 space-y-1">
              <p>
                <strong>Source:</strong> {qa.source}
              </p>
              <p>
                <strong>Channel:</strong> {qa.channel}
              </p>
              <p>
                <strong>Question Author:</strong> {qa.question.author.name}
              </p>
              <p>
                <strong>Answer Author:</strong> {qa.answer.author.name}
              </p>
              <p>
                <strong>Created:</strong>{" "}
                {new Date(qa.created_at).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save & Continue
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
