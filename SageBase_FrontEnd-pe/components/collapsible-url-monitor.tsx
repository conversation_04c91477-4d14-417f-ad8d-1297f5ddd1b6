"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { getBackendUrl } from '@/lib/api-config';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  ChevronDown,
  ChevronRight,
  PlusCircle,
  Globe,
  Eye,
  EyeOff,
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  Github,
  GitBranch,
  RefreshCw,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog as Mo<PERSON>,
  DialogContent as <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/auth-context";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { messageRouter } from "@/hooks/use-notifications";

type MonitoredItem = {
  id: string;
  type: "url" | "github_repo" | "github_cross_repo_monitor";
  url: string;
  name: string;
  status: "active" | "paused" | "error";
  lastChecked?: Date;
  changes?: number;
  addedDate: Date;
  notifications?: any[]; // for github_repo
  repo?: string; // for github_repo
  hookId?: number; // for github_repo
  internal_repo?: string; // for cross-repo monitor
  external_repos?: string[]; // for cross-repo monitor
};

const BACKEND_BASE_URL =
  getBackendUrl();

// Polling frequency constants (in milliseconds)
const FREQUENCY_POLLING_REPOS = 3600000; // 1 hour - for approved repo changes
const FREQUENCY_POLLING_COUNTS = 3600000; // 1 hour - for repo change counts

// --- Repo Change Monitoring ---
interface RepoChange {
  id: string;
  internal_repo: string;
  external_repo: string;
  commit_id: string;
  previous_commit_id?: string;
  commit_url?: string;
  diff?: any;
  summary: string;
  details: any;
  code_diffs?: any;
  llm_suggested_docs?: any;
  status: string;
  created_at: string;
  updated_at: string;
}

function parseLLMSuggestedDocs(raw: any): any[] {
  if (!raw) return [];
  let str = raw;
  if (typeof str !== "string") return [];
  str = str.trim();
  if (str.startsWith("```")) {
    str = str.replace(/^```[a-zA-Z]*\s*/, "").replace(/```$/, "").trim();
  }
  if (str.toLowerCase().startsWith("json")) {
    str = str.slice(4).trim();
  }
  try {
    const parsed = JSON.parse(str);
    if (Array.isArray(parsed)) return parsed;
    return [];
  } catch {
    return [];
  }
}

export default function CollapsibleURLMonitor() {
  const { connectedUserCompany, user } = useAuth();
  const companyId = connectedUserCompany?.company_id;
  const userEmail = user?.email;
  const { platforms: connectedPlatforms } = useConnectedPlatforms();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newURL, setNewURL] = useState("");
  const [newURLName, setNewURLName] = useState("");
  const { toast } = useToast();
  const [monitoredItems, setMonitoredItems] = useState<MonitoredItem[]>([]);
  const [loading, setLoading] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);
  const [addTypeModalOpen, setAddTypeModalOpen] = useState(false);
  const [addType, setAddType] = useState<'single' | 'cross' | null>(null);
  const [repoChanges, setRepoChanges] = useState<RepoChange[]>([]);
  const [repoChangesLoading, setRepoChangesLoading] = useState(false);
  // Add state for modal repo changes
  const [modalRepoChanges, setModalRepoChanges] = useState<any[]>([]);
  const [modalRepoLoading, setModalRepoLoading] = useState(false);
  const [repoChangeCounts, setRepoChangeCounts] = useState<{[internalRepo: string]: number}>({});
  const [modalInternalRepo, setModalInternalRepo] = useState<string>("");
  const [externalRepos, setExternalRepos] = useState<string[]>([]);
  const [externalRepoInput, setExternalRepoInput] = useState<string>("");

  // Define fetchItems function outside useEffect so it can be called from event handlers
  const fetchItems = useCallback(async () => {
    if (!companyId) return;
    setLoading(true);
    try {
      const res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-monitored-items/?company_id=${companyId}`
      );
      if (!res.ok) throw new Error("Failed to fetch monitored items");
      const data = await res.json();
      
      // Also fetch cross-repo monitors
      const crossRepoRes = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-cross-repo-monitors/?company_id=${companyId}`
      );
      let crossRepoData = [];
      if (crossRepoRes.ok) {
        crossRepoData = await crossRepoRes.json();
      }
      
      // Combine regular monitored items with cross-repo monitors
      const combinedItems = [
        ...data,
        ...crossRepoData.map((crossRepo: any) => ({
          id: crossRepo.id,
          type: "github_cross_repo_monitor" as const,
          url: `https://github.com/${crossRepo.internal_repo}`,
          name: `Cross-Repo: ${crossRepo.internal_repo}`,
          status: "active" as const,
          addedDate: crossRepo.created_at,
          internal_repo: crossRepo.internal_repo,
          external_repos: crossRepo.external_repos,
        }))
      ];
      
      setMonitoredItems(combinedItems);
    } catch (e) {
      setMonitoredItems([]);
    } finally {
      setLoading(false);
    }
  }, [companyId]);

  // Initial data fetch
  useEffect(() => {
    fetchItems();
  }, [fetchItems]);

  // Handle platform uninstall events to refresh data
  useEffect(() => {
    const handlePlatformUninstall = (message: any) => {
      if (message.type === 'platform.uninstalled' && message.platform === 'github') {
        console.log('🔄 GitHub platform uninstalled, refreshing monitored items...');
        // Refresh the monitored items data
        fetchItems();
      }
    };

    // Register the handler
    const handlerId = 'collapsible-url-monitor-platform-uninstall';
    messageRouter.registerHandler({
      id: handlerId,
      types: ['platform.uninstalled'],
      handler: handlePlatformUninstall,
      priority: 1
    });

    // Cleanup on unmount
    return () => {
      messageRouter.unregisterHandler(handlerId);
    };
  }, [companyId, fetchItems]);

  // Poll for approved repo changes
  useEffect(() => {
    if (!companyId) return;
    let interval: any;
    const fetchRepoChanges = async () => {
      setRepoChangesLoading(true);
      console.log('[RepoChangePolling] Fetching approved repo changes...');
      try {
        const res = await fetch(`${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/?status=approved&company_id=${companyId}`);
        if (res.ok) {
          const data = await res.json();
          console.log('[RepoChangePolling] Approved repo changes received:', data);
          setRepoChanges(data);
        } else {
          console.log('[RepoChangePolling] Error: Non-OK response', res.status);
          setRepoChanges([]);
        }
      } catch (err) {
        console.log('[RepoChangePolling] Error:', err);
        setRepoChanges([]);
      } finally {
        setRepoChangesLoading(false);
      }
    };
    fetchRepoChanges();
    // interval for polling repo-changes
    interval = setInterval(fetchRepoChanges, FREQUENCY_POLLING_REPOS);
    return () => clearInterval(interval);
  }, [companyId]);

  const fetchCounts = useCallback(async () => {
    if (!companyId) return;
    const counts: {[internalRepo: string]: number} = {};
    const crossRepos = monitoredItems.filter(i => i.type === 'github_cross_repo_monitor');
    await Promise.all(crossRepos.map(async (item) => {
      if (!item.internal_repo) return;
      const res = await fetch(`${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/?status=approved&company_id=${companyId}&internal_repo=${encodeURIComponent(item.internal_repo)}`);
      if (res.ok) {
        const data = await res.json();
        counts[item.internal_repo] = data.length;
      } else {
        counts[item.internal_repo] = 0;
      }
    }));
    setRepoChangeCounts(counts);
  }, [companyId, monitoredItems]);

  useEffect(() => {
    fetchCounts();
    const interval = setInterval(fetchCounts, FREQUENCY_POLLING_COUNTS);
    return () => clearInterval(interval);
  }, [fetchCounts]);

  // Function to fetch approved repo changes for a given internal_repo
  async function fetchApprovedRepoChanges(internalRepo: string) {
    if (!companyId || !internalRepo) return;
    setModalRepoLoading(true);
    console.log('[RepoChangeModal] Fetching approved repo changes for:', internalRepo);
    try {
      const res = await fetch(`${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/?status=approved&company_id=${companyId}&internal_repo=${encodeURIComponent(internalRepo)}`);
      if (res.ok) {
        setModalRepoChanges(await res.json());
      } else {
        setModalRepoChanges([]);
      }
    } catch {
      setModalRepoChanges([]);
    } finally {
      setModalRepoLoading(false);
    }
  }

  // Helper to get GitHub token and installation_id
  const getGitHubPlatform = () => connectedPlatforms.find((p) => p.id === "github" && p.connected);
  const getGitHubToken = (): string | null => getGitHubPlatform()?.config?.ghu_token || null;
  const getGitHubInstallationId = (): string | null => getGitHubPlatform()?.config?.installation_id || null;

  const githubPlatform = connectedPlatforms.find((p) => p.id === "github" && p.connected);
  const [githubRepos, setGithubRepos] = useState<{ id: string; full_name: string; name: string }[]>([]);

  // Fetch GitHub repos from backend DB/config
  useEffect(() => {
    const fetchRepos = async () => {
      if (!companyId || !githubPlatform) {
        setGithubRepos([]);
        return;
      }
      try {
        const res = await fetch(`${BACKEND_BASE_URL}/api/integrations/github-company-repos/?company_id=${companyId}`);
        if (res.ok) {
          const data = await res.json();
          setGithubRepos(Array.isArray(data) ? data : []);
        } else {
          setGithubRepos([]);
        }
      } catch {
        setGithubRepos([]);
      }
    };
    fetchRepos();
  }, [companyId, githubPlatform]);
  const monitoredRepoFullNames = monitoredItems.filter(i => i.type === 'github_repo').map(i => i.repo);
  const availableRepos = githubRepos.filter(r => !monitoredRepoFullNames.includes(r.full_name));
  const [selectedRepoFullName, setSelectedRepoFullName] = useState<string>("");

  // Add URL or GitHub repo
  const handleAddURL = async () => {
    if (!companyId || !userEmail) {
      console.log("[DEBUG] Missing companyId or userEmail", { companyId, userEmail });
      return;
    }
    // If GitHub is connected and a repo is selected, add as monitored item
    if (githubPlatform && selectedRepoFullName) {
      const repo = githubRepos.find(r => r.full_name === selectedRepoFullName);
      if (!repo) {
        console.log("[DEBUG] Selected repo not found", { selectedRepoFullName, githubRepos });
        return;
      }
      try {
        const monitoredPayload = {
          type: "github_repo",
          name: repo.full_name,
          url: `https://github.com/${repo.full_name}`,
          repo: repo.full_name.replaceAll("/", "_"),
          company_id: companyId,
          acting_user_email: userEmail,
        };
        console.log("[DEBUG] Sending monitored item payload to backend:", monitoredPayload);
        const res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/company-monitored-items/`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(monitoredPayload),
          }
        );
        console.log("[DEBUG] Monitored item creation response status:", res.status);
        let responseBody = null;
        try { responseBody = await res.clone().json(); } catch { responseBody = await res.text(); }
        console.log("[DEBUG] Monitored item creation response body:", responseBody);
        if (!res.ok) {
          toast({
            title: "Error",
            description: (responseBody && responseBody.error) || "Failed to add repo",
            variant: "destructive",
          });
          return;
        }
        setMonitoredItems((prev) => [...prev, responseBody]);
        setSelectedRepoFullName("");
        setShowAddDialog(false);
        toast({
          title: "GitHub Repo Added",
          description: `Now monitoring ${repo.full_name}`,
        });
        return;
      } catch (e: any) {
        console.log("[DEBUG] Exception during monitored item creation:", e);
        toast({
          title: "Error",
          description: e.message,
          variant: "destructive",
        });
        return;
      }
    }
    // Otherwise, treat as generic URL
    if (!newURL.trim()) {
      toast({
        title: "Error",
        description: "Please enter a valid URL",
        variant: "destructive",
      });
      return;
    }
    try {
      new URL(newURL);
    } catch {
      toast({
        title: "Error",
        description: "Please enter a valid URL",
        variant: "destructive",
      });
      return;
    }
    try {
      const res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-monitored-items/`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "url",
            name: newURLName.trim() || new URL(newURL).hostname,
            url: newURL,
            company_id: companyId,
            acting_user_email: userEmail,
          }),
        }
      );
      if (!res.ok) {
        const err = await res.json();
        toast({
          title: "Error",
          description: err.error || "Failed to add URL",
          variant: "destructive",
        });
        return;
      }
      const newItem = await res.json();
      setMonitoredItems((prev) => [...prev, newItem]);
      setNewURL("");
      setNewURLName("");
      setShowAddDialog(false);
      toast({
        title: "URL Added",
        description: `Now monitoring ${newItem.name}`,
      });
    } catch (e: any) {
      toast({ title: "Error", description: e.message, variant: "destructive" });
    }
  };

  // Remove monitored item
  const handleRemoveItem = async (id: string, type?: string) => {
    if (!companyId || !userEmail) return;
    try {
      let res;
      if (type === "github_cross_repo_monitor") {
        // Use cross-repo monitor endpoint
        res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/company-cross-repo-monitors/?id=${id}`,
          {
            method: "DELETE",
          }
        );
      } else {
        // Use regular monitored items endpoint
        res = await fetch(
        `${BACKEND_BASE_URL}/api/integrations/company-monitored-items/${id}/?company_id=${companyId}&acting_user_email=${userEmail}`,
        {
          method: "DELETE",
        }
      );
      }
      if (!res.ok && res.status !== 204) {
        const err = await res.json();
        toast({
          title: "Error",
          description: err.error || "Failed to remove item",
          variant: "destructive",
        });
        return;
      }
      setMonitoredItems((prev) => prev.filter((i) => i.id !== id));
      toast({ title: "Removed", description: `Stopped monitoring item` });
    } catch (e: any) {
      toast({ title: "Error", description: e.message, variant: "destructive" });
    }
  };

  const handleToggleStatus = (id: string) => {
    setMonitoredItems(
      monitoredItems.map((item) =>
        item.id === id
          ? {
              ...item,
              status: item.status === "active" ? "paused" : "active",
            }
          : item
      )
    );
  };

  const getStatusIcon = (status: MonitoredItem["status"]) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case "paused":
        return <Clock className="h-3 w-3 text-yellow-500" />;
      case "error":
        return <AlertCircle className="h-3 w-3 text-red-500" />;
    }
  };

  const getStatusColor = (status: MonitoredItem["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-700";
      case "paused":
        return "bg-yellow-100 text-yellow-700";
      case "error":
        return "bg-red-100 text-red-700";
    }
  };

  const formatLastChecked = (date?: Date) => {
    if (!date) return "Never";
    const now = new Date();
    const diff = now.getTime() - new Date(date).getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  const [modalOpen, setModalOpen] = useState(false);
  const [modalChanges, setModalChanges] = useState<any[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);
  const [pendingDeleteType, setPendingDeleteType] = useState<string | null>(null);

  const handleDeleteClick = (id: string, type?: string) => {
    setPendingDeleteId(id);
    setPendingDeleteType(type || null);
    setDeleteConfirmOpen(true);
  };

  const [internalRepo, setInternalRepo] = useState<string>("");

  return (
    <div className="border-b border-gray-200 pb-2">
      <div className="flex items-center justify-between text-xs font-medium text-gray-500 mb-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center hover:text-gray-700 transition-colors"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 mr-1" />
                ) : (
                  <ChevronRight className="h-4 w-4 mr-1" />
                )}
                <Globe className="h-4 w-4 mr-1" />
                URL MONITOR
              </button>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 shadow-lg">
              <div className="flex items-start space-x-2">
                <div className="flex-shrink-0 mt-0.5">
                  <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                    <Globe className="h-3 w-3 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-1 mb-1">
                    <span className="text-xs font-semibold text-blue-700 uppercase tracking-wide">
                      URL Monitor
                    </span>
                  </div>
                  <p className="text-xs text-gray-700 leading-relaxed">
                    Subscribe to any public git repo, and you will get alerted when your code is using deprecated functions or when the signature is different.
                  </p>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <div className="flex items-center space-x-1">
          <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
            {monitoredItems.length}
          </Badge>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5"
            onClick={() => fetchItems()}
            title="Refresh monitored items"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
          {user?.role === "ADMIN" && (
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5"
              onClick={() => setAddTypeModalOpen(true)}
              title="Add URL to monitor"
            >
              <PlusCircle className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      {isExpanded && (
        <div className="space-y-2">
          {monitoredItems.length === 0 ? (
            <div className="text-center py-4">
              <Globe className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-xs text-gray-500 mb-2">
                No URLs being monitored
              </p>
              {user?.role === "ADMIN" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAddTypeModalOpen(true)}
                  className="text-xs"
                >
                  <PlusCircle className="h-3 w-3 mr-1" />
                  Add URL
                </Button>
              )}
            </div>
          ) : (
            monitoredItems.map((item) => {
              // Only show engineer changes badge if there are actionable changes in notifications
              let engineerChangeNotifications = (
                item.notifications || []
              ).filter(
                (n) =>
                  Array.isArray(n.engineer_changes) &&
                  n.engineer_changes.length > 0
              );
              
              let engineerChangeCount = 0;
              if (item.type === "github_cross_repo_monitor") {
                engineerChangeCount = repoChangeCounts[item.internal_repo ?? ""] || 0;
              }else {
                engineerChangeCount = engineerChangeNotifications.reduce(
                (acc, n) => acc + (n.engineer_changes?.length || 0),
                0
              );
              }
              
              return (
                <div
                  key={item.id}
                  className={`rounded-lg p-2 border transition-colors ${
                    item.type === "github_cross_repo_monitor"
                      ? "bg-purple-50 border-purple-200 hover:bg-purple-100"
                      : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                  }`}
                >
                  <div className="flex items-start justify-between mb-1">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-1 mb-1">
                        {getStatusIcon(item.status)}
                        {item.type === "github_repo" && (
                          <Github className="h-3 w-3 text-gray-700" />
                        )}
                        {item.type === "github_cross_repo_monitor" && (
                          <GitBranch className="h-3 w-3 text-purple-700" />
                        )}
                        <span className={`text-xs font-medium truncate ${
                          item.type === "github_cross_repo_monitor" ? "text-purple-700" : "text-gray-700"
                        }`}>
                          {item.name}
                        </span>
                      </div>
                      <p
                        className="text-xs text-gray-500 truncate"
                        title={item.url}
                      >
                        {item.url}
                      </p>
                      {item.type === "github_cross_repo_monitor" && item.external_repos && (
                        <p className="text-xs text-purple-600 mt-1">
                          Monitoring: {item.external_repos.slice(0, 2).join(", ")}
                          {item.external_repos.length > 2 && ` +${item.external_repos.length - 2} more`}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 ml-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        onClick={() => handleToggleStatus(item.id)}
                        title={
                          item.status === "active"
                            ? "Pause monitoring"
                            : "Resume monitoring"
                        }
                      >
                        {item.status === "active" ? (
                          <EyeOff className="h-3 w-3" />
                        ) : (
                          <Eye className="h-3 w-3" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 text-red-500 hover:text-red-700"
                        onClick={() => handleDeleteClick(item.id, item.type)}
                        title="Remove URL"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <Badge
                      variant="secondary"
                      className={`text-xs px-1.5 py-0.5 ${
                        item.type === "github_cross_repo_monitor"
                          ? "bg-purple-100 text-purple-700"
                          : getStatusColor(item.status)
                      }`}
                    >
                      {item.type === "github_cross_repo_monitor" ? "cross-repo" : item.status}
                    </Badge>
                    <div className="flex items-center space-x-2 text-gray-500">
                      {/* Blue badge for engineer changes, only if there are any */}
                      {engineerChangeCount > 0 && (
                        <span
                          className="bg-blue-100 text-blue-700 rounded px-2 py-0.5 cursor-pointer font-medium"
                          onClick={() => {
                            if (item.internal_repo) {
                              setModalInternalRepo(item.internal_repo);
                              fetchApprovedRepoChanges(item.internal_repo);
                              setModalOpen(true);
                            }
                          }}
                        >
                          {engineerChangeCount} changes
                        </span>
                      )}
                      <span>{formatLastChecked(item.lastChecked)}</span>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      )}
      {/* Add Type Selection Modal */}
      <Dialog open={addTypeModalOpen} onOpenChange={setAddTypeModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Monitor</DialogTitle>
            <DialogDescription>
              Would you like to add a cross-repo monitor (track changes in external repos that affect your internal repo) or a single repo/URL monitor?
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 mt-4">
            <Button onClick={() => { setAddType('cross'); setAddTypeModalOpen(false); setShowAddDialog(true); }}>Add Cross-Repo Monitor</Button>
            <Button onClick={() => { setAddType('single'); setAddTypeModalOpen(false); setShowAddDialog(true); }}>Add Single Repo/URL Monitor</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* Main Add Modal (conditionally renders cross or single) */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{addType === 'cross' ? 'Add Cross-Repo Monitor' : 'Add GitHub Repo or URL to Monitor'}</DialogTitle>
            <DialogDescription>
              {addType === 'cross'
                ? 'Select the internal repository (your main repo) and one or more external repositories to monitor for changes that affect your codebase.'
                : githubPlatform && availableRepos.length > 0
                  ? 'Select a GitHub repo to monitor for changes.'
                  : 'Add a URL to monitor for changes.'}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {/* Cross-Repo Monitor UI or error */}
            {addType === 'cross' && (!githubPlatform || githubRepos.length === 0) && (
              <div className="text-sm text-red-500">
                { !githubPlatform
                  ? 'GitHub is not connected. Please connect your GitHub App to enable cross-repo monitoring.'
                  : 'No GitHub repositories found. Please ensure your GitHub App is installed and repositories are selected.'
                }
              </div>
            )}
            {addType === 'cross' && githubPlatform && githubRepos.length > 0 && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Internal Repository (affected repo)
                  </label>
                  <select
                    className="w-full border rounded px-2 py-2 mb-4"
                    value={internalRepo}
                    onChange={e => {
                      setInternalRepo(e.target.value);
                      setExternalRepos(externalRepos.filter(r => r !== e.target.value));
                    }}
                  >
                    <option value="">Select internal repository...</option>
                    {githubRepos.map(repo => (
                      <option key={repo.id} value={repo.full_name}>{repo.full_name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    External Repositories (monitored repos)
                  </label>
                  <div className="space-y-1 max-h-40 overflow-y-auto border rounded p-2">
                    <div className="flex gap-2">
                      <Input
                        type="text"
                        placeholder="Enter external GitHub repo URL"
                        value={externalRepoInput}
                        onChange={e => setExternalRepoInput(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            const newRepo = externalRepoInput.trim();
                            if (newRepo && !externalRepos.includes(newRepo)) {
                              setExternalRepos([...externalRepos, newRepo]);
                              setExternalRepoInput('');
                            }
                          }
                        }}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => {
                          const newRepo = externalRepoInput.trim();
                          if (newRepo && !externalRepos.includes(newRepo)) {
                            setExternalRepos([...externalRepos, newRepo]);
                            setExternalRepoInput('');
                          }
                        }}
                        disabled={!externalRepoInput.trim()}
                      >
                        Add
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {externalRepos.map((repo, index) => (
                        <Badge key={index} variant="secondary" className="text-xs px-2 py-1">
                          {repo}
                          <button
                            type="button"
                            onClick={() => setExternalRepos(externalRepos.filter((_, i) => i !== index))}
                            className="ml-1 text-gray-500 hover:text-gray-700"
                            title="Remove"
                          >
                            &times;
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Example: <span className="font-mono">https://github.com/org/repo1</span>
                    </div>
                  </div>
                </div>
              </>
            )}
            {/* Single Repo Monitor UI or error */}
            {addType === 'single' && (!githubPlatform || (githubPlatform && availableRepos.length === 0)) && (
              <div className="text-sm text-red-500">
                { !githubPlatform
                  ? 'GitHub is not connected. Please connect your GitHub App to enable repo monitoring.'
                  : 'No available GitHub repositories to monitor. All repos may already be monitored or none are available.'
                }
              </div>
            )}
            {addType === 'single' && githubPlatform && availableRepos.length > 0 && (
              <div>
                <label htmlFor="repo" className="block text-sm font-medium text-gray-700 mb-1">
                  GitHub Repository
                </label>
                <select
                  id="repo"
                  className="w-full border rounded px-2 py-2"
                  value={selectedRepoFullName}
                  onChange={e => setSelectedRepoFullName(e.target.value)}
                >
                  <option value="">Select a repository...</option>
                  {availableRepos.map(repo => (
                    <option key={repo.id} value={repo.full_name}>{repo.full_name}</option>
                  ))}
                </select>
              </div>
            )}
            {addType === 'single' && !githubPlatform && (
              <div>
                <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
                  URL
              </label>
              <Input
                  id="url"
                  type="url"
                  placeholder="https://example.com"
                  value={newURL}
                  onChange={(e) => setNewURL(e.target.value)}
              />
            </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowAddDialog(false);
              setExternalRepoInput("");
              setExternalRepos([]);
              setInternalRepo("");
            }}>
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (addType === 'cross' && githubPlatform && githubRepos.length > 0) {
                  // Check if there's content in the input field
                  if (externalRepoInput.trim() && !externalRepos.includes(externalRepoInput.trim())) {
                    const shouldAdd = confirm(`You have "${externalRepoInput.trim()}" in the input field. Would you like to add it to the external repos list before continuing?`);
                    if (shouldAdd) {
                      setExternalRepos([...externalRepos, externalRepoInput.trim()]);
                      setExternalRepoInput('');
                    }
                  }
                  
                  if (!internalRepo || externalRepos.length === 0) {
                    toast({ title: "Error", description: "Select internal and at least one external repo", variant: "destructive" });
                    return;
                  }
                  // Convert externalRepos to repo full names if URLs are provided
                  const parsedExternalRepos = externalRepos.map(url => {
                    // Accept either full_name or URL
                    try {
                      const u = new URL(url);
                      // Expect format: https://github.com/org/repo
                      const parts = u.pathname.split("/").filter(Boolean);
                      if (u.hostname === "github.com" && parts.length === 2) {
                        return `${parts[0]}/${parts[1]}`;
                      }
                      return url; // fallback to raw input
                    } catch {
                      return url; // fallback to raw input
                    }
                  });
                  const payload = {
                    type: "github_cross_repo_monitor",
                    internal_repo: internalRepo,
                    external_repos: parsedExternalRepos,
                    company_id: companyId,
                    acting_user_email: userEmail,
                  };
                  try {
                    const res = await fetch(
                      `${BACKEND_BASE_URL}/api/integrations/company-cross-repo-monitors/`,
                      {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify(payload),
                      }
                    );
                    let responseBody = null;
                    try { responseBody = await res.clone().json(); } catch { responseBody = await res.text(); }
                    if (!res.ok) {
                      toast({
                        title: "Error",
                        description: (responseBody && responseBody.error) || "Failed to add cross-repo monitor",
                        variant: "destructive",
                      });
                      return;
                    }
                    // Add the new cross-repo monitor to the state
                    const newCrossRepoMonitor = {
                      id: responseBody.id,
                      type: "github_cross_repo_monitor" as const,
                      url: `https://github.com/${responseBody.internal_repo}`,
                      name: `Cross-Repo: ${responseBody.internal_repo}`,
                      status: "active" as const,
                      addedDate: new Date(),
                      internal_repo: responseBody.internal_repo,
                      external_repos: responseBody.external_repos,
                    };
                    setMonitoredItems((prev) => [...prev, newCrossRepoMonitor]);
                    toast({
                      title: "Cross-Repo Monitor Added",
                      description: `Now monitoring ${externalRepos.length} external repos for changes affecting ${internalRepo}`,
                    });
                    setInternalRepo("");
                    setExternalRepos([]);
                    setExternalRepoInput("");
                    setShowAddDialog(false);
                    return;
                  } catch (e: any) {
                    toast({ title: "Error", description: e.message, variant: "destructive" });
                    return;
                  }
                } else if (addType === 'single' && githubPlatform && availableRepos.length > 0 && selectedRepoFullName) {
                  const repo = githubRepos.find(r => r.full_name === selectedRepoFullName);
                  if (!repo) {
                    toast({ title: "Error", description: "Selected repo not found", variant: "destructive" });
                    return;
                  }
                  try {
                    const monitoredPayload = {
                      type: "github_repo",
                      name: repo.full_name,
                      url: `https://github.com/${repo.full_name}`,
                      repo: repo.full_name.replaceAll("/", "_"),
                      company_id: companyId,
                      acting_user_email: userEmail,
                    };
                    const res = await fetch(
                      `${BACKEND_BASE_URL}/api/integrations/company-monitored-items/`,
                      {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify(monitoredPayload),
                      }
                    );
                    if (!res.ok) {
                      const err = await res.json();
                      toast({
                        title: "Error",
                        description: err.error || "Failed to add repo",
                        variant: "destructive",
                      });
                      return;
                    }
                    const newItem = await res.json();
                    setMonitoredItems((prev) => [...prev, newItem]);
                    setSelectedRepoFullName("");
                    setShowAddDialog(false);
                    toast({
                      title: "GitHub Repo Added",
                      description: `Now monitoring ${repo.full_name}`,
                    });
                    return;
                  } catch (e: any) {
                    toast({ title: "Error", description: e.message, variant: "destructive" });
                    return;
                  }
                } else if (addType === 'single') {
                  if (!newURL.trim()) {
                    toast({
                      title: "Error",
                      description: "Please enter a valid URL",
                      variant: "destructive",
                    });
                    return;
                  }
                  try {
                    new URL(newURL);
                  } catch {
                    toast({
                      title: "Error",
                      description: "Please enter a valid URL",
                      variant: "destructive",
                    });
                    return;
                  }
                  try {
                    const res = await fetch(
                      `${BACKEND_BASE_URL}/api/integrations/company-monitored-items/`,
                      {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                          type: "url",
                          name: newURLName.trim() || new URL(newURL).hostname,
                          url: newURL,
                          company_id: companyId,
                          acting_user_email: userEmail,
                        }),
                      }
                    );
                    if (!res.ok) {
                      const err = await res.json();
                      toast({
                        title: "Error",
                        description: err.error || "Failed to add URL",
                        variant: "destructive",
                      });
                      return;
                    }
                    const newItem = await res.json();
                    setMonitoredItems((prev) => [...prev, newItem]);
                    setNewURL("");
                    setNewURLName("");
                    setShowAddDialog(false);
                    toast({
                      title: "URL Added",
                      description: `Now monitoring ${newItem.name}`,
                    });
                  } catch (e: any) {
                    toast({ title: "Error", description: e.message, variant: "destructive" });
                  }
                }
              }}
              disabled={addType === 'cross' ? (githubPlatform && githubRepos.length > 0 && (!internalRepo || externalRepos.length === 0)) : (githubPlatform && availableRepos.length > 0 && !selectedRepoFullName)}
            >
              Add
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* Modal for doc changes */}
      <Modal open={modalOpen} onOpenChange={setModalOpen}>
        <ModalContent className="max-w-5xl w-full">
          <ModalHeader>
            <ModalTitle>Actionable Changes</ModalTitle>
          </ModalHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {modalChanges.length === 0 ? (
              <div className="text-center text-gray-500">
                No actionable changes found.
              </div>
            ) : (
              <ul className="space-y-4">
                {modalChanges
                  .slice()
                  .sort((a: any, b: any) => {
                    // Try to sort by time descending
                    const ta = a.time ? new Date(a.time).getTime() : 0;
                    const tb = b.time ? new Date(b.time).getTime() : 0;
                    return tb - ta;
                  })
                  .map((eventGroup: any, idx: number) => (
                    <li
                      key={idx}
                      className="bg-white rounded shadow p-3 border border-blue-100"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-gray-400">
                          {eventGroup.time
                            ? new Date(eventGroup.time).toLocaleString()
                            : ""}
                        </span>
                        <span className="text-xs text-blue-700">
                          {eventGroup.repo}
                        </span>
                        <span className="text-xs bg-blue-100 text-blue-700 rounded px-2 py-0.5 ml-2">
                          {eventGroup.changes.length} changes
                        </span>
                      </div>
                      <ul className="pl-4">
                        {Array.isArray(eventGroup.changes) ? (
                          eventGroup.changes.map(
                            (change: any, cidx: number) => {
                              // Determine tag color based on change.severity
                              let tagColor = "bg-gray-200 text-gray-700";
                              const sev = (change.severity || "").toLowerCase();
                              if (sev === "breaking")
                                tagColor = "bg-red-200 text-red-800";
                              else if (sev === "major")
                                tagColor = "bg-orange-200 text-orange-800";
                              else if (sev === "minor")
                                tagColor = "bg-gray-200 text-gray-700";
                              else if (sev === "deprecation")
                                tagColor = "bg-orange-200 text-orange-800";
                              else if (sev === "removal")
                                tagColor = "bg-pink-200 text-pink-800";
                              else if (sev === "addition")
                                tagColor = "bg-green-200 text-green-800";
                              else if (sev === "modification")
                                tagColor = "bg-blue-200 text-blue-800";
                              return (
                                <li key={cidx} className="mb-2">
                                  <div className="mb-1 flex items-center space-x-2 pl-0 ml-0">
                                    {change.severity && (
                                      <span
                                        className={`text-xs px-2 py-0.5 rounded ${tagColor}`}
                                      >
                                        {change.severity}
                                      </span>
                                    )}
                                    <span className="font-semibold text-blue-700 m-0 p-0">
                                      {change.summary}
                                    </span>
                                  </div>
                                  <div className="pl-4">
                                    {change.file && (
                                      <div className="mb-1">
                                        <span className="text-xs text-gray-500">
                                          File:{" "}
                                        </span>
                                        <span className="text-xs text-gray-700">
                                          {change.file}
                                        </span>
                                      </div>
                                    )}
                                    {change.before && (
                                      <div className="mb-1">
                                        <span className="text-xs text-gray-500">
                                          Before:{" "}
                                        </span>
                                        <pre className="bg-gray-100 rounded p-2 text-xs overflow-x-auto">
                                          <code>{change.before}</code>
                                        </pre>
                                      </div>
                                    )}
                                    {change.after && (
                                      <div className="mb-1">
                                        <span className="text-xs text-gray-500">
                                          After:{" "}
                                        </span>
                                        <pre className="bg-gray-100 rounded p-2 text-xs overflow-x-auto">
                                          <code>{change.after}</code>
                                        </pre>
                                      </div>
                                    )}
                                    {change.details && (
                                      <div className="mb-1">
                                        <span className="text-xs text-gray-500">
                                          Details:{" "}
                                        </span>
                                        <span className="text-xs text-gray-700">
                                          {change.details}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </li>
                              );
                            }
                          )
                        ) : (
                          <li className="text-xs text-gray-400">
                            No valid changes found for this event.
                          </li>
                        )}
                      </ul>
                    </li>
                  ))}
              </ul>
            )}
          </div>
          <div className="flex justify-end mt-4">
            <Button variant="outline" onClick={() => setModalOpen(false)}>
              Close
            </Button>
          </div>
        </ModalContent>
      </Modal>
      {/* Modal for approved repo changes */}
      <Modal open={modalOpen} onOpenChange={setModalOpen}>
        <ModalContent className="max-w-5xl w-full">
          <ModalHeader>
            <ModalTitle>Approved Repo Changes for {modalInternalRepo}</ModalTitle>
          </ModalHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {modalRepoLoading ? (
              <div className="text-center text-gray-500">Loading approved repo changes...</div>
            ) : modalRepoChanges.length === 0 ? (
              <div className="text-center text-gray-500">No approved repo changes found for this cross-repo monitor.</div>
            ) : (
              <div className="space-y-6">
                {modalRepoChanges.map((change: any, idx: number) => {
                  // Use details.llm_suggested_docs if available, else fallback
                  const llmDocs = parseLLMSuggestedDocs(change.details?.llm_suggested_docs || change.llm_suggested_docs);
                  return (
                    <div key={change.id} className="mb-4">
                      <div className="font-bold text-purple-800 mb-1">{change.summary}</div>
                      <div className="text-xs text-gray-500 mb-2">Commit: {change.commit_id}</div>
                      {llmDocs.length > 0 && (
                        <div className="space-y-4">
                          {llmDocs.map((doc: any, dIdx: number) => (
                            <div key={dIdx} className="bg-purple-50 border border-purple-200 rounded p-4">
                              <div className="flex items-center justify-between mb-1">
                                <span className="font-bold text-purple-800">{doc.summary}</span>
                                {doc.severity && (
                                  <Badge className={
                                    doc.severity.toLowerCase() === "breaking" ? "bg-red-200 text-red-800" :
                                    doc.severity.toLowerCase() === "major" ? "bg-orange-200 text-orange-800" :
                                    doc.severity.toLowerCase() === "addition" ? "bg-green-200 text-green-800" :
                                    doc.severity.toLowerCase() === "removal" ? "bg-pink-200 text-pink-800" :
                                    doc.severity.toLowerCase() === "modification" ? "bg-blue-200 text-blue-800" :
                                    "bg-gray-200 text-gray-700"
                                  }>{doc.severity}</Badge>
                                )}
                              </div>
                              {doc.file && <div className="text-xs text-gray-500 mb-1">File: {doc.file}</div>}
                              {doc.before && (
                                <div className="mb-1">
                                  <div className="text-xs text-gray-500">Before:</div>
                                  <pre className="bg-gray-100 rounded p-2 text-xs overflow-x-auto"><code>{doc.before}</code></pre>
                                </div>
                              )}
                              {doc.after && (
                                <div className="mb-1">
                                  <div className="text-xs text-gray-500">After:</div>
                                  <pre className="bg-gray-100 rounded p-2 text-xs overflow-x-auto"><code>{doc.after}</code></pre>
                                </div>
                              )}
                              {doc.details && <div className="text-xs text-gray-700 mt-1">{doc.details}</div>}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          <div className="flex justify-end mt-4">
            <Button variant="outline" onClick={() => setModalOpen(false)}>
              Close
            </Button>
          </div>
        </ModalContent>
      </Modal>
      {/* Delete confirmation dialog */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Removal</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this monitored item? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (pendingDeleteId) {
                  const item = monitoredItems.find(i => i.id === pendingDeleteId);
                  handleRemoveItem(pendingDeleteId, item?.type);
                }
                setDeleteConfirmOpen(false);
                setPendingDeleteId(null);
                setPendingDeleteType(null);
              }}
            >
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
