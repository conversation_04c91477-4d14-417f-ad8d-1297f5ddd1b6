"use client"

import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Folder, Trash2, X } from "lucide-react"

interface ContextMenuProps {
  x: number
  y: number
  onClose: () => void
  type: "space" | "folder" | "document"
  itemName: string
  parentSpace: string
  parentFolder?: string
  onAddDocument: (parentSpace: string, parentFolder?: string) => void
  onAddFolder: (parentSpace: string, parentFolder?: string) => void
  onDelete: (
    type: "space" | "folder" | "document",
    itemName: string,
    parentSpace: string,
    parentFolder?: string,
  ) => void
}

export default function ContextMenu({
  x,
  y,
  onClose,
  type,
  itemName,
  parentSpace,
  parentFolder,
  onAddDocument,
  onAddFolder,
  onDelete,
}: ContextMenuProps) {
  const menuRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    document.addEventListener("keydown", handleEscapeKey)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.removeEventListener("keydown", handleEscapeKey)
    }
  }, [onClose])

  return (
    <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50">
      <div ref={menuRef} className="bg-white rounded-lg shadow-xl w-full max-w-sm overflow-hidden">
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-800">Context Menu</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
            aria-label="Close menu"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-2 space-y-2">
          <button
            className="w-full text-left px-4 py-3 flex items-center space-x-3 hover:bg-gray-50 rounded-md transition-colors"
            onClick={() => {
              onAddDocument(parentSpace, parentFolder)
              onClose()
            }}
          >
            <PlusCircle className="h-5 w-5 text-gray-500" />
            <span className="font-medium">Add Document</span>
          </button>

          <button
            className="w-full text-left px-4 py-3 flex items-center space-x-3 hover:bg-gray-50 rounded-md transition-colors"
            onClick={() => {
              onAddFolder(parentSpace, parentFolder)
              onClose()
            }}
          >
            <Folder className="h-5 w-5 text-gray-500" />
            <span className="font-medium">Add Folder</span>
          </button>

          <button
            className="w-full text-left px-4 py-3 flex items-center space-x-3 hover:bg-gray-50 rounded-md transition-colors"
            onClick={() => {
              onDelete(type, itemName, parentSpace, parentFolder)
              onClose()
            }}
          >
            <Trash2 className="h-5 w-5 text-red-500" />
            <span className="font-medium text-red-600">Delete</span>
          </button>
        </div>
      </div>
    </div>
  )
}
