"use client";

import { useNotifications } from "@/hooks/use-notifications";
import { useAuth } from "@/contexts/auth-context";

export function NotificationTest() {
  const { user } = useAuth();
  const { notifications, isConnected, connectionError, reconnect } =
    useNotifications(user?.email || "");

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">
        📡 WebSocket Connection Status
      </h2>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              isConnected ? "bg-green-500" : "bg-red-500"
            }`}
          ></div>
          <span
            className={`font-medium ${
              isConnected ? "text-green-700" : "text-red-700"
            }`}
          >
            {isConnected ? "Connected" : "Disconnected"}
          </span>
        </div>

        {connectionError && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-800 text-sm">
              <strong>Connection Error:</strong> {connectionError}
            </p>
            <button
              onClick={reconnect}
              className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
            >
              Reconnect
            </button>
          </div>
        )}

        <div>
          <h3 className="font-medium text-gray-900 mb-2">
            Recent Notifications ({notifications.length})
          </h3>
          {notifications.length === 0 ? (
            <p className="text-gray-500 text-sm">
              No notifications received yet.
            </p>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {notifications.slice(-10).map((notification, index) => (
                <div key={index} className="bg-gray-50 rounded p-2 text-sm">
                  <div className="font-medium">{notification.title}</div>
                  <div className="text-gray-600">{notification.message}</div>
                  <div className="text-xs text-gray-400 mt-1">
                    {new Date(notification.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="text-xs text-gray-500">
          <p>User Email: {user?.email || "Not logged in"}</p>
          <p>Check browser console for detailed WebSocket logs.</p>
        </div>
      </div>
    </div>
  );
}
