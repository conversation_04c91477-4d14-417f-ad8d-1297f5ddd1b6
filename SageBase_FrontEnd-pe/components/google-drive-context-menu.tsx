"use client";
import { 
  Context<PERSON>enu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { 
  FolderOpen,
  Trash2
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";

interface GoogleDriveContextMenuProps {
  children: React.ReactNode;
  platformId: string;
}

export default function GoogleDriveContextMenu({ children, platformId }: GoogleDriveContextMenuProps) {
  const router = useRouter();
  const { disconnectPlatform } = useConnectedPlatforms();

  const handleDisconnect = async () => {
    try {
      await disconnectPlatform(platformId);
    } catch (error) {
      console.error('Error disconnecting Google Drive:', error);
    }
  };

  const handleViewDriveFiles = () => {
    router.push('/google-drive-integration');
  };

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        <ContextMenuItem onClick={handleViewDriveFiles} className="flex items-center gap-2">
          <FolderOpen className="h-4 w-4" />
          View Drive Files
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem 
          onClick={handleDisconnect} 
          className="flex items-center gap-2 text-red-600 focus:text-red-600"
        >
          <Trash2 className="h-4 w-4" />
          Disconnect
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}
