"use client";
import { useState, useEffect, useCallback } from "react";
import {
  ChevronRight,
  ChevronDown,
  Plus,
  Check,
  Trash2,
  Clock,
  RefreshCw,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { usePlatformStatus } from "@/hooks/use-platform-status";
import { useAuth } from "@/contexts/auth-context";

// Import DEFAULT_PLATFORMS for loading state
const DEFAULT_PLATFORMS = [
  {
    id: "github",
    name: "GitHub",
    icon: "/images/platform-logos/github.svg",
    connected: false,
    status: "disconnected" as const,
  },
  {
    id: "google-drive",
    name: "Google Drive",
    icon: "/images/platform-logos/google-drive.svg",
    connected: false,
    status: "disconnected" as const,
  },
  {
    id: "confluence",
    name: "Confluence",
    icon: "/images/platform-logos/confluence.svg",
    connected: false,
    status: "disconnected" as const,
  },
  {
    id: "slack",
    name: "Slack",
    icon: "/images/platform-logos/slack.svg",
    connected: false,
    status: "disconnected" as const,
  },
  {
    id: "teams",
    name: "Microsoft Teams",
    icon: "/images/platform-logos/teams.svg",
    connected: false,
    status: "disconnected" as const,
  },
];

type PlatformConnection = {
  id: string;
  name: string;
  status: "connected" | "will be available soon" | "available";
  icon?: string;
};

// Local icon mapping - frontend determines icons based on platform ID
const getIconForPlatform = (platformId: string): string => {
  const iconMap: Record<string, string> = {
    github: "/images/platform-logos/github.svg",
    confluence: "/images/platform-logos/confluence.svg",
    teams: "/images/platform-logos/teams.svg",
    slack: "/images/platform-logos/slack.svg",
    discord: "/images/platform-logos/discord.svg",
    gitlab: "/images/platform-logos/gitlab.svg",
    notion: "/images/platform-logos/notion.svg",
    jira: "/images/platform-logos/jira.svg",
    gmail: "/images/platform-logos/gmail.svg",
    email: "/images/platform-logos/gmail.svg",
    whatsapp: "/images/platform-logos/whatsapp.svg",
    "google-drive": "/images/platform-logos/google-drive.svg",
    // Add more platforms as needed
  };

  return iconMap[platformId.toLowerCase()] || "/placeholder.svg";
};

export function CollapsiblePlatforms() {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newlyConnected, setNewlyConnected] = useState<string | null>(null);
  const [platformToDelete, setPlatformToDelete] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const {
    platforms,
    connectPlatform,
    disconnectPlatform,
    refreshPlatforms,
    isLoading: platformsLoading,
  } = useConnectedPlatforms();
  const { userRole, connectedUserCompany, user } = useAuth();

  const companyId = connectedUserCompany?.company_id;
  const userEmail = user?.email;

  // Use the platform status hook for available platforms in modal
  const {
    possibleConnections,
    isLoading: statusLoading,
    error,
    fetchPossibleConnections,
    startPolling,
    stopPolling,
    isPolling,
  } = usePlatformStatus();

  // Only wait for essential platform data (connected platforms from REST API)
  const isLoading = platformsLoading;

  // Show default platforms while loading to avoid empty state
  const displayPlatforms = isLoading ? DEFAULT_PLATFORMS : platforms;
  const displayPossibleConnections = statusLoading
    ? [
        { id: "github", name: "GitHub", status: "available" as const },
        { id: "confluence", name: "Confluence", status: "available" as const },
        { id: "teams", name: "Microsoft Teams", status: "available" as const },
        { id: "slack", name: "Slack", status: "available" as const },
      ]
    : possibleConnections;

  // Debug loading states
  useEffect(() => {
    console.log("🔍 CollapsiblePlatforms loading states:", {
      platformsLoading: platformsLoading,
      statusLoading: statusLoading,
      mainUILoading: isLoading, // Only blocks on platformsLoading now
      platformsCount: platforms.length,
      possibleConnectionsCount: possibleConnections.length,
    });
  }, [
    platformsLoading,
    statusLoading,
    isLoading,
    platforms.length,
    possibleConnections.length,
  ]);

  // Initial fetch for available platforms (only for modal)
  useEffect(() => {
    fetchPossibleConnections();
  }, [fetchPossibleConnections]);

  // Listen for platform connection events and refresh
  useEffect(() => {
    const handlePlatformConnection = () => {
      console.log("🔄 Platform connected, refreshing platforms...");
      setIsRefreshing(true);
      refreshPlatforms().finally(() => {
        setIsRefreshing(false);
      });
    };

    const handleStorageEvent = (event: StorageEvent) => {
      if (
        event.key === "github-connected" ||
        event.key === "slack-connected" ||
        event.key === "teams-connected" ||
        event.key?.includes("platform-connected")
      ) {
        handlePlatformConnection();
      }
    };

    const handleCustomEvent = (event: Event) => {
      if (
        event.type === "platform-connected" ||
        event.type === "platform-disconnected"
      ) {
        handlePlatformConnection();
      }
    };

    // Add event listeners
    window.addEventListener("storage", handleStorageEvent);
    window.addEventListener(
      "platform-connected",
      handleCustomEvent as EventListener
    );
    window.addEventListener(
      "platform-disconnected",
      handleCustomEvent as EventListener
    );

    // Cleanup
    return () => {
      window.removeEventListener("storage", handleStorageEvent);
      window.removeEventListener(
        "platform-connected",
        handleCustomEvent as EventListener
      );
      window.removeEventListener(
        "platform-disconnected",
        handleCustomEvent as EventListener
      );
    };
  }, [refreshPlatforms]);

  // Refetch when window gains focus (OAuth popup closes) - but only if not polling
  useEffect(() => {
    const handleFocus = () => {
      // Only refetch if we're not currently polling
      if (!isPolling) {
        fetchPossibleConnections(true); // Force refresh after OAuth
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [fetchPossibleConnections, isPolling]);

  const handleConnect = (platformId: string) => {
    const platform = possibleConnections.find((p) => p.id === platformId);

    // Don't allow connection if status is "will be available soon"
    if (platform?.status === "will be available soon") {
      return;
    }

    if (platformId === "github") {
      // Open GitHub OAuth in a new window
      const oauthUrl = `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/integrations/github/start/`;

      if (!process.env.NEXT_PUBLIC_BACKEND_API_URL) {
        console.error(
          "NEXT_PUBLIC_BACKEND_API_URL environment variable is not set!"
        );
        alert(
          "Backend API URL not configured. Please check your environment variables."
        );
        return;
      }

      window.open(
        oauthUrl,
        "_blank",
        "width=600,height=700,scrollbars=yes,resizable=yes"
      );
      setIsModalOpen(false);

      // Start polling for connection status
      startPolling();

      return;
    }

    if (platformId === "slack") {
      console.log(
        "🔄 Slack OAuth URL:",
        `${process.env.NEXT_PUBLIC_BACKEND_NGROK_BASE_URL}`
      );
      // Open Slack OAuth in a new window
      const oauthUrl = `${process.env.NEXT_PUBLIC_BACKEND_NGROK_BASE_URL}/api/integrations/slack/connect/`;

      if (!process.env.NEXT_PUBLIC_BACKEND_NGROK_BASE_URL) {
        console.error(
          "NEXT_PUBLIC_BACKEND_NGROK_BASE_URL environment variable is not set!"
        );
        alert(
          "Backend NGROK URL not configured. Please check your environment variables."
        );
        return;
      }

      window.open(
        oauthUrl,
        "_blank",
        "width=600,height=700,scrollbars=yes,resizable=yes"
      );
      setIsModalOpen(false);

      // Start polling for connection status
      startPolling();

      return;
    }

    if (platformId === "discord") {
      // Open Discord OAuth in a new window
      if (!process.env.NEXT_PUBLIC_BACKEND_API_URL) {
        console.error(
          "NEXT_PUBLIC_BACKEND_API_URL environment variable is not set!"
        );
        alert(
          "Backend API URL not configured. Please check your environment variables."
        );
        return;
      }

      if (!companyId) {
        console.error("Company ID is not available!");
        alert("Company ID is not available. Please try again.");
        return;
      }

      const oauthUrl = `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/integrations/discord/start/?company_id=${companyId}`;
      console.log("Discord OAuth URL:", oauthUrl);

      window.open(
        oauthUrl,
        "_blank",
        "width=600,height=700,scrollbars=yes,resizable=yes"
      );
      setIsModalOpen(false);
      return;
    }

    if (platformId === "google-drive") {
      // Open Google Drive OAuth in a new window
      if (!process.env.NEXT_PUBLIC_BACKEND_API_URL) {
        console.error(
          "NEXT_PUBLIC_BACKEND_API_URL environment variable is not set!"
        );
        alert(
          "Backend API URL not configured. Please check your environment variables."
        );
        return;
      }

      if (!companyId || !userEmail) {
        console.error("Company ID or user email is not available!");
        alert("Company ID or user email is not available. Please try again.");
        return;
      }

      // Get user ID from auth context
      if (!user?.id) {
        console.error("User ID is not available!");
        alert("User ID is not available. Please try again.");
        return;
      }

      const oauthUrl = `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/integrations/google-drive/start/?company_id=${companyId}&workspace=default`;
      console.log("Google Drive OAuth URL:", oauthUrl);

      window.open(
        oauthUrl,
        "_blank",
        "width=600,height=700,scrollbars=yes,resizable=yes"
      );
      setIsModalOpen(false);
      return;
    }

    connectPlatform(platformId); //this is done in the callback page in case of github
    setNewlyConnected(platformId);
    setTimeout(() => setNewlyConnected(null), 3000);
    setIsModalOpen(false);
  };

  const handleDeletePlatform = (platformId: string) => {
    disconnectPlatform(platformId);
    setPlatformToDelete(null);
  };

  // Get connected platforms from REST API context (no WebSocket dependency)
  const getConnectedPlatforms = () => {
    return displayPlatforms.filter((platform) => platform.connected);
  };

  // Get available platforms for connection modal
  const getAvailablePlatforms = () => {
    const available = displayPossibleConnections.filter((apiPlatform) => {
      const contextPlatform = displayPlatforms.find(
        (p) => p.id === apiPlatform.id
      );
      const isConnected = contextPlatform?.connected;
      return !isConnected;
    });

    return available;
  };

  if (isLoading) {
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between w-full py-2 px-3">
          <div className="flex items-center">
            <ChevronDown className="h-3.5 w-3.5 text-gray-500 mr-1" />
            <span className="text-xs font-semibold text-gray-500 tracking-wider uppercase">
              Connected Platforms
            </span>
          </div>
        </div>
        <div className="mt-1 px-3 py-2 text-sm text-gray-500">
          Loading platforms...
        </div>
      </div>
    );
  }

  // Don't block the entire component for status errors - only show error if platform loading fails
  // Status errors will be handled in the modal when user tries to add platforms

  const connectedPlatforms = getConnectedPlatforms();
  const availablePlatforms = getAvailablePlatforms();

  return (
    <div className="mb-6">
      {/* Header */}
      <div className="flex items-center justify-between w-full py-2 px-3 hover:bg-gray-100/50 rounded-md transition-colors group">
        <button
          className="flex items-center flex-1 text-left"
          onClick={() => setIsExpanded(!isExpanded)}
          aria-expanded={isExpanded}
          aria-controls="connected-platforms-list"
        >
          {isExpanded ? (
            <ChevronDown className="h-3.5 w-3.5 text-gray-500 mr-1" />
          ) : (
            <ChevronRight className="h-3.5 w-3.5 text-gray-500 mr-1" />
          )}
          <span className="text-xs font-semibold text-gray-500 tracking-wider uppercase">
            Connected Platforms
          </span>
        </button>
        <div className="flex items-center space-x-1">
          <button
            className="p-1 rounded-full hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={(e) => {
              e.stopPropagation();
              setIsRefreshing(true);
              refreshPlatforms().finally(() => {
                setIsRefreshing(false);
              });
            }}
            aria-label="Refresh platforms"
            disabled={isRefreshing}
          >
            <RefreshCw
              className={`h-3.5 w-3.5 text-gray-500 ${
                isRefreshing ? "animate-spin" : ""
              }`}
            />
          </button>
          {userRole === "ADMIN" && (
            <button
              className="p-1 rounded-full hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                setIsModalOpen(true);
              }}
              aria-label="Add platform"
            >
              <Plus className="h-3.5 w-3.5 text-gray-500" />
            </button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div id="connected-platforms-list" className="mt-1 space-y-1">
          {connectedPlatforms.length > 0 ? (
            connectedPlatforms.map((platform) => (
              <div
                key={platform.id}
                className={`flex items-center py-1.5 px-3 text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer relative group ${
                  newlyConnected === platform.id ? "bg-green-50" : ""
                } ${platform.status === "connected" ? "bg-green-50/30" : ""}`}
              >
                <div className="h-5 w-5 mr-2 relative flex-shrink-0 overflow-hidden">
                  <Image
                    src={getIconForPlatform(platform.id)}
                    alt={`${platform.name} icon`}
                    width={20}
                    height={20}
                    className="object-contain"
                  />
                </div>
                <span
                  className={`text-sm flex-1 ${
                    platform.status === "connected"
                      ? "text-green-700 font-medium"
                      : ""
                  }`}
                >
                  {platform.name}
                </span>

                {/* Status indicator for connected platforms */}
                {platform.status === "connected" && (
                  <div className="ml-auto flex items-center space-x-1">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-green-600 font-medium">
                      Connected
                    </span>
                  </div>
                )}

                {/* Success indicator for newly connected platforms */}
                {newlyConnected === platform.id && (
                  <div className="ml-auto flex items-center space-x-1 animate-fade-in">
                    <Check className="h-4 w-4 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">
                      Connected!
                    </span>
                  </div>
                )}

                {/* Delete button - only visible on hover and when not showing success */}
                {newlyConnected !== platform.id && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setPlatformToDelete(platform.id);
                    }}
                    className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-red-100 text-red-500"
                    aria-label={`Disconnect ${platform.name}`}
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </button>
                )}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-sm text-gray-500 italic">
              No platforms connected yet. Click the + button to add platforms.
            </div>
          )}
        </div>
      )}

      {/* Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Connect your Platforms</DialogTitle>
            <DialogDescription>
              Connect SageBase to your knowledge platforms.
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="popular" className="mt-4">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="popular">Popular</TabsTrigger>
              <TabsTrigger value="all">All Platforms</TabsTrigger>
            </TabsList>

            <div className="space-y-3 mt-2">
              {/* Show status loading or error in modal */}
              {statusLoading && (
                <div className="text-center py-4 text-gray-500">
                  Loading available platforms...
                </div>
              )}
              {error && !statusLoading && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-800">
                    ⚠️ Unable to load latest platform options: {error}
                  </p>
                  <p className="text-xs text-yellow-600 mt-1">
                    Some platforms may not be available for connection.
                  </p>
                </div>
              )}
              {!statusLoading && availablePlatforms.length > 0 ? (
                availablePlatforms.map((platform) => (
                  <div
                    key={platform.id}
                    className={`flex items-center justify-between p-3 border rounded-md ${
                      platform.status === "will be available soon"
                        ? "bg-gray-50 border-gray-200"
                        : ""
                    }`}
                  >
                    <div className="flex items-center">
                      <div
                        className={`h-6 w-6 mr-3 relative flex-shrink-0 overflow-hidden ${
                          platform.status === "will be available soon"
                            ? "opacity-50"
                            : ""
                        }`}
                      >
                        <Image
                          src={getIconForPlatform(platform.id)}
                          alt={`${platform.name} icon`}
                          width={24}
                          height={24}
                          className="object-contain"
                        />
                      </div>
                      <div className="flex flex-col">
                        <p
                          className={`font-medium ${
                            platform.status === "will be available soon"
                              ? "text-gray-500"
                              : ""
                          }`}
                        >
                          {platform.name}
                        </p>
                        {platform.status === "will be available soon" && (
                          <div className="flex items-center mt-1">
                            <Clock className="h-3 w-3 text-gray-400 mr-1" />
                            <span className="text-xs text-gray-400">
                              Coming soon
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleConnect(platform.id)}
                      disabled={platform.status === "will be available soon"}
                      variant={
                        platform.status === "will be available soon"
                          ? "outline"
                          : "default"
                      }
                      className={
                        platform.status === "will be available soon"
                          ? "cursor-not-allowed opacity-50"
                          : ""
                      }
                    >
                      {platform.status === "will be available soon"
                        ? "Soon"
                        : "Connect"}
                    </Button>
                  </div>
                ))
              ) : !statusLoading && !error ? (
                <div className="text-center py-4 text-gray-500">
                  No platforms available to connect.
                </div>
              ) : null}
            </div>

            <div className="flex justify-end mt-4">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Done
              </Button>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Deletion Confirmation Dialog */}
      <Dialog
        open={!!platformToDelete}
        onOpenChange={() => setPlatformToDelete(null)}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Disconnect Platform</DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect{" "}
              {possibleConnections.find((p) => p.id === platformToDelete)?.name}
              ? This will remove access to data from this platform.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={() => setPlatformToDelete(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() =>
                platformToDelete && handleDeletePlatform(platformToDelete)
              }
            >
              Disconnect
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateX(10px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
}

// Add default export
export default CollapsiblePlatforms;
