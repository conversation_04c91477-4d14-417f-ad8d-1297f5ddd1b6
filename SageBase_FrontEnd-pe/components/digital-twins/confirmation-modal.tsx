"use client"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog" // Using AlertDialog for semantic correctness

interface ConfirmationModalProps {
  isOpen: boolean
  onOpenChange: (isOpen: boolean) => void
  employeeName: string
  onConfirm: () => void
}

export default function ConfirmationModal({ isOpen, onOpenChange, employeeName, onConfirm }: ConfirmationModalProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirm Digital Twin Creation</AlertDialogTitle>
          <AlertDialogDescription>
            SageBase will create a digital twin for <strong>{employeeName}</strong>.
            <br />
            Access rights are inherited from the real access. Access can be configured at any time.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => onOpenChange(false)}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>Confirm</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
