"use client"

import { useState } from "react"
import Image from "next/image"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  AlertCircle,
  Mail,
  SlackIcon as LucideSlackIcon,
  GroupIcon as TeamsIcon,
  PhoneIcon as WhatsAppIcon,
  Briefcase,
  FileText,
  Github,
  Globe,
  Power,
  PowerOff,
  CheckCircle,
  XCircle,
  type LucideIcon,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface Tool {
  id: string
  name: string
  icon: LucideIcon
  connected: boolean
}

const initialTools: Tool[] = [
  { id: "email", name: "<PERSON><PERSON>", icon: Mail, connected: true },
  { id: "slack", name: "<PERSON>lack", icon: LucideSlackIcon, connected: true },
  { id: "discord", name: "Discord", icon: MessageSquare, connected: false },
  { id: "teams", name: "Microsoft Teams", icon: TeamsIcon, connected: false },
  { id: "jira", name: "Jira", icon: Briefcase, connected: true },
  { id: "confluence", name: "Confluence", icon: FileText, connected: true },
  { id: "github", name: "GitHub", icon: Github, connected: false },
]

interface DeploymentInstance {
  id: string
  name: string
  location: string
  status: "Active" | "Inactive" | "Pending"
  platform?: "teams" | "slack" | "whatsapp"
  customIconPath?: string
}

const mockDeployments: DeploymentInstance[] = [
  // Team Alpha removed
  // {
  //   id: "dep1",
  //   name: "Team Alpha",
  //   location: "Spain",
  //   status: "Active",
  //   customIconPath: "/images/platform-logos/slack-symbol-logo.png",
  // },
  {
    id: "dep2",
    name: "Team Beta",
    location: "Tunisia",
    status: "Active",
    customIconPath: "/images/platform-logos/msteams-logo.png", // Uses MS Teams logo
  },
  { id: "dep3", name: "Project Phoenix", location: "USA (East)", status: "Pending", platform: "whatsapp" },
  // General Deployment removed
  // { id: "dep4", name: "General Deployment", location: "Germany", status: "Inactive" },
]

interface ConfigureTwinModalProps {
  isOpen: boolean
  onOpenChange: (isOpen: boolean) => void
  twinName: string
}

export default function ConfigureTwinModal({ isOpen, onOpenChange, twinName }: ConfigureTwinModalProps) {
  const [tools, setTools] = useState<Tool[]>(initialTools)
  const [deployments] = useState<DeploymentInstance[]>(mockDeployments)

  const handleToggleConnection = (toolId: string) => {
    setTools((prevTools) =>
      prevTools.map((tool) => (tool.id === toolId ? { ...tool, connected: !tool.connected } : tool)),
    )
    console.log(`Toggled connection for ${toolId}`)
  }

  const getDeploymentStatusVariant = (status: DeploymentInstance["status"]) => {
    switch (status) {
      case "Active":
        return "default"
      case "Pending":
        return "secondary"
      case "Inactive":
        return "outline"
      default:
        return "outline"
    }
  }

  const getDeploymentPlatformIcon = (deployment: DeploymentInstance) => {
    if (deployment.customIconPath) {
      return (
        <Image
          src={deployment.customIconPath || "/placeholder.svg"}
          alt={`${deployment.name} platform logo`}
          width={20}
          height={20}
          className="mr-3 object-contain"
        />
      )
    }
    switch (deployment.platform) {
      case "teams":
        return <TeamsIcon className="h-5 w-5 mr-3 text-blue-600" />
      case "slack":
        return <LucideSlackIcon className="h-5 w-5 mr-3 text-purple-600" />
      case "whatsapp":
        return <WhatsAppIcon className="h-5 w-5 mr-3 text-green-600" />
      default:
        return <Globe className="h-5 w-5 mr-3 text-gray-500" />
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg md:max-w-xl">
        <DialogHeader>
          <DialogTitle>Configure Digital Twin: {twinName}</DialogTitle>
          <DialogDescription>
            Manage connected data sources, deployment instances, and access permissions for this digital twin.
          </DialogDescription>
        </DialogHeader>

        <Alert variant="destructive" className="mt-4 bg-red-50 border-red-500 text-red-700">
          <AlertCircle className="h-5 w-5 !text-red-600" />
          <AlertTitle className="font-semibold !text-red-800">Important: Data Privacy</AlertTitle>
          <AlertDescription className="!text-red-700">
            SageBase will <span className="font-semibold">never</span> communicate about any private data, or any data
            that has been explicitly set to "non-authorized" by the original user or administrators. Access rights are
            designed to mirror real-world permissions.
          </AlertDescription>
        </Alert>

        <ScrollArea className="max-h-[55vh] pr-4 mt-4">
          <div className="py-4 space-y-6">
            {/* Connected Tools Section (omitted for brevity, no changes) */}
            <div>
              <h3 className="text-md font-semibold mb-3 text-gray-700">Connected Tools & Data Sources</h3>
              <div className="space-y-2">
                {tools.map((tool) => (
                  <div
                    key={tool.name}
                    className="flex items-center justify-between p-3 border rounded-md bg-gray-50/50 hover:bg-gray-100/70 transition-colors"
                  >
                    <div className="flex items-center">
                      <tool.icon className="h-5 w-5 mr-3 text-gray-500" />
                      <span className="text-sm font-medium text-gray-800">{tool.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={tool.connected ? "default" : "outline"} className="w-24 justify-center">
                        {tool.connected ? (
                          <CheckCircle className="h-3.5 w-3.5 mr-1" />
                        ) : (
                          <XCircle className="h-3.5 w-3.5 mr-1" />
                        )}
                        {tool.connected ? "Connected" : "Not Connected"}
                      </Badge>
                      <Button
                        variant={tool.connected ? "destructive" : "outline"}
                        size="sm"
                        onClick={() => handleToggleConnection(tool.id)}
                        className="w-28"
                      >
                        {tool.connected ? (
                          <PowerOff className="h-4 w-4 mr-1.5" />
                        ) : (
                          <Power className="h-4 w-4 mr-1.5" />
                        )}
                        {tool.connected ? "Disconnect" : "Connect"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-2">Changes to connections may take a few moments to reflect.</p>
            </div>
            <Separator />

            {/* Deployment Instances Section */}
            <div>
              <h3 className="text-md font-semibold mb-3 text-gray-700">Deployment Instances</h3>
              {deployments.length > 0 ? (
                <div className="space-y-2">
                  {deployments.map((deployment) => (
                    <div
                      key={deployment.id}
                      className="flex items-center justify-between p-3 border rounded-md bg-gray-50/50"
                    >
                      <div className="flex items-center">
                        {getDeploymentPlatformIcon(deployment)}
                        <div>
                          <span className="text-sm font-medium text-gray-800">{deployment.name}</span>
                          <p className="text-xs text-gray-500">{deployment.location}</p>
                        </div>
                      </div>
                      <Badge variant={getDeploymentStatusVariant(deployment.status)}>{deployment.status}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No deployment instances found for this digital twin.</p>
              )}
              <p className="text-xs text-gray-500 mt-2">
                Digital twin instances can be deployed to specific teams or projects on various communication channels.
              </p>
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
