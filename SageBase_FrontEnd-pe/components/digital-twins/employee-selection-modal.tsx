"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import type { Employee } from "@/types/employee"
import { UserPlus } from "lucide-react"

interface EmployeeSelectionModalProps {
  isOpen: boolean
  onOpenChange: (isOpen: boolean) => void
  onSelectEmployee: (employee: Employee) => void
}

const mockEmployees: Employee[] = [
  {
    id: "emp1",
    name: "<PERSON>",
    department: "Engineering",
    yearsInCompany: 5,
    email: "<EMAIL>",
    role: "Software Engineer",
  },
  {
    id: "emp2",
    name: "<PERSON> The Builder",
    department: "Product",
    yearsInCompany: 3,
    email: "<EMAIL>",
    role: "Product Manager",
  },
  {
    id: "emp3",
    name: "<PERSON>",
    department: "Marketing",
    yearsInCompany: 7,
    email: "<EMAIL>",
    role: "Marketing Lead",
  },
  {
    id: "emp4",
    name: "<PERSON>",
    department: "HR",
    yearsInCompany: 2,
    email: "<EMAIL>",
    role: "HR Specialist",
  },
  {
    id: "emp5",
    name: "Edward Scissorhands",
    department: "Design",
    yearsInCompany: 4,
    email: "<EMAIL>",
    role: "UX Designer",
  },
  {
    id: "emp6",
    name: "Fiona Gallagher",
    department: "Sales",
    yearsInCompany: 6,
    email: "<EMAIL>",
    role: "Sales Executive",
  },
  {
    id: "emp7",
    name: "George Jetson",
    department: "Operations",
    yearsInCompany: 1,
    email: "<EMAIL>",
    role: "Operations Analyst",
  },
  {
    id: "emp8",
    name: "Hannah Montana",
    department: "Engineering",
    yearsInCompany: 8,
    email: "<EMAIL>",
    role: "Senior Engineer",
  },
  {
    id: "emp9",
    name: "Ian Malcolm",
    department: "Research",
    yearsInCompany: 10,
    email: "<EMAIL>",
    role: "Lead Researcher",
  },
  {
    id: "emp10",
    name: "Jane Doe",
    department: "Support",
    yearsInCompany: 3,
    email: "<EMAIL>",
    role: "Support Agent",
  },
]

export default function EmployeeSelectionModal({
  isOpen,
  onOpenChange,
  onSelectEmployee,
}: EmployeeSelectionModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Select Employee to Create Digital Twin</DialogTitle>
          <DialogDescription>Choose an employee from the list below to create their digital twin.</DialogDescription>
        </DialogHeader>
        <div className="max-h-[60vh] overflow-y-auto pr-2">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Department</TableHead>
                <TableHead className="text-center">Years with Company</TableHead>
                <TableHead className="text-right">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell className="font-medium">{employee.name}</TableCell>
                  <TableCell>{employee.department}</TableCell>
                  <TableCell className="text-center">{employee.yearsInCompany}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm" onClick={() => onSelectEmployee(employee)}>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Create Digital Twin
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
