"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send, RefreshCw, Sparkles, Users, TrendingUp } from 'lucide-react';
import Comment from './comment';
import commentsAPI from '@/services/comments-api';
import { useAuth } from '@/contexts/auth-context';
import type { Comment as CommentType } from '@/types/comments';

interface CommentsSectionProps {
  qaId: string;
  className?: string;
}

export default function CommentsSection({ qaId, className = '' }: CommentsSectionProps) {
  const { user } = useAuth();
  const [comments, setComments] = useState<CommentType[]>([]);
  const [newComment, setNewComment] = useState('');
  const [userVotes, setUserVotes] = useState<Record<string, 'up' | 'down' | 'none'>>({});
  
  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [votingComments, setVotingComments] = useState<Set<string>>(new Set());
  const [editingComments, setEditingComments] = useState<Set<string>>(new Set());
  const [deletingComments, setDeletingComments] = useState<Set<string>>(new Set());
  
  // Error states
  const [error, setError] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Load comments on mount
  useEffect(() => {
    loadComments();
  }, [qaId]);

  const loadComments = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const commentsData = await commentsAPI.getComments(qaId);
      setComments(commentsData);
      
      // Initialize user votes (you might want to get this from the API)
      const initialVotes: Record<string, 'up' | 'down' | 'none'> = {};
      commentsData.forEach(comment => {
        initialVotes[comment.id] = 'none'; // Default to no vote
      });
      setUserVotes(initialVotes);
      
    } catch (err) {
      console.error('Error loading comments:', err);
      setError(err instanceof Error ? err.message : 'Failed to load comments');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newComment.trim()) {
      setSubmitError('Please enter a comment');
      return;
    }

    if (!user) {
      setSubmitError('You must be logged in to comment');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const comment = await commentsAPI.createComment(qaId, newComment.trim());
      
      // Add the new comment to the list
      setComments(prevComments => [...prevComments, comment]);
      
      // Initialize vote state for new comment
      setUserVotes(prev => ({ ...prev, [comment.id]: 'none' }));
      
      // Clear the form
      setNewComment('');
      
    } catch (err) {
      console.error('Error creating comment:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to post comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVote = async (commentId: string, voteType: 'up' | 'down') => {
    if (!user) {
      setSubmitError('You must be logged in to vote');
      return;
    }

    // Optimistic update
    const currentVote = userVotes[commentId];
    const newVote = currentVote === voteType ? 'none' : voteType;
    
    setVotingComments(prev => new Set(prev).add(commentId));
    
    try {
      const voteData = await commentsAPI.voteComment(qaId, commentId, voteType);
      
      // Update comment votes
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId
            ? { ...comment, votes: voteData.votes }
            : comment
        )
      );
      
      // Update user vote state
      setUserVotes(prev => ({ ...prev, [commentId]: voteData.user_vote }));
      
    } catch (err) {
      console.error('Error voting on comment:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to vote');
    } finally {
      setVotingComments(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    }
  };

  const handleEditComment = async (commentId: string, newContent: string) => {
    setEditingComments(prev => new Set(prev).add(commentId));
    setSubmitError(null);
    
    try {
      const updatedComment = await commentsAPI.updateComment(qaId, commentId, newContent);
      
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId ? updatedComment : comment
        )
      );
      
    } catch (err) {
      console.error('Error updating comment:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to update comment');
      throw err; // Re-throw to let the Comment component handle UI state
    } finally {
      setEditingComments(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    setDeletingComments(prev => new Set(prev).add(commentId));
    setSubmitError(null);
    
    try {
      await commentsAPI.deleteComment(qaId, commentId);
      
      setComments(prevComments =>
        prevComments.filter(comment => comment.id !== commentId)
      );
      
      // Remove from user votes
      setUserVotes(prev => {
        const newVotes = { ...prev };
        delete newVotes[commentId];
        return newVotes;
      });
      
    } catch (err) {
      console.error('Error deleting comment:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to delete comment');
      throw err; // Re-throw to let the Comment component handle UI state
    } finally {
      setDeletingComments(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    }
  };

  if (isLoading) {
    return (
      <Card className={`${className} border-0 bg-gradient-to-br from-white to-gray-50/50 shadow-lg rounded-xl`}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-xl font-bold text-gray-800">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl mr-3 shadow-md">
              <MessageSquare className="h-5 w-5 text-white" />
            </div>
            Comments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-sm opacity-20 animate-pulse"></div>
              </div>
              <span className="text-gray-600 font-medium">Loading comments...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className} border-0 bg-gradient-to-br from-white to-gray-50/50 shadow-lg rounded-xl overflow-hidden`}>
      <CardHeader className="pb-6 bg-gradient-to-r from-gray-50 to-white border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-md">
                <MessageSquare className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-gray-800">
                  Comments
                </CardTitle>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <Users className="h-4 w-4" />
                    <span>{comments.length} comments</span>
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <TrendingUp className="h-4 w-4" />
                    <span>{comments.reduce((total, comment) => total + comment.votes.upvotes + comment.votes.downvotes, 0)} votes</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => loadComments(true)}
            disabled={isRefreshing}
            className="h-9 px-4 border-gray-300 hover:bg-gray-50 rounded-full"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6 p-6">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Submit Error Display */}
        {submitError && (
          <Alert variant="destructive">
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        {/* Enhanced Comment Form */}
        {user ? (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
            <div className="flex items-center space-x-2 mb-4">
              <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
              <span className="font-semibold text-gray-800">Share your thoughts</span>
            </div>
            <form onSubmit={handleSubmitComment} className="space-y-4">
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Write your comment here... Share your insights, ask questions, or provide additional context..."
                className="min-h-[120px] resize-none border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg bg-white shadow-sm"
                disabled={isSubmitting}
                maxLength={1000}
              />
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className={`text-xs ${newComment.length > 900 ? 'text-red-500' : 'text-gray-500'}`}>
                    {newComment.length}/1000 characters
                  </span>
                  {newComment.length > 0 && (
                    <Badge variant="secondary" className="text-xs bg-green-50 text-green-600 border-green-200">
                      Ready to post
                    </Badge>
                  )}
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting || !newComment.trim()}
                  size="sm"
                  className="h-9 px-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-md rounded-full"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Posting...' : 'Post Comment'}
                </Button>
              </div>
            </form>
          </div>
        ) : (
          <div className="text-center py-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full mb-4">
              <Users className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Join the conversation</h3>
            <p className="text-gray-500">You must be logged in to comment and engage with the community.</p>
          </div>
        )}

        {/* Enhanced Comments List */}
        <div className="space-y-6">
          {comments.length > 0 ? (
            <div className="space-y-6">
              {comments.map((comment, index) => (
                <div key={comment.id} className="relative">
                  {/* Comment Number Badge */}
                  <div className="absolute -left-2 -top-2 z-10">
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs px-2 py-1 rounded-full shadow-md">
                      #{index + 1}
                    </Badge>
                  </div>
                  <Comment
                    comment={comment}
                    currentUserId={user?.id}
                    userVote={userVotes[comment.id] || 'none'}
                    onVote={handleVote}
                    onEdit={handleEditComment}
                    onDelete={handleDeleteComment}
                    isVoting={votingComments.has(comment.id)}
                    isEditing={editingComments.has(comment.id)}
                    isDeleting={deletingComments.has(comment.id)}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
              <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-300 to-gray-400 rounded-full mb-6">
                <MessageSquare className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No comments yet</h3>
              <p className="text-gray-500 mb-4">Be the first to share your thoughts and start the conversation!</p>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-400">
                <Sparkles className="h-4 w-4" />
                <span>Your comment could help others</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
