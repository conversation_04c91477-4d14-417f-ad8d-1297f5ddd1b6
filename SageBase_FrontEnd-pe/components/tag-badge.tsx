"use client"

import { Badge } from "@/components/ui/badge"
import Link from "next/link"

interface TagBadgeProps {
  tag: string
  count?: number
  onClick?: () => void
  href?: string
}

export function TagBadge({ tag, count, onClick, href }: TagBadgeProps) {
  const badge = (
    <Badge variant="secondary" className="bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer" onClick={onClick}>
      {tag}
      {count !== undefined && <span className="ml-1 text-blue-500">×{count}</span>}
    </Badge>
  )

  if (href) {
    return <Link href={href}>{badge}</Link>
  }

  return badge
}
