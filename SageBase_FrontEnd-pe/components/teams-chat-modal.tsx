"use client";

import { ContextMenuTrigger } from "@/components/ui/context-menu";
import { useRouter } from "next/navigation";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  X,
  Send,
  Paperclip,
  Smile,
  Maximize2,
  Minimize2,
  MessageSquare,
  Check,
  Copy,
  BookOpen,
  FileText,
  Database,
  Share2,
  Info,
} from "lucide-react";
import HtmlResponse from "./html-response";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from "@/components/ui/context-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import Sage<PERSON>ase<PERSON>ogo from "./sagebase-logo";

// Add the chat message styles
const chatMessageStyles = `
  .chat-message-content a {
    color: #3b82f6;
    text-decoration: underline;
    transition: color 0.2s;
  }
  
  .chat-message-content a:hover {
    color: #2563eb;
  }
  
  .chat-message-content ul, .chat-message-content ol {
    margin-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .chat-message-content ul {
    list-style-type: disc;
  }
  
  .chat-message-content ol {
    list-style-type: decimal;
  }
  
  .chat-message-content p {
    margin-bottom: 0.5rem;
  }
  
  .chat-message-content p:last-child {
    margin-bottom: 0;
  }
  
  .chat-message-content pre {
    background-color: #2d2d2d;
    padding: 0.75rem;
    border-radius: 0.25rem;
    overflow-x: auto;
    margin: 0.75rem 0;
    color: #e6e6e6;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
  }
  
  .chat-message-content code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #2d2d2d;
    color: #e6e6e6;
    padding: 0.2rem 0.4rem;
    border-radius: 0.2rem;
    font-size: 0.9rem;
    line-height: 1.4;
  }
  
  .chat-message-content div {
    margin-bottom: 0.5rem;
  }
  
  .chat-message-content div:last-child {
    margin-bottom: 0;
  }

  .selectable-message {
    position: relative;
    user-select: text;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .message-selected {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  .highlight-message {
    animation: highlight-pulse 2s ease-in-out;
  }

  @keyframes highlight-pulse {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
  }
`;

type Message = {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  sender?: string;
  avatar?: string;
  timestamp: Date;
  selected?: boolean;
  highlighted?: boolean;
};

type ChatModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function ChatModal({ isOpen, onClose }: ChatModalProps) {
  const router = useRouter();

  // Initialize with hardcoded conversation
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      content: "Welcome to the chat!",
      role: "system",
      timestamp: new Date(Date.now() - 3600000 * 24), // 24 hours ago
    },
    {
      id: "user-question",
      content:
        "Hi, I have this ERROR on my dev environment \"Error: Cannot find module '@babel/preset-env' - This is a very weird error!\" Do you please have any idea how to fix it?",
      role: "user",
      timestamp: new Date(Date.now() - 3600000 * 2), // 2 hours ago
    },
    {
      id: "assistant-answer",
      content:
        "Yes, I also had it yesterday, and I've done this command to fix it: <pre><code>npm install --save-dev @babel/preset-env && npm cache clean --force</code></pre> This is a solution that I found myself after debugging the build process. It seems there's an issue with the babel dependencies not being properly installed or cached incorrectly.",
      role: "assistant",
      sender: "Assistant",
      avatar: "A",
      timestamp: new Date(Date.now() - 3600000 * 1), // 1 hour ago
    },
  ]);
  const [input, setInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedText, setSelectedText] = useState("");
  const [isClosing, setIsClosing] = useState(false);
  const [isForwarding, setIsForwarding] = useState(false);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  // Change from single selection to multiple selections
  const [selectedMessageIds, setSelectedMessageIds] = useState<string[]>([]);

  const [showForwardDialog, setShowForwardDialog] = useState(false);
  const [forwardDestination, setForwardDestination] = useState("document");
  const [documentTitle, setDocumentTitle] = useState(
    "Development Environment Troubleshooting.md"
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const assistantMessageRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const styleRef = useRef<HTMLStyleElement | null>(null);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        if (showForwardDialog) {
          setShowForwardDialog(false);
        } else {
          handleClose();
        }
      }
    };

    window.addEventListener("keydown", handleEscapeKey);
    return () => window.removeEventListener("keydown", handleEscapeKey);
  }, [isOpen, onClose, showForwardDialog]);

  // Add this inside the component, after the state declarations
  useEffect(() => {
    if (isOpen) {
      // Add the styles to the document
      const styleElement = document.createElement("style");
      styleElement.innerHTML = chatMessageStyles;
      document.head.appendChild(styleElement);
      styleRef.current = styleElement;

      // Clean up on unmount
      return () => {
        if (styleRef.current && document.head.contains(styleRef.current)) {
          document.head.removeChild(styleElement);
          styleRef.current = null;
        }
      };
    }
  }, [isOpen]);

  // Handle redirection after forwarding
  useEffect(() => {
    if (redirectPath) {
      // Small delay to allow the modal to close smoothly
      const redirectTimer = setTimeout(() => {
        router.push(redirectPath);
        setRedirectPath(null);
      }, 300);

      return () => clearTimeout(redirectTimer);
    }
  }, [redirectPath, router]);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isTyping]);

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        inputRef.current?.focus();
        // Scroll to the bottom when opening
        messagesEndRef.current?.scrollIntoView({ behavior: "auto" });
      }, 100);
    }
  }, [isOpen]);

  // Update selected text whenever selected messages change
  useEffect(() => {
    if (selectedMessageIds.length > 0) {
      // Combine text from all selected messages
      const combinedText = selectedMessageIds
        .map((id) => {
          const message = messages.find((m) => m.id === id);
          if (!message) return "";

          // Get the text content of the message without HTML tags
          let messageText = message.content.replace(/<[^>]*>/g, "");

          // If it's a code block, try to extract just the code
          if (message.content.includes("<pre><code>")) {
            const codeMatch = message.content.match(
              /<pre><code>([\s\S]*?)<\/code><\/pre>/
            );
            if (codeMatch && codeMatch[1]) {
              messageText = codeMatch[1];
            }
          }

          // Add sender info for assistants
          if (message.role === "assistant" && message.sender) {
            return `${message.sender}: ${messageText}`;
          }

          return messageText;
        })
        .join("\n\n");

      setSelectedText(combinedText);
    } else {
      setSelectedText("");
    }
  }, [selectedMessageIds, messages]);

  // Handle closing the modal
  const handleClose = () => {
    setIsClosing(true);
    setIsFullscreen(false);
    setSelectedMessageIds([]);
    setSelectedText("");
    setShowForwardDialog(false);

    // Call the parent's onClose function with a slight delay
    // to ensure all state updates have been processed
    setTimeout(() => {
      if (typeof onClose === "function") {
        onClose();
      }
      setIsClosing(false);
    }, 50);
  };

  // Handle clicking outside the modal to close it
  const handleOutsideClick = (e: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      if (showForwardDialog) {
        // Don't close the main modal if the forward dialog is open
        return;
      }
      handleClose();
    }
  };

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);

    // Simulate typing delay and add assistant response
    setTimeout(() => {
      // Add assistant notification message
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        content:
          "SageBase captured important information from your discussion.<br/>A draft documentation is ready for your review and approval.<br/><a href='/editor/nova/qa' class='text-blue-600 font-semibold hover:text-blue-700 hover:underline cursor-pointer'>Knowledge Spaces/Nova/Tips/Q/A</a>",
        role: "assistant",
        sender: "SageBase",
        avatar: "SB",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
      setIsTyping(false);

      // Add click event listener to the link after the message is added
      setTimeout(() => {
        const link = document.querySelector(".chat-message-content a");
        if (link) {
          link.addEventListener("click", (e) => {
            e.preventDefault();
            window.location.href = "/editor/nova/qa";
          });
        }
      }, 100);
    }, 2000);
  };

  // Function to handle clicking on the document link
  const handleDocumentLinkClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    router.push("/editor/nova/qa");
    handleClose();
  };

  // Clear all selections
  const clearSelections = () => {
    setSelectedMessageIds([]);
    setSelectedText("");
  };

  // Handle dialog state change
  const handleDialogChange = (open: boolean) => {
    setShowForwardDialog(open);
    // If dialog is closing, clear selections
    if (!open) {
      setSelectedMessageIds([]);
      setSelectedText("");
    }
  };

  const handleTextSelection = (messageId: string) => {
    // Implement handleTextSelection logic here
  };

  const handleMessageSelect = (message: Message, e: React.MouseEvent) => {
    // Implement handleMessageSelect logic here
  };

  const handleForwardToSageBase = () => {
    // Implement handleForwardToSageBase logic here
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Implement handleKeyDown logic here
  };

  const handleConfirmForward = () => {
    // Implement handleConfirmForward logic here
  };

  if (!isOpen || isClosing) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-start justify-center ${
        isFullscreen ? "" : "bg-black/50"
      } transition-all duration-200`}
      onClick={handleOutsideClick}
    >
      <div
        ref={modalRef}
        className={`bg-white rounded-lg shadow-xl overflow-hidden flex flex-col transition-all duration-200 ${
          isFullscreen
            ? "fixed inset-0 rounded-none"
            : "w-[1000px] max-w-[95vw] h-[700px] max-h-[95vh] mt-16"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Chat Header */}
        <div className="bg-gray-800 text-white px-4 py-2 flex items-center justify-between">
          <div className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            <div className="flex items-center">
              <span className="font-medium">Chat</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white hover:bg-white/20"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white hover:bg-white/20"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 overflow-y-auto p-4 bg-white space-y-4">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className={`flex ${
                message.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div className="flex max-w-[70%] relative">
                {message.role === "system" && (
                  <div className="w-8 mr-2 mt-1 flex items-center justify-center">
                    {message.content.includes("SageBase captured") ? (
                      <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                        <SageBaseLogo className="h-4 w-4" />
                      </div>
                    ) : (
                      <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center">
                        <Info className="h-3.5 w-3.5 text-gray-500" />
                      </div>
                    )}
                  </div>
                )}
                {message.role === "assistant" && (
                  <Avatar className="h-8 w-8 mr-2 mt-1 bg-blue-100">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {message.avatar}
                    </AvatarFallback>
                  </Avatar>
                )}
                <ContextMenu>
                  <ContextMenuTrigger>
                    <div
                      ref={
                        message.id === "assistant-answer"
                          ? assistantMessageRef
                          : undefined
                      }
                      className={`rounded-lg p-3 selectable-message relative ${
                        message.role === "user"
                          ? "bg-blue-600 text-white"
                          : message.role === "assistant"
                          ? "bg-gray-100 text-gray-800"
                          : message.content.includes("SageBase captured")
                          ? "bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 text-gray-900"
                          : "bg-gray-100 text-gray-700"
                      } ${
                        selectedMessageIds.includes(message.id)
                          ? "message-selected"
                          : ""
                      } ${message.highlighted ? "highlight-message" : ""}`}
                      onMouseUp={(e) => {
                        // Only handle text selection if the user has dragged
                        const selection = window.getSelection();
                        if (selection && selection.toString()) {
                          handleTextSelection(message.id);
                        }
                      }}
                      onClick={(e) => {
                        // Special handling for system message with document link
                        if (
                          message.role === "system" &&
                          message.content.includes("SageBase has updated")
                        ) {
                          // Don't select the message, let the link handle the click
                          return;
                        }
                        handleMessageSelect(message, e);
                      }}
                    >
                      {/* Add checkbox inside the message */}
                      <div className="flex items-start gap-3">
                        {message.role !== "system" && (
                          <div
                            className={`mt-0.5 h-4 w-4 rounded border ${
                              selectedMessageIds.includes(message.id)
                                ? "bg-blue-500 border-blue-500"
                                : message.role === "user"
                                ? "bg-white/20 border-white/40"
                                : "bg-white border-gray-300"
                            } flex items-center justify-center cursor-pointer flex-shrink-0`}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMessageSelect(message, e);
                            }}
                          >
                            {selectedMessageIds.includes(message.id) && (
                              <Check
                                className={`h-2.5 w-2.5 ${
                                  message.role === "user"
                                    ? "text-white"
                                    : "text-white"
                                }`}
                              />
                            )}
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          {message.role === "assistant" && (
                            <div className="text-sm font-medium text-blue-700 mb-1">
                              {message.sender}
                            </div>
                          )}
                          <div
                            className={`text-sm chat-message-content ${
                              message.role === "user"
                                ? "text-white"
                                : message.role === "assistant"
                                ? "text-gray-800"
                                : message.content.includes("SageBase captured")
                                ? "text-gray-900"
                                : "text-gray-700"
                            }`}
                            onClick={(e) => {
                              // If this is a system message with a link, handle the link click
                              if (
                                message.role === "system" &&
                                message.content.includes(
                                  "SageBase has updated"
                                ) &&
                                (e.target as HTMLElement).tagName === "A"
                              ) {
                                e.preventDefault();
                                handleDocumentLinkClick(
                                  e as unknown as React.MouseEvent<HTMLAnchorElement>
                                );
                              }
                            }}
                          >
                            <HtmlResponse
                              content={message.content}
                              className={
                                message.role === "user"
                                  ? "text-white"
                                  : message.content.includes(
                                      "SageBase captured"
                                    )
                                  ? "text-gray-900"
                                  : "text-gray-800"
                              }
                            />
                          </div>
                          <div
                            className={`text-xs mt-1 ${
                              message.role === "user"
                                ? "text-white/70"
                                : message.role === "assistant"
                                ? "text-blue-400"
                                : message.content.includes("SageBase captured")
                                ? "text-blue-500"
                                : "text-gray-500"
                            }`}
                          >
                            {message.timestamp.toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                  </ContextMenuTrigger>
                  <ContextMenuContent className="w-64">
                    <ContextMenuItem
                      onClick={() =>
                        navigator.clipboard.writeText(
                          message.content.replace(/<[^>]*>/g, "")
                        )
                      }
                    >
                      <Copy className="mr-2 h-4 w-4" />
                      Copy text
                    </ContextMenuItem>
                    <ContextMenuSeparator />
                    <ContextMenuSub>
                      <ContextMenuSubTrigger>
                        <Share2 className="mr-2 h-4 w-4" />
                        Forward to SageBase
                      </ContextMenuSubTrigger>
                      <ContextMenuSubContent className="w-48">
                        <ContextMenuItem onClick={handleForwardToSageBase}>
                          <FileText className="mr-2 h-4 w-4" />
                          Add to document
                        </ContextMenuItem>
                        <ContextMenuItem onClick={handleForwardToSageBase}>
                          <Database className="mr-2 h-4 w-4" />
                          Create knowledge item
                        </ContextMenuItem>
                      </ContextMenuSubContent>
                    </ContextMenuSub>
                    <ContextMenuItem>
                      <BookOpen className="mr-2 h-4 w-4" />
                      Find related documents
                    </ContextMenuItem>
                  </ContextMenuContent>
                </ContextMenu>
                {message.role === "user" && (
                  <Avatar className="h-8 w-8 ml-2 mt-1">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      U
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            </div>
          ))}
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex">
                <Avatar className="h-8 w-8 mr-2 mt-1 bg-blue-100">
                  <AvatarFallback className="bg-blue-100 text-blue-600">
                    A
                  </AvatarFallback>
                </Avatar>
                <div className="bg-white rounded-lg p-3 shadow-sm flex items-center">
                  <div className="flex space-x-1">
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
                    <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Actions Bar - Only visible when messages are selected */}
        {selectedMessageIds.length > 0 && (
          <div className="bg-gray-100 border-t border-gray-200 p-2 flex items-center justify-between">
            <div className="text-sm text-gray-600 ml-2">
              {selectedMessageIds.length === 1
                ? "1 message selected"
                : `${selectedMessageIds.length} messages selected`}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="text-gray-700 border-gray-300"
                onClick={clearSelections}
              >
                Cancel
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-gray-700 border-gray-300"
                onClick={() => navigator.clipboard.writeText(selectedText)}
              >
                <Copy className="h-3.5 w-3.5 mr-1" /> Copy
              </Button>
              <Button
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={handleForwardToSageBase}
              >
                <Share2 className="h-3.5 w-3.5 mr-1" /> Forward to SageBase
              </Button>
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="p-3 border-t border-gray-200 bg-white">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-gray-500"
            >
              <Paperclip className="h-5 w-5" />
            </Button>
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type a new message"
              className="flex-1 mx-2 border-gray-300 focus-visible:ring-blue-600"
              disabled={isTyping}
            />
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-gray-500 mr-1"
            >
              <Smile className="h-5 w-5" />
            </Button>
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isTyping}
              size="icon"
              className="h-9 w-9 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Forward to SageBase Dialog */}
      <Dialog open={showForwardDialog} onOpenChange={handleDialogChange}>
        <DialogContent
          className="sm:max-w-4xl"
          onClick={(e) => e.stopPropagation()}
        >
          <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-gray-600" />
              <div>
                <DialogTitle className="text-lg font-semibold">
                  Forward Chat Messages
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600">
                  Select messages from the conversation to forward to your
                  document database
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {/* Question & Answer Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Question & Answer
              </h3>
            </div>

            <div className="max-h-96 overflow-y-auto space-y-3">
              {(() => {
                const qaItems: Array<{ question: Message; answer: Message }> =
                  [];
                let currentQuestion: Message | null = null;

                // Process selected messages to find Q&A pairs
                selectedMessageIds.forEach((id) => {
                  const message = messages.find((m) => m.id === id);
                  if (!message) return;

                  if (message.role === "user") {
                    currentQuestion = message;
                  } else if (message.role === "assistant" && currentQuestion) {
                    qaItems.push({
                      question: currentQuestion,
                      answer: message,
                    });
                    currentQuestion = null;
                  }
                });

                if (qaItems.length === 0) {
                  return (
                    <div className="p-8 text-center border-2 border-dashed border-gray-200 rounded-lg">
                      <MessageSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-600 mb-1">
                        No Q&A pairs selected
                      </p>
                      <p className="text-xs text-gray-500">
                        Select question and answer messages to forward them
                      </p>
                    </div>
                  );
                }

                return qaItems.map((item, index) => (
                  <div
                    key={`qa-${index}`}
                    className="border border-gray-200 rounded-lg p-4 space-y-4 bg-white"
                  >
                    {/* Tags */}
                    <div className="flex items-center space-x-2">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                        Development
                      </span>
                      <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                        Error Resolution
                      </span>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        Babel
                      </span>
                    </div>

                    {/* Question Component */}
                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">
                              Q
                            </span>
                          </div>
                          <span className="text-sm font-semibold text-blue-900">
                            Question
                          </span>
                        </div>
                        <span className="text-xs text-blue-600">
                          {item.question.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                      <p className="text-sm text-blue-900 leading-relaxed">
                        {item.question.content}
                      </p>
                    </div>

                    {/* Solution Component */}
                    <div className="bg-green-50 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                          <span className="text-sm font-semibold text-green-900">
                            Solution by {item.answer.sender}
                          </span>
                        </div>
                        <span className="text-xs text-green-600">
                          {item.answer.timestamp.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                      <div className="text-sm text-green-900 leading-relaxed">
                        <HtmlResponse
                          content={item.answer.content}
                          className="text-green-900"
                        />
                      </div>
                    </div>

                    {/* Modify Button */}
                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-gray-600 border-gray-300"
                      >
                        <span className="mr-1">✏️</span>
                        Modify
                      </Button>
                    </div>
                  </div>
                ));
              })()}
            </div>
          </div>

          {/* Additional Context */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-900">
              Additional Context (Optional)
            </h4>
            <textarea
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
              rows={3}
              placeholder="Add any additional context, notes, or summary about these messages..."
            />
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t">
            <span className="text-sm text-gray-500">
              {selectedMessageIds.length} of {messages.length} messages selected
            </span>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowForwardDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmForward}
                className="bg-primary hover:bg-primary/90 text-white"
                disabled={isForwarding || selectedMessageIds.length === 0}
              >
                {isForwarding ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <span className="mr-2">📤</span>
                    Forward to SageBase
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
