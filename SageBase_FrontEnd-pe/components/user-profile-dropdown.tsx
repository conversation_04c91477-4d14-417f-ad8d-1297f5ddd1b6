"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { useUserInfo } from "@/hooks/use-user-info";
import { ChevronDown, User, Settings, LogOut, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

export default function UserProfileDropdown() {
  const { user, signOut } = useAuth();
  const { userInfo, refreshUserInfo } = useUserInfo(user?.email);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Get user role from userInfo
  const userRole = userInfo?.role || "User";

  // Update role when userInfo changes - no need for useEffect since we're using userInfo directly

  const handleDropdownToggle = async () => {
    if (!isOpen) {
      // Refresh user info when opening dropdown to get latest role
      console.log("🔄 Refreshing user info for latest role...");
      await refreshUserInfo();
    }
    setIsOpen(!isOpen);
  };

  const handleSignOut = async () => {
    console.log("🚪 UserProfileDropdown: Sign out button clicked");

    setIsLoggingOut(true);
    try {
      console.log(
        "🚪 UserProfileDropdown: Calling signOut from auth context..."
      );
      await signOut();
      console.log("🚪 UserProfileDropdown: Sign out completed successfully");
    } catch (error) {
      console.error("🚪 UserProfileDropdown: Error during sign out:", error);
    } finally {
      setIsLoggingOut(false);
      setIsOpen(false);
    }
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user) return "U";

    // If user has first and last name
    if (user.first_name && user.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
    }

    // If user has only first name
    if (user.first_name) {
      return user.first_name[0].toUpperCase();
    }

    // Fallback to email
    if (user.email) {
      return user.email.charAt(0).toUpperCase();
    }

    return "U";
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        onClick={handleDropdownToggle}
        className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
      >
        <span className="sr-only">Open user menu</span>
        <Avatar className="h-8 w-8">
          <AvatarFallback className="bg-emerald-100 text-emerald-800">
            {getUserInitials()}
          </AvatarFallback>
        </Avatar>
        <ChevronDown className="ml-1 h-4 w-4 text-gray-400" />
      </Button>

      {isOpen && (
        <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 z-10">
          <div className="px-4 py-3 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-900">
              {user?.first_name && user?.last_name
                ? `${user.first_name} ${user.last_name}`
                : user?.email || "User"}
            </p>
            <p className="text-xs text-gray-500 mt-1">{userRole || "User"}</p>
          </div>
          <Link
            href="/profile"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            onClick={() => setIsOpen(false)}
          >
            <User className="mr-2 h-4 w-4 text-gray-500" />
            Your Profile
          </Link>
          <Link
            href="/settings"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            onClick={() => setIsOpen(false)}
          >
            <Settings className="mr-2 h-4 w-4 text-gray-500" />
            Settings
          </Link>
          {userRole === "ADMIN" && (
            <Link
              href="/users"
              className="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 flex items-center font-semibold"
              onClick={() => setIsOpen(false)}
            >
              <Users className="mr-2 h-4 w-4 text-primary-500" />
              Manage Users
            </Link>
          )}
          <Button
            variant="ghost"
            onClick={handleSignOut}
            disabled={isLoggingOut}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
          >
            <LogOut className="mr-2 h-4 w-4 text-gray-500" />
            {isLoggingOut ? "Signing out..." : "Sign out"}
          </Button>
        </div>
      )}
    </div>
  );
}
