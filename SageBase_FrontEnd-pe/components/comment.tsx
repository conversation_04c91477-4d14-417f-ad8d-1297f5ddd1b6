"use client";

import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ThumbsUp, 
  ThumbsDown, 
  Edit2, 
  Trash2, 
  Check, 
  X,
  MoreHorizontal,
  MessageCircle,
  Clock,
  User,
  Heart,
  Reply
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import type { Comment as CommentType } from '@/types/comments';

interface CommentProps {
  comment: CommentType;
  currentUserId?: string;
  userVote?: 'up' | 'down' | 'none';
  onVote: (commentId: string, voteType: 'up' | 'down') => Promise<void>;
  onEdit: (commentId: string, newContent: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  isVoting?: boolean;
  isEditing?: boolean;
  isDeleting?: boolean;
}

export default function Comment({
  comment,
  currentUserId,
  userVote = 'none',
  onVote,
  onEdit,
  onDelete,
  isVoting = false,
  isEditing = false,
  isDeleting = false,
}: CommentProps) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const isOwnComment = currentUserId === comment.author.id;
  const hasVoted = userVote !== 'none';

  const handleStartEdit = () => {
    setEditContent(comment.content);
    setIsEditMode(true);
  };

  const handleCancelEdit = () => {
    setEditContent(comment.content);
    setIsEditMode(false);
  };

  const handleSaveEdit = async () => {
    if (editContent.trim() === comment.content.trim()) {
      setIsEditMode(false);
      return;
    }

    if (!editContent.trim()) {
      return;
    }

    try {
      await onEdit(comment.id, editContent.trim());
      setIsEditMode(false);
    } catch (error) {
      console.error('Failed to edit comment:', error);
    }
  };

  const handleDelete = async () => {
    try {
      await onDelete(comment.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  const handleVote = async (voteType: 'up' | 'down') => {
    try {
      await onVote(comment.id, voteType);
    } catch (error) {
      console.error('Failed to vote:', error);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <>
      <Card className="group relative overflow-hidden border-0 bg-gradient-to-r from-white to-gray-50/50 hover:from-white hover:to-gray-100/50 transition-all duration-300 shadow-sm hover:shadow-md rounded-xl">
        <CardContent className="p-6">
          <div className="flex space-x-4">
            {/* Enhanced Avatar */}
            <div className="flex-shrink-0">
              <Avatar className="h-10 w-10 ring-2 ring-white shadow-sm">
                <AvatarImage src={comment.author.avatar} alt={comment.author.name} />
                <AvatarFallback className="text-sm font-semibold bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 text-white">
                  {getInitials(comment.author.name)}
                </AvatarFallback>
              </Avatar>
            </div>

            {/* Comment Content */}
            <div className="flex-1 min-w-0">
              {/* Enhanced Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-sm text-gray-900">
                      {comment.author.name}
                    </span>
                    {isOwnComment && (
                      <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-blue-50 text-blue-600 border-blue-200">
                        You
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>{comment.date} at {comment.timestamp}</span>
                    {comment.is_edited && (
                      <>
                        <span>•</span>
                        <span className="italic text-amber-600">edited</span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Comment Content */}
              {isEditMode ? (
                <div className="space-y-4">
                  <Textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="min-h-[100px] resize-none border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                    placeholder="Edit your comment..."
                    disabled={isEditing}
                  />
                  <div className="flex space-x-3">
                    <Button
                      size="sm"
                      onClick={handleSaveEdit}
                      disabled={isEditing || !editContent.trim()}
                      className="h-9 px-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-sm"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      {isEditing ? 'Saving...' : 'Save Changes'}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isEditing}
                      className="h-9 px-4 border-gray-300 hover:bg-gray-50"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap bg-white rounded-lg p-4 border border-gray-100 shadow-sm">
                    {comment.content}
                  </div>

                  {/* Enhanced Actions Bar */}
                  <div className="flex items-center justify-between pt-2">
                    {/* Vote Section */}
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant={userVote === 'up' ? 'default' : 'ghost'}
                        onClick={() => handleVote('up')}
                        disabled={isVoting}
                        className={`h-8 px-3 rounded-full transition-all duration-200 ${
                          userVote === 'up' 
                            ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-md hover:from-green-600 hover:to-emerald-600' 
                            : 'hover:bg-green-50 hover:text-green-600 border-gray-200'
                        }`}
                      >
                        <ThumbsUp className="h-4 w-4 mr-1.5" />
                        <span className="text-xs font-medium">{comment.votes.upvotes}</span>
                      </Button>
                      
                      <Button
                        size="sm"
                        variant={userVote === 'down' ? 'default' : 'ghost'}
                        onClick={() => handleVote('down')}
                        disabled={isVoting}
                        className={`h-8 px-3 rounded-full transition-all duration-200 ${
                          userVote === 'down' 
                            ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-md hover:from-red-600 hover:to-pink-600' 
                            : 'hover:bg-red-50 hover:text-red-600 border-gray-200'
                        }`}
                      >
                        <ThumbsDown className="h-4 w-4 mr-1.5" />
                        <span className="text-xs font-medium">{comment.votes.downvotes}</span>
                      </Button>

                      {/* Vote Summary */}
                      <div className="flex items-center space-x-1 text-xs text-gray-500 ml-2">
                        <Heart className="h-3 w-3" />
                        <span>{comment.votes.upvotes + comment.votes.downvotes} votes</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-3 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      >
                        <Reply className="h-4 w-4 mr-1.5" />
                        <span className="text-xs">Reply</span>
                      </Button>

                      {/* Edit/Delete Menu (Only for own comments) */}
                      {isOwnComment && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              className="h-8 w-8 p-0 rounded-full hover:bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-40 rounded-lg shadow-lg border-gray-200">
                            <DropdownMenuItem 
                              onClick={handleStartEdit} 
                              disabled={isEditing}
                              className="flex items-center space-x-2 rounded-md"
                            >
                              <Edit2 className="h-4 w-4" />
                              <span>Edit Comment</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => setShowDeleteDialog(true)}
                              disabled={isDeleting}
                              className="flex items-center space-x-2 text-red-600 focus:text-red-600 rounded-md"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span>Delete Comment</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
