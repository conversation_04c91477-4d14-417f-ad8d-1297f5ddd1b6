"use client"

import type React from "react"
import { useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getSourceIcon } from "@/lib/source-icon"
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context"

interface UnifiedSearchProps {
  onSearch: (query: string, filters: string[]) => void
}

const UnifiedSearch: React.FC<UnifiedSearchProps> = ({ onSearch }) => {
  const [query, setQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const { getConnectedPlatforms, isPlatformConnected } = useConnectedPlatforms()
  const connectedPlatforms = getConnectedPlatforms()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value)
  }

  const handleSearchClick = () => {
    onSearch(query, activeFilters)
  }

  const toggleFilter = (filter: string) => {
    if (activeFilters.includes(filter)) {
      setActiveFilters(activeFilters.filter((f) => f !== filter))
    } else {
      setActiveFilters([...activeFilters, filter])
    }
  }

  return (
    <div className="w-full">
      <div className="flex items-center space-x-2">
        <Input type="text" placeholder="Search..." value={query} onChange={handleInputChange} className="flex-1" />
        <Button onClick={handleSearchClick}>
          <Search className="mr-2 h-4 w-4" />
          Search
        </Button>
      </div>

      <div className="mt-4 space-x-2">
        {connectedPlatforms.map((platform) => (
          <Badge
            key={platform.id}
            variant={activeFilters.includes(platform.id) ? "default" : "outline"}
            className={`cursor-pointer ${
              activeFilters.includes(platform.id) ? "bg-primary-100 text-primary-800 hover:bg-primary-200" : ""
            }`}
            onClick={() => toggleFilter(platform.id)}
          >
            {getSourceIcon(platform.id)}
            {platform.name}
          </Badge>
        ))}
      </div>
    </div>
  )
}

export default UnifiedSearch
