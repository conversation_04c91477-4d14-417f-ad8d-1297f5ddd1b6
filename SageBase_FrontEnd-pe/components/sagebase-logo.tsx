import Image from "next/image"

interface SageBaseLogoProps {
  className?: string
  width?: number
  height?: number
}

export default function SageBaseLogo({ className = "", width = 120, height = 40 }: SageBaseLogoProps) {
  return (
    <Image
      src="/images/sagebase-purple-full.svg"
      alt="SageBase"
      width={width}
      height={height}
      className={`${className}`}
      priority
    />
  )
}
