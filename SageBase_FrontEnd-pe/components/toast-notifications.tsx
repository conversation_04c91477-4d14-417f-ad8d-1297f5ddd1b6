"use client";
import { useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { useNotifications } from "@/hooks/use-notifications";
import { AlertCircle, CheckCircle, Info, X } from "lucide-react";

interface ToastNotificationsProps {
  userEmail: string;
}

const getToastIcon = (level: string) => {
  switch (level) {
    case "high":
      return <AlertCircle className="h-4 w-4" />;
    case "medium":
      return <Info className="h-4 w-4" />;
    case "low":
      return <CheckCircle className="h-4 w-4" />;
    default:
      return <Info className="h-4 w-4" />;
  }
};

const getToastVariant = (level: string) => {
  switch (level) {
    case "high":
      return "destructive";
    case "medium":
      return "default";
    case "low":
      return "default";
    default:
      return "default";
  }
};

export function ToastNotifications({ userEmail }: ToastNotificationsProps) {
  const { toast } = useToast();
  const { notifications } = useNotifications(userEmail);

  // Show toast for new notifications
  useEffect(() => {
    if (notifications.length > 0) {
      const latestNotification = notifications[0];

      // Only show toast if notification is very recent (within last 5 seconds)
      const notificationTime = new Date(latestNotification.timestamp).getTime();
      const now = Date.now();
      const isRecent = now - notificationTime < 5000;

      if (isRecent) {
        toast({
          title: latestNotification.title,
          description: latestNotification.message,
          variant: getToastVariant(latestNotification.level),
          duration: 5000,
          action: latestNotification.data ? (
            <div className="flex items-center gap-2">
              {getToastIcon(latestNotification.level)}
              <span className="text-xs opacity-70">
                {Object.keys(latestNotification.data).length} data points
              </span>
            </div>
          ) : undefined,
        });
      }
    }
  }, [notifications, toast]);

  return null; // This component doesn't render anything visible
}
