import Image from "next/image";

interface SageBaseSLogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export default function SageBaseSLogo({
  className = "",
  width = 32,
  height = 32,
}: SageBaseSLogoProps) {
  return (
    <Image
      src="/images/branding/sageBaseLogo_s.svg"
      alt="SageBase"
      width={width}
      height={height}
      className={`${className}`}
      priority
    />
  );
}
