"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { Bell, Menu, X, Archive, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import UserProfileDropdown from "./user-profile-dropdown";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import NotificationPopover from "./notification-popover";
import { Notification } from "@/types/notifications";
import { useSidebarRefresh } from "@/contexts/sidebar-refresh-context";

export default function TopNavigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const { refreshSidebar } = useSidebarRefresh();
  // Note: Q&A notifications are now handled by NotificationPopover component
  // const [dynamicNotifications, setDynamicNotifications] = useState<Notification[]>([]);
  // const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const [successMessage, setSuccessMessage] = useState<{
    show: boolean;
    qaId: string;
    title: string;
    documentId: string;
  } | null>(null);

  // Note: Q&A notifications are now handled by the NotificationPopover component
  // using the useQAApprovals hook, so we don't need to load them here anymore
  useEffect(() => {
    // Legacy notification loading removed - now handled by NotificationPopover
    console.log(
      "TopNavigation: Q&A notifications now handled by NotificationPopover"
    );
  }, []);

  // Note: Q&A approval/rejection is now handled by the NotificationPopover component
  // using the useQAApprovals hook with the correct API endpoints

  return (
    <header className="bg-white/80 backdrop-blur-md border-b border-white/20 sticky top-0 z-30 shadow-sm">
      <div className="px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-white/90 via-white/95 to-white/90 backdrop-blur-sm">
        <div className="flex justify-between h-16">
          {/* Mobile Menu Button - Only visible on mobile */}
          <div className="flex items-center lg:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileMenuOpen(true)}
              className="-ml-1"
            >
              <Menu className="h-6 w-6" />
              <span className="sr-only">Open mobile menu</span>
            </Button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:justify-between lg:flex-1">
            {/* Right Navigation */}
            <div className="ml-auto flex items-center">
              {/* Notification Button */}
              <NotificationPopover
                isOpen={isNotificationOpen}
                onOpenChange={setIsNotificationOpen}
                onViewHistory={() => {
                  router.push("/qa_notifs");
                  setIsNotificationOpen(false);
                }}
                onRefreshSidebar={refreshSidebar}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="relative p-1 rounded-full text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">View Q&A notifications</span>
                  <Bell className="h-6 w-6" />
                  {unreadCount > 0 && (
                    <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white" />
                  )}
                </Button>
              </NotificationPopover>

              {/* Profile Dropdown */}
              <UserProfileDropdown />
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex items-center justify-end flex-1 lg:hidden">
            {/* Mobile Notification Button */}
            <Button
              variant="ghost"
              size="icon"
              className="ml-2 relative p-1 rounded-full text-gray-400 hover:text-gray-500"
              onClick={() => setIsNotificationOpen(true)}
            >
              <span className="sr-only">View notifications</span>
              <Bell className="h-6 w-6" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white" />
              )}
            </Button>

            {/* Mobile Profile */}
            <div className="ml-2">
              <UserProfileDropdown />
            </div>
          </div>
        </div>
      </div>

      {/* Success Message Toast */}
      {successMessage?.show && (
        <div className="fixed bottom-4 right-4 z-50 bg-white border border-green-200 rounded-lg shadow-lg p-4 max-w-sm animate-in slide-in-from-bottom">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Successfully stored in Knowledge Base
              </h4>
              <p className="text-sm text-gray-600 mb-2">
                "{successMessage.title.substring(0, 50)}..." has been added to
                your knowledge base.
              </p>
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  onClick={() => {
                    router.push(`/documents/${successMessage.documentId}`);
                    setSuccessMessage(null);
                  }}
                  className="bg-primary hover:bg-primary/90 text-white text-xs"
                >
                  View Document
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setSuccessMessage(null)}
                  className="text-gray-500 text-xs"
                >
                  Dismiss
                </Button>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSuccessMessage(null)}
              className="p-1 h-6 w-6 text-gray-400 hover:text-gray-600"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
    </header>
  );
}
