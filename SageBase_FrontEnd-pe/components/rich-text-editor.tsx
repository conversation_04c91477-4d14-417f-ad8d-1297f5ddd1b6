"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  LinkIcon,
  ImageIcon,
  Table,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  ChevronDown,
  Code,
  Quote,
  Minus,
  CheckSquare,
  Type,
  Heading1,
  Heading2,
  Heading3,
  PenTool,
  X,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import DrawingToolbar from "./drawing-toolbar"
import DrawingCanvas from "./drawing-canvas"

type DrawingTool =
  | "select"
  | "hand"
  | "rectangle"
  | "diamond"
  | "circle"
  | "arrow"
  | "line"
  | "link"
  | "text"
  | "image"
  | "eraser"
  | "group"

type RichTextEditorProps = {
  initialValue: string
  onChange: (value: string) => void
  placeholder?: string
}

export default function RichTextEditor({ initialValue, onChange, placeholder }: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [isFocused, setIsFocused] = useState(false)
  const [showDrawingTools, setShowDrawingTools] = useState(false)
  const [currentDrawingTool, setCurrentDrawingTool] = useState<DrawingTool>("select")

  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = initialValue || ""
    }
  }, [initialValue])

  useEffect(() => {
    const handleToolChange = (e: Event) => {
      const customEvent = e as CustomEvent
      setCurrentDrawingTool(customEvent.detail as DrawingTool)
    }

    document.addEventListener("tool-change", handleToolChange)
    return () => {
      document.removeEventListener("tool-change", handleToolChange)
    }
  }, [])

  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML)
    }
  }

  const execCommand = (command: string, value = "") => {
    document.execCommand(command, false, value)
    handleInput()
    editorRef.current?.focus()
  }

  const formatBlock = (block: string) => {
    execCommand("formatBlock", block)
  }

  const insertTable = () => {
    const table = `
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Header 1</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Header 2</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Header 3</th>
        </tr>
        <tr>
          <td style="border: 1px solid #ddd; padding: 8px;">Row 1, Cell 1</td>
          <td style="border: 1px solid #ddd; padding: 8px;">Row 1, Cell 2</td>
          <td style="border: 1px solid #ddd; padding: 8px;">Row 1, Cell 3</td>
        </tr>
        <tr>
          <td style="border: 1px solid #ddd; padding: 8px;">Row 2, Cell 1</td>
          <td style="border: 1px solid #ddd; padding: 8px;">Row 2, Cell 2</td>
          <td style="border: 1px solid #ddd; padding: 8px;">Row 2, Cell 3</td>
        </tr>
      </table>
    `
    execCommand("insertHTML", table)
  }

  const insertImage = () => {
    const url = prompt("Enter image URL:")
    if (url) {
      execCommand("insertImage", url)
    }
  }

  const createLink = () => {
    const url = prompt("Enter link URL:")
    if (url) {
      execCommand("createLink", url)
    }
  }

  const insertHorizontalRule = () => {
    execCommand("insertHorizontalRule")
  }

  const toggleDrawingTools = () => {
    setShowDrawingTools(!showDrawingTools)
  }

  return (
    <div className="border border-gray-200 rounded-md overflow-hidden">
      {/* Editor Toolbar */}
      <div className="bg-white border-b border-gray-200 px-2 py-1 flex flex-wrap items-center gap-0.5">
        {/* Paragraph Style Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 px-2 text-sm font-normal">
              <span>Paragraph</span>
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-48">
            <DropdownMenuItem onClick={() => formatBlock("p")}>
              <Type className="mr-2 h-4 w-4" />
              <span>Paragraph</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock("h1")}>
              <Heading1 className="mr-2 h-4 w-4" />
              <span>Heading 1</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock("h2")}>
              <Heading2 className="mr-2 h-4 w-4" />
              <span>Heading 2</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock("h3")}>
              <Heading3 className="mr-2 h-4 w-4" />
              <span>Heading 3</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock("pre")}>
              <Code className="mr-2 h-4 w-4" />
              <span>Code Block</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => formatBlock("blockquote")}>
              <Quote className="mr-2 h-4 w-4" />
              <span>Quote</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="h-6 w-px bg-gray-200 mx-1"></div>

        {/* Text Formatting */}
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("bold")}>
          <Bold className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("italic")}>
          <Italic className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("underline")}>
          <Underline className="h-4 w-4" />
        </Button>

        <div className="h-6 w-px bg-gray-200 mx-1"></div>

        {/* Lists */}
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("insertUnorderedList")}>
          <List className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("insertOrderedList")}>
          <ListOrdered className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("insertHTML", "<div><input type='checkbox'> Task</div>")}
        >
          <CheckSquare className="h-4 w-4" />
        </Button>

        <div className="h-6 w-px bg-gray-200 mx-1"></div>

        {/* Alignment */}
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("justifyLeft")}>
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("justifyCenter")}>
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("justifyRight")}>
          <AlignRight className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => execCommand("justifyFull")}>
          <AlignJustify className="h-4 w-4" />
        </Button>

        <div className="h-6 w-px bg-gray-200 mx-1"></div>

        {/* Insert */}
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={createLink}>
          <LinkIcon className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={insertImage}>
          <ImageIcon className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={insertTable}>
          <Table className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={insertHorizontalRule}>
          <Minus className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("insertHTML", "<code>Code</code>")}
        >
          <Code className="h-4 w-4" />
        </Button>

        <div className="h-6 w-px bg-gray-200 mx-1"></div>

        {/* Drawing Tools Toggle */}
        <Button
          variant={showDrawingTools ? "secondary" : "ghost"}
          size="sm"
          className={`h-8 w-8 p-0 ${showDrawingTools ? "bg-purple-100 text-purple-800" : ""}`}
          onClick={toggleDrawingTools}
          title="Drawing Tools"
        >
          <PenTool className="h-4 w-4" />
        </Button>
      </div>

      {/* Drawing Tools */}
      {showDrawingTools && (
        <div className="border-b border-gray-200">
          <div className="flex justify-between items-center px-2 py-1 bg-gray-50">
            <h3 className="text-sm font-medium">Drawing Tools</h3>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => setShowDrawingTools(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DrawingToolbar onToolChange={setCurrentDrawingTool} currentTool={currentDrawingTool} />
          <DrawingCanvas currentTool={currentDrawingTool} />
        </div>
      )}

      {/* Editor Content */}
      <div
        ref={editorRef}
        contentEditable
        className={`min-h-[300px] p-4 focus:outline-none ${isFocused ? "ring-2 ring-emerald-500 ring-inset" : ""}`}
        onInput={handleInput}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        dangerouslySetInnerHTML={{ __html: initialValue }}
      />
    </div>
  )
}
