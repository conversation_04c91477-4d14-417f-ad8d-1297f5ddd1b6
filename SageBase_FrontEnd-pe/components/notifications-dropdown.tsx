"use client";
import React, { useState, useEffect } from "react";
import {
  Bell,
  Check,
  X,
  AlertCircle,
  Info,
  CheckCircle,
  Edit,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useNotifications, Notification } from "@/hooks/use-notifications";
import { useQAApprovals } from "@/hooks/use-qa-approvals";
import { formatDistanceToNow } from "date-fns";
import QAEditDialog from "@/components/qa-edit-dialog";
import { PendingQAApproval, QAEditData } from "@/services/qa-approval-api";

interface NotificationsDropdownProps {
  userEmail: string;
}

const getNotificationIcon = (level: string) => {
  switch (level) {
    case "high":
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    case "medium":
      return <Info className="h-4 w-4 text-blue-500" />;
    case "low":
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    default:
      return <Info className="h-4 w-4 text-gray-500" />;
  }
};

const getNotificationStyle = (level: string) => {
  switch (level) {
    case "high":
      return "border-l-4 border-l-red-500 bg-red-50";
    case "medium":
      return "border-l-4 border-l-blue-500 bg-blue-50";
    case "low":
      return "border-l-4 border-l-green-500 bg-green-50";
    default:
      return "border-l-4 border-l-gray-500 bg-gray-50";
  }
};

export function NotificationsDropdown({
  userEmail,
}: NotificationsDropdownProps) {
  const {
    notifications: wsNotifications,
    isConnected,
    connectionError,
    clearNotifications,
    removeNotification,
    reconnect,
  } = useNotifications(userEmail);

  const {
    pendingApprovals,
    isLoading: qaLoading,
    approveQA,
    rejectQA,
    editQA,
  } = useQAApprovals();

  // Convert pending Q&A approvals to notification format
  const qaNotifications = pendingApprovals.map((qa) => ({
    id: qa.id,
    type: "qa_approval" as const,
    title: "Q&A Pending Approval",
    message: `Question: ${qa.question.content.substring(0, 100)}...`,
    level: "medium" as const,
    timestamp: qa.created_at,
    data: {
      question: qa.question.content,
      answer: qa.answer.content,
      questionAuthor: qa.question.author.name,
      answerAuthor: qa.answer.author.name,
      questionTime: qa.question.date,
      answerTime: qa.answer.date,
      platform: "knowledge-base",
      source: qa.source,
      channel: qa.channel,
      tags: qa.question.tags || [],
      verified: qa.answer.isVerified || false,
    },
  }));

  // Combine WebSocket notifications with Q&A notifications
  const notifications = [...qaNotifications, ...wsNotifications];

  const [isOpen, setIsOpen] = useState(false);

  // State for edit dialog
  const [editingQA, setEditingQA] = useState<PendingQAApproval | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Auto-close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest("[data-notification-dropdown]")) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const unreadCount = notifications.length;

  const handleNotificationClick = (notification: Notification) => {
    // Handle notification click - could navigate to specific page
    console.log("Notification clicked:", notification);

    // For Q&A approvals, we don't remove them on click - they need explicit approve/reject
    if (notification.type !== "qa_approval" && notification.id) {
      removeNotification(notification.id);
    }
  };

  const handleApproveQA = (qaId: string | undefined) => {
    if (qaId) {
      approveQA(qaId);
    }
  };

  const handleRejectQA = (qaId: string | undefined) => {
    if (qaId) {
      rejectQA(qaId, "Rejected via dropdown");
    }
  };

  const handleEditQA = async (
    qaId: string,
    projectId: string,
    editData: QAEditData
  ) => {
    try {
      await editQA(qaId, projectId, editData);
      setShowEditDialog(false);
      setEditingQA(null);
    } catch (error) {
      console.error("Error editing QA:", error);
    }
  };

  const openEditDialog = (qa: PendingQAApproval) => {
    setEditingQA(qa);
    setShowEditDialog(true);
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return "Just now";
    }
  };

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="relative p-2"
            data-notification-dropdown
          >
            <Bell className="h-4 w-4" />
            {unreadCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
              >
                {unreadCount > 99 ? "99+" : unreadCount}
              </Badge>
            )}
            {connectionError && (
              <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full" />
            )}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          align="end"
          className="w-80 max-h-96 overflow-y-auto"
          data-notification-dropdown
        >
          <div className="flex items-center justify-between p-2 border-b">
            <h3 className="text-sm font-semibold">Notifications</h3>
            <div className="flex items-center gap-2">
              {connectionError ? (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={reconnect}
                  className="text-xs"
                >
                  Reconnect
                </Button>
              ) : (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <div
                    className={`h-2 w-2 rounded-full ${
                      isConnected ? "bg-green-500" : "bg-gray-400"
                    }`}
                  />
                  {isConnected ? "Connected" : "Disconnected"}
                </div>
              )}
              {notifications.length > 0 && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={clearNotifications}
                  className="text-xs h-6 px-2"
                >
                  Clear all
                </Button>
              )}
            </div>
          </div>

          {connectionError && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border-b">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                <span>Connection error: {connectionError}</span>
              </div>
            </div>
          )}

          {notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-gray-500">
              {isConnected ? "No notifications yet" : "Connecting..."}
            </div>
          ) : (
            <div className="p-2 space-y-1">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 transition-colors ${getNotificationStyle(
                    notification.level
                  )}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3">
                    {getNotificationIcon(notification.level)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {notification.title}
                        </h4>
                        {notification.type === "qa_approval" ? (
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApproveQA(notification.id);
                              }}
                              disabled={qaLoading}
                              className="h-4 w-4 p-0 text-green-600 hover:text-green-700"
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Find the QA in pending approvals
                                const qa = pendingApprovals.find(
                                  (qa) => qa.id === notification.id
                                );
                                if (qa) {
                                  openEditDialog(qa);
                                }
                              }}
                              disabled={qaLoading}
                              className="h-4 w-4 p-0 text-blue-600 hover:text-blue-700"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRejectQA(notification.id);
                              }}
                              disabled={qaLoading}
                              className="h-4 w-4 p-0 text-red-600 hover:text-red-700"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (notification.id) {
                                removeNotification(notification.id);
                              }
                            }}
                            className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-400">
                          {formatTimestamp(notification.timestamp)}
                        </span>
                        {notification.data && (
                          <Badge variant="outline" className="text-xs">
                            {Object.keys(notification.data).length} data
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {notifications.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={clearNotifications}
                className="text-center text-sm text-gray-500 cursor-pointer"
              >
                Clear all notifications
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* QA Edit Dialog */}
      <QAEditDialog
        qa={editingQA}
        isOpen={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={handleEditQA}
        isLoading={qaLoading}
      />
    </>
  );
}
