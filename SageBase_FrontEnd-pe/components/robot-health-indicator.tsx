"use client"

import { useEffect, useState } from "react"
// We no longer need to import "../styles/robot.css" for this version

interface RobotHealthIndicatorProps {
  answeredQuestions: number
  totalQuestions: number
  className?: string
}

export default function RobotHealthIndicator({
  answeredQuestions,
  totalQuestions,
  className = "",
}: RobotHealthIndicatorProps) {
  const [healthState, setHealthState] = useState<"perfect" | "moderate" | "bad">("perfect")

  useEffect(() => {
    const resolutionRate = totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100 : 0

    if (resolutionRate >= 90) {
      setHealthState("perfect")
    } else if (resolutionRate >= 70) {
      setHealthState("moderate")
    } else {
      setHealthState("bad")
    }
  }, [answeredQuestions, totalQuestions])

  const getHealthMessage = () => {
    switch (healthState) {
      case "perfect":
        return "Excellent! Your knowledge base is performing optimally."
      case "moderate":
        return "Good performance, but there's room for improvement."
      case "bad":
        return "Attention needed! Many questions remain unanswered."
      default:
        return ""
    }
  }

  const getHealthColor = () => {
    // For text color
    switch (healthState) {
      case "perfect":
        return "text-green-600"
      case "moderate":
        return "text-yellow-600"
      case "bad":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  const getIndicatorDotColor = () => {
    // For the status dot
    switch (healthState) {
      case "perfect":
        return "bg-green-500"
      case "moderate":
        return "bg-yellow-500"
      case "bad":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const resolutionRate = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0

  return (
    <div
      className={`flex flex-col md:flex-row items-center justify-between p-6 rounded-lg shadow-sm ${className}`}
      style={{ backgroundColor: "#FEFEFE" }}
    >
      {/* Text Metrics Section */}
      <div className="flex-1 mb-4 md:mb-0 md:mr-6 text-center md:text-left">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Knowledge Base Health</h3>
        <p className={`text-4xl font-medium mb-1 ${getHealthColor()}`}>{resolutionRate}% Resolution Rate</p>
        <p className="text-lg text-gray-600">{getHealthMessage()}</p>
        <div className="mt-3 text-xs text-gray-500">
          {answeredQuestions} of {totalQuestions} questions resolved
        </div>
      </div>

      {/* Static Robot Image Section */}
      <div className="flex-shrink-0">
        <div className="relative w-24 h-24 md:w-32 md:h-32">
          {" "}
          {/* Adjusted size */}
          <img
            src="/images/robot-logo.png"
            alt="Knowledge Base Health Robot"
            className="w-full h-full object-contain"
          />
          <div
            title={`Health: ${healthState}`}
            className={`absolute bottom-1 right-1 w-4 h-4 md:w-5 md:h-5 rounded-full border-2 border-white ${getIndicatorDotColor()}`}
          />
        </div>
      </div>
    </div>
  )
}
