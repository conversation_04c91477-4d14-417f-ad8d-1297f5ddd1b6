import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertCircle } from "lucide-react";

interface GoogleDriveApprovalPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: () => void;
}

export default function GoogleDriveApprovalPopup({
  isOpen,
  onClose,
  onContinue,
}: GoogleDriveApprovalPopupProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-yellow-600" />
            Google Drive Integration Notice
          </DialogTitle>
          <DialogDescription>
            Important information about Google Drive integration status
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-yellow-800 mb-2">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Under Google Approval</span>
            </div>
            <p className="text-sm text-yellow-700">
              SageBase is currently going through Google's approval process for Google Drive integration. 
              This process ensures security and compliance with Google's policies.
            </p>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button 
              variant="outline" 
              onClick={onClose}
            >
              Abort
            </Button>
            <Button 
              onClick={() => {
                onClose();
                onContinue();
              }}
              variant="outline"
              className="border-green-600 text-green-600 hover:bg-green-50"
            >
              Continue Anyway
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
