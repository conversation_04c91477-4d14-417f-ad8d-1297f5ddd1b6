"use client";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Folder, 
  File, 
  FileText, 
  Image, 
  Video, 
  Music, 
  Archive, 
  Code, 
  ChevronRight, 
  ChevronDown,
  Loader2,
  RefreshCw,
  Search,
  Eye,
  EyeOff,
  Save,
  CheckSquare,
  Square
} from "lucide-react";

interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
  parents?: string[];
  size?: string;
  modifiedTime?: string;
  is_included: boolean;
  has_preference: boolean;
}

interface GoogleDriveFolder {
  id: string;
  name: string;
  mimeType: string;
  parents?: string[];
  size?: string;
  modifiedTime?: string;
}

interface FileBrowserProps {
  workspaceId: string;
  onFileSelectionChange?: (selectedFiles: string[]) => void;
}

const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

export default function GoogleDriveFileBrowser({ workspaceId, onFileSelectionChange }: FileBrowserProps) {
  const { user, connectedUserCompany, userRole } = useAuth();
  const [files, setFiles] = useState<GoogleDriveFile[]>([]);
  const [folders, setFolders] = useState<GoogleDriveFolder[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string>("root");
  const [currentFolderName, setCurrentFolderName] = useState<string>("My Drive");
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [localPreferences, setLocalPreferences] = useState<Map<string, boolean>>(new Map());
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [folderContents, setFolderContents] = useState<Map<string, { files: GoogleDriveFile[], folders: GoogleDriveFolder[] }>>(new Map());
  const [loadingFolders, setLoadingFolders] = useState<Set<string>>(new Set());
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{id: string, name: string}>>([
    { id: "root", name: "My Drive" }
  ]);

  // Get file icon based on MIME type
  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes("folder")) return <Folder className="h-4 w-4 text-blue-500" />;
    if (mimeType.includes("image")) return <Image className="h-4 w-4 text-green-500" />;
    if (mimeType.includes("video")) return <Video className="h-4 w-4 text-purple-500" />;
    if (mimeType.includes("audio")) return <Music className="h-4 w-4 text-yellow-500" />;
    if (mimeType.includes("application/zip") || mimeType.includes("application/x-rar")) 
      return <Archive className="h-4 w-4 text-orange-500" />;
    if (mimeType.includes("text/") || mimeType.includes("application/pdf")) 
      return <FileText className="h-4 w-4 text-red-500" />;
    if (mimeType.includes("application/") && mimeType.includes("json") || mimeType.includes("text/") && mimeType.includes("javascript")) 
      return <Code className="h-4 w-4 text-indigo-500" />;
    return <File className="h-4 w-4 text-gray-500" />;
  };

  // Load files and folders for current directory
  const loadFolderContents = async (folderId: string = "root") => {
    if (!user || !connectedUserCompany) return;

    setIsLoading(true);
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/files/?workspace_id=${workspaceId}&folder_id=${folderId}`);

      if (!response.ok) {
        throw new Error('Failed to load folder contents');
      }

      const data = await response.json();
      // Remove or comment out most console.log statements for cleaner output
      // console.log(`Main folder contents:`, data.files);
      
      const newFiles = data.files.filter((file: GoogleDriveFile) => !file.mimeType.includes("folder"));
      const newFolders = data.files.filter((file: GoogleDriveFile) => file.mimeType.includes("folder"));
      
      setFiles(newFiles);
      setFolders(newFolders);
      setCurrentFolderId(folderId);
      setCurrentFolderName(data.folder_name || "My Drive");
      
      // Reset local preferences when loading new folder
      setLocalPreferences(new Map());
      setHasUnsavedChanges(false);
      
      return { files: newFiles, folders: newFolders };
    } catch (error) {
      console.error('Error loading folder contents:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Load contents of a specific folder (for expansion)
  const loadSubfolderContents = async (folderId: string, folderName: string) => {
    if (!user || !connectedUserCompany || folderContents.has(folderId)) return;

    setLoadingFolders(prev => new Set([...prev, folderId]));
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/files/?workspace_id=${workspaceId}&folder_id=${folderId}`);

      if (!response.ok) {
        throw new Error('Failed to load subfolder contents');
      }

      const data = await response.json();
      // console.log(`Subfolder contents for ${folderName}:`, data.files);
      
      const subfolderFiles = data.files.filter((file: GoogleDriveFile) => !file.mimeType.includes("folder"));
      const subfolderFolders = data.files.filter((file: GoogleDriveFile) => file.mimeType.includes("folder"));
      
      setFolderContents(prev => new Map(prev).set(folderId, {
        files: subfolderFiles,
        folders: subfolderFolders
      }));
    } catch (error) {
      console.error('Error loading subfolder contents:', error);
    } finally {
      setLoadingFolders(prev => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });
    }
  };

  // Navigate to folder
  const navigateToFolder = async (folderId: string, folderName: string) => {
    setCurrentFolderId(folderId);
    setCurrentFolderName(folderName);
    
    // Update breadcrumbs
    const newBreadcrumbs = [...breadcrumbs];
    const existingIndex = newBreadcrumbs.findIndex(bc => bc.id === folderId);
    if (existingIndex !== -1) {
      newBreadcrumbs.splice(existingIndex + 1);
    } else {
      newBreadcrumbs.push({ id: folderId, name: folderName });
    }
    setBreadcrumbs(newBreadcrumbs);
    
    await loadFolderContents(folderId);
  };

  // Toggle folder expansion
  const toggleFolderExpansion = async (folderId: string, folderName: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
      // Load folder contents if not already loaded
      await loadSubfolderContents(folderId, folderName);
    }
    setExpandedFolders(newExpanded);
  };

  // Select all files in a folder
  const selectAllFilesInFolder = (folderId: string) => {
    const contents = folderContents.get(folderId);
    if (!contents) return;

    const newPreferences = new Map(localPreferences);
    contents.files.forEach(file => {
      const currentState = getFilePreferenceStateGlobal(file.id);
      if (!currentState) {
        newPreferences.set(file.id, true);
      }
    });
    setLocalPreferences(newPreferences);
    setHasUnsavedChanges(true);
  };

  // Deselect all files in a folder
  const deselectAllFilesInFolder = (folderId: string) => {
    const contents = folderContents.get(folderId);
    if (!contents) return;

    const newPreferences = new Map(localPreferences);
    contents.files.forEach(file => {
      const currentState = getFilePreferenceStateGlobal(file.id);
      if (currentState) {
        newPreferences.set(file.id, false);
      }
    });
    setLocalPreferences(newPreferences);
    setHasUnsavedChanges(true);
  };

  // Toggle file selection
  const toggleFileSelection = (fileId: string) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId);
    } else {
      newSelected.add(fileId);
    }
    setSelectedFiles(newSelected);
    onFileSelectionChange?.(Array.from(newSelected));
  };

  // Get current preference state for a file (local changes override server state)
  const getFilePreferenceState = (fileId: string, fileList: GoogleDriveFile[]) => {
    const localPref = localPreferences.get(fileId);
    if (localPref !== undefined) {
      return localPref;
    }
    const file = fileList.find(f => f.id === fileId);
    return file ? file.is_included : false;
  };

  // Get current preference state for any file (searches in current files and all expanded folders)
  const getFilePreferenceStateGlobal = (fileId: string) => {
    const localPref = localPreferences.get(fileId);
    if (localPref !== undefined) {
      // Remove preference debug logs
      return localPref;
    }
    // Check in current files
    const currentFile = files.find(f => f.id === fileId);
    if (currentFile) {
      // console.log(`File ${fileId}: Found in current files, is_included=${currentFile.is_included}`);
      return currentFile.is_included;
    }
    // Check in all expanded folders
    for (const [folderId, contents] of folderContents.entries()) {
      const file = contents.files.find(f => f.id === fileId);
      if (file) {
        // console.log(`File ${fileId}: Found in expanded folder ${folderId}, is_included=${file.is_included}`);
        return file.is_included;
      }
    }
    // console.log(`File ${fileId}: Not found anywhere, defaulting to false`);
    return false;
  };

  // Update local file preference (doesn't save to backend yet)
  const updateLocalFilePreference = (fileId: string, include: boolean) => {
    const newPreferences = new Map(localPreferences);
    newPreferences.set(fileId, include);
    setLocalPreferences(newPreferences);
    setHasUnsavedChanges(true);
  };

  // Apply all local preferences to backend
  const applyPreferences = async () => {
    if (!user || !connectedUserCompany || localPreferences.size === 0) return;

    setIsApplying(true);
    try {
      const preferences = Array.from(localPreferences.entries()).map(([fileId, isIncluded]) => {
        // Find the file in current files or any expanded folder
        let file = files.find(f => f.id === fileId);
        let folderPath = currentFolderName;
        
        if (!file) {
          // Search in expanded folders
          for (const [folderId, contents] of folderContents.entries()) {
            file = contents.files.find(f => f.id === fileId);
            if (file) {
              const folder = folders.find(f => f.id === folderId);
              folderPath = folder ? `${currentFolderName}/${folder.name}` : currentFolderName;
              break;
            }
          }
        }
        
        const preference = {
          file_id: fileId,
          file_path: file ? `${folderPath}/${file.name}` : fileId,
          is_included: isIncluded
        };
        
        // Remove preference debug logs
        return preference;
      });

      // Remove preference debug logs
      // console.log('Applying preferences:', preferences);

      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/batch-preferences/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspace_id: workspaceId,
          preferences: preferences,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);
        throw new Error(`Failed to apply preferences: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      // Remove success debug logs
      // console.log('Preferences applied successfully:', result);

      // Clear local preferences
      setLocalPreferences(new Map());
      setHasUnsavedChanges(false);
      
      // Store current expanded folders before clearing
      const expandedFoldersArray = Array.from(expandedFolders);
      
      // Clear expanded folder contents to force refresh
      setFolderContents(new Map());
      
      // Reload main folder contents and get the new data
      const folderData = await loadFolderContents(currentFolderId);
      
      // Reload all expanded folders with fresh data
      if (folderData) {
        for (const folderId of expandedFoldersArray) {
          // Find the folder in the newly loaded folders
          const folder = folderData.folders.find((f: GoogleDriveFolder) => f.id === folderId);
          if (folder) {
            await loadSubfolderContents(folderId, folder.name);
          }
        }
      }
    } catch (error) {
      console.error('Error applying preferences:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Error applying preferences: ${errorMessage}`);
    } finally {
      setIsApplying(false);
    }
  };

  // Filter files based on search query
  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredFolders = folders.filter(folder => 
    folder.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (workspaceId) {
      loadFolderContents();
    }
  }, [workspaceId]);

  // Determine if user is admin
  const isAdmin = userRole === "ADMIN";

  // Render file list
  const renderFileList = (fileList: GoogleDriveFile[], folderPath: string = "") => {
    return fileList.map((file) => {
      const currentPreference = getFilePreferenceStateGlobal(file.id);
      const hasLocalChange = localPreferences.has(file.id);
      
      return (
        <div
          key={file.id}
          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 ml-6"
        >
          <div className="flex items-center gap-3">
            {getFileIcon(file.mimeType)}
            <div className="flex flex-col">
              <span className="font-medium">{file.name}</span>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <Badge variant={currentPreference ? "default" : "secondary"}>
                  {currentPreference ? "Included" : "Excluded"}
                </Badge>
                {file.has_preference && !hasLocalChange && <span>Custom</span>}
                {hasLocalChange && <span className="text-orange-600">Modified</span>}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox
              checked={currentPreference}
              onCheckedChange={isAdmin ? (checked) => updateLocalFilePreference(file.id, !!checked) : undefined}
              title={currentPreference ? "Click to exclude from SageBase" : "Click to include in SageBase"}
              disabled={!isAdmin}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={isAdmin ? () => updateLocalFilePreference(file.id, !currentPreference) : undefined}
              title={currentPreference ? "Exclude from SageBase" : "Include in SageBase"}
              disabled={!isAdmin}
            >
              {currentPreference ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      );
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Folder className="h-5 w-5" />
          Google Drive Files
        </CardTitle>
        <CardDescription>
          Browse and manage files included in SageBase (all files are included by default)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Admin-only warning */}
        {!isAdmin && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-300 rounded-lg text-yellow-900 font-medium">
            Only company admins can change this!
          </div>
        )}
        {/* Breadcrumbs */}
        <div className="flex items-center gap-2 text-sm text-gray-600">
          {breadcrumbs.map((breadcrumb, index) => (
            <div key={breadcrumb.id} className="flex items-center gap-2">
              {index > 0 && <ChevronRight className="h-4 w-4" />}
              <button
                onClick={() => navigateToFolder(breadcrumb.id, breadcrumb.name)}
                className="hover:text-blue-600 hover:underline"
              >
                {breadcrumb.name}
              </button>
            </div>
          ))}
        </div>

        {/* Search and Controls */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search files and folders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={!isAdmin}
            />
          </div>
          <Button
            onClick={() => loadFolderContents(currentFolderId)}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          {hasUnsavedChanges && isAdmin && (
            <Button
              onClick={applyPreferences}
              disabled={isApplying}
              className="bg-green-600 hover:bg-green-700"
            >
              {isApplying ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Apply Changes
            </Button>
          )}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          </div>
        )}

        {/* Files and Folders */}
        {!isLoading && (
          <div className="space-y-2">
            {/* Folders */}
            {filteredFolders.map((folder) => {
              const isExpanded = expandedFolders.has(folder.id);
              const contents = folderContents.get(folder.id);
              const isLoadingFolder = loadingFolders.has(folder.id);
              
              return (
                <div key={folder.id}>
                  <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => toggleFolderExpansion(folder.id, folder.name)}
                        className="p-1 hover:bg-gray-200 rounded"
                      >
                        {isLoadingFolder ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </button>
                      {getFileIcon(folder.mimeType)}
                      <span className="font-medium">{folder.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {isExpanded && contents && contents.files.length > 0 && isAdmin && (
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => selectAllFilesInFolder(folder.id)}
                            title="Select all files in this folder"
                          >
                            <CheckSquare className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deselectAllFilesInFolder(folder.id)}
                            title="Deselect all files in this folder"
                          >
                            <Square className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigateToFolder(folder.id, folder.name)}
                      >
                        Open
                      </Button>
                    </div>
                  </div>
                  
                  {/* Expanded folder contents */}
                  {isExpanded && contents && (
                    <div className="mt-2 space-y-2">
                      {contents.folders.map((subfolder) => (
                        <div
                          key={subfolder.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 ml-6"
                        >
                          <div className="flex items-center gap-3">
                            {getFileIcon(subfolder.mimeType)}
                            <span className="font-medium">{subfolder.name}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigateToFolder(subfolder.id, subfolder.name)}
                          >
                            Open
                          </Button>
                        </div>
                      ))}
                      {renderFileList(contents.files, folder.name)}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Files in current folder */}
            {renderFileList(filteredFiles)}

            {filteredFiles.length === 0 && filteredFolders.length === 0 && !isLoading && (
              <div className="text-center py-8 text-gray-500">
                No files or folders found
              </div>
            )}
          </div>
        )}

        {/* Selection Summary */}
        {selectedFiles.size > 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              {selectedFiles.size} file{selectedFiles.size !== 1 ? 's' : ''} selected
            </p>
          </div>
        )}

        {/* Unsaved Changes Warning */}
        {hasUnsavedChanges && isAdmin && (
          <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <p className="text-sm text-orange-800">
              You have unsaved changes. Click "Apply Changes" to save your preferences.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 