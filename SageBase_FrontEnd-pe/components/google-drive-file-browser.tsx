"use client";
import { useState, useEffect } from "react";
import { getBackendUrl } from '@/lib/api-config';
import { useAuth } from "@/contexts/auth-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Folder, 
  File, 
  FileText, 
  Image, 
  Video, 
  Music, 
  Archive, 
  Code, 
  ChevronRight, 
  ChevronDown,
  Loader2,
  RefreshCw,
  Search,
  Eye,
  EyeOff,
  Save,
  CheckSquare,
  Square
} from "lucide-react";

interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
  parents?: string[];
  size?: string;
  modifiedTime?: string;
  is_included: boolean;
  has_preference: boolean;
}

interface GoogleDriveFolder {
  id: string;
  name: string;
  mimeType: string;
  parents?: string[];
  size?: string;
  modifiedTime?: string;
}

interface FileBrowserProps {
  workspaceId: string;
  onFileSelectionChange?: (selectedFiles: string[]) => void;
}

const BACKEND_BASE_URL = getBackendUrl();

export default function GoogleDriveFileBrowser({ workspaceId, onFileSelectionChange }: FileBrowserProps) {
  const { user, connectedUserCompany } = useAuth();
  const [files, setFiles] = useState<GoogleDriveFile[]>([]);
  const [folders, setFolders] = useState<GoogleDriveFolder[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string>("root");
  const [currentFolderName, setCurrentFolderName] = useState<string>("My Drive");
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [selectedFolders, setSelectedFolders] = useState<Set<string>>(new Set());
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [localPreferences, setLocalPreferences] = useState<Map<string, boolean>>(new Map());
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [folderContents, setFolderContents] = useState<Map<string, { files: GoogleDriveFile[], folders: GoogleDriveFolder[] }>>(new Map());
  const [loadingFolders, setLoadingFolders] = useState<Set<string>>(new Set());
  const [processingFolders, setProcessingFolders] = useState<Set<string>>(new Set());
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{id: string, name: string}>>([
    { id: "root", name: "My Drive" }
  ]);
  const [activeTab, setActiveTab] = useState<'browse'|'preferences'>('browse');
  const [preferences, setPreferences] = useState<Array<{file_id: string; file_path: string; is_included: boolean; file_state: string; updated_at?: string;}>>([]);
  const [isLoadingPrefs, setIsLoadingPrefs] = useState(false);

  // Get file icon based on MIME type
  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes("folder")) return <Folder className="h-4 w-4 text-blue-500" />;
    if (mimeType.includes("image")) return <Image className="h-4 w-4 text-green-500" />;
    if (mimeType.includes("video")) return <Video className="h-4 w-4 text-purple-500" />;
    if (mimeType.includes("audio")) return <Music className="h-4 w-4 text-yellow-500" />;
    if (mimeType.includes("application/zip") || mimeType.includes("application/x-rar")) 
      return <Archive className="h-4 w-4 text-orange-500" />;
    if (mimeType.includes("text/") || mimeType.includes("application/pdf")) 
      return <FileText className="h-4 w-4 text-red-500" />;
    if (mimeType.includes("application/") && mimeType.includes("json") || mimeType.includes("text/") && mimeType.includes("javascript")) 
      return <Code className="h-4 w-4 text-indigo-500" />;
    return <File className="h-4 w-4 text-gray-500" />;
  };

  // Clear folder selections
  const clearFolderSelections = () => {
    setSelectedFolders(new Set());
  };

  // Load files and folders for current directory
  const loadFolderContents = async (folderId: string = "root") => {
    if (!user || !connectedUserCompany) return;

    setIsLoading(true);
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/files/?workspace_id=${workspaceId}&folder_id=${folderId}`);

      if (!response.ok) {
        throw new Error('Failed to load folder contents');
      }

      const data = await response.json();
      // Remove or comment out most console.log statements for cleaner output
      // console.log(`Main folder contents:`, data.files);
      
      const newFiles = data.files.filter((file: GoogleDriveFile) => !file.mimeType.includes("folder"));
      const newFolders = data.files.filter((file: GoogleDriveFile) => file.mimeType.includes("folder"));
      
      setFiles(newFiles);
      setFolders(newFolders);
      setCurrentFolderId(folderId);
      setCurrentFolderName(data.folder_name || "My Drive");
      
      // Only reset local preferences if we're loading a different folder
      // or if there are no unsaved changes
      if (currentFolderId !== folderId || !hasUnsavedChanges) {
        setLocalPreferences(new Map());
        setHasUnsavedChanges(false);
        clearFolderSelections(); // Clear folder selections when navigating
      }
      
      return { files: newFiles, folders: newFolders };
    } catch (error) {
      console.error('Error loading folder contents:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Load flat preferences list
  const loadPreferences = async () => {
    if (!user || !connectedUserCompany) return;
    setIsLoadingPrefs(true);
    try {
      const resp = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/preferences-list/?workspace_id=${workspaceId}`);
      if (!resp.ok) throw new Error('Failed to load preferences');
      const data = await resp.json();
      setPreferences(data.preferences || []);
    } catch (e) {
      console.error('Error loading preferences:', e);
    } finally {
      setIsLoadingPrefs(false);
    }
  };

  // Load contents of a specific folder (for expansion)
  const loadSubfolderContents = async (folderId: string, folderName: string) => {
    if (!user || !connectedUserCompany || folderContents.has(folderId)) return;

    setLoadingFolders(prev => new Set([...prev, folderId]));
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/files/?workspace_id=${workspaceId}&folder_id=${folderId}`);

      if (!response.ok) {
        throw new Error('Failed to load subfolder contents');
      }

      const data = await response.json();
      // console.log(`Subfolder contents for ${folderName}:`, data.files);
      
      const subfolderFiles = data.files.filter((file: GoogleDriveFile) => !file.mimeType.includes("folder"));
      const subfolderFolders = data.files.filter((file: GoogleDriveFile) => file.mimeType.includes("folder"));
      
      setFolderContents(prev => new Map(prev).set(folderId, {
        files: subfolderFiles,
        folders: subfolderFolders
      }));
    } catch (error) {
      console.error('Error loading subfolder contents:', error);
    } finally {
      setLoadingFolders(prev => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });
    }
  };

  // Navigate to folder
  const navigateToFolder = async (folderId: string, folderName: string) => {
    setCurrentFolderId(folderId);
    setCurrentFolderName(folderName);
    
    // Update breadcrumbs
    const newBreadcrumbs = [...breadcrumbs];
    const existingIndex = newBreadcrumbs.findIndex(bc => bc.id === folderId);
    if (existingIndex !== -1) {
      newBreadcrumbs.splice(existingIndex + 1);
    } else {
      newBreadcrumbs.push({ id: folderId, name: folderName });
    }
    setBreadcrumbs(newBreadcrumbs);
    
    await loadFolderContents(folderId);
  };

  // Toggle folder expansion
  const toggleFolderExpansion = async (folderId: string, folderName: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
      // Load folder contents if not already loaded
      await loadSubfolderContents(folderId, folderName);
    }
    setExpandedFolders(newExpanded);
  };

  // Get summary of pending changes
  const getPendingChangesSummary = () => {
    if (localPreferences.size === 0) return null;
    
    let filesToInclude = 0;
    let filesToExclude = 0;
    
    for (const [fileId, isIncluded] of localPreferences.entries()) {
      if (isIncluded) {
        filesToInclude++;
      } else {
        filesToExclude++;
      }
    }
    
    return { filesToInclude, filesToExclude };
  };

  // Helper function to count all files in a folder and its subfolders
  const getFileCountInFolder = (folderId: string): number => {
    const contents = folderContents.get(folderId);
    if (!contents) return 0;
    
    let count = contents.files.length;
    
    // Count files in expanded subfolders
    for (const subfolder of contents.folders) {
      if (expandedFolders.has(subfolder.id)) {
        count += getFileCountInFolder(subfolder.id);
      }
    }
    
    return count;
  };

  // Helper function to recursively collect all files from a folder and its subfolders
  const getAllFilesRecursively = (folderId: string): GoogleDriveFile[] => {
    const allFiles: GoogleDriveFile[] = [];
    const contents = folderContents.get(folderId);
    
    if (!contents) return allFiles;
    
    // Add files from current folder
    allFiles.push(...contents.files);
    
    // Recursively add files from subfolders (only if they are expanded and loaded)
    for (const subfolder of contents.folders) {
      if (expandedFolders.has(subfolder.id)) {
        allFiles.push(...getAllFilesRecursively(subfolder.id));
      }
    }
    
    return allFiles;
  };

  // Helper function to recursively collect all folder IDs that are expanded
  const getAllExpandedFolderIds = (folderId: string): string[] => {
    const folderIds: string[] = [];
    const contents = folderContents.get(folderId);
    
    if (!contents) return folderIds;
    
    // Add current folder ID
    folderIds.push(folderId);
    
    // Recursively add subfolder IDs
    for (const subfolder of contents.folders) {
      if (expandedFolders.has(subfolder.id)) {
        folderIds.push(...getAllExpandedFolderIds(subfolder.id));
      }
    }
    
    return folderIds;
  };

  // Select all files in a folder (recursively including subfolders)
  const selectAllFilesInFolder = async (folderId: string) => {
    const contents = folderContents.get(folderId);
    if (!contents) return;

    setProcessingFolders(prev => new Set([...prev, folderId]));
    
    try {
      // Use recursive exploration to find all files at deepest levels
      const allFiles = await exploreAllFoldersRecursively(folderId);
      
      console.log(`Folder ${folderId} has ${contents.files.length} direct files and ${contents.folders.length} subfolders`);
      console.log(`After recursive exploration, found ${allFiles.length} total files at deepest levels`);
      
      if (allFiles.length === 0) {
        alert('No files found in this folder or its subfolders.');
        return;
      }
      
      // Show confirmation for large operations
      if (allFiles.length > 50) {
        const confirmed = confirm(`This will select ${allFiles.length} files across this folder and all subfolders. This may take a moment. Continue?`);
        if (!confirmed) {
          return;
        }
      }
      
      const newPreferences = new Map(localPreferences);
      allFiles.forEach(file => {
        const currentState = getFilePreferenceStateGlobal(file.id);
        if (!currentState) {
          newPreferences.set(file.id, true);
        }
      });
      
      setLocalPreferences(newPreferences);
      
      console.log(`Selected ${allFiles.length} files in folder ${folderId} (including all subfolders at deepest levels)`);
    } finally {
      setProcessingFolders(prev => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });
    }
  };

  // Deselect all files in a folder (recursively including subfolders)
  const deselectAllFilesInFolder = async (folderId: string) => {
    const contents = folderContents.get(folderId);
    if (!contents) return;

    setProcessingFolders(prev => new Set([...prev, folderId]));
    
    try {
      // Use recursive exploration to find all files at deepest levels
      const allFiles = await exploreAllFoldersRecursively(folderId);
      
      console.log(`Folder ${folderId} has ${contents.files.length} direct files and ${contents.folders.length} subfolders`);
      console.log(`After recursive exploration, found ${allFiles.length} total files at deepest levels`);
      
      if (allFiles.length === 0) {
        alert('No files found in this folder or its subfolders.');
        return;
      }
      
      // Show confirmation for large operations
      if (allFiles.length > 50) {
        const confirmed = confirm(`This will deselect ${allFiles.length} files across this folder and all subfolders. Continue?`);
        if (!confirmed) {
          return;
        }
      }
      
      const newPreferences = new Map(localPreferences);
      allFiles.forEach(file => {
        const currentState = getFilePreferenceStateGlobal(file.id);
        if (currentState) {
          newPreferences.set(file.id, false);
        }
      });
      
      setLocalPreferences(newPreferences);
      
      console.log(`Deselected ${allFiles.length} files in folder ${folderId} (including all subfolders at deepest levels)`);
    } finally {
      setProcessingFolders(prev => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });
    }
  };

  // Toggle folder selection
  const toggleFolderSelection = (folderId: string) => {
    console.log(`🔄 Toggling folder selection for: ${folderId}`);
    console.log(`📁 Current selected folders:`, Array.from(selectedFolders));
    
    const newSelected = new Set(selectedFolders);
    if (newSelected.has(folderId)) {
      newSelected.delete(folderId);
      console.log(`❌ Removed folder ${folderId} from selection`);
    } else {
      newSelected.add(folderId);
      console.log(`✅ Added folder ${folderId} to selection`);
    }
    
    setSelectedFolders(newSelected);
    console.log(`📁 New selected folders:`, Array.from(newSelected));
  };

  // Get all files from current folder context (including expanded subfolders)
  const getAllFilesFromCurrentContext = (): GoogleDriveFile[] => {
    const allFiles: GoogleDriveFile[] = [];
    
    // Add files from current folder
    allFiles.push(...files);
    
    // Add files from expanded subfolders
    for (const [folderId, contents] of folderContents.entries()) {
      allFiles.push(...contents.files);
    }
    
    console.log(`📁 Current context: ${files.length} files in current folder, ${folderContents.size} expanded subfolders`);
    console.log(`📁 Total files in current context: ${allFiles.length}`);
    
    return allFiles;
  };

  // Recursively explore all folders to find files at deepest levels
  const exploreAllFoldersRecursively = async (folderId: string): Promise<GoogleDriveFile[]> => {
    const allFiles: GoogleDriveFile[] = [];
    
    try {
      console.log(`🔍 Exploring folder: ${folderId}`);
      
      // First, check if this is the current folder we're viewing
      if (folderId === currentFolderId) {
        console.log(`📁 This is the current folder, using current files and folders`);
        // Use current files and folders from state
        allFiles.push(...files);
        
        // Also check expanded folders for additional files
        for (const [expandedFolderId, contents] of folderContents.entries()) {
          if (expandedFolderId !== folderId) { // Don't double-count current folder
            allFiles.push(...contents.files);
          }
        }
        
        console.log(`📁 Current folder has ${files.length} files, expanded folders have additional files`);
        return allFiles;
      }
      
      // Load folder contents if not already loaded
      if (!folderContents.has(folderId)) {
        console.log(`📁 Loading contents for folder: ${folderId}`);
        const folder = folders.find(f => f.id === folderId);
        if (folder) {
          await loadSubfolderContents(folderId, folder.name);
          // Wait for state to update and retry if needed
          let retries = 0;
          while (!folderContents.has(folderId) && retries < 5) {
            await new Promise(resolve => setTimeout(resolve, 200));
            retries++;
          }
        }
      }
      
      const contents = folderContents.get(folderId);
      if (!contents) {
        console.log(`❌ No contents found for folder: ${folderId}`);
        return allFiles;
      }
      
      console.log(`📁 Folder ${folderId} has ${contents.files.length} files and ${contents.folders.length} subfolders`);
      
      // Add files from current folder
      allFiles.push(...contents.files);
      
      // Recursively explore all subfolders - ensure they are loaded first
      for (const subfolder of contents.folders) {
        console.log(`🔍 Exploring subfolder: ${subfolder.name} (${subfolder.id})`);
        
        // Load subfolder contents if not already loaded
        if (!folderContents.has(subfolder.id)) {
          console.log(`📁 Loading contents for subfolder: ${subfolder.name}`);
          await loadSubfolderContents(subfolder.id, subfolder.name);
          // Wait for state to update and retry if needed
          let retries = 0;
          while (!folderContents.has(subfolder.id) && retries < 5) {
            await new Promise(resolve => setTimeout(resolve, 200));
            retries++;
          }
        }
        
        // Now recursively explore the subfolder
        const subfolderFiles = await exploreAllFoldersRecursively(subfolder.id);
        console.log(`📁 Subfolder ${subfolder.name} returned ${subfolderFiles.length} files`);
        allFiles.push(...subfolderFiles);
      }
      
      console.log(`✅ Folder ${folderId} exploration complete. Total files found: ${allFiles.length}`);
      return allFiles;
    } catch (error) {
      console.error(`❌ Error exploring folder ${folderId}:`, error);
      return allFiles;
    }
  };

  // Select all files from selected folders
  const selectAllFilesFromSelectedFolders = async () => {
    if (selectedFolders.size === 0) return;
    
    console.log(`🚀 Processing ${selectedFolders.size} selected folder(s) for file selection`);
    
    // Just show a message that files will be processed when applying changes
    alert(`Selected ${selectedFolders.size} folder(s). All files in these folders (including subfolders) will be included when you click "Apply Changes". Embedding runs in the background and you'll be notified when it's done.`);
  };

  // Toggle file selection
  const toggleFileSelection = (fileId: string) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId);
    } else {
      newSelected.add(fileId);
    }
    setSelectedFiles(newSelected);
    onFileSelectionChange?.(Array.from(newSelected));
  };

  // Get current preference state for a file (local changes override server state)
  const getFilePreferenceState = (fileId: string, fileList: GoogleDriveFile[]) => {
    const localPref = localPreferences.get(fileId);
    if (localPref !== undefined) {
      return localPref;
    }
    const file = fileList.find(f => f.id === fileId);
    return file ? file.is_included : false;
  };

  // Get current preference state for any file (searches in current files and all expanded folders)
  const getFilePreferenceStateGlobal = (fileId: string) => {
    const localPref = localPreferences.get(fileId);
    if (localPref !== undefined) {
      return localPref;
    }
    // Check in current files
    const currentFile = files.find(f => f.id === fileId);
    if (currentFile) {
      return currentFile.is_included;
    }
    // Check in all expanded folders
    for (const [folderId, contents] of folderContents.entries()) {
      const file = contents.files.find(f => f.id === fileId);
      if (file) {
        return file.is_included;
      }
    }
    return false;
  };

  // Sync local preferences with backend state to avoid conflicts
  const syncPreferencesWithBackend = () => {
    const newLocalPreferences = new Map();
    
    // Check current files
    files.forEach(file => {
      if (file.has_preference) {
        newLocalPreferences.set(file.id, file.is_included);
      }
    });
    
    // Check expanded folders
    for (const [folderId, contents] of folderContents.entries()) {
      contents.files.forEach(file => {
        if (file.has_preference) {
          newLocalPreferences.set(file.id, file.is_included);
        }
      });
    }
    
    setLocalPreferences(newLocalPreferences);
    setHasUnsavedChanges(false);
  };

  // Update local file preference (doesn't save to backend yet)
  const updateLocalFilePreference = (fileId: string, include: boolean) => {
    console.log(`Updating local preference for file ${fileId}: ${include ? 'include' : 'exclude'}`);
    const newPreferences = new Map(localPreferences);
    newPreferences.set(fileId, include);
    setLocalPreferences(newPreferences);
    console.log(`Local preferences updated. Total: ${newPreferences.size}`);
  };

  // Apply all local preferences to backend
  const applyPreferences = async () => {
    if (!user || !connectedUserCompany || (localPreferences.size === 0 && selectedFolders.size === 0)) return;

    setIsApplying(true);
    try {
      let allPreferences = new Map(localPreferences);
      
      // If there are selected folders, use the new folder processing endpoint
      if (selectedFolders.size > 0) {
        console.log(`🔍 Processing ${selectedFolders.size} selected folder(s) using folder endpoint`);
        console.log(`📁 Selected folder IDs:`, Array.from(selectedFolders));
        
        try {
          // Use the new folder processing endpoint
          const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/process-folder-files/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              workspace_id: workspaceId,
              folder_ids: Array.from(selectedFolders),
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('Folder processing API Error:', response.status, errorData);
            throw new Error(`Failed to process folders: ${errorData.error || response.statusText}`);
          }

          const result = await response.json();
          console.log('Folders processed successfully:', result);
          
          // Show success message for folder processing
          alert(`Folders processed.
• Folders: ${result.summary.folders_processed}
• Files queued for inclusion: ${result.summary.total_files_processed}

Next: Click "Start Indexing" to process the content. You'll receive a notification when embedding completes.`);
          
          // Clear folder selections and reload data
          setSelectedFolders(new Set());
          setHasUnsavedChanges(false);
          
          // Reload current folder contents to reflect changes
          await loadFolderContents(currentFolderId);
          
          // Also reload any expanded folders to ensure consistency
          for (const folderId of expandedFolders) {
            const folder = folders.find(f => f.id === folderId);
            if (folder) {
              await loadSubfolderContents(folderId, folder.name);
            }
          }
          
          return; // Exit early since we've processed folders
          
        } catch (error) {
          console.error('Error processing folders:', error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          alert(`Failed to process folders: ${errorMessage}`);
          return;
        }
      }
      
      // If no folders selected, process individual file preferences as before
      if (allPreferences.size === 0 && files.length > 0) {
        console.log(`📁 No preferences but found ${files.length} files in current folder. Adding them to preferences.`);
        files.forEach(file => {
          allPreferences.set(file.id, true); // Include all files in current folder
        });
      }
      
      if (allPreferences.size === 0) {
        console.log(`📁 Still no preferences found, trying to process all files in current context`);
        const currentContextFiles = getAllFilesFromCurrentContext();
        
        if (currentContextFiles.length > 0) {
          console.log(`📁 Found ${currentContextFiles.length} files in current context, adding them to preferences`);
          currentContextFiles.forEach(file => {
            allPreferences.set(file.id, true);
          });
        } else {
          alert('No files found to process. Please select files or folders first, then click "Apply Changes".');
          return;
        }
      }
      
      // Show confirmation for large operations
      if (allPreferences.size > 50) {
        const confirmed = confirm(`This will save preferences for ${allPreferences.size} files. Continue?`);
        if (!confirmed) {
          return;
        }
      }
      
      const preferences = Array.from(allPreferences.entries()).map(([fileId, isIncluded]) => {
        // Find the file in current files or any expanded folder
        let file = files.find(f => f.id === fileId);
        let folderPath = currentFolderName;
        
        if (!file) {
          // Search in expanded folders
          for (const [folderId, contents] of folderContents.entries()) {
            file = contents.files.find(f => f.id === fileId);
            if (file) {
              const folder = folders.find(f => f.id === folderId);
              folderPath = folder ? `${currentFolderName}/${folder.name}` : currentFolderName;
              break;
            }
          }
        }
        
        const preference = {
          file_id: fileId,
          file_path: file ? `${folderPath}/${file.name}` : fileId,
          is_included: isIncluded,
          mime_type: file ? file.mimeType : ''
        };
        
        return preference;
      });

      console.log('Sending preferences to backend:', preferences);

      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/batch-preferences/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspace_id: workspaceId,
          preferences: preferences,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);
        throw new Error(`Failed to apply preferences: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      console.log('Preferences applied successfully:', result);

      // Clear local preferences and folder selections
      setLocalPreferences(new Map());
      setSelectedFolders(new Set());
      setHasUnsavedChanges(false);
      
      // Reload current folder contents to reflect changes
      await loadFolderContents(currentFolderId);
      
      // Also reload any expanded folders to ensure consistency
      for (const folderId of expandedFolders) {
        const folder = folders.find(f => f.id === folderId);
        if (folder) {
          await loadSubfolderContents(folderId, folder.name);
        }
      }
      
      // Show success message
      alert(`Preferences saved.
• Files updated: ${preferences.length}

To build the search index, start the embedding process from the embedding screen. You'll be notified when embedding completes.`);
      
    } catch (error) {
      console.error('Error applying preferences:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      alert(`Failed to update preferences: ${errorMessage}`);
    } finally {
      setIsApplying(false);
    }
  };

  // Remove all file preferences
  const removeAllPreferences = async () => {
    if (!user || !connectedUserCompany) return;
    
    if (!confirm('Are you sure? This will remove ALL file preferences and reset the workspace. Existing embeddings will be removed from ChromaDB. This action cannot be undone.')) {
      return;
    }

    setIsApplying(true);
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/api/integrations/google-drive/remove-all-preferences/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspace_id: workspaceId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);
        throw new Error(`Failed to remove preferences: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      console.log('All preferences removed successfully:', result);

      // Clear local preferences
      setLocalPreferences(new Map());
      setHasUnsavedChanges(false);
      
      // Reload current folder contents to reflect changes
      await loadFolderContents(currentFolderId);
      
      // Show success message
      alert(`Removed ${result.total_removed} file preferences. Vector index cleanup is being handled in the background.`);
      
    } catch (error) {
      console.error('Error removing all preferences:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      alert(`Failed to remove preferences: ${errorMessage}`);
    } finally {
      setIsApplying(false);
    }
  };

  // Filter files based on search query
  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredFolders = folders.filter(folder => 
    folder.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (workspaceId) {
      if (activeTab === 'browse') {
        loadFolderContents();
      } else {
        loadPreferences();
      }
    }
  }, [workspaceId, activeTab]);

  // Track unsaved changes
  useEffect(() => {
    const hasChanges = localPreferences.size > 0 || selectedFolders.size > 0;
    setHasUnsavedChanges(hasChanges);
  }, [localPreferences.size, selectedFolders.size]);

  // Determine if user is admin
  const isAdmin = user?.role === "ADMIN";

  // Recursive folder component for unlimited nesting
  const renderFolderRecursive = (folder: GoogleDriveFolder, level: number = 0) => {
    const isExpanded = expandedFolders.has(folder.id);
    const contents = folderContents.get(folder.id);
    const isLoadingFolder = loadingFolders.has(folder.id);
    const indentClass = `ml-${Math.min(level * 6, 24)}`; // Cap indentation at ml-24
    
    return (
      <div key={folder.id}>
        <div className={`flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 ${indentClass} ${
          selectedFolders.has(folder.id) ? 'bg-blue-50 border-blue-300' : ''
        }`}>
          <div className="flex items-center gap-3">
            <button
              onClick={() => toggleFolderExpansion(folder.id, folder.name)}
              className="p-1 hover:bg-gray-200 rounded"
            >
              {isLoadingFolder ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
            {isAdmin && (
              <Checkbox
                checked={selectedFolders.has(folder.id)}
                onCheckedChange={() => toggleFolderSelection(folder.id)}
                title="Select this folder and all its contents"
              />
            )}
            {getFileIcon(folder.mimeType)}
            <span className="font-medium">{folder.name}</span>
            {processingFolders.has(folder.id) && (
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            )}
          </div>
          <div className="flex items-center gap-2">
            {isExpanded && contents && contents.files.length > 0 && isAdmin && (
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => selectAllFilesInFolder(folder.id)}
                  title={`Select all ${getFileCountInFolder(folder.id)} files in this folder (including subfolders)`}
                  disabled={processingFolders.has(folder.id)}
                >
                  {processingFolders.has(folder.id) ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <CheckSquare className="h-3 w-3" />
                  )}
                  <span className="ml-1 text-xs">
                    {getFileCountInFolder(folder.id)}
                  </span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deselectAllFilesInFolder(folder.id)}
                  title={`Deselect all ${getFileCountInFolder(folder.id)} files in this folder (including subfolders)`}
                  disabled={processingFolders.has(folder.id)}
                >
                  {processingFolders.has(folder.id) ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Square className="h-3 w-3" />
                  )}
                  <span className="ml-1 text-xs">
                    {getFileCountInFolder(folder.id)}
                  </span>
                </Button>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateToFolder(folder.id, folder.name)}
            >
              Open
            </Button>
          </div>
        </div>
        
        {/* Recursively render subfolder contents */}
        {isExpanded && contents && (
          <div className="mt-2 space-y-2">
            {contents.folders.map((subfolder) => renderFolderRecursive(subfolder, level + 1))}
            {renderFileList(contents.files, folder.name)}
          </div>
        )}
      </div>
    );
  };

  // Render file list
  const renderFileList = (fileList: GoogleDriveFile[], folderName?: string) => {
    return fileList.map((file) => {
      const currentPreference = getFilePreferenceStateGlobal(file.id);
      const hasLocalChange = localPreferences.has(file.id);
      
      console.log(`Rendering file ${file.id} (${file.name}): current=${currentPreference}, hasLocal=${hasLocalChange}, localValue=${localPreferences.get(file.id)}`);
      
      return (
        <div
          key={file.id}
          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 ml-6"
        >
          <div className="flex items-center gap-3">
            {getFileIcon(file.mimeType)}
            <div className="flex-1 min-w-0">
              <span className="font-medium">{file.name}</span>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <Badge variant={currentPreference ? "default" : "secondary"}>
                  {currentPreference ? "Included" : "Excluded"}
                </Badge>
                {file.has_preference && !hasLocalChange && <span>Custom</span>}
                {hasLocalChange && <span className="text-orange-600">Modified</span>}
                {localPreferences.has(file.id) && (
                  <span className="text-blue-600">
                    Local: {localPreferences.get(file.id) ? "Include" : "Exclude"}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox
              checked={currentPreference}
              onCheckedChange={isAdmin ? (checked) => updateLocalFilePreference(file.id, !!checked) : undefined}
              title={currentPreference ? "Click to exclude from SageBase" : "Click to include in SageBase"}
              disabled={!isAdmin}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={isAdmin ? () => updateLocalFilePreference(file.id, !currentPreference) : undefined}
              title={currentPreference ? "Exclude from SageBase" : "Include in SageBase"}
              disabled={!isAdmin}
            >
              {currentPreference ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      );
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Folder className="h-5 w-5" />
          Google Drive Files
        </CardTitle>
        <CardDescription>
          Browse and manage files included in SageBase (all files are included by default)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Admin-only warning */}
        {!isAdmin && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-300 rounded-lg text-yellow-900 font-medium">
            Only company admins can change this!
          </div>
        )}
        {/* Breadcrumbs */}
        <div className="flex items-center gap-2 text-sm text-gray-600">
          {breadcrumbs.map((breadcrumb, index) => (
            <div key={breadcrumb.id} className="flex items-center gap-2">
              {index > 0 && <ChevronRight className="h-4 w-4" />}
              <button
                onClick={() => navigateToFolder(breadcrumb.id, breadcrumb.name)}
                className="hover:text-blue-600 hover:underline"
              >
                {breadcrumb.name}
              </button>
            </div>
          ))}
        </div>

        {/* Tabs */}
        <div className="flex items-center gap-2">
          <Button variant={activeTab==='browse'? 'default': 'outline'} size="sm" onClick={() => setActiveTab('browse')}>Browse Files</Button>
          <Button variant={activeTab==='preferences'? 'default': 'outline'} size="sm" onClick={() => setActiveTab('preferences')}>My Preferences</Button>
        </div>

        {/* Search and Controls (Browse tab) */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search files and folders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={!isAdmin}
            />
          </div>
          <Button
            onClick={() => loadFolderContents(currentFolderId)}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          {isAdmin && (
            <Button
              onClick={removeAllPreferences}
              variant="outline"
              size="sm"
              title="Remove all file preferences and clean ChromaDB"
              className="text-red-600 border-red-600 hover:bg-red-50"
            >
              <RefreshCw className="h-4 w-4" />
              Remove All
            </Button>
          )}
          {selectedFolders.size > 0 && isAdmin && (
            <Button
              onClick={selectAllFilesFromSelectedFolders}
              variant="outline"
              size="sm"
              title={`${selectedFolders.size} folder(s) selected. Files will be processed when applying changes.`}
              className="text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <CheckSquare className="h-4 w-4 mr-2" />
              {selectedFolders.size} folder{selectedFolders.size !== 1 ? 's' : ''} selected
            </Button>
          )}
          {hasUnsavedChanges && isAdmin && (
            <Button
              onClick={applyPreferences}
              disabled={isApplying}
              className="bg-green-600 hover:bg-green-700"
            >
              {isApplying ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Apply Changes
            </Button>
          )}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          </div>
        )}

        {/* Files and Folders (Browse tab) */}
        {activeTab==='browse' && !isLoading && (
          <div className="space-y-2">
            {/* Folders */}
            {filteredFolders.map((folder) => renderFolderRecursive(folder))}

            {/* Files in current folder */}
            {renderFileList(filteredFiles)}

            {filteredFiles.length === 0 && filteredFolders.length === 0 && !isLoading && (
              <div className="text-center py-8 text-gray-500">
                No files or folders found
              </div>
            )}
          </div>
        )}

        {/* Selection Summary (Browse tab) */}
        {activeTab==='browse' && (selectedFiles.size > 0 || selectedFolders.size > 0) && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              {selectedFiles.size > 0 && (
                <span>{selectedFiles.size} file{selectedFiles.size !== 1 ? 's' : ''} selected</span>
              )}
              {selectedFiles.size > 0 && selectedFolders.size > 0 && <span> • </span>}
              {selectedFolders.size > 0 && (
                <span>{selectedFolders.size} folder{selectedFolders.size !== 1 ? 's' : ''} selected</span>
              )}
            </p>
            {selectedFolders.size > 0 && (
              <p className="text-xs text-blue-700 mt-1">
                <strong>Note:</strong> When you click "Apply Changes", all files in selected folders (including all subfolders) will be automatically included in SageBase.
              </p>
            )}
          </div>
        )}

        {/* Unsaved Changes Warning (Browse tab) */}
        {activeTab==='browse' && hasUnsavedChanges && isAdmin && (
          <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <p className="text-sm text-orange-800">
              You have unsaved changes. Click "Apply Changes" to save your preferences.
            </p>
            {(() => {
              const summary = getPendingChangesSummary();
              if (summary) {
                return (
                  <div className="mt-2 text-xs text-orange-700">
                    <span className="font-medium">Summary:</span> {summary.filesToInclude} files to include, {summary.filesToExclude} files to exclude
                  </div>
                );
              }
              return null;
            })()}
          </div>
        )}

        {/* My Preferences tab content (read-only list) */}
        {activeTab==='preferences' && (
          <div className="mt-4 space-y-3">
            {isLoadingPrefs ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              </div>
            ) : (
              <div className="space-y-2">
                {preferences.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">No preferences yet</div>
                ) : (
                  preferences.map((pref) => (
                    <div key={pref.file_id || pref.file_path} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3 min-w-0">
                        <File className="h-4 w-4 text-gray-500" />
                        <span className="truncate max-w-[60ch]" title={pref.file_path}>{pref.file_path}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={pref.is_included ? 'default' : 'secondary'}>
                          {pref.is_included ? 'Included' : 'Excluded'}
                        </Badge>
                        <Badge variant="outline">
                          {pref.file_state.replace(/_/g, ' ').toLowerCase()}
                        </Badge>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 