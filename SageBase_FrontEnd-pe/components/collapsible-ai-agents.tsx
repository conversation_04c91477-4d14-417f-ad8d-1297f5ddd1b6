"use client"

import { useState } from "react"
import { ChevronRight, ChevronDown, Plus } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Image from "next/image"

type AIAgent = {
  id: string
  name: string
  connected: boolean
  provider: string
  logo: string
}

const aiAgents: AIAgent[] = [
  {
    id: "slack-ai",
    name: "Slack AI",
    connected: true,
    provider: "Slack",
    logo: "/images/slack-logo.png",
  },
  {
    id: "cursor",
    name: "<PERSON><PERSON><PERSON>",
    connected: true,
    provider: "Cursor",
    logo: "/images/cursor-logo.png",
  },
  {
    id: "claude",
    name: "<PERSON>",
    connected: false,
    provider: "Anthropic",
    logo: "/placeholder.svg?key=n4h55",
  },
  {
    id: "gpt4",
    name: "GPT-4",
    connected: false,
    provider: "OpenAI",
    logo: "/placeholder.svg?key=q6gz3",
  },
  {
    id: "gemini",
    name: "<PERSON>",
    connected: false,
    provider: "Google",
    logo: "/placeholder.svg?key=fiu8o",
  },
]

export function CollapsibleAIAgents() {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Add this function inside the CollapsibleAIAgents component
  const getColorForAgent = (id: string) => {
    // Simple hash function to generate a consistent color for each agent ID
    let hash = 0
    for (let i = 0; i < id.length; i++) {
      hash = id.charCodeAt(i) + ((hash << 5) - hash)
    }

    // Convert to hex color
    const colors = [
      "#FF6B6B", // red
      "#4ECDC4", // teal
      "#FFD166", // yellow
      "#6A0572", // purple
      "#1A535C", // dark teal
      "#F9C80E", // gold
      "#2EC4B6", // turquoise
      "#011627", // navy
      "#FF9F1C", // orange
      "#7B287D", // violet
    ]

    // Use the hash to pick a color from our palette
    const colorIndex = Math.abs(hash) % colors.length
    return colors[colorIndex]
  }

  return (
    <div className="mb-6">
      {/* Header */}
      <div className="flex items-start justify-between w-full py-2 px-3 text-left hover:bg-gray-100/50 rounded-md transition-colors group">
      <button
          className="flex items-center flex-1 text-left"
        onClick={() => setIsExpanded(!isExpanded)}
        aria-expanded={isExpanded}
        aria-controls="connected-ai-agents-list"
      >
          {isExpanded ? (
            <ChevronDown className="h-3.5 w-3.5 text-gray-500 mr-1" />
          ) : (
            <ChevronRight className="h-3.5 w-3.5 text-gray-500 mr-1" />
          )}
          <span className="text-xs font-semibold text-gray-500 tracking-wider uppercase">Connected AI Agents</span>
        </button>
        <button
          className="p-1 rounded-full hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={(e) => {
            e.stopPropagation()
            setIsModalOpen(true)
          }}
          aria-label="Add AI agent"
        >
          <Plus className="h-3.5 w-3.5 text-gray-500" />
        </button>
      </div>

      {/* AI Agents List */}
      {isExpanded && (
        <div id="connected-ai-agents-list" className="mt-1 space-y-1">
          {aiAgents
            .filter((agent) => agent.connected)
            .map((agent) => (
              <div
                key={agent.id}
                className="flex items-center py-1.5 px-3 text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer"
              >
                <div
                  className="h-6 w-6 mr-2 relative flex-shrink-0 rounded-full overflow-hidden flex items-center justify-center"
                  style={{ backgroundColor: getColorForAgent(agent.id) }}
                >
                  <span className="text-white text-xs font-bold">{agent.name.charAt(0).toUpperCase()}</span>
                </div>
                <span className="text-sm">{agent.name}</span>
              </div>
            ))}
        </div>
      )}

      {/* Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add AI Agent</DialogTitle>
            <DialogDescription>Connect SageBase to AI agents to enhance your knowledge workflows.</DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="popular" className="mt-4">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="popular">Popular</TabsTrigger>
              <TabsTrigger value="all">All Agents</TabsTrigger>
            </TabsList>

            <div className="space-y-3 mt-2">
              {aiAgents
                .filter((agent) => !agent.connected)
                .map((agent) => (
                  <div key={agent.id} className="flex items-center justify-between p-3 border rounded-md">
                    <div className="flex items-center">
                      <div className="h-6 w-6 mr-3 relative flex-shrink-0 rounded-full overflow-hidden">
                        <Image
                          src={agent.logo || "/placeholder.svg"}
                          alt={`${agent.name} logo`}
                          width={24}
                          height={24}
                          className="object-contain"
                        />
                      </div>
                      <div>
                        <p className="font-medium">{agent.name}</p>
                        <p className="text-xs text-gray-500">by {agent.provider}</p>
                      </div>
                    </div>
                    <Button size="sm">Connect</Button>
                  </div>
                ))}
            </div>

            <div className="flex justify-end mt-4">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Done
              </Button>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default CollapsibleAIAgents
