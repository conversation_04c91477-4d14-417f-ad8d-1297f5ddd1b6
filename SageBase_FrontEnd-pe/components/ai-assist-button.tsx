"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, X, Search } from "lucide-react"
import { CheckCircle } from "lucide-react"

type AIAssistButtonProps = {
  onInsertContent?: (content: string) => void
}

export default function AIAssistButton({ onInsertContent = () => {} }: AIAssistButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [prompt, setPrompt] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [scanningPlatforms, setScanningPlatforms] = useState<{ [key: string]: boolean }>({})

  const platforms = ["Confluence", "Slack", "Jira", "GitHub", "Email"]

  const handleGenerate = () => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    setSuggestions([])
    setScanningPlatforms({})

    const platformScanTimes = {
      Confluence: 800,
      Slack: 1200,
      Jira: 1000,
      GitHub: 1500,
      Email: 700,
    }

    const scanPlatform = (platform: string, delay: number) => {
      return new Promise<void>((resolve) => {
        setTimeout(() => {
          setScanningPlatforms((prev) => ({ ...prev, [platform]: true }))
          resolve()
        }, delay)
      })
    }

    const scanAllPlatforms = async () => {
      for (const platform of platforms) {
        await scanPlatform(platform, platformScanTimes[platform])
      }

      // Simulate AI generation after scanning
      setTimeout(() => {
        const generatedSuggestions = [
          generateSuggestionBasedOnPrompt(prompt, 1),
          generateSuggestionBasedOnPrompt(prompt, 2),
          generateSuggestionBasedOnPrompt(prompt, 3),
        ]

        setSuggestions(generatedSuggestions)
        setIsGenerating(false)
      }, 500)
    }

    scanAllPlatforms()
  }

  const generateSuggestionBasedOnPrompt = (userPrompt: string, variant: number) => {
    // Enhanced AI content generation logic
    const baseDescription = `Based on research across Confluence documentation, Slack discussions, Jira tickets, GitHub repositories, and emails, here's a summary for the topic "${userPrompt}": `

    const confluenceInsight = "Confluence documents indicate that..."
    const slackInsight = "Slack discussions reveal that..."
    const jiraInsight = "Jira tickets suggest that..."
    const githubInsight = "GitHub repos contain information about..."
    const emailInsight = "Emails mention that..."

    if (userPrompt.toLowerCase().includes("oauth implementation")) {
      return variant === 1
        ? `<p>${baseDescription} ${confluenceInsight} OAuth implementation should follow the guidelines outlined in the security architecture document. ${slackInsight} The team discussed using specific libraries for OAuth. ${jiraInsight} There are open tickets related to OAuth configuration issues. ${githubInsight} The 'oauth-integration' repo contains relevant code samples. ${emailInsight} Recent emails confirm the agreed-upon OAuth strategy.</p>`
        : variant === 2
          ? `<p>${baseDescription} ${confluenceInsight} The recommended OAuth flow is described in detail. ${slackInsight} There were debates about the best approach for token management. ${jiraInsight} Several bugs are associated with incorrect OAuth scopes. ${githubInsight} Check the 'oauth-middleware' repo for middleware implementations. ${emailInsight} Older emails contain initial discussions about OAuth providers.</p>`
          : `<p>${baseDescription} ${confluenceInsight} Ensure compliance with the latest security standards for OAuth. ${slackInsight} The security team raised concerns about potential vulnerabilities in the OAuth implementation. ${jiraInsight} Closed tickets document previous OAuth-related incidents. ${githubInsight} The 'oauth-testing' repo provides testing tools and examples. ${emailInsight} Review emails from the security audit for OAuth-specific recommendations.</p>`
    }

    if (userPrompt.toLowerCase().includes("api security")) {
      return variant === 1
        ? `<p>${baseDescription} ${confluenceInsight} API security best practices are documented in the API design guide. ${slackInsight} The team discussed implementing rate limiting to prevent abuse. ${jiraInsight} There are tickets for addressing potential API vulnerabilities. ${githubInsight} The 'api-security' repo contains security-related code and configurations. ${emailInsight} Recent emails highlight the importance of API key rotation.</p>`
        : variant === 2
          ? `<p>${baseDescription} ${confluenceInsight} The API security checklist covers common security threats. ${slackInsight} There were discussions about using JWT for authentication. ${jiraInsight} Several bugs are related to API authentication issues. ${githubInsight} Check the 'api-gateway' repo for gateway implementations. ${emailInsight} Older emails contain initial discussions about API security measures.</p>`
          : `<p>${baseDescription} ${confluenceInsight} Ensure compliance with the latest OWASP guidelines for API security. ${slackInsight} The security team raised concerns about potential vulnerabilities in the API endpoints. ${jiraInsight} Closed tickets document previous API-related security incidents. ${githubInsight} The 'api-testing' repo provides testing tools and examples. ${emailInsight} Review emails from the security audit for API-specific recommendations.</p>`
    }

    // Default responses for other prompts
    return variant === 1
      ? `<p>${baseDescription} This is the first variation that provides a concise and informative perspective.</p>`
      : variant === 2
        ? `<p>${baseDescription} This AI-generated content addresses "${userPrompt}" with a more detailed analysis and comprehensive explanation.</p>`
        : `<p>${baseDescription} The third variation of AI-generated content for "${userPrompt}" offers an alternative approach with different emphasis and structure.</p>`
  }

  const applySuggestion = (suggestion: string) => {
    if (onInsertContent && typeof onInsertContent === "function") {
      onInsertContent(suggestion)
    }
    setIsOpen(false)
    setPrompt("")
    setSuggestions([])
  }

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        className="bg-white text-purple-600 border-purple-200 hover:bg-purple-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Search className="mr-1.5 h-4 w-4" />
        Topic Research
      </Button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="flex items-center justify-between p-3 border-b border-gray-200">
            <h3 className="font-medium text-gray-800">AI Topic Research</h3>
            <Button variant="ghost" size="sm" className="h-7 w-7 p-0" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="p-3">
            <div className="mb-3">
              <label htmlFor="ai-prompt" className="block text-xs font-medium text-gray-700 mb-1">
                Enter a topic to research across all platforms
              </label>
              <textarea
                id="ai-prompt"
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., OAuth implementation, API security, deployment process..."
                rows={3}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
              />
            </div>

            <Button
              className="w-full bg-purple-600 hover:bg-purple-700 mb-3"
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
            >
              {isGenerating ? "Scanning platforms..." : "Scan Platforms & Generate"}
            </Button>

            {isGenerating && (
              <div className="mb-4">
                <h4 className="text-xs font-medium text-gray-700">Scanning Platforms:</h4>
                <ul className="mt-2 space-y-1">
                  {platforms.map((platform) => (
                    <li key={platform} className="flex items-center text-sm text-gray-600">
                      {scanningPlatforms[platform] ? (
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                      ) : (
                        <div className="mr-2 h-4 w-4 animate-pulse rounded-full bg-gray-300" />
                      )}
                      {platform}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {suggestions.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-xs font-medium text-gray-700">AI Suggestions:</h4>
                {suggestions.map((suggestion, index) => (
                  <div key={index} className="bg-purple-50 border border-purple-100 rounded-md p-2">
                    <div className="text-xs text-gray-700 mb-2" dangerouslySetInnerHTML={{ __html: suggestion }} />
                    <div className="flex justify-end">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-7 text-xs text-purple-600 hover:text-purple-700 hover:bg-purple-100"
                        onClick={() => applySuggestion(suggestion)}
                      >
                        Insert <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
