"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MessageSquare, Check, Clock, Flag, Bookmark, Share2 } from "lucide-react"

type Comment = {
  id: string
  content: string
  author: {
    name: string
    avatar: string
    reputation?: number
  }
  createdAt: string
}

type Answer = {
  id: string
  content: string
  author: {
    name: string
    avatar: string
    reputation?: number
  }
  votes: number
  isAccepted: boolean
  createdAt: string
  comments: Comment[]
}

type Question = {
  id: string
  title: string
  content: string
  author: {
    name: string
    avatar: string
    reputation?: number
  }
  origin?: {
    source: "slack" | "teams"
    timestamp: string
  }
  approvedBy?: {
    name: string
    avatar: string
    date: string
  }
  tags: string[]
  votes: number
  answers: Answer[]
  views: number
  createdAt: string
  comments: Comment[]
}

interface StackOverflowQAProps {
  question: Question
  onVoteQuestion?: (id: string, direction: "up" | "down") => void
  onVoteAnswer?: (id: string, direction: "up" | "down") => void
  onAcceptAnswer?: (id: string) => void
  onAddComment?: (parentId: string, content: string) => void
  onAddAnswer?: (questionId: string, content: string) => void
  currentUser?: {
    name: string
    avatar: string
  }
}

export default function StackOverflowQA({
  question,
  onVoteQuestion,
  onVoteAnswer,
  onAcceptAnswer,
  onAddComment,
  onAddAnswer,
  currentUser = { name: "Current User", avatar: "" },
}: StackOverflowQAProps) {
  const [newComment, setNewComment] = useState("")
  const [commentingOn, setCommentingOn] = useState<string | null>(null)
  const [newAnswer, setNewAnswer] = useState("")
  const [showAddAnswer, setShowAddAnswer] = useState(false)

  // Keep vote functionality in the backend but remove UI arrows
  const handleVote = (id: string, type: "question" | "answer", direction: "up" | "down") => {
    if (type === "question" && onVoteQuestion) {
      onVoteQuestion(id, direction)
    } else if (type === "answer" && onVoteAnswer) {
      onVoteAnswer(id, direction)
    }
  }

  const handleAccept = (id: string) => {
    if (onAcceptAnswer) {
      onAcceptAnswer(id)
    }
  }

  const handleAddComment = (parentId: string) => {
    if (onAddComment && newComment.trim()) {
      onAddComment(parentId, newComment)
      setNewComment("")
      setCommentingOn(null)
    }
  }

  const handleAddAnswer = () => {
    if (onAddAnswer && newAnswer.trim()) {
      onAddAnswer(question.id, newAnswer)
      setNewAnswer("")
      setShowAddAnswer(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Question */}
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-white p-6">
          <div className="flex gap-4">
            {/* Voting */}
            <div className="flex flex-col items-center">
              <div className="bg-gray-100 rounded-full px-3 py-1">
                <span className="text-lg font-bold">{question.votes}</span>
              </div>
              <button className="mt-3 text-gray-400 hover:text-amber-500">
                <Bookmark className="h-5 w-5" />
              </button>
            </div>

            {/* Question content */}
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800 mb-4">{question.title}</h1>
              <div className="flex items-center text-sm text-gray-500 mb-4">
                <span className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  Asked {question.createdAt}
                </span>
                <span className="mx-2">•</span>
                <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-sm flex items-center">
                  <span className="font-medium">Seen {question.views} times</span>
                </span>
              </div>

              <div className="prose max-w-none mb-4" dangerouslySetInnerHTML={{ __html: question.content }} />

              {/* Origin and Approved by information */}
              <div className="flex flex-wrap gap-4 mb-4 text-sm">
                {question.origin && (
                  <div className="flex items-center bg-blue-50 rounded-md px-3 py-1.5">
                    <span className="text-gray-600 mr-2">Origin:</span>
                    <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-sm capitalize">
                      {question.origin.source}
                    </span>
                    <span className="text-gray-500 ml-2">{question.origin.timestamp}</span>
                  </div>
                )}
                {question.approvedBy && (
                  <div className="flex items-center bg-green-50 rounded-md px-3 py-1.5">
                    <span className="text-gray-600 mr-2">Approved by:</span>
                    <Avatar className="h-5 w-5 mr-1">
                      <AvatarImage
                        src={question.approvedBy.avatar || "/placeholder.svg"}
                        alt={question.approvedBy.name}
                      />
                      <AvatarFallback>{question.approvedBy.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span className="text-green-600 font-medium">{question.approvedBy.name}</span>
                    <span className="text-gray-500 ml-2">{question.approvedBy.date}</span>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                {question.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
                    {tag}
                  </Badge>
                ))}
              </div>

              <div className="flex justify-between items-center mt-6">
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" className="text-gray-500">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                  <Button variant="ghost" size="sm" className="text-gray-500">
                    <Flag className="h-4 w-4 mr-1" />
                    Report
                  </Button>
                </div>

                <div className="flex items-center">
                  <Button variant="ghost" size="sm" className="text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    Active today
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Comments */}
          <div className="mt-6 border-t border-gray-100 pt-4">
            {question.comments.length > 0 && (
              <div className="space-y-3 mb-4">
                {question.comments.map((comment) => (
                  <div key={comment.id} className="flex items-start text-sm">
                    <div className="flex-1 text-gray-600">{comment.content}</div>
                    <div className="ml-4 flex items-center text-xs text-gray-500">
                      <span>– {comment.author.name}</span>
                      <span className="mx-1">•</span>
                      <span>{comment.createdAt}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {commentingOn === question.id ? (
              <div className="mt-3">
                <textarea
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                  rows={3}
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                ></textarea>
                <div className="flex justify-end mt-2 space-x-2">
                  <Button variant="outline" size="sm" onClick={() => setCommentingOn(null)}>
                    Cancel
                  </Button>
                  <Button size="sm" onClick={() => handleAddComment(question.id)} disabled={!newComment.trim()}>
                    Add Comment
                  </Button>
                </div>
              </div>
            ) : (
              <Button variant="ghost" size="sm" className="text-gray-500" onClick={() => setCommentingOn(question.id)}>
                <MessageSquare className="h-4 w-4 mr-1" />
                Add a comment
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Answers */}
      <div className="space-y-1">
        <h2 className="text-xl font-bold text-gray-800">
          {question.answers.length} {question.answers.length === 1 ? "Answer" : "Answers"}
        </h2>
        <div className="flex items-center text-sm text-gray-500">
          <Button variant="ghost" size="sm" className="text-gray-600">
            Highest score (default)
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-500">
            Date modified
          </Button>
        </div>
      </div>

      {/* Answer list */}
      <div className="space-y-6">
        {question.answers.map((answer) => (
          <div key={answer.id} className="border border-gray-200 rounded-lg overflow-hidden">
            <div className={`bg-white p-6 ${answer.isAccepted ? "border-l-4 border-green-500" : ""}`}>
              <div className="flex gap-4">
                {/* Voting */}
                <div className="flex flex-col items-center">
                  <div className="bg-gray-100 rounded-full px-3 py-1">
                    <span className="text-lg font-bold">{answer.votes}</span>
                  </div>
                  <button
                    className={`mt-3 ${answer.isAccepted ? "text-green-500" : "text-gray-400 hover:text-green-500"}`}
                    onClick={() => handleAccept(answer.id)}
                    title={answer.isAccepted ? "Accepted answer" : "Accept this answer"}
                  >
                    <Check className="h-6 w-6" />
                  </button>
                </div>

                {/* Answer content */}
                <div className="flex-1">
                  <div className="prose max-w-none mb-4" dangerouslySetInnerHTML={{ __html: answer.content }} />

                  <div className="flex justify-between items-center mt-6">
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm" className="text-gray-500">
                        <Share2 className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                      <Button variant="ghost" size="sm" className="text-gray-500">
                        <Flag className="h-4 w-4 mr-1" />
                        Report
                      </Button>
                    </div>

                    <div className="bg-blue-50 rounded-md p-3 flex items-center">
                      <div className="text-sm text-gray-600 mr-3">
                        <div>Answered {answer.createdAt}</div>
                      </div>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={answer.author.avatar || "/placeholder.svg"} alt={answer.author.name} />
                        <AvatarFallback>{answer.author.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="ml-2">
                        <div className="text-sm font-medium text-blue-600">{answer.author.name}</div>
                        {answer.author.reputation && (
                          <div className="text-xs text-gray-500">
                            {answer.author.reputation.toLocaleString()} reputation
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Comments */}
              <div className="mt-6 border-t border-gray-100 pt-4">
                {answer.comments.length > 0 && (
                  <div className="space-y-3 mb-4">
                    {answer.comments.map((comment) => (
                      <div key={comment.id} className="flex items-start text-sm">
                        <div className="flex-1 text-gray-600">{comment.content}</div>
                        <div className="ml-4 flex items-center text-xs text-gray-500">
                          <span>– {comment.author.name}</span>
                          <span className="mx-1">•</span>
                          <span>{comment.createdAt}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {commentingOn === answer.id ? (
                  <div className="mt-3">
                    <textarea
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                      rows={3}
                      placeholder="Add a comment..."
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                    ></textarea>
                    <div className="flex justify-end mt-2 space-x-2">
                      <Button variant="outline" size="sm" onClick={() => setCommentingOn(null)}>
                        Cancel
                      </Button>
                      <Button size="sm" onClick={() => handleAddComment(answer.id)} disabled={!newComment.trim()}>
                        Add Comment
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-500"
                    onClick={() => setCommentingOn(answer.id)}
                  >
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Add a comment
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add your answer */}
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-white p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Your Answer</h2>

          {showAddAnswer ? (
            <>
              <textarea
                className="w-full p-4 border border-gray-300 rounded-md"
                rows={8}
                placeholder="Write your answer here..."
                value={newAnswer}
                onChange={(e) => setNewAnswer(e.target.value)}
              ></textarea>
              <div className="flex justify-end mt-4 space-x-2">
                <Button variant="outline" onClick={() => setShowAddAnswer(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleAddAnswer}
                  disabled={!newAnswer.trim()}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  Post Your Answer
                </Button>
              </div>
            </>
          ) : (
            <Button className="w-full py-6 bg-emerald-600 hover:bg-emerald-700" onClick={() => setShowAddAnswer(true)}>
              Write Your Answer
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
