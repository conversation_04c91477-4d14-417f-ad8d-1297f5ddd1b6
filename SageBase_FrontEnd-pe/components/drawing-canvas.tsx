"use client"

import type React from "react"

import { useRef, useEffect, useState } from "react"

type DrawingTool =
  | "select"
  | "hand"
  | "rectangle"
  | "diamond"
  | "circle"
  | "arrow"
  | "line"
  | "link"
  | "text"
  | "image"
  | "eraser"
  | "group"

interface DrawingCanvasProps {
  currentTool: DrawingTool
}

export const DrawingCanvas = ({ currentTool }: DrawingCanvasProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [startX, setStartX] = useState(0)
  const [startY, setStartY] = useState(0)

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas size to match parent
    const resizeCanvas = () => {
      const parent = canvas.parentElement
      if (parent) {
        canvas.width = parent.clientWidth
        canvas.height = 400 // Fixed height or you could make it dynamic
      }
    }

    resizeCanvas()
    window.addEventListener("resize", resizeCanvas)

    // Clear canvas
    ctx.fillStyle = "#f8f9fa"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    return () => {
      window.removeEventListener("resize", resizeCanvas)
    }
  }, [])

  // Handle mouse events
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (currentTool === "hand") return

    setIsDrawing(true)
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    setStartX(e.clientX - rect.left)
    setStartY(e.clientY - rect.top)
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || currentTool === "hand" || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const currentX = e.clientX - rect.left
    const currentY = e.clientY - rect.top

    // Clear canvas and redraw
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.fillStyle = "#f8f9fa"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Draw based on selected tool
    ctx.strokeStyle = "#000"
    ctx.lineWidth = 2

    switch (currentTool) {
      case "rectangle":
        ctx.strokeRect(startX, startY, currentX - startX, currentY - startY)
        break
      case "circle":
        ctx.beginPath()
        const radius = Math.sqrt(Math.pow(currentX - startX, 2) + Math.pow(currentY - startY, 2))
        ctx.arc(startX, startY, radius, 0, 2 * Math.PI)
        ctx.stroke()
        break
      case "line":
        ctx.beginPath()
        ctx.moveTo(startX, startY)
        ctx.lineTo(currentX, currentY)
        ctx.stroke()
        break
      case "arrow":
        drawArrow(ctx, startX, startY, currentX, currentY)
        break
      case "diamond":
        drawDiamond(ctx, startX, startY, currentX, currentY)
        break
      default:
        break
    }
  }

  const handleMouseUp = () => {
    setIsDrawing(false)
  }

  // Helper functions for drawing
  const drawArrow = (ctx: CanvasRenderingContext2D, fromX: number, fromY: number, toX: number, toY: number) => {
    const headLength = 10
    const dx = toX - fromX
    const dy = toY - fromY
    const angle = Math.atan2(dy, dx)

    ctx.beginPath()
    ctx.moveTo(fromX, fromY)
    ctx.lineTo(toX, toY)
    ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6))
    ctx.moveTo(toX, toY)
    ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6))
    ctx.stroke()
  }

  const drawDiamond = (ctx: CanvasRenderingContext2D, startX: number, startY: number, endX: number, endY: number) => {
    const width = endX - startX
    const height = endY - startY
    const centerX = startX + width / 2
    const centerY = startY + height / 2

    ctx.beginPath()
    ctx.moveTo(centerX, startY)
    ctx.lineTo(endX, centerY)
    ctx.lineTo(centerX, endY)
    ctx.lineTo(startX, centerY)
    ctx.closePath()
    ctx.stroke()
  }

  return (
    <div className="w-full border border-gray-200 rounded-md overflow-hidden bg-gray-50">
      <canvas
        ref={canvasRef}
        className="w-full cursor-crosshair"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      />
      <div className="text-xs text-gray-500 p-2 text-center border-t border-gray-200">
        To move canvas, hold mouse wheel or spacebar while dragging, or use the hand tool
      </div>
    </div>
  )
}

export default DrawingCanvas
