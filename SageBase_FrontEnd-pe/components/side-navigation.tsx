"use client";

import type React from "react";
import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import VersionBadge from "./version-badge";
import { usePathname, useRouter } from "next/navigation";
import {
  FileText,
  PlusCircle,
  BarChart2,
  HelpCircle,
  ChevronDown,
  ChevronRight,
  Folder,
  MoreHorizontal,
  Eye,
  X,
  Search,
  Users,
  Shield,
  Edit3,
  Trash2,
  Plus,
  Lock,
  User,
  MessageSquare,
} from "lucide-react";
import { Upload, Info, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { createProject, deleteProject } from "@/lib/api-utils";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { qaAPI } from "@/services/qa-api";
import { useToast } from "@/hooks/use-toast";
// Import components with correct imports
import CollapsiblePlatforms from "./collapsible-platforms";
import CollapsibleURLMonitor from "./collapsible-url-monitor";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { useAuth } from "@/contexts/auth-context";
import { useSidebarRefresh } from "@/contexts/sidebar-refresh-context";
import { useQAPageRefresh } from "@/contexts/qa-page-refresh-context";
import { Project } from "@/types/qa";

type SpaceItem = {
  id: string;
  name: string;
  type: "folder" | "document";
  children?: SpaceItem[];
  href?: string;
  qaData?: any; // Store full Q&A data for editing
};

type Space = {
  id: string;
  name: string;
  color: string;
  initial: string;
  items: SpaceItem[];
};

export default function SideNavigation() {
  const router = useRouter();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const { toast } = useToast();
  const { setRefreshFunction } = useSidebarRefresh();
  const { refreshQAPage } = useQAPageRefresh();
  const { connectedUserCompany } = useAuth();

  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    type: "space" | "folder" | "document";
    itemName: string;
    parentSpace: string;
    parentFolder?: string;
    spaceId?: string;
  }>({
    show: false,
    x: 0,
    y: 0,
    type: "space",
    itemName: "",
    parentSpace: "",
    spaceId: "",
  });

  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleteItem, setDeleteItem] = useState<{
    type: "space" | "folder" | "document";
    itemName: string;
    parentSpace: string;
    parentFolder?: string;
  } | null>(null);

  const [showEditQAModal, setShowEditQAModal] = useState(false);
  const [editQAData, setEditQAData] = useState<{
    qaId: string;
    projectId: string;
    question_title: string;
    question_content: string;
    question_tags: string[];
    answer_content: string;
  } | null>(null);

  const [showCreateQAModal, setShowCreateQAModal] = useState(false);
  const [createQAData, setCreateQAData] = useState<{
    projectId: string;
    projectName: string;
  } | null>(null);

  const [showAddDocumentModal, setShowAddDocumentModal] = useState(false);
  const [modalData, setModalData] = useState({
    parentSpace: "",
    parentFolder: "",
  });

  const [showUploadFileModal, setShowUploadFileModal] = useState(false);
  const [uploadModalData, setUploadModalData] = useState({
    parentSpace: "",
    parentFolder: "",
  });
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    detected_qas_count?: number;
    message?: string;
    processing_results?: Array<{
      filename: string;
      file_size: number;
      processing_result: {
        message?: string;
        [key: string]: any;
      };
    }>;
    summary?: {
      total_files: number;
      successful_files: number;
      failed_files: number;
      new_qas_detected: number;
    };
    user_info?: {
      user_id: string;
      user_email: string;
      company_name?: string;
    };
    knowledge_space?: string;
  } | null>(null);

  // Upload progress state
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const [expandedSpaces, setExpandedSpaces] = useState<string[]>([
    "Atlas",
    "Nova",
  ]);
  const [expandedFolders, setExpandedFolders] = useState<string[]>([]);

  // Add a state update counter to force re-renders
  const [updateCounter, setUpdateCounter] = useState(0);

  const [spaces, setSpaces] = useState<Space[]>([]);
  const [spacesLoading, setSpacesLoading] = useState(true);
  const [qaData, setQaData] = useState<Record<string, any[]>>({});

  // Auto-expand all folders when spaces change
  useEffect(() => {
    const allFolderKeys: string[] = [];
    spaces.forEach((space) => {
      space.items.forEach((item) => {
        if (item.type === "folder") {
          allFolderKeys.push(`${space.name}-${item.name}`);
        }
      });
    });
    setExpandedFolders(allFolderKeys);
  }, [spaces]);

  // Add this useEffect after the existing useEffect for logging
  useEffect(() => {
    // This effect will run whenever spaces or expandedSpaces or expandedFolders change
    // It forces a re-render by updating the counter
    setUpdateCounter((prev) => prev + 1);
  }, [spaces, expandedSpaces, expandedFolders]);

  // Fetch knowledge spaces dynamically
  const fetchSpaces = async () => {
    try {
      setSpacesLoading(true);

      // Get auth headers
      const getAuthHeaders = async () => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      const response = await fetch(`${API_BASE_URL}/api/knowledge-spaces`, {
        headers: headers as HeadersInit,
      });
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Convert Project data to Space format with Q&A items
          const convertedSpaces = await Promise.all(
            result.data.map(async (project: any) => {
              // Fetch Q&As for each project
              let qaItems: SpaceItem[] = [];
              try {
                const qaResponse = await fetch(
                  `${API_BASE_URL}/api/knowledge-spaces/knowledge-space/${project.id}`,
                  { headers: headers as HeadersInit }
                );
                if (qaResponse.ok) {
                  const qaResult = await qaResponse.json();
                  if (qaResult.success) {
                    // Convert Q&As to space items with meaningful names
                    qaItems = qaResult.data.map((qa: any, index: number) => {
                      const href = `/qa/${project.id}/${qa.id}`;

                      return {
                        id: qa.id,
                        name:
                          qa.question?.title ||
                          qa.question ||
                          `Q&A ${index + 1}`,
                        type: "document" as const,
                        href: href,
                        qaData: qa, // Store full Q&A data for editing
                      };
                    });
                  }
                }
              } catch (error) {
                console.error(`Error fetching Q&As for ${project.id}:`, error);
              }

              return {
                id: project.id,
                name: project.name,
                color: "bg-primary-100",
                initial: project.initial,
                items: qaItems,
              };
            })
          );
          setSpaces(convertedSpaces);
        }
      } else {
        console.error("Failed to fetch knowledge spaces");
      }
    } catch (error) {
      console.error("Error fetching knowledge spaces:", error);
    } finally {
      setSpacesLoading(false);
    }
  };

  useEffect(() => {
    fetchSpaces();
  }, []);

  // Register fetchSpaces with the context
  useEffect(() => {
    setRefreshFunction(() => fetchSpaces);
  }, [setRefreshFunction]);

  const { platforms } = useConnectedPlatforms();
  const { user, userRole } = useAuth();

  const handleContextMenu = (
    e: React.MouseEvent,
    type: "space" | "folder" | "document",
    itemName: string,
    parentSpace: string,
    parentFolder?: string,
    spaceId?: string
  ) => {
    e.preventDefault();
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      type,
      itemName,
      parentSpace,
      parentFolder,
      spaceId,
    });
  };

  const closeContextMenu = () => {
    setContextMenu({ ...contextMenu, show: false });
  };

  const handleViewSpace = (spaceId: string) => {
    // Navigate to the QAs page for this space
    router.push(`/spaces/${spaceId}/qas`);
    closeContextMenu();
  };

  const handleViewAllSpaces = () => {
    // Navigate to the all spaces page
    router.push("/spaces");
  };

  const toggleSpace = (space: string) => {
    if (expandedSpaces.includes(space)) {
      setExpandedSpaces(expandedSpaces.filter((s) => s !== space));
    } else {
      setExpandedSpaces([...expandedSpaces, space]);
    }
  };

  const toggleFolder = (folderId: string) => {
    if (expandedFolders.includes(folderId)) {
      setExpandedFolders(expandedFolders.filter((f) => f !== folderId));
    } else {
      setExpandedFolders([...expandedFolders, folderId]);
    }
  };

  const [showAddSpaceModal, setShowAddSpaceModal] = useState(false);
  const [newSpaceName, setNewSpaceName] = useState("");
  const [newSpaceColor, setNewSpaceColor] = useState("bg-primary-100");
  const [showResponsibleUsersModal, setShowResponsibleUsersModal] =
    useState(false);
  const [selectedPrimaryAssignee, setSelectedPrimaryAssignee] = useState("");
  const [selectedSecondaryAssignee, setSelectedSecondaryAssignee] =
    useState("");
  const [teamMembers, setTeamMembers] = useState<
    {
      id: string;
      email: string;
      first_name?: string;
      last_name?: string;
      role?: string;
    }[]
  >([]);
  const [loadingTeamMembers, setLoadingTeamMembers] = useState(false);

  const handleAddSpace = () => {
    setShowAddSpaceModal(true);
  };

  // Fetch team members for responsible users assignment
  const fetchTeamMembers = async () => {
    setLoadingTeamMembers(true);
    try {
      const getAuthHeaders = async (): Promise<HeadersInit> => {
        try {
          const { createClientComponentClient } = await import(
            "@supabase/auth-helpers-nextjs"
          );
          const supabase = createClientComponentClient();
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (session?.access_token) {
            return {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            };
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return { "Content-Type": "application/json" };
      };

      const headers = await getAuthHeaders();
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

      // Get company ID from the auth context
      if (!connectedUserCompany?.company_id) {
        console.error("No company ID found in auth context");
        return [];
      }

      // Fetch company users using the integrations/company-users endpoint
      const response = await fetch(
        `${API_BASE_URL}/api/integrations/company-users/?company_id=${connectedUserCompany.company_id}`,
        { headers }
      );

      if (response.ok) {
        const data = await response.json();
        setTeamMembers(data);
        return data;
      } else {
        console.error("Failed to fetch team members:", response.status);
        return [];
      }
    } catch (error) {
      console.error("Error fetching team members:", error);
      return [];
    } finally {
      setLoadingTeamMembers(false);
    }
  };

  const createNewSpace = async () => {
    if (!newSpaceName.trim()) return;

    // Show responsible users modal first
    setLoadingTeamMembers(true);
    const teamMembersData = await fetchTeamMembers();
    setTeamMembers(teamMembersData);
    setShowResponsibleUsersModal(true);
  };

  const finalizeSpaceCreation = async () => {
    if (!newSpaceName.trim()) return;

    // Prepare project data for backend
    const projectData = {
      name: newSpaceName,
      description: `Knowledge space for ${newSpaceName}`,
      categories: [],
      repo_path: "",
      docs_path: "",
      repo_type: "github" as const,
      color: newSpaceColor, // Pass the selected color
      doc_responsible_id: selectedPrimaryAssignee || undefined,
      secondary_responsible_id: selectedSecondaryAssignee || undefined,
    };

    const result = await createProject(projectData);

    // Always close the modals and reset form, regardless of success/failure
    setNewSpaceName("");
    setShowAddSpaceModal(false);
    setShowResponsibleUsersModal(false);
    setSelectedPrimaryAssignee("");
    setSelectedSecondaryAssignee("");

    if (result.success) {
      // Use the actual ID returned by the backend
      const actualSpaceId =
        (result.data as Project)?.id ||
        newSpaceName.toLowerCase().replace(/\s+/g, "-");
      const initial = newSpaceName.charAt(0).toUpperCase();

      const newSpace: Space = {
        id: actualSpaceId,
        name: newSpaceName,
        color: newSpaceColor,
        initial: initial,
        items: [],
      };

      // Update local state
      setSpaces([...spaces, newSpace]);
      setExpandedSpaces([...expandedSpaces, newSpaceName]);

      console.log("✅ Knowledge space created successfully with assignees");
    } else {
      console.log("Space creation failed:", result.error);
      // Error handling is done automatically by the createProject function
    }
  };

  const handleAddDocument = (parentSpace: string, parentFolder?: string) => {
    // Set the modal data with the correct parameters
    setModalData({
      parentSpace,
      parentFolder: parentFolder || "",
    });

    // Show the modal
    setShowAddDocumentModal(true);
  };

  const createDocument = (
    title: string,
    parentSpace: string,
    parentFolder?: string
  ) => {
    console.log(
      `Creating document "${title}" in space "${parentSpace}"${
        parentFolder ? ` folder "${parentFolder}"` : ""
      }`
    );

    // Create a deep copy of the spaces array to avoid mutation issues
    const newSpaces = JSON.parse(JSON.stringify(spaces));

    // Find the space by name (case-insensitive to be safe)
    const spaceIndex = newSpaces.findIndex(
      (s: Space) => s.name.toLowerCase() === parentSpace.toLowerCase()
    );

    if (spaceIndex === -1) {
      console.error(`Space "${parentSpace}" not found`);
      return;
    }

    const newDocId = title.toLowerCase().replace(/\s+/g, "-");

    if (parentFolder) {
      // Find the folder (case-insensitive to be safe)
      const folderIndex = newSpaces[spaceIndex].items.findIndex(
        (item: SpaceItem) =>
          item.type === "folder" &&
          item.name.toLowerCase() === parentFolder.toLowerCase()
      );

      if (folderIndex === -1) {
        console.error(
          `Folder "${parentFolder}" not found in space "${parentSpace}"`
        );
        return;
      }

      console.log(
        `Found folder at index ${folderIndex}: ${newSpaces[spaceIndex].items[folderIndex].name}`
      );

      // Initialize children array if it doesn't exist
      if (!newSpaces[spaceIndex].items[folderIndex].children) {
        newSpaces[spaceIndex].items[folderIndex].children = [];
      }

      // Add document to folder
      const newDocument = {
        id: newDocId,
        name: title,
        type: "document" as const,
      };

      newSpaces[spaceIndex].items[folderIndex].children!.push(newDocument);
    } else {
      // Add document directly to space
      const newDocument = {
        id: newDocId,
        name: title,
        type: "document" as const,
      };

      newSpaces[spaceIndex].items.push(newDocument);
    }

    // Update the state with the new spaces array
    setSpaces(newSpaces);

    // Increment the update counter to force a re-render
    setUpdateCounter((prev) => prev + 1);

    // Ensure the space is expanded to show the new document
    if (!expandedSpaces.includes(parentSpace)) {
      setExpandedSpaces([...expandedSpaces, parentSpace]);
    }

    // If document is added to a folder, ensure the folder is expanded
    if (parentFolder) {
      const folderKey = `${parentSpace}-${parentFolder}`;
      if (!expandedFolders.includes(folderKey)) {
        setExpandedFolders([...expandedFolders, folderKey]);
      }
    }

    // Navigate to the new document
    const spaceId = newSpaces[spaceIndex].id;
    router.push(`/editor/${spaceId}/${newDocId}`);
  };

  const getTextColor = (bgColor: string) => {
    if (bgColor.includes("primary")) return "text-primary-600";
    return "text-gray-600";
  };

  // Add a handleDelete function to the SideNavigation component
  const handleCreateQA = (parentSpace: string) => {
    // Find the space to get its ID
    const space = spaces.find((s) => s.name === parentSpace);
    if (space) {
      setCreateQAData({
        projectId: space.id,
        projectName: space.name,
      });
      setShowCreateQAModal(true);
    }
    closeContextMenu();
  };

  const handleUploadFile = (parentSpace: string) => {
    setUploadModalData({
      parentSpace,
      parentFolder: "",
    });
    setShowUploadFileModal(true);
    closeContextMenu();
  };

  const handleFileSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(selectedFiles.filter((_, i) => i !== index));
  };

  const handleEditQA = (qaId: string, parentSpace: string) => {
    // Find the space to get its ID
    const space = spaces.find((s) => s.name === parentSpace);
    if (space) {
      // Find the Q&A item to get its stored data
      const qaItem = space.items.find((item) => item.id === qaId);
      if (qaItem && qaItem.qaData) {
        const qaData = qaItem.qaData;
        setEditQAData({
          qaId,
          projectId: space.id,
          question_title:
            qaData.question?.title || qaData.question?.content || "",
          question_content: qaData.question?.content || "",
          question_tags: qaData.question?.tags || [],
          answer_content: qaData.answer?.content || "",
        });
        setShowEditQAModal(true);
      } else {
        toast({
          title: "Error",
          description: "Q&A data not found",
          variant: "destructive",
        });
      }
    }
    closeContextMenu();
  };

  const handleDelete = (
    type: "space" | "folder" | "document",
    itemName: string,
    parentSpace: string,
    parentFolder?: string
  ) => {
    // Show confirmation dialog instead of immediately deleting
    setDeleteItem({
      type,
      itemName,
      parentSpace,
      parentFolder,
    });
    setShowDeleteConfirmation(true);
    closeContextMenu();
  };

  // Function to actually perform the deletion after confirmation
  const confirmDelete = async () => {
    if (!deleteItem) return;

    try {
      if (deleteItem.type === "space") {
        // Find the space to get its ID
        const space = spaces.find((s) => s.name === deleteItem.itemName);
        console.log(
          "Attempting to delete space:",
          deleteItem.itemName,
          "Space object:",
          space
        );
        if (space) {
          console.log("Calling deleteProject with ID:", space.id);
          // Call the backend API to delete the project
          const result = await deleteProject(space.id);
          console.log("Delete result:", result);
          if (result.success) {
            console.log("Delete successful, updating local state");
            // Remove from local state only after successful API call
            setSpaces(spaces.filter((s) => s.name !== deleteItem.itemName));
            toast({
              title: "Success",
              description: "Space deleted successfully",
              variant: "success",
            });
          } else {
            console.log("Delete failed:", result.error);
            toast({
              title: "Error",
              description: result.error || "Failed to delete space",
              variant: "destructive",
            });
          }
        } else {
          console.log("Space not found:", deleteItem.itemName);
          toast({
            title: "Error",
            description: "Space not found",
            variant: "destructive",
          });
        }
      } else if (deleteItem.type === "folder") {
        // Delete folder (local state only for now)
        setSpaces(
          spaces.map((space) => {
            if (space.name === deleteItem.parentSpace) {
              return {
                ...space,
                items: space.items.filter(
                  (item) => item.name !== deleteItem.itemName
                ),
              };
            }
            return space;
          })
        );
        toast({
          title: "Success",
          description: "Folder deleted successfully",
          variant: "success",
        });
      } else if (deleteItem.type === "document") {
        // Delete Q&A document - call backend API
        const space = spaces.find((s) => s.name === deleteItem.parentSpace);
        if (space) {
          console.log(
            "Attempting to delete Q&A:",
            deleteItem.itemName,
            "from project:",
            space.id
          );

          // Extract Q&A ID from the item ID
          const qaId = deleteItem.itemName; // This is now the actual Q&A ID

          const result = await qaAPI.deleteQA(space.id, qaId);

          if (result.success) {
            console.log("Q&A deleted successfully, refreshing data");
            toast({
              title: "Success",
              description: result.message,
            });
            // Refresh the spaces to show updated data
            fetchSpaces();
            // Refresh the Q&A page if it's open (it will show an error since Q&A is deleted)
            refreshQAPage(space.id, qaId);
          } else {
            console.log("Q&A delete failed:", result.message);
            toast({
              title: "Error",
              description: result.message,
              variant: "destructive",
            });
          }
        } else {
          console.log("Space not found for Q&A deletion");
          toast({
            title: "Error",
            description: "Space not found",
            variant: "destructive",
          });
        }
      }

      // Increment the update counter to force a re-render
      setUpdateCounter((prev) => prev + 1);
    } catch (error) {
      console.error("Error deleting item:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred during deletion",
        variant: "destructive",
      });
    } finally {
      // Close the confirmation dialog
      setShowDeleteConfirmation(false);
      setDeleteItem(null);
    }
  };

  // Example search terms for the placeholder
  const searchPlaceholder = "Report.docx, authentication...";

  return (
    <div
      className={`bg-white border-r border-gray-200 flex flex-col h-full transition-all duration-300 ${
        isCollapsed ? "w-16" : "w-72"
      }`}
    >
      {/* Side Navigation Content */}
      <div className="flex-1 overflow-y-auto [&::-webkit-scrollbar-thumb]:bg-[#F3F3F3] [&::-webkit-scrollbar-thumb:hover]:bg-[#E8E8E8]">
        {/* Search Bar - Only visible when not collapsed */}
        {!isCollapsed && (
          <div className="px-3 pt-4 pb-5 m-2 bg-gray-50/30 rounded-lg">
            <div className="flex justify-start">
              <Link href="/" className="flex items-center">
                <Image
                  src="/images/sagebase-purple-full.svg"
                  alt="SageBase Logo"
                  width={97}
                  height={25}
                  className="h-7 w-auto"
                  priority
                />
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/beta-au7izTqQE0bWQNLseCPR43dCXYGM4s.png"
                  alt="Beta"
                  width={60}
                  height={20}
                  className="ml-2"
                />
                {/* App version badge */}
                <VersionBadge className="ml-2" />
              </Link>
            </div>
          </div>
        )}

        {/* Main Navigation */}
        <nav className="px-2 pt-2 pb-6">
          <ul className="space-y-1">
            <li>
              <Link
                href="/dashboard"
                className={`flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  pathname === "/dashboard"
                    ? "bg-primary-50 text-primary-700"
                    : "text-black hover:bg-gray-50 hover:text-black"
                }`}
              >
                <BarChart2
                  className={`h-5 w-5 ${
                    pathname === "/dashboard"
                      ? "text-primary-500"
                      : "text-black"
                  }`}
                />
                {!isCollapsed && <span className="ml-3">Dashboard</span>}
              </Link>
            </li>
            <li>
              <Link
                href="/ai-search"
                className={`flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  pathname === "/ai-search"
                    ? "bg-primary-50 text-primary-700"
                    : "text-black hover:bg-gray-50 hover:text-black"
                }`}
              >
                <Search
                  className={`h-5 w-5 ${
                    pathname === "/ai-search"
                      ? "text-primary-500"
                      : "text-black"
                  }`}
                />
                {!isCollapsed && <span className="ml-3">AI Search</span>}
              </Link>
            </li>

            <li>
              <Link
                href="/team-knowledge-map"
                className={`flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  pathname === "/team-knowledge-map"
                    ? "bg-primary-50 text-primary-700"
                    : "text-black hover:bg-gray-50 hover:text-black"
                }`}
              >
                <Users
                  className={`h-5 w-5 ${
                    pathname === "/team-knowledge-map"
                      ? "text-primary-500"
                      : "text-black"
                  }`}
                />
                {!isCollapsed && (
                  <span className="ml-3">Team Knowledge Map</span>
                )}
              </Link>
            </li>

            <li>
              <Link
                href="/qa_notifs"
                className={`flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  pathname === "/qa-history"
                    ? "bg-primary-50 text-primary-700"
                    : "text-black hover:bg-gray-50 hover:text-black"
                }`}
              >
                <HelpCircle
                  className={`h-5 w-5 ${
                    pathname === "/qa_notifs"
                      ? "text-primary-500"
                      : "text-black"
                  }`}
                />
                {!isCollapsed && (
                  <span className="ml-3">Q&A and Notifications</span>
                )}
              </Link>
            </li>
          </ul>
        </nav>
        {!isCollapsed && <div className="border-b border-gray-200 mx-2"></div>}

        {/* Collapsible Sections */}
        {!isCollapsed && (
          <>
            <div className="px-3 py-2">
              <CollapsiblePlatforms />
            </div>

            <div className="px-3 py-2">
              <CollapsibleURLMonitor />
            </div>
          </>
        )}

        {/* KNOWLEDGE SPACES SECTION - MOVED UP */}
        {!isCollapsed && (
          <div className="px-3 py-2 m-2 rounded-lg bg-gradient-to-br from-gray-50/20 to-gray-100/30">
            <div className="flex items-center justify-between text-xs font-medium text-gray-500 mb-2 py-3">
              <span>KNOWLEDGE SPACES</span>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5"
                  onClick={handleViewAllSpaces}
                  title="View all knowledge spaces"
                >
                  <Eye className="h-4 w-4 text-gray-500 hover:text-primary-500" />
                </Button>
                {userRole === "ADMIN" && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5"
                    onClick={() => setShowAddSpaceModal(true)}
                    title="Add new space"
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {spacesLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
              </div>
            ) : (
              <ul className="space-y-1">
                {spaces.map((space) => (
                  <li key={`${space.id}-${updateCounter}`}>
                    <div
                      className="flex items-center justify-between text-sm text-gray-700 px-2 py-1.5 rounded-md hover:bg-gray-100 hover:bg-opacity-70 cursor-pointer group"
                      onClick={() => toggleSpace(space.name)}
                      onContextMenu={(e) =>
                        handleContextMenu(
                          e,
                          "space",
                          space.name,
                          space.name,
                          undefined,
                          space.id
                        )
                      }
                    >
                      <div className="flex items-center">
                        <div
                          className={`w-5 h-5 ${space.color} rounded flex items-center justify-center mr-2`}
                        >
                          <span
                            className={`${getTextColor(
                              space.color
                            )} text-xs font-medium`}
                          >
                            {space.initial}
                          </span>
                        </div>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="font-semibold text-gray-800">
                                {space.name}
                              </span>
                            </TooltipTrigger>
                            {space.name === user?.email && (
                              <TooltipContent className="max-w-xs bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200 shadow-lg">
                                <div className="flex items-start space-x-2">
                                  <div className="flex-shrink-0 mt-0.5">
                                    <div className="w-6 h-6 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                                      <Lock className="h-3 w-3 text-white" />
                                    </div>
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-1 mb-1">
                                      <User className="h-3 w-3 text-purple-600" />
                                      <span className="text-xs font-semibold text-purple-700 uppercase tracking-wide">
                                        Personal Space
                                      </span>
                                    </div>
                                    <p className="text-xs text-gray-700 leading-relaxed">
                                      This is a personal knowledge space, and
                                      its contents are private.
                                    </p>
                                    <p className="text-xs text-gray-600 mt-1">
                                      Nothing shared with the rest of the team.
                                    </p>
                                  </div>
                                </div>
                              </TooltipContent>
                            )}
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex items-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity mr-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewSpace(space.id);
                          }}
                          title="View all Q&As"
                        >
                          <Eye className="h-3.5 w-3.5 text-gray-500 hover:text-primary-500" />
                        </Button>
                        {expandedSpaces.includes(space.name) ? (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        )}
                      </div>
                    </div>

                    {expandedSpaces.includes(space.name) && (
                      <ul className="ml-7 mt-1 space-y-1">
                        {space.items.length === 0 ? (
                          <li className="text-xs text-gray-500 px-2 py-1">
                            No Q&As yet
                          </li>
                        ) : (
                          space.items.map((item) => (
                            <li key={`${space.id}-${item.id}-${updateCounter}`}>
                              <button
                                type="button"
                                className="flex items-center text-sm text-gray-600 px-2 py-1 rounded-md hover:bg-gray-200 w-full text-left"
                                onContextMenu={(e) => {
                                  e.preventDefault();
                                  handleContextMenu(
                                    e,
                                    "document",
                                    item.id,
                                    space.name
                                  );
                                }}
                                onClick={(e) => {
                                  if (e.button === 0) {
                                    // Left click only
                                    console.log(
                                      "🔗 Q&A Link clicked:",
                                      item.href || `/qa/${space.id}/${item.id}`
                                    );
                                    router.push(
                                      item.href || `/qa/${space.id}/${item.id}`
                                    );
                                  }
                                }}
                              >
                                <FileText className="h-3.5 w-3.5 text-gray-500 mr-1.5" />
                                <span className="text-xs">{item.name}</span>
                              </button>
                            </li>
                          ))
                        )}
                        {space.items.length > 0 && (
                          <li>
                            <Link
                              href={`/spaces/${space.id}/qas`}
                              className="flex items-center text-xs text-primary-600 px-2 py-1 rounded-md hover:bg-primary-50"
                            >
                              <MoreHorizontal className="h-3 w-3 mr-1.5" />
                              <span>View all Q&As</span>
                            </Link>
                          </li>
                        )}
                      </ul>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}
        {!isCollapsed && <div className="border-b border-gray-200 mx-2"></div>}
      </div>

      {/* Bottom Section */}
      <div className="px-2 pt-2 pr-2 pb-[112px] border-t border-gray-200 bg-white z-10 transition-all duration-300">
        <Button
          variant="ghost"
          size="sm"
          className="w-full flex items-center justify-center"
          onClick={() => setIsCollapsed(!isCollapsed)}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? (
            <ChevronRight className="h-5 w-5" />
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
              <path d="M9 3v18" />
              <path d="m16 15-3-3 3-3" />
            </svg>
          )}
        </Button>
      </div>

      {contextMenu.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Context Menu
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={closeContextMenu}
                className="h-8 w-8 hover:bg-gray-100 rounded-full"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="p-4 space-y-2">
              {/* Context Menu Content */}
              {contextMenu.type === "space" && (
                <>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    onClick={() => {
                      const space = spaces.find(
                        (s) => s.name === contextMenu.parentSpace
                      );
                      if (space) {
                        router.push(`/spaces/${space.id}/qas`);
                      }
                    }}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    View Q&As
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-green-600 hover:text-green-700 hover:bg-green-50"
                    onClick={() => {
                      handleCreateQA(contextMenu.parentSpace);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Q&A
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    onClick={() => {
                      handleUploadFile(contextMenu.parentSpace);
                    }}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Upload Documents containing knowledge
                  </Button>
                </>
              )}
              {contextMenu.type === "document" && (
                <Button
                  variant="outline"
                  className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  onClick={() => {
                    handleEditQA(contextMenu.itemName, contextMenu.parentSpace);
                  }}
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}

              <Button
                variant="outline"
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={() =>
                  handleDelete(
                    contextMenu.type,
                    contextMenu.itemName,
                    contextMenu.parentSpace,
                    contextMenu.parentFolder
                  )
                }
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {showAddDocumentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Add New Document
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowAddDocumentModal(false)}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const documentName = formData.get("document-name") as string;
                if (documentName.trim()) {
                  createDocument(
                    documentName,
                    modalData.parentSpace,
                    modalData.parentFolder
                  );
                  setShowAddDocumentModal(false);
                }
              }}
              className="p-4"
            >
              <div className="mb-4">
                <label
                  htmlFor="document-name"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Document Name
                </label>
                <Input
                  id="document-name"
                  name="document-name"
                  placeholder="Enter document name"
                  className="w-full"
                  autoFocus
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setShowAddDocumentModal(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Create Document</Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showAddSpaceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Add New Space
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowAddSpaceModal(false)}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <form
              onSubmit={(e) => {
                e.preventDefault();
                createNewSpace();
              }}
              className="p-4"
            >
              <div className="mb-4">
                <label
                  htmlFor="space-name"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Space Name
                </label>
                <Input
                  id="space-name"
                  value={newSpaceName}
                  onChange={(e) => setNewSpaceName(e.target.value)}
                  placeholder="Enter space name"
                  className="w-full"
                  autoFocus
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space Color
                </label>
                <div className="grid grid-cols-7 gap-3">
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-primary-100")}
                    className={`w-8 h-8 rounded-full bg-primary-100 ${
                      newSpaceColor === "bg-primary-100"
                        ? "ring-2 ring-primary-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-primary-50")}
                    className={`w-8 h-8 rounded-full bg-primary-50 ${
                      newSpaceColor === "bg-primary-50"
                        ? "ring-2 ring-primary-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-primary-200")}
                    className={`w-8 h-8 rounded-full bg-primary-200 ${
                      newSpaceColor === "bg-primary-200"
                        ? "ring-2 ring-primary-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-gray-100")}
                    className={`w-8 h-8 rounded-full bg-gray-100 ${
                      newSpaceColor === "bg-gray-100"
                        ? "ring-2 ring-gray-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-blue-100")}
                    className={`w-8 h-8 rounded-full bg-blue-100 ${
                      newSpaceColor === "bg-blue-100"
                        ? "ring-2 ring-blue-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-green-100")}
                    className={`w-8 h-8 rounded-full bg-green-100 ${
                      newSpaceColor === "bg-green-100"
                        ? "ring-2 ring-green-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-yellow-100")}
                    className={`w-8 h-8 rounded-full bg-yellow-100 ${
                      newSpaceColor === "bg-yellow-100"
                        ? "ring-2 ring-yellow-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-red-100")}
                    className={`w-8 h-8 rounded-full bg-red-100 ${
                      newSpaceColor === "bg-red-100"
                        ? "ring-2 ring-red-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-purple-100")}
                    className={`w-8 h-8 rounded-full bg-purple-100 ${
                      newSpaceColor === "bg-purple-100"
                        ? "ring-2 ring-purple-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-pink-100")}
                    className={`w-8 h-8 rounded-full bg-pink-100 ${
                      newSpaceColor === "bg-pink-100"
                        ? "ring-2 ring-pink-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-indigo-100")}
                    className={`w-8 h-8 rounded-full bg-indigo-100 ${
                      newSpaceColor === "bg-indigo-100"
                        ? "ring-2 ring-indigo-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-orange-100")}
                    className={`w-8 h-8 rounded-full bg-orange-100 ${
                      newSpaceColor === "bg-orange-100"
                        ? "ring-2 ring-orange-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-teal-100")}
                    className={`w-8 h-8 rounded-full bg-teal-100 ${
                      newSpaceColor === "bg-teal-100"
                        ? "ring-2 ring-teal-500"
                        : ""
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setNewSpaceColor("bg-cyan-100")}
                    className={`w-8 h-8 rounded-full bg-cyan-100 ${
                      newSpaceColor === "bg-cyan-100"
                        ? "ring-2 ring-cyan-500"
                        : ""
                    }`}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setShowAddSpaceModal(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={!newSpaceName.trim()}>
                  Create Space
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && deleteItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Confirm Delete
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setShowDeleteConfirmation(false);
                  setDeleteItem(null);
                }}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-4">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete{" "}
                <span className="font-semibold">"{deleteItem.itemName}"</span>?
                {deleteItem.type === "document" &&
                  " This Q&A will be permanently removed from the knowledge base."}
                {deleteItem.type === "space" &&
                  " This space and all its contents will be permanently deleted."}
                {deleteItem.type === "folder" &&
                  " This folder and all its contents will be permanently deleted."}
                This action cannot be undone.
              </p>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteConfirmation(false);
                    setDeleteItem(null);
                  }}
                >
                  Cancel
                </Button>
                <Button variant="destructive" onClick={confirmDelete}>
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Q&A Modal */}
      {showEditQAModal && editQAData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">Edit Q&A</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setShowEditQAModal(false);
                  setEditQAData(null);
                }}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <form
              onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);

                const editData = {
                  question_title: formData.get("question_title") as string,
                  question_content: formData.get("question_content") as string,
                  question_tags: (formData.get("question_tags") as string)
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag),
                  answer_content: formData.get("answer_content") as string,
                };

                try {
                  const result = await qaAPI.updateQA(
                    editQAData.projectId,
                    editQAData.qaId,
                    editData
                  );

                  if (result.success) {
                    toast({
                      title: "Success",
                      description: result.message,
                    });
                    setShowEditQAModal(false);
                    setEditQAData(null);
                    // Refresh the spaces to show updated data
                    fetchSpaces();
                    // Refresh the Q&A page if it's open
                    refreshQAPage(editQAData.projectId, editQAData.qaId);
                  } else {
                    toast({
                      title: "Error",
                      description: result.message,
                      variant: "destructive",
                    });
                  }
                } catch (error) {
                  console.error("Error updating Q&A:", error);
                  toast({
                    title: "Error",
                    description: "Failed to update Q&A",
                    variant: "destructive",
                  });
                }
              }}
              className="p-4 space-y-4"
            >
              <div>
                <label
                  htmlFor="question_title"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Title
                </label>
                <Input
                  id="question_title"
                  name="question_title"
                  defaultValue={editQAData.question_title}
                  placeholder="Enter question title"
                  className="w-full"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="question_content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Content
                </label>
                <textarea
                  id="question_content"
                  name="question_content"
                  defaultValue={editQAData.question_content}
                  placeholder="Enter question content"
                  className="w-full h-24 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="question_tags"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Tags (comma-separated)
                </label>
                <Input
                  id="question_tags"
                  name="question_tags"
                  defaultValue={editQAData.question_tags.join(", ")}
                  placeholder="django, authentication, api"
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="answer_content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Answer Content
                </label>
                <textarea
                  id="answer_content"
                  name="answer_content"
                  defaultValue={editQAData.answer_content}
                  placeholder="Enter answer content"
                  className="w-full h-32 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => {
                    setShowEditQAModal(false);
                    setEditQAData(null);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Q&A</Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Create Q&A Modal */}
      {showCreateQAModal && createQAData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Create New Q&A in {createQAData.projectName}
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setShowCreateQAModal(false);
                  setCreateQAData(null);
                }}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <form
              onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);

                const createData = {
                  question_title: formData.get("question_title") as string,
                  question_content: formData.get("question_content") as string,
                  question_tags: (formData.get("question_tags") as string)
                    .split(",")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag),
                  answer_content: formData.get("answer_content") as string,
                  question_tokens_count: 0,
                  answer_tokens_count: 0,
                };

                try {
                  const result = await qaAPI.createQA(
                    createQAData.projectId,
                    createData
                  );

                  if (result.success) {
                    toast({
                      title: "Success",
                      description: result.message,
                    });
                    setShowCreateQAModal(false);
                    setCreateQAData(null);
                    // Refresh the spaces to show new Q&A
                    fetchSpaces();
                  } else {
                    toast({
                      title: "Error",
                      description: result.message,
                      variant: "destructive",
                    });
                  }
                } catch (error) {
                  console.error("Error creating Q&A:", error);
                  toast({
                    title: "Error",
                    description: "Failed to create Q&A",
                    variant: "destructive",
                  });
                }
              }}
              className="p-4 space-y-4"
            >
              <div>
                <label
                  htmlFor="question_title"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Title *
                </label>
                <Input
                  id="question_title"
                  name="question_title"
                  placeholder="Enter question title"
                  className="w-full"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="question_content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Content *
                </label>
                <textarea
                  id="question_content"
                  name="question_content"
                  placeholder="Enter question content"
                  className="w-full h-24 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="question_tags"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Question Tags (comma-separated)
                </label>
                <Input
                  id="question_tags"
                  name="question_tags"
                  placeholder="django, authentication, api"
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="answer_content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Answer Content *
                </label>
                <textarea
                  id="answer_content"
                  name="answer_content"
                  placeholder="Enter answer content"
                  className="w-full h-32 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => {
                    setShowCreateQAModal(false);
                    setCreateQAData(null);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit">Create Q&A</Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Upload File Modal */}
      {showUploadFileModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg mx-4">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Upload className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Upload Files to {uploadModalData.parentSpace}
                  </h2>
                  <p className="text-sm text-gray-500 mt-1">
                    Add knowledge to your space
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setShowUploadFileModal(false);
                  setSelectedFiles([]);
                  setUploadResult(null);
                  setIsUploading(false);
                  setUploadProgress(0);
                }}
                className="h-8 w-8 hover:bg-gray-100 rounded-full"
                disabled={isUploading}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Info Card */}
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-1">
                      How it works
                    </h3>
                    <p className="text-sm text-blue-700">
                      Information will be extracted from the uploaded files and
                      new QAs will be created to be reviewed by an admin.
                    </p>
                  </div>
                </div>
              </div>

              <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  const formData = new FormData();

                  if (selectedFiles.length > 0) {
                    for (let file of selectedFiles) {
                      formData.append("files[]", file);
                    }

                    // Add knowledge space name parameter
                    formData.append(
                      "name_of_knowledge_space",
                      uploadModalData.parentSpace
                    );

                    try {
                      // Start upload progress
                      setIsUploading(true);
                      setUploadProgress(0);

                      // Get auth token using the same pattern as other API calls
                      const { createClientComponentClient } = await import(
                        "@supabase/auth-helpers-nextjs"
                      );
                      const supabase = createClientComponentClient();
                      const {
                        data: { session },
                      } = await supabase.auth.getSession();

                      if (!session?.access_token) {
                        toast({
                          title: "Error",
                          description: "Please log in to upload files",
                          variant: "destructive",
                        });
                        setIsUploading(false);
                        setUploadProgress(0);
                        return;
                      }

                      const API_BASE_URL =
                        process.env.NEXT_PUBLIC_BACKEND_API_URL ||
                        "http://localhost:8001";

                      // Create XMLHttpRequest for progress tracking
                      const xhr = new XMLHttpRequest();

                      // Set up progress tracking
                      xhr.upload.addEventListener("progress", (event) => {
                        if (event.lengthComputable) {
                          const progress = Math.round(
                            (event.loaded / event.total) * 100
                          );
                          setUploadProgress(progress);
                        }
                      });

                      // Set up response handling
                      xhr.addEventListener("load", () => {
                        if (xhr.status === 200) {
                          try {
                            const data = JSON.parse(xhr.responseText);

                            if (data.success) {
                              // Extract data from the new response format
                              const summary = data.summary || {};
                              const userInfo = data.user_info || {};
                              const knowledgeSpace =
                                data.knowledge_space ||
                                uploadModalData.parentSpace;
                              const results = data.results || [];

                              setUploadResult({
                                success: true,
                                detected_qas_count:
                                  summary.new_qas_detected || 0,
                                message:
                                  data.message ||
                                  `Processed ${summary.total_files || 0} files`,
                                processing_results: results.map(
                                  (result: any) => ({
                                    filename: result.filename || "Unknown file",
                                    file_size: result.file_size || 0,
                                    processing_result: {
                                      message:
                                        result.processing_result?.message ||
                                        "Processed successfully",
                                      ...result.processing_result,
                                    },
                                  })
                                ),
                                summary: summary,
                                user_info: userInfo,
                                knowledge_space: knowledgeSpace,
                              });

                              toast({
                                title: "Upload Successful!",
                                description: `Successfully processed ${
                                  summary.total_files || 0
                                } files. ${
                                  summary.new_qas_detected || 0
                                } new Q&As detected and waiting for approval.`,
                              });

                              console.log("Upload response:", data);
                              console.log("Summary:", summary);
                              console.log("User info:", userInfo);

                              // Don't close modal immediately, show result first
                              setSelectedFiles([]);
                              // Refresh spaces to show new QAs when they're created
                              fetchSpaces();
                            } else {
                              toast({
                                title: "Error",
                                description:
                                  data.error || "Failed to upload files",
                                variant: "destructive",
                              });
                            }
                          } catch (error) {
                            console.error("Error parsing response:", error);
                            toast({
                              title: "Error",
                              description: "Failed to parse server response",
                              variant: "destructive",
                            });
                          }
                        } else if (xhr.status === 401) {
                          toast({
                            title: "Authentication Error",
                            description:
                              "Authentication required. Please log in.",
                            variant: "destructive",
                          });
                        } else {
                          toast({
                            title: "Error",
                            description: `Upload failed with status ${xhr.status}`,
                            variant: "destructive",
                          });
                        }

                        setIsUploading(false);
                        setUploadProgress(0);
                      });

                      xhr.addEventListener("error", () => {
                        console.error("Error uploading files:", xhr.statusText);
                        toast({
                          title: "Error",
                          description: "Failed to upload files",
                          variant: "destructive",
                        });
                        setIsUploading(false);
                        setUploadProgress(0);
                      });

                      // Open and send the request
                      xhr.open(
                        "POST",
                        `${API_BASE_URL}/api/knowledge-spaces/upload/process-files`
                      );
                      xhr.setRequestHeader(
                        "Authorization",
                        `Bearer ${session.access_token}`
                      );
                      xhr.send(formData);
                    } catch (error) {
                      console.error("Error uploading files:", error);
                      toast({
                        title: "Error",
                        description: "Failed to upload files",
                        variant: "destructive",
                      });
                      setIsUploading(false);
                      setUploadProgress(0);
                    }
                  }
                }}
                className="space-y-6"
              >
                {/* File Upload Area - Only show when no upload result */}
                {!uploadResult && (
                  <>
                    <div>
                      <label
                        htmlFor="file-upload"
                        className="block text-sm font-medium text-gray-700 mb-3"
                      >
                        Select Files
                      </label>

                      {/* Drag & Drop Area */}
                      <div className="relative">
                        <input
                          id="file-upload"
                          type="file"
                          multiple
                          accept=".pdf,.docx,.txt"
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                          required
                          onChange={handleFileSelection}
                        />
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-colors">
                          <div className="flex flex-col items-center space-y-3">
                            <div className="p-3 bg-gray-100 rounded-full">
                              <Upload className="h-8 w-8 text-gray-600" />
                            </div>
                            <div>
                              <p className="text-lg font-medium text-gray-900">
                                Drop files here or click to browse
                              </p>
                              <p className="text-sm text-gray-500 mt-1">
                                PDF, DOCX, TXT files accepted
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* File List Preview */}
                      {selectedFiles.length > 0 && (
                        <div className="mt-4 space-y-2">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">
                            Selected Files ({selectedFiles.length})
                          </h4>
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {selectedFiles.map((file, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                              >
                                <div className="flex items-center space-x-3">
                                  <div className="p-1 bg-blue-100 rounded">
                                    <FileText className="h-4 w-4 text-blue-600" />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {file.name}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {(file.size / 1024 / 1024).toFixed(2)} MB
                                    </p>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(index)}
                                  className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Supported Formats */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">
                        Supported Formats
                      </h4>
                      <div className="flex items-center space-x-4 text-xs text-gray-600">
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4 text-red-500" />
                          <span>PDF</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4 text-blue-500" />
                          <span>DOCX</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4 text-green-500" />
                          <span>TXT</span>
                        </div>
                      </div>
                    </div>

                    {/* Upload Progress */}
                    {isUploading && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                          <div>
                            <h4 className="text-sm font-medium text-blue-900">
                              Uploading Files...
                            </h4>
                            <p className="text-xs text-blue-700">
                              Please wait while your files are being processed
                            </p>
                          </div>
                        </div>
                        <div className="w-full bg-blue-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                            style={{ width: `${uploadProgress}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                          <span className="text-xs text-blue-700">
                            {uploadProgress}% complete
                          </span>
                          <span className="text-xs text-blue-600 font-medium">
                            {uploadProgress < 30 && "Preparing files..."}
                            {uploadProgress >= 30 &&
                              uploadProgress < 60 &&
                              "Uploading..."}
                            {uploadProgress >= 60 &&
                              uploadProgress < 90 &&
                              "Processing..."}
                            {uploadProgress >= 90 && "Almost done..."}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                      <Button
                        variant="outline"
                        type="button"
                        onClick={() => {
                          setShowUploadFileModal(false);
                          setSelectedFiles([]);
                          setUploadResult(null);
                        }}
                        className="px-6"
                        disabled={isUploading}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        className="px-6 bg-blue-600 hover:bg-blue-700"
                        disabled={isUploading || selectedFiles.length === 0}
                      >
                        {isUploading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            Upload Files
                          </>
                        )}
                      </Button>
                    </div>
                  </>
                )}
              </form>

              {/* Success Result Section */}
              {uploadResult && uploadResult.success && (
                <div className="mt-6 p-6 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-lg">
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-green-100 rounded-full">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-green-900 mb-2">
                        Upload Successful! 🎉
                      </h3>

                      {/* Summary Stats */}
                      {uploadResult.summary && (
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div className="bg-white rounded-lg p-3 border border-green-200">
                            <div className="text-2xl font-bold text-green-600">
                              {uploadResult.summary.new_qas_detected || 0}
                            </div>
                            <div className="text-xs text-gray-600">
                              New Q&As Created
                            </div>
                          </div>
                          <div className="bg-white rounded-lg p-3 border border-green-200">
                            <div className="text-2xl font-bold text-blue-600">
                              {uploadResult.summary.total_files || 0}
                            </div>
                            <div className="text-xs text-gray-600">
                              Files Processed
                            </div>
                          </div>
                        </div>
                      )}

                      {/* User Info */}
                      {uploadResult.user_info && (
                        <div className="bg-white rounded-lg p-3 border border-green-200 mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            Processing Details
                          </h4>
                          <div className="space-y-1 text-xs text-gray-600">
                            <div className="flex justify-between">
                              <span>User:</span>
                              <span className="font-medium">
                                {uploadResult.user_info.user_email}
                              </span>
                            </div>
                            {uploadResult.user_info.company_name && (
                              <div className="flex justify-between">
                                <span>Company:</span>
                                <span className="font-medium">
                                  {uploadResult.user_info.company_name}
                                </span>
                              </div>
                            )}
                            <div className="flex justify-between">
                              <span>Knowledge Space:</span>
                              <span className="font-medium">
                                {uploadResult.knowledge_space}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Approval Notice */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div className="flex items-start space-x-3">
                          <div className="p-1 bg-blue-100 rounded-full">
                            <Info className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-blue-900 mb-1">
                              Next Steps
                            </h4>
                            <p className="text-sm text-blue-700">
                              {uploadResult.summary?.new_qas_detected || 0} new
                              Q&As have been created and are now waiting for
                              admin approval. You'll be notified once they're
                              approved and available in your knowledge space.
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* File Processing Results */}
                      {uploadResult.processing_results &&
                        uploadResult.processing_results.length > 0 && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium text-gray-900 mb-3">
                              File Processing Details
                            </h4>
                            <div className="space-y-2 max-h-40 overflow-y-auto">
                              {uploadResult.processing_results.map(
                                (result, index) => (
                                  <div
                                    key={index}
                                    className="p-3 bg-white rounded border border-gray-200"
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="flex items-center space-x-2 mb-1">
                                          <FileText className="h-4 w-4 text-green-600" />
                                          <span className="text-sm font-medium text-gray-900">
                                            {result.filename}
                                          </span>
                                          <span className="text-xs text-gray-500">
                                            (
                                            {(result.file_size / 1024).toFixed(
                                              1
                                            )}{" "}
                                            KB)
                                          </span>
                                        </div>
                                        {result.processing_result.message && (
                                          <p className="text-xs text-gray-600 mt-1">
                                            {result.processing_result.message}
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                  <div className="mt-6 flex justify-end space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowUploadFileModal(false);
                        setUploadResult(null);
                        setIsUploading(false);
                        setUploadProgress(0);
                      }}
                      className="border-green-300 text-green-700 hover:bg-green-50"
                    >
                      Close
                    </Button>
                    <Button
                      onClick={() => {
                        setShowUploadFileModal(false);
                        setUploadResult(null);
                        setIsUploading(false);
                        setUploadProgress(0);
                        // Navigate to Q&A notifications page
                        router.push("/qa_notifs");
                      }}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      View Pending Approvals
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Responsible Users Modal for New Space Creation */}
      {showResponsibleUsersModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">
                Assign Responsible Users for "{newSpaceName}"
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setShowResponsibleUsersModal(false);
                  setSelectedPrimaryAssignee("");
                  setSelectedSecondaryAssignee("");
                }}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <p className="text-sm text-gray-600 mb-4">
                  Assign primary and secondary responsible users for this
                  knowledge space. These users will be responsible for managing
                  documentation and content.
                </p>
              </div>

              {/* Primary Assignee */}
              <div className="mb-6">
                <h3 className="text-md font-semibold text-gray-900 mb-3">
                  Primary Assignee
                </h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {loadingTeamMembers ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-gray-600">
                        Loading team members...
                      </span>
                    </div>
                  ) : teamMembers.length > 0 ? (
                    teamMembers.map((member) => {
                      // Handle the new data structure with first_name and last_name
                      const fullName =
                        member.first_name && member.last_name
                          ? `${member.first_name} ${member.last_name}`.trim()
                          : member.first_name ||
                            member.last_name ||
                            member.email;

                      const initials =
                        member.first_name && member.last_name
                          ? `${member.first_name.charAt(
                              0
                            )}${member.last_name.charAt(0)}`.toUpperCase()
                          : member.first_name
                          ? member.first_name.charAt(0).toUpperCase()
                          : member.last_name
                          ? member.last_name.charAt(0).toUpperCase()
                          : member.email?.charAt(0).toUpperCase() || "U";

                      return (
                        <label
                          key={member.id}
                          className={`flex items-center gap-3 p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors ${
                            selectedPrimaryAssignee === member.id
                              ? "bg-blue-50 border-blue-300"
                              : "border-gray-200"
                          }`}
                        >
                          <input
                            type="radio"
                            name="primaryAssignee"
                            value={member.id}
                            checked={selectedPrimaryAssignee === member.id}
                            onChange={(e) =>
                              setSelectedPrimaryAssignee(e.target.value)
                            }
                            className="text-blue-600 focus:ring-blue-500"
                          />
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                              {initials}
                            </div>
                            <div>
                              <span className="font-medium text-gray-900">
                                {fullName}
                              </span>
                              {member.role && (
                                <p className="text-xs text-gray-500">
                                  {member.role}
                                </p>
                              )}
                            </div>
                          </div>
                        </label>
                      );
                    })
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No team members found. Please add users in the Admin
                      section.
                    </div>
                  )}
                </div>
              </div>

              {/* Secondary Assignee */}
              <div className="mb-6">
                <h3 className="text-md font-semibold text-gray-900 mb-3">
                  Secondary Assignee (Optional)
                </h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {teamMembers.length > 0 ? (
                    teamMembers.map((member) => {
                      // Handle the new data structure with first_name and last_name
                      const fullName =
                        member.first_name && member.last_name
                          ? `${member.first_name} ${member.last_name}`.trim()
                          : member.first_name ||
                            member.last_name ||
                            member.email;

                      const initials =
                        member.first_name && member.last_name
                          ? `${member.first_name.charAt(
                              0
                            )}${member.last_name.charAt(0)}`.toUpperCase()
                          : member.first_name
                          ? member.first_name.charAt(0).toUpperCase()
                          : member.last_name
                          ? member.last_name.charAt(0).toUpperCase()
                          : member.email?.charAt(0).toUpperCase() || "U";

                      return (
                        <label
                          key={member.id}
                          className={`flex items-center gap-3 p-3 border rounded-lg hover:bg-purple-50 cursor-pointer transition-colors ${
                            selectedSecondaryAssignee === member.id
                              ? "bg-purple-50 border-purple-300"
                              : "border-gray-200"
                          }`}
                        >
                          <input
                            type="radio"
                            name="secondaryAssignee"
                            value={member.id}
                            checked={selectedSecondaryAssignee === member.id}
                            onChange={(e) =>
                              setSelectedSecondaryAssignee(e.target.value)
                            }
                            className="text-purple-600 focus:ring-purple-500"
                          />
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                              {initials}
                            </div>
                            <div>
                              <span className="font-medium text-gray-900">
                                {fullName}
                              </span>
                              {member.role && (
                                <p className="text-xs text-gray-500">
                                  {member.role}
                                </p>
                              )}
                            </div>
                          </div>
                        </label>
                      );
                    })
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No team members found.
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-3 pt-4 border-t border-gray-200">
                <Button
                  onClick={() => {
                    setShowResponsibleUsersModal(false);
                    setSelectedPrimaryAssignee("");
                    setSelectedSecondaryAssignee("");
                  }}
                  variant="outline"
                  className="flex-1"
                >
                  Skip
                </Button>
                <Button
                  onClick={finalizeSpaceCreation}
                  disabled={
                    !selectedPrimaryAssignee && !selectedSecondaryAssignee
                  }
                  className="flex-1"
                >
                  Create Space
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
