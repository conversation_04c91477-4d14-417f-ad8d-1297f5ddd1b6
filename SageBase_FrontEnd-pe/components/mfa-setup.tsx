"use client"

import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, CheckCircle } from "lucide-react"

export default function MFASetup() {
  const { user } = useAuth()
  const [factorId, setFactorId] = useState<string | null>(null)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [verifyCode, setVerifyCode] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isMFAEnabled, setIsMFAEnabled] = useState(false)
  const [isEnrolling, setIsEnrolling] = useState(false)

  // Check if MFA is already enabled
  useEffect(() => {
    const checkMFA = async () => {
      try {
        const { data, error } = await supabase.auth.mfa.listFactors()

        if (error) {
          console.error("Error checking MFA status:", error)
          return
        }

        const verifiedFactors = data.totp.filter((factor) => factor.verified)
        setIsMFAEnabled(verifiedFactors.length > 0)
      } catch (err) {
        console.error("Error checking MFA status:", err)
      }
    }

    if (user) {
      checkMFA()
    }
  }, [user])

  const startEnrollment = async () => {
    setIsEnrolling(true)
    setError(null)
    setSuccess(null)

    try {
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: "totp",
      })

      if (error) {
        setError(error.message)
        return
      }

      setFactorId(data.id)
      setQrCode(data.totp.qr_code)
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    }
  }

  const verifyMFA = async () => {
    if (!factorId) return

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const { error } = await supabase.auth.mfa.challengeAndVerify({
        factorId,
        code: verifyCode,
      })

      if (error) {
        setError(error.message)
      } else {
        setSuccess("MFA enabled successfully!")
        setIsMFAEnabled(true)
        setIsEnrolling(false)
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  const disableMFA = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const { data, error } = await supabase.auth.mfa.listFactors()

      if (error) {
        setError(error.message)
        return
      }

      const verifiedFactors = data.totp.filter((factor) => factor.verified)

      if (verifiedFactors.length > 0) {
        const { error: unenrollError } = await supabase.auth.mfa.unenroll({
          factorId: verifiedFactors[0].id,
        })

        if (unenrollError) {
          setError(unenrollError.message)
        } else {
          setSuccess("MFA disabled successfully!")
          setIsMFAEnabled(false)
        }
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5 text-emerald-600" />
          Multi-Factor Authentication
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account by enabling multi-factor authentication.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-emerald-50 text-emerald-800 border-emerald-200">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {isMFAEnabled && !isEnrolling ? (
          <div className="flex items-center space-x-2 text-emerald-600">
            <CheckCircle className="h-5 w-5" />
            <span>MFA is enabled for your account</span>
          </div>
        ) : isEnrolling ? (
          <div className="space-y-4">
            {qrCode && (
              <div className="flex flex-col items-center space-y-4">
                <p className="text-sm text-gray-600 mb-2">
                  Scan this QR code with your authenticator app (like Google Authenticator, Authy, or 1Password)
                </p>
                <div className="border p-4 rounded-lg bg-white">
                  <img src={qrCode || "/placeholder.svg"} alt="QR Code for MFA" width={200} height={200} />
                </div>
                <div className="w-full space-y-2">
                  <Label htmlFor="verify-code">Enter the 6-digit code from your authenticator app</Label>
                  <Input
                    id="verify-code"
                    value={verifyCode}
                    onChange={(e) => setVerifyCode(e.target.value)}
                    placeholder="000000"
                    maxLength={6}
                  />
                </div>
              </div>
            )}
          </div>
        ) : null}
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        {isMFAEnabled && !isEnrolling ? (
          <Button variant="destructive" onClick={disableMFA} disabled={isLoading}>
            {isLoading ? "Disabling..." : "Disable MFA"}
          </Button>
        ) : isEnrolling ? (
          <>
            <Button variant="outline" onClick={() => setIsEnrolling(false)} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              onClick={verifyMFA}
              disabled={isLoading || verifyCode.length !== 6}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              {isLoading ? "Verifying..." : "Verify"}
            </Button>
          </>
        ) : (
          <Button onClick={startEnrollment} className="bg-emerald-600 hover:bg-emerald-700">
            Enable MFA
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
