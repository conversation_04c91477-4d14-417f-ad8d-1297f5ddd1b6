"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield, Clock } from "lucide-react";

export default function MFASetup() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5 text-emerald-600" />
          Multi-Factor Authentication
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account by enabling
          multi-factor authentication.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Alert className="mb-4 bg-blue-50 text-blue-800 border-blue-200">
          <Clock className="h-4 w-4 mr-2" />
          <AlertDescription>
            MFA functionality is coming soon! We're working on integrating
            multi-factor authentication with our Django backend. This feature
            will allow you to use authenticator apps like Google Authenticator,
            <PERSON><PERSON>, or 1Password for enhanced security.
          </AlertDescription>
        </Alert>

        <div className="text-sm text-gray-600 space-y-2">
          <p>When MFA is available, you'll be able to:</p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Enable TOTP-based authentication</li>
            <li>Scan QR codes with your authenticator app</li>
            <li>Generate 6-digit codes for login verification</li>
            <li>Disable MFA if needed</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button disabled className="bg-gray-400 cursor-not-allowed">
          Coming Soon
        </Button>
      </CardFooter>
    </Card>
  );
}
