"use client";

import type React from "react";
import { getBackendUrl } from '@/lib/api-config';

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar } from "@/components/ui/avatar";
import {
  Sparkles,
  Send,
  X,
  Maximize2,
  Minimize2,
  ChevronDown,
  ChevronUp,
  Monitor,
  Bot,
  User,
  MoreHorizontal,
  Paperclip,
  Mic,
} from "lucide-react";
import Image from "next/image";
import HtmlResponse from "./html-response";

// Backend URL configuration
const BACKEND_BASE_URL =
  getBackendUrl();

type Message = {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
};

type ChatInterfaceProps = {
  initialContext?: string;
  onClose?: () => void;
};

// Enhanced chat message styles with modern design and better colors
const chatMessageStyles = `
  .chat-message-content {
    line-height: 1.6;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
  }
  
  .chat-message-content a {
    color: #4907E2;
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 3px;
    transition: all 0.2s ease;
    font-weight: 500;
  }
  
  .chat-message-content a:hover {
    color: #3B1398;
    text-decoration-thickness: 3px;
    background-color: rgba(73, 7, 226, 0.05);
    padding: 0 2px;
    border-radius: 3px;
  }
  
  .chat-message-content ul, .chat-message-content ol {
    margin: 0.75rem 0;
    padding-left: 0;
  }
  
  .chat-message-content li {
    margin-bottom: 0.25rem;
    line-height: 1.5;
  }
  
  .chat-message-content p {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .chat-message-content p:last-child {
    margin-bottom: 0;
  }
  
  .chat-message-content h1, 
  .chat-message-content h2, 
  .chat-message-content h3 {
    font-weight: 600;
    line-height: 1.3;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .chat-message-content h1:first-child,
  .chat-message-content h2:first-child,
  .chat-message-content h3:first-child {
    margin-top: 0;
  }
  
  .chat-message-content pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 0.75rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
    border-left: 4px solid #4907E2;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    white-space: pre-wrap;
  }
  
  .chat-message-content code {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    font-weight: 500;
  }
  
  .chat-message-content blockquote {
    border-left: 4px solid #4907E2;
    background-color: rgba(73, 7, 226, 0.05);
    padding: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #374151;
    border-radius: 0 0.5rem 0.5rem 0;
  }

  .chat-message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .chat-message-content th,
  .chat-message-content td {
    border: 1px solid #e5e7eb;
    padding: 0.75rem;
    text-align: left;
  }

  .chat-message-content th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
  }

  .chat-message-content tr:nth-child(even) {
    background-color: #f9fafb;
  }

  .chat-message-content hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #4907E2, #8b5cf6, #4907E2);
    margin: 1.5rem 0;
    border-radius: 1px;
  }

  /* Enhance code syntax highlighting appearance */
  .chat-message-content pre code {
    color: inherit;
    background: none;
    padding: 0;
    border: none;
  }

  /* Better spacing for nested elements */
  .chat-message-content > *:first-child {
    margin-top: 0;
  }

  .chat-message-content > *:last-child {
    margin-bottom: 0;
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #94a3b8;
    animation: typing-bounce 1.4s infinite ease-in-out;
  }

  .typing-dot:nth-child(1) { animation-delay: -0.32s; }
  .typing-dot:nth-child(2) { animation-delay: -0.16s; }

  @keyframes typing-bounce {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .message-bubble {
    position: relative;
    transition: all 0.2s ease;
  }

  .message-bubble:hover {
    transform: translateY(-1px);
  }

  .user-message {
    background: linear-gradient(135deg, #4907E2 0%, #3B1398 100%);
    color: white;
    border-radius: 1.25rem 1.25rem 0.25rem 1.25rem;
    box-shadow: 0 4px 12px rgba(73, 7, 226, 0.15);
  }

  .assistant-message {
    background: white;
    color: #1e293b;
    border: 1px solid #e2e8f0;
    border-radius: 1.25rem 1.25rem 1.25rem 0.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .welcome-message {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-radius: 1.25rem;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  }
`;

// Enhanced markdown to HTML converter with better styling
const markdownToHtml = (markdown: string): string => {
  if (!markdown) return "";

  console.log("🔄 MARKDOWN PARSER INPUT:", markdown.substring(0, 200));
  
  const result = (
    markdown
      // Headers with better styling
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-base font-semibold mt-3 mb-2 text-gray-900 border-b border-gray-200 pb-1">$1</h3>'
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-lg font-semibold mt-3 mb-2 text-gray-900 border-b border-purple-200 pb-1">$1</h2>'
      )
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-xl font-bold mt-3 mb-2 text-gray-900 border-b border-purple-300 pb-1">$1</h1>'
      )

      // Bold and italic with better styling
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-gray-800">$1</em>')

      // Code blocks with syntax highlighting-like styling
      .replace(
        /```(\w+)?\n([\s\S]*?)```/g,
        '<div class="my-4"><div class="bg-gray-800 text-gray-100 text-xs px-3 py-1 rounded-t-lg font-mono">$1</div><pre class="bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-x-auto border-l-4 border-purple-500"><code class="text-sm font-mono leading-relaxed">$2</code></pre></div>'
      )
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-purple-50 text-purple-800 px-2 py-1 rounded font-mono text-sm border">$1</code>'
      )

      // Enhanced links with better styling
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" class="text-blue-600 hover:text-blue-800 underline decoration-2 underline-offset-2 transition-colors duration-200 font-medium" target="_blank" rel="noopener noreferrer">$1 ↗</a>'
      )

      // Auto-link URLs that aren't already in markdown links
      .replace(
        /(?<![\[\(])(https?:\/\/[^\s\)]+)(?![\]\)])/g,
        '<a href="$1" class="text-blue-600 hover:text-blue-800 underline decoration-2 underline-offset-2 transition-colors duration-200 font-medium break-all" target="_blank" rel="noopener noreferrer">$1 ↗</a>'
      )

      // Enhanced lists with better spacing and styling
      .replace(/^\* (.*$)/gim, '<li class="ml-6 mb-1 relative before:content-["•"] before:text-purple-600 before:font-bold before:absolute before:-ml-4">$1</li>')
      .replace(/^- (.*$)/gim, '<li class="ml-6 mb-1 relative before:content-["–"] before:text-purple-600 before:font-bold before:absolute before:-ml-4">$1</li>')
      .replace(/^(\d+)\. (.*$)/gim, '<li class="ml-6 mb-1 list-decimal">$2</li>')

      // Blockquotes
      .replace(
        /^> (.*$)/gim,
        '<blockquote class="border-l-4 border-purple-300 pl-4 py-2 my-4 bg-purple-50 italic text-gray-700">$1</blockquote>'
      )

      // Tables (basic support)
      .replace(
        /\|(.+)\|/g,
        '<tr class="border-b border-gray-200">$1</tr>'
      )

      // Wrap consecutive list items
      .replace(
        /((?:<li class="ml-6[^>]*>.*<\/li>\s*)+)/g,
        '<ul class="my-3 space-y-1">$1</ul>'
      )

      // Paragraphs with better spacing
      .replace(/\n\n/g, '</p><p class="mb-2 leading-normal">')
      .replace(/^(.+)$/gm, '<p class="mb-2 leading-normal">$1</p>')

      // Clean up empty paragraphs
      .replace(/<p class="mb-2 leading-normal"><\/p>/g, "")
      .replace(/<p class="mb-2 leading-normal">\s*<\/p>/g, "")

      // Clean up multiple newlines
      .replace(/\n\s*\n/g, "\n")

      // Horizontal rules
      .replace(/^---$/gm, '<hr class="my-6 border-gray-300">')

      // Final cleanup
      .trim()
  );
  
  console.log("✅ MARKDOWN PARSER OUTPUT:", result.substring(0, 200));
  return result;
};

export default function ChatInterface({
  initialContext,
  onClose,
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      content:
        initialContext ||
        "Hello! I'm your AI assistant. How can I help you with your search?",
      role: "assistant",
      timestamp: new Date(),
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Add this inside the component, after the state declarations
  useEffect(() => {
    // Add the styles to the document
    const styleElement = document.createElement("style");
    styleElement.innerHTML = chatMessageStyles;
    document.head.appendChild(styleElement);

    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    try {
      console.log("Sending message to backend:", input);
      
      // Call the backend orchestrator endpoint
      const response = await fetch(`${BACKEND_BASE_URL}/api/agent/ask/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          question: input,
          context: {
            user_id: "chat-interface-user",
            user_email: "<EMAIL>",
            filters: {
              platforms: [],
              tags: [],
              authors: [],
            },
            preferences: [],
            metadata: {
              total_filters: 0,
              has_platform_filters: false,
              has_tag_filters: false,
              has_author_filters: false,
            },
            history: messages
              .slice(-5)
              .map((msg) => ({
                user: msg.role === "user" ? msg.content : "",
                answer: msg.role === "assistant" ? msg.content : "",
              }))
              .filter((pair) => pair.user && pair.answer),
          },
          user_email: "<EMAIL>",
        }),
      });

      console.log("Backend response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Backend error response:", errorText);
        throw new Error(`Backend responded with status: ${response.status}. Response: ${errorText}`);
      }

      const data = await response.json();
      console.log("🔍 BACKEND RESPONSE DEBUG:");
      console.log("- Full response data:", data);
      console.log("- Raw answer content:", data.answer);
      console.log("- Answer type:", typeof data.answer);
      console.log("- Answer length:", data.answer?.length || 0);
      console.log("- First 200 chars:", data.answer?.substring(0, 200));

      // Check if the response is already HTML or if it's markdown/plain text
      const rawAnswer = data.answer || "I'm sorry, I couldn't generate a response at this time.";
      let processedContent = rawAnswer;
      
      // If it doesn't contain HTML tags, treat it as markdown
      if (!rawAnswer.includes('<') || !rawAnswer.includes('>')) {
        console.log("🔄 Converting markdown to HTML");
        processedContent = markdownToHtml(rawAnswer);
        console.log("✅ Converted HTML:", processedContent.substring(0, 200));
      } else {
        console.log("📄 Content appears to be HTML already");
      }
      
      console.log("🎯 FINAL PROCESSED CONTENT:");
      console.log("- Content length:", processedContent.length);
      console.log("- Content preview:", processedContent.substring(0, 300));

      // Add the response to messages
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: processedContent,
        role: "assistant",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error generating response:", error);

      // Add a fallback message when the API fails
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `I'm sorry, I encountered an error processing your request: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,
        role: "assistant",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    if (isMinimized) setIsMinimized(false);
    if (isFullscreen) setIsFullscreen(false);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
    if (isFullscreen) setIsFullscreen(false);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (isMinimized) setIsMinimized(false);
    if (isExpanded) setIsExpanded(false);
  };

  return (
    <div
      className={`bg-white shadow-xl border border-gray-200 transition-all duration-300 z-50 flex flex-col overflow-hidden ${
        isFullscreen
          ? "fixed inset-0 rounded-none"
          : isExpanded
          ? "fixed inset-4 rounded-xl"
          : isMinimized
          ? "fixed bottom-4 right-4 w-80 h-14 rounded-xl"
          : "fixed bottom-4 right-4 w-[400px] h-[450px] rounded-xl max-w-[90vw] max-h-[80vh]"
      }`}
    >
      {/* Enhanced Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-primary-50 to-purple-50">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <Bot className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-lg">SageBase Assistant</h3>
            <p className="text-sm text-gray-600">AI-powered knowledge companion</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          {!isMinimized && !isFullscreen && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
              onClick={toggleExpand}
            >
              {isExpanded ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          )}
          {!isMinimized && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Monitor className="h-4 w-4" />
              )}
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
            onClick={toggleMinimize}
          >
            {isMinimized ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          {onClose && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Enhanced Chat Messages */}
      {!isMinimized && (
        <div
          className={`flex-1 overflow-y-auto overflow-x-hidden bg-gray-50 ${
            isFullscreen ? "p-8" : "p-4"
          }`}
        >
          <div className="w-full space-y-4">
            {messages.map((message, index) => {
              console.log(`🎨 RENDERING MESSAGE ${index}:`, {
                id: message.id,
                role: message.role,
                contentLength: message.content.length,
                contentPreview: message.content.substring(0, 100),
                isWelcome: index === 0
              });
              
              return (
              <div
                key={message.id}
                className={`flex ${
                  message.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`flex items-end gap-2 ${
                    isFullscreen ? "max-w-[90%]" : "max-w-[85%]"
                  }`}
                >
                  {message.role === "assistant" && (
                    <Avatar className="h-8 w-8 bg-gradient-to-br from-primary-500 to-purple-600 shadow-md">
                      <Bot className="h-4 w-4 text-white" />
                    </Avatar>
                  )}
                  <div
                    className={`message-bubble p-3 min-w-0 flex-1 ${
                      message.role === "user"
                        ? "user-message"
                        : index === 0
                        ? "welcome-message"
                        : "assistant-message"
                    }`}
                  >
                    <div
                      className={`text-sm chat-message-content overflow-hidden ${
                        message.role === "user" || index === 0
                          ? "text-white"
                          : "text-gray-800"
                      }`}
                    >
                      <HtmlResponse
                        content={message.content}
                        className={
                          message.role === "user" || index === 0
                            ? "text-white"
                            : "text-gray-800"
                        }
                      />
                    </div>
                    <div
                      className={`text-xs mt-2 ${
                        message.role === "user"
                          ? "text-white/70"
                          : index === 0
                          ? "text-white/70"
                          : "text-gray-500"
                      }`}
                    >
                      {message.timestamp.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </div>
                  </div>
                  {message.role === "user" && (
                    <Avatar className="h-8 w-8 bg-gradient-to-br from-gray-500 to-gray-600 shadow-md">
                      <User className="h-4 w-4 text-white" />
                    </Avatar>
                  )}
                </div>
              </div>
              );
            })}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-end gap-3">
                  <Avatar className="h-8 w-8 bg-gradient-to-br from-primary-500 to-purple-600 shadow-md">
                    <Bot className="h-4 w-4 text-white" />
                  </Avatar>
                  <div className="assistant-message p-4">
                    <div className="typing-indicator">
                      <div className="typing-dot"></div>
                      <div className="typing-dot"></div>
                      <div className="typing-dot"></div>
                    </div>
                    <div className="text-xs mt-2 text-gray-500">
                      SageBase is typing...
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>
      )}

      {/* Enhanced Chat Input */}
      {!isMinimized && (
        <div
          className={`border-t border-gray-200 bg-white ${
            isFullscreen ? "p-6" : "p-4"
          }`}
        >
          <div className="w-full">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                title="Attach file"
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              <div className="flex-1 relative">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your message..."
                  className="w-full py-3 px-4 pr-12 text-base border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-sm resize-none"
                  disabled={isLoading}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 text-gray-400 hover:text-gray-600"
                  title="Voice input"
                >
                  <Mic className="h-4 w-4" />
                </Button>
              </div>
              <Button
                onClick={handleSendMessage}
                disabled={!input.trim() || isLoading}
                size="icon"
                className="h-10 w-10 bg-gradient-to-r from-primary-500 to-purple-600 hover:from-primary-600 hover:to-purple-700 text-white shadow-lg rounded-xl"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
              Press Enter to send, Shift+Enter for new line
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
