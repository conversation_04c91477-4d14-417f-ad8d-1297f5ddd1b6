"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  Hand,
  MousePointer,
  Square,
  Diamond,
  Circle,
  ArrowRight,
  Minus,
  Link,
  Type,
  ImageIcon,
  Eraser,
  Layers,
} from "lucide-react"

type DrawingTool =
  | "select"
  | "hand"
  | "rectangle"
  | "diamond"
  | "circle"
  | "arrow"
  | "line"
  | "link"
  | "text"
  | "image"
  | "eraser"
  | "group"

interface DrawingToolbarProps {
  onToolChange: (tool: DrawingTool) => void
  currentTool: DrawingTool
}

export default function DrawingToolbar({ onToolChange, currentTool }: DrawingToolbarProps) {
  const [isLocked, setIsLocked] = useState(false)

  const tools = [
    { id: "select" as DrawingTool, icon: MousePointer, tooltip: "Selection Tool (V)" },
    { id: "hand" as DrawingTool, icon: Hand, tooltip: "Hand Tool (H)" },
    { id: "rectangle" as DrawingTool, icon: Square, tooltip: "Rectangle (R)" },
    { id: "diamond" as DrawingTool, icon: Diamond, tooltip: "Diamond (D)" },
    { id: "circle" as DrawingTool, icon: Circle, tooltip: "Circle (O)" },
    { id: "arrow" as DrawingTool, icon: ArrowRight, tooltip: "Arrow (A)" },
    { id: "line" as DrawingTool, icon: Minus, tooltip: "Line (L)" },
    { id: "link" as DrawingTool, icon: Link, tooltip: "Link" },
    { id: "text" as DrawingTool, icon: Type, tooltip: "Text (T)" },
    { id: "image" as DrawingTool, icon: ImageIcon, tooltip: "Image" },
    { id: "eraser" as DrawingTool, icon: Eraser, tooltip: "Eraser (E)" },
    { id: "group" as DrawingTool, icon: Layers, tooltip: "Group/Layers" },
  ]

  return (
    <div className="flex items-center gap-1 p-2 bg-white rounded-lg shadow-sm border border-gray-200 max-w-fit mx-auto mb-2">
      <Button
        variant={isLocked ? "default" : "ghost"}
        size="icon"
        className={`h-8 w-8 ${isLocked ? "bg-gray-200" : ""}`}
        onClick={() => setIsLocked(!isLocked)}
        title="Lock/Unlock Elements"
      >
        <Lock className="h-4 w-4" />
      </Button>

      {tools.map((tool) => (
        <Button
          key={tool.id}
          variant={currentTool === tool.id ? "secondary" : "ghost"}
          size="icon"
          className={`h-8 w-8 ${currentTool === tool.id ? "bg-purple-100 text-purple-800" : ""}`}
          onClick={() => onToolChange(tool.id)}
          title={tool.tooltip}
        >
          <tool.icon className="h-4 w-4" />
        </Button>
      ))}
    </div>
  )
}
