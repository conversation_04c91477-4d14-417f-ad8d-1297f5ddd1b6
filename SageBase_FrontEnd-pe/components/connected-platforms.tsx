"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { CheckCircle2, Circle, Loader2 } from "lucide-react"
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context"

interface ConnectedPlatform {
  id: string
  name: string
  isConnected: boolean
  connect: () => Promise<void>
  disconnect: () => Promise<void>
  isLoading: boolean
}

const ConnectedPlatforms = () => {
  const { toast } = useToast()
  const { platforms, getConnectedPlatforms, isPlatformConnected } = useConnectedPlatforms()
  const connectedPlatforms = getConnectedPlatforms()

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {connectedPlatforms.map((platform) => (
        <Card key={platform.id}>
          <CardH<PERSON>er>
            <CardTitle>{platform.name}</CardTitle>
            <CardDescription>{platform.isConnected ? "Connected" : "Not connected"}</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-2">
            <div className="flex items-center space-x-2">
              {platform.isConnected ? (
                <>
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <p className="text-sm text-muted-foreground">Connected to {platform.name}</p>
                </>
              ) : (
                <>
                  <Circle className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-muted-foreground">Not connected to {platform.name}</p>
                </>
              )}
            </div>
          </CardContent>
          <CardFooter className="justify-end">
            {platform.isConnected ? (
              <Button
                variant="destructive"
                onClick={async () => {
                  try {
                    await platform.disconnect()
                    toast({
                      title: "Disconnected",
                      description: `Disconnected from ${platform.name}`,
                    })
                  } catch (error: any) {
                    toast({
                      title: "Error",
                      description: error?.message || `Failed to disconnect from ${platform.name}`,
                      variant: "destructive",
                    })
                  }
                }}
                disabled={platform.isLoading}
              >
                {platform.isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Disconnecting...
                  </>
                ) : (
                  "Disconnect"
                )}
              </Button>
            ) : (
              <Button
                onClick={async () => {
                  try {
                    await platform.connect()
                    toast({
                      title: "Connected",
                      description: `Connected to ${platform.name}`,
                    })
                  } catch (error: any) {
                    toast({
                      title: "Error",
                      description: error?.message || `Failed to connect to ${platform.name}`,
                      variant: "destructive",
                    })
                  }
                }}
                disabled={platform.isLoading}
              >
                {platform.isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  "Connect"
                )}
              </Button>
            )}
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

export default ConnectedPlatforms
