import Link from "next/link"
import { TagBadge } from "./tag-badge"

interface RelatedQuestion {
  id: string
  title: string
  votes: number
  answers: number
  views: number
  tags: string[]
}

interface RelatedQuestionsProps {
  questions: RelatedQuestion[]
}

export function RelatedQuestions({ questions }: RelatedQuestionsProps) {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-700">Related Questions</h3>
      </div>
      <div className="bg-white">
        <ul className="divide-y divide-gray-100">
          {questions.map((question) => (
            <li key={question.id} className="p-4 hover:bg-gray-50">
              <Link href={`/questions/${question.id}`} className="block">
                <h4 className="text-sm font-medium text-blue-600 hover:text-blue-800 mb-1">{question.title}</h4>
                <div className="flex flex-wrap gap-1 mb-2">
                  {question.tags.map((tag) => (
                    <TagBadge key={tag} tag={tag} />
                  ))}
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <span className="font-medium text-gray-700">{question.votes} votes</span>
                  <span className="mx-1.5">•</span>
                  <span className={`${question.answers > 0 ? "text-green-600 font-medium" : ""}`}>
                    {question.answers} answers
                  </span>
                  <span className="mx-1.5">•</span>
                  <span>{question.views} views</span>
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}
