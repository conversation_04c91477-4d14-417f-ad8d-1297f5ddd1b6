"use client";

import React from "react";
import { useAppVersion } from "@/hooks/use-app-version";

export default function VersionBadge({
  className = "",
}: {
  className?: string;
}) {
  const { data, loading } = useAppVersion();

  if (loading) {
    return (
      <span
        className={`ml-2 inline-flex items-center rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600 ${className}`}
      >
        …
      </span>
    );
  }

  if (!data) return null;

  const shortCommit = data.commit ? data.commit.substring(0, 7) : "";

  return (
    <span
      title={`version: ${data.version}\nenv: ${data.env}\ncommit: ${data.commit}\nbuild: ${data.build_time}`}
      className={`inline-flex items-center rounded-md bg-indigo-50 px-2 py-0.5 text-xs font-semibold text-indigo-700 ring-1 ring-inset ring-indigo-200 ${className}`}
    >
      v{data.version}
      {shortCommit ? ` · ${shortCommit}` : ""}
    </span>
  );
}
