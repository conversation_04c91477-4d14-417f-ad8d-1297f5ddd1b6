"use client";

import React, { useEffect, useState } from "react";
import { getBackendUrl } from "@/lib/api-config";
import {
  MessageSquare,
  Archive,
  X,
  ExternalLink,
  Bell,
  CheckCircle,
  XCircle,
  Edit,
  Check,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Notification,
  QAApprovalData,
  NewKnowledgeBaseData,
} from "@/types/notifications";
import { useNotifications } from "@/hooks/use-notifications";
import { useAuth } from "@/contexts/auth-context";
import { useQAApprovals } from "@/hooks/use-qa-approvals";
import { testQAApiConnection } from "@/services/qa-approval-api";
import QAEditDialog from "@/components/qa-edit-dialog";
import { PendingQAApproval, QAEditData } from "@/services/qa-approval-api";
import { useGenericNotifications } from "@/hooks/use-generic-notifications";

interface NotificationPopoverProps {
  children: React.ReactNode;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onViewHistory: () => void;
  onRefreshSidebar?: () => void;
  onCountChange?: (count: number) => void;
  onNewNotification?: () => void;
}

// --- Repo Change Approvals ---
interface RepoChange {
  id: string;
  internal_repo: string;
  external_repo: string;
  commit_id: string;
  previous_commit_id?: string;
  commit_url?: string;
  diff?: any;
  summary: string;
  details: any;
  code_diffs?: any;
  llm_suggested_docs?: any;
  status: string;
  created_at: string;
  updated_at: string;
}

const BACKEND_BASE_URL = getBackendUrl();

// Polling frequency constants (in milliseconds)
const FREQUENCY_POLLING_PENDING_REPOS = 300000; // 5 minutes - for pending repo changes

export default function NotificationPopover({
  children,
  isOpen,
  onOpenChange,
  onViewHistory,
  onRefreshSidebar,
  onCountChange,
  onNewNotification,
}: NotificationPopoverProps) {
  const { user } = useAuth();
  const userEmail = user?.email || "<EMAIL>";
  const [activeTab, setActiveTab] = useState<"qa" | "repo" | "system">("qa");

  // State for bulk action confirmation dialogs
  const [showApproveAllDialog, setShowApproveAllDialog] = useState(false);
  const [showRejectAllDialog, setShowRejectAllDialog] = useState(false);
  const [showRepoApproveAllDialog, setShowRepoApproveAllDialog] = useState(false);
  const [showRepoRejectAllDialog, setShowRepoRejectAllDialog] = useState(false);
  const [showSystemAcknowledgeAllDialog, setShowSystemAcknowledgeAllDialog] = useState(false);
  const [showSystemIgnoreAllDialog, setShowSystemIgnoreAllDialog] = useState(false);
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false);

  // State for edit dialog
  const [editingQA, setEditingQA] = useState<PendingQAApproval | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // WebSocket notifications state
  const [wsNotifications, setWsNotifications] = useState<Notification[]>([]);
  const [wsConnectionStatus, setWsConnectionStatus] =
    useState<string>("disconnected");

  // --- Repo Change Approvals ---
  const [repoChanges, setRepoChanges] = useState<RepoChange[]>([]);
  const [repoChangesLoading, setRepoChangesLoading] = useState(false);
  const { connectedUserCompany } = useAuth();
  const companyId = connectedUserCompany?.company_id;

  // Use the WebSocket hook
  const {
    notifications: wsNotificationsData,
    isConnected,
    connectionError,
  } = useNotifications(userEmail);

  // Use the Q&A approvals hook
  const {
    pendingApprovals,
    isLoading: qaLoading,
    error: qaError,
    approveQA,
    rejectQA,
    editQA,
    fetchPendingApprovals,
  } = useQAApprovals();

  // Use the generic notifications hook
  const {
    notifications: genericNotifications,
    isLoading: genericLoading,
    error: genericError,
    acceptNotification,
    ignoreNotification,
    fetchNotifications,
  } = useGenericNotifications();

  // Debug: Log all WebSocket notifications
  useEffect(() => {
    console.log("🔌 All WebSocket notifications:", wsNotificationsData);
    console.log("🔌 WebSocket connection status:", {
      isConnected,
      connectionError,
    });
  }, [wsNotificationsData, isConnected, connectionError]);

  // Filter for NEW_KNOWLEDGE_BASE notifications from WebSocket
  const newKnowledgeNotifications = wsNotificationsData.filter(
    (notification) => {
      console.log("🔍 Checking notification:", {
        type: notification.type,
        dataType: notification.data?.type,
        title: notification.title,
        message: notification.message,
      });
      return (
        notification.type === "NEW_KNOWLEDGE_BASE" ||
        notification.type === "new_knowledge_base" ||
        notification.type === "qa_approval" ||
        notification.type === "project_update" ||
        notification.type === "team_notification" ||
        ((notification.type === "notification" ||
          notification.type === "info") &&
          (notification.data?.type === "NEW_KNOWLEDGE_BASE" ||
            notification.data?.type === "new_knowledge_base" ||
            notification.title?.includes("knowledge") ||
            notification.message?.includes("knowledge") ||
            notification.message?.includes("Q&A")))
      );
    }
  );

  // Update WebSocket notifications when new ones arrive
  useEffect(() => {
    if (newKnowledgeNotifications.length > 0) {
      console.log(
        "🔌 Received NEW_KNOWLEDGE_BASE notification:",
        newKnowledgeNotifications[0]
      );

      // Convert WebSocket notification to our Notification type
      const convertedNotifications = newKnowledgeNotifications.map(
        (wsNotif) => ({
          id: wsNotif.id || Date.now().toString(),
          type: "NEW_KNOWLEDGE_BASE" as const,
          title: wsNotif.title,
          message: wsNotif.message,
          data: {
            knowledgeId:
              wsNotif.data?.knowledgeId || wsNotif.id || Date.now().toString(),
            title: wsNotif.data?.title || wsNotif.title,
            content: wsNotif.data?.content || wsNotif.message,
            author: wsNotif.data?.author || "Unknown",
            category: wsNotif.data?.category || "General",
            tags: wsNotif.data?.tags || [],
            source: wsNotif.data?.source || "WebSocket",
            timestamp: wsNotif.timestamp,
          },
          createdAt: wsNotif.timestamp,
          priority: wsNotif.level,
          userId: userEmail,
        })
      );

      console.log("🔄 Converting notifications:", convertedNotifications);
      setWsNotifications(convertedNotifications);
    }
  }, [newKnowledgeNotifications.length, userEmail]); // Only depend on length, not the array itself

  // Update connection status
  useEffect(() => {
    setWsConnectionStatus(isConnected ? "connected" : "disconnected");
  }, [isConnected]);

  // Poll for pending repo changes
  useEffect(() => {
    if (!companyId) return;
    let interval: any;
    const fetchRepoChanges = async () => {
      setRepoChangesLoading(true);
      console.log("[RepoChangePolling] Fetching pending repo changes...");
      try {
        const res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/?status=pending&company_id=${companyId}`
        );
        if (res.ok) {
          const data = await res.json();
          console.log(
            "[RepoChangePolling] Pending repo changes received:",
            data
          );
          setRepoChanges(data);
        } else {
          console.log("[RepoChangePolling] Error: Non-OK response", res.status);
          setRepoChanges([]);
        }
      } catch (err) {
        console.log("[RepoChangePolling] Error:", err);
        setRepoChanges([]);
      } finally {
        setRepoChangesLoading(false);
      }
    };
    fetchRepoChanges();
    interval = setInterval(fetchRepoChanges, FREQUENCY_POLLING_PENDING_REPOS);
    return () => clearInterval(interval);
  }, [companyId]);
  // Approve/reject repo change
  const [confirmDialog, setConfirmDialog] = useState<{
    id: string;
    action: "approve" | "reject" | null;
  } | null>(null);
  const handleRepoChangeAction = (id: string, action: "approve" | "reject") => {
    setConfirmDialog({ id, action });
  };
  const confirmRepoChangeAction = async () => {
    if (!confirmDialog) return;
    await fetch(
      `${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/${confirmDialog.id}/approve/`,
      {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: confirmDialog.action,
          approved_by: user?.email,
        }),
      }
    );
    setRepoChanges((prev) => prev.filter((c) => c.id !== confirmDialog.id));
    setConfirmDialog(null);
  };

  // Bulk action functions
  const handleApproveAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Approving all Q&A approvals...");
      const qaApprovalIds = pendingApprovals.map((qa) => qa.id);

      // Approve each Q&A one by one
      for (const qaId of qaApprovalIds) {
        await approveQA(qaId);
      }

      console.log("✅ All Q&A approvals completed successfully");
      setShowApproveAllDialog(false);

      // Refresh the sidebar to show updated Q&A list
      if (onRefreshSidebar) {
        onRefreshSidebar();
      }
    } catch (error) {
      console.error("❌ Error approving all Q&As:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleRejectAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Rejecting all Q&A approvals...");
      const qaApprovalIds = pendingApprovals.map((qa) => qa.id);

      // Reject each Q&A one by one
      for (const qaId of qaApprovalIds) {
        await rejectQA(qaId, "Bulk rejected by admin");
      }

      console.log("✅ All Q&A rejections completed successfully");
      setShowRejectAllDialog(false);

      // Refresh the sidebar to show updated Q&A list
      if (onRefreshSidebar) {
        onRefreshSidebar();
      }
    } catch (error) {
      console.error("❌ Error rejecting all Q&As:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleRepoApproveAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Approving all repo changes...");
      const repoChangeIds = repoChanges.map((change) => change.id);

      // Approve each repo change one by one
      for (const changeId of repoChangeIds) {
        await fetch(
          `${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/${changeId}/approve/`,
          {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              action: "approve",
              approved_by: user?.email,
            }),
          }
        );
      }

      console.log("✅ All repo changes approved successfully");
      setShowRepoApproveAllDialog(false);
      setRepoChanges([]); // Clear the list since all are approved
    } catch (error) {
      console.error("❌ Error approving all repo changes:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleRepoRejectAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Rejecting all repo changes...");
      const repoChangeIds = repoChanges.map((change) => change.id);

      // Reject each repo change one by one
      for (const changeId of repoChangeIds) {
        await fetch(
          `${BACKEND_BASE_URL}/api/integrations/github/api/repo-changes/${changeId}/approve/`,
          {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              action: "reject",
              approved_by: user?.email,
            }),
          }
        );
      }

      console.log("✅ All repo changes rejected successfully");
      setShowRepoRejectAllDialog(false);
      setRepoChanges([]); // Clear the list since all are rejected
    } catch (error) {
      console.error("❌ Error rejecting all repo changes:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleSystemAcknowledgeAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Acknowledging all system notifications...");
      const systemNotificationIds = genericNotifications.map((notification) => notification.id);

      // Acknowledge each system notification one by one
      for (const notificationId of systemNotificationIds) {
        await acceptNotification(notificationId);
      }

      console.log("✅ All system notifications acknowledged successfully");
      setShowSystemAcknowledgeAllDialog(false);
      // Refresh notifications to update the list
      fetchNotifications();
    } catch (error) {
      console.error("❌ Error acknowledging all system notifications:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleSystemIgnoreAll = async () => {
    setIsBulkActionLoading(true);
    try {
      console.log("🚀 Ignoring all system notifications...");
      const systemNotificationIds = genericNotifications.map((notification) => notification.id);

      // Ignore each system notification one by one
      for (const notificationId of systemNotificationIds) {
        await ignoreNotification(notificationId);
      }

      console.log("✅ All system notifications ignored successfully");
      setShowSystemIgnoreAllDialog(false);
      // Refresh notifications to update the list
      fetchNotifications();
    } catch (error) {
      console.error("❌ Error ignoring all system notifications:", error);
    } finally {
      setIsBulkActionLoading(false);
    }
  };

  const handleEditQA = async (
    qaId: string,
    projectId: string,
    editData: QAEditData
  ) => {
    try {
      await editQA(qaId, projectId, editData);
      setShowEditDialog(false);
      setEditingQA(null);
    } catch (error) {
      console.error("Error editing QA:", error);
    }
  };

  const openEditDialog = (qa: PendingQAApproval) => {
    setEditingQA(qa);
    setShowEditDialog(true);
  };

  // Refresh pending approvals when popover opens
  useEffect(() => {
    if (isOpen) {
      console.log(
        "🔄 Notification popover opened - fetching pending approvals and notifications..."
      );

      // Test the API connection first
      testQAApiConnection();

      // Then fetch pending approvals and notifications
      fetchPendingApprovals();
      fetchNotifications();
    }
  }, [isOpen, fetchPendingApprovals, fetchNotifications]);
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "slack":
        return "💬";
      case "teams":
        return "👥";
      case "github":
        return "🐙";
      case "discord":
        return "🎮";
      default:
        return "📝";
    }
  };

  // Convert pending Q&A approvals to notification format
  const qaNotifications = pendingApprovals.map((qa) => ({
    id: qa.id,
    type: "qa_approval" as const,
    title: "Q&A Pending Approval",
    message: `Question: ${
      qa.question?.title || qa.question?.content || "Untitled"
    }...`,
    data: {
      question: qa.question?.content || "",
      answer: qa.answer?.content || "",
      questionAuthor: qa.question?.author?.name || "Unknown",
      answerAuthor: qa.answer?.author?.name || "Unknown",
      questionTime: qa.question?.timestamp || "",
      answerTime: qa.answer?.timestamp || "",
      platform: "knowledge-base",
      source: qa.knowledgeSpaceName || "Knowledge Space",
      channel: "Q&A",
      tags: qa.question?.tags || [],
      verified: qa.answer?.isVerified || false,
    },
    createdAt: qa.created_at,
    priority: "medium" as const,
    userId: userEmail,
  }));

  const wsNewKnowledgeNotifications = wsNotifications.filter(
    (n) => n.type === "NEW_KNOWLEDGE_BASE"
  );

  // Convert repo changes to notification format
  const repoChangeNotifications = repoChanges.map((change) => ({
    id: change.id,
    type: "repo_change" as const,
    title: "Repo Change Pending Approval",
    message: change.summary,
    data: {
      internal_repo: change.internal_repo,
      external_repo: change.external_repo,
      commit_id: change.commit_id,
      summary: change.summary,
      details: change.details,
    },
    createdAt: change.created_at,
    priority: "high" as const,
    userId: userEmail,
  }));

  // Convert system/generic notifications to unified Notification format
  const genericAsNotifications = genericNotifications.map((g: any) => ({
    id: g.id,
    type: "generic_notification" as const,
    title: g.title,
    message: g.details || g.message || "",
    data: {
      type: g.type,
      severity: g.severity,
      title: g.title,
      message: g.details || g.message || "",
      description: g.description,
    },
    createdAt: g.created_at,
    priority: g.severity,
    userId: userEmail,
  }));

  const allNotifications = [
    ...qaNotifications,
    ...wsNewKnowledgeNotifications,
    ...repoChangeNotifications,
    ...genericAsNotifications,
  ];

  // Debug: Log final notification counts
  useEffect(() => {
    console.log("📊 Notification counts:", {
      qaNotifications: qaNotifications.length,
      pendingApprovals: pendingApprovals.length,
      repoChangeNotifications: repoChangeNotifications.length,
      wsNewKnowledgeNotifications: wsNewKnowledgeNotifications.length,
      allNotifications: allNotifications.length,
      wsNotifications: wsNotifications.length,
    });
    // Emit count to parent if requested
    if (onCountChange) {
      onCountChange(allNotifications.length);
    }
  }, [
    qaNotifications.length,
    pendingApprovals.length,
    repoChangeNotifications.length,
    wsNewKnowledgeNotifications.length,
    allNotifications.length,
    wsNotifications.length,
    onCountChange,
  ]);

  // Detect newly received notifications (simple heuristic: count increase)
  const [prevCount, setPrevCount] = useState<number>(0);
  const [lastSoundTime, setLastSoundTime] = useState<number>(0);
  const SOUND_COOLDOWN = 2000; // 2 seconds cooldown between sounds

  useEffect(() => {
    if (allNotifications.length > prevCount) {
      if (onNewNotification) {
        onNewNotification();
      }
      
      // Play sound notification with batching
      const now = Date.now();
      if (now - lastSoundTime > SOUND_COOLDOWN) {
        playNotificationSound();
        setLastSoundTime(now);
      }
    }
    setPrevCount(allNotifications.length);
  }, [allNotifications.length, prevCount, onNewNotification, lastSoundTime]);

  // Function to play notification sound
  const playNotificationSound = () => {
    try {
      // Create audio context for better browser compatibility
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      // Connect nodes
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // Configure sound
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 800Hz tone
      oscillator.type = 'sine';
      
      // Fade in/out for pleasant sound
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);
      
      // Play the sound
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
      
      console.log("🔊 Notification sound played");
    } catch (error) {
      console.warn("Could not play notification sound:", error);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent className="w-[480px] p-0" align="end">
        <div className="p-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-bold text-gray-900 text-lg">
                📥 Notifications
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Review and approve knowledge contributions from your team
              </p>
            </div>
            {/* Hidden refresh and connection status - kept for future WebSocket functionality */}
            {/* <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  console.log("🔄 Manual refresh clicked");
                  fetchPendingApprovals();
                }}
                disabled={qaLoading}
                className="text-xs"
              >
                {qaLoading ? "Refreshing..." : "Refresh"}
              </Button>
              <div
                className={`w-2 h-2 rounded-full ${
                  wsConnectionStatus === "connected"
                    ? "bg-green-500"
                    : "bg-red-500"
                }`}
              ></div>
              <span className="text-xs text-gray-500">
                {wsConnectionStatus === "connected" ? "Live" : "Offline"}
              </span>
            </div> */}
            {/* Tabs */}
            <div className="flex items-center space-x-2">
              <button
                className={`relative px-3 py-1 rounded-full text-xs font-semibold border ${
                  activeTab === "qa"
                    ? "bg-white text-gray-900 border-gray-300"
                    : "bg-white/60 text-gray-600 border-gray-200 hover:bg-white"
                }`}
                onClick={() => setActiveTab("qa")}
              >
                QA approvals
                {qaNotifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500" />
                )}
              </button>
              <button
                className={`relative px-3 py-1 rounded-full text-xs font-semibold border ${
                  activeTab === "repo"
                    ? "bg-yellow-100 text-yellow-900 border-yellow-200"
                    : "bg-yellow-50 text-yellow-800 border-yellow-200 hover:bg-yellow-100"
                }`}
                onClick={() => setActiveTab("repo")}
              >
                Repo changes
                {repoChanges.length > 0 && (
                  <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500" />
                )}
              </button>
              <button
                className={`relative px-3 py-1 rounded-full text-xs font-semibold border ${
                  activeTab === "system"
                    ? "bg-blue-100 text-blue-900 border-blue-200"
                    : "bg-blue-50 text-blue-800 border-blue-200 hover:bg-blue-100"
                }`}
                onClick={() => setActiveTab("system")}
              >
                System notifs
                {genericNotifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500" />
                )}
              </button>
            </div>
          </div>
          {/* Bulk action buttons - only show if there are more than 5 pending approvals */}
          {activeTab === "qa" && qaNotifications.length > 5 && (
            <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-gray-200">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowApproveAllDialog(true)}
                disabled={isBulkActionLoading || qaLoading}
                className="text-xs bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Approve All ({qaNotifications.length})
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowRejectAllDialog(true)}
                disabled={isBulkActionLoading || qaLoading}
                className="text-xs bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
              >
                <XCircle className="h-3 w-3 mr-1" />
                Reject All ({qaNotifications.length})
              </Button>
            </div>
          )}
          {activeTab === "repo" && repoChanges.length > 3 && (
            <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-gray-200">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowRepoApproveAllDialog(true)}
                disabled={isBulkActionLoading || repoChangesLoading}
                className="text-xs bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Approve All ({repoChanges.length})
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowRepoRejectAllDialog(true)}
                disabled={isBulkActionLoading || repoChangesLoading}
                className="text-xs bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
              >
                <XCircle className="h-3 w-3 mr-1" />
                Reject All ({repoChanges.length})
              </Button>
            </div>
          )}
          {activeTab === "system" && genericNotifications.length > 3 && (
            <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-gray-200">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowSystemAcknowledgeAllDialog(true)}
                disabled={isBulkActionLoading || genericLoading}
                className="text-xs bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100"
              >
                <Check className="h-3 w-3 mr-1" />
                Acknowledge All ({genericNotifications.length})
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowSystemIgnoreAllDialog(true)}
                disabled={isBulkActionLoading || genericLoading}
                className="text-xs bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100"
              >
                <X className="h-3 w-3 mr-1" />
                Ignore All ({genericNotifications.length})
              </Button>
            </div>
          )}
        </div>
        <div className="max-h-96 overflow-y-auto">
          {/* Debug info */}
          <div className="p-2 text-xs text-gray-500 bg-yellow-50 border border-yellow-200 rounded m-2">
            {allNotifications.length} notifications:{" "}
            {qaNotifications.length} Q&A notifications,{" "}
            {repoChangeNotifications.length} Repo Changes notifications,{" "}
            {genericNotifications.length} system notifications available
          </div>
          {/* Per-tab rendering */}
          {activeTab === "system" && (
            <div className="p-5 border-b border-gray-100 bg-blue-50">
              <h4 className="font-bold text-blue-800 text-base mb-2">System Notifications</h4>
              {genericLoading && (
                <div className="text-xs text-gray-500">Loading notifications...</div>
              )}
              {genericNotifications.length === 0 && !genericLoading && (
                <div className="text-xs text-gray-500">No system notifications</div>
              )}
              {genericNotifications.map((notification) => (
                <div
                  key={`generic-${notification.id}`}
                  className="p-4 border border-blue-200 rounded-lg bg-white mb-3"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Bell className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-semibold text-blue-800">
                        {notification.type.replace("_", " ")}
                      </span>
                      <span className="text-xs text-blue-600">• {notification.severity}</span>
                    </div>
                    <span className="text-xs text-gray-400">{getTimeAgo(new Date(notification.created_at))}</span>
                  </div>

                  <h5 className="text-sm font-semibold text-gray-900 mb-1">{notification.title}</h5>
                  <p className="text-sm text-gray-700 mb-3">{notification.details}</p>

                  <div className="flex flex-wrap gap-2">
                    <Button
                      size="sm"
                      onClick={async () => {
                        await acceptNotification(notification.id);
                      }}
                      disabled={genericLoading}
                      className="flex-1 min-w-0 bg-emerald-600 hover:bg-emerald-700 text-white font-medium shadow-sm"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      {genericLoading ? "Acknowledging..." : "Acknowledge"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={async () => {
                        await ignoreNotification(notification.id);
                      }}
                      disabled={genericLoading}
                      className="flex-1 min-w-0 bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 font-medium"
                    >
                      <X className="h-4 w-4 mr-1" />
                      {genericLoading ? "Ignoring..." : "Ignore"}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === "repo" && (
            <div className="p-5 border-b border-gray-100 bg-yellow-50">
              <h4 className="font-bold text-yellow-800 text-base mb-2">Repo Change Approvals</h4>
              {repoChangesLoading && (
                <div className="text-xs text-gray-500">Loading repo changes...</div>
              )}
              {repoChanges.length === 0 && !repoChangesLoading && (
                <div className="text-xs text-gray-500">No repo changes</div>
              )}
              {repoChanges.map((change) => (
                <div
                  key={change.id}
                  className="mb-4 p-3 bg-white rounded shadow border border-yellow-200"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-700 font-semibold">
                      {change.internal_repo} &larr; {change.external_repo}
                    </span>
                    <span className="text-xs text-gray-400">{getTimeAgo(new Date(change.created_at))}</span>
                  </div>
                  <div className="text-sm font-medium text-yellow-900 mb-1">{change.summary}</div>
                  <div className="text-xs text-gray-600 mb-2">Commit: {change.commit_id}</div>
                  <Button
                    size="sm"
                    variant="link"
                    className="text-blue-600 px-0"
                    onClick={() => (window.location.href = `/repo-change/${change.id}`)}
                  >
                    View Details
                  </Button>
                  <div className="flex space-x-2 mt-2">
                    <Button
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => handleRepoChangeAction(change.id, "approve")}
                    >
                      Approve
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                      onClick={() => handleRepoChangeAction(change.id, "reject")}
                    >
                      Reject
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === "qa" && (
            <>
              {qaLoading ? (
                <div className="p-6 text-center text-gray-500">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-3"></div>
                  <p className="text-sm font-medium">Loading Q&A approvals...</p>
                </div>
              ) : qaNotifications.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <MessageSquare className="h-10 w-10 mx-auto mb-3 text-gray-300" />
                  <p className="text-sm font-medium">No Q&A approvals</p>
                </div>
              ) : (
                allNotifications
                  .filter((n) => n.type !== "generic_notification" && n.type !== "repo_change")
                  .map((notification) => {
                    const qaCreatedAt = new Date(notification.createdAt);
                    const qaTimeAgo = getTimeAgo(qaCreatedAt);
                const createdAt = new Date(notification.createdAt);
                const timeAgo = getTimeAgo(createdAt);

                // Handle different notification types
                if (notification.type === "qa_approval") {
                  const qaData = notification.data as QAApprovalData;

                  return (
                    <div
                      key={`qa-${notification.id}-${Date.now()}`}
                      className="p-5 border-b border-gray-100 hover:bg-gray-50 transition-colors"
                    >
                      {/* Header with Platform and Time */}
                      <div>
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-2 bg-white rounded-full px-3 py-1 shadow-sm border">
                              <span className="text-base">
                                {getPlatformIcon(qaData.platform)}
                              </span>
                              <span className="text-sm font-semibold text-gray-900">
                                {qaData.source}
                              </span>
                              <span className="text-xs text-gray-500">
                                • {qaData.channel}
                              </span>
                            </div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-800 leading-relaxed">
                          {qaData.answer}
                        </p>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-wrap gap-2">
                        <Button
                          size="sm"
                          onClick={async () => {
                            console.log(
                              "🔵 Approve button clicked for notification:",
                              notification.id
                            );
                            await approveQA(notification.id);

                            // Refresh the sidebar to show updated Q&A list
                            if (onRefreshSidebar) {
                              onRefreshSidebar();
                            }
                          }}
                          disabled={qaLoading}
                          className="flex-1 min-w-0 bg-emerald-600 hover:bg-emerald-700 text-white font-medium shadow-sm"
                        >
                          <Archive className="h-4 w-4 mr-2" />
                          {qaLoading ? "Approving..." : "Approve"}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            console.log(
                              "✏️ Edit button clicked for notification:",
                              notification.id
                            );
                            // Find the QA in pending approvals
                            const qa = pendingApprovals.find(
                              (qa) => qa.id === notification.id
                            );
                            if (qa) {
                              openEditDialog(qa);
                            }
                          }}
                          disabled={qaLoading}
                          className="flex-1 min-w-0 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 font-medium"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            console.log(
                              "🔴 Reject button clicked for notification:",
                              notification.id
                            );
                            await rejectQA(
                              notification.id,
                              "Rejected by admin"
                            );

                            // Refresh the sidebar to show updated Q&A list
                            if (onRefreshSidebar) {
                              onRefreshSidebar();
                            }
                          }}
                          disabled={qaLoading}
                          className="flex-1 min-w-0 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 font-medium"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  );
                } else if (notification.type === "generic_notification") {
                  // Already rendered in the dedicated System Notifications section above
                  // Skip here to avoid duplicate rendering
                  return null;
                } else if (notification.type === "repo_change") {
                  // Repo changes are already rendered in the separate section above
                  // Skip rendering them here to avoid duplication
                  return null;
                } else {
                  // Handle other notification types (NEW_KNOWLEDGE_BASE, etc.)
                  return (
                    <div
                      key={`${notification.type}-${
                        notification.id
                      }-${Date.now()}`}
                      className="p-5 border-b border-gray-100 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-semibold text-gray-900">
                            {notification.title}
                          </span>
                        </div>
                        <span className="text-xs text-gray-400">{qaTimeAgo}</span>
                      </div>
                      <p className="text-sm text-gray-800 leading-relaxed">
                        {notification.message}
                      </p>
                    </div>
                  );
                }
                  })
              )}
            </>
          )}
        </div>
        {allNotifications.length > 0 && (
          <div className="p-3 border-t border-gray-200 text-center">
            <Button
              variant="ghost"
              size="sm"
              className="text-primary"
              onClick={onViewHistory}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View All Q&A in details
            </Button>
          </div>
        )}
      </PopoverContent>

      {/* Approve All Confirmation Dialog */}
      <AlertDialog
        open={showApproveAllDialog}
        onOpenChange={setShowApproveAllDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve All Q&A Approvals</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to approve all {qaNotifications.length}{" "}
              pending Q&A approvals? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkActionLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApproveAll}
              disabled={isBulkActionLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isBulkActionLoading ? "Approving..." : "Approve All"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reject All Confirmation Dialog */}
      <AlertDialog
        open={showRejectAllDialog}
        onOpenChange={setShowRejectAllDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject All Q&A Approvals</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reject all {qaNotifications.length}{" "}
              pending Q&A approvals? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkActionLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRejectAll}
              disabled={isBulkActionLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isBulkActionLoading ? "Rejecting..." : "Reject All"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Repo Approve All Confirmation Dialog */}
      <AlertDialog
        open={showRepoApproveAllDialog}
        onOpenChange={setShowRepoApproveAllDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve All Repo Changes</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to approve all {repoChanges.length}{" "}
              pending repo changes? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkActionLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRepoApproveAll}
              disabled={isBulkActionLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isBulkActionLoading ? "Approving..." : "Approve All"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Repo Reject All Confirmation Dialog */}
      <AlertDialog
        open={showRepoRejectAllDialog}
        onOpenChange={setShowRepoRejectAllDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject All Repo Changes</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reject all {repoChanges.length}{" "}
              pending repo changes? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkActionLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRepoRejectAll}
              disabled={isBulkActionLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isBulkActionLoading ? "Rejecting..." : "Reject All"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* System Acknowledge All Confirmation Dialog */}
      <AlertDialog
        open={showSystemAcknowledgeAllDialog}
        onOpenChange={setShowSystemAcknowledgeAllDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Acknowledge All System Notifications</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to acknowledge all {genericNotifications.length}{" "}
              system notifications? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkActionLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSystemAcknowledgeAll}
              disabled={isBulkActionLoading}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              {isBulkActionLoading ? "Acknowledging..." : "Acknowledge All"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* System Ignore All Confirmation Dialog */}
      <AlertDialog
        open={showSystemIgnoreAllDialog}
        onOpenChange={setShowSystemIgnoreAllDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Ignore All System Notifications</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to ignore all {genericNotifications.length}{" "}
              system notifications? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkActionLoading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSystemIgnoreAll}
              disabled={isBulkActionLoading}
              className="bg-gray-600 hover:bg-gray-700"
            >
              {isBulkActionLoading ? "Ignoring..." : "Ignore All"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* QA Edit Dialog */}
      <QAEditDialog
        qa={editingQA}
        isOpen={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={handleEditQA}
        isLoading={qaLoading}
      />
      {/* Confirmation Dialog for Approve/Reject */}
      <AlertDialog
        open={!!confirmDialog}
        onOpenChange={(open) => !open && setConfirmDialog(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmDialog?.action === "approve"
                ? "Approve Repo Change"
                : "Reject Repo Change"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {confirmDialog?.action} this repo change?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRepoChangeAction}
              className={
                confirmDialog?.action === "approve"
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {confirmDialog?.action === "approve" ? "Approve" : "Reject"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Popover>
  );
}

function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays}d ago`;
  }

  return date.toLocaleDateString();
}
