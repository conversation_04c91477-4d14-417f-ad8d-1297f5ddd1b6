"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { getImageByFileName } from "@/lib/image-storage"

interface BetaBadgeProps {
  className?: string
  width?: number
  height?: number
  fallbackUrl?: string
}

export default function BetaBadge({
  className = "",
  width = 60,
  height = 20,
  fallbackUrl = "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/beta-au7izTqQE0bWQNLseCPR43dCXYGM4s.png",
}: BetaBadgeProps) {
  const [imageUrl, setImageUrl] = useState<string>(fallbackUrl)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  useEffect(() => {
    async function loadBetaImage() {
      try {
        setIsLoading(true)
        // Try to get the image from Supabase storage
        const url = await getImageByFileName("beta.png")

        if (url) {
          setImageUrl(url)
        } else {
          // If not found in storage or database not available, use the fallback URL
          setImageUrl(fallbackUrl)
        }
      } catch (err) {
        // On any error, just use the fallback URL
        console.log("Using fallback URL for beta badge")
        setImageUrl(fallbackUrl)
      } finally {
        setIsLoading(false)
      }
    }

    // Only try to load from database if we have a different fallback URL
    // This prevents unnecessary database calls during development
    if (fallbackUrl) {
      loadBetaImage()
    }
  }, [fallbackUrl])

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {isLoading ? (
        <div className="animate-pulse bg-gray-200 rounded-full" style={{ width, height }}></div>
      ) : (
        <Image
          src={imageUrl || "/placeholder.svg"}
          alt="Beta Version"
          width={width}
          height={height}
          className="object-contain"
          priority
        />
      )}
    </div>
  )
}
