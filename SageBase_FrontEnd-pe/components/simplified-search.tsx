"use client";

import type React from "react";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import {
  Search,
  X,
  ArrowUp,
  Send,
  ChevronDown,
  ChevronUp,
  Filter,
  FileText,
  ExternalLink,
  User,
  Bo<PERSON>,
  Paperclip,
  Mic,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Image from "next/image";
import HtmlResponse from "./html-response";
import { Avatar } from "@/components/ui/avatar";
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context";
import { useAuth } from "@/contexts/auth-context";
import { HISTORY_CONTEXT_LENGTH } from "@/lib/search-service";
import QuestionsSection from "@/components/questions-section";
import SageBaseLogo from "@/components/sagebase-logo";
import SageBaseSLogo from "@/components/sagebase-s-logo";

// Backend URL configuration
const BACKEND_BASE_URL =
  process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

// Simple markdown to HTML converter
const markdownToHtml = (markdown: string): string => {
  if (!markdown) return "";

  return (
    markdown
      // Headers
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>'
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>'
      )
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>'
      )

      // Bold and italic
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")

      // Code blocks
      .replace(
        /```(\w+)?\n([\s\S]*?)```/g,
        '<pre class="bg-gray-100 p-3 rounded-lg overflow-x-auto my-3"><code class="text-sm">$2</code></pre>'
      )
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>'
      )

      // Links
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">$1</a>'
      )

      // Lists
      .replace(/^\* (.*$)/gim, '<li class="ml-4">$1</li>')
      .replace(/^- (.*$)/gim, '<li class="ml-4">$1</li>')
      .replace(/^(\d+)\. (.*$)/gim, '<li class="ml-4">$2</li>')

      // Wrap lists in ul/ol
      .replace(/(<li.*<\/li>)/g, '<ul class="list-disc my-2">$1</ul>')

      // Paragraphs
      .replace(/\n\n/g, '</p><p class="my-2">')
      .replace(/^(.+)$/gm, '<p class="my-2">$1</p>')

      // Clean up empty paragraphs
      .replace(/<p class="my-2"><\/p>/g, "")
      .replace(/<p class="my-2">\s*<\/p>/g, "")

      // Clean up multiple newlines
      .replace(/\n\s*\n/g, "\n")

      // Final cleanup
      .trim()
  );
};

// Add the chat message styles
const chatMessageStyles = `
.chat-message-content a {
  color: #4A18BE;
  text-decoration: underline;
  transition: color 0.2s;
}

.chat-message-content a:hover {
  color: #3B1398;
}

.chat-message-content ul, .chat-message-content ol {
  margin-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.chat-message-content ul {
  list-style-type: disc;
}

.chat-message-content ol {
  list-style-type: decimal;
}

.chat-message-content p {
  margin-bottom: 0.5rem;
}

.chat-message-content p:last-child {
  margin-bottom: 0;
}

.chat-message-content pre {
  background-color: #f1f1f1;
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.chat-message-content code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.1rem 0.2rem;
  border-radius: 0.2rem;
}

.chat-message-content div {
  margin-bottom: 0.5rem;
}

.chat-message-content div:last-child {
  margin-bottom: 0;
}

.chat-message-content .mt-4.pt-4.border-t.border-gray-200 {
  margin-top: 1rem !important;
  padding-top: 1rem !important;
  border-top: 1px solid #e5e7eb !important;
}

.chat-message-content h4.font-semibold.mb-2 {
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  color: #374151 !important;
}

.chat-message-content ul.list-disc.list-inside {
  list-style-type: disc !important;
  margin-left: 1.5rem !important;
}

.chat-message-content li.mb-1 {
  margin-bottom: 0.25rem !important;
}

.chat-message-content a.text-blue-600.underline {
  color: #2563eb !important;
  text-decoration: underline !important;
}

.chat-message-content blockquote {
  border-left: 4px solid #4907E2;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #64748b;
}

.chat-message-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.chat-message-content th,
.chat-message-content td {
  border: 1px solid #e2e8f0;
  padding: 0.75rem;
  text-align: left;
}

.chat-message-content th {
  background-color: #f8fafc;
  font-weight: 600;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #94a3b8;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.message-bubble {
  position: relative;
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
}

.user-message {
  background: linear-gradient(135deg, #4907E2 0%, #3B1398 100%);
  color: white;
  border-radius: 1.25rem 1.25rem 0.25rem 1.25rem;
  box-shadow: 0 4px 12px rgba(73, 7, 226, 0.15);
}

.assistant-message {
  background: white;
  color: #1e293b;
  border: 1px solid #e2e8f0;
  border-radius: 1.25rem 1.25rem 1.25rem 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.welcome-message {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border-radius: 1.25rem;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}
`;

const magicalBorderStyles = `
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}
`;

// Declare the generateAiResponse function
const generateAiResponse = (userQuestion: string) => {
  return `<p>Fallback response for: ${userQuestion}</p><p>This is a fallback response when the AI service is unavailable.</p>`;
};

// Enhanced mock search results with query-specific results
const generateMockResults = (query = "") => {
  // Default results
  const defaultResults = [
    {
      id: 1,
      title: "API Authentication Documentation",
      snippet:
        "Implements OAuth 2.0 for secure API authentication. All requests must include a valid token...",
      source: "confluence",
      date: "2 days ago",
      author: "Michael Chen",
      url: "/documents/1",
      techStack: ["Node.js", "JWT", "OAuth"],
      type: "Documentation",
    },
    {
      id: 2,
      title: "Auth Service Refactoring",
      snippet:
        "We need to refactor the authentication service to support the new OAuth flow as discussed. The current implementation has performance issues with token validation.",
      source: "slack",
      date: "1 week ago",
      author: "Sarah Johnson",
      url: "/documents/2",
      techStack: ["Microservices", "OAuth"],
      type: "Discussion",
    },
    {
      id: 3,
      title: "Fix OAuth token refresh logic",
      snippet:
        "Current implementation doesn't handle token expiration correctly. We need to implement proper refresh logic.",
      source: "confluence",
      date: "3 days ago",
      author: "Alex Rodriguez",
      url: "/documents/3",
      techStack: ["TypeScript", "OAuth"],
      type: "Documentation",
    },
  ];

  // If query contains babel or module, return only the internal Q/A file
  if (
    query.toLowerCase().includes("babel") ||
    query.toLowerCase().includes("module") ||
    query.toLowerCase().includes("error: i can not find module babel")
  ) {
    return [
      {
        id: 1,
        title: "Babel preset-env missing - solution found",
        snippet:
          "Fixed the 'Cannot find module @babel/preset-env' error by running npm install --save-dev @babel/preset-env && npm cache clean --force",
        source: "other",
        date: "Today",
        author: "Wissem Khlifi",
        url: "/editor/nova/qa",
        path: "Nova/Tips/Q/A",
        techStack: ["Babel", "JavaScript", "Node.js"],
        type: "Q/A Document",
      },
    ];
  }

  return defaultResults;
};

// Helper to build history pairs for backend context
function buildHistoryPairs(
  messages: { text: string; isUser: boolean }[],
  maxPairs: number
) {
  console.log("buildHistoryPairs input:", { messages, maxPairs });
  const pairs = [];
  let i = 0;
  while (i < messages.length) {
    if (messages[i].isUser && messages[i + 1] && !messages[i + 1].isUser) {
      pairs.push({
        user: messages[i].text,
        answer: messages[i + 1].text,
      });
      i += 2;
    } else {
      i++;
    }
  }
  console.log("buildHistoryPairs output:", pairs);
  const result = pairs.slice(-maxPairs);
  console.log("buildHistoryPairs final result:", result);
  return result;
}

export default function SimplifiedSearch() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [chatMessages, setChatMessages] = useState<
    { text: string; isUser: boolean }[]
  >([]);
  const [chatInput, setChatInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const historyRef = useRef<HTMLDivElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  const chatInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [hasSearched, setHasSearched] = useState(false);
  const modalSearchInputRef = useRef<HTMLInputElement>(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const { platforms: connectedPlatforms, isLoading: platformsLoading } =
    useConnectedPlatforms();
  const { user } = useAuth();

  // Track active filters - now dynamic based on connected platforms
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  // Add new state variables for tag and author filters
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedAuthors, setSelectedAuthors] = useState<string[]>([]);
  const [showTagFilter, setShowTagFilter] = useState(false);
  const [showAuthorFilter, setShowAuthorFilter] = useState(false);

  // Mock search results
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [aiSummary, setAiSummary] = useState("");

  // Add a new state for tracking progress:
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Add state for references
  const [references, setReferences] = useState<string[]>([]);

  // Helper to get user ID for context (replace with actual user ID logic if available)
  const getUserId = () => {
    return user?.id || "guest_user";
  };

  // Helper to get user email for context
  const getUserEmail = () => {
    return user?.email || "<EMAIL>";
  };

  // Initialize active filters based on connected platforms
  // Exclude Slack/Discord and ensure 'internet' is active by default
  useEffect(() => {
    const connectedPlatformIds = connectedPlatforms
      .filter(
        (platform) =>
          platform.connected &&
          platform.id !== "slack" &&
          platform.id !== "discord"
      )
      .map((platform) => platform.id);

    setActiveFilters(
      Array.from(new Set([...connectedPlatformIds, "internet"]))
    );
  }, [connectedPlatforms]);

  // Add effect to apply chat message styles
  useEffect(() => {
    // Add the styles to the document
    const styleElement = document.createElement("style");
    styleElement.innerHTML = chatMessageStyles + magicalBorderStyles;
    document.head.appendChild(styleElement);

    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Toggle filter selection
  const toggleFilter = (filterId: string) => {
    if (activeFilters.includes(filterId)) {
      setActiveFilters(activeFilters.filter((id) => id !== filterId));
    } else {
      setActiveFilters([...activeFilters, filterId]);
    }
  };

  // Toggle advanced filters
  const toggleAdvancedFilters = () => {
    setShowAdvancedFilters(!showAdvancedFilters);
  };

  // Handle opening a file
  const handleOpenFile = (e: React.MouseEvent, path: string) => {
    e.preventDefault();
    router.push(path);
  };

  // Mock search history - in a real app, this would come from localStorage or a database
  const [searchHistory] = useState([
    "Authentication service architecture",
    "How to implement OAuth in Atlas API?",
    "Token refresh mechanism documentation",
    "API rate limiting best practices",
    "WebSocket connection debugging in Pulse",
    "Nova frontend error handling",
    "CI/CD pipeline setup for Pulse",
  ]);

  // No hardcoded suggested questions
  const defaultSuggestions: string[] = [];

  // Mock question suggestions based on input
  const getSuggestions = (_input: string): string[] => {
    return [];
  };

  // Get filtered suggestions based on current input
  const filteredSuggestions = getSuggestions(searchQuery);

  // Modified handleSearch function with GitHub LLM integration
  const handleSearch = async () => {
    setIsSearching(true);
    setLoadingProgress(0);
    setShowResults(false);
    setHasSearched(false);
    setAiSummary("");
    setSearchResults([]);

    // Start progress bar
    let progress = 0;
    setLoadingProgress(progress);
    const progressInterval = setInterval(() => {
      progress += 10;
      setLoadingProgress(progress);
      if (progress >= 90) clearInterval(progressInterval);
    }, 100);

    try {
      // Build context based on selected filters
      const historyPairs = buildHistoryPairs(
        chatMessages,
        HISTORY_CONTEXT_LENGTH
      );
      // Normalize platform slugs to minimal letters (e.g., google-drive -> google_drive)
      const normalizedFilters = activeFilters.map((id) =>
        id.replace(/-/g, "_").toLowerCase()
      );

      const context: any = {
        user_id: getUserId(),
        user_email: getUserEmail(),
        filters: normalizedFilters,
        metadata: {
          total_filters: activeFilters.length,
          has_platform_filters: activeFilters.length > 0,
          has_tag_filters: false,
          has_author_filters: false,
        },
        history: historyPairs,
      };
      // Add other filters here as needed
      // e.g. if (activeFilters.includes("slack")) { ... }

      // Log the search request for debugging
      console.log("Search request with filters:", {
        question: searchQuery,
        context,
        activeFilters,
        selectedTags,
        selectedAuthors,
      });

      // Always call the orchestrator endpoint with extended timeout for long-running AI operations
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout for AI operations

      try {
        const response = await fetch(`${BACKEND_BASE_URL}/api/agent/ask/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            question: searchQuery,
            context,
            user_email: getUserEmail(),
          }),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Backend error (${response.status}):`, errorText);

          if (response.status === 504) {
            throw new Error(
              "Gateway timeout. The AI is taking longer than expected to process your query. Please try again with a simpler question."
            );
          }

          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { error: errorText };
          }

          throw new Error(
            errorData.error ||
              `Backend error: ${response.status} ${response.statusText}`
          );
        }

        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const responseText = await response.text();
          console.error("Non-JSON response received:", responseText);
          throw new Error(
            "Invalid response format from server. Please try again."
          );
        }

        const data = await response.json();
        console.log("references: ", data.references);

        setLoadingProgress(100);
        clearInterval(progressInterval);

        setSearchResults([
          {
            id: 1,
            title: "AI Analysis",
            snippet: "AI-powered analysis of your query",
            source: data.references.join(", ") || "general",
            date: "Just now",
            author: "AI Assistant",
            url: "#",
            techStack: ["AI"],
            type: "AI Analysis",
          },
        ]);

        setAiSummary(
          markdownToHtml(data.answer) || "No response from orchestrator"
        );
        setReferences(Array.isArray(data.references) ? data.references : []);

        // Add the search question and AI response to chat history
        let responseText = markdownToHtml(
          data.answer || "No response from orchestrator"
        );

        // Add references if available
        console.log("Search response data:", data);
        console.log("References from backend:", data.references);

        if (
          data.references &&
          Array.isArray(data.references) &&
          data.references.length > 0
        ) {
          console.log("Adding references to search message:", data.references);
          responseText +=
            '<div class="mt-4 pt-4 border-t border-gray-200" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;"><h4 class="font-semibold mb-2" style="font-weight: 600; margin-bottom: 0.5rem; color: #374151;">References:</h4><ul class="list-disc list-inside" style="list-style-type: disc; margin-left: 1.5rem;">';
          data.references.forEach((ref: string) => {
            const urlMatch = ref.match(/https?:\/\/[^\s)]+/);
            if (urlMatch) {
              responseText += `<li class="mb-1" style="margin-bottom: 0.25rem;"><a href="${urlMatch[0]}" target="_blank" rel="noopener noreferrer" class="text-blue-600 underline" style="color: #2563eb; text-decoration: underline;">${ref}</a></li>`;
            } else {
              responseText += `<li class="mb-1" style="margin-bottom: 0.25rem;">${ref}</li>`;
            }
          });
          responseText += "</ul></div>";
        } else {
          // No references from backend, don't show References section
          console.log(
            "No references from backend, skipping References section"
          );
        }

        setChatMessages([
          { text: searchQuery, isUser: true },
          { text: responseText, isUser: false },
        ]);

        setTimeout(() => {
          setIsSearching(false);
          setShowResults(true);
          setHasSearched(true);
        }, 500);
      } catch (error) {
        clearTimeout(timeoutId);

        if (error instanceof Error && error.name === "AbortError") {
          console.error("Request timed out after 2 minutes");
          throw new Error(
            "Request timed out. The AI is taking longer than expected to process your query. Please try again with a simpler question."
          );
        }

        throw error;
      }
    } catch (error) {
      console.error("Error in search:", error);
      setLoadingProgress(100);
      clearInterval(progressInterval);
      setTimeout(() => {
        const errorMessage =
          error instanceof Error ? error.message : "Search failed";
        setSearchResults(generateMockResults(searchQuery));
        setAiSummary(`<div class="text-red-600">
          <p><strong>Error:</strong> ${errorMessage}</p>
          <p>Showing fallback results instead.</p>
        </div>`);

        // Add the search question and error response to chat history
        setChatMessages([
          { text: searchQuery, isUser: true },
          {
            text: markdownToHtml(
              `Error: ${errorMessage}. Showing fallback results instead.`
            ),
            isUser: false,
          },
        ]);

        setIsSearching(false);
        setShowResults(true);
        setHasSearched(true);
      }, 500);
    }
  };

  // Updated handleChatSubmit with unified orchestrator logic
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!chatInput.trim()) return;

    const userQuestion = chatInput;
    setChatInput("");

    setChatMessages((prev) => [...prev, { text: userQuestion, isUser: true }]);

    setIsTyping(true);

    try {
      // Build context based on selected filters - use current chatMessages (without new message)
      console.log("chatMessages before building history:", chatMessages);
      const historyPairs = buildHistoryPairs(
        chatMessages,
        HISTORY_CONTEXT_LENGTH
      );
      const normalizedFilters = activeFilters.map((id) =>
        id.replace(/-/g, "_").toLowerCase()
      );

      const context: any = {
        user_id: getUserId(),
        user_email: getUserEmail(),
        filters: normalizedFilters,
        metadata: {
          total_filters: activeFilters.length,
          has_platform_filters: activeFilters.length > 0,
          has_tag_filters: false,
          has_author_filters: false,
        },
        history: historyPairs,
      };

      // Log the chat request for debugging
      console.log("Chat request with filters:", {
        question: userQuestion,
        context,
        activeFilters,
        selectedTags,
        selectedAuthors,
      });

      // Always call the orchestrator endpoint with extended timeout for long-running AI operations
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout for AI operations

      try {
        const response = await fetch(`${BACKEND_BASE_URL}/api/agent/ask/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            question: userQuestion,
            context,
            user_email: getUserEmail(),
          }),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Backend error (${response.status}):`, errorText);

          if (response.status === 504) {
            throw new Error(
              "Gateway timeout. The AI is taking longer than expected to process your message. Please try again with a simpler question."
            );
          }

          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { error: errorText };
          }

          throw new Error(
            errorData.error ||
              `Backend error: ${response.status} ${response.statusText}`
          );
        }

        // Check if response is JSON
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const responseText = await response.text();
          console.error("Non-JSON response received:", responseText);
          throw new Error(
            "Invalid response format from server. Please try again."
          );
        }

        const data = await response.json();

        // Add both user question and assistant response to chat history after getting response
        let responseText = markdownToHtml(data.answer);

        // Add references if available
        console.log("Chat response data:", data);
        console.log("References from backend:", data.references);

        if (
          data.references &&
          Array.isArray(data.references) &&
          data.references.length > 0
        ) {
          console.log("Adding references to chat message:", data.references);
          responseText +=
            '<div class="mt-4 pt-4 border-t border-gray-200" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;"><h4 class="font-semibold mb-2" style="font-weight: 600; margin-bottom: 0.5rem; color: #374151;">References:</h4><ul class="list-disc list-inside" style="list-style-type: disc; margin-left: 1.5rem;">';
          data.references.forEach((ref: string) => {
            const urlMatch = ref.match(/https?:\/\/[^\s)]+/);
            if (urlMatch) {
              responseText += `<li class="mb-1" style="margin-bottom: 0.25rem;"><a href="${urlMatch[0]}" target="_blank" rel="noopener noreferrer" class="text-blue-600 underline" style="color: #2563eb; text-decoration: underline;">${ref}</a></li>`;
            } else {
              responseText += `<li class="mb-1" style="margin-bottom: 0.25rem;">${ref}</li>`;
            }
          });
          responseText += "</ul></div>";
        } else {
          // No references from backend, don't show References section
          console.log(
            "No references from backend, skipping References section"
          );
        }

        setChatMessages((prev) => [
          ...prev,
          {
            text: responseText,
            isUser: false,
          },
        ]);
      } catch (error) {
        clearTimeout(timeoutId);

        if (error instanceof Error && error.name === "AbortError") {
          console.error("Chat request timed out after 5 minutes");
          setChatMessages((prev) => [
            ...prev,
            {
              text: markdownToHtml(
                "Request timed out. The AI is taking longer than expected to process your message. Please try again with a simpler question."
              ),
              isUser: false,
            },
          ]);
        } else {
          // Add both user question and fallback response to chat history on error
          setChatMessages((prev) => [
            ...prev,
            {
              text: markdownToHtml(
                `An error occurred while processing your request: ${error}`
              ),
              isUser: false,
            },
          ]);
        }
      } finally {
        setIsTyping(false);
      }
    } catch (error) {
      console.error("Error in handleChatSubmit:", error);
    }
  };

  const handleHistoryItemClick = (query: string) => {
    setSearchQuery(query);
    setShowHistory(false);
    setShowSuggestions(false);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
    // Trigger search immediately
    setTimeout(() => handleSearch(), 100);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
    // Trigger search immediately
    setTimeout(() => handleSearch(), 100);
  };

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages, isTyping]);

  // Close history dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click target is not part of the search input or suggestions
      const target = event.target as Node;
      const isOutsideSearchInput =
        searchInputRef.current && !searchInputRef.current.contains(target);
      const isOutsideSuggestions =
        suggestionsRef.current && !suggestionsRef.current.contains(target);

      // If clicking outside both elements, hide the suggestions
      if (isOutsideSearchInput && isOutsideSuggestions) {
        setShowSuggestions(false);
        setShowHistory(false);
      }
    };

    // Add the event listener to the document
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Show suggestions regardless of input, as long as we have suggestions to show
    if (getSuggestions(value).length > 0) {
      setShowSuggestions(true);
      setShowHistory(false);
    } else {
      setShowSuggestions(false);
      setShowHistory(false);
    }
  };

  // Reset search
  const resetSearch = () => {
    setSearchQuery("");
    setShowResults(false);
    setChatMessages([]);
    setSearchResults([]);
    setAiSummary("");
    setHasSearched(false);
    setIsFullscreen(false);
    setChatInput("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Dynamic platform definitions based on connected platforms
  const getPlatformConfig = (platformId: string) => {
    const platformConfigs: Record<string, any> = {
      confluence: {
        name: "Confluence",
        logo: "/images/platform-logos/confluence.svg",
        activeColor: "bg-primary-600 text-white",
        hoverColor: "hover:bg-primary-100",
        iconColor: "text-primary-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      jira: {
        name: "Jira",
        logo: "/images/platform-logos/jira.svg",
        activeColor: "bg-blue-600 text-white",
        hoverColor: "hover:bg-blue-100",
        iconColor: "text-blue-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      slack: {
        name: "Slack",
        logo: "/images/platform-logos/slack.svg",
        activeColor: "bg-purple-600 text-white",
        hoverColor: "hover:bg-purple-100",
        iconColor: "text-purple-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      discord: {
        name: "Discord",
        logo: "/images/platform-logos/discord.svg",
        activeColor: "bg-indigo-600 text-white",
        hoverColor: "hover:bg-indigo-100",
        iconColor: "text-indigo-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      github: {
        name: "GitHub",
        logo: "/images/platform-logos/github.svg",
        activeColor: "bg-gray-800 text-white",
        hoverColor: "hover:bg-gray-200",
        iconColor: "text-gray-700",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      "google-drive": {
        name: "Google Drive",
        logo: "/images/platform-logos/google-drive.svg",
        activeColor: "bg-green-600 text-white",
        hoverColor: "hover:bg-green-100",
        iconColor: "text-green-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      notion: {
        name: "Notion",
        logo: "/images/platform-logos/notion.svg",
        activeColor: "bg-black text-white",
        hoverColor: "hover:bg-gray-200",
        iconColor: "text-gray-700",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      other: {
        name: "Other",
        logo: "/placeholder.svg",
        activeColor: "bg-gray-600 text-white",
        hoverColor: "hover:bg-gray-100",
        iconColor: "text-gray-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
      internet: {
        name: "Internet",
        logo: "/images/platform-logos/internet.svg",
        activeColor: "bg-blue-600 text-white",
        hoverColor: "hover:bg-blue-100",
        iconColor: "text-blue-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      },
    };

    return (
      platformConfigs[platformId] || {
        name: platformId.charAt(0).toUpperCase() + platformId.slice(1),
        logo: "/placeholder.svg",
        activeColor: "bg-gray-600 text-white",
        hoverColor: "hover:bg-gray-100",
        iconColor: "text-gray-500",
        inactiveColor: "bg-gray-100 text-gray-600",
      }
    );
  };

  // Get platforms from connected platforms context
  // Exclude Slack and Discord from filter options
  const platforms = [
    ...connectedPlatforms
      .filter(
        (platform) =>
          platform.connected &&
          platform.id !== "slack" &&
          platform.id !== "discord"
      )
      .map((platform) => ({
        id: platform.id,
        ...getPlatformConfig(platform.id),
      })),
    {
      id: "internet",
      ...getPlatformConfig("internet"),
    },
  ];

  // Mock tags and authors data
  const availableTags = [
    { id: "oauth", name: "OAuth", count: 12 },
    { id: "jwt", name: "JWT", count: 8 },
    { id: "api", name: "API", count: 15 },
    { id: "authentication", name: "Authentication", count: 10 },
    { id: "security", name: "Security", count: 7 },
    { id: "nodejs", name: "Node.js", count: 9 },
    { id: "typescript", name: "TypeScript", count: 6 },
    { id: "microservices", name: "Microservices", count: 4 },
    { id: "backend", name: "Backend", count: 11 },
    { id: "frontend", name: "Frontend", count: 5 },
  ];

  const availableAuthors = [
    {
      id: "michael-chen",
      name: "Michael Chen",
      avatar: "/current-user-profile.png",
      count: 8,
    },
    {
      id: "sarah-johnson",
      name: "Sarah Johnson",
      avatar: "/diverse-avatars.png",
      count: 6,
    },
    {
      id: "alex-rodriguez",
      name: "Alex Rodriguez",
      avatar: "/diverse-group-avatars.png",
      count: 4,
    },
    {
      id: "jamie-smith",
      name: "Jamie Smith",
      avatar: "/current-user-profile.png",
      count: 3,
    },
    {
      id: "wissem-khlifi",
      name: "Wissem Khlifi",
      avatar: "/diverse-avatars.png",
      count: 5,
    },
  ];

  const toggleTag = (tagId: string) => {
    setSelectedTags((prev) =>
      prev.includes(tagId)
        ? prev.filter((id) => id !== tagId)
        : [...prev, tagId]
    );
  };

  const toggleAuthor = (authorId: string) => {
    setSelectedAuthors((prev) =>
      prev.includes(authorId)
        ? prev.filter((id) => id !== authorId)
        : [...prev, authorId]
    );
  };

  const clearAllFilters = () => {
    setSelectedTags([]);
    setSelectedAuthors([]);
    const connectedPlatformIds = connectedPlatforms
      .filter(
        (platform) =>
          platform.connected &&
          platform.id !== "slack" &&
          platform.id !== "discord"
      )
      .map((platform) => platform.id);
    setActiveFilters(
      Array.from(new Set([...connectedPlatformIds, "internet"]))
    );
  };

  // Generate AI summary based on search results
  const generateAiSummary = (results: any[], query: string) => {
    // Check if the query is about babel or modules
    if (
      query.toLowerCase().includes("babel") ||
      query.toLowerCase().includes("module") ||
      query.toLowerCase().includes("error")
    ) {
      return `<div>
        <p>The error "Cannot find module '@babel/preset-env'" typically occurs when the Babel preset-env package is not installed or not properly configured in your development environment.</p>
        
        <p>According to a discussion between Wissem and John in the Wifi_team channel, this issue can be resolved by running the following commands:</p>
        
        <pre><code>npm install --save-dev @babel/preset-env
npm cache clean --force</code></pre>
        
        <p>This solution worked for several team members who encountered the same error. The issue is often caused by:</p>
        
        <ul>
          <li>Missing dependencies in package.json</li>
          <li>Corrupted npm cache</li>
          <li>Incorrect Babel configuration in .babelrc or babel.config.js</li>
        </ul>
        
        <p>Wissem has documented this solution in the troubleshooting guide, which is now available in Confluence.</p>
      </div>`;
    }

    // Check if the query is a question
    const isQuestion =
      query.trim().endsWith("?") ||
      query.toLowerCase().startsWith("how") ||
      query.toLowerCase().startsWith("what") ||
      query.toLowerCase().startsWith("why") ||
      query.toLowerCase().startsWith("when") ||
      query.toLowerCase().startsWith("where") ||
      query.toLowerCase().startsWith("which") ||
      query.toLowerCase().startsWith("who") ||
      query.toLowerCase().startsWith("can");

    if (
      query.toLowerCase().includes("authentication") ||
      query.toLowerCase().includes("auth")
    ) {
      return `<div>
        <p>The system uses OAuth 2.0 with JWT tokens for authentication. Tokens expire after 1 hour and must be included in the Authorization header of all API requests. There's an ongoing project to implement a refresh mechanism to handle token expiration more gracefully.</p>
      </div>`;
    } else if (
      query.toLowerCase().includes("token") ||
      query.toLowerCase().includes("jwt")
    ) {
      return `<div>
        <p>JWT tokens are generated using the jsonwebtoken library with a 1-hour expiration. The tokens must be included in the Authorization header of all API requests.</p>
        <p>The current implementation has some performance issues with token validation that the team is working to address:</p>
        <ul>
          <li>High CPU usage during validation</li>
          <li>Occasional timeouts with high traffic</li>
          <li>No proper refresh mechanism</li>
        </ul>
        <p>You can find more details in the <a href="#">Authentication Service Documentation</a>.</p>
      </div>`;
    } else if (
      query.toLowerCase().includes("timeline") ||
      query.toLowerCase().includes("deadline")
    ) {
      return `<div>
        <p>According to the latest information, the OAuth implementation needs to be completed by the end of Q2 to align with the security roadmap. The refresh token mechanism is being prioritized based on the architecture review discussions.</p>
      </div>`;
    } else if (
      query.toLowerCase().includes("api") ||
      query.toLowerCase().includes("endpoint")
    ) {
      return `<div>
        <p>The API authentication uses OAuth 2.0 for secure access. All endpoints require a valid JWT token in the Authorization header. The documentation includes examples for token generation and usage across different services.</p>
      </div>`;
    } else if (isQuestion) {
      // Provide a more direct answer for questions
      return `<div>
        <p>Based on the available information, ${query.replace(
          /\?$/,
          ""
        )} involves the authentication system that uses OAuth 2.0 with JWT tokens. The implementation details are documented across several platforms, with specific code examples available in our documentation.</p>
      </div>`;
    } else {
      // For non-questions or unrecognized queries, provide a more helpful response
      return `<div>
        <p>"${query}" appears to be related to our technical documentation. The most relevant information comes from our authentication system documentation, which describes OAuth 2.0 implementation and JWT token usage. Would you like more specific details about any particular aspect?</p>
      </div>`;
    }
  };

  // Get source icon based on platform
  const getSourceIcon = (source: string) => {
    const platform = platforms.find((p) => p.id === source);
    if (platform) {
      return (
        <div className="relative h-4 w-4">
          <Image
            src={platform.logo || "/placeholder.svg"}
            alt={platform.name}
            width={16}
            height={16}
            className="object-contain"
          />
        </div>
      );
    }

    // If source is other, use a file icon
    if (source === "other") {
      return <FileText className="h-4 w-4 text-gray-600" />;
    }

    // For unknown sources, try to get config
    const config = getPlatformConfig(source);
    return (
      <div className="relative h-4 w-4">
        <Image
          src={config.logo || "/placeholder.svg"}
          alt={config.name}
          width={16}
          height={16}
          className="object-contain"
        />
      </div>
    );
  };

  const getSourceLabel = (source: string) => {
    const platform = platforms.find((p) => p.id === source);
    if (platform) {
      return platform.name;
    }

    // For unknown sources, try to get config
    const config = getPlatformConfig(source);
    return config.name;
  };

  // Show suggestions when the search input is focused
  useEffect(() => {
    if (isFocused && !showResults && !isSearching && !hasSearched) {
      setShowSuggestions(true);
    }
  }, [isFocused, showResults, isSearching, hasSearched]);

  return (
    <div
      className={`w-full ${
        hasSearched
          ? "h-full flex flex-col"
          : "max-w-4xl mx-auto flex flex-col justify-center min-h-full space-y-8 pb-[120px]"
      }`}
    >
      {!hasSearched && (
        <>
          {/* Hero Section */}
          <div className="text-center -mt-4 mb-8">
            <h1 className="text-[40px] font-bold text-gray-800 mb-2">
              Welcome to SageBase
            </h1>
            <p className="text-gray-600 text-lg">
              Your company already knows the answer. SageBase helps you find it.
            </p>
          </div>
        </>
      )}

      <div
        className={`${
          hasSearched
            ? "flex-1 flex flex-col h-full"
            : "bg-white rounded-xl border border-gray-200 shadow-sm max-h-[80vh] overflow-auto p-4"
        }`}
      >
        {/* Search Input - Animated based on search state */}
        <div
          className={`${
            hasSearched
              ? "fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50 transition-all duration-500 ease-in-out"
              : "p-4 border-b border-gray-200"
          } flex items-center gap-4`}
        >
          <div className="relative flex-1 max-w-4xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              ref={searchInputRef}
              type="search"
              placeholder={
                hasSearched ? "Message SageBase..." : "Ask anything..."
              }
              className={`pl-10 pr-10 py-6 text-lg border-0 bg-gradient-to-r from-gray-50 to-gray-100 w-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 ${
                showResults ? "bg-gray-50" : ""
              }`}
              value={hasSearched ? chatInput : searchQuery}
              onChange={
                hasSearched
                  ? (e) => setChatInput(e.target.value)
                  : handleInputChange
              }
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  if (hasSearched) {
                    handleChatSubmit(e);
                  } else if (!showResults) {
                    handleSearch();
                  }
                }
              }}
              onFocus={() => {
                setIsFocused(true);
                if (!showResults && !isSearching && !hasSearched) {
                  setShowSuggestions(true);
                }
              }}
              readOnly={showResults && !hasSearched}
            />
            {((searchQuery && !showResults) || (chatInput && hasSearched)) && (
              <button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                onClick={() => {
                  if (hasSearched) {
                    setChatInput("");
                  } else {
                    setSearchQuery("");
                  }
                }}
              >
                <X className="h-5 w-5" />
              </button>
            )}
            {showResults && !hasSearched && (
              <button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                onClick={resetSearch}
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>

          <Button
            className={`ml-2 py-6 px-4 rounded-full transition-all duration-300 ${
              hasSearched
                ? "bg-gradient-to-r from-primary-500 to-purple-600 hover:from-primary-600 hover:to-purple-700 text-white"
                : "bg-primary hover:bg-primary-600"
            }`}
            onClick={hasSearched ? handleChatSubmit : handleSearch}
            disabled={
              isSearching ||
              isTyping ||
              (!hasSearched ? !searchQuery.trim() : !chatInput.trim())
            }
            aria-label={hasSearched ? "Send message" : "Search"}
          >
            {isSearching || isTyping ? (
              <div className="animate-pulse">
                <span className="sr-only">Processing...</span>
                <div className="h-4 w-4 bg-white rounded-full"></div>
              </div>
            ) : hasSearched ? (
              <Send className="h-5 w-5 text-white" />
            ) : (
              <ArrowUp className="h-5 w-5 text-white" />
            )}
          </Button>
        </div>

        {/* Question Suggestions Dropdown - Only show when typing and not in chat mode */}
        {showSuggestions &&
          !showResults &&
          !hasSearched &&
          filteredSuggestions.length > 0 && (
            <div
              ref={suggestionsRef}
              className="absolute z-20 mt-1 w-3/4 max-w-3xl bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-y-auto"
            >
              <div className="px-4 py-2 text-sm font-medium text-gray-500 border-b border-gray-100">
                Suggested questions
              </div>
              <ul className="py-1">
                {filteredSuggestions.map((suggestion, index) => (
                  <li
                    key={index}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <Search className="h-4 w-4 text-primary-500 mr-2" />
                    <span className="text-gray-700">{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

        {/* Simplified Filters - Only Source Filters Visible by Default */}
        {!showResults && !hasSearched && (
          <div className="mt-4">
            {/* Platform Filters - Always Visible */}
            <div className="flex items-center space-x-3 mb-2">
              <span className="text-sm font-medium text-gray-600">
                Filter by source:
              </span>
              <div className="flex flex-wrap gap-2">
                {platformsLoading ? (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full"></div>
                    <span>Loading platforms...</span>
                  </div>
                ) : platforms.length > 0 ? (
                  platforms.map((platform) => (
                    <Badge
                      key={platform.id}
                      className={`cursor-pointer transition-colors duration-200 border-0 ${
                        activeFilters.includes(platform.id)
                          ? platform.activeColor
                          : platform.inactiveColor
                      } ${platform.hoverColor}`}
                      onClick={() => toggleFilter(platform.id)}
                    >
                      <div
                        className={`relative h-4 w-4 mr-1.5 ${
                          activeFilters.includes(platform.id)
                            ? "filter brightness-0 invert"
                            : ""
                        }`}
                      >
                        <Image
                          src={platform.logo || "/placeholder.svg"}
                          alt={platform.name}
                          width={16}
                          height={16}
                          className="object-contain"
                        />
                      </div>
                      <span>{platform.name}</span>
                    </Badge>
                  ))
                ) : (
                  <div className="text-sm text-gray-500">
                    No platforms connected. Connect platforms in the sidebar to
                    enable search.
                  </div>
                )}
              </div>
            </div>

            {/* Advanced filters removed */}
          </div>
        )}

        {/* Loading State */}
        {isSearching && !hasSearched && (
          <div className="p-8">
            <div className="mb-4 text-center">
              <p className="text-gray-700 mb-2">
                Searching for "{searchQuery}"
              </p>
              <p className="text-sm text-gray-500">
                Analyzing documents across platforms...
              </p>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
              <div
                className="bg-primary-500 h-2.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>
            <div className="flex justify-center items-center space-x-2 text-xs text-gray-500">
              {loadingProgress < 30 && <p>Finding relevant documents...</p>}
              {loadingProgress >= 30 && loadingProgress < 60 && (
                <p>Analyzing content...</p>
              )}
              {loadingProgress >= 60 && loadingProgress < 90 && (
                <p>Generating response...</p>
              )}
              {loadingProgress >= 90 && <p>Almost ready...</p>}
            </div>
          </div>
        )}

        {/* Search Results - Updated to use HtmlResponse */}
        {showResults && !isSearching && !hasSearched && (
          <div
            ref={resultsRef}
            className={`overflow-y-auto ${
              isFullscreen ? "max-h-[calc(95vh-200px)]" : "max-h-[60vh]"
            }`}
          >
            {/* References */}
            <div className="mb-4 px-4">
              {/* Check if we're searching for Babel-related content */}
              {searchQuery.toLowerCase().includes("babel") ||
              searchQuery.toLowerCase().includes("module") ||
              searchQuery.includes("error") ? (
                // Show only the internal Q/A file reference
                <div className="grid grid-cols-1 gap-3">
                  <div
                    className="bg-gray-50 rounded-lg p-4 h-full hover:bg-gray-100 transition-colors border border-gray-200 cursor-pointer"
                    onClick={(e) => handleOpenFile(e, "/editor/nova/qa")}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-gray-600 mr-2" />
                        <span className="text-sm font-medium text-gray-700">
                          Internal Q/A
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full hover:bg-purple-100"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenFile(e, "/editor/nova/qa");
                        }}
                      >
                        <ExternalLink className="h-4 w-4 text-purple-600" />
                        <span className="sr-only">Open file</span>
                      </Button>
                    </div>
                    <div className="mt-2">
                      <h4 className="text-lg font-medium text-gray-800">
                        Babel preset-env missing - solution found
                      </h4>
                      <p className="text-sm text-gray-600 mt-2">
                        Fixed the 'Cannot find module @babel/preset-env' error
                        by running npm install --save-dev @babel/preset-env &&
                        npm cache clean --force
                      </p>
                      <div className="flex items-center mt-3 text-sm text-gray-500">
                        <span className="flex items-center">
                          <FileText className="h-4 w-4 mr-1" />
                          Knowledge Spaces/Nova/Tips/Q/A
                        </span>
                      </div>
                      <div className="flex justify-between items-center mt-3">
                        <span className="text-sm text-gray-500">Today</span>
                        <span className="text-sm text-gray-500">
                          Wissem Khlifi
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                // Show regular search results for non-Babel searches
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {searchResults
                    .filter((result) => activeFilters.includes(result.source))
                    .map((result) => (
                      <div
                        key={result.id}
                        className="bg-gray-50 rounded-lg p-3 h-full hover:bg-gray-100 transition-colors cursor-pointer"
                        onClick={(e) => handleOpenFile(e, result.url)}
                      >
                        <div className="flex items-center">
                          <div className="flex items-center">
                            {getSourceIcon(result.source)}
                            <span className="ml-2 text-sm font-medium text-gray-700">
                              {getSourceLabel(result.source)}
                            </span>
                          </div>
                        </div>
                        <div className="mt-2">
                          <h4 className="text-base font-medium text-gray-800">
                            {result.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {result.snippet}
                          </p>
                          <span className="text-sm text-gray-500 mt-2 block">
                            {result.date}
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Chat Interface - Integrated into main component */}
        {hasSearched && (
          <div className="flex-1 flex flex-col h-full bg-gray-50">
            {/* Enhanced Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-primary-50 to-purple-50 border-b border-gray-200 shadow-sm">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center shadow-lg p-1">
                  <SageBaseSLogo width={32} height={32} />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 text-lg">
                    SageBase Assistant
                  </h3>
                  <p className="text-sm text-gray-600">
                    AI-powered knowledge companion
                  </p>
                </div>
              </div>
              <button
                className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-colors"
                onClick={() => resetSearch()}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Enhanced Messages Container */}
            <div className="flex-1 overflow-y-auto bg-gray-50">
              {chatMessages.length === 0 && (
                <div className="flex flex-col items-center justify-center h-full p-8">
                  <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mb-6 shadow-lg p-2">
                    <SageBaseSLogo width={64} height={64} />
                  </div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                    Welcome to SageBase Assistant
                  </h3>
                  <p className="text-gray-600 text-center max-w-md leading-relaxed">
                    I'm here to help you find and understand information across
                    your knowledge base. Ask me anything about your search
                    results or company knowledge.
                  </p>
                  <div className="mt-6 flex flex-wrap gap-2 justify-center">
                    <div className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm">
                      Search documents
                    </div>
                    <div className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm">
                      Find answers
                    </div>
                    <div className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm">
                      Get insights
                    </div>
                  </div>
                </div>
              )}

              <div className="max-w-4xl mx-auto space-y-6 p-6">
                {chatMessages.map((message, index) => (
                  <div
                    key={index}
                    className={`flex ${
                      message.isUser ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div className="flex items-end gap-3 max-w-[85%]">
                      {message.isUser ? (
                        <>
                          <div className="message-bubble p-4 user-message">
                            <div className="text-sm text-white">
                              {message.text}
                            </div>
                            <div className="text-xs mt-2 text-white/70">
                              {new Date().toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </div>
                          </div>
                          <div className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                            <User className="h-4 w-4 text-white" />
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-md p-1">
                            <SageBaseSLogo width={24} height={24} />
                          </div>
                          <div className="message-bubble p-4 assistant-message">
                            <div
                              dangerouslySetInnerHTML={{
                                __html: message.text,
                              }}
                              className="text-sm text-gray-800"
                            />
                            <div className="text-xs mt-2 text-gray-500">
                              {new Date().toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex justify-start">
                    <div className="flex items-end gap-3">
                      <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-md p-1">
                        <SageBaseSLogo width={24} height={24} />
                      </div>
                      <div className="message-bubble p-4 assistant-message">
                        <div className="typing-indicator">
                          <div className="typing-dot"></div>
                          <div className="typing-dot"></div>
                          <div className="typing-dot"></div>
                        </div>
                        <div className="text-xs mt-2 text-gray-500">
                          SageBase is typing...
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef}></div>
              </div>
            </div>

            {/* Chat input is now handled by the animated input at the bottom */}
            <div className="pb-24"></div>
          </div>
        )}
      </div>

      {!hasSearched && (
        <>
          {/* Questions Section */}
          <QuestionsSection />
        </>
      )}
    </div>
  );
}
