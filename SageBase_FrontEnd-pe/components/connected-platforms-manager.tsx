"use client"
import { useState } from "react"
import { useConnectedPlatforms } from "@/contexts/connected-platforms-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
  Plus,
  Database,
  MessageSquare,
  Code,
  Mail,
  FileText,
  AlertCircle,
} from "lucide-react"
import GoogleDriveApprovalPopup from "./google-drive-approval-popup";

const platformIcons = {
  confluence: Database,
  jira: FileText,
  slack: MessageSquare,
  teams: MessageSquare,
  github: Code,
  gmail: Mail,
}

const platformColors = {
  confluence: "bg-blue-50 text-blue-700 border-blue-200",
  jira: "bg-indigo-50 text-indigo-700 border-indigo-200",
  slack: "bg-purple-50 text-purple-700 border-purple-200",
  teams: "bg-cyan-50 text-cyan-700 border-cyan-200",
  github: "bg-gray-50 text-gray-700 border-gray-200",
  gmail: "bg-red-50 text-red-700 border-red-200",
}

export default function ConnectedPlatformsManager() {
  const {
    platforms,
    isLoading,
    connectPlatform,
    disconnectPlatform,
    syncPlatform,
    updatePlatformConfig,
    refreshPlatforms,
  } = useConnectedPlatforms()

  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null)
  const [syncingPlatform, setSyncingPlatform] = useState<string | null>(null)
  const [configuring, setConfiguring] = useState<string | null>(null)
  const [configs, setConfigs] = useState<Record<string, any>>({})
  const [showGoogleDriveApproval, setShowGoogleDriveApproval] = useState(false)

  const handleConnect = async (platformId: string) => {
    // Show Google Drive approval popup if trying to connect Google Drive
    if (platformId === "google-drive") {
      setShowGoogleDriveApproval(true)
      return
    }

    setConnectingPlatform(platformId)
    try {
      const platform = platforms.find((p) => p.id === platformId)
      if (platform) {
        await connectPlatform({
          ...platform,
          config: configs[platformId] || {},
        })
      }
    } catch (error) {
      console.error("Failed to connect platform:", error)
    } finally {
      setConnectingPlatform(null)
    }
  }

  const handleDisconnect = async (platformId: string) => {
    try {
      await disconnectPlatform(platformId)
    } catch (error) {
      console.error("Failed to disconnect platform:", error)
    }
  }

  const handleSync = async (platformId: string) => {
    setSyncingPlatform(platformId)
    try {
      await syncPlatform(platformId)
    } catch (error) {
      console.error("Failed to sync platform:", error)
    } finally {
      setSyncingPlatform(null)
    }
  }

  const handleConfigSave = (platformId: string) => {
    updatePlatformConfig(platformId, configs[platformId] || {})
    setConfiguring(null)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "syncing":
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const connectedCount = platforms.filter((p) => p.isConnected).length

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading platforms...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Connected Platforms</h2>
          <p className="text-gray-600 mt-1">
            Manage your platform connections ({connectedCount} of {platforms.length} connected)
          </p>
        </div>
        <Button onClick={refreshPlatforms} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh All
        </Button>
      </div>

      {connectedCount > 0 && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Your platform connections are active and will persist across all pages in the application.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {platforms.map((platform) => {
          const Icon = platformIcons[platform.id as keyof typeof platformIcons] || Database
          const isConnecting = connectingPlatform === platform.id
          const isSyncing = syncingPlatform === platform.id
          const isConfiguring = configuring === platform.id

          return (
            <Card key={platform.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="h-5 w-5 text-gray-600" />
                    <CardTitle className="text-lg">{platform.name}</CardTitle>
                  </div>
                  {getStatusIcon(platform.status)}
                </div>
                <CardDescription className="capitalize">{platform.type.replace("-", " ")}</CardDescription>
              </CardHeader>

              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <Badge
                    variant={platform.isConnected ? "default" : "secondary"}
                    className={platform.isConnected ? platformColors[platform.id as keyof typeof platformColors] : ""}
                  >
                    {platform.isConnected ? "Connected" : "Not Connected"}
                  </Badge>

                  {platform.isConnected && platform.lastSync && (
                    <span className="text-xs text-gray-500">
                      Last sync: {new Date(platform.lastSync).toLocaleDateString()}
                    </span>
                  )}
                </div>

                {isConfiguring && (
                  <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                    <Label htmlFor={`${platform.id}-config`} className="text-sm font-medium">
                      Configuration
                    </Label>
                    <Input
                      id={`${platform.id}-config`}
                      placeholder="API Key or Configuration"
                      value={configs[platform.id] || ""}
                      onChange={(e) => setConfigs((prev) => ({ ...prev, [platform.id]: e.target.value }))}
                    />
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleConfigSave(platform.id)}>
                        Save
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setConfiguring(null)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}

                <div className="flex space-x-2">
                  {!platform.isConnected ? (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleConnect(platform.id)}
                        disabled={isConnecting}
                        className="flex-1"
                      >
                        {isConnecting ? (
                          <>
                            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                            Connecting...
                          </>
                        ) : (
                          <>
                            <Plus className="h-3 w-3 mr-1" />
                            Connect
                          </>
                        )}
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setConfiguring(platform.id)}>
                        <Settings className="h-3 w-3" />
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSync(platform.id)}
                        disabled={isSyncing}
                        className="flex-1"
                      >
                        {isSyncing ? (
                          <>
                            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                            Syncing...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Sync
                          </>
                        )}
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setConfiguring(platform.id)}>
                        <Settings className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="destructive" onClick={() => handleDisconnect(platform.id)}>
                        <XCircle className="h-3 w-3" />
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Google Drive Approval Popup */}
      <GoogleDriveApprovalPopup
        isOpen={showGoogleDriveApproval}
        onClose={() => setShowGoogleDriveApproval(false)}
        onContinue={() => {
          // Continue with the original Google Drive OAuth flow
          if (!process.env.NEXT_PUBLIC_BACKEND_API_URL) {
            console.error("NEXT_PUBLIC_BACKEND_API_URL environment variable is not set!");
            alert("Backend API URL not configured. Please check your environment variables.");
            return;
          }
          if (!companyId || !userEmail) {
            console.error("Company ID or user email is not available!");
            alert("Company ID or user email is not available. Please try again.");
            return;
          }
          const oauthUrl = `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/api/integrations/google-drive/start/?company_id=${companyId}&workspace=default`;
          console.log("Google Drive OAuth URL:", oauthUrl);
          window.open(oauthUrl, "_blank", "width=600,height=700,scrollbars=yes,resizable=yes");
        }}
      />
    </div>
  )
}
