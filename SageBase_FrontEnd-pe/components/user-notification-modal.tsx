import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { CheckCircle, Info, AlertTriangle, XCircle } from "lucide-react";

interface UserNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  details?: Array<{ label: string; value: string | number }>;
  nextStep?: string;
  additionalInfo?: string;
  buttonText?: string;
}

export default function UserNotificationModal({
  isOpen,
  onClose,
  title = "app.sagebase.tech says",
  message,
  type = 'info',
  details = [],
  nextStep,
  additionalInfo,
  buttonText = "OK"
}: UserNotificationModalProps) {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-800">
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Main Message */}
          <div className="text-gray-700">
            {message}
          </div>

          {/* Details Section */}
          {details.length > 0 && (
            <div className="space-y-2">
              {details.map((detail, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="font-medium">{detail.label}:</span>
                  <span>{detail.value}</span>
                </div>
              ))}
            </div>
          )}

          {/* Next Step */}
          {nextStep && (
            <div className="text-sm text-gray-700">
              <span className="font-medium">Next:</span> {nextStep}
            </div>
          )}

          {/* Additional Info */}
          {additionalInfo && (
            <div className="text-sm text-gray-600 italic">
              {additionalInfo}
            </div>
          )}

          {/* Action Button */}
          <div className="flex justify-end pt-4">
            <Button 
              onClick={onClose}
              className="bg-orange-500 hover:bg-orange-600 text-white border border-blue-300 px-6 py-2 rounded-lg font-medium"
            >
              {buttonText}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
