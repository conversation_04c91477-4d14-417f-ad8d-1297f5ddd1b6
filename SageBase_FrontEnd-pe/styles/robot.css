/* Styles for moderate state */
.robot-container.moderate .antenna .light {
  background-color: #ffc107;
  box-shadow: 0 0 8px 2px #ffc107;
  animation: pulse-moderate 2s infinite ease-in-out;
}

.robot-container.moderate .panel::before {
  content: "!";
  color: #ffc107;
}

.robot-container.moderate .robot {
  animation: sway 2s infinite ease-in-out;
}

@keyframes pulse-moderate {
  0%,
  100% {
    box-shadow: 0 0 6px 1px #ffc107;
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 10px 3px #ffc107;
    opacity: 0.6;
  }
}

@keyframes sway {
  0%,
  100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(2deg);
  }
}

/* Styles for bad state */
.robot-container.bad .antenna .light {
  background-color: #dc3545;
  box-shadow: 0 0 8px 2px #dc3545;
  animation: pulse-bad 1s infinite steps(2, start);
}

.robot-container.bad .panel::before {
  content: "\2717";
  color: #dc3545;
}

.robot-container.bad .robot {
  animation: shake 0.5s infinite linear;
}

@keyframes pulse-bad {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px) rotate(-1deg);
  }
  75% {
    transform: translateX(2px) rotate(1deg);
  }
}
