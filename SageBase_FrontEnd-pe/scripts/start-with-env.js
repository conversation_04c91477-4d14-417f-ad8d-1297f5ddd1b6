#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get current git branch
function getCurrentBranch() {
  try {
    return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  } catch (error) {
    console.warn('Could not determine git branch, using default environment');
    return 'main';
  }
}

// Get the command to run (dev, build, start)
const command = process.argv[2] || 'dev';
const validCommands = ['dev', 'build', 'start'];

if (!validCommands.includes(command)) {
  console.error(`Invalid command: ${command}. Valid commands: ${validCommands.join(', ')}`);
  process.exit(1);
}

// Determine environment file based on branch
const currentBranch = getCurrentBranch();
let envFile = '.env.local';

if (currentBranch === 'deployment') {
  envFile = '.env.deployment';
} else if (currentBranch === 'production') {
  envFile = '.env.production';
} else if (currentBranch === 'staging') {
  envFile = '.env.staging';
}

// Check if the environment file exists
const envPath = path.join(process.cwd(), envFile);
if (!fs.existsSync(envPath)) {
  console.warn(`Environment file ${envFile} not found for branch ${currentBranch}, using default`);
  envFile = '.env.local';
}

console.log(`🌿 Branch: ${currentBranch}`);
console.log(`📁 Environment file: ${envFile}`);
console.log(`🚀 Running: next ${command}\n`);

// Run the Next.js command with the appropriate environment file
try {
  execSync(`dotenv -e ${envFile} next ${command}`, { stdio: 'inherit' });
} catch (error) {
  process.exit(error.status || 1);
} 