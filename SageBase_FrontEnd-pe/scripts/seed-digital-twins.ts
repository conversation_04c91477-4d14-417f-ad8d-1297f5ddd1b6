import { digitalTwinsService } from "@/lib/digital-twins-service"

// Image URLs from the provided images
const AVATAR_IMAGES = [
  {
    name: "<PERSON>",
    url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Middle_Eastern_Woman_Professional_Headshot-C7dalrC4hEMi88IPIRluqWetUKxBJI.png",
    role: "Senior Backend Engineer",
    department: "Engineering",
    coverage_score: 92,
    knowledge_areas: ["Node.js", "PostgreSQL", "API Design", "Microservices"],
  },
  {
    name: "<PERSON>",
    url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Black_Man_Professional_Headshot-9DAGGZRt0csLKnG84s43MGAZnlfQCK.png",
    role: "Product Manager",
    department: "Product",
    coverage_score: 88,
    knowledge_areas: ["Product Strategy", "User Research", "Roadmap Planning"],
  },
  {
    name: "<PERSON>",
    url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Hispanic_Woman_Professional_Headshot-5vBkK7uJFFQF36xtB2kDRe2FASqceU.png",
    role: "UX Lead",
    department: "Design",
    coverage_score: 75,
    knowledge_areas: ["User Experience", "Design Systems", "Prototyping"],
  },
  {
    name: "David B.",
    url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/White_Man_Professional_Headshot-mi0SIGt1po5kuirSrxhewehFZ2pLeV.png",
    role: "DevOps Specialist",
    department: "Engineering",
    coverage_score: 95,
    knowledge_areas: ["AWS", "Kubernetes", "CI/CD", "Infrastructure"],
  },
]

export async function seedDigitalTwins() {
  console.log("Starting digital twins seeding...")

  try {
    for (const twinData of AVATAR_IMAGES) {
      // Download image and convert to File object
      const response = await fetch(twinData.url)
      const blob = await response.blob()
      const file = new File([blob], `${twinData.name.replace(" ", "_")}.png`, { type: "image/png" })

      // Create digital twin with avatar
      await digitalTwinsService.createDigitalTwin({
        name: twinData.name,
        email: `${twinData.name.toLowerCase().replace(" ", ".")}@company.com`,
        role: twinData.role,
        department: twinData.department,
        coverage_score: twinData.coverage_score,
        knowledge_areas: twinData.knowledge_areas,
        avatar_file: file,
        metadata: {
          specialties: [],
          projects: [],
          seeded: true,
        },
      })

      console.log(`✅ Created digital twin: ${twinData.name}`)
    }

    console.log("🎉 Digital twins seeding completed successfully!")
  } catch (error) {
    console.error("❌ Error seeding digital twins:", error)
    throw error
  }
}
