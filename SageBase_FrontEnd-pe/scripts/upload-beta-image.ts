import { uploadImage } from "../lib/image-storage"

async function uploadBetaImage() {
  try {
    // Fetch the image from the provided URL
    const response = await fetch(
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/beta-au7izTqQE0bWQNLseCPR43dCXYGM4s.png",
    )
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`)
    }

    // Convert the response to a blob
    const blob = await response.blob()

    // Create a File object from the blob
    const file = new File([blob], "beta.png", { type: "image/png" })

    // Upload the image with metadata
    const result = await uploadImage(file, "app-assets", "ui", {
      width: 60,
      height: 20,
      tags: ["ui", "badge", "beta"],
      description: "Beta version badge for the application UI",
    })

    if (result) {
      console.log("Beta image uploaded successfully:", result.url)
      console.log("Metadata:", result.metadata)
    } else {
      console.error("Failed to upload beta image")
    }
  } catch (error) {
    console.error("Error in upload process:", error)
  }
}

// Execute the upload function
uploadBetaImage()
