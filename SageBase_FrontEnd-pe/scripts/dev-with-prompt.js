#!/usr/bin/env node

const { spawn } = require('child_process');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 Starting SageBase Frontend Development Server');
console.log('');

// Check which .env file is being loaded
const envFiles = ['.env.local', '.env.development', '.env.production', '.env'];
let loadedEnvFile = null;

for (const envFile of envFiles) {
  if (fs.existsSync(path.join(process.cwd(), envFile))) {
    loadedEnvFile = envFile;
    break;
  }
}

if (loadedEnvFile) {
  console.log(`📁 Loading environment from: ${loadedEnvFile}`);
} else {
  console.log('⚠️  No .env file found, using default environment');
}

console.log('');

// Only prompt for ngrok URI when loading .env.local
if (loadedEnvFile === '.env.local') {
rl.question('Please enter your ngrok URI (e.g., https://abc123.ngrok-free.app): ', (ngrokUri) => {
  if (!ngrokUri.trim()) {
    console.log('❌ No ngrok URI provided. Exiting...');
    rl.close();
    process.exit(1);
  }

  // Remove trailing slash if present
  const cleanNgrokUri = ngrokUri.trim().replace(/\/$/, '');
  
  console.log(`✅ Using ngrok URI: ${cleanNgrokUri}`);
  console.log('');
  console.log('🔄 Starting development server...');
  console.log('');

  rl.close();

  // Set the environment variable and start the dev server
  // Set NGROK URL for development - the API config will automatically use it
  const env = { 
    ...process.env, 
    NEXT_PUBLIC_BACKEND_NGROK_BASE_URL: cleanNgrokUri
  };
  
  const devProcess = spawn('npx', ['next', 'dev', '-H', '0.0.0.0', '-p', '3000'], {
    stdio: 'inherit',
    env: env
  });

  devProcess.on('error', (error) => {
    console.error('❌ Failed to start development server:', error);
    process.exit(1);
  });

  devProcess.on('exit', (code) => {
    process.exit(code);
  });
});
} else {
  console.log('🔄 Starting development server (no ngrok prompt needed)...');
  console.log('');

  rl.close();

  // Start the dev server without ngrok prompt
  const devProcess = spawn('npx', ['next', 'dev', '-H', '0.0.0.0', '-p', '3000'], {
    stdio: 'inherit',
    env: process.env
  });

  devProcess.on('error', (error) => {
    console.error('❌ Failed to start development server:', error);
    process.exit(1);
  });

  devProcess.on('exit', (code) => {
    process.exit(code);
  });
}