-- Create digital_twins table
CREATE TABLE IF NOT EXISTS digital_twins (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  email VARCHAR(255),
  role VARCHAR(255) NOT NULL,
  department VARCHAR(255),
  avatar_url TEXT,
  avatar_file_path TEXT,
  coverage_score INTEGER DEFAULT 0 CHECK (coverage_score >= 0 AND coverage_score <= 100),
  last_sync_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
  knowledge_areas TEXT[],
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_digital_twins_name ON digital_twins(name);
CREATE INDEX IF NOT EXISTS idx_digital_twins_role ON digital_twins(role);
CREATE INDEX IF NOT EXISTS idx_digital_twins_status ON digital_twins(status);
CREATE INDEX IF NOT EXISTS idx_digital_twins_coverage_score ON digital_twins(coverage_score);
CREATE INDEX IF NOT EXISTS idx_digital_twins_created_at ON digital_twins(created_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_digital_twins_updated_at
BEFORE UPDATE ON digital_twins
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create RLS policies
ALTER TABLE digital_twins ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to read all digital twins
CREATE POLICY "Users can view digital twins" ON digital_twins
FOR SELECT USING (auth.role() = 'authenticated');

-- Policy for authenticated users to insert digital twins
CREATE POLICY "Users can create digital twins" ON digital_twins
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy for authenticated users to update digital twins
CREATE POLICY "Users can update digital twins" ON digital_twins
FOR UPDATE USING (auth.role() = 'authenticated');

-- Insert sample data
INSERT INTO digital_twins (name, email, role, department, coverage_score, knowledge_areas, metadata) VALUES
('Clara H.', '<EMAIL>', 'Senior Backend Engineer', 'Engineering', 92, ARRAY['Node.js', 'PostgreSQL', 'API Design', 'Microservices'], '{"specialties": ["database optimization", "API architecture"], "projects": ["user-auth-service", "payment-gateway"]}'),
('Omar S.', '<EMAIL>', 'Product Manager', 'Product', 88, ARRAY['Product Strategy', 'User Research', 'Roadmap Planning', 'Stakeholder Management'], '{"specialties": ["B2B products", "analytics"], "projects": ["dashboard-redesign", "mobile-app-v2"]}'),
('Lena K.', '<EMAIL>', 'UX Lead', 'Design', 75, ARRAY['User Experience', 'Design Systems', 'Prototyping', 'User Testing'], '{"specialties": ["accessibility", "design systems"], "projects": ["component-library", "user-onboarding"]}'),
('David B.', '<EMAIL>', 'DevOps Specialist', 'Engineering', 95, ARRAY['AWS', 'Kubernetes', 'CI/CD', 'Infrastructure', 'Monitoring'], '{"specialties": ["container orchestration", "infrastructure as code"], "projects": ["k8s-migration", "monitoring-setup"]}');
