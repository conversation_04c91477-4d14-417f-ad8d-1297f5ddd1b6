-- Create a table for storing image metadata
CREATE TABLE IF NOT EXISTS image_metadata (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  file_path TEXT NOT NULL,
  public_url TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  width INTEGER,
  height INTEGER,
  tags TEXT[],
  description TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_image_metadata_file_name ON image_metadata(file_name);
CREATE INDEX IF NOT EXISTS idx_image_metadata_tags ON image_metadata USING GIN(tags);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_image_metadata_updated_at
BEFORE UPDATE ON image_metadata
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a storage policy to restrict access to authenticated users
-- Note: This assumes you have already created the 'app-assets' bucket in Supabase storage
BEGIN;
  -- Insert default beta.png metadata if it doesn't exist
  INSERT INTO image_metadata (
    file_path,
    public_url,
    file_name,
    file_size,
    file_type,
    width,
    height,
    tags,
    description
  )
  VALUES (
    'ui/beta.png',
    'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/beta-au7izTqQE0bWQNLseCPR43dCXYGM4s.png',
    'beta.png',
    5120, -- Approximate size in bytes
    'image/png',
    60,
    20,
    ARRAY['ui', 'badge', 'beta'],
    'Beta version badge for the application UI'
  )
  ON CONFLICT (file_path) DO NOTHING;
COMMIT;
