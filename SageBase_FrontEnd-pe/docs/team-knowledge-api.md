# Team Knowledge Base API Documentation

This document outlines all the RESTful API endpoints required to make the Team Knowledge Base page fully dynamic.

## Base URL
```
/api
```

## Authentication
All endpoints require authentication. Include the authorization token in the request headers:
```
Authorization: Bearer <token>
```

## Data Models

### Project
```typescript
interface Project {
  id: string;
  name: string;
  description: string;
  lastActivity: string;
  totalContributors: number;
  categories: string[];
  docResponsible: string | null;
  secondaryResponsible: string | null;
  repoPath: string;
  docsPath: string;
  repoType: 'github' | 'gitlab';
  topContributors: Contributor[];
}
```

### Contributor
```typescript
interface Contributor {
  name: string;
  role: string;
  contributions: number;
  commits: number;
  reviews: number;
  docs: number;
}
```

### TeamMember
```typescript
interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
}
```

### ProjectMetrics
```typescript
interface ProjectMetrics {
  id: string;
  projectId: string;
  healthScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  lastUpdated: string;
  documentationCoverage: number;
  activeContributors: number;
}
```

## API Endpoints

### 1. Projects API

#### GET /api/projects
Get all projects with optional time range filtering.

**Query Parameters:**
- `timeRange` (optional): `1month` | `3months` | `6months` | `1year`

**Response:**
```json
{
  "data": [
    {
      "id": "atlas-docs",
      "name": "Atlas Documentation",
      "description": "Comprehensive documentation for the Atlas platform",
      "lastActivity": "2 days ago",
      "totalContributors": 8,
      "categories": ["Documentation", "API", "Architecture"],
      "docResponsible": "user-id-123",
      "secondaryResponsible": "user-id-456",
      "repoPath": "https://github.com/company/atlas-platform",
      "docsPath": "https://docs.company.com/atlas",
      "repoType": "github",
      "topContributors": [
        {
          "name": "Sarah Johnson",
          "role": "Tech Lead",
          "contributions": 145,
          "commits": 89,
          "reviews": 34,
          "docs": 22
        }
      ]
    }
  ],
  "success": true
}
```

#### GET /api/projects/:id
Get a specific project by ID.

**Response:**
```json
{
  "data": {
    "id": "atlas-docs",
    "name": "Atlas Documentation",
    // ... other project fields
  },
  "success": true
}
```

#### POST /api/projects
Create a new project.

**Request Body:**
```json
{
  "name": "New Project",
  "description": "Project description",
  "categories": ["API", "Documentation"],
  "repoPath": "https://github.com/company/new-project",
  "docsPath": "https://docs.company.com/new-project",
  "repoType": "github"
}
```

**Response:**
```json
{
  "data": {
    "id": "new-project",
    "name": "New Project",
    // ... other project fields
  },
  "success": true
}
```

#### PUT /api/projects/:id
Update an existing project.

**Request Body:**
```json
{
  "name": "Updated Project Name",
  "description": "Updated description",
  "categories": ["Updated", "Categories"]
}
```

**Response:**
```json
{
  "data": {
    "id": "project-id",
    "name": "Updated Project Name",
    // ... other project fields
  },
  "success": true
}
```

#### DELETE /api/projects/:id
Delete a project.

**Response:**
```json
{
  "success": true,
  "message": "Project deleted successfully"
}
```

#### POST /api/projects/:id/assign-documentation
Assign documentation responsibility to a team member.

**Request Body:**
```json
{
  "memberId": "user-id-123",
  "type": "main" | "secondary"
}
```

**Response:**
```json
{
  "data": {
    "id": "project-id",
    "docResponsible": "user-id-123",
    // ... other project fields
  },
  "success": true
}
```

### 2. Team Members API

#### GET /api/team-members
Get all team members.

**Response:**
```json
{
  "data": [
    {
      "id": "user-id-123",
      "name": "Sarah Johnson",
      "role": "Tech Lead",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg"
    }
  ],
  "success": true
}
```

#### GET /api/team-members/:id
Get a specific team member by ID.

**Response:**
```json
{
  "data": {
    "id": "user-id-123",
    "name": "Sarah Johnson",
    "role": "Tech Lead",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg"
  },
  "success": true
}
```

### 3. Contributors API

#### GET /api/projects/:id/contributors
Get top contributors for a specific project.

**Query Parameters:**
- `timeRange` (optional): `1month` | `3months` | `6months` | `1year`

**Response:**
```json
{
  "data": [
    {
      "name": "Sarah Johnson",
      "role": "Tech Lead",
      "contributions": 145,
      "commits": 89,
      "reviews": 34,
      "docs": 22
    }
  ],
  "success": true
}
```

#### GET /api/projects/:projectId/contributors/:contributorId
Get specific contributor activity for a project.

**Query Parameters:**
- `timeRange` (optional): `1month` | `3months` | `6months` | `1year`

**Response:**
```json
{
  "data": {
    "name": "Sarah Johnson",
    "role": "Tech Lead",
    "contributions": 145,
    "commits": 89,
    "reviews": 34,
    "docs": 22
  },
  "success": true
}
```

### 4. Metrics API

#### GET /api/projects/:id/metrics
Get metrics for a specific project.

**Response:**
```json
{
  "data": {
    "id": "metric-id",
    "projectId": "project-id",
    "healthScore": 85,
    "riskLevel": "Low",
    "lastUpdated": "2023-12-01T10:00:00Z",
    "documentationCoverage": 92,
    "activeContributors": 8
  },
  "success": true
}
```

#### GET /api/projects/metrics
Get metrics for all projects.

**Response:**
```json
{
  "data": [
    {
      "id": "metric-id-1",
      "projectId": "project-id-1",
      "healthScore": 85,
      "riskLevel": "Low",
      "lastUpdated": "2023-12-01T10:00:00Z",
      "documentationCoverage": 92,
      "activeContributors": 8
    }
  ],
  "success": true
}
```

### 5. Search API

#### GET /api/projects/search
Search projects with filters.

**Query Parameters:**
- `q` (required): Search query string
- `timeRange` (optional): `1month` | `3months` | `6months` | `1year`
- `categories` (optional): Comma-separated list of categories
- `contributors` (optional): Comma-separated list of contributor IDs

**Example:**
```
GET /api/projects/search?q=documentation&categories=API,Documentation&timeRange=3months
```

**Response:**
```json
{
  "data": [
    {
      "id": "atlas-docs",
      "name": "Atlas Documentation",
      // ... other project fields
    }
  ],
  "success": true,
  "total": 1
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details if available"
  }
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting

All endpoints are rate-limited to 100 requests per minute per user. Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: 1640995200
```

## Pagination

For endpoints that return lists, pagination is supported:

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)

**Response includes pagination metadata:**
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  },
  "success": true
}
```

## WebSocket Events (Optional)

For real-time updates, the application can subscribe to WebSocket events:

```javascript
const ws = new WebSocket('ws://localhost:3000/api/ws');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch (data.type) {
    case 'project_updated':
      // Handle project update
      break;
    case 'contributor_activity':
      // Handle new contributor activity
      break;
    case 'documentation_assigned':
      // Handle documentation assignment
      break;
  }
};
```

## Implementation Notes

1. **Environment Variables**: Set `NEXT_PUBLIC_API_URL` in your environment to point to your backend API
2. **Error Handling**: All API calls include proper error handling with user-friendly messages
3. **Loading States**: The UI shows loading indicators while API calls are in progress
4. **Caching**: Consider implementing client-side caching for frequently accessed data
5. **Authentication**: Ensure proper authentication tokens are included in all requests
6. **Data Validation**: All request bodies should be validated on both client and server sides