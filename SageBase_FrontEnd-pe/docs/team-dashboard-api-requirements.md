# Team Contribution Dashboard API Requirements

## Overview
This document outlines all the data requirements, API endpoints, and data structures needed to power the Team Contribution Dashboard.

## Base URL
```
/api
```

## Authentication
All endpoints require JWT authentication via <PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer {jwt_token}
```

---

## 1. Core API Endpoints

### 1.1 Get Time Periods
**Endpoint:** `GET /api/time-periods`

**Description:** Fetch available time periods for filtering project data

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "value": "last-7-days",
      "label": "Last 7 days"
    },
    {
      "value": "last-30-days", 
      "label": "Last 30 days"
    },
    {
      "value": "last-3-months",
      "label": "Last 3 months"
    },
    {
      "value": "last-6-months",
      "label": "Last 6 months"
    },
    {
      "value": "custom",
      "label": "Custom range"
    }
  ]
}
```

**Notes:**
- This endpoint should be called first to populate the time period selector
- The frontend will use the first available period as default
- Custom time ranges can be implemented later for advanced filtering

### 1.2 Get All Projects
**Endpoint:** `GET /api/projects`

**Query Parameters:**
- `timeRange` (required): Filter projects by time range
  - Values: `last-7-days`, `last-30-days`, `last-3-months`, `last-6-months`, `custom`

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "status": "string",
      "repositoryUrl": "string (full URL)",
      "documentationUrl": "string (full URL)",
      "teamData": [TeamMember],
      "lastUpdated": "ISO 8601 timestamp",
      "totalCommits": "number",
      "totalLinesOfCode": "number",
      "totalPullRequests": "number",
      "totalDocContributions": "number"
    }
  ]
}
```

### 1.3 Get Project Details
**Endpoint:** `GET /api/projects/{projectId}`

**Query Parameters:**
- `timeRange` (required): Same as above

**Response:** Single project object with full team data

### 1.4 Recalculate Project Metrics
**Endpoint:** `POST /api/projects/{projectId}/recalculate`

**Request Body:**
```json
{
  "timeRange": "string"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "string"
  }
}
```

---

## 2. Data Models

### 2.1 Project Object
```typescript
interface Project {
  id: string;                    // Unique project identifier
  name: string;                  // Project name (e.g., "Project Alpha")
  description: string;           // Project description
  status: string;                // Current project status (dynamic from backend)
  repositoryUrl: string;         // Full GitHub/GitLab URL
  documentationUrl: string;      // Full documentation URL
  teamData: TeamMember[];       // Array of team members
  lastUpdated: string;          // ISO 8601 timestamp
  totalCommits: number;         // Sum of all team commits
  totalLinesOfCode: number;     // Sum of all team lines of code
  totalPullRequests: number;    // Sum of all team pull requests
  totalDocContributions: number; // Sum of all team documentation
}
```

### 2.2 Team Member Object
```typescript
interface TeamMember {
  id: string;                    // Unique member identifier
  name: string;                  // Full name (e.g., "Emily Watson")
  avatar: string;                // Initials (e.g., "EW")
  commits: number;               // Number of commits in time range
  linesOfCode: number;           // Lines of code added/modified
  pullRequests: number;          // Number of pull requests created
  docContributions: number;      // Documentation contributions count
  role: string;                  // Job title (e.g., "Lead Developer")
  contributionScore: number;     // Calculated score (0-100)
  email: string;                 // Member email address
  lastActive: string;            // ISO 8601 timestamp of last activity
}
```

### 2.3 Time Period Object
```typescript
interface TimePeriod {
  value: string;                 // Internal identifier (e.g., "last-30-days")
  label: string;                 // Display label (e.g., "Last 30 days")
}
```

---

## 3. Data Sources Required

### 3.1 Git Repository Integration
**Required APIs:**
- GitHub API v3 or GitLab API v4
- Access to repository statistics
- Commit history and metadata
- Pull request/Merge request data
- Code change analytics

**Data to Extract:**
- Commit counts per user
- Lines of code added/removed
- Pull request creation and review
- File modification patterns
- Branch activity

### 3.2 Documentation Platforms
**Possible Sources:**
- Confluence API
- Notion API
- GitHub Wiki
- Custom documentation systems
- Markdown file tracking

**Data to Extract:**
- Documentation page creation
- Content updates and revisions
- User contribution tracking
- Documentation quality metrics

### 3.3 User Management System
**Required Data:**
- User profiles and roles
- Team assignments
- Email addresses
- Avatar/initials
- Last activity timestamps

---

## 4. Calculation Requirements

### 4.1 Contribution Score Formula
```
Contribution Score = (Commits × 0.4) + (Lines of Code × 0.3) + (Pull Requests × 0.2) + (Documentation × 0.1)

Where each metric is normalized to a 0-100 scale based on team averages
```

### 4.2 Time Range Filtering
**Implementation:**
- Filter all metrics by selected time range
- Aggregate data from multiple sources
- Cache results for performance
- Real-time updates for recent data

**Time Ranges:**
- Last 7 days: Current week
- Last 30 days: Current month
- Last 3 months: Quarterly view
- Last 6 months: Semi-annual view
- Custom: User-defined range (future enhancement)

---

## 5. Performance Requirements

### 5.1 Response Times
- **Time Periods:** < 200ms
- **Projects List:** < 500ms
- **Project Details:** < 800ms
- **Metrics Recalculation:** < 5 seconds
- **Real-time Updates:** < 200ms

### 5.2 Caching Strategy
- **Time Periods:** Cache for 1 hour (rarely changes)
- **Project List:** Cache for 5 minutes
- **Team Data:** Cache for 2 minutes
- **Metrics:** Cache for 10 minutes
- **User Profiles:** Cache for 1 hour

### 5.3 Database Considerations
- Index on `projectId` and `timeRange`
- Partition tables by time if large datasets
- Use read replicas for analytics queries
- Implement connection pooling

---

## 6. Error Handling

### 6.1 HTTP Status Codes
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Project not found
- `500` - Internal server error

### 6.2 Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "errorCode": "ERROR_CODE",
  "timestamp": "ISO 8601 timestamp"
}
```

---

## 7. Security Requirements

### 7.1 Authentication
- JWT tokens with expiration
- Refresh token mechanism
- Secure token storage

### 7.2 Authorization
- Role-based access control
- Project-level permissions
- Team member data privacy

### 7.3 Rate Limiting
- 100 requests per minute per user
- 1000 requests per hour per user
- Special limits for recalculation endpoints

---

## 8. Integration Examples

### 8.1 GitHub Integration
```bash
# Get repository statistics
GET https://api.github.com/repos/{owner}/{repo}/stats/contributors

# Get commit activity
GET https://api.github.com/repos/{owner}/{repo}/stats/commit_activity

# Get pull requests
GET https://api.github.com/repos/{owner}/{repo}/pulls?state=all
```

### 8.2 GitLab Integration
```bash
# Get repository statistics
GET https://gitlab.com/api/v4/projects/{id}/statistics

# Get commit statistics
GET https://gitlab.com/api/v4/projects/{id}/repository/commits

# Get merge requests
GET https://gitlab.com/api/v4/projects/{id}/merge_requests
```

---

## 9. Sample Data for Testing

### 9.1 Sample Time Periods
```json
{
  "success": true,
  "data": [
    {
      "value": "last-7-days",
      "label": "Last 7 days"
    },
    {
      "value": "last-30-days",
      "label": "Last 30 days"
    },
    {
      "value": "last-3-months",
      "label": "Last 3 months"
    },
    {
      "value": "last-6-months",
      "label": "Last 6 months"
    },
    {
      "value": "custom",
      "label": "Custom range"
    }
  ]
}
```

### 9.2 Sample Project
```json
{
  "id": "project-alpha",
  "name": "Project Alpha",
  "description": "Main application development",
  "status": "Active",
  "repositoryUrl": "https://github.com/company/project-alpha",
  "documentationUrl": "https://docs.company.com/project-alpha",
  "teamData": [
    {
      "id": 1,
      "name": "Emily Watson",
      "avatar": "EW",
      "commits": 52,
      "linesOfCode": 3200,
      "pullRequests": 15,
      "docContributions": 12,
      "role": "Lead Developer",
      "contributionScore": 98,
      "email": "<EMAIL>",
      "lastActive": "2024-01-15T10:30:00Z"
    }
  ],
  "lastUpdated": "2024-01-15T10:30:00Z",
  "totalCommits": 166,
  "totalLinesOfCode": 9790,
  "totalPullRequests": 43,
  "totalDocContributions": 28
}
```

---

## 10. Implementation Checklist

- [ ] Set up authentication system (JWT)
- [ ] Create time periods API endpoint
- [ ] Integrate with Git provider APIs
- [ ] Set up documentation platform integration
- [ ] Implement user management system
- [ ] Create database schema for projects and team data
- [ ] Implement contribution score calculation
- [ ] Add time range filtering
- [ ] Set up caching layer
- [ ] Implement error handling
- [ ] Add rate limiting
- [ ] Set up monitoring and logging
- [ ] Create API documentation
- [ ] Implement testing suite
- [ ] Set up CI/CD pipeline

---

## 11. Frontend Integration Notes

### 11.1 Data Loading Flow
1. **Initial Load:** Fetch time periods first
2. **Set Default:** Use first available period or preferred default
3. **Load Projects:** Fetch projects with selected time range
4. **Set Active Project:** Automatically select first project
5. **Dynamic Updates:** Refresh data when period changes

### 11.2 Error Handling
- Graceful fallback for time periods if API fails
- Retry mechanisms for failed requests
- User-friendly error messages
- Loading states for all async operations

### 11.3 State Management
- No hardcoded values in frontend
- All data fetched from backend APIs
- Dynamic rendering based on API responses
- Proper loading and error states

---

## 12. Contact & Support

For questions about this API specification or implementation details, please refer to the development team or create an issue in the project repository.

**Last Updated:** January 2024
**Version:** 1.1.0
