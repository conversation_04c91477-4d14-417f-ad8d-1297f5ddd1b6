# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://jaxpdlvzhfyjagxncdmo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpheHBkbHZ6aGZ5amFneG5jZG1vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1NjE4NTUsImV4cCI6MjA2MzEzNzg1NX0.IHuyw9duwgQDzyXA8TFYaXj5acTHmYfWEPbUuwHwruY

# Database Configuration
DATABASE_URL=postgresql://postgres.jaxpdlvzhfyjagxncdmo:<EMAIL>:5432/postgres

# Site URL for local development
NEXT_PUBLIC_SITE_URL=http://localhost:3001

# Backend API URL for local development
NEXT_PUBLIC_BACKEND_API_URL=http://localhost:8000

# OpenAI API Key (if needed)
OPENAI_API_KEY=*****************************************************************************************************************************
