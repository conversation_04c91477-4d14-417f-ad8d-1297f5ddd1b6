"use client";

import React, { createContext, useContext, useState } from "react";

interface SidebarRefreshContextType {
  refreshSidebar: () => void;
  setRefreshFunction: (fn: () => void) => void;
}

const SidebarRefreshContext = createContext<
  SidebarRefreshContextType | undefined
>(undefined);

export function SidebarRefreshProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [refreshFunction, setRefreshFunction] = useState<(() => void) | null>(
    null
  );

  const refreshSidebar = () => {
    if (refreshFunction) {
      refreshFunction();
    }
  };

  return (
    <SidebarRefreshContext.Provider
      value={{ refreshSidebar, setRefreshFunction }}
    >
      {children}
    </SidebarRefreshContext.Provider>
  );
}

export function useSidebarRefresh() {
  const context = useContext(SidebarRefreshContext);
  if (context === undefined) {
    throw new Error(
      "useSidebarRefresh must be used within a SidebarRefreshProvider"
    );
  }
  return context;
}
