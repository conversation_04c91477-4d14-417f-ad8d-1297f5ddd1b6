"use client";

import React, { createContext, useContext, useState, useCallback } from "react";

interface QAPageRefreshContextType {
  refreshQAPage: (projectId: string, qaId: string) => void;
  registerQAPage: (
    projectId: string,
    qaId: string,
    refreshFn: () => void
  ) => void;
  unregisterQAPage: (projectId: string, qaId: string) => void;
}

const QAPageRefreshContext = createContext<
  QAPageRefreshContextType | undefined
>(undefined);

export function QAPageRefreshProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [qaPages, setQAPages] = useState<Map<string, () => void>>(new Map());

  const getQAPageKey = (projectId: string, qaId: string) =>
    `${projectId}-${qaId}`;

  const refreshQAPage = useCallback((projectId: string, qaId: string) => {
    const key = getQAPageKey(projectId, qaId);
    const refreshFn = qaPages.get(key);
    if (refreshFn) {
      console.log(`🔄 Refreshing Q&A page: ${projectId}/${qaId}`);
      refreshFn();
    }
  }, [qaPages]);

  const registerQAPage = useCallback((
    projectId: string,
    qaId: string,
    refreshFn: () => void
  ) => {
    const key = getQAPageKey(projectId, qaId);
    setQAPages((prev) => new Map(prev.set(key, refreshFn)));
    console.log(`📝 Registered Q&A page: ${projectId}/${qaId}`);
  }, []);

  const unregisterQAPage = useCallback((projectId: string, qaId: string) => {
    const key = getQAPageKey(projectId, qaId);
    setQAPages((prev) => {
      const newMap = new Map(prev);
      const wasDeleted = newMap.delete(key);
      if (wasDeleted) {
        console.log(`🗑️ Unregistered Q&A page: ${projectId}/${qaId}`);
      }
      return newMap;
    });
  }, []);

  return (
    <QAPageRefreshContext.Provider
      value={{ refreshQAPage, registerQAPage, unregisterQAPage }}
    >
      {children}
    </QAPageRefreshContext.Provider>
  );
}

export function useQAPageRefresh() {
  const context = useContext(QAPageRefreshContext);
  if (context === undefined) {
    throw new Error(
      "useQAPageRefresh must be used within a QAPageRefreshProvider"
    );
  }
  return context;
}
