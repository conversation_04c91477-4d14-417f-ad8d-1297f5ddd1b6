"use client";

import type React from "react";
import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { useAuth } from "@/contexts/auth-context";

export interface Platform {
  id: string;
  name: string;
  icon: string;
  connected: boolean;
  lastSync?: Date;
  status: "connected" | "disconnected" | "syncing" | "error";
  config?: Record<string, any>;
}

interface ConnectedPlatformsContextType {
  platforms: Platform[];
  isLoading: boolean;
  connectPlatform: (
    platformId: string,
    config?: Record<string, any>
  ) => Promise<void>;
  disconnectPlatform: (platformId: string) => Promise<void>;
  getPlatformStatus: (platformId: string) => Platform | undefined;
  refreshPlatforms: () => Promise<void>;
  syncPlatform: (platformId: string) => Promise<void>;
}

const ConnectedPlatformsContext =
  createContext<ConnectedPlatformsContextType | null>(null);

const DEFAULT_PLATFORMS: Platform[] = [
  {
    id: "github",
    name: "GitHub",
    icon: "/images/platform-logos/github.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "google-drive",
    name: "Google Drive",
    icon: "/images/platform-logos/google-drive.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "confluence",
    name: "Confluence",
    icon: "/images/platform-logos/confluence.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "slack",
    name: "Slack",
    icon: "/images/platform-logos/slack.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "discord",
    name: "Discord",
    icon: "/images/platform-logos/discord.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "teams",
    name: "Microsoft Teams",
    icon: "/images/platform-logos/teams.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "gitlab",
    name: "Gitlab",
    icon: "/images/platform-logos/gitlab.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "notion",
    name: "Notion",
    icon: "/images/platform-logos/notion.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "email",
    name: "Email",
    icon: "/images/platform-logos/gmail.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "jira",
    name: "Jira",
    icon: "/images/platform-logos/jira.svg",
    connected: false,
    status: "disconnected",
  },
  {
    id: "internet",
    name: "Internet",
    icon: "/images/platform-logos/internet.svg",
    connected: false,
    status: "disconnected",
  },
];

const BACKEND_BASE_URL =
  process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";

console.log("🌐 BACKEND_BASE_URL:", BACKEND_BASE_URL);
console.log(
  "🌐 NEXT_PUBLIC_BACKEND_API_URL env:",
  process.env.NEXT_PUBLIC_BACKEND_API_URL
);

export function ConnectedPlatformsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { connectedUserCompany, user } = useAuth();
  const companyId = connectedUserCompany?.company_id;
  const userEmail = user?.email;
  const [platforms, setPlatforms] = useState<Platform[]>(DEFAULT_PLATFORMS);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch company integrations from backend
  const fetchIntegrations = useCallback(async () => {
    if (!companyId) {
      console.log("🔗 No companyId available, skipping fetch");
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    // Reduce timeout to 3 seconds for faster fallback
    const timeoutId = setTimeout(() => {
      console.warn(
        "⚠️ Fetch integrations timeout after 3 seconds, using defaults"
      );
      setIsLoading(false);
      setPlatforms(DEFAULT_PLATFORMS);
    }, 3000);

    try {
      console.log("🔗 Fetching company integrations for company:", companyId);
      const fullUrl = `${BACKEND_BASE_URL}/api/integrations/company-integrations/?company_id=${companyId}`;
      console.log("🔗 Full URL being called:", fullUrl);

      const controller = new AbortController();
      const res = await fetch(fullUrl, {
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
        },
      });

      clearTimeout(timeoutId);

      if (!res.ok) {
        console.error(
          "❌ Failed to fetch company integrations:",
          res.status,
          res.statusText
        );
        throw new Error(`Failed to fetch company integrations: ${res.status}`);
      }

      const data = await res.json();
      console.log("✅ Company integrations fetched successfully:", data);

      // Map backend integrations to Platform[]
      const mapped = DEFAULT_PLATFORMS.map((defaultPlatform) => {
        const integration = data.find(
          (i: any) =>
            i.tool.slug === defaultPlatform.id && i.status === "CONNECTED"
        );
        return integration
          ? {
              ...defaultPlatform,
              connected: true,
              status: "connected" as const,
              config: integration.config || {},
              lastSync: integration.connected_at
                ? new Date(integration.connected_at)
                : undefined,
            }
          : {
              ...defaultPlatform,
              connected: false,
              status: "disconnected" as const,
              config: undefined,
            };
      });
      console.log("integrations from backend: ", mapped);
      setPlatforms(mapped);
    } catch (e) {
      clearTimeout(timeoutId);
      console.error(
        "❌ Error fetching integrations, using default platforms:",
        e
      );
      setPlatforms(DEFAULT_PLATFORMS);
    } finally {
      setIsLoading(false);
    }
  }, [companyId]);

  // Only fetch integrations when companyId is available and we're on a page that needs it
  useEffect(() => {
    // Don't fetch on auth pages (login, reset-password, etc.)
    const isAuthPage =
      typeof window !== "undefined" &&
      (window.location.pathname.includes("/login") ||
        window.location.pathname.includes("/reset-password") ||
        window.location.pathname.includes("/signup") ||
        window.location.pathname.includes("/forgot-password"));

    console.log("🔗 ConnectedPlatformsProvider useEffect:", {
      companyId,
      isAuthPage,
      currentPath:
        typeof window !== "undefined" ? window.location.pathname : "unknown",
    });

    if (companyId && !isAuthPage) {
      console.log("🔗 Fetching integrations - not on auth page");
      fetchIntegrations();
    } else {
      console.log(
        "🔗 Skipping integrations fetch - on auth page or no companyId"
      );
      // If we're skipping, make sure loading is false
      if (!companyId) {
        setIsLoading(false);
      }
    }
  }, [fetchIntegrations, companyId]);

  // Connect platform (admin only)
  const connectPlatform = useCallback(
    async (platformId: string, config?: Record<string, any>) => {
      if (!companyId || !userEmail) return;
      setIsLoading(true);
      try {
        // For GitHub, store installation_id in config
        let finalConfig = config;
        if (
          platformId === "github" &&
          config?.installation_id &&
          config?.ghu_token &&
          config?.ghr_token
        ) {
          finalConfig = {
            installation_id: config.installation_id,
            ghu_token: config.ghu_token,
            ghr_token: config.ghr_token,
          };
        }
        const res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              tool_slug: platformId,
              company_id: companyId,
              acting_user_email: userEmail,
              config: finalConfig,
            }),
          }
        );
        if (!res.ok) {
          const err = await res.json();
          throw new Error(err.error || "Failed to connect platform");
        }
        await fetchIntegrations();
      } catch (e) {
        // Optionally show error toast
      } finally {
        setIsLoading(false);
      }
    },
    [companyId, userEmail, fetchIntegrations]
  );

  // Disconnect platform (admin only, set as disconnected locally)
  const disconnectPlatform = useCallback(
    async (platformId: string) => {
      if (!companyId || !userEmail) return;
      setIsLoading(true);
      try {
        // For GitHub, POST installation_id to /github/disconnect/ and show uninstall link
        if (platformId === "github") {
          // Find installation_id from config
          const githubPlatform = platforms.find((p) => p.id === "github");
          const installation_id = githubPlatform?.config?.installation_id;
          if (!installation_id)
            throw new Error("No GitHub installation_id found");
          const res = await fetch(
            `${BACKEND_BASE_URL}/api/integrations/github/disconnect/`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ installation_id }),
            }
          );
          const data = await res.json();
          if (res.ok && data.uninstall_url) {
            // Optionally open uninstall_url or show to user
            window.open(data.uninstall_url, "_blank");
          } else {
            throw new Error(data.error || "Failed to get uninstall link");
          }
        } else {
          // Default disconnect for other platforms
          const res = await fetch(
            `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
            {
              method: "DELETE",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                tool_slug: platformId,
                company_id: companyId,
                acting_user_email: userEmail,
              }),
            }
          );
          if (!res.ok && res.status !== 204) {
            const err = await res.json();
            throw new Error(err.error || "Failed to disconnect platform");
          }
        }
        await fetchIntegrations();
      } catch (e) {
        // Optionally show error toast
      } finally {
        setIsLoading(false);
      }
    },
    [companyId, userEmail, fetchIntegrations, platforms]
  );

  const getPlatformStatus = useCallback(
    (platformId: string) => {
      return platforms.find((platform) => platform.id === platformId);
    },
    [platforms]
  );

  const refreshPlatforms = fetchIntegrations;

  const syncPlatform = useCallback(async (platformId: string) => {
    // Optionally implement sync logic
  }, []);

  const value = {
    platforms,
    isLoading,
    connectPlatform,
    disconnectPlatform,
    getPlatformStatus,
    refreshPlatforms,
    syncPlatform,
  };

  return (
    <ConnectedPlatformsContext.Provider value={value}>
      {children}
    </ConnectedPlatformsContext.Provider>
  );
}

export function useConnectedPlatforms() {
  const context = useContext(ConnectedPlatformsContext);
  if (!context) {
    throw new Error(
      "useConnectedPlatforms must be used within a ConnectedPlatformsProvider"
    );
  }
  return context;
}

export default ConnectedPlatformsContext;
