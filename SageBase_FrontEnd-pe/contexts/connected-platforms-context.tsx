"use client";

import type React from "react";
import { getBackendUrl } from "@/lib/api-config";
import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
} from "react";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "@/hooks/use-toast";

export interface Platform {
  id: string;
  name: string;
  icon: string;
  connected: boolean;
  lastSync?: Date;
  status: "connected" | "disconnected" | "syncing" | "error";
  originalStatus?: "connected" | "available" | "will be available soon";
  config?: Record<string, any>;
}

interface ConnectedPlatformsContextType {
  platforms: Platform[];
  isLoading: boolean;
  connectPlatform: (
    platformId: string,
    config?: Record<string, any>
  ) => Promise<void>;
  disconnectPlatform: (platformId: string) => Promise<void>;
  getPlatformStatus: (platformId: string) => Platform | undefined;
  refreshPlatforms: () => Promise<void>;
  syncPlatform: (platformId: string) => Promise<void>;
}

const ConnectedPlatformsContext =
  createContext<ConnectedPlatformsContextType | null>(null);

const BACKEND_BASE_URL = getBackendUrl();

console.log("🌐 BACKEND_BASE_URL:", BACKEND_BASE_URL);
console.log(
  "🌐 NEXT_PUBLIC_BACKEND_API_URL env:",
  process.env.NEXT_PUBLIC_BACKEND_API_URL
);

export function ConnectedPlatformsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { connectedUserCompany, user, isLoading: authLoading } = useAuth();
  const companyId = connectedUserCompany?.company_id;
  const userEmail = user?.email;
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const hasInitialized = useRef(false);

  // Fetch integrations immediately when component mounts (not on auth pages)
  useEffect(() => {
    // Don't fetch on auth pages (login, reset-password, etc.)
    const isAuthPage =
      typeof window !== "undefined" &&
      (window.location.pathname.includes("/login") ||
        window.location.pathname.includes("/reset-password") ||
        window.location.pathname.includes("/signup") ||
        window.location.pathname.includes("/forgot-password"));

    console.log("🔗 ConnectedPlatformsProvider useEffect:", {
      isAuthPage,
      currentPath:
        typeof window !== "undefined" ? window.location.pathname : "unknown",
      windowExists: typeof window !== "undefined",
      companyId,
      authLoading,
    });

    if (!isAuthPage && companyId && !authLoading && !hasInitialized.current) {
      console.log(
        "🔗 Fetching integrations immediately - not on auth page, companyId available, auth fully loaded, and not yet initialized"
      );
      hasInitialized.current = true;
      // Call the API directly without going through fetchIntegrations callback
      const fetchData = async () => {
        setIsLoading(true);
        try {
          console.log("🔗 Fetching possible connections");
          const fullUrl = `${BACKEND_BASE_URL}/api/possible_connections/?company_id=${companyId}`;

          const res = await fetch(fullUrl, {
            headers: { "Content-Type": "application/json" },
          });

          if (!res.ok) {
            console.error("❌ API call failed:", res.status, res.statusText);
            throw new Error(`Failed to fetch: ${res.status} ${res.statusText}`);
          }

          const data = await res.json();

          // Map backend possible connections to Platform[]
          const mapped = data.map((connection: any) => {
            // Backend returns: "will be available soon", "connected", "available"
            let platformStatus:
              | "connected"
              | "disconnected"
              | "syncing"
              | "error";
            let isConnected = false;

            if (connection.status === "connected") {
              platformStatus = "connected";
              isConnected = true;
            } else if (connection.status === "available") {
              platformStatus = "disconnected";
              isConnected = false;
            } else {
              // "will be available soon" or any other status
              platformStatus = "disconnected";
              isConnected = false;
            }

            return {
              id: connection.id,
              name: connection.name || connection.id,
              icon: `/images/platform-logos/${connection.id}.svg`,
              connected: isConnected,
              status: platformStatus,
              originalStatus: connection.status,
              config: connection.config || {},
              lastSync: connection.lastSync || undefined,
            };
          });
          console.log("🔗 Setting platforms state with count:", mapped.length);
          setPlatforms(mapped);
        } catch (e) {
          console.error("❌ Error fetching integrations:", e);
          console.log("🔗 Error details:", e);
          setPlatforms([]);
        } finally {
          console.log("🔗 Setting isLoading to false");
          setIsLoading(false);
        }
      };

      fetchData();
    } else if (isAuthPage) {
      console.log("🔗 Skipping integrations fetch - on auth page");
      setIsLoading(false);
    } else if (!companyId || authLoading) {
      console.log(
        "🔗 Skipping integrations fetch - companyId not available yet or auth still loading"
      );
      setIsLoading(false);
    }
  }, [companyId, authLoading]); // Run when companyId or authLoading changes

  // Connect platform (admin only)
  const connectPlatform = useCallback(
    async (platformId: string, config?: Record<string, any>) => {
      if (!companyId || !userEmail) return;
      setIsLoading(true);
      try {
        // For GitHub, store installation_id in config
        let finalConfig = config;
        if (
          platformId === "github" &&
          config?.installation_id &&
          config?.ghu_token &&
          config?.ghr_token
        ) {
          finalConfig = {
            installation_id: config.installation_id,
            ghu_token: config.ghu_token,
            ghr_token: config.ghr_token,
          };
        }
        const res = await fetch(
          `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              tool_slug: platformId,
              company_id: companyId,
              acting_user_email: userEmail,
              config: finalConfig,
            }),
          }
        );
        if (!res.ok) {
          const err = await res.json();
          throw new Error(err.error || "Failed to connect platform");
        }

        // Refresh platforms after successful connection
        try {
          await refreshPlatforms();
          console.log("✅ Platforms refreshed after successful connection");
        } catch (refreshError) {
          console.warn(
            "⚠️ Failed to refresh platforms after connection:",
            refreshError
          );
        }

        // Show success notification
        toast({
          title: `${
            platformId.charAt(0).toUpperCase() + platformId.slice(1)
          } Connected`,
          description: `${
            platformId.charAt(0).toUpperCase() + platformId.slice(1)
          } integration has been successfully connected.`,
          variant: "default",
        });
      } catch (e) {
        console.error("❌ Failed to connect platform:", e);
        throw e; // Re-throw to let caller handle the error
      } finally {
        setIsLoading(false);
      }
    },
    [companyId, userEmail]
  );

  // Disconnect platform (admin only, set as disconnected locally)
  const disconnectPlatform = useCallback(
    async (platformId: string) => {
      if (!companyId || !userEmail) return;
      setIsLoading(true);
      try {
        if (platformId === "github") {
          // For GitHub, we need to delete the company integration record
          // The backend will handle the GitHub app uninstallation internally
          console.log("🔄 Disconnecting GitHub integration...");

          const res = await fetch(
            `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
            {
              method: "DELETE",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                tool_slug: "github",
                company_id: companyId,
                acting_user_email: userEmail,
              }),
            }
          );

          if (!res.ok && res.status !== 204) {
            const err = await res.json().catch(() => ({} as any));
            throw new Error(
              err?.error || "Failed to disconnect GitHub integration"
            );
          }

          console.log("✅ GitHub integration disconnected successfully");
          toast({
            title: "GitHub Disconnected",
            description: "GitHub integration has been successfully removed.",
            variant: "default",
          });
        } else if (platformId === "confluence") {
          // For Confluence, try to call delete-profile API if it exists
          console.log(
            `🔄 Attempting to delete Confluence profile for user: ${userEmail}`
          );
          try {
            const res = await fetch(
              `${BACKEND_BASE_URL}/api/integrations/confluence/delete-profile/`,
              {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                  email: userEmail,
                }),
              }
            );
            if (res.ok) {
              console.log("✅ Confluence profile deleted successfully");
            } else if (res.status === 404) {
              console.log("ℹ️ Delete-profile endpoint not found, skipping...");
            } else {
              const err = await res.json();
              console.warn("⚠️ Failed to delete Confluence profile:", err);
            }
          } catch (error) {
            console.warn("⚠️ Error calling delete-profile endpoint:", error);
          }

          // Always call the default disconnect to remove from company integrations
          console.log(
            `🔄 Removing Confluence from company integrations for company: ${companyId}`
          );
          const defaultRes = await fetch(
            `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
            {
              method: "DELETE",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                tool_slug: "confluence", // Ensure we use the correct slug
                company_id: companyId,
                acting_user_email: userEmail,
              }),
            }
          );

          if (!defaultRes.ok && defaultRes.status !== 204) {
            const err = await defaultRes.json();
            console.error(
              "❌ Failed to remove from company integrations:",
              err
            );
            throw new Error(err.error || "Failed to disconnect platform");
          } else {
            console.log("✅ Confluence removed from company integrations");
            toast({
              title: "Confluence Disconnected",
              description:
                "Confluence integration has been successfully removed.",
              variant: "default",
            });
          }
        } else if (platformId === "google-drive") {
          // Delete entire Google Drive workspace on backend (clears embeds + DB)
          let workspaceId = getPlatformStatus("google-drive")?.config
            ?.workspace_id as string | undefined;
          if (!workspaceId) {
            // Try to resolve workspace_id from backend status
            try {
              const statusRes = await fetch(
                `${BACKEND_BASE_URL}/api/integrations/google-drive/status/?company_id=${companyId}`
              );
              if (statusRes.ok) {
                const statusJson = await statusRes.json();
                if (statusJson?.connected && statusJson?.workspace_id) {
                  workspaceId = statusJson.workspace_id as string;
                }
              }
            } catch {}
          }
          if (workspaceId) {
            const delWsRes = await fetch(
              `${BACKEND_BASE_URL}/api/integrations/google-drive/delete-workspace/`,
              {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ workspace_id: workspaceId }),
              }
            );
            if (!delWsRes.ok) {
              const err = await delWsRes.json().catch(() => ({} as any));
              throw new Error(
                err?.error || "Failed to delete Google Drive workspace"
              );
            }
          }

          // Also remove the company integration record for google-drive
          const removeIntegrationRes = await fetch(
            `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
            {
              method: "DELETE",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                tool_slug: "google-drive",
                company_id: companyId,
                acting_user_email: userEmail,
              }),
            }
          );
          if (!removeIntegrationRes.ok && removeIntegrationRes.status !== 204) {
            const err = await removeIntegrationRes
              .json()
              .catch(() => ({} as any));
            throw new Error(
              err?.error || "Failed to disconnect Google Drive integration"
            );
          }

          console.log("✅ Google Drive integration disconnected successfully");
          toast({
            title: "Google Drive Disconnected",
            description:
              "Google Drive integration has been successfully removed.",
            variant: "default",
          });
        } else {
          // Default disconnect for other platforms
          const res = await fetch(
            `${BACKEND_BASE_URL}/api/integrations/company-integrations/`,
            {
              method: "DELETE",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                tool_slug: platformId,
                company_id: companyId,
                acting_user_email: userEmail,
              }),
            }
          );
          if (!res.ok && res.status !== 204) {
            const err = await res.json();
            throw new Error(err.error || "Failed to disconnect platform");
          }

          console.log(`✅ ${platformId} integration disconnected successfully`);
          toast({
            title: `${
              platformId.charAt(0).toUpperCase() + platformId.slice(1)
            } Disconnected`,
            description: `${
              platformId.charAt(0).toUpperCase() + platformId.slice(1)
            } integration has been successfully removed.`,
            variant: "default",
          });
        }
        // Refresh platforms after disconnecting
        try {
          await refreshPlatforms();
          console.log("✅ Platforms refreshed after disconnection");
        } catch (refreshError) {
          console.warn(
            "⚠️ Failed to refresh platforms after disconnection:",
            refreshError
          );
        }
      } catch (e) {
        console.error(`❌ Failed to disconnect ${platformId}:`, e);
        // Optionally show error toast
      } finally {
        setIsLoading(false);
      }
    },
    [companyId, userEmail]
  );

  const getPlatformStatus = useCallback((platformId: string) => {
    return platforms.find((platform) => platform.id === platformId);
  }, []);

  const refreshPlatforms = useCallback(async () => {
    if (!companyId) {
      console.log("🔄 Cannot refresh platforms - no company ID");
      return;
    }

    // Prevent multiple simultaneous calls
    if (isLoading) {
      console.log("🔄 Skipping refresh - already loading");
      return;
    }

    console.log("🔄 Refreshing platforms...");
    setIsLoading(true);
    try {
      const fullUrl = `${BACKEND_BASE_URL}/api/possible_connections/?company_id=${companyId}`;

      const res = await fetch(fullUrl, {
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        console.error(
          "❌ Platform refresh failed:",
          res.status,
          res.statusText
        );
        throw new Error(`Failed to refresh: ${res.status} ${res.statusText}`);
      }

      const data = await res.json();

      // Map backend possible connections to Platform[]
      const mapped = data.map((connection: any) => {
        let platformStatus: "connected" | "disconnected" | "syncing" | "error";
        let isConnected = false;

        if (connection.status === "connected") {
          platformStatus = "connected";
          isConnected = true;
        } else if (connection.status === "available") {
          platformStatus = "disconnected";
          isConnected = false;
        } else {
          platformStatus = "disconnected";
          isConnected = false;
        }

        return {
          id: connection.id,
          name: connection.name || connection.id,
          icon: `/images/platform-logos/${connection.id}.svg`,
          connected: isConnected,
          status: platformStatus,
          originalStatus: connection.status,
          config: connection.config || {},
          lastSync: connection.lastSync || undefined,
        };
      });

      setPlatforms(mapped);
      console.log("🔄 Platforms updated:", mapped);
    } catch (e) {
      console.error("❌ Error refreshing platforms:", e);
    } finally {
      setIsLoading(false);
    }
  }, [companyId, isLoading]);

  const syncPlatform = useCallback(async (platformId: string) => {
    // Optionally implement sync logic
  }, []);

  const value = {
    platforms,
    isLoading,
    connectPlatform,
    disconnectPlatform,
    getPlatformStatus,
    refreshPlatforms,
    syncPlatform,
  };

  return (
    <ConnectedPlatformsContext.Provider value={value}>
      {children}
    </ConnectedPlatformsContext.Provider>
  );
}

export function useConnectedPlatforms() {
  const context = useContext(ConnectedPlatformsContext);
  if (!context) {
    throw new Error(
      "useConnectedPlatforms must be used within a ConnectedPlatformsProvider"
    );
  }
  return context;
}

export default ConnectedPlatformsContext;
