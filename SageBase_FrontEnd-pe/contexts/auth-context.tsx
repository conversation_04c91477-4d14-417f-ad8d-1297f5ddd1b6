"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { getBackendUrl } from "@/lib/api-config";
import { slackNotificationService } from "@/services/slack-notification-service";
// Slack notifications disabled

// Define user types for Django
interface DjangoUser {
  id: string;
  email: string;
  role: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  is_active: boolean;
  date_joined: string;
  last_login?: string;
  company_id?: string;
  company_name?: string;
}

interface ConnectedUserCompany {
  company_id: string;
  company_name?: string;
}

interface AuthContextType {
  user: DjangoUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isGuest: boolean;
  connectedUserCompany: ConnectedUserCompany | null;
  refreshConnectedCompany: () => void;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  signUp: (
    email: string,
    password: string,
    firstName?: string,
    lastName?: string
  ) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  signInWithOAuth: (
    provider: string
  ) => Promise<{ success: boolean; error?: string }>;
  exitGuestMode: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<DjangoUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isGuest, setIsGuest] = useState(false);
  const [connectedUserCompany, setConnectedUserCompany] =
    useState<ConnectedUserCompany | null>(null);
  const router = useRouter();

  // Load connected company quickly from storage on mount (if present)
  useEffect(() => {
    try {
      const storedCompany = localStorage.getItem("connectedCompany");
      if (storedCompany) {
        const parsed = JSON.parse(storedCompany);
        if (parsed && parsed.company_id) {
          setConnectedUserCompany(parsed);
        }
      }
    } catch {}
  }, []);

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const decodeJwt = (token: string): Record<string, any> | null => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => {
            return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (e) {
      return null;
    }
  };

  const deriveConnectedCompany = (
    u: DjangoUser | null,
    token: string | null
  ): ConnectedUserCompany | null => {
    // 1) Prefer explicit fields on user if present
    if (u?.company_id) {
      return { company_id: u.company_id, company_name: u.company_name };
    }

    // 2) Try JWT claims
    if (token) {
      const claims = decodeJwt(token) || {};
      const possibleId =
        claims.company_id ||
        claims.companyId ||
        (claims.company && (claims.company.id || claims.company.company_id));
      const possibleName =
        claims.company_name ||
        claims.companyName ||
        (claims.company &&
          (claims.company.name || claims.company.company_name));
      if (typeof possibleId === "string" && possibleId.length > 0) {
        return { company_id: possibleId, company_name: possibleName };
      }
    }

    // 3) Otherwise, unknown for now
    return null;
  };

  const persistConnectedCompany = (company: ConnectedUserCompany | null) => {
    try {
      if (company) {
        localStorage.setItem("connectedCompany", JSON.stringify(company));
      } else {
        localStorage.removeItem("connectedCompany");
      }
    } catch {}
  };

  const fetchConnectedCompanyFromBackend = async (
    email: string,
    authToken: string | null
  ): Promise<ConnectedUserCompany | null> => {
    try {
      const backendUrl = getBackendUrl();
      const headers: HeadersInit = {
        "Content-Type": "application/json",
      };
      if (authToken) {
        (headers as any).Authorization = `Bearer ${authToken}`;
      }

      // Try a dedicated endpoint if available
      const controller = new AbortController();
      const to = setTimeout(() => controller.abort(), 2500);
      const res = await fetch(
        `${backendUrl}/api/integrations/company-of-user/?acting_user_email=${encodeURIComponent(
          email
        )}`,
        { headers, signal: controller.signal }
      );
      clearTimeout(to);
      if (res.ok) {
        const data = await res.json();
        if (data && data.company_id) {
          return {
            company_id: data.company_id,
            company_name: data.company_name,
          };
        }
      }
    } catch (e) {
      // ignore; will return null
    }

    // Fallback: no backend way
    return null;
  };

  const refreshConnectedCompany = () => {
    try {
      const authToken = localStorage.getItem("authToken");
      const storedUser = localStorage.getItem("user");
      const u = storedUser ? (JSON.parse(storedUser) as DjangoUser) : null;
      const derived = deriveConnectedCompany(u, authToken);
      setConnectedUserCompany(derived);
      persistConnectedCompany(derived);

      // If still not known but we have an email, attempt backend fetch in background
      if (!derived && u?.email) {
        fetchConnectedCompanyFromBackend(u.email, authToken).then((company) => {
          if (company) {
            setConnectedUserCompany(company);
            persistConnectedCompany(company);
          }
        });
      }
    } catch {}
  };

  const checkAuth = async () => {
    try {
      setIsLoading(true);

      // Get from localStorage (set during login)
      const storedUser = localStorage.getItem("user");
      const authToken = localStorage.getItem("authToken");

      if (!storedUser || !authToken) {
        console.log("🔍 No stored user or token found, user not authenticated");
        setIsAuthenticated(false);
        setUser(null);
        setConnectedUserCompany(null);
        persistConnectedCompany(null);
        return;
      }

      // Check token expiration manually
      try {
        const tokenPayload = JSON.parse(atob(authToken.split(".")[1]));
        const isValid = tokenPayload.exp > Date.now() / 1000;

        if (!isValid) {
          console.log("❌ Token expired, clearing auth state");
          localStorage.removeItem("authToken");
          localStorage.removeItem("user");
          setUser(null);
          setIsAuthenticated(false);
          setIsGuest(false);
          setConnectedUserCompany(null);
          persistConnectedCompany(null);
          return;
        }
      } catch (error) {
        console.error("❌ Error parsing token:", error);
        localStorage.removeItem("authToken");
        localStorage.removeItem("user");
        setUser(null);
        setIsAuthenticated(false);
        setIsGuest(false);
        setConnectedUserCompany(null);
        persistConnectedCompany(null);
        return;
      }

      // Token is valid, set user from localStorage
      const userData = JSON.parse(storedUser) as DjangoUser;
      console.log("✅ User authenticated from localStorage:", userData);
      setUser(userData);
      setIsAuthenticated(true);
      setIsGuest(false);

      // Send Slack notification for user reconnection
      try {
        await slackNotificationService.sendUserConnectionNotification({
          userEmail: userData.email,
          userName:
            `${userData.first_name || ""} ${userData.last_name || ""}`.trim() ||
            userData.email,
          connectionType: "login",
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        });
      } catch (error) {
        console.error(
          "❌ Failed to send Slack notification for reconnection:",
          error
        );
      }

      // Derive connected company from user/token
      const derivedCompany = deriveConnectedCompany(userData, authToken);
      setConnectedUserCompany(derivedCompany);
      persistConnectedCompany(derivedCompany);

      // If not derivable, fetch from backend by email in background
      if (!derivedCompany && userData.email) {
        fetchConnectedCompanyFromBackend(userData.email, authToken).then(
          (company) => {
            if (company) {
              setConnectedUserCompany(company);
              persistConnectedCompany(company);
            }
          }
        );
      }
    } catch (error) {
      console.error("❌ Auth check error:", error);
      setUser(null);
      setIsAuthenticated(false);
      setIsGuest(false);
      setConnectedUserCompany(null);
      persistConnectedCompany(null);
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      console.log("🔐 Attempting Django sign in...");

      const backendUrl = getBackendUrl();
      const response = await fetch(
        `${backendUrl}/api/integrations/auth/login/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, password }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log("✅ Django sign in successful:", data);

        if (data.success && data.token && data.user) {
          // Store auth token and user data in localStorage
          localStorage.setItem("authToken", data.token);
          localStorage.setItem("user", JSON.stringify(data.user));

          // Set cookies for middleware to detect authentication
          document.cookie = `authToken=${data.token}; path=/; max-age=${
            60 * 60 * 24 * 7
          }; SameSite=Lax`; // 7 days
          document.cookie = `user=${encodeURIComponent(
            JSON.stringify(data.user)
          )}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`; // 7 days

          setUser(data.user);
          setIsAuthenticated(true);
          setIsGuest(false);

          // Derive connected company now that we have token + user
          const derivedCompany = deriveConnectedCompany(data.user, data.token);
          setConnectedUserCompany(derivedCompany);
          persistConnectedCompany(derivedCompany);

          // If still unknown, try backend fetch in background
          if (!derivedCompany && data.user.email) {
            fetchConnectedCompanyFromBackend(data.user.email, data.token).then(
              (company) => {
                if (company) {
                  setConnectedUserCompany(company);
                  persistConnectedCompany(company);
                }
              }
            );
          }

          // Send Slack notification for user login
          try {
            await slackNotificationService.sendUserConnectionNotification({
              userEmail: data.user.email,
              userName:
                `${data.user.first_name || ""} ${
                  data.user.last_name || ""
                }`.trim() || data.user.email,
              connectionType: "login",
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent,
            });
          } catch (error) {
            console.error(
              "❌ Failed to send Slack notification for login:",
              error
            );
          }

          return { success: true };
        } else {
          return { success: false, error: "Invalid response format" };
        }
      } else {
        const errorData = await response.json();
        console.error("❌ Django sign in failed:", errorData);
        return { success: false, error: errorData.message || "Sign in failed" };
      }
    } catch (error) {
      console.error("❌ Django sign in error:", error);
      return { success: false, error: "Network error occurred" };
    }
  };

  const signUp = async (
    email: string,
    password: string,
    firstName?: string,
    lastName?: string
  ) => {
    try {
      console.log("📝 Attempting Django sign up...");

      const backendUrl = getBackendUrl();
      const response = await fetch(
        `${backendUrl}/api/integrations/auth/register/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email,
            password,
            first_name: firstName,
            last_name: lastName,
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log("✅ Django sign up successful:", data);

        if (data.success && data.user) {
          // Store user data (no token on signup, user needs to verify email)
          localStorage.setItem("user", JSON.stringify(data.user));

          setUser(data.user);
          setIsAuthenticated(false); // Not authenticated until email verified
          setIsGuest(false);

          // Clear any previous company info
          setConnectedUserCompany(null);
          persistConnectedCompany(null);

          // Send Slack notification for user signup
          try {
            await slackNotificationService.sendUserConnectionNotification({
              userEmail: email,
              userName: `${firstName || ""} ${lastName || ""}`.trim() || email,
              connectionType: "signup",
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent,
            });
          } catch (error) {
            console.error(
              "❌ Failed to send Slack notification for signup:",
              error
            );
          }

          return { success: true };
        } else {
          return { success: false, error: "Invalid response format" };
        }
      } else {
        const errorData = await response.json();
        console.error("❌ Django sign up failed:", errorData);
        return { success: false, error: errorData.message || "Sign up failed" };
      }
    } catch (error) {
      console.error("❌ Django sign up error:", error);
      return { success: false, error: "Network error occurred" };
    }
  };

  const signOut = async () => {
    try {
      console.log("🚪 Attempting Django sign out...");

      // Clear local storage
      localStorage.removeItem("authToken");
      localStorage.removeItem("user");
      localStorage.removeItem("connectedCompany");

      // Clear cookies
      document.cookie =
        "authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie = "user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

      // Update state
      setUser(null);
      setIsAuthenticated(false);
      setIsGuest(false);
      setConnectedUserCompany(null);

      console.log("✅ Django sign out successful");

      // Redirect to login
      router.push("/login");
    } catch (error) {
      console.error("❌ Django sign out error:", error);
      // Even if there's an error, clear local state
      localStorage.removeItem("authToken");
      localStorage.removeItem("user");
      localStorage.removeItem("connectedCompany");
      setUser(null);
      setIsAuthenticated(false);
      setIsGuest(false);
      setConnectedUserCompany(null);
      router.push("/login");
    }
  };

  const signInWithOAuth = async (provider: string) => {
    try {
      console.log(`🔐 Attempting OAuth sign in with ${provider}...`);

      // Redirect to Django OAuth endpoint
      const backendUrl = getBackendUrl();
      const response = await fetch(
        `${backendUrl}/api/integrations/auth/oauth/${provider}/`,
        {
          method: "GET",
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.auth_url) {
          window.location.href = data.auth_url;
          return { success: true };
        } else {
          return { success: false, error: "No OAuth URL received" };
        }
      } else {
        return { success: false, error: "Failed to initiate OAuth" };
      }
    } catch (error) {
      console.error("❌ OAuth sign in error:", error);
      return { success: false, error: "OAuth sign in failed" };
    }
  };

  const exitGuestMode = async () => {
    try {
      console.log("🚪 Exiting guest mode...");
      setIsGuest(false);
      // Redirect to login page
      router.push("/login");
    } catch (error) {
      console.error("❌ Exit guest mode error:", error);
    }
  };

  const refreshUser = async () => {
    await checkAuth();
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    isGuest,
    connectedUserCompany,
    refreshConnectedCompany,
    signIn,
    signUp,
    signOut,
    signInWithOAuth,
    exitGuestMode,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
