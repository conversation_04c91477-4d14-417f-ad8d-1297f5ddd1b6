"use client";

import type React from "react";
import { createContext, useContext, useEffect, useState } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import type { Session, User } from "@supabase/supabase-js";
import { useRouter } from "next/navigation"; // Corrected: using next/navigation
import { clearSessionCache } from "@/lib/api-utils";
import {
  initializeAuth,
  subscribeToAuthChanges,
  getCurrentUser,
  getCurrentSession,
  signOut as authServiceSignOut,
} from "@/lib/auth-service";

type ConnectedUserCompany = {
  company_id: string | null;
  company_name: string | null;
};

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isGuest: boolean;
  connectedUserCompany: ConnectedUserCompany | null;
  setConnectedUserCompany: (company: ConnectedUserCompany | null) => void;
  userRole: string | null;
  setUserRole: (role: string | null) => void;
  backendUserId: string | null;
  setBackendUserId: (id: string | null) => void;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  enterGuestMode: () => void;
  exitGuestMode: () => void;
  clearCachedCompanyData: () => void;
};

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGuest, setIsGuest] = useState(false);
  const [connectedUserCompany, _setConnectedUserCompany] =
    useState<ConnectedUserCompany | null>(null);
  const [userRole, _setUserRole] = useState<string | null>(null);
  const [backendUserId, _setBackendUserId] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true); // Add initialization flag
  const router = useRouter();

  // Persist connectedUserCompany in localStorage
  useEffect(() => {
    // On mount, load from localStorage if present
    const stored = localStorage.getItem("connectedUserCompany");
    if (stored) {
      try {
        _setConnectedUserCompany(JSON.parse(stored));
      } catch (e) {
        console.error(
          "Failed to parse connectedUserCompany from localStorage",
          e
        );
        _setConnectedUserCompany(null);
      }
    }
  }, []);

  // Setter that also persists to localStorage
  const setConnectedUserCompany = (company: ConnectedUserCompany | null) => {
    // Prevent infinite loops by checking if the value is actually different
    const currentStored = localStorage.getItem("connectedUserCompany");
    const newStored = company ? JSON.stringify(company) : null;

    if (currentStored !== newStored) {
      _setConnectedUserCompany(company);
      if (company) {
        localStorage.setItem("connectedUserCompany", JSON.stringify(company));
      } else {
        localStorage.removeItem("connectedUserCompany");
      }
    }
  };

  // Debug function to clear cached company data
  const clearCachedCompanyData = () => {
    localStorage.removeItem("connectedUserCompany");
    localStorage.removeItem("userRole");
    localStorage.removeItem("backendUserId");
    // Also clear user info cache
    const userEmail = user?.email;
    if (userEmail) {
      localStorage.removeItem(`user_info_${userEmail}`);
    }
    _setConnectedUserCompany(null);
    _setUserRole(null);
    _setBackendUserId(null);
    console.log("🗑️ Cleared all cached company data");
  };

  // Persist userRole in localStorage
  useEffect(() => {
    const stored = localStorage.getItem("userRole");
    if (stored) {
      _setUserRole(stored);
    }
  }, []);

  const setUserRole = (role: string | null) => {
    _setUserRole(role);
    if (role) {
      localStorage.setItem("userRole", role);
    } else {
      localStorage.removeItem("userRole");
    }
  };

  // Persist backendUserId in localStorage
  useEffect(() => {
    const stored = localStorage.getItem("backendUserId");
    if (stored) {
      _setBackendUserId(stored);
    }
  }, []);

  const setBackendUserId = (id: string | null) => {
    _setBackendUserId(id);
    if (id) {
      localStorage.setItem("backendUserId", id);
    } else {
      localStorage.removeItem("backendUserId");
    }
  };

  // Check if Supabase environment variables are available
  const hasSupabaseConfig =
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Only create Supabase client if environment variables are available
  const supabase = hasSupabaseConfig ? createClientComponentClient() : null;

  // Function to fetch user info from backend
  const fetchUserInfoFromBackend = async (email: string) => {
    try {
      console.log("🔄 Fetching user info from backend for:", email);
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_API_URL || "http://localhost:8000";
      const response = await fetch(
        `${backendUrl}/api/integrations/get-user-by-email/?email=${encodeURIComponent(
          email
        )}`
      );

      if (response.ok) {
        const userInfo = await response.json();
        console.log("✅ User info fetched:", userInfo);

        // Set user role and company info
        _setUserRole(userInfo.role || null);
        _setBackendUserId(userInfo.id || null);

        const companyData = {
          company_id: userInfo.company,
          company_name: userInfo.company_name,
        };
        _setConnectedUserCompany(companyData);

        // Store in localStorage
        if (userInfo.role) {
          localStorage.setItem("userRole", userInfo.role);
        }
        if (userInfo.id) {
          localStorage.setItem("backendUserId", userInfo.id);
        }
        localStorage.setItem(
          "connectedUserCompany",
          JSON.stringify(companyData)
        );
      } else {
        console.error("❌ Failed to fetch user info:", response.status);
      }
    } catch (err) {
      console.error("❌ Error fetching user info:", err);
    }
  };

  useEffect(() => {
    // Prevent multiple initializations
    if (!isInitializing) {
      return;
    }

    const initializeAuthContext = async () => {
      try {
        setIsLoading(true);

        // Initialize the centralized auth service
        await initializeAuth();

        // Subscribe to auth changes
        const unsubscribe = subscribeToAuthChanges((session, user) => {
          console.log("🔐 Auth context: Received auth state change", { 
            hasSession: !!session, 
            hasUser: !!user,
            sessionId: session?.access_token?.substring(0, 10) + '...',
            userId: user?.id?.substring(0, 8) + '...'
          });

          if (session && user) {
            console.log("🔐 Auth context: Setting authenticated state");
            setSession(session);
            setUser(user);
            setIsGuest(false);
            localStorage.removeItem("guestMode");
          } else {
            console.log("🔐 Auth context: Setting guest mode state");
            // No session, enter guest mode
            setIsGuest(true);
            localStorage.setItem("guestMode", "true");
            document.cookie = "guestMode=true; path=/; max-age=86400";
            setSession(null);
            setUser(null);
          }
        });

        // Set initial state from centralized service
        const currentSession = getCurrentSession();
        const currentUser = getCurrentUser();

        if (currentSession && currentUser) {
          setSession(currentSession);
          setUser(currentUser);
          setIsGuest(false);
          localStorage.removeItem("guestMode");
        } else {
          // Check if we should be in guest mode
          const storedGuestMode = localStorage.getItem("guestMode");
          if (storedGuestMode === "true") {
            setIsGuest(true);
            setSession(null);
            setUser(null);
          } else {
            // Default to guest mode
            setIsGuest(true);
            localStorage.setItem("guestMode", "true");
            document.cookie = "guestMode=true; path=/; max-age=86400";
            setSession(null);
            setUser(null);
          }
        }

        // Cleanup function
        return unsubscribe;
      } catch (error) {
        console.error("Error initializing auth context:", error);
        // Fallback to guest mode on any error during init
        setIsGuest(true);
        localStorage.setItem("guestMode", "true");
        document.cookie = "guestMode=true; path=/; max-age=86400";
        setSession(null);
        setUser(null);
      } finally {
        setIsLoading(false);
        setIsInitializing(false);
      }
    };

    // Initialize auth context
    initializeAuthContext();
  }, [isInitializing]);

  const signIn = async (email: string, password: string) => {
    if (!supabase) {
      if (email === "<EMAIL>" && password === "demo123") {
        enterGuestMode(); // Simulate login by entering guest mode
        router.push("/");
        return;
      }
      throw new Error(
        "Demo mode: Use email '<EMAIL>' and password 'demo123'"
      );
    }

    // Retry logic with exponential backoff - reduced max retries
    const maxRetries = 1; // Reduced from 3 to 1 to prevent excessive requests
    const baseDelay = 2000; // Increased from 1000 to 2000ms

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          console.error(
            `❌ Supabase sign in error (attempt ${attempt + 1}):`,
            error
          );

          // Handle specific error types
          if (error.message.includes("Invalid login credentials")) {
            throw new Error(
              "Invalid login credentials. Please check your email and password."
            );
          } else if (error.message.includes("Email not confirmed")) {
            throw new Error(
              "Please check your email and click the confirmation link before signing in."
            );
          } else if (
            error.message.includes("rate limit") ||
            error.message.includes("too many requests")
          ) {
            if (attempt < maxRetries) {
              const delay = baseDelay * Math.pow(2, attempt);
              console.log(
                `🔄 Rate limited, retrying in ${delay}ms (attempt ${
                  attempt + 1
                }/${maxRetries})`
              );
              await new Promise((resolve) => setTimeout(resolve, delay));
              continue;
            } else {
              throw new Error(
                "Too many login attempts. Please wait a moment and try again."
              );
            }
          } else {
            throw error;
          }
        }

        // Success - fetch user info from backend
        await fetchUserInfoFromBackend(email);
        return; // Exit retry loop on success
      } catch (err: any) {
        if (attempt === maxRetries) {
          // Final attempt failed
          console.error("❌ All sign in attempts failed:", err);
          throw err;
        }

        // For non-rate-limit errors, don't retry
        if (
          !err.message.includes("rate limit") &&
          !err.message.includes("too many requests")
        ) {
          throw err;
        }
      }
    }
  };

  const signUp = async (email: string, password: string) => {
    if (!supabase) {
      throw new Error(
        "Demo mode: Sign up is not available. Use 'Continue as guest' instead."
      );
    }

    const redirectOrigin =
      process.env.NEXT_PUBLIC_SITE_URL || window.location.origin;

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${redirectOrigin}/auth/callback`,
      },
    });
    if (error) throw error;
    alert("Please check your email for a confirmation link.");
  };

  const signInWithGoogle = async () => {
    if (!supabase) {
      enterGuestMode();
      router.push("/");
      return;
    }

    const redirectOrigin =
      process.env.NEXT_PUBLIC_SITE_URL || window.location.origin;

    const { error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${redirectOrigin}/auth/callback`,
        queryParams: {
          redirect_to: `${redirectOrigin}/auth/callback`,
        },
      },
    });
    if (error) throw error;
  };

  const signOut = async () => {
    console.log("🔄 Starting sign out process...");
    try {
      if (isGuest) {
        console.log("🔄 Guest mode sign out");
        // If in guest mode, just exit guest mode
        exitGuestMode();
        return;
      }

      console.log("🔄 Using centralized auth service for sign out");
      // Use centralized auth service
      await authServiceSignOut();
      console.log("✅ Centralized auth service sign out successful");
      
      // Clear session cache
      clearSessionCache();
      
      // Clear local state
      setSession(null);
      setUser(null);
      setIsGuest(true);
      
      // Set guest mode in localStorage and cookies
      localStorage.setItem("guestMode", "true");
      document.cookie = "guestMode=true; path=/; max-age=86400";
      
      // Redirect to login
      try {
        router.push("/login");
      } catch (routerError) {
        console.error("❌ Router error during redirect:", routerError);
        // Fallback to window.location if router fails
        window.location.href = "/login";
      }
    } catch (error) {
      console.error("❌ Error during signOut:", error);
      // Force logout even if there's an error
      localStorage.setItem("guestMode", "true");
      document.cookie = "guestMode=true; path=/; max-age=86400";
      setIsGuest(true);
      setSession(null);
      setUser(null);
      console.log("🔄 Force redirect to login due to error");
      // Use router.push instead of window.location.href for consistency
      try {
        router.push("/login");
      } catch (routerError) {
        console.error("❌ Router error during redirect:", routerError);
        // Fallback to window.location if router fails
        window.location.href = "/login";
      }
    }
  };

  const enterGuestMode = () => {
    localStorage.setItem("guestMode", "true");
    document.cookie = "guestMode=true; path=/; max-age=86400";
    setIsGuest(true);
    setSession(null); // Ensure no session when in guest mode
    setUser(null);
    // Optionally, navigate to a specific page or refresh if needed
    // router.push('/'); // Or some other guest-friendly page
  };

  const exitGuestMode = () => {
    localStorage.removeItem("guestMode");
    document.cookie = "guestMode=; path=/; max-age=0";
    setIsGuest(false);
    // Typically, exiting guest mode means signing in or going to login
    router.push("/login");
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        isLoading,
        isGuest,
        connectedUserCompany,
        setConnectedUserCompany,
        userRole,
        setUserRole,
        backendUserId,
        setBackendUserId,
        signIn,
        signUp,
        signInWithGoogle,
        signOut,
        enterGuestMode,
        exitGuestMode,
        clearCachedCompanyData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export default AuthContext;
