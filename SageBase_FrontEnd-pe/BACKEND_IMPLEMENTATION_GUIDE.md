# Backend Implementation Guide - Notification System

## 🎯 Overview
This guide provides step-by-step instructions for implementing the notification system backend. The frontend is already implemented and expects these exact API endpoints and data structures.

## 📋 Prerequisites
- Authentication system with Bearer token validation
- Database (PostgreSQL/MySQL/MongoDB)
- Real-time capability (Server-Sent Events or WebSockets)
- Integration with external platforms (Teams, Slack, GitHub)

---

## 🗄️ Step 1: Database Schema

### PostgreSQL Schema
```sql
-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('qa_approval', 'project_update', 'document_approval', 'team_invite')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE NULL,
    expires_at TIMESTAMP WITH TIME ZONE NULL,
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    user_id VARCHAR(255) NOT NULL,
    organization_id VARCHAR(255) NULL,
    created_by VARCHAR(255) NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification actions log
CREATE TABLE notification_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(20) NOT NULL CHECK (action_type IN ('approve', 'reject', 'dismiss', 'view')),
    action_data JSONB NULL,
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User notification settings
CREATE TABLE user_notification_settings (
    user_id VARCHAR(255) PRIMARY KEY,
    qa_approval_enabled BOOLEAN DEFAULT TRUE,
    project_update_enabled BOOLEAN DEFAULT TRUE,
    document_approval_enabled BOOLEAN DEFAULT TRUE,
    team_invite_enabled BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT FALSE,
    push_notifications BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_read_at ON notifications(read_at);
CREATE INDEX idx_notifications_expires_at ON notifications(expires_at);
```

### MongoDB Schema (Alternative)
```javascript
// notifications collection
{
  _id: ObjectId,
  id: String, // UUID
  type: String, // enum: qa_approval, project_update, document_approval, team_invite
  title: String,
  message: String,
  data: Object, // Flexible JSONB-like structure
  createdAt: Date,
  readAt: Date | null,
  expiresAt: Date | null,
  priority: String, // enum: low, medium, high
  userId: String,
  organizationId: String | null,
  createdBy: String | null,
  updatedAt: Date
}

// notification_actions collection
{
  _id: ObjectId,
  notificationId: String,
  userId: String,
  actionType: String, // enum: approve, reject, dismiss, view
  actionData: Object | null,
  performedAt: Date
}
```

---

## 🔧 Step 2: Core Models and Services

### Node.js/Express Example

#### models/Notification.js
```javascript
const { DataTypes } = require('sequelize');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  type: {
    type: DataTypes.ENUM('qa_approval', 'project_update', 'document_approval', 'team_invite'),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  data: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  readAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'medium'
  },
  userId: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  organizationId: {
    type: DataTypes.STRING(255),
    allowNull: true
  }
}, {
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
});

module.exports = Notification;
```

#### services/NotificationService.js
```javascript
const Notification = require('../models/Notification');
const { Op } = require('sequelize');
const EventEmitter = require('events');

class NotificationService extends EventEmitter {
  
  async createNotification(notificationData) {
    try {
      const notification = await Notification.create({
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        data: notificationData.data,
        priority: notificationData.priority || 'medium',
        userId: notificationData.userId,
        organizationId: notificationData.organizationId,
        expiresAt: notificationData.expiresAt
      });

      // Emit real-time event
      this.emit('notification_created', {
        userId: notification.userId,
        notification: notification.toJSON()
      });

      return notification;
    } catch (error) {
      throw new Error(`Failed to create notification: ${error.message}`);
    }
  }

  async getNotifications(userId, options = {}) {
    const {
      unreadOnly = false,
      limit = 20,
      offset = 0,
      type = null
    } = options;

    const whereClause = {
      userId,
      [Op.or]: [
        { expiresAt: null },
        { expiresAt: { [Op.gt]: new Date() } }
      ]
    };

    if (unreadOnly) {
      whereClause.readAt = null;
    }

    if (type) {
      whereClause.type = type;
    }

    const notifications = await Notification.findAndCountAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    return {
      data: notifications.rows,
      totalCount: notifications.count,
      hasMore: (offset + limit) < notifications.count
    };
  }

  async getNotificationById(notificationId, userId) {
    const notification = await Notification.findOne({
      where: {
        id: notificationId,
        userId
      }
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    return notification;
  }

  async markAsRead(notificationId, userId) {
    const [updatedRows] = await Notification.update(
      { readAt: new Date() },
      {
        where: {
          id: notificationId,
          userId,
          readAt: null
        }
      }
    );

    if (updatedRows === 0) {
      throw new Error('Notification not found or already read');
    }

    // Emit unread count update
    const unreadCount = await this.getUnreadCount(userId);
    this.emit('unread_count_updated', {
      userId,
      count: unreadCount
    });

    return { success: true };
  }

  async markAllAsRead(userId) {
    const [updatedRows] = await Notification.update(
      { readAt: new Date() },
      {
        where: {
          userId,
          readAt: null
        }
      }
    );

    // Emit unread count update
    this.emit('unread_count_updated', {
      userId,
      count: 0
    });

    return { success: true, updatedCount: updatedRows };
  }

  async getUnreadCount(userId) {
    const count = await Notification.count({
      where: {
        userId,
        readAt: null,
        [Op.or]: [
          { expiresAt: null },
          { expiresAt: { [Op.gt]: new Date() } }
        ]
      }
    });

    return count;
  }

  async deleteNotification(notificationId, userId) {
    const deletedRows = await Notification.destroy({
      where: {
        id: notificationId,
        userId
      }
    });

    if (deletedRows === 0) {
      throw new Error('Notification not found');
    }

    return { success: true };
  }

  async performAction(notificationId, userId, action) {
    const notification = await this.getNotificationById(notificationId, userId);
    
    // Log the action
    await NotificationAction.create({
      notificationId,
      userId,
      actionType: action.type,
      actionData: action.data || null
    });

    let result = { success: true };

    // Handle different action types
    switch (action.type) {
      case 'approve':
        result = await this.handleApproveAction(notification, userId);
        break;
      case 'reject':
        result = await this.handleRejectAction(notification, userId);
        break;
      case 'dismiss':
        await this.markAsRead(notificationId, userId);
        result = { success: true, message: 'Notification dismissed' };
        break;
      case 'view':
        await this.markAsRead(notificationId, userId);
        result = { success: true, message: 'Notification viewed' };
        break;
      default:
        throw new Error('Invalid action type');
    }

    return result;
  }

  async handleApproveAction(notification, userId) {
    if (notification.type === 'qa_approval') {
      const qaData = notification.data;
      
      // Create document in knowledge base
      const document = await this.createKnowledgeBaseDocument(qaData, userId);
      
      // Mark notification as read
      await this.markAsRead(notification.id, userId);
      
      return {
        success: true,
        message: 'Q&A approved and stored in knowledge base',
        data: {
          documentId: document.id,
          knowledgeBaseUrl: `/documents/${document.id}`
        }
      };
    }
    
    // Handle other approval types...
    return { success: true, message: 'Approved successfully' };
  }

  async handleRejectAction(notification, userId) {
    // Mark as read and potentially log rejection reason
    await this.markAsRead(notification.id, userId);
    
    return {
      success: true,
      message: 'Notification rejected'
    };
  }

  async createKnowledgeBaseDocument(qaData, userId) {
    // This should integrate with your knowledge base system
    // Example implementation:
    const document = {
      id: generateUUID(),
      title: qaData.question,
      content: `
## Question
${qaData.question}

## Answer
${qaData.answer}

## Source Information
- **Platform**: ${qaData.platform}
- **Channel**: ${qaData.channel}
- **Question Author**: ${qaData.questionAuthor}
- **Answer Author**: ${qaData.author}
- **Asked**: ${qaData.questionTime}
- **Answered**: ${qaData.answerTime}

## Tags
${qaData.tags ? qaData.tags.map(tag => `#${tag}`).join(' ') : 'No tags'}
      `,
      type: 'qa_knowledge',
      createdBy: userId,
      sourceData: qaData
    };

    // Save to your knowledge base system
    // await KnowledgeBaseService.createDocument(document);
    
    return document;
  }

  // Clean up expired notifications
  async cleanupExpiredNotifications() {
    const deletedRows = await Notification.destroy({
      where: {
        expiresAt: {
          [Op.lt]: new Date()
        }
      }
    });

    console.log(`Cleaned up ${deletedRows} expired notifications`);
    return deletedRows;
  }
}

module.exports = new NotificationService();
```

---

## 🚀 Step 3: API Routes Implementation

### routes/notifications.js
```javascript
const express = require('express');
const router = express.Router();
const NotificationService = require('../services/NotificationService');
const { authenticateToken } = require('../middleware/auth');
const { SSEManager } = require('../services/SSEManager');

// Middleware to authenticate all notification routes
router.use(authenticateToken);

// GET /api/notifications - Get user notifications
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const options = {
      unreadOnly: req.query.unreadOnly === 'true',
      limit: parseInt(req.query.limit) || 20,
      offset: parseInt(req.query.offset) || 0,
      type: req.query.type || null
    };

    const result = await NotificationService.getNotifications(userId, options);
    
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: error.message,
      code: 'FETCH_ERROR'
    });
  }
});

// GET /api/notifications/:id - Get specific notification
router.get('/:id', async (req, res) => {
  try {
    const notification = await NotificationService.getNotificationById(
      req.params.id,
      req.user.id
    );
    
    res.json({ data: notification });
  } catch (error) {
    if (error.message === 'Notification not found') {
      return res.status(404).json({
        error: 'Notification not found',
        code: 'NOT_FOUND'
      });
    }
    
    res.status(500).json({
      error: error.message,
      code: 'FETCH_ERROR'
    });
  }
});

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', async (req, res) => {
  try {
    const result = await NotificationService.markAsRead(
      req.params.id,
      req.user.id
    );
    
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    res.status(400).json({
      error: error.message,
      code: 'UPDATE_ERROR'
    });
  }
});

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all', async (req, res) => {
  try {
    const result = await NotificationService.markAllAsRead(req.user.id);
    
    res.json({
      success: true,
      message: 'All notifications marked as read',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      code: 'UPDATE_ERROR'
    });
  }
});

// POST /api/notifications/:id/action - Perform action on notification
router.post('/:id/action', async (req, res) => {
  try {
    const { type, data } = req.body;
    
    if (!['approve', 'reject', 'dismiss', 'view'].includes(type)) {
      return res.status(400).json({
        error: 'Invalid action type',
        code: 'INVALID_ACTION'
      });
    }

    const result = await NotificationService.performAction(
      req.params.id,
      req.user.id,
      { type, data }
    );
    
    res.json(result);
  } catch (error) {
    res.status(400).json({
      error: error.message,
      code: 'ACTION_ERROR'
    });
  }
});

// DELETE /api/notifications/:id - Delete notification
router.delete('/:id', async (req, res) => {
  try {
    await NotificationService.deleteNotification(req.params.id, req.user.id);
    
    res.json({
      success: true,
      message: 'Notification deleted'
    });
  } catch (error) {
    res.status(400).json({
      error: error.message,
      code: 'DELETE_ERROR'
    });
  }
});

// GET /api/notifications/unread-count - Get unread count
router.get('/unread-count', async (req, res) => {
  try {
    const count = await NotificationService.getUnreadCount(req.user.id);
    
    res.json({
      data: { count }
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      code: 'COUNT_ERROR'
    });
  }
});

// GET /api/notifications/stream/:userId - Server-Sent Events
router.get('/stream/:userId', (req, res) => {
  const userId = req.params.userId;
  
  // Verify user can access this stream
  if (userId !== req.user.id) {
    return res.status(403).json({
      error: 'Access denied',
      code: 'ACCESS_DENIED'
    });
  }

  SSEManager.addConnection(userId, res);
});

// POST /api/notifications/test - Create test notification (development only)
router.post('/test', async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  try {
    const { type, data } = req.body;
    
    const notification = await NotificationService.createNotification({
      type,
      title: `Test ${type} notification`,
      message: 'This is a test notification',
      data,
      userId: req.user.id,
      priority: 'medium'
    });
    
    res.json({
      success: true,
      message: 'Test notification created',
      data: { notificationId: notification.id }
    });
  } catch (error) {
    res.status(400).json({
      error: error.message,
      code: 'TEST_ERROR'
    });
  }
});

module.exports = router;
```

---

## 🔄 Step 4: Real-time Updates (Server-Sent Events)

### services/SSEManager.js
```javascript
class SSEManager {
  constructor() {
    this.connections = new Map(); // userId -> Set of response objects
  }

  addConnection(userId, res) {
    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial connection success
    res.write('data: {"type": "connected"}\n\n');

    // Store connection
    if (!this.connections.has(userId)) {
      this.connections.set(userId, new Set());
    }
    this.connections.get(userId).add(res);

    // Handle client disconnect
    req.on('close', () => {
      this.removeConnection(userId, res);
    });

    req.on('aborted', () => {
      this.removeConnection(userId, res);
    });
  }

  removeConnection(userId, res) {
    const userConnections = this.connections.get(userId);
    if (userConnections) {
      userConnections.delete(res);
      if (userConnections.size === 0) {
        this.connections.delete(userId);
      }
    }
  }

  sendToUser(userId, data) {
    const userConnections = this.connections.get(userId);
    if (userConnections) {
      const message = `data: ${JSON.stringify(data)}\n\n`;
      
      // Send to all user connections and handle failures
      const toRemove = [];
      userConnections.forEach(res => {
        try {
          res.write(message);
        } catch (error) {
          console.error('Failed to send SSE message:', error);
          toRemove.push(res);
        }
      });

      // Remove failed connections
      toRemove.forEach(res => {
        userConnections.delete(res);
      });
    }
  }

  broadcast(data) {
    this.connections.forEach((connections, userId) => {
      this.sendToUser(userId, data);
    });
  }

  getConnectionCount() {
    let total = 0;
    this.connections.forEach(connections => {
      total += connections.size;
    });
    return total;
  }
}

const sseManager = new SSEManager();

// Set up event listeners
const NotificationService = require('./NotificationService');

NotificationService.on('notification_created', (event) => {
  sseManager.sendToUser(event.userId, {
    type: 'notification',
    notification: event.notification
  });
});

NotificationService.on('unread_count_updated', (event) => {
  sseManager.sendToUser(event.userId, {
    type: 'unread_count',
    count: event.count
  });
});

module.exports = { SSEManager: sseManager };
```

---

## 🔗 Step 5: Integration with External Platforms

### services/PlatformIntegrationService.js
```javascript
const NotificationService = require('./NotificationService');

class PlatformIntegrationService {

  // Microsoft Teams webhook handler
  async handleTeamsMessage(webhookData) {
    try {
      // Parse Teams message for Q&A pattern
      const qaPattern = this.detectQAPattern(webhookData.text);
      
      if (qaPattern.isQA) {
        const notification = await NotificationService.createNotification({
          type: 'qa_approval',
          title: 'New Q&A from Teams needs approval',
          message: `A Q&A conversation in ${webhookData.channelName} needs review`,
          data: {
            qaId: generateUUID(),
            question: qaPattern.question,
            answer: qaPattern.answer,
            source: 'Teams',
            channel: webhookData.channelName,
            author: webhookData.author.name,
            questionAuthor: qaPattern.questionAuthor,
            platform: 'teams',
            tags: this.extractTags(qaPattern.question + ' ' + qaPattern.answer),
            verified: false,
            timestamp: this.formatTimestamp(webhookData.timestamp),
            questionTime: this.formatTimestamp(qaPattern.questionTime),
            answerTime: this.formatTimestamp(qaPattern.answerTime)
          },
          userId: await this.determineTargetUser(webhookData.channelId),
          priority: 'high'
        });

        console.log(`Created Teams Q&A notification: ${notification.id}`);
      }
    } catch (error) {
      console.error('Failed to process Teams webhook:', error);
    }
  }

  // Slack webhook handler
  async handleSlackMessage(webhookData) {
    // Similar to Teams handler
    // Implementation depends on Slack's webhook format
  }

  // GitHub webhook handler for issues/discussions
  async handleGitHubWebhook(webhookData) {
    // Handle GitHub issues, discussions, or comments
    // that might contain Q&A content
  }

  detectQAPattern(text) {
    // Implement AI or rule-based Q&A detection
    // This is a simplified example
    const lines = text.split('\n');
    const questionMarkers = ['?', 'how', 'what', 'why', 'when', 'where'];
    const answerMarkers = ['answer:', 'solution:', 'try this:'];
    
    // Simple detection logic
    let question = '';
    let answer = '';
    let isQA = false;

    // More sophisticated implementation would use NLP
    // or integration with your AI systems
    
    return {
      isQA,
      question,
      answer,
      questionAuthor: 'Unknown',
      questionTime: new Date(),
      answerTime: new Date()
    };
  }

  extractTags(text) {
    // Extract relevant tags from the Q&A content
    const commonTechTags = [
      'javascript', 'python', 'react', 'node', 'api', 'database',
      'error', 'bug', 'performance', 'security', 'deployment'
    ];
    
    const textLower = text.toLowerCase();
    return commonTechTags.filter(tag => 
      textLower.includes(tag)
    );
  }

  async determineTargetUser(channelId) {
    // Logic to determine which user should receive the notification
    // Could be based on channel ownership, role, or rotation
    return 'admin-user-id'; // Placeholder
  }

  formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString();
  }
}

module.exports = new PlatformIntegrationService();
```

---

## 🔧 Step 6: Middleware and Utilities

### middleware/auth.js
```javascript
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }
    
    req.user = user;
    next();
  });
};

module.exports = { authenticateToken };
```

### utils/helpers.js
```javascript
const crypto = require('crypto');

function generateUUID() {
  return crypto.randomUUID();
}

function validateNotificationData(data, type) {
  const schemas = {
    qa_approval: {
      required: ['qaId', 'question', 'answer', 'source', 'channel', 'author', 'questionAuthor', 'platform']
    },
    project_update: {
      required: ['projectId', 'projectName', 'updateType', 'description', 'author']
    },
    document_approval: {
      required: ['documentId', 'documentTitle', 'documentType', 'author', 'changes']
    },
    team_invite: {
      required: ['teamId', 'teamName', 'inviterName', 'role']
    }
  };

  const schema = schemas[type];
  if (!schema) {
    throw new Error(`Invalid notification type: ${type}`);
  }

  for (const field of schema.required) {
    if (!data[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  return true;
}

module.exports = {
  generateUUID,
  validateNotificationData
};
```

---

## 📅 Step 7: Scheduled Tasks and Cleanup

### tasks/notificationCleanup.js
```javascript
const cron = require('node-cron');
const NotificationService = require('../services/NotificationService');

// Run cleanup every hour
cron.schedule('0 * * * *', async () => {
  try {
    console.log('Running notification cleanup...');
    await NotificationService.cleanupExpiredNotifications();
    console.log('Notification cleanup completed');
  } catch (error) {
    console.error('Notification cleanup failed:', error);
  }
});

// Run unread count updates every 5 minutes (optional optimization)
cron.schedule('*/5 * * * *', async () => {
  // Could batch update unread counts if needed
});

console.log('Notification cleanup tasks scheduled');
```

---

## 🧪 Step 8: Testing

### tests/notificationService.test.js
```javascript
const NotificationService = require('../services/NotificationService');
const { validateNotificationData } = require('../utils/helpers');

describe('NotificationService', () => {
  beforeEach(async () => {
    // Clear test database
    await Notification.destroy({ where: {} });
  });

  test('should create QA approval notification', async () => {
    const notificationData = {
      type: 'qa_approval',
      title: 'Test Q&A',
      message: 'Test message',
      data: {
        qaId: 'test-qa-123',
        question: 'How does this work?',
        answer: 'It works like this...',
        source: 'Teams',
        channel: 'Test Channel',
        author: 'Test User',
        questionAuthor: 'Test Questioner',
        platform: 'teams',
        timestamp: '1 hour ago',
        questionTime: 'Today at 14:00',
        answerTime: 'Today at 14:30'
      },
      userId: 'test-user-123',
      priority: 'high'
    };

    const notification = await NotificationService.createNotification(notificationData);
    
    expect(notification.id).toBeDefined();
    expect(notification.type).toBe('qa_approval');
    expect(notification.userId).toBe('test-user-123');
  });

  test('should get user notifications', async () => {
    // Create test notification first
    await NotificationService.createNotification({
      type: 'qa_approval',
      title: 'Test',
      message: 'Test',
      data: { qaId: 'test' },
      userId: 'test-user'
    });

    const result = await NotificationService.getNotifications('test-user');
    
    expect(result.data).toHaveLength(1);
    expect(result.totalCount).toBe(1);
  });

  test('should mark notification as read', async () => {
    const notification = await NotificationService.createNotification({
      type: 'qa_approval',
      title: 'Test',
      message: 'Test',
      data: { qaId: 'test' },
      userId: 'test-user'
    });

    await NotificationService.markAsRead(notification.id, 'test-user');
    
    const updated = await NotificationService.getNotificationById(notification.id, 'test-user');
    expect(updated.readAt).toBeDefined();
  });

  test('should validate notification data', () => {
    const validData = {
      qaId: 'test',
      question: 'Test?',
      answer: 'Test answer',
      source: 'Teams',
      channel: 'Test',
      author: 'User',
      questionAuthor: 'Questioner',
      platform: 'teams'
    };

    expect(() => validateNotificationData(validData, 'qa_approval')).not.toThrow();
    
    const invalidData = { qaId: 'test' }; // missing required fields
    expect(() => validateNotificationData(invalidData, 'qa_approval')).toThrow();
  });
});
```

---

## 🚀 Step 9: Deployment and Environment

### docker-compose.yml (Example)
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/notifications
      - JWT_SECRET=your-jwt-secret
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=notifications
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    
volumes:
  postgres_data:
```

### Environment Variables (.env)
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/notifications

# Authentication
JWT_SECRET=your-super-secure-jwt-secret

# Server
PORT=3000
NODE_ENV=development

# External Integrations
TEAMS_WEBHOOK_SECRET=your-teams-webhook-secret
SLACK_WEBHOOK_SECRET=your-slack-webhook-secret
GITHUB_WEBHOOK_SECRET=your-github-webhook-secret

# Redis (for pub/sub if using multiple instances)
REDIS_URL=redis://localhost:6379

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3001
```

---

## 📋 Implementation Checklist

### Phase 1: Core Implementation
- [ ] Set up database schema
- [ ] Implement NotificationService
- [ ] Create API routes
- [ ] Set up authentication middleware
- [ ] Implement Server-Sent Events
- [ ] Test basic CRUD operations

### Phase 2: Integration
- [ ] Implement platform webhooks (Teams/Slack)
- [ ] Set up Q&A detection logic
- [ ] Integrate with knowledge base system
- [ ] Test end-to-end notification flow

### Phase 3: Production Readiness
- [ ] Add comprehensive error handling
- [ ] Implement rate limiting
- [ ] Set up monitoring and logging
- [ ] Configure cleanup tasks
- [ ] Load testing
- [ ] Security audit

### Phase 4: Advanced Features
- [ ] Add email/push notification options
- [ ] Implement user notification preferences
- [ ] Add notification templates
- [ ] Implement notification analytics

---

## 🔍 Testing the Implementation

### Manual Testing Steps
1. **Create test notification via API**
2. **Verify real-time updates work**
3. **Test approve/reject actions**
4. **Verify knowledge base integration**
5. **Test platform webhook integration**

### Frontend Integration Test
Once your backend is running, test with the frontend:

```bash
# Frontend expects these endpoints to work:
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/notifications

curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/notifications/unread-count

# Test SSE connection
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/notifications/stream/USER_ID
```

---

## 🚨 Common Issues and Solutions

### Issue 1: CORS Problems
```javascript
// Add CORS middleware
app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true
}));
```

### Issue 2: SSE Connection Drops
- Implement heartbeat/keep-alive
- Handle connection recovery in frontend
- Add connection pooling

### Issue 3: High Memory Usage
- Implement connection limits
- Clean up dead connections
- Use Redis for scaling

### Issue 4: Database Performance
- Add proper indexes
- Implement pagination
- Archive old notifications

This guide provides everything needed to implement a production-ready notification system that integrates seamlessly with your frontend!